import ast
import csv
import json
import logging
import random
import traceback
from datetime import datetime, timedelta

import pandas as pd
from django.core.paginator import EmptyPage, Paginator
from django.db import connection
from django.db.models import Max, Q
from django.http import HttpResponse, HttpResponseNotFound, JsonResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse

from action import action as sanka_action
from action.event_trigger import FormValidator
from data.constants.constant import (CHART_CHANNEL_STAT, CHART_POST_STAT,
                                     CHART_TYPE_NAME, PANEL_METRIC_TITLE,
                                     PANEL_TYPE_TITLE)
from data.constants.properties_constant import *
from data.constants.reports_constant import *
from data.models import *
from data.property import properties as forward_properties
from data.report.utility import apply_search_setting, apply_view_filter
from utils.actions import log_redirect_failed
from utils.decorator import login_or_hubspot_required
from utils.filter import get_filter_operators
from utils.logger import logger
from utils.project import get_ordered_views
from utils.properties.properties import (ALIAS_TABLE, get_field,
                                         get_page_object, get_report_metrics)
from utils.reports import fetch_panel_data
from utils.report.bar_chart import fetch_bar_chart_data
from utils.report.pivot_chart import fetch_panel_pivot_data
from utils.report.sheet_chart import fetch_sheet_data_v3
from utils.report.cohort_chart import fetch_cohort_chart_data
from utils.report.summaries_chart import fetch_summaries_chart, fetch_summaries_table
from utils.report.funnel_chart import fetch_funnel_chart_data
from utils.report.line_chart import fetch_line_chart_data
from utils.reports_bg_jobs.reports_event import check_event_properties
from utils.utility import get_workspace, is_valid_uuid, save_custom_property, get_permission_filter
from utils.workflow import get_nodes
from utils.workspace import get_permission

logger = logging.getLogger(__name__)

def app_settings(request):
    workspace = get_workspace(request.user)

    if request.method == 'POST':
        if request.POST.get("app_settings", None) == "manage_app":
            search_setting_panel = request.POST.get('search_setting_panel')
            search_setting_dashboard = request.POST.get(
                'search_setting_dashboard')
            page_group_type = request.POST.get('page_group_type')

            app_setting = AppSetting.objects.get(
                workspace=workspace, app_target=DASHBOARD_APP_TARGET)

            search_setting = ""
            if search_setting_panel:
                search_setting = search_setting_panel
            elif search_setting_dashboard:
                search_setting = search_setting_dashboard

            search_setting = search_setting.replace('id_predefined,', '')

            # If it only Id , will remove it as blank string
            if search_setting == "id_predefined":
                search_setting = ""

            if page_group_type == TYPE_OBJECT_PANEL:
                app_setting.search_setting_panel = search_setting
            elif page_group_type == TYPE_OBJECT_DASHBOARD:
                app_setting.search_setting_dashboard = search_setting

            app_setting.save()

            return forward_properties(request)

        elif request.POST.get("app_settings", None) == "manage_app_access":
            users = request.POST.getlist('users', [])
            groups = request.POST.getlist('groups', [])

            app_setting = AppSetting.objects.get(
                workspace=workspace, app_target=DASHBOARD_APP_TARGET)
            app_setting.user.clear()
            app_setting.group.clear()

            selected_users = User.objects.filter(id__in=users)
            if selected_users:
                for selected_user in selected_users:
                    app_setting.user.add(selected_user)

            selected_groups = Group.objects.filter(id__in=groups)
            if selected_groups:
                for selected_group in selected_groups:
                    app_setting.group.add(selected_group)

            app_setting.save()

        return redirect(reverse('report_detail', host='app'))

    app_setting = AppSetting.objects.get(
        workspace=workspace, app_target=DASHBOARD_APP_TARGET)
    role = UserManagement.objects.get(user=request.user, workspace=workspace)
    groups = Group.objects.filter(workspace=workspace)

    context = {
        'object_type': DASHBOARD_APP_TARGET,
        'app_slug': DASHBOARD_APP_SLUG,

        'role': role,
        'groups': groups,
        'app_setting': app_setting
    }
    return render(request, 'data/reports/manage-reports-app.html', context)

@login_or_hubspot_required
def reports(request, object_type):
    if not (request.path.startswith('/modules/') or request.path.startswith('/ja/modules/')) and request.method == 'GET':
        return redirect(reverse('main', host='app'))

    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    page = request.GET.get('page', 1)
    page_obj = get_page_object(object_type, lang)
    page_type = page_obj['page_type']
    page_title = page_obj['page_title']
    

    if request.method == 'GET':
        permission = get_permission(
            object_type=object_type, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == 'hide':
            context = {
                'permission': permission,

                'page_type': page_type,
                'page_title': page_title,
            }
            return render(request, 'data/reports/reports-list.html', context)

        # Fix: AppSetting.objects.get() may raise DoesNotExist
        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=workspace, app_target=DASHBOARD_APP_TARGET)
        view_id = request.GET.get('view_id', None)

        # object_type = request.GET.get('object_type', 'panels')
        search_q = request.GET.get('q')

        views = get_ordered_views(workspace, object_type, user=request.user)
        view = None
        if view_id:
            view = View.objects.filter(id=view_id).first()
            if not view or (view.is_private and (not request.user or view.user != request.user)):
                view, _ = View.objects.get_or_create(
                    workspace=workspace, title__isnull=True, target=object_type)
                view_id = view.id
        else:
            view, _ = View.objects.get_or_create(
                workspace=workspace, title__isnull=True, target=object_type)
            view_id = view.id

        # Initialize view filter with default settings
        view_filter, is_new_view_filter = ViewFilter.objects.get_or_create(
            view=view)
        if is_new_view_filter:
            view_filter.view_type = 'list'
            # Set default columns based on object type
            view_filter.column = page_obj['default_columns']
            view_filter.save()
        elif not view_filter.column:
            # Ensure existing view filters have columns set
            view_filter.column = page_obj['default_columns']
            view_filter.save()

        config_view = view_filter.view_type

        panels = None
        reports_v2 = None
        # Force view filter application before template render
        filter_conditions = Q(workspace=workspace)

        filter_conditions &= get_permission_filter(
            permission, request.user
        )
        if object_type == 'panels':
            if search_q:
                filter_conditions = apply_search_setting(
                    page_obj['custom_model'], app_setting.search_setting_panel, TYPE_OBJECT_PANEL, search_q)

            # Ensure view filter is applied
            filter_conditions = apply_view_filter(
                filter_conditions, view_filter, object_type)
            print('............. Panels filter:', filter_conditions)

            panels_queryset = ReportPanel.objects.filter(
                filter_conditions, is_template=False, workspace=workspace).order_by('-created_at')
            if view_filter.sort_order_by:
                if view_filter.sort_order_method == 'asc':
                    panels_queryset = panels_queryset.distinct(
                        'id', view_filter.sort_order_by).order_by(view_filter.sort_order_by)
                else:
                    panels_queryset = panels_queryset.distinct(
                        'id', view_filter.sort_order_by).order_by('-'+view_filter.sort_order_by)
            paginator = Paginator(panels_queryset, MAX_PAGE_CONTENT)

            try:
                panels = paginator.page(page)
            except EmptyPage:
                panels = paginator.page(page)
        else:
            if search_q:
                filter_conditions = apply_search_setting(
                    page_obj['custom_model'], app_setting.search_setting_dashboard, TYPE_OBJECT_DASHBOARD, search_q)

            filter_conditions = apply_view_filter(
                filter_conditions, view_filter, object_type)
            print('............. Dashboard filter:', filter_conditions)

            reports_queryset = Report.objects.filter(filter_conditions,
                                                     workspace=workspace, channel=None, is_deleted=False).order_by('-created_at')
            paginator = Paginator(reports_queryset, MAX_PAGE_CONTENT)

            try:
                reports_v2 = paginator.page(page)
            except EmptyPage:
                page = paginator.num_pages
                reports_v2 = paginator.page(page)

        paginator_item_begin = (
            MAX_PAGE_CONTENT*int(page))-(MAX_PAGE_CONTENT-1)
        paginator_item_end = MAX_PAGE_CONTENT * \
            int(page) if MAX_PAGE_CONTENT * \
            int(page) < paginator.count else paginator.count

        channel_by_user = Channel.objects.filter(workspace=workspace).exclude(
            integration__slug__in=['facebook', 'gmail']).order_by('name')

        templates = DashboardTemplate.objects.filter(
            Q(workspace=workspace) | Q(hide=False)).order_by('-order')

        if view_filter.column and 'panel_id' in view_filter.column:
            col = view_filter.column
            col = col.replace("'panel_id', ", '')
            col = col.replace(", 'panel_id'", '')
            view_filter.column = col
            view_filter.save()

        try:
            columns = ast.literal_eval(
                view_filter.column) if view_filter and view_filter.column else page_obj['default_columns']
        except (ValueError, SyntaxError):
            # If literal_eval fails, try to parse as comma-separated string
            if isinstance(view_filter.column, list):
                columns = view_filter.column
            else:
                columns = [col.strip() for col in view_filter.column.split(
                    ',')] if view_filter and view_filter.column else page_obj['default_columns']

        print('............. object_type:', object_type)
        if object_type == 'dashboards':
            if lang == 'ja':
                page_title = 'ダッシュボード'
            else:
                page_title = 'Dashboards'
        else:
            if lang == 'ja':
                page_title = 'レポート'
            else:
                page_title = 'Reports'

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list('id', flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ','.join(str(id) for id in members)
        context = {
            'page_title': page_title,
            'app_slug': DASHBOARD_APP_SLUG,

            'permission': permission,
            'reports_v2': reports_v2,
            'channel_by_user': channel_by_user,
            'page': page,
            'panels': panels,
            'paginator': paginator,
            'paginator_item_begin': paginator_item_begin,
            'paginator_item_end': paginator_item_end,
            'workspace': workspace,
            'templates': templates,
            'template_type': DASHBOARD_TEMPLATE_TYPE,

            'config_view': config_view,
            'view_filter': view_filter,
            'view_id': view_id,
            'views': views,
            'current_view': view,

            'target': object_type,

            'search_q': search_q,
            'columns': columns,
            'group_members': members
        }
        return render(request, 'data/reports/reports-list.html', context)

    elif request.method == 'POST':
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PANEL]
        module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_PANEL).order_by(
            'order', 'created_at').first()
        module_slug = module.slug if module else None

        if "update-view-button" in request.POST:
            view_id = request.POST.get('view_id', None)
            view_name = request.POST.get("view_name", None)
            view_page = request.POST.get("view_page", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column_str = request.POST.get('column')
            order_by = request.POST.get('order-by')
            sort_method = request.POST.get('sort-method')
            id_field = page_obj['id_field']
            default_columns = page_obj['default_columns']

            module_object_slug = OBJECT_TYPE_TO_SLUG[view_page]
            module = Module.objects.filter(workspace=workspace, object_values__contains=view_page).order_by(
                'order', 'created_at').first()
            module_slug = module.slug if module else None

            columns = column_str.split(',') if column_str else default_columns
            if id_field and id_field not in columns:
                if object_type not in [TYPE_OBJECT_DASHBOARD]:
                    columns.insert(0, id_field)

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if view.is_private and (view.user == request.user):
                    view.is_private = bool(request.POST.get("set-private-view", False))
                    view.user = request.user if view.is_private else None
                    view.save()
            # create
            else:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                view = View.objects.create(
                    workspace=workspace,
                    title=view_name,
                    target=view_page,
                    is_private=is_private,
                    user=user
                )

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            if view_name:
                view.title = view_name

            if view_table:
                view_filter.view_type = view_table

            if archive:
                view_filter.archive = archive

            if columns:
                view_filter.column = columns

            if order_by:
                view_filter.sort_order_by = order_by
            else:
                view_filter.sort_order_by = None

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = 'asc'
                else:
                    view_filter.sort_order_method = None

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx]
                }
            view_filter.filter_value = filter_dictionary
            view_filter.save()
            view.save()

            if view.title:
                if module:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + "?view_id=" + str(view.id))
            else:
                if module:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
            return redirect(reverse('main', host='app'))

        elif "update-view-button-delete" in request.POST:
            view_id = request.POST.get('view_id', None)
            view_page = request.POST.get("view_page", None)
            module_object_slug = OBJECT_TYPE_TO_SLUG[view_page]
            module = Module.objects.filter(workspace=workspace, object_values__contains=view_page).order_by(
                'order', 'created_at').first()
            module_slug = module.slug if module else None
            if view_id:
                View.objects.get(id=view_id).delete()
            if module:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
        if module:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+"?object_type=panels")
        return redirect(reverse('main', host='app'))

@login_or_hubspot_required
def panel_export(request, panel_id):
    try:
        panel = ReportPanel.objects.get(id=panel_id)
    except Exception:
        return HttpResponseNotFound("Panel not found")

    lang = request.LANGUAGE_CODE
    response = HttpResponse(
        content_type='text/csv',
        headers={'Content-Disposition': f'attachment; filename="{panel.name}.csv"'}
    )

    writer = csv.writer(response)
    if panel.panel_type in ['chart', 'ltv_chart', 'custom', 'bar']:
        start_timestamp = int(request.GET.get(
            "since", (datetime.today() - timezone.timedelta(days=8)).timestamp()))
        end_timestamp = int(request.GET.get(
            "end", (datetime.today() - timezone.timedelta(days=1)).timestamp()))
        group_by = request.GET.get('group_by', None)
        is_realtime = request.GET.get('is_realtime')
        if is_realtime:
            is_realtime = True if is_realtime == 'true' else False

        start_date = datetime.fromtimestamp(start_timestamp, tz=timezone.utc)
        end_date = datetime.fromtimestamp(end_timestamp, tz=timezone.utc)

        chart_data = fetch_line_chart_data(
            panel, start_date, end_date, lang, group_by, is_realtime)

        col_names = ['Time']
        for y_axis in chart_data['y']:
            col_names.append(y_axis['label'])
        writer.writerow(col_names)

        metric_num = len(chart_data['y'])
        if metric_num > 0:
            for i, _ in enumerate(chart_data['y'][0]['data']):
                row = [chart_data['x'][i].replace(',', ' ')]
                for metric_id in range(metric_num):
                    row.append(chart_data['y'][metric_id]['data'][i])
                writer.writerow(row)

    elif panel.panel_type in ['table', 'summary_table']:
        if panel.panel_type == 'table':
            # Handle custom download columns
            custom_columns = {}
            for param in request.POST:
                # Columns
                if param.startswith('column-'):
                    id = param.split('-', 1)[1]
                    if not id:
                        continue

                    if id not in custom_columns:
                        custom_columns[id] = {'col': None, 'filters': None}

                    parts = request.POST[param].split(' - ')
                    custom_columns[id]['col'] = ' - '.join(parts[:2])

                # Filters
                elif param.startswith('filter-select-'):
                    id = param.split('-', 2)[2]
                    if not id:
                        continue

                    if id not in custom_columns:
                        custom_columns[id] = {'col': None, 'filters': None}

                    filter_select = request.POST.getlist(param)
                    filter_input = request.POST.getlist(f'filter-input-{id}')
                    filter_operator = request.POST.getlist(
                        f'filter-operator-{id}')
                    filter_type = request.POST.getlist(f'filter-type-{id}')
                    filter_source = request.POST.getlist(f'filter-source-{id}')
                    if request.POST.get(f'column-{id}') not in ['date', 'dates']:
                        if filter_input:
                            filter_input = [json.loads(json.dumps(
                                [{'value': val}])) for val in filter_input]

                    parsed_filters = []
                    for i, select in enumerate(filter_select):
                        try:
                            pf = {
                                'filter_select': select,
                                'filter_input': filter_input[i],
                            }
                            if panel.panel_type in ['chart', 'stacked_chart', 'funnel_chart', 'summary_table', 'forecast']:
                                pf['filter_operator'] = filter_operator[i]
                                pf['filter_type'] = filter_type[i]
                                pf['filter_source'] = filter_source[i]

                            parsed_filters.append(pf)
                        except Exception as e:
                            print(f'ERROR === reports.py - 1706: {e}')

                    custom_columns[id]['filters'] = parsed_filters

            custom_metrics = [item for item in custom_columns.values()]
            col_data, row_data, _ = fetch_panel_data(
                panel, custom_metrics=custom_metrics if len(custom_metrics) > 0 else None, lang=lang)
        else:
            col_data, row_data, _, _ = fetch_summaries_table(panel)
            col_data = [item['display'] for item in col_data]

        writer.writerow(col_data)
        sorted_rows = sorted(row_data.items())
        for _, row in sorted_rows:
            cleaned_row = [value.replace('"', '') if isinstance(
                value, str) else value for value in row.values()]
            writer.writerow(cleaned_row)

    elif panel.panel_type == 'funnel_chart':
        start_timestamp = int(request.GET.get(
            "since", (datetime.today() - timezone.timedelta(days=8)).timestamp()))
        end_timestamp = int(request.GET.get(
            "end", (datetime.today() - timezone.timedelta(days=1)).timestamp()))

        start_date = datetime.fromtimestamp(start_timestamp, tz=timezone.utc)
        end_date = datetime.fromtimestamp(end_timestamp, tz=timezone.utc)
        is_realtime = request.GET.get('is_realtime')
        if is_realtime:
            is_realtime = True if is_realtime == 'true' else False

        chart_data = fetch_funnel_chart_data(
            panel, start_date, end_date, lang, is_realtime)

        writer.writerow(['Funnel', 'Value'])

        total = chart_data['total']
        for i, funnel in enumerate(chart_data['y']):
            x_value = chart_data['x'][i]
            row = [funnel, (x_value[1]-x_value[0])*100/total]
            writer.writerow(row)

    return response

@login_or_hubspot_required
def panel_view(request, panel_id, page=1):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    paginator = None
    try:
        panel = ReportPanel.objects.get(id=panel_id)
    except ReportPanel.DoesNotExist:
        return redirect(reverse('report_reports', host='app'))

    if panel.workspace != workspace:
        return redirect(reverse('report_reports', host='app'))

    reports_v2 = Report.objects.filter(is_template=False,
                                       workspace=workspace, channel=None, is_deleted=False).order_by('name')

    if (request.GET.get("since") and request.GET.get('end')) or panel.start_time is None: 
        start_timestamp = int(request.GET.get(
            "since", (datetime.today() - timezone.timedelta(days=8)).timestamp()))
        end_timestamp = int(request.GET.get(
            "end", (datetime.today() - timezone.timedelta(days=1)).timestamp()))

        start_date = datetime.fromtimestamp(start_timestamp, tz=timezone.utc)
        end_date = datetime.fromtimestamp(end_timestamp, tz=timezone.utc)
        panel.start_time = start_date
        panel.end_time = end_date
        panel.save()
    else:
        start_date = panel.start_time
        end_date = panel.end_time

    group_by = request.GET.get('group_by', None)
    if group_by is None:
        group_by = panel.group_by or 'day'
    else:
        panel.group_by = group_by
        panel.save()

    is_realtime = request.GET.get('is_realtime')
    if is_realtime:
        is_realtime = True if is_realtime == 'true' else False

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PANEL]
    module_slug = request.POST.get('module')
    if not module_slug:
        module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_PANEL).order_by(
            'order', 'created_at').first()
        module_slug = module.slug if module else None
    menu_key = module_slug

    print('panel_id: ', panel_id)

    context = {
        'app_slug': DASHBOARD_APP_SLUG,
        'panel_id': panel_id,
        'page_type': 'reports_v2',
        'panel': panel,
        'reports_v2': reports_v2,
        'current_page': page,
        'prev_page': page-1,
        'next_page': page+1,
        'start_date': start_date,
        'end_date': end_date,
        'menu_key': menu_key,
        'module': menu_key,
        'object_type': TYPE_OBJECT_PANEL,
    }
    widget_id = request.GET.get('widget_id')
    if widget_id:
        widget = Widget.objects.filter(id=widget_id).first()
        if widget:
            context['widget'] = widget

    if panel.panel_type == 'table':
        col_data, row_data, paginator = fetch_panel_data(
            panel, page, lang=lang)
        context['col_data'] = col_data
        context['row_data'] = row_data
        context['paginator'] = paginator

    if panel.panel_type == 'pivot_table':
        # Detecting Changes in Date Range
        since = request.GET.get("since", None)
        end = request.GET.get("end", None)
        if not since and not end:
            start_date = panel.start_time if panel.start_time else start_date
            end_date = panel.end_time if panel.end_time else end_date

        if group_by:
            panel.group_by = group_by

        if panel.group_by:
            group_by = panel.group_by

        column_headers, row_headers, annotations, pivot_data, err, display_settings, is_row_date, custom_val_trans = fetch_panel_pivot_data(
            panel, start_date=start_date, end_date=end_date, group_by=group_by, lang=lang)

        if not is_row_date:
            panel.group_by = None
            panel.save()
        context['start_date'] = panel.start_time if panel.start_time else start_date
        context['end_date'] = panel.end_time if panel.end_time else end_date

        context['column_headers'] = column_headers
        context['row_headers'] = row_headers
        context['annotations'] = annotations
        context['pivot_data'] = pivot_data
        context['metric_type'] = 'normal'
        context['is_error'] = err
        context['display_settings'] = display_settings
        context['custom_val_trans'] = custom_val_trans

    elif panel.panel_type in ['chart', 'stacked_chart', 'funnel_chart', 'ltv_chart', 'custom', 'forecast', 'bar']:
        if panel.panel_type in ['chart', 'stacked_chart', 'ltv_chart', 'custom', 'forecast']:
            chart = fetch_line_chart_data(
                panel, start_date, end_date, lang=lang, group_by=group_by, is_realtime=is_realtime)
            context['chart'] = chart

        elif panel.panel_type == 'bar':
            chart = fetch_bar_chart_data(
                panel, start_date, end_date, lang=lang, group_by=group_by, is_realtime=is_realtime)
            context['chart'] = chart

        else:
            chart = fetch_funnel_chart_data(
                panel, start_date, end_date, lang=lang, is_realtime=is_realtime)
            context['chart'] = chart

        context['start_date'] = panel.start_time if panel.start_time else start_date
        context['end_date'] = panel.end_time if panel.end_time else end_date

    elif panel.panel_type == 'summary_table':
        panel.start_time = start_date
        panel.end_time = end_date
        panel.save()

        context['start_date'] = start_date
        context['end_date'] = end_date
        context['metrics'] = panel.metrics.filter(role='y_axis')

    elif panel.panel_type == 'sheet':
        # data = fetch_sheet_data(panel, lang, group_by)
        data, currency_rows, currency_cols, hidden_rows, merge_cells, color_cells, image_cell, edited_cell, tabs = fetch_sheet_data_v3(
            panel, lang, group_by)
        context['panel'] = panel
        context['data'] = data
        context['currency_rows'] = currency_rows
        context['currency_cols'] = currency_cols
        context['hidden_rows'] = hidden_rows
        context['merge_cells'] = merge_cells
        context['color_cells'] = color_cells
        context['image_cell'] = image_cell
        context['edited_cell'] = edited_cell
        context['tabs'] = tabs

    elif panel.panel_type == 'cohort_chart':
        cohort_data, cohort_header, maximum_value, is_percentage = fetch_cohort_chart_data(panel, start_date, end_date, lang, group_by, is_realtime=is_realtime)
        context['data'] = cohort_data
        context['cohort_header'] = cohort_header
        context['start_date'] = panel.start_time if panel.start_time else start_date
        context['end_date'] = panel.end_time if panel.end_time else end_date
        context['maximum_value'] = maximum_value
        context['is_percentage'] = is_percentage

    if request.GET.get('render_widget', None):
        context['render_widget'] = True
        context['render_dashboard'] = True

        return render(request, 'data/reports/report-panel-view.html', context)

    return render(request, 'data/reports/panel-view.html', context)


@login_or_hubspot_required
def report_view(request, report_id, page=1):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if lang == 'ja':
        page_title = 'レポート'
    else:
        page_title = 'Reports'

    page = int(request.GET.get("page", 1))
    report = Report.objects.get(workspace=workspace, id=report_id)
    panels = report.get_panels()
    paginator = Paginator(panels, 2)
    page_obj = paginator.get_page(page)

    reports_v2 = Report.objects.filter(is_template=False,
                                       workspace=workspace, channel=None, is_deleted=False).order_by('name')

    module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_DASHBOARD).order_by(
        'order', 'created_at').first()
    module_slug = module.slug if module else None

    context = {
        'page_title': page_title,
        'menu_key': module_slug,
        'object_type': TYPE_OBJECT_DASHBOARD,

        'app_slug': DASHBOARD_APP_SLUG,

        'page_type': 'reports_v2',

        'panels': page_obj.object_list,

        'report': report,
        'reports_v2': reports_v2,
        'has_next': page_obj.has_next(),
        'next_page': page + 1,
    }
    return render(request, 'data/reports/report-view.html', context)


def panels_list_view(request, report_id):
    workspace = get_workspace(request.user)
    page = int(request.GET.get("page", 1))
    report = Report.objects.get(workspace=workspace, id=report_id)
    panels = report.get_panels()
    paginator = Paginator(panels, 2)
    page_obj = paginator.get_page(page)

    return render(request, "data/reports/report-view-panel-partial.html", {
        "report": report,
        "panels": page_obj.object_list,
        "has_next": page_obj.has_next(),
        "page": page,
        "next_page": page + 1,
    })

@login_or_hubspot_required
def report_panel_view(request, report_id, panel_id, page=1):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    paginator = None

    try:
        report = Report.objects.get(id=report_id, workspace=workspace)
        panel = ReportPanel.objects.get(
            panelreportpanel__report_id=report_id, id=panel_id, workspace=workspace)
    except:
        return redirect(reverse('report_reports', host='app'))

    context = {
        'report': report,
        'panel': panel,
        'current_page': page,
        'prev_page': page-1,
        'next_page': page+1,
    }

    if panel.panel_type in ['table']:
        if panel.panel_type == 'table':
            col_data, row_data, paginator = fetch_panel_data(
                panel, page, lang=lang)
            context['paginator'] = paginator
        else:
            col_data, row_data, _, _ = fetch_summaries_table(panel)
            context['summary_fields'] = SUMMARY_FIELDS

        context['col_data'] = col_data
        context['row_data'] = row_data

    elif panel.panel_type in ['chart', 'funnel_chart', 'ltv_chart', 'custom', 'summary_table', 'bar']:
        start_timestamp = int(request.GET.get(
            "since", (datetime.today() - timezone.timedelta(days=8)).timestamp()))
        end_timestamp = int(request.GET.get(
            "end", (datetime.today() - timezone.timedelta(days=1)).timestamp()))

        start_date = datetime.fromtimestamp(start_timestamp, tz=timezone.utc)
        end_date = datetime.fromtimestamp(end_timestamp, tz=timezone.utc)
        group_by = request.GET.get('group_by', None)
        if not panel.group_by:
            group_by = panel.group_by

        if panel.panel_type in ['chart', 'ltv_chart', 'custom']:
            chart = fetch_line_chart_data(panel, start_date, end_date, lang)
            context['chart'] = chart
        elif panel.panel_type == 'summary_table':
            start_date = datetime.fromtimestamp(
                start_timestamp, tz=timezone.utc)
            end_date = datetime.fromtimestamp(end_timestamp, tz=timezone.utc)
            context['start_date'] = start_date
            context['end_date'] = end_date
            context['metrics'] = panel.metrics.filter(role='y_axis')
        elif panel.panel_type == 'bar':
            chart = fetch_bar_chart_data(
                panel, start_date, end_date, lang=lang, group_by=group_by)
            context['chart'] = chart
        else:
            chart = fetch_funnel_chart_data(panel, start_date, end_date, lang)
            context['chart'] = chart
    elif panel.panel_type == 'pivot_table':
        if panel.start_time and panel.end_time:
            start_date = panel.start_time
            end_date = panel.end_time
        else:
            start_date = datetime.fromtimestamp(
                (datetime.today() - timezone.timedelta(days=8)).timestamp(), tz=timezone.utc)
            end_date = datetime.fromtimestamp(
                (datetime.today() - timezone.timedelta(days=1)).timestamp(), tz=timezone.utc)

            start_date = panel.start_time if panel.start_time else start_date
            end_date = panel.end_time if panel.end_time else end_date

        column_headers, row_headers, annotations, pivot_data, err, display_settings, is_row_date, custom_val_trans = fetch_panel_pivot_data(
            panel, start_date=start_date, group_by=panel.group_by, end_date=end_date, lang=lang)
        if not is_row_date:
            panel.group_by = None
            panel.save()
        context['column_headers'] = column_headers
        context['row_headers'] = row_headers
        context['annotations'] = annotations
        context['pivot_data'] = pivot_data
        context['is_error'] = err
        context['display_settings'] = display_settings
        context['custom_val_trans'] = custom_val_trans

    elif panel.panel_type == 'sheet':
        # data = fetch_sheet_data(panel, lang, group_by)
        data, currency_rows, currency_cols, hidden_rows, merge_cells, color_cells, image_cell, edited_cell, tabs = fetch_sheet_data_v3(
            panel, lang, None)
        context['panel'] = panel
        context['data'] = data
        context['currency_rows'] = currency_rows
        context['currency_cols'] = currency_cols
        context['hidden_rows'] = hidden_rows
        context['merge_cells'] = merge_cells
        context['color_cells'] = color_cells
        context['image_cell'] = image_cell
        context['edited_cell'] = edited_cell
        context['tabs'] = tabs
        context['render_dashboard'] = True

    elif panel.panel_type == 'cohort_chart':
        group_by = request.GET.get('group_by', None)
        if not panel.group_by:
            group_by = panel.group_by
        cohort_data, cohort_header, maximum_value, is_percentage = fetch_cohort_chart_data(panel, panel.start_time, panel.end_time, lang, group_by)
        context['data'] = cohort_data
        context['cohort_header'] = cohort_header
        context['start_date'] = panel.start_time if panel.start_time else start_date
        context['end_date'] = panel.end_time if panel.end_time else end_date
        context['maximum_value'] = maximum_value
        context['is_percentage'] = is_percentage
        context['render_dashboard'] = True

    return render(request, 'data/reports/report-panel-view.html', context)

def template(request):
    if request.method == 'GET':
        reports_queryset = Report.objects.filter(is_template=True, is_deleted=False).order_by('-created_at')
        
        context = {
            'reports': reports_queryset
        }
        return render(request, 'data/reports/dashboard-template.html', context)

    report_id = request.POST.get('report_id', None)
    if not report_id:
        return HttpResponse(status=400)

    workspace = get_workspace(request.user)
    user = request.user
    
    old_report = Report.objects.get(id=report_id)
    old_report.id = None
    old_report.workspace = workspace
    old_report.created_by = user
    old_report.updated_by = user
    old_report.is_template = False
    old_report.created_at = timezone.now()
    old_report.updated_at = timezone.now()
    old_report.save()
    new_report = old_report
    
    old_panel_links = PanelReportPanel.objects.filter(report_id=report_id)
    for link in old_panel_links:
        old_panel = link.panel
        cloned_metrics = []
        for m in old_panel.metrics.all():
            m.pk = None
            m.save()
            cloned_metrics.append(m)
        channels = old_panel.comparison_channel.all()

        old_panel_id = old_panel.id
        old_panel.id = None
        old_panel.workspace = workspace
        old_panel.is_template = False
        old_panel.end_time = timezone.now()
        old_panel.start_time = timezone.now() - timedelta(days=30)
        old_panel.created_by = user
        old_panel.created_at = timezone.now()
        old_panel.updated_at = timezone.now()
        old_panel.save()
        new_panel = old_panel

        new_panel.metrics.set(cloned_metrics)
        new_panel.comparison_channel.set(channels)

        PanelReportPanel.objects.create(report=new_report, panel=new_panel, order=link.order)

        sheets = ReportPanelSheet.objects.filter(report_panel_id=old_panel_id)
        tab_map = {}
        old_tabs = ReportPanelSheetTab.objects.filter(report_panel_id=old_panel_id)
        
        for tab in old_tabs:
            old_tab_id = tab.id
            tab.id = None  # force insert
            tab.report_panel = new_panel
            tab.save()
            tab_map[old_tab_id] = tab
            
        sheet_clones = [
            ReportPanelSheet(
                report_panel=new_panel,
                tab=tab_map.get(sheet.tab_id),
                col=sheet.col,
                row=sheet.row,
                value=sheet.value,
            )
            for sheet in sheets
        ]
        ReportPanelSheet.objects.bulk_create(sheet_clones)

        rows = ReportPanelSheetRows.objects.filter(report_panel_id=old_panel_id)
        row_clones = [
            ReportPanelSheetRows(
                report_panel=new_panel,
                rows_source=row.rows_source,
                rows_display=row.rows_display,
                grouped_date_field=row.grouped_date_field,
                rows_formula=row.rows_formula,
                filter=row.filter,
            )
            for row in rows
        ]
        ReportPanelSheetRows.objects.bulk_create(row_clones)
    
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_DASHBOARD]
    module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_DASHBOARD).order_by(
        'order', 'created_at').first()
    module_slug = module.slug if module else None
    
    if module:
        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+"?object_type=panels")
    return redirect(reverse('main', host='app'))

def panels_templates(request):
    if request.method == 'GET':
        panel_queryset = ReportPanel.objects.filter(is_template=True, is_deleted=False).order_by('-created_at')
        
        context = {
            'templates': panel_queryset
        }
        return render(request, 'data/reports/panel-template.html', context)

    template_id = request.POST.get('template_id', None)
    if not template_id:
        return HttpResponse(status=400)

    workspace = get_workspace(request.user)
    user = request.user
    
    panel = ReportPanel.objects.get(id=template_id)
    old_panel = panel
    cloned_metrics = []
    for m in old_panel.metrics.all():
        m.pk = None
        m.save()
        cloned_metrics.append(m)
    channels = old_panel.comparison_channel.all()

    old_panel_id = old_panel.id
    old_panel.id = None
    old_panel.workspace = workspace
    old_panel.is_template = False
    old_panel.end_time = timezone.now()
    old_panel.start_time = timezone.now() - timedelta(days=30)
    old_panel.created_by = user
    old_panel.created_at = timezone.now()
    old_panel.updated_at = timezone.now()
    old_panel.save()
    new_panel = old_panel

    new_panel.metrics.set(cloned_metrics)
    new_panel.comparison_channel.set(channels)

    sheets = ReportPanelSheet.objects.filter(report_panel_id=old_panel_id)
    tab_map = {}
    old_tabs = ReportPanelSheetTab.objects.filter(report_panel_id=old_panel_id)
    
    for tab in old_tabs:
        old_tab_id = tab.id
        tab.id = None  # force insert
        tab.report_panel = new_panel
        tab.save()
        tab_map[old_tab_id] = tab
        
    sheet_clones = [
        ReportPanelSheet(
            report_panel=new_panel,
            tab=tab_map.get(sheet.tab_id),
            col=sheet.col,
            row=sheet.row,
            value=sheet.value,
        )
        for sheet in sheets
    ]
    ReportPanelSheet.objects.bulk_create(sheet_clones)

    rows = ReportPanelSheetRows.objects.filter(report_panel_id=old_panel_id)
    row_clones = [
        ReportPanelSheetRows(
            report_panel=new_panel,
            rows_source=row.rows_source,
            rows_display=row.rows_display,
            grouped_date_field=row.grouped_date_field,
            rows_formula=row.rows_formula,
            filter=row.filter,
        )
        for row in rows
    ]
    ReportPanelSheetRows.objects.bulk_create(row_clones)
    
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PANEL]
    module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_PANEL).order_by(
        'order', 'created_at').first()
    module_slug = module.slug if module else None
    
    if module:
        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+"?object_type=panels")
    return redirect(reverse('main', host='app'))