
{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}

{% block content %}


<div id="container--{{uuid}}" class="mb-5">
    <label class="mb-2 d-flex align-items-center fs-6 fw-bold">
        <span>
            {% if app_type == "orders"  %}
                {% if data_filter|search_custom_field_object_orders:request %}
                    {% with channel_column=data_filter|search_custom_field_object_orders:request %}
                        {{channel_column.name}}
                    {% endwith %}
                {% elif data_filter|split:'|'|length > 1 %}
                    {% with column_display=data_filter|split:'|'|search_custom_field_object_order_customer:request %}
                        {{column_display.name}}
                    {% endwith %}
                {% else %}
                    {% with column_display=data_filter|display_column_orders:request %}
                        {{column_display}}
                    {% endwith %}
                {% endif %}
            {% elif app_type == "items"  %}
                {% if data_filter|search_custom_field_object_items:request %}
                    {% with channel_column=data_filter|search_custom_field_object_items:request %}
                        {{channel_column.name}}
                    {% endwith %}
                {% else %}
                    {% with column_display=data_filter|display_column_items:request %}
                        {% if data_filter == 'tax' %}
                            {{column_display}} (%)
                        {% else %}
                            {{column_display}}
                        {% endif %}
                    {% endwith %}
                {% endif %}
            {% elif app_type == "subscriptions"  %}
                {% if data_filter|search_custom_field_object_subscriptions:request %}
                    {% with channel_column=data_filter|search_custom_field_object_subscriptions:request %}
                        {{channel_column.name}}
                    {% endwith %}
                {% else %}
                    {% with column_display=data_filter|display_column_subscriptions:request %}
                        {{column_display}}
                    {% endwith %}
                {% endif %}
            {% elif app_type == "inventory"  %}
                {% if data_filter|search_custom_field_object_inventory:request %}
                    {% with channel_column=data_filter|search_custom_field_object_inventory:request %}
                        {{channel_column.name}}
                    {% endwith %}
                {% else %}
                    {% with column_display=data_filter|display_column_inventory:request %}
                        {{column_display}}
                    {% endwith %}
                {% endif %}
            {% elif app_type == "inventory-transaction"  %}
                {% if data_filter|search_custom_field_object_inventory_transaction:request %}
                    {% with channel_column=data_filter|search_custom_field_object_inventory_transaction:request %}
                        {{channel_column.name}}
                    {% endwith %}
                {% else %}
                    {% with column_display=data_filter|display_column_inventory_transaction:request %}
                        {{column_display}}
                    {% endwith %}
                {% endif %}
            {% elif app_type == "deals"  %}
                {% if data_filter|search_custom_field_object_deals:request %}
                    {% with channel_column=data_filter|search_custom_field_object_deals:request %}
                        {{channel_column.name}}
                    {% endwith %}
                {% else %}
                    {% with column_display=data_filter|display_column_deals:request %}
                        {{column_display}}
                    {% endwith %}
                {% endif %}
            {% elif app_type == "panels" %}
                {% with column_display=data_filter|display_column_panels:request %}
                    {{column_display}}
                {% endwith %}

            {% endif %}
    </label>

    {% if data_filter|in_list:'item_id,order_id,inventory_id' %}
        <span class="fw-bolder d-none">
            <input
                type="file"
                id="csvFileInput-{{uuid}}"
                name="csv_file"
                accept=".csv"
                hx-post="{% host_url 'csv_id_upload' host 'app' %}"
                hx-vals='{"data_filter": "{{data_filter}}","data_type": "{{data_type}}","target": "{{uuid}}", "app_type": "{{app_type}}"}'
                hx-encoding="multipart/form-data"
                hx-trigger="change"
                hx-target="#container--{{uuid}}"
                hx-swap="outerHTML"
                hx-on:htmx:before-send="document.getElementById('container--{{uuid}}').innerHTML = ''"
            >
        </span>
    {% endif %}
    <div class="mb-5 d-flex justify-content-between">
        {% if is_advanced_search %}
        <input id="filter_checkbox-{{uuid}}" type="checkbox" name="filter_checkbox" {% if filter_status %} checked {% endif %} onchange="handleCheckbox(this)" class="mt-2 me-3 form-check-input">
        <input type="hidden" name="filter_status" value="{% if filter_status %}{{filter_status}}{% else %}False{% endif %}">
        <script>
            $(document).ready(function() {
                const filterCheckbox = document.getElementById('filter_checkbox-{{uuid}}');
                handleCheckbox(filterCheckbox);
            });
            function handleCheckbox(checkbox) {
                // Get the parent element of the checkbox
                var parent = checkbox.parentNode;
                // Find the next input element within the parent
                var nextInput = parent.querySelector('input[type="hidden"]');
                if (!checkbox.checked) {
                    nextInput.value = "False";
                } else {
                    nextInput.value = "True";
                }

                var filterTypesInput = parent.closest('div').querySelector('[name="filter_type"]');
                var filterOptionsInput = parent.closest('div').querySelector('[name="filter_options"]');
                var filterValueInput = parent.closest('div').querySelector('[name="filter_value"]');
                var customerFilterValueInput = parent.closest('div').querySelector('[name="customer_filter_value"]');

                if (filterValueInput) {
                    filterTypesInput.disabled = !checkbox.checked;
                    filterOptionsInput.disabled = !checkbox.checked;
                    filterValueInput.disabled = !checkbox.checked;

                    if (customerFilterValueInput) {
                        customerFilterValueInput.disabled = !checkbox.checked;
                        if (!checkbox.checked) {
                            customerFilterValueInput.classList.remove('bg-white');
                            customerFilterValueInput.multiple = false;
                            if ($(customerFilterValueInput).hasClass('select2-hidden-accessible')) {
                                $(customerFilterValueInput).select2('destroy'); // Destroy select2 instance
                            }
                        } else {
                            customerFilterValueInput.classList.add('bg-white');
                            customerFilterValueInput.multiple = true;
                            if (!$(customerFilterValueInput).hasClass('select2-hidden-accessible')) {
                                $(customerFilterValueInput).select2(); // Reinitialize select2
                            }
                        }
                    } else {
                        if (!checkbox.checked) {
                            filterOptionsInput.classList.remove('bg-white');
                            filterValueInput.classList.remove('bg-white');
                            filterOptionsInput.style.width = '93%';
                            if ($(filterOptionsInput).hasClass('select2-hidden-accessible')) {
                                $(filterOptionsInput).select2('destroy'); // Destroy select2 instance
                            }
                            if ($(filterValueInput).hasClass('select2-hidden-accessible')) {
                                $(filterValueInput).select2('destroy'); // Destroy select2 instance
                            }
                        } else {
                            filterOptionsInput.classList.add('bg-white');
                            filterValueInput.classList.add('bg-white');
                            filterOptionsInput.style.width = '100%';
                            if (!$(filterOptionsInput).hasClass('select2-hidden-accessible') && !filterOptionsInput.hidden) {
                                $(filterOptionsInput).select2(); // Reinitialize select2
                            }
                            if (filterValueInput.tagName === 'SELECT') {
                                if (!$(filterValueInput).hasClass('select2-hidden-accessible')) {
                                    $(filterValueInput).select2(); // Reinitialize select2
                                }
                            }
                        }
                    }

                    // Ensure the values are still sent by creating hidden inputs
                    if (!checkbox.checked) {
                        if (!parent.querySelector('[name="filter_type_hidden"]')) {
                            var hiddenFilterType = document.createElement('input');
                            hiddenFilterType.type = 'hidden';
                            hiddenFilterType.name = 'filter_type_hidden';
                            hiddenFilterType.value = filterTypesInput.value;
                            parent.appendChild(hiddenFilterType);
                        }
                        if (!parent.querySelector('[name="filter_options_hidden"]')) {
                            var hiddenFilterOptions = document.createElement('input');
                            hiddenFilterOptions.type = 'hidden';
                            hiddenFilterOptions.name = 'filter_options_hidden';
                            hiddenFilterOptions.value = filterOptionsInput.value;
                            parent.appendChild(hiddenFilterOptions);
                        }
                        if (!parent.querySelector('[name="filter_value_hidden"]')) {
                            var hiddenFilterValue = document.createElement('input');
                            hiddenFilterValue.type = 'hidden';
                            hiddenFilterValue.name = 'filter_value_hidden';
                            hiddenFilterValue.value = filterValueInput.value;
                            parent.appendChild(hiddenFilterValue);
                        }
                    } else {
                        // Remove hidden inputs when checkbox is checked
                        var hiddenInputs = parent.querySelectorAll('[name$="_hidden"]');
                        hiddenInputs.forEach(function(input) {
                            input.remove();
                        });
                    }
                }
            }
        </script>
        {% endif %}

        <div class="d-flex w-100">
            <input hidden name="filter_type" {% if data_filter %} value="{{data_filter}}" {% endif %}/>
            {% if not field_choice or data_filter|in_list:'warehouse' %}
                {% if data_filter == 'owner' %}
                {% with selected_filter_values=predefined_data_filter.value|split:',' %}
                    <input hidden name="filter_options" {% if data_filter %} value="is" {% endif %}/>
                    <select class="me-3 {% include 'data/utility/select-form.html' %} w-auto select2-this" 
                    name="filter_value"
                    data-placeholder="{% if LANGUAGE_CODE == 'ja' %}ユーザーを選択します{% else %}Select User{% endif %}">
                        <option value="myself">{% if LANGUAGE_CODE == 'ja' %}自分自身{% else %}Myself{% endif %}</option>
                        
                        {% for user in workspace.user.all %}
                            <option value="{{user.id}}"
                            {% if user.id|stringify in selected_filter_values %} selected {% endif %}
                            >{{user.first_name}} {% if user.email %}- {{user.email}} {% endif %}</option>
                        {% endfor %} 
                    </select>
                {% endwith %}
                {% else %}
                <select

                    {% if data_filter != 'item_id' and data_filter != 'inventory_id'%}
                        {% if app_type == 'panels' %}
                            hx-get="{% host_url 'report_views_drawer' host 'app' %}"
                        {% else %}
                            hx-get="{% host_url 'shopturbo_load_drawer' host 'app' %}"
                        {% endif %}


                        hx-target="#container--{{uuid}}"
                        hx-trigger="htmx-change{% if not filter_options %},load{% endif %}"
                        hx-swap="outerHTML"

                        {% if app_type == 'orders' %}
                        hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "data_filter":"{{data_filter}}", "app_type":"orders", "is_advanced_search":"{{is_advanced_search}}", "filter_status":"{{filter_status}}"}'
                        {% elif app_type == 'items' %}
                        hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "data_filter":"{{data_filter}}", "app_type":"items", "is_advanced_search":"{{is_advanced_search}}", "filter_status":"{{filter_status}}"}'
                        {% elif app_type == 'subscriptions' %}
                        hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "data_filter":"{{data_filter}}", "app_type":"subscriptions", "is_advanced_search":"{{is_advanced_search}}", "filter_status":"{{filter_status}}"}'
                        {% elif app_type == 'inventory' %}
                        hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "data_filter":"{{data_filter}}", "app_type":"inventory", "is_advanced_search":"{{is_advanced_search}}", "filter_status":"{{filter_status}}"}'
                        {% elif app_type == 'inventory-transaction' %}
                        hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "data_filter":"{{data_filter}}", "app_type":"inventory-transaction", "is_advanced_search":"{{is_advanced_search}}", "filter_status":"{{filter_status}}"}'
                        {% elif app_type == 'deals' %}
                        hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "data_filter":"{{data_filter}}", "app_type":"deals", "is_advanced_search":"{{is_advanced_search}}", "filter_status":"{{filter_status}}"}'
                        {% elif app_type == 'panels' %}
                        hx-vals='{"drawer_type":"report-view-settings","type":"advance-filter", "data_filter":"{{data_filter}}", "app_type":"panels"}'
                        {% endif %}
                    {% endif %}
                    id="filter-type-{{uuid}}"
                    class="{% include 'data/utility/select-form.html' %} select2-this {% if filter_options != 'is_empty' and filter_options != 'is_not_empty' %}input-prehead{% endif %}"
                    name="filter_options"
                    data-placeholder="value">
                        {% for rule in data_type|get_filtering_rule %}
                            <option value="{{rule}}" {% if predefined_data_filter.key == rule or filter_options == rule %}selected{% endif %}>{% translate_lang rule|display_filter_rule:LANGUAGE_CODE|title LANGUAGE_CODE %}</option>
                        {% endfor %}
                    
                        {% if data_filter|in_list:'item_id,order_id,inventory_id' %}   
                            {% if data_filter|in_list:'item_id,inventory_id' %}
                            <option value="csv_contains" {% if predefined_data_filter.key == 'csv_contains' or filter_options == 'csv_contains' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}CSVの値{% else %}CSV Value{% endif %}</option>
                            {% endif %}
                        {% endif %}
                    
                </select>                    
                {% endif %}
            {% else %}
            <input hidden name="filter_options" value="is"/>
            {% endif %}
            <script>

                $('#filter-type-{{uuid}}').on('select2:close', function (e) {

                    var filter_value = document.getElementById('{{uuid}}');
                    if (filter_value){filter_value.value = '';}
  
                    var selectElement = $(this).closest('select').get(0);
                    elemen = selectElement
                    console.log(elemen.value);
                    if (elemen.value == 'csv_contains'){
                        document.getElementById('csvFileInput-{{uuid}}').click();
                    }
                });

            </script>
            {% if app_type == "orders"  %}
                {% if data_filter|search_custom_field_object_orders:request %}
                    {% with channel_column=data_filter|search_custom_field_object_orders:request %}
                        {% if channel_column.type == "choice" or channel_column.type == "user"  %}
                            {% include "data/shopturbo/list-data-filter-selector-choice.html" %}
                        {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                            <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                                {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                            />
                        {% else %}
                            <input hidden name="filter_value">
                        {% endif %}
                    {% endwith %}
                {% else %}
                    {% if data_filter == 'customer' %}
                        <input hidden name="filter_value" value="placeholder_customer_filter_value"/>
                        <select id="{{uuid}}" multiple class="bg-white border min-h-40px form-select form-select-solid select2-this" name="customer_filter_value"
                        {% if LANGUAGE_CODE == 'ja' %}
                        data-placeholder="顧客を選択"
                        {% else %}
                        data-placeholder="Select Customer"
                        {% endif %}>
                            {% for contact in contact_choices %}
                                {% if contact.id|to_str in predefined_data_filter.value %}
                                <option value="{{contact.id}}" selected>
                                    #{{contact.contact_id|stringformat:"04d"}} | {{contact|display_contact_name:LANGUAGE_CODE}} ({% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %})
                                </option>
                                {% else %}
                                <option value="{{contact.id}}" >
                                    #{{contact.contact_id|stringformat:"04d"}} | {{contact|display_contact_name:LANGUAGE_CODE}} ({% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %})
                                </option>
                                {% endif %}
                            {% endfor %}
                            {% for company in company_choices %}
                                {% if company.id|to_str in predefined_data_filter.value %}
                                <option value="{{company.id}}" selected>
                                    #{{company.company_id|stringformat:"04d"}} | {{company.name}} ({% if LANGUAGE_CODE == 'ja'%}企業{% else %}Company{% endif %})
                                </option>
                                {% else %}
                                <option value="{{company.id}}" >
                                    #{{company.company_id|stringformat:"04d"}} | {{company.name}} ({% if LANGUAGE_CODE == 'ja'%}企業{% else %}Company{% endif %})
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    {% elif field_choice %}
                        <select id="{{uuid}}" class="bg-white border min-h-40px form-select form-select-solid select2-this" name="filter_value" data-placeholder="value">
                            {% for choice_value in field_choice %}
                                {% if predefined_data_filter.value == choice_value.0 %}
                                <option value="{{choice_value.0}}" selected>
                                    {{choice_value.0|display_status_orders:request}}
                                </option>
                                {% else %}
                                <option value="{{choice_value.0}}" >
                                    {{choice_value.0|display_status_orders:request}}
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' and data_filter != 'owner' %}
                        <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                            {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                        />
                    {% else %}
                        <input hidden name="filter_value">
                    {% endif %}
                {% endif %}

            {% elif app_type == "items"  %}
                {% if data_filter|search_custom_field_object_items:request %}
                    {% with channel_column=data_filter|search_custom_field_object_items:request %}
                        {% if channel_column.type == "choice" or channel_column.type == "user" %}
                            {% include "data/shopturbo/list-data-filter-selector-choice.html" %}
                        {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                            <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                                {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                            />
                        {% else %}
                            <input hidden name="filter_value">
                        {% endif %}
                    {% endwith %}
                {% else %}
                    {% if field_choice %}
                        <select id="{{uuid}}" class="bg-white border min-h-40px form-select form-select-solid select2-this" name="filter_value" data-placeholder="value">
                            {% for choice_value in field_choice %}
                                {% if predefined_data_filter.value == choice_value.0 %}
                                <option value="{{choice_value.0}}" selected>
                                    {{choice_value.0|display_column_items:request}}
                                </option>
                                {% else %}
                                <option value="{{choice_value.0}}" >
                                    {{choice_value.0|display_column_items:request}}
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' and data_filter != 'owner' %}
                        <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                            {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                        />
                    {% else %}
                        <input hidden name="filter_value">
                    {% endif %}
                {% endif %}


            {% elif app_type == "subscriptions"  %}
                {% if data_filter|search_custom_field_object_subscriptions:request %}
                    {% with channel_column=data_filter|search_custom_field_object_subscriptions:request %}
                        {% if channel_column.type == "choice" or channel_column.type == "user" %}
                            {% include "data/shopturbo/list-data-filter-selector-choice.html" %}
                        {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                            <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                                {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                            />
                        {% else %}
                            <input hidden name="filter_value">
                        {% endif %}
                    {% endwith %}
                {% else %}
                    {% if field_choice %}
                        <select id="{{uuid}}" class="bg-white border min-h-40px form-select form-select-solid select2-this" name="filter_value" data-placeholder="value">
                            {% for choice_value in field_choice %}
                                {% if predefined_data_filter.value == choice_value.0 %}
                                    <option value="{{choice_value.0}}" selected>
                                        {% if 'subscription_platform|' in data_filter %}
                                            {{choice_value.1}}
                                        {% else %}
                                            {{choice_value.0|display_column_subscriptions:request}}
                                        {% endif %}
                                    </option>
                                {% else %}
                                    <option value="{{choice_value.0}}" >
                                        {% if 'subscription_platform|' in data_filter %}
                                            {{choice_value.1}}
                                        {% else %}
                                            {{choice_value.0|display_column_subscriptions:request}}
                                        {% endif %}
                                    </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' and data_filter != 'owner' %}
                        <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                            {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                        />
                    {% else %}
                        <input hidden name="filter_value">
                    {% endif %}
                {% endif %}
            {% elif app_type == "inventory"  %}
                {% if data_filter|search_custom_field_object_inventory:request %}
                    {% with channel_column=data_filter|search_custom_field_object_inventory:request %}
                        {% if channel_column.type == "choice" %}
                            {% include "data/shopturbo/list-data-filter-selector-choice.html" %}
                        {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                            <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                                {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                            />
                        {% else %}
                            <input hidden name="filter_value">
                        {% endif %}
                    {% endwith %}
                {% else %}
                    {% if data_filter == 'warehouse' %}
                        <input type="hidden" name="filter_value" value="{{ predefined_data_filter.value }}">
                        {% if filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                            <select id="{{uuid}}" class="bg-white border min-h-40px form-select form-select-solid select2-this input-posthead "
                            data-placeholder="value" {% if filter_options|in_list:'includes,excludes' %} multiple {% endif %}
                            onchange="updateSelectInput(this);">
                            <option value=""></option>
                                {% for choice_value in field_choice %}
                                    {% if predefined_data_filter.value == choice_value.0 %}
                                    <option value="{{choice_value.0}}" selected>
                                        {{choice_value.1}}
                                    </option>
                                    {% else %}
                                    <option value="{{choice_value.0}}" >
                                        {{choice_value.1}}
                                    </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        {% endif%}
                        <script>
                            function updateSelectInput(elm){
                                const values = $(elm).val()
                                {% if filter_options|in_list:'includes,excludes' %}
                                    $(elm).parent().find('input[name="filter_value"]').val(values.join(','))
                                {% else %}
                                    $(elm).parent().find('input[name="filter_value"]').val(values)
                                {% endif %}
                            }
                        </script>
                    {% elif field_choice %}
                        <select id="{{uuid}}" class="bg-white border min-h-40px form-select form-select-solid select2-this" name="filter_value" data-placeholder="value">
                            {% for choice_value in field_choice %}
                                {% if predefined_data_filter.value == choice_value.0 %}
                                <option value="{{choice_value.0}}" selected>
                                    {{choice_value.0|display_column_inventory:request}}
                                </option>
                                {% else %}
                                <option value="{{choice_value.0}}" >
                                    {{choice_value.0|display_column_inventory:request}}
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    {% elif filter_options == 'between' %}
                        <div class="w-100 mb-3 input-group">
                            <input type="number" class="form-control rounded-0 h-40px" name="filter_value" required {% if predefined_data_filter.value %}value="{{predefined_data_filter.value|split:'-'|first}}"{% endif %}>
                            <span class="input-group-text h-40px">-</span>
                            <input type="number" class="form-control rounded-end h-40px" name="filter_value-2" required {% if predefined_data_filter.value %}value="{{predefined_data_filter.value|split:'-'|last}}"{% endif %}>
                        </div>
                    {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' and data_filter != 'owner'  %}
                        <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                            {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                        />
                    {% else %}
                        <input hidden name="filter_value">
                    {% endif %}
                {% endif %}
            {% elif app_type == 'inventory-transaction'  %}
                {% if data_filter|search_custom_field_object_inventory_transaction:request %}
                    {% with channel_column=data_filter|search_custom_field_object_inventory_transaction:request %}
                        {% if channel_column.type == "choice" or data_filter == "user" %}
                            {% include "data/shopturbo/list-data-filter-selector-choice.html" %}
                        {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                            <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                                {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                            />
                        {% else %}
                            <input hidden name="filter_value">
                        {% endif %}
                    {% endwith %}
                {% else %}
                    {% if field_choice %}
                        <select id="{{uuid}}" class="bg-white border min-h-40px form-select form-select-solid select2-this" name="filter_value" data-placeholder="value">
                            {% for choice_value in field_choice %}
                                {% if predefined_data_filter.value == choice_value.0 %}
                                <option value="{{choice_value.0}}" selected>
                                    {{choice_value.0|display_column_inventory_transaction:request}}
                                </option>
                                {% else %}
                                <option value="{{choice_value.0}}" >
                                    {{choice_value.0|display_column_inventory_transaction:request}}
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' and data_filter != 'owner' %}
                        <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                            {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                        />
                    {% else %}
                        <input hidden name="filter_value">
                    {% endif %}
                {% endif %}
            {% elif app_type == "deals"  %}
                {% if data_filter|search_custom_field_object_deals:request %}
                    {% with channel_column=data_filter|search_custom_field_object_deals:request %}
                        {% if channel_column.type == "choice" %}
                            {% include "data/shopturbo/list-data-filter-selector-choice.html" %}
                        {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                            <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                                {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                            />
                        {% else %}
                            <input hidden name="filter_value">
                        {% endif %}
                    {% endwith %}
                {% else %}
                    {% if field_choice %}
                        <select id="{{uuid}}" class="bg-white border min-h-40px form-select form-select-solid select2-this" name="filter_value" data-placeholder="value">
                            {% for choice_value in field_choice %}
                                {% if predefined_data_filter.value == choice_value.0 %}
                                <option value="{{choice_value.0}}" selected>{% translate_lang choice_value.1 LANGUAGE_CODE %}</option>
                                {% else %}
                                <option value="{{choice_value.0}}" >{% translate_lang choice_value.1 LANGUAGE_CODE %}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                        <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                            {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                        />
                    {% else %}
                        <input hidden name="filter_value">
                    {% endif %}
                {% endif %}
            {% elif app_type == 'panels' %}
                {% if field_choice %}
                    <select id="{{uuid}}" class="bg-white border min-h-40px form-select form-select-solid select2-this" name="filter_value" data-placeholder="value">
                        {% for choice_value in field_choice %}
                            {% if predefined_data_filter.value == choice_value.0 %}
                            <option value="{{choice_value.0}}" selected>{% translate_lang choice_value.1 LANGUAGE_CODE %}</option>
                            {% else %}
                            <option value="{{choice_value.0}}" >{% translate_lang choice_value.1 LANGUAGE_CODE %}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                {% elif filter_options != 'is_empty' and filter_options != 'is_not_empty' %}
                    <input id="{{uuid}}" class="form-control d-flex input-posthead h-40px" name="filter_value" {% if filter_options == 'contains' or filter_options == 'does_not_contain' %}required{% endif %}
                        {% if predefined_data_filter.value %}value="{{predefined_data_filter.value}}"{% endif %}
                    />
                {% else %}
                    <input hidden name="filter_value">
                {% endif %}
            {% endif %}

            {% if data_type == "date" or data_type == "date_time" or data_type == "date_range" %}
                <script>
                    $('#{{uuid}}').attr('autocomplete', 'off');
                    $('#{{uuid}}').daterangepicker({
                        {% if filter_options == "date_range" or predefined_data_filter.key == "date_range" %}
                        singleDatePicker: false, // Enable range selection
                        {% else %}
                        singleDatePicker: true,
                        {% endif %}

                        {% if data_type == "date_time" %}
                        timePicker: true, // Enable time picker for date_time
                        {% else %}
                        timePicker: false, // Disable time picker for date only
                        {% endif %}

                        autoUpdateInput: false,
                        showDropdowns: true,
                        drops: "auto",
                        locale: {
                            {% if LANGUAGE_CODE == 'ja' %}
                                cancelLabel: 'クリア',
                                {% if data_type == "date" or data_type == "date_range" %}
                                format: "YYYY年MM月DD日",
                                {% elif data_type == "date_time" %}
                                format: 'YYYY年MM月DD日  HH:mm',
                                {% endif %}
                                separator: ' 〜 ',
                                applyLabel: '選択',
                                cancelLabel: 'キャンセル',
                                fromLabel: 'From',
                                toLabel: 'To',
                                customRangeLabel: 'カスタム範囲',
                                daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                                monthNames: [
                                    '1月', '2月', '3月', '4月', '5月', '6月',
                                    '7月', '8月', '9月', '10月', '11月', '12月'
                                ],
                            {% else %}
                                {% if data_type == "date" or data_type == "date_range" %}
                                format: "Y-M-DD",
                                {% elif data_type == "date_time" %}
                                format: "Y-M-DD HH:mm",
                                {% endif %}
                                cancelLabel: 'Clear',
                                separator: " - ", // Define the separator for the range
                            {% endif %}
                        }
                    });

                    $('#{{uuid}}').on('apply.daterangepicker', function(ev, picker) {
                        {% if filter_options == "date_range" or predefined_data_filter.key == "date_range" %}
                            {% if data_type == "date" %}
                                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
                            {% elif data_type == "date_time" %}
                                $(this).val(picker.startDate.format('YYYY-MM-DD HH:mm') + ' - ' + picker.endDate.format('YYYY-MM-DD HH:mm'));
                            {% endif %}
                        {% elif data_type == "date" %}
                            $(this).val(picker.startDate.format('YYYY-MM-DD'));
                        {% elif data_type == "date_time" %}
                            $(this).val(picker.startDate.format('YYYY-MM-DD HH:mm'));
                        {% endif %}
                    });
                    console.log("{{uuid}}")

                    $('#{{uuid}}').on('cancel.daterangepicker', function(ev, picker) {
                        $(this).val('');
                    });
                </script>

            {% endif %}
        </div>

        <div class="input-group-append">
            <button class="btn btn-danger ms-3" onclick='delete_item(this);' type="button">X</button>
        </div>

    </div>

<script>
    $(document).ready(function() {
        $('.select2-this').select2().on('select2:select', function (e) {
            var selectElement = $(this).closest('select').get(0);
            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
        })
    });
    function delete_item(elm){
        elm.parentElement.parentElement.parentElement.remove()
    }

    /* Commented out because of adding filter like number(less than,less, etc).
     it is not relevant using tagify. task:SAN-1723

    {% if data_filter|in_list:'item_id,order_id' %}

    var input_posthead = document.getElementById("{{uuid}}");
    
    {% comment %} input_posthead {% endcomment %}
    {% if not is_advanced_search %}
    var tagify = new Tagify(input_posthead, {
        dropdown: {
            maxItems: 20,           // <- mixumum allowed rendered suggestions
            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
            enabled: 0,             // <- show suggestions on focus
            closeOnSelect: true    // <- do not hide the suggestions dropdown once an item has been selected
        },
        originalInputValueFormat: valuesArr => valuesArr.map(item => item.value).join(',')
        });

        {% if predefined_data_filter|is_list %}
            tagify.addTags([
                {% for item in predefined_data_filter %}
                    {value: "{{item}}"},
                {% endfor %}
            ]);
        {% endif %}
    {% endif %}

    {% endif %}*/
</script>


</div>


{% endblock %}