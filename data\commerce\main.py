import csv
import io
import traceback
import uuid
import ast
from datetime import datetime
from django_hosts.resolvers import reverse


import pandas as pd
from django.core.paginator import EmptyPage, Paginator
from django.db.models import Q
from django.db.models.fields.files import FieldFile
from django.http import HttpResponse
from django.shortcuts import redirect, render

from data.commerce.commerce_functions import commerce_form_update
from data.constants.commerce_constant import COMMERCE_APP_TARGET
from data.constants.constant import (
    DEFAULT_SLIPS_TYPE_DISPLAY,
    DEFAULT_JOURNAL_CATEGORY,
    TEMPLATE_FILE,
)
from data.constants.properties_constant import (
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_WORKER,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_TASK,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_USER_MANAGEMENT,
    TYPE_OBJECT_SLIP,
    OBJECT_TYPE_TO_SLUG,
)
from data.models import (
    AppSetting,
    PropertySet,
    CustomProperty,
    View,
    TaskCustomFieldName,
    TransferHistory,
    ExportTemplate,
    ShopTurboItems,
    Projects,
    Module,
    Notification,
    ViewFilter,
)
from utils.contact import get_contact_custom_property
from utils.csv import csv_row_values
from utils.decorator import login_or_hubspot_required
from utils.filter import build_view_filter
from utils.formula import calculate_math
from utils.properties.properties import get_list_view_columns, get_page_object
from utils.utility import (
    generate_zip,
    get_private_file,
    get_workspace,
    is_valid_uuid,
    remove_unsupported_characters,
)


@login_or_hubspot_required
def view_settings(request):
    """
    Handles GET and POST requests for managing commerce-related view settings, including filtering, sorting, pagination, and data export.

    On GET requests, retrieves or initializes view settings and prepares context for rendering UI components such as view drawers, advanced filters, and import/export sections. Supports dynamic filtering, sorting, and pagination for commerce objects, with special handling for inventory transactions and slips. Enables exporting all records in a view when requested.

    On POST requests, processes form submissions to create, update, or delete views and their filters, including privacy, sorting, and pagination settings. Supports exporting data in CSV, DAT, PDF, or ZIP formats, streaming files or generating archives as needed, and records export operations. Handles direct CSV and ZIP downloads, including file collection and user notification if no files are available.

    Returns rendered templates or file responses based on the requested operation.
    """
    try:
        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=get_workspace(request.user), app_target=COMMERCE_APP_TARGET
        )
    except AppSetting.MultipleObjectsReturned:
        app_setting = AppSetting.objects.filter(
            workspace=get_workspace(request.user), app_target=COMMERCE_APP_TARGET
        ).first()

    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    object_type = (
        request.GET.get("object_type")
        if request.method == "GET"
        else request.POST.get("object_type")
    )
    print("INFO === commerce.py -- 29: object_type -", object_type)

    page_obj = get_page_object(object_type, lang)
    base_model = page_obj["base_model"]
    custom_model = page_obj["custom_model"]
    base_columns = page_obj["base_columns"]
    default_columns = page_obj["default_columns"]
    columns_display = page_obj["columns_display"]
    reverse_query = page_obj["reverse_query"]
    id_field = page_obj["id_field"]
    file_name = page_obj["file_name"]
    file_field = page_obj["file_field"]
    custom_relation = page_obj["custom_relation"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    download_formats = page_obj["download_formats"]
    download_form_url = page_obj["download_form_url"]
    view_types = page_obj["view_types"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    required_column_fields = page_obj["required_column_fields"]
    if request.method == "GET":
        view_id = request.GET.get("view_id")
        if view_id == "None":
            view_id = None
        object_type = request.GET.get("object_type")
        module = request.GET.get("module")
        view_type = request.GET.get("type")
        download_view = request.GET.get("download_view")
        p_id = request.GET.get("p_id", None)
        object_ids = request.GET.get("ids", None)

        columns = get_list_view_columns(object_type, workspace)
        print("Columns: %s", columns)
        if not columns:
            # NOTE: Modularized this filter if we need more filter in the future
            custom_fields_filter = Q()
            if p_id:
                custom_fields_filter = Q(project_target=p_id)

            custom_columns = []
            if custom_model:
                custom_columns = custom_model.objects.filter(
                    custom_fields_filter, name__isnull=False, workspace=workspace
                ).values_list("id", flat=True)

            columns = base_columns.copy()
            # NOTE: Add Elements of custom columns
            columns.extend([str(item) for item in custom_columns])
            # NOTE: Add Elements of default columns
            columns.extend(
                [str(item) for item in default_columns if item not in columns]
            )

        if "checkbox" in columns:
            columns.remove("checkbox")
        if "checkbox" in default_columns:
            default_columns.remove("checkbox")

        if download_view:
            if "usage_status" in columns:
                columns.remove("usage_status")
        else:
            if page_obj["id_field"] in columns:
                columns.remove(page_obj["id_field"])

        context = {
            "object_type": object_type,
            "default_columns": default_columns,
            "columns": columns,
            "app_setting": app_setting,
            "download_formats": download_formats,
            "download_form_url": download_form_url,
            "view_types": view_types,
            "id_field": id_field,
            "module": module,
        }

        # For Group by in Gantt Chart in Task
        if object_type == TYPE_OBJECT_TASK:
            production_property_list = []
            project = (
                Projects.objects.filter(workspace=workspace, id=p_id).first()
                if p_id
                else None
            )
            if project:
                production_property_list = TaskCustomFieldName.objects.filter(
                    type="production_line", workspace=workspace, project_target=project
                ).order_by("order")
            context["project"] = project
            context["production_property_list"] = production_property_list

        if view_type == "advance-filter":
            data_filter = request.GET.get("data_filter", None)
            filter_options = request.GET.get("filter_options", None)
            data_type = "string"
            fields = []
            field_choice = []
            categories = None
            from_property = False

            fields = base_model._meta.fields
            default_columns_dict = {
                field.name: field.get_internal_type() for field in fields
            }

            print("[DEBUG] Data Filter:", data_filter)
            print("[DEBUG] Custom Model:", custom_model)

            if data_filter in default_columns_dict.keys():
                if "status" in data_filter or data_filter in [
                    "slip_type",
                    "category",
                    "tax_rate",
                ]:
                    field_choice = []
                    custom_field = CustomProperty.objects.filter(
                        workspace=workspace,
                        model=base_model._meta.db_table,
                        name=data_filter,
                    )
                    if custom_field:
                        from_property = True
                        custom_field = custom_field.first()
                        choices = ast.literal_eval(custom_field.value)
                        if object_type == "journal" and data_filter == "category":
                            default_category_list = {}
                            default_choice_list = {}
                            for (
                                key,
                                category,
                            ) in DEFAULT_JOURNAL_CATEGORY.copy().items():
                                default_category_list[key] = [
                                    category["en"],
                                    category["ja"],
                                ]
                                for choice in category["choices"]:
                                    default_choice_list[choice["value"]] = [
                                        choice["en"],
                                        choice["ja"],
                                    ]

                            custom_property_value = ast.literal_eval(custom_field.value)
                            result = []
                            for category in custom_property_value:
                                for key, cat_ in default_category_list.items():
                                    if cat_[0] == category["category"]:
                                        if lang == "ja":
                                            category_value = cat_[1]
                                        else:
                                            category_value = cat_[0]
                                        break
                                    elif cat_[1] == category["category"]:
                                        if lang == "ja":
                                            category_value = cat_[1]
                                        else:
                                            category_value = cat_[0]
                                        break
                                    else:
                                        category_value = category["category"]

                                category_data = {"category": category_value}

                                choices_list = []
                                for choice in category["choices"]:
                                    choice_list_dict = {
                                        "label": choice["label"],
                                        "value": choice["value"],
                                    }
                                    choices_list.append(choice_list_dict)
                                    if choice["value"] in default_choice_list:
                                        default_label_list = default_choice_list[
                                            choice["value"]
                                        ]
                                        if choice["label"] in default_label_list:
                                            if lang == "ja":
                                                choice_list_dict["label"] = (
                                                    default_choice_list[
                                                        choice["value"]
                                                    ][1]
                                                )
                                            else:
                                                choice_list_dict["label"] = (
                                                    default_choice_list[
                                                        choice["value"]
                                                    ][0]
                                                )
                                    category_data["choices"] = choices_list
                                result.append(category_data)
                            categories = result
                        else:
                            for value in choices:
                                field_choice.append((value, choices[value]))

                    else:
                        if object_type == "journal" and data_filter == "category":
                            result = []
                            for (
                                key,
                                category,
                            ) in DEFAULT_JOURNAL_CATEGORY.copy().items():
                                category_data = {
                                    # Use English name as the category label
                                    "category": category[lang],
                                    "choices": [
                                        {
                                            # Use English name as the label
                                            "label": choice[lang],
                                            # Keep the value as-is
                                            "value": choice["value"],
                                        }
                                        for choice in category["choices"]
                                    ],
                                }
                                result.append(category_data)
                            categories = result
                        else:
                            # Display Predefined
                            choice_status_display = page_obj["editable_columns"]
                            if data_filter in choice_status_display:
                                choice_status_display = choice_status_display[
                                    data_filter
                                ]
                                from_property = True
                                # Intead of using field.choice, just take from key of the dict _DISPLAY
                                for value, label in choice_status_display.items():
                                    field_choice.append(
                                        (value, choice_status_display[value][lang])
                                    )
                            else:
                                field_choice = getattr(
                                    base_model, data_filter
                                ).field.choices

                for check in ["float", "integer"]:
                    if check in default_columns_dict[data_filter].lower():
                        data_type = "number"
                        break
                for check in ["date"]:
                    if check in default_columns_dict[data_filter].lower():
                        data_type = "date"
                        break

            elif custom_model:
                if is_valid_uuid(data_filter):
                    customfield_name = custom_model.objects.filter(
                        id=data_filter
                    ).first()
                else:
                    customfield_name = custom_model.objects.filter(
                        name__iexact=data_filter, workspace=workspace
                    ).first()
                if customfield_name:
                    if (
                        customfield_name.type == "number"
                        or customfield_name.type == "formula"
                    ):
                        data_type = "number"
                    elif (
                        customfield_name.type == "date"
                        or customfield_name.type == "date_time"
                    ):
                        data_type = customfield_name.type
                    elif customfield_name.type == "choice":
                        field_choice = ast.literal_eval(customfield_name.choice_value)

                if data_filter in ["assignee"]:
                    field_choice = workspace.user.all()

            predefined_data_filter = request.GET.get("predefined_data_filter", None)
            if predefined_data_filter:
                predefined_data_filter = ast.literal_eval(predefined_data_filter)

            context = {
                "data_filter": data_filter,
                "data_type": data_type,
                "app_type": object_type,
                "predefined_data_filter": predefined_data_filter,
                "field_choice": field_choice,
                "categories": categories,
                "from_property": from_property,
                "uuid": uuid.uuid4(),
                "filter_options": filter_options,
                "p_id": p_id,
                "object_type": object_type,
                "is_advanced_search": request.GET.get("is_advanced_search", False),
                "filter_status": request.GET.get("filter_status", False),
            }
            return render(
                request, "data/commerce/list-data-filter-selector.html", context
            )

        else:
            if p_id:
                context["p_id"] = p_id

            if not view_id:
                if object_type == TYPE_OBJECT_JOURNAL:
                    default_name = "journal-entry"
                    secondary_name = "transaction"
                    property_set, _ = PropertySet.objects.get_or_create(
                        workspace=workspace, target=object_type, name=default_name
                    )
                    property_set, _ = PropertySet.objects.get_or_create(
                        workspace=workspace, target=object_type, name=secondary_name
                    )

                return render(request, "data/commerce/view-drawer.html", context)

            try:
                view = View.objects.get(id=view_id)
            except Exception as e:
                print(f"ERROR === commerce.py --2732: {e}")
                return HttpResponse(status=404)

            context["view"] = view
            view_filter = view.viewfilter_set.first()
            if view_filter and not view_filter.column:
                view_filter.column = default_columns
                # Convert column to list if it's a string representation of a list
                column_list = default_columns
                if isinstance(column_list, list) and "checkbox" in column_list:
                    column_list.remove("checkbox")
                    view_filter.column = column_list
                view_filter.save()
            elif not view_filter:
                # Create a new view_filter if none exists
                view_filter, _ = ViewFilter.objects.get_or_create(
                    view=view, defaults={"column": default_columns}
                )
            context["view_filter"] = view_filter
            context["download_view"] = download_view
            if object_type == "commerce_inventory_transaction":
                iventory_items = (
                    ShopTurboItems.objects.filter(workspace=workspace)
                    .filter(shopturboinventory__isnull=False)
                    .distinct()
                    .order_by("item_id")
                )
                item_id = None
                transaction_date_range = None
                if view_filter and view_filter.value:
                    if "item" in view_filter.value:
                        item_id = view_filter.value["item"]
                    if "transaction_date_range" in view_filter.value:
                        transaction_date_range = view_filter.value[
                            "transaction_date_range"
                        ]
                context["iventory_items"] = iventory_items
                context["item_id"] = item_id
                context["transaction_date_range"] = transaction_date_range

            elif object_type == "slips":
                sets = PropertySet.objects.filter(
                    workspace=workspace,
                    target=object_type,
                )
                default_sets = PropertySet.objects.filter(
                    workspace=workspace, target=object_type, as_default=True
                )
                for i, prop in enumerate(DEFAULT_SLIPS_TYPE_DISPLAY):
                    prop_set_name = DEFAULT_SLIPS_TYPE_DISPLAY[prop][lang]
                    prop_list = (
                        DEFAULT_SLIPS_TYPE_DISPLAY[prop]["en"],
                        DEFAULT_SLIPS_TYPE_DISPLAY[prop]["ja"],
                    )
                    prop_set = PropertySet.objects.filter(
                        workspace=workspace,
                        name__in=prop_list,
                        target=object_type,
                        edit_by__isnull=True,
                    ).first()
                    if prop_set:
                        prop_set.name = prop_set_name
                        prop_set.save()

                if not sets:  # Set Default Value For first time if none sets
                    property_set = PropertySet.objects.create(
                        workspace=workspace,
                        name=DEFAULT_SLIPS_TYPE_DISPLAY["sales_slips"][
                            request.LANGUAGE_CODE
                        ],
                        target=object_type,
                    )
                    property_set.children = [
                        str(p)
                        for p in DEFAULT_SLIPS_TYPE_DISPLAY["sales_slips"][
                            "value"
                        ].split(",")
                    ]
                    property_set.save()

                    sets = [property_set]
                    view.form = property_set
                    view.save()
                    context["view"] = view

                if not default_sets:
                    sets = PropertySet.objects.filter(
                        workspace=workspace,
                        target=object_type,
                    )
                    set_ = sets.first()
                    set_.as_default = True
                    set_.save()

                    sets = PropertySet.objects.filter(
                        workspace=workspace,
                        target=object_type,
                    )

                context["sets"] = sets

            import_export_type = request.GET.get("import_export_type", None)
            if import_export_type:
                import_export_type = request.GET.get("import_export_type", None)

                object_ids = request.GET.get("record_ids", None)
                view_id = request.GET.get("view_id", None)
                if view_id == "None":
                    view_id = None
                flag_all = request.GET.get("flag_all", False)
                section = request.GET.get("section", None)

                if flag_all == "true" and view_id:  # Export select all record in view
                    view = View.objects.filter(id=view_id, workspace=workspace).first()
                    view_filter = view.viewfilter_set.first()
                    filter_condition = Q(workspace=workspace)
                    filter_condition &= Q(usage_status="active")
                    filter_condition = build_view_filter(
                        filter_condition,
                        view_filter,
                        object_type,
                        force_filter_list=additional_filter_fields,
                    )
                    object_ids = base_model.objects.filter(
                        filter_condition
                    ).values_list("id", flat=True)
                    object_ids = ",".join(str(object_id) for object_id in object_ids)

                if section == "export_selector":
                    context = {
                        "page_group_type": object_type,
                        "view_id": view_id,
                        "ids": object_ids,
                    }
                    return render(
                        request,
                        "data/commerce/view-drawer-export-source-selector.html",
                        context,
                    )

                elif section == "direct_import":
                    if object_type == TYPE_OBJECT_EXPENSE:
                        return render(
                            request,
                            "data/commerce/view-drawer-import-expense.html",
                            context,
                        )
                    elif object_type == TYPE_OBJECT_BILL:
                        return render(
                            request,
                            "data/commerce/view-drawer-import-bill.html",
                            context,
                        )
                    return HttpResponse(200)

                elif section == "direct_download":
                    export_type = request.GET.get("export_type", None)
                    export_template = ExportTemplate.objects.filter(
                        workspace=workspace, target=object_type, as_default=True
                    ).first()
                    context["view_id"] = view_id
                    context["ids"] = object_ids
                    context["record_ids"] = request.GET.get("record_ids", None)
                    context["export_template"] = export_template
                    if export_type == "file":
                        return render(
                            request,
                            "data/commerce/view-drawer-download-file.html",
                            context,
                        )
                    else:
                        return render(
                            request,
                            "data/commerce/view-drawer-import-export-direct.html",
                            context,
                        )

                elif section == "other_exports":
                    object_type = request.GET.get("object_type", None)
                    export_file_type = request.GET.get("export_file_type", "csv")

                    ids = request.GET.get("ids", None)
                    context["record_ids"] = ids

                    if object_type not in [
                        "invoices",
                        "receipts",
                        "estimates",
                        "delivery_slips",
                        "slips",
                    ]:
                        context["change_view_card_body"] = True

                    context["export_file_type"] = export_file_type

                    # For CSV export, ensure ID field is available in columns
                    if (
                        export_file_type == "csv"
                        and page_obj["id_field"] not in context["columns"]
                    ):
                        context["columns"] = [page_obj["id_field"]] + context["columns"]

                    export_template = ExportTemplate.objects.filter(
                        workspace=workspace, target=object_type, as_default=True
                    ).first()
                    if export_template:
                        if export_template.properties:
                            if "view_filter" in context:
                                if context["view_filter"]:
                                    context[
                                        "view_filter"
                                    ].column = export_template.properties.split(",")

                    return render(
                        request,
                        "data/commerce/view-drawer-import-export-others.html",
                        context,
                    )

                elif section == "history":
                    import_export_type = request.GET.get("import_export_type", None)
                    filter_conditions = Q(workspace=workspace)

                    if import_export_type == "import":
                        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                            filter_conditions &= Q(type="import_purchase_order")
                        elif object_type == TYPE_OBJECT_BILL:
                            filter_conditions &= Q(type="import_bill")
                        elif object_type == TYPE_OBJECT_EXPENSE:
                            filter_conditions &= Q(type="import_" + TYPE_OBJECT_EXPENSE)
                        elif object_type == TYPE_OBJECT_CASE:
                            filter_conditions &= Q(type="import_case")
                        elif object_type == TYPE_OBJECT_ESTIMATE:
                            filter_conditions &= Q(type="import_estimates")
                        elif object_type == TYPE_OBJECT_WORKER:
                            filter_conditions &= Q(type="import_worker")

                        elif object_type == TYPE_OBJECT_INVOICE:
                            filter_conditions &= Q(type="import_invoice")
                        elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
                            filter_conditions &= Q(
                                type="import_" + TYPE_OBJECT_DELIVERY_NOTE
                            )
                        elif object_type == TYPE_OBJECT_RECEIPT:
                            filter_conditions &= Q(type="import_" + TYPE_OBJECT_RECEIPT)
                        elif object_type == TYPE_OBJECT_SLIP:
                            filter_conditions &= Q(type="import_" + TYPE_OBJECT_SLIP)
                        elif object_type == TYPE_OBJECT_JOURNAL:
                            filter_conditions &= Q(type="import_journal")
                    elif import_export_type == "export":
                        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                            filter_conditions &= Q(type="export_purchase_order")
                        elif object_type == TYPE_OBJECT_BILL:
                            filter_conditions &= Q(type="export_bill")
                        elif object_type == TYPE_OBJECT_EXPENSE:
                            filter_conditions &= Q(type="export_expense")
                        elif object_type == TYPE_OBJECT_CASE:
                            filter_conditions &= Q(type="export_case")
                        elif object_type == TYPE_OBJECT_ESTIMATE:
                            filter_conditions &= Q(type="export_estimates")
                        elif object_type == TYPE_OBJECT_WORKER:
                            filter_conditions &= Q(type="export_worker")

                        elif object_type == TYPE_OBJECT_INVOICE:
                            filter_conditions &= Q(type="export_invoice")
                        elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
                            filter_conditions &= Q(
                                type="export_" + TYPE_OBJECT_DELIVERY_NOTE
                            )
                        elif object_type == TYPE_OBJECT_RECEIPT:
                            filter_conditions &= Q(type="export_" + TYPE_OBJECT_RECEIPT)
                        elif object_type == TYPE_OBJECT_SLIP:
                            filter_conditions &= Q(type="export_" + TYPE_OBJECT_SLIP)
                        elif object_type == TYPE_OBJECT_JOURNAL:
                            filter_conditions &= Q(type="export_journal")

                    history = TransferHistory.objects.filter(
                        filter_conditions
                    ).order_by("-created_at")

                    has_next_page = False
                    page = int(request.GET.get("page", 1))
                    if history:
                        paginator = Paginator(history, 10)
                        try:
                            page_content = paginator.page(page)
                            history = page_content.object_list
                            has_next_page = page_content.has_next()
                            print(page)
                            page += 1
                        except EmptyPage:
                            history = []
                            page = None
                    else:
                        page = None

                    context = {
                        "history": history,
                        "next_page": page,
                        "has_next_page": has_next_page,
                        "import_export_type": import_export_type,
                        "section": section,
                        "object_type": object_type,
                        "view_id": view_id,
                    }

                    if page and page > 2:
                        return render(
                            request,
                            "data/shopturbo/parts/import-export-history-row.html",
                            context,
                        )
                    return render(
                        request,
                        "data/shopturbo/manage-sync-settings-shopturbo-items-import-export-history.html",
                        context,
                    )

                else:
                    view_id = request.GET.get("view_id", None)
                    context = {
                        "import_export_type": import_export_type,
                        "page_group_type": object_type,
                        "view_id": view_id,
                        "ids": object_ids,
                    }
                    return render(
                        request, "data/commerce/view-drawer-tab.html", context
                    )

            return render(request, "data/commerce/view-drawer.html", context)

    elif request.method == "POST":
        try:
            module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
            module_slug = request.POST.get("module")
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=object_type
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
        except:
            module_object_slug = None
            module_slug = None

        view_id = request.POST.get("view_id")
        if view_id == "None":
            view_id = None
        view_type = request.POST.get("view_type")
        column_str = request.POST.get("column")
        p_id = request.POST.get("p_id", None)
        columns = column_str.split(",") if column_str else default_columns

        if view_type in ["receivable_list", "payable_list"]:
            sub_view_type = request.POST.get("sub_view_type_account", None)
            sub_view_type_date = request.POST.get(
                "sub_view_type_date_payable_receivable", None
            )
        else:
            sub_view_type = request.POST.get("sub_view_type", None)
            sub_view_type_date = request.POST.get("sub_view_type_date", None)

        if sub_view_type_date:
            if " 〜 " in sub_view_type_date:
                date_start = sub_view_type_date.split(" 〜 ")[0]
                date_end = sub_view_type_date.split(" 〜 ")[1]

                less_date = (
                    datetime.strptime(date_start, "%Y年%m月%d日")
                    .date()
                    .strftime("%Y-%m-%d")
                )
                greater_date = (
                    datetime.strptime(date_end, "%Y年%m月%d日")
                    .date()
                    .strftime("%Y-%m-%d")
                )

                sub_view_type_date = less_date + " - " + greater_date

        if object_type == TYPE_OBJECT_JOURNAL:
            if sub_view_type and view_type in ["receivable_list", "payable_list"]:
                sub_view_type = {
                    "account_type": sub_view_type.split(","),
                    "date_range": sub_view_type_date,
                }
            else:
                if view_type == "receivable_list":
                    sub_view_type = {
                        "account_type": [
                            "accounts_receivable",
                            "accounts_receivable_other",
                        ],
                        "date_range": sub_view_type_date,
                    }
                elif view_type == "payable_list":
                    sub_view_type = {
                        "account_type": ["accounts_payable", "accounts_payable_other"],
                        "date_range": sub_view_type_date,
                    }
                elif view_type == "balance_sheet":
                    if sub_view_type == "bs_custom_date":
                        sub_view_type = sub_view_type_date

        if id_field and id_field not in columns:
            if id_field != "task_id":
                columns.insert(0, id_field)

        for required_column_field in required_column_fields:
            if required_column_field not in columns:
                columns = columns + [required_column_field]

        name = request.POST.get("name")

        order_by = request.POST.get("order-by", None)
        sort_method = request.POST.get("sort-method", None)

        filter_dictionary = {}
        filter_types = request.POST.getlist("filter_type", None)
        filter_options = request.POST.getlist("filter_options", None)
        filter_values = request.POST.getlist("filter_value", None)
        for idx, filter_type in enumerate(filter_types):
            if (filter_type == "category" and object_type == TYPE_OBJECT_JOURNAL) or (
                filter_type == "inventory_transactions"
                and object_type == TYPE_OBJECT_PURCHASE_ORDER
            ):
                filter_value = request.POST.getlist("filter_value_" + filter_type, None)
                if filter_value:
                    filter_value = ",".join(filter_value)
            else:
                filter_value = filter_values[idx]

            if "id" in filter_type:
                try:
                    value = str(filter_value).split(",")[0]
                    _ = uuid.UUID(str(value))
                    filter_type = "id"
                except (ValueError, TypeError):
                    pass

            filter_dictionary[str(filter_type)] = {
                "key": filter_options[idx],
                "value": filter_value,
            }

        sort_dictionary = {}
        sort_types = request.POST.getlist("sort_type", None)
        sort_options = request.POST.getlist("sort_options", None)
        for idx, sort_type in enumerate(sort_types):
            if idx < len(sort_options):
                # idx is within the range of sort_options
                sort_dictionary[str(sort_type)] = sort_options[idx]
            else:
                # idx exceeds the length of sort_options, assign a default value or skip
                sort_dictionary[str(sort_type)] = None

        if "update-view-button" in request.POST:
            view = None
            view_filter = None
            inventory_item = request.POST.get("inventory_item", None)
            transaction_date_range = request.POST.get("transaction_date_range", None)
            if transaction_date_range:
                transaction_date_range = (
                    transaction_date_range.replace("年", "-")
                    .replace("月", "-")
                    .replace("日", "")
                )

            if view_id:
                try:
                    view = View.objects.get(id=view_id)
                    if view.is_private and (view.user == request.user):
                        view.is_private = bool(
                            request.POST.get("set-private-view", False)
                        )
                        view.user = request.user if view.is_private else None
                        view.save()
                    view_filter = view.viewfilter_set.first()
                except Exception as e:
                    print(f"ERROR === commerce.py -- 2752: {e}")
                    query_string = f"?{reverse_query}" if reverse_query else ""
                    if module_slug:
                        return redirect(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module_slug,
                                    "object_slug": module_object_slug,
                                },
                            )
                            + query_string
                        )
                    return redirect(reverse("main", host="app"))

            if not view:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                if p_id:
                    project_target = Projects.objects.get(id=p_id)
                    view = View.objects.create(
                        workspace=workspace,
                        title=name,
                        target=object_type,
                        project_target=project_target,
                        is_private=is_private,
                        user=user,
                    )
                else:
                    view = View.objects.create(
                        workspace=workspace,
                        title=name,
                        target=object_type,
                        is_private=is_private,
                        user=user,
                    )

                view_filter, _ = ViewFilter.objects.get_or_create(
                    view=view,
                    view_type="list",
                )

            property_set_is_default_input = request.POST.get(
                "property_set_default", None
            )
            if property_set_is_default_input:
                default_set = PropertySet.objects.get(id=property_set_is_default_input)
                view.form = default_set
                default_set.as_default = True
                default_set.save()
                # check default
                PropertySet.objects.filter(
                    workspace=default_set.workspace, target=default_set.target
                ).exclude(id=default_set.id).update(as_default=False)

            if name:
                view.title = name

            if columns:
                view_filter.column = columns

            if sub_view_type:
                view_filter.sub_view_type = sub_view_type
            else:
                view_filter.sub_view_type = None

            if view_type:
                view_filter.view_type = view_type
            view_filter_value = {}

            if inventory_item:
                view_filter_value["item"] = inventory_item
            if transaction_date_range:
                view_filter_value["transaction_date_range"] = transaction_date_range

            if order_by:
                view_filter.sort_order_by = order_by
            else:
                view_filter.sort_order_by = None

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = "asc"
                else:
                    view_filter.sort_order_method = None

            view_filter.value = view_filter_value

            pagination = request.POST.get("pagination", None)
            if pagination:
                view_filter.pagination = pagination
            else:
                view_filter.pagination = 20

            if object_type in ["purchaseorder"]:
                enable_status = request.POST.get("enable_status", False)
                property_status_default = request.POST.get(
                    "property_status_default", None
                )
                if enable_status and property_status_default:
                    custom_property = CustomProperty.objects.filter(
                        id=property_status_default
                    ).first()
                    if custom_property:
                        view.custom_property = custom_property
                else:
                    view.custom_property = None

            view.save()
            view_filter.filter_value = filter_dictionary
            view_filter.sort_value = sort_dictionary
            view_filter.save()

            query_string = (
                f"?view_id={view.id}&{reverse_query}"
                if reverse_query
                else f"?view_id={view.id}"
            )
            if p_id:
                query_string = (
                    f"{query_string}&p_id={p_id}" if query_string else f"?p_id={p_id}"
                )

            if object_type == "slips":
                commerce_form_update(request, view.id)

            if object_type == TYPE_OBJECT_USER_MANAGEMENT:
                return redirect(reverse("workspace", host="app") + query_string)

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + query_string
            )

        elif "delete-view" in request.POST:
            view = None
            if view_id:
                try:
                    view = View.objects.get(id=view_id).delete()
                except Exception as e:
                    print(f"ERROR === commerce.py -- 2752: {e}")

            query_string = f"?{reverse_query}" if reverse_query else ""

            if object_type == TYPE_OBJECT_USER_MANAGEMENT:
                return redirect(reverse("workspace", host="app") + query_string)

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + query_string
            )

        elif "download" in request.POST:
            download_formats = request.POST.get("download_formats", "").split(",")
            view_type = request.POST.get("view_type")
            encoded_format = request.POST.get("encoded_format", None)
            if not view_type:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + query_string
                )

            filter_condition = Q(workspace=workspace)
            if "usage_status" in [str(v.name) for v in base_model._meta.fields]:
                # Handle status filter for archived items
                status = request.GET.get("status")
                if status == "archived":
                    filter_condition &= Q(usage_status="archived")
                else:
                    # By default, show only active items
                    filter_condition &= Q(usage_status="active")
            view_filter = ViewFilter(
                filter_value=filter_dictionary,
                view=View(workspace=workspace),
                view_type=view_type,
            )
            filter_condition = build_view_filter(
                filter_condition,
                view_filter,
                object_type,
                force_filter_list=additional_filter_fields,
                request=request,
            )
            if request.POST.get("record_ids"):
                filter_condition &= Q(id__in=request.POST.getlist("record_ids"))

            items = []
            try:
                items = base_model.objects.filter(filter_condition).order_by(
                    "-created_at"
                )

            except Exception as e:
                traceback.print_exc()
                print(f"ERROR === commerce.py -- 2668: {e}")

            csv_content = None
            csv_writer = None
            namecustomfields = []
            if "csv" in download_formats:
                for i, col in enumerate(columns):
                    try:
                        custom_field = custom_model.objects.get(id=col)
                        columns[i] = custom_field.name
                    except:
                        pass

                csv_content = io.StringIO()
                csv_writer = csv.writer(csv_content)
                headers = []
                for col in columns:
                    if "|" in col:
                        col_parts = col.split("|")
                        result = get_contact_custom_property(workspace, col_parts[:3])
                        col = result["name"] if result else col
                    elif col in columns_display:
                        col = columns_display[col][lang]
                    headers.append(col)

                if lang == "ja":
                    headers = [remove_unsupported_characters(col) for col in headers]
                csv_writer.writerow(headers)

                if custom_model:
                    namecustomfields = custom_model.objects.filter(
                        workspace=workspace, name__isnull=False
                    ).values_list("name", flat=True)
                    namecustomfields = [data.lower() for data in namecustomfields]

            if "dat" in download_formats:
                for i, col in enumerate(columns):
                    try:
                        custom_field = custom_model.objects.get(id=col)
                        columns[i] = custom_field.name
                    except:
                        pass

                headers = []
                for col in columns:
                    if "|" in col:
                        col_parts = col.split("|")
                        result = get_contact_custom_property(workspace, col_parts[:3])
                        col = result["name"] if result else col
                    elif col in columns_display:
                        col = columns_display[col][lang]
                    headers.append(col)

                if lang == "ja":
                    headers = [remove_unsupported_characters(col) for col in headers]
                df = pd.DataFrame(columns=headers)

                if custom_model:
                    namecustomfields = custom_model.objects.filter(
                        workspace=workspace, name__isnull=False
                    ).values_list("name", flat=True)
                    namecustomfields = [data.lower() for data in namecustomfields]

            file_tuple = []
            # print("========= items: ", items)
            print(view_filter.__dict__)
            if (
                object_type == TYPE_OBJECT_PURCHASE_ORDER
                and view_filter.view_type == "item_list"
            ):
                items = items.filter(purchase_order_object__isnull=False).distinct()

            for item in items:
                if "pdf" in download_formats and file_field:
                    try:
                        field_value = getattr(item, file_field)
                        if isinstance(field_value, FieldFile):
                            files = [field_value]
                        else:
                            files = [item.file for item in field_value.all()]

                        for file in files:
                            file_tuple.append(
                                (
                                    f"{item.title}-{item.id}.pdf",
                                    get_private_file(file.url),
                                )
                            )
                    except Exception as e:
                        traceback.print_exc()
                        print(f"ERROR === commerce.py -- 2704: {e}")

                if "csv" in download_formats:
                    taskhistory_type = "export_purchase_order"
                    if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                        taskhistory_type = "export_purchase_order"
                    elif object_type == TYPE_OBJECT_BILL:
                        taskhistory_type = "export_bill"
                    elif object_type == TYPE_OBJECT_EXPENSE:
                        taskhistory_type = "export_expense"
                    elif object_type == TYPE_OBJECT_CASE:
                        taskhistory_type = "export_case"

                    transfer_history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type=taskhistory_type,
                    )

                    try:
                        if (
                            object_type == TYPE_OBJECT_PURCHASE_ORDER
                            and view_filter.view_type == "item_list"
                        ):
                            for item_ in item.purchase_order_object.all():
                                csv_row = csv_row_values(
                                    object_type=object_type,
                                    page_obj=page_obj,
                                    obj=item,
                                    columns=columns,
                                    user=request.user,
                                    workspace=workspace,
                                    lang=lang,
                                    item=item_,
                                )
                                csv_writer.writerow(csv_row)
                        else:
                            csv_row = csv_row_values(
                                object_type=object_type,
                                page_obj=page_obj,
                                obj=item,
                                columns=columns,
                                user=request.user,
                                workspace=workspace,
                                lang=lang,
                            )
                            csv_writer.writerow(csv_row)

                        transfer_history.name = f"{file_name}.csv"
                        transfer_history.status = "completed"
                        transfer_history.progress = 100
                        transfer_history.save()

                    except Exception as e:
                        traceback.print_exc()
                        print(f"ERROR === commerce.py -- 2732: {e}")
                if "dat" in download_formats:
                    try:
                        if (
                            object_type == TYPE_OBJECT_PURCHASE_ORDER
                            and view_filter.view_type == "item_list"
                        ):
                            for item_ in item.purchase_order_object.all():
                                csv_row = csv_row_values(
                                    object_type=object_type,
                                    page_obj=page_obj,
                                    obj=item,
                                    columns=columns,
                                    user=request.user,
                                    workspace=workspace,
                                    lang=lang,
                                    item=item_,
                                )
                                df.loc[len(df)] = csv_row
                        else:
                            csv_row = csv_row_values(
                                object_type=object_type,
                                page_obj=page_obj,
                                obj=item,
                                columns=columns,
                                user=request.user,
                                workspace=workspace,
                                lang=lang,
                            )
                            df.loc[len(df)] = csv_row

                    except Exception as e:
                        traceback.print_exc()
                        print(f"ERROR === commerce.py -- 636: {e}")

            if "csv" in download_formats:
                file_tuple.append((f"{file_name}.csv", csv_content.getvalue()))
            if "dat" in download_formats:
                dat_content = io.StringIO()
                df.to_csv(dat_content, index=False, sep=" ", header=True)
                file_tuple.append((f"{file_name}.dat", dat_content.getvalue()))

            if len(file_tuple) == 1:
                if "csv" in download_formats:
                    if lang == "ja":
                        content_type = "text/csv; charset=Shift-JIS"
                    else:
                        content_type = "text/csv"
                else:
                    if encoded_format == "shift-jis":
                        content_type = "application/force-download; charset=Shift-JIS"
                    else:
                        content_type = "application/force-download"
                return HttpResponse(
                    file_tuple[0][1],
                    content_type=content_type,
                    headers={
                        "Content-Disposition": f'attachment; filename="{file_tuple[0][0]}"'
                    },
                )

            return HttpResponse(
                generate_zip(file_tuple),
                content_type="application/force-download",
                headers={
                    "Content-Disposition": f'attachment; filename="{file_name}.zip"'
                },
            )
        elif "download_csv" in request.POST:
            download_formats = "csv"

            filter_condition = Q(workspace=workspace)
            if "usage_status" in [str(v.name) for v in base_model._meta.fields]:
                # Handle status filter for archived items
                status = request.GET.get("status")
                if status == "archived":
                    filter_condition &= Q(usage_status="archived")
                else:
                    # By default, show only active items
                    filter_condition &= Q(usage_status="active")
            view_filter = ViewFilter(filter_value=filter_dictionary)

            filter_condition = build_view_filter(
                filter_condition,
                view_filter,
                object_type,
                force_filter_list=additional_filter_fields,
            )
            items = []
            try:
                items = base_model.objects.filter(filter_condition).order_by(
                    f"-{id_field}" if id_field else "-created_at"
                )
            except Exception as e:
                traceback.print_exc()
                print(f"ERROR === commerce.py -- 2668: {e}")

            if lang == "ja":
                content_type = "text/csv; charset=Shift-JIS"
            else:
                content_type = "text/csv"

            response = HttpResponse(
                content_type=content_type,
            )
            response["Content-Disposition"] = (
                'attachment; filename="' + file_name + '.csv"'
            )

            csv_writer = None
            namecustomfields = []
            for i, col in enumerate(columns):
                try:
                    custom_field = custom_model.objects.get(id=col)
                    columns[i] = custom_field.name
                except:
                    pass
            csv_writer = csv.writer(response)
            headers = []
            for col in columns:
                if col in columns_display:
                    col = columns_display[col][lang]
                headers.append(col)

            if lang == "ja":
                headers = [remove_unsupported_characters(col) for col in headers]
            csv_writer.writerow(headers)

            if custom_model:
                namecustomfields = custom_model.objects.filter(
                    workspace=workspace, name__isnull=False
                ).values_list("name", flat=True)
                namecustomfields = [data.lower() for data in namecustomfields]

            for item in items:
                try:
                    csv_rows = []
                    for col in columns:
                        col = col.lower()
                        col_data = None

                        if "submitter__first_name" in col:
                            col = "submitter"

                        if col in [str(v.name) for v in base_model._meta.fields]:
                            if "submitter" in col:
                                col_data = item.submitter.first_name
                            elif col == "file":
                                if item.file:
                                    col_data = "https:" + reverse(
                                        "bills_download",
                                        host="app",
                                        kwargs={"id": item.id},
                                    )
                                else:
                                    col_data = ""
                            else:
                                col_data = getattr(item, col, None)
                                if "id_" in col:
                                    col_data = f"{'%04d' % col_data}"

                            if col_data and col_data != "None":
                                if "amount" in col or "price" in col:
                                    currency = getattr(item, "currency", "JPY")
                                    if currency == "JPY":
                                        col_data = int(col_data)
                                    else:
                                        col_data = "{:.2f}".format(col_data)

                        elif col in namecustomfields:
                            value_custom_field = custom_value_model.objects.filter(
                                Q(
                                    **{
                                        "field_name__name__iexact": col,
                                        custom_value_relation: item,
                                    }
                                )
                            ).last()
                            if value_custom_field:
                                if value_custom_field.field_name.type == "image":
                                    if value_custom_field.file:
                                        col_data = value_custom_field.file.url
                                else:
                                    col_data = value_custom_field.value
                        csv_rows.append(col_data)

                    if lang == "ja":
                        try:
                            csv_rows = [
                                remove_unsupported_characters(col) for col in csv_rows
                            ]
                        except:
                            pass
                    csv_writer.writerow(csv_rows)
                except Exception as e:
                    traceback.print_exc()
                    print(f"ERROR === commerce.py -- 553: {e}")

            return response

        elif "download_zip" in request.POST:
            download_formats = "files"

            filter_condition = Q(workspace=workspace)
            if "usage_status" in [str(v.name) for v in base_model._meta.fields]:
                # Handle status filter for archived items
                status = request.GET.get("status")
                if status == "archived":
                    filter_condition &= Q(usage_status="archived")
                else:
                    # By default, show only active items
                    filter_condition &= Q(usage_status="active")
            view_filter = ViewFilter(filter_value=filter_dictionary)
            filter_condition = build_view_filter(
                filter_condition,
                view_filter,
                object_type,
                force_filter_list=additional_filter_fields,
            )
            items = []
            try:
                items = base_model.objects.filter(filter_condition).order_by(
                    f"-{id_field}" if id_field else "-created_at"
                )
            except Exception as e:
                traceback.print_exc()
                print(f"ERROR === commerce.py -- 2668: {e}")

            csv_content = None
            csv_writer = None
            namecustomfields = []
            file_tuple = []
            for item in items:
                try:
                    field_value = getattr(item, file_field)
                    if isinstance(field_value, FieldFile):
                        files = [field_value]
                    else:
                        files = [item.file for item in field_value.all()]

                    for file in files:
                        file_tuple.append(
                            (
                                f"{item.title}-{item.id}.{file.url.split('.')[len(file.url.split('.')) - 1]}",
                                get_private_file(file.url),
                            )
                        )
                except Exception as e:
                    print(f"ERROR === commerce.py -- 911: {e}")

            if len(file_tuple) == 1:
                return HttpResponse(
                    file_tuple[0][1],
                    content_type="application/force-download",
                    headers={
                        "Content-Disposition": f'attachment; filename="{file_tuple[0][0]}"'
                    },
                )
            elif len(file_tuple) == 0:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message=f"ダウンロードする{file_name}データはありませんd.",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message=f"There is no {file_name} data to be downloaded.",
                        type="error",
                    )

                view_id = request.POST.get("view_id", "")
                if view_id == "None":
                    view_id = ""
                query_string = (
                    f"?view_id={view_id}&{reverse_query}"
                    if reverse_query
                    else f"?view_id={view_id}"
                )
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + query_string
                )

            return HttpResponse(
                generate_zip(file_tuple),
                content_type="application/force-download",
                headers={
                    "Content-Disposition": f'attachment; filename="{file_name}.zip"'
                },
            )


# ===================================== Specify your conditions here ===================================== #


def api_formula(request):
    obj_id = request.POST.get("obj_id", None)
    CustomFieldName_id = request.POST.get("CustomFieldName_id", None)
    object_type = request.POST.get("object_type")

    page_obj = get_page_object(object_type)
    base_model = page_obj["base_model"]
    custom_model = page_obj["custom_model"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]

    obj = base_model.objects.get(id=obj_id)
    custom_field = custom_model.objects.get(id=CustomFieldName_id)
    result = calculate_math(page_obj, obj, custom_field)
    if result:
        custom_field_values = custom_value_model.objects.filter(
            Q(**{"field_name": custom_field, custom_value_relation: obj})
        )
        if custom_field_values.count() > 0:
            for custom_field_value in custom_field_values:
                custom_field_value.value = result
                custom_field_value.save()
        else:
            custom_value_model_obj = custom_value_model(field_name=custom_field)
            setattr(custom_value_model_obj, custom_value_relation, obj)
            setattr(custom_value_model_obj, "value", result)
            custom_value_model_obj.save()

    context = {"value": result}
    return render(
        request, "data/common/custom_field/formula/row-partial-target.html", context
    )


def import_export_section(request):
    """
    Renders the import/export section for a given object type and section.

    Depending on the object type, returns the appropriate template and context for handling import/export operations, including URLs for bulk creation and header extraction.
    """
    context = {}
    if request.method == "GET":
        section = request.GET.get("section")
        object_type = request.GET.get("object_type", None)
        view_id = request.GET.get("view_id")
        if view_id == "None":
            view_id = None

        print("===========================================")
        print(object_type)
        print(section)
        print(view_id)

        context["section"] = section
        context["object_type"] = object_type
        context["view_id"] = view_id
        context["template_file"] = TEMPLATE_FILE[object_type][request.LANGUAGE_CODE]

        if object_type == TYPE_OBJECT_JOURNAL:
            context["url_post"] = "journal_csv_bulk_create"
            context["url_extractor"] = "journal_csv_header_extractor"
            return render(request, "data/journal/journal-import-form.html", context)
        else:
            context["url_post"] = "worker_csv_bulk_create"
            context["url_extractor"] = "worker_csv_header_extractor"

        return render(request, "data/common/import-export-section.html", context)
