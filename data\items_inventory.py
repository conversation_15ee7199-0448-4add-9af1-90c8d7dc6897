import ast
import copy
from datetime import datetime, timed<PERSON>ta
from io import StringIO
import json
import re
import traceback
import uuid

import chardet
from django.core.mail import EmailMessage
from django.core.paginator import EmptyPage, Paginator
from django.db.models import OuterRef, Prefetch, Q, Subquery
from django.forms.models import model_to_dict
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from django.template.loader import render_to_string
from django.utils import timezone
from django.views.decorators.http import require_GET, require_POST
from django_hosts.resolvers import reverse
import pandas as pd
import pytz
import shopify

from action.action import trigger_next_action
from data.accounts.association_labels import save_association_label
from data.constants.constant import (
    APP_TARGET_SLUG,
    CURRENCY_MODEL,
    DEFAULT_COLUMNS_INVENTORY,
    DEFAULT_COLUMNS_WAREHOUSE,
    DEFAULT_PERMISSION,
    EXCLUDE_SYNC_CHANNEL_NAME,
    INVENTORY_TRANSACTION_USAGE_CATEGORY,
    INVENTORY_USAGE_CATEGORY,
    TEMPLATE_FILE,
    WAREHOUSE_USAGE_CATEGORY,
)
from data.constants.properties_constant import (
    DEFAULT_OBJECT_DISPLAY,
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_WORKFLOW,
)
from data.constants.shopturbo_constant import TYPE_SHOPIFY_SYNC_KEY_PLATFORM
from data.inventory.background.export_csv_inventory import (
    ExportCSVInventoryPayload,
    export_csv_inventory,
)
from data.item.background.shopify_sync_items import (
    ShopifySyncItemsPayload,
    shopify_sync_dag,
)
from data.items import apply_shopturboitems_view_filter
from data.models import (
    Action,
    ActionHistory,
    ActionNode,
    ActionTaskHistory,
    ActionTracker,
    AdvanceSearchFilter,
    AppSetting,
    AssociationLabel,
    AssociationLabelObject,
    BackgroundJob,
    Channel,
    ExportImportConfiguration,
    ImportMappingFields,
    InventoryTransaction,
    InventoryTransactionNameCustomField,
    InventoryWarehouse,
    InventoryWarehouseNameCustomField,
    InventoryWarehousePlatforms,
    InventoryWarehouseValueCustomField,
    ItemPurchasePrice,
    Module,
    Notification,
    ObjectManager,
    PropertySet,
    PurchaseItems,
    PurchaseOrders,
    ShopTurboInventory,
    ShopTurboInventoryNameCustomField,
    ShopTurboInventoryPlatforms,
    ShopTurboInventoryValueCustomField,
    ShopTurboItems,
    ShopTurboItemsMappingFields,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsPlatforms,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
    ShopTurboOrders,
    ShopTurboSubscriptions,
    TransferHistory,
    View,
    ViewFilter,
    WorkflowActionTracker,
)
from data.models import TASK_STATUS, TASK_STATUS_JA
from data.shopturbo import apply_item_search_setting
from utils.actions import transfer_output_to_target_input
from utils.amazon import push_amazon_items
from utils.bgjobs.handler import add_hatchet_run_id, create_bg_job, is_run_bg_job
from utils.bgjobs.runner import trigger_bg_job
from utils.decorator import login_or_hubspot_required
from utils.discord import DiscordNotification
from utils.filter import apply_search_setting, build_view_filter
from utils.inventory import (
    create_inventory_transaction_helper,
    get_components_inventory,
    get_inventory_forecast_data,
    re_calculate_all_inventory_transactions,
    re_calculate_inventory_stock,
    recalculate_transactions_after,
    update_inventory_stock_price,
)
from utils.list_action_trigger_event import trigger_workflow_by_inventory_less_than
from utils.logger import logger
from utils.logger import logger
from utils.makeshop import push_makeshop_items
from utils.meter import get_workspace_available_storage, has_quota, sync_usage
from utils.project import get_ordered_views
from utils.properties.properties import (
    get_list_view_columns,
    get_object_display_based_columns,
    get_page_object,
)
from utils.rakuten_bg_jobs.rakuten_orders import push_rakuten_items
from utils.serializer import custom_serializer
from utils.shopify_bg_job.rest.push_data.push_inventories import (
    push_shopify_inventories,
)
from utils.shopify_bg_job.shopify_items import (
    delete_shopify_webhook_items,
    push_shopify_webhook_items,
)
from utils.shopify_bg_job.shopify_orders import api_version
from utils.utility import (
    assign_object_owner,
    build_redirect_url,
    get_permission_filter,
    get_redirect_workflow,
    get_workspace,
    is_valid_uuid,
    read_csv,
    save_custom_property,
    update_query_params_url,
)
from utils.workflow import get_workflows_by_first_action_trigger
from utils.workspace import get_permission
from utils.yahoo import push_yahoo_shopping_inventories

SHOPTURBO_APP_TARGET = "shopturbo"
SHOPTURBO_APP_SLUG = APP_TARGET_SLUG[SHOPTURBO_APP_TARGET]

POSTS_PER_PAGE = 30
POSTS_PER_PAGE_BEGIN = 29


@login_or_hubspot_required
def shopturbo_inventory(request):
    lang = request.LANGUAGE_CODE
    if lang == "ja":
        page_title = "在庫"
    else:
        page_title = "Inventory"

    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse("start", host="app"))

    page_obj = get_page_object(TYPE_OBJECT_INVENTORY, lang)
    base_model = page_obj["base_model"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    custom_value_file_model = page_obj["custom_value_file_model"]
    custom_value_file_relation = page_obj["custom_value_file_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    id_field = page_obj["id_field"]

    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=workspace, app_target=SHOPTURBO_APP_TARGET
    )

    if request.method == "GET":
        config_view = None
        archive = None
        view_id = request.GET.get("view_id", None)
        inventory_id = request.GET.get("inventory_id", None)
        transfer_history_id = request.GET.get("imported", None)
        transfer_history = None
        if transfer_history_id:
            transfer_history = TransferHistory.objects.filter(
                id=transfer_history_id
            ).first()

        permission = get_permission(
            object_type=TYPE_OBJECT_INVENTORY, user=request.user
        )
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == "hide":
            context = {
                "permission": permission,
                "page_title": page_title,
            }
            return render(request, "data/shopturbo/inventory.html", context)

        views = get_ordered_views(workspace, TYPE_OBJECT_INVENTORY, user=request.user)
        view = None
        if view_id and view_id != "None":
            view = View.objects.filter(id=view_id).first()
            if not view or (
                view.is_private and (not request.user or view.user != request.user)
            ):
                view, _ = View.objects.get_or_create(
                    workspace=workspace,
                    title__isnull=True,
                    target=TYPE_OBJECT_INVENTORY,
                )
                view_id = view.id
        else:
            view, _ = View.objects.get_or_create(
                workspace=workspace, title__isnull=True, target=TYPE_OBJECT_INVENTORY
            )
            view_id = view.id

        view_filter, _ = ViewFilter.objects.get_or_create(view=view)

        # Log ViewFilter creation details for inventory
        logger.info(
            "Creating ViewFilter for inventory search - view_id: %s, workspace: %s, target: %s",
            view_id,
            workspace.id,
            TYPE_OBJECT_INVENTORY,
        )

        # Log ViewFilter object state after creation
        logger.info(
            "ViewFilter created for inventory - view_id: %s, view_type: %s, column_count: %s, filter_value_keys: %s, has_view: %s",
            str(view_filter.view.id) if view_filter.view else None,
            view_filter.view_type,
            len(view_filter.column) if view_filter.column else 0,
            list(view_filter.filter_value.keys()) if view_filter.filter_value else [],
            view_filter.view is not None,
        )

        config_view = view_filter.view_type
        archive = view_filter.archive

        pagination_number = view_filter.pagination
        if not pagination_number:
            pagination_number = 25

        current_inventory = True
        if view_filter.value:
            if "current_inventory" in view_filter.value:
                current_inventory = bool(view_filter.value["current_inventory"])

        shopturbo_inventory_columns = DEFAULT_COLUMNS_INVENTORY[2:].copy()
        if view_filter:
            if view_filter.column:
                if isinstance(view_filter.column, str):
                    shopturbo_inventory_columns = ast.literal_eval(view_filter.column)
                else:
                    shopturbo_inventory_columns = view_filter.column
                if "inventory_id" in shopturbo_inventory_columns:
                    shopturbo_inventory_columns.remove("inventory_id")
                    view_filter.column = str(shopturbo_inventory_columns)
                    view_filter.save()
                if "checkbox" in shopturbo_inventory_columns:
                    shopturbo_inventory_columns.remove("checkbox")
                    view_filter.column = str(shopturbo_inventory_columns)
                    view_filter.save()
                for idx, column in enumerate(["checkbox", "inventory_id"]):
                    shopturbo_inventory_columns.insert(idx, column)
                shopturbo_inventory_columns = [
                    data.lower() for data in shopturbo_inventory_columns
                ]
            else:
                for idx, column in enumerate(["checkbox", "inventory_id"]):
                    shopturbo_inventory_columns.insert(idx, column)
                shopturbo_inventory_columns = [
                    data.lower() for data in shopturbo_inventory_columns
                ]
                view_filter.column = shopturbo_inventory_columns
                view_filter.save()

        filter_conditions = Q(workspace=workspace)

        filter_conditions &= get_permission_filter(permission, request.user)

        # Handle status filter for archived items
        status = request.GET.get("status")
        if status == "archived":
            filter_conditions &= Q(status="archived")
        else:
            # By default, show only active items
            filter_conditions &= Q(status="active")
        search_q = request.GET.get("q")

        advance_search = None
        if transfer_history:
            filter_conditions &= Q(
                inventory_id__in=transfer_history.checkpoint_details.get("imported", [])
            )
        else:
            advance_search = AdvanceSearchFilter.objects.filter(
                workspace=workspace, object_type=TYPE_OBJECT_INVENTORY, type="default"
            ).first()
            if not advance_search:
                advance_search = None

            if advance_search:
                if advance_search.search_settings:
                    app_setting.search_setting_inventory = (
                        advance_search.search_settings
                    )
                    app_setting.save()
            logger.debug("search settings:", app_setting.search_setting_inventory)

        if search_q:
            # Log search query details for inventory
            logger.info(
                "Processing inventory search query - query: %s, view_id: %s, search_settings: %s",
                search_q,
                view_id,
                app_setting.search_setting_inventory,
            )

            match_special_char = re.search(r"#(\d+)", search_q)
            if match_special_char:
                number = match_special_char.group(1)  # Get the number part
                if number:
                    filter_conditions &= Q(inventory_id=number)
                    logger.debug(
                        "Applied special character search for inventory_id: %s", number
                    )
            else:
                search_q = search_q.lower()
                search_filters = Q()
                search_filters |= Q(inventory_id__icontains=search_q)
                if app_setting.search_setting_inventory:
                    search_fields = app_setting.search_setting_inventory.split(",")

                    logger.info(
                        "Processing inventory search fields - fields: %s, view_filter_view_id: %s, view_filter_type: %s",
                        search_fields,
                        str(view_filter.view.id) if view_filter.view else None,
                        view_filter.view_type,
                    )

                    for s_field in search_fields:
                        # Log each search field processing
                        logger.debug(
                            "Processing inventory search field - field: %s, is_uuid: %s",
                            s_field,
                            is_valid_uuid(s_field),
                        )

                        if s_field in ["item__name"]:
                            search_filters |= apply_search_setting(
                                "commerce_inventory",
                                view_filter,
                                s_field,
                                search_q,
                                force=True,
                            )
                        else:
                            search_filters |= apply_search_setting(
                                "commerce_inventory", view_filter, s_field, search_q
                            )
                filter_conditions &= search_filters
                logger.info(
                    "Final inventory search filter conditions applied - query_str: %s",
                    str(filter_conditions),
                )

        advance_search_filter = None
        if advance_search:
            if advance_search.is_active:
                advance_search_filter = advance_search.search_filter
                advance_search_filter_status = advance_search.search_filter_status

                # Log advance search filter details
                logger.info(
                    "Processing advance search filter for inventory - raw_filter: %s, filter_status: %s",
                    advance_search_filter,
                    advance_search_filter_status,
                )

                if advance_search_filter_status:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                        and advance_search_filter_status.get(k) != "False"
                    }
                else:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }

                # Log filtered advance search filter
                logger.info(
                    "Filtered advance search filter for inventory - filtered_filter: %s",
                    advance_search_filter,
                )

                try:
                    if (
                        advance_search_filter.get("usage_status", {}).get("value")
                        == "all"
                    ):
                        del advance_search_filter["usage_status"]
                except KeyError:
                    pass

                view_filter.filter_value = advance_search_filter

                # Log final view filter value
                logger.info(
                    "Set view_filter.filter_value for inventory - final_filter_value: %s",
                    view_filter.filter_value,
                )

        if not transfer_history:
            # Log before applying view filter
            logger.info(
                "Applying view filter for inventory - before_filter: %s, view_filter_id: %s, filter_value: %s",
                str(filter_conditions),
                str(view_filter.view.id) if view_filter.view else None,
                view_filter.filter_value,
            )

            # Log workspace information for debugging workspace-specific issues
            logger.info(
                "Inventory filtering workspace context - workspace_id: %s, workspace_name: %s, user_id: %s",
                workspace.id,
                workspace.name,
                request.user.id if request.user else None,
            )

            # Log detailed filter_value structure if it contains excludes filters
            has_excludes_filter = False
            if view_filter.filter_value:
                for filter_key, filter_config in view_filter.filter_value.items():
                    if filter_config.get("key") == "excludes":
                        has_excludes_filter = True
                        logger.info(
                            "EXCLUDES filter detected in inventory view_filter - filter_key: %s, filter_config: %s",
                            filter_key,
                            filter_config,
                        )

            # Store the conditions before applying view filters (similar to contact search fix)
            pre_filter_conditions = filter_conditions
            logger.info(
                "Filter conditions before build_view_filter - query_str: %s",
                str(filter_conditions),
            )

            filter_conditions = build_view_filter(
                filter_conditions, view_filter, TYPE_OBJECT_INVENTORY, request=request
            )

            # Log after applying view filter
            logger.info(
                "Applied view filter for inventory - after_filter: %s, view_filter_type: %s",
                str(filter_conditions),
                view_filter.view_type,
            )

            # If we have excludes filters and build_view_filter resulted in no results,
            # check if the excludes filter is being overridden (similar to contact search fix)
            if has_excludes_filter and filter_conditions != pre_filter_conditions:
                try:
                    pre_count = ShopTurboInventory.objects.filter(
                        pre_filter_conditions
                    ).count()
                    post_count = ShopTurboInventory.objects.filter(
                        filter_conditions
                    ).count()
                    logger.info(
                        "Excludes filter result check - pre_filter_count: %s, post_filter_count: %s",
                        pre_count,
                        post_count,
                    )

                    # If the filter eliminated all results unexpectedly, consider fallback
                    if post_count == 0 and pre_count > 0:
                        logger.warning(
                            "build_view_filter with excludes eliminated all results - pre_count: %s, post_count: %s, workspace: %s",
                            pre_count,
                            post_count,
                            workspace.name,
                        )

                        # Check if this is due to conflicting filters by testing excludes filter alone
                        excludes_only_filter = Q(workspace=workspace, status="active")
                        excludes_only_filter &= get_permission_filter(
                            permission, request.user
                        )

                        # Apply only the excludes filters
                        excludes_view_filter = ViewFilter(view=view_filter.view)
                        excludes_view_filter.filter_value = {
                            k: v
                            for k, v in view_filter.filter_value.items()
                            if v.get("key") == "excludes"
                        }

                        if excludes_view_filter.filter_value:
                            excludes_only_filter = build_view_filter(
                                excludes_only_filter,
                                excludes_view_filter,
                                TYPE_OBJECT_INVENTORY,
                                request=request,
                            )
                            excludes_only_count = ShopTurboInventory.objects.filter(
                                excludes_only_filter
                            ).count()

                            logger.info(
                                "Excludes-only filter test - count: %s, original_pre_count: %s",
                                excludes_only_count,
                                pre_count,
                            )

                            # If excludes-only filter works but combined filter doesn't,
                            # there's likely a conflict with other filters
                            if (
                                excludes_only_count > 0
                                and excludes_only_count < pre_count
                            ):
                                logger.warning(
                                    "Detected filter conflict: excludes filter works alone but not with other filters - workspace: %s",
                                    workspace.name,
                                )

                except Exception as e:
                    logger.error(
                        "Error during excludes filter fallback check: %s", str(e)
                    )
        logger.debug(filter_conditions)

        # Log final filter conditions before query execution
        logger.info(
            "Final inventory query filter conditions - filter: %s, current_inventory: %s",
            str(filter_conditions),
            current_inventory,
        )

        if current_inventory:
            try:
                shopturbo_inventories = (
                    ShopTurboInventory.objects.select_related("workspace", "warehouse")
                    .prefetch_related(
                        "item",
                        "item_variant",
                        "inventory__channel",  # For platform data
                        "shopturbo_inventory_custom_field_relations",  # For custom fields
                    )
                    .filter(date__lte=timezone.now())
                    .filter(filter_conditions)
                )

                # Log query result count
                logger.info(
                    "Inventory query executed (current_inventory=True) - result_count: %s",
                    shopturbo_inventories.count(),
                )
            except Exception as e:
                logger.error(
                    "Error executing inventory query with current_inventory=True: %s",
                    str(e),
                )
                logger.debug(e)
                shopturbo_inventories = (
                    ShopTurboInventory.objects.select_related("workspace", "warehouse")
                    .prefetch_related(
                        "item",
                        "item_variant",
                        "inventory__channel",
                        "shopturbo_inventory_custom_field_relations",
                    )
                    .filter(date__lte=timezone.now())
                )
        else:
            shopturbo_inventories = (
                ShopTurboInventory.objects.select_related("workspace", "warehouse")
                .prefetch_related(
                    "item",
                    "item_variant",
                    "inventory__channel",
                    "shopturbo_inventory_custom_field_relations",
                )
                .filter(filter_conditions)
            )

            # Log query result count
            logger.info(
                "Inventory query executed (current_inventory=False) - result_count: %s",
                shopturbo_inventories.count(),
            )

        try:
            if view_filter.sort_order_by:
                order_method = view_filter.sort_order_method
                order_by = view_filter.sort_order_by

                if order_by == "items":
                    order_by = "item__item_id"

                # Handle invalid field mapping for inventory
                if order_by == "purchase_price_currency":
                    # Map to the correct field for inventory model
                    order_by = "currency"
                    logger.warning(
                        "WARNING: Mapped invalid field 'purchase_price_currency' to 'currency' for inventory sorting"
                    )
                elif order_by == "purchase_price":
                    # Purchase price field doesn't exist in inventory model, fallback to default sorting
                    logger.warning(
                        "WARNING: Invalid field 'purchase_price' for inventory, using default sorting"
                    )
                    shopturbo_inventories = shopturbo_inventories.distinct(
                        "id", "inventory_id", "date", "created_at"
                    ).order_by("-inventory_id", "-date", "-created_at")
                elif order_by == "supplier":
                    # Supplier field doesn't exist in inventory model, fallback to default sorting
                    logger.warning(
                        "WARNING: Invalid field 'supplier' for inventory, using default sorting"
                    )
                    shopturbo_inventories = shopturbo_inventories.distinct(
                        "id", "inventory_id", "date", "created_at"
                    ).order_by("-inventory_id", "-date", "-created_at")
                elif is_valid_uuid(order_by):
                    # Check if this is an inventory custom field
                    field_name = ShopTurboInventoryNameCustomField.objects.filter(
                        id=order_by, workspace=workspace
                    ).first()

                    if field_name:
                        # Optimize custom field sorting with a single subquery
                        custom_value_subquery = (
                            ShopTurboInventoryValueCustomField.objects.filter(
                                inventory=OuterRef("pk"), field_name=field_name
                            ).values(
                                "value_time"
                                if field_name.type in ["date", "date_time"]
                                else "value"
                            )[:1]
                        )

                        # Apply sorting with a single annotation and order_by, including distinct
                        shopturbo_inventories = (
                            shopturbo_inventories.annotate(
                                custom_value=Subquery(custom_value_subquery)
                            )
                            .distinct("id", "custom_value")
                            .order_by(
                                "custom_value"
                                if order_method == "asc"
                                else "-custom_value"
                            )
                        )
                    else:
                        # Check if this might be an item custom field instead
                        from data.models.item import ShopTurboItemsNameCustomField

                        item_field = ShopTurboItemsNameCustomField.objects.filter(
                            id=order_by, workspace=workspace
                        ).first()
                        if item_field:
                            # This is an item custom field - we need to sort through the item relationship
                            from data.models.item import ShopTurboItemsValueCustomField

                            # Create a subquery to get the item custom field value
                            # Since item is a ManyToManyField, we need to use the correct relationship
                            item_custom_value_subquery = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    item__in=OuterRef("item"), field_name=item_field
                                ).values(
                                    "value_time"
                                    if item_field.type in ["date", "date_time"]
                                    else "value"
                                )[:1]
                            )

                            # Apply sorting through the item relationship
                            shopturbo_inventories = (
                                shopturbo_inventories.annotate(
                                    item_custom_value=Subquery(
                                        item_custom_value_subquery
                                    )
                                )
                                .distinct("id", "item_custom_value")
                                .order_by(
                                    "item_custom_value"
                                    if order_method == "asc"
                                    else "-item_custom_value"
                                )
                            )
                        else:
                            # Custom field not found, fall back to default sorting
                            logger.debug(
                                f"WARNING: Custom field with ID '{order_by}' not found for inventory, using default sorting"
                            )
                            shopturbo_inventories = shopturbo_inventories.distinct(
                                "id", "inventory_id", "date", "created_at"
                            ).order_by("-inventory_id", "-date", "-created_at")

                elif "-SKU" in order_by or "-Inventory ID" in order_by:
                    values = "platform_sku" if "-SKU" in order_by else "platform_id"
                    name = (
                        order_by.replace("-SKU", "")
                        if "-SKU" in order_by
                        else order_by.replace("-Inventory ID", "")
                    )
                    channel = Channel.objects.filter(
                        workspace=workspace, name=name, integration__slug="shopify"
                    ).first()
                    inv_platform = ShopTurboInventoryPlatforms.objects.filter(
                        inventory=OuterRef("pk"), channel=channel
                    ).values(values)[:1]

                    shopturbo_inventories = shopturbo_inventories.annotate(
                        custom_value=Subquery(inv_platform)
                    )

                    if order_method == "asc":
                        shopturbo_inventories = shopturbo_inventories.distinct(
                            "id", "custom_value"
                        ).order_by("custom_value")
                    else:
                        shopturbo_inventories = shopturbo_inventories.distinct(
                            "id", "custom_value"
                        ).order_by("-custom_value")
                else:
                    # Validate field exists in model before applying order_by
                    try:
                        # Check if field exists by trying to get the field from the model
                        ShopTurboInventory._meta.get_field(order_by.replace("-", ""))
                        if order_method == "asc":
                            shopturbo_inventories = shopturbo_inventories.distinct(
                                "id", order_by
                            ).order_by(order_by)
                        else:
                            shopturbo_inventories = shopturbo_inventories.distinct(
                                "id", order_by
                            ).order_by("-" + order_by)
                    except:
                        # Field doesn't exist, fall back to default sorting
                        logger.debug(
                            f"WARNING: Invalid sort field '{order_by}' for inventory, using default sorting"
                        )
                        shopturbo_inventories = shopturbo_inventories.distinct(
                            "id", "inventory_id", "date", "created_at"
                        ).order_by("-inventory_id", "-date", "-created_at")
            else:
                shopturbo_inventories = shopturbo_inventories.distinct(
                    "id", "inventory_id", "date", "created_at"
                ).order_by("-inventory_id", "-date", "-created_at")
        except Exception as e:
            logger.debug("===== Debug Error at ShopTurbo Items =====", e)
            shopturbo_inventories = shopturbo_inventories.distinct(
                "id", "inventory_id", "date", "created_at"
            ).order_by("inventory_id", "date", "created_at")

        # Get page number and validate
        try:
            page = int(request.GET.get("page", 1))
            if page < 1:
                page = 1
        except (TypeError, ValueError):
            page = 1

        # Calculate pagination indices
        start = (page - 1) * int(pagination_number)
        end = start + int(pagination_number)

        # Get total count for pagination
        total_count = shopturbo_inventories.count()

        shopturbo_inventories = shopturbo_inventories[start:end]

        # Create paginator for template rendering
        paginator = Paginator(range(total_count), pagination_number)
        page_content = paginator.page(page)

        # Set pagination display values
        paginator_item_begin = start
        paginator_item_end = end

        forecast_data = None
        forecast_dates = []
        if config_view == "forecast":
            forecast_data = get_inventory_forecast_data(
                filter_conditions, workspace.timezone, view_filter
            )
            forecast_dates = sorted(forecast_data.keys())
            shopturbo_inventory_columns = [" ", " - "] + [
                date for date in forecast_dates
            ]
            # logger.debug(f'... INFO === shopturbo.py -- 2913: Forecast data - {forecast_data}')
            if forecast_data:
                date_gte = None
                date_lte = None
                date_range = None

                for condition in filter_conditions.children:
                    if isinstance(condition, tuple):
                        key, value = condition
                        try:
                            if "__gte" in key:
                                if isinstance(value, str):
                                    date_gte = datetime.strptime(value, "%Y-%m-%d")
                                else:
                                    date_gte = value.date()
                            if "__lte" in key:
                                if isinstance(value, str):
                                    date_lte = datetime.strptime(value, "%Y-%m-%d")
                                else:
                                    date_lte = value.date()
                            if "__range" in key:
                                if isinstance(value[0], str) and isinstance(
                                    value[1], str
                                ):
                                    date_range = (
                                        datetime.strptime(value[0], "%Y-%m-%d"),
                                        datetime.strptime(value[1], "%Y-%m-%d"),
                                    )
                                else:
                                    date_range = (value[0].date(), value[1].date())
                        except Exception as e:
                            logger.debug(e)
                            DiscordNotification().send_message(
                                f"Forecast data filtering error: {e}"
                            )

                # Use extracted dates to define the range
                if date_range:
                    start_date = date_range[0]
                    end_date = date_range[1]
                elif date_gte and date_lte:
                    start_date = date_gte
                    end_date = date_lte
                elif date_gte:
                    start_date = date_gte
                    end_date = max(
                        forecast_data.keys(),
                        key=lambda d: datetime.strptime(d, "%Y-%m-%d").date(),
                    )  # Latest date in forecast_data
                elif date_lte:
                    start_date = min(
                        forecast_data.keys(),
                        key=lambda d: datetime.strptime(
                            # Earliest date in forecast_data
                            d,
                            "%Y-%m-%d",
                        ).date(),
                    )
                    end_date = date_lte
                else:
                    start_date = min(
                        forecast_data.keys(),
                        key=lambda d: datetime.strptime(
                            # Earliest date in forecast_data
                            d,
                            "%Y-%m-%d",
                        ).date(),
                    )
                    end_date = max(
                        forecast_data.keys(),
                        key=lambda d: datetime.strptime(d, "%Y-%m-%d").date(),
                    )  # Latest date in forecast_data

                if isinstance(start_date, str):
                    start_date = datetime.strptime(start_date, "%Y-%m-%d")
                elif hasattr(start_date, "date") and callable(start_date.date):
                    # Already a datetime object, do nothing
                    pass
                else:
                    # Convert date object to datetime
                    start_date = datetime.combine(start_date, datetime.min.time())

                if isinstance(end_date, str):
                    end_date = datetime.strptime(end_date, "%Y-%m-%d")
                elif hasattr(end_date, "date") and callable(end_date.date):
                    # Already a datetime object, do nothing
                    pass
                else:
                    # Convert date object to datetime
                    end_date = datetime.combine(end_date, datetime.min.time())

                logger.debug(
                    f"... INFO === shopturbo.py -- 2950: Start date - {start_date}, End date - {end_date}"
                )
                delta = end_date - start_date
                forecast_dates = [
                    (start_date + timedelta(days=i)).strftime("%Y-%m-%d")
                    for i in range(delta.days + 1)
                ]
                shopturbo_inventory_columns = ["  ", " - "] + [
                    date for date in forecast_dates
                ]
                forecast_data = fill_forecast_data(forecast_data)

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_INVENTORY
        ).order_by("created_at")

        set_id = request.GET.get("set_id")
        if view_filter.view.form:
            set_id = view_filter.view.form.id
        else:
            if not set_id:
                property_set_default = property_sets.filter(as_default=True).first()
                if property_set_default:
                    set_id = property_set_default.id

        shopturbo_inventory_columns_table = copy.deepcopy(shopturbo_inventory_columns)
        # Prevent redundant checking in each table row
        for idx, shopturbo_inventory_column in enumerate(shopturbo_inventory_columns):
            if is_valid_uuid(shopturbo_inventory_column):
                shopturbo_inventory_column_field = (
                    ShopTurboInventoryNameCustomField.objects.filter(
                        id=shopturbo_inventory_column
                    ).first()
                )
                if not shopturbo_inventory_column_field:
                    shopturbo_inventory_columns_table[idx] = (
                        f"{shopturbo_inventory_column}|item_custom_field"
                    )
                else:
                    pass

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list("id", flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ",".join(str(id) for id in members)

        context = {
            "page_title": page_title,
            "search_q": search_q,
            # NOTE: use Object Type
            "app_slug": SHOPTURBO_APP_SLUG,
            "shopturbo_inventories": shopturbo_inventories,
            "shopturbo_inventory_columns": shopturbo_inventory_columns,
            "shopturbo_inventory_columns_table": shopturbo_inventory_columns_table,
            "CURRENCY_MODEL": CURRENCY_MODEL,
            "page": page,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "paginator": paginator,
            "config_view": config_view,
            "view_filter": view_filter,
            "view_id": view_id,
            "target": TYPE_OBJECT_INVENTORY,
            "views": views,
            "forecast_data": forecast_data,
            "forecast_dates": forecast_dates,
            "inventory_id": inventory_id,
            "current_inventory": current_inventory,
            "permission": permission,
            "open_drawer": request.GET.get("open_drawer"),
            "advance_search": advance_search,
            "set_id": set_id,
            "advance_search_filter": advance_search_filter,
            "group_members": members,
        }

        return render(request, "data/shopturbo/inventory.html", context)

    # Post Functions
    else:
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY]
        module_slug = request.POST.get("module")
        if not module_slug:
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_INVENTORY
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug

        if "bulk_duplicate" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                # Check if view_id is a valid UUID before using it in the filter
                if view_id and is_valid_uuid(view_id):
                    view = View.objects.filter(id=view_id).first()
                else:
                    view = None

                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_INVENTORY, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_INVENTORY,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            for obj in objects:
                original_obj = base_model.objects.get(id=obj.id)
                customFields = custom_value_model.objects.filter(
                    **{custom_value_relation: obj}
                )

                obj.id = uuid.uuid4()
                setattr(obj, id_field, None)
                obj.created_at = timezone.now()
                obj.save()

                # Handle many-to-many relationship for items
                if hasattr(obj, "item") and hasattr(
                    obj._meta.get_field("item"), "many_to_many"
                ):
                    original_items = original_obj.item.all()
                    logger.debug("original_items: ", original_items)
                    for item in original_items:
                        getattr(obj, "item").add(item)

                for field in customFields:
                    old_field_id = field.id
                    field.id = None
                    setattr(field, custom_value_relation, obj)
                    field.save()

                    # duplicate file or image
                    customFieldsValueFile = custom_value_file_model.objects.filter(
                        **{f"{custom_value_file_relation}__id": old_field_id}
                    )
                    if customFieldsValueFile:
                        for value_file in customFieldsValueFile:
                            value_file.id = None
                            value_file.created_at = timezone.now()
                            setattr(value_file, custom_value_file_relation, field)
                            value_file.save()

                create_inventory_transaction_helper(obj, request.user, lang=lang)

        elif "update-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            view_name = request.POST.get("view_name", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column = request.POST.get("column", None)
            view_pagination = request.POST.get("pagination", None)
            status_selector = request.POST.get("status-selector", None)
            current_inventory = request.POST.get("current_inventory", None)

            order_by = request.POST.get("order-by", None)
            sort_method = request.POST.get("sort-method", None)

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if view.is_private and (view.user == request.user):
                    view.is_private = bool(request.POST.get("set-private-view", False))
                    view.user = request.user if view.is_private else None
                    view.save()
            # create
            else:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                view = View.objects.create(
                    workspace=workspace,
                    title=view_name,
                    target="commerce_inventory",
                    is_private=is_private,
                    user=user,
                )

            property_set_is_default_input = request.POST.get(
                "property_set_default", None
            )
            if property_set_is_default_input:
                default_set = PropertySet.objects.get(id=property_set_is_default_input)
                view.form = default_set
                default_set.as_default = True
                default_set.save()
                # check default
                PropertySet.objects.filter(
                    workspace=default_set.workspace, target=default_set.target
                ).exclude(id=default_set.id).update(as_default=False)

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            if view_name:
                view.title = view_name

            if view_table:
                view_filter.view_type = view_table

            view_filter.archive = archive

            # convert to normal List
            column = column.split(",")
            if view and column[0]:
                view_filter.column = column
            else:
                view_filter.column = DEFAULT_COLUMNS_INVENTORY[2:].copy()

            if view_pagination not in ["25", "50", "100"]:
                view_pagination = 25
            view_filter.pagination = view_pagination

            if view_table and status_selector:
                if view_table == "kanban":
                    view_filter.choice_customfield = status_selector
                else:
                    view_filter.choice_customfield = None

            if order_by:
                view_filter.sort_order_by = order_by
            else:
                view_filter.sort_order_by = None

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = "asc"
                else:
                    view_filter.sort_order_method = None

            view_filter_value = {}
            if current_inventory:
                view_filter_value["current_inventory"] = True
            else:
                view_filter_value["current_inventory"] = False

            if view_table == "forecast":
                show_available = request.POST.get("show-available", None)
                view_filter_value["show_available"] = show_available
                show_committed = request.POST.get("show-committed", None)
                view_filter_value["show_committed"] = show_committed
                show_unavailable = request.POST.get("show-unavailable", None)
                view_filter_value["show_unavailable"] = show_unavailable

            view_filter.value = view_filter_value

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            # Handle "between" operation (have two filter values)
            filter_values2 = request.POST.getlist("filter_value-2", None)
            idx_filter_values2 = 0
            for idx, filter_type in enumerate(filter_types):
                value = filter_values[idx]
                if filter_options[idx] == "between":
                    try:
                        value = (
                            f"{filter_values[idx]}-{filter_values2[idx_filter_values2]}"
                        )
                    except:
                        value = ""
                    idx_filter_values2 += 1
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": value,
                }
            view_filter.filter_value = filter_dictionary
            view_filter.save()
            view.save()

            if view.title:
                if module_slug:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + "?view_id="
                        + str(view.id)
                    )
            else:
                if module_slug:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
            return redirect(reverse("main", host="app"))

        elif "task_id" in request.POST:
            task = TransferHistory.objects.filter(
                workspace=workspace, id=request.POST.get("task_id")
            ).first()
            if task:
                task.status = "canceled"
                task.save()

        elif "bulk_delete_items" in request.POST:
            logger.debug(request.POST)
            account_ids = request.POST.getlist("checkbox")
            flag_all = request.POST.get("flag_all")

            if flag_all:
                view_id = request.POST.get("view_id")
                search_q = request.POST.get("q")

                view = View.objects.filter(id=view_id).first()
                view_filter, _ = ViewFilter.objects.get_or_create(view=view)
                config_view = view_filter.view_type
                archive = view_filter.archive

                current_inventory = True
                if view_filter.value:
                    if "current_inventory" in view_filter.value:
                        current_inventory = bool(view_filter.value["current_inventory"])

                filter_conditions = Q(workspace=workspace)

                # Handle status filter for archived items
                status = request.GET.get("status")
                if status == "archived":
                    filter_conditions &= Q(status="archived")
                else:
                    # By default, show only active items
                    filter_conditions &= Q(status="active")
                if search_q:
                    match_special_char = re.search(r"#(\d+)", search_q)
                    if match_special_char:
                        number = match_special_char.group(1)  # Get the number part
                        if number:
                            filter_conditions &= Q(inventory_id=number)
                    else:
                        search_q = search_q.lower()
                        search_filters = Q()
                        search_filters |= Q(inventory_id__icontains=search_q)
                        if app_setting.search_setting_inventory:
                            search_fields = app_setting.search_setting_inventory.split(
                                ","
                            )
                            for s_field in search_fields:
                                if s_field in ["item__name"]:
                                    search_filters |= apply_search_setting(
                                        "commerce_inventory",
                                        view_filter,
                                        s_field,
                                        search_q,
                                        force=True,
                                    )
                                else:
                                    search_filters |= apply_search_setting(
                                        "commerce_inventory",
                                        view_filter,
                                        s_field,
                                        search_q,
                                    )
                        filter_conditions &= search_filters

                filter_conditions = build_view_filter(
                    filter_conditions, view_filter, TYPE_OBJECT_INVENTORY
                )

                if current_inventory:
                    try:
                        shopturbo_inventories = (
                            ShopTurboInventory.objects.select_related(
                                "workspace", "warehouse"
                            )
                            .prefetch_related(
                                "item",
                                "item_variant",
                                "inventory__channel",
                                "shopturbo_inventory_custom_field_relations",
                            )
                            .filter(date__lte=timezone.now())
                            .filter(filter_conditions)
                            .filter(status="active")
                        )
                    except Exception as e:
                        logger.debug(e)
                        shopturbo_inventories = (
                            ShopTurboInventory.objects.select_related(
                                "workspace", "warehouse"
                            )
                            .prefetch_related(
                                "item",
                                "item_variant",
                                "inventory__channel",
                                "shopturbo_inventory_custom_field_relations",
                            )
                            .filter(date__lte=timezone.now(), status="active")
                        )
                else:
                    shopturbo_inventories = (
                        ShopTurboInventory.objects.select_related(
                            "workspace", "warehouse"
                        )
                        .prefetch_related(
                            "item",
                            "item_variant",
                            "inventory__channel",
                            "shopturbo_inventory_custom_field_relations",
                        )
                        .filter(filter_conditions)
                        .filter(status="active")
                    )
                shopturbo_inventories.update(status="archived")
            else:
                ShopTurboInventory.objects.filter(id__in=account_ids).update(
                    status="archived"
                )
            sync_usage(workspace, INVENTORY_USAGE_CATEGORY)

        elif "bulk_restore_items" in request.POST:
            logger.debug(request.POST)
            account_ids = request.POST.getlist("checkbox")
            flag_all = request.POST.get("flag_all")

            available_storage = get_workspace_available_storage(
                workspace, INVENTORY_USAGE_CATEGORY
            )
            sync_usage(workspace, INVENTORY_USAGE_CATEGORY)
            logger.debug(available_storage)
            if available_storage is None or available_storage > 0:
                if flag_all:
                    view_id = request.POST.get("view_id")
                    search_q = request.POST.get("q")

                    view = View.objects.filter(id=view_id).first()
                    view_filter, _ = ViewFilter.objects.get_or_create(view=view)
                    config_view = view_filter.view_type
                    archive = view_filter.archive

                    current_inventory = True
                    if view_filter.value:
                        if "current_inventory" in view_filter.value:
                            current_inventory = bool(
                                view_filter.value["current_inventory"]
                            )

                    filter_conditions = Q(workspace=workspace)

                    # Handle status filter for archived items
                    status = request.GET.get("status")
                    if status == "archived":
                        filter_conditions &= Q(status="archived")
                    else:
                        # By default, show only active items
                        filter_conditions &= Q(status="active")
                    if search_q:
                        match_special_char = re.search(r"#(\d+)", search_q)
                        if match_special_char:
                            number = match_special_char.group(1)  # Get the number part
                            if number:
                                filter_conditions &= Q(inventory_id=number)
                        else:
                            search_q = search_q.lower()
                            search_filters = Q()
                            search_filters |= Q(inventory_id__icontains=search_q)
                            if app_setting.search_setting_inventory:
                                search_fields = (
                                    app_setting.search_setting_inventory.split(",")
                                )
                                for s_field in search_fields:
                                    if s_field in ["item__name"]:
                                        search_filters |= apply_search_setting(
                                            "commerce_inventory",
                                            view_filter,
                                            s_field,
                                            search_q,
                                            force=True,
                                        )
                                    else:
                                        search_filters |= apply_search_setting(
                                            "commerce_inventory",
                                            view_filter,
                                            s_field,
                                            search_q,
                                        )
                            filter_conditions &= search_filters

                    filter_conditions = build_view_filter(
                        filter_conditions, view_filter, TYPE_OBJECT_INVENTORY
                    )

                    if current_inventory:
                        try:
                            shopturbo_inventories = (
                                ShopTurboInventory.objects.filter(
                                    date__lte=timezone.now()
                                )
                                .filter(filter_conditions)
                                .filter(status="archived")
                            )
                        except Exception as e:
                            logger.debug(e)
                            shopturbo_inventories = ShopTurboInventory.objects.filter(
                                date__lte=timezone.now(), status="archived"
                            )
                    else:
                        shopturbo_inventories = ShopTurboInventory.objects.filter(
                            filter_conditions
                        ).filter(status="archived")
                    ids = shopturbo_inventories.order_by("-created_at").values_list(
                        "id", flat=True
                    )
                    ShopTurboInventory.objects.filter(
                        workspace=workspace, id__in=ids[:available_storage]
                    ).update(status="active")
                else:
                    ids = (
                        ShopTurboInventory.objects.filter(
                            workspace=workspace, status="archived", id__in=account_ids
                        )
                        .order_by("-created_at")
                        .values_list("id", flat=True)
                    )
                    ShopTurboInventory.objects.filter(
                        id__in=ids[:available_storage]
                    ).update(status="active")
                sync_usage(workspace, INVENTORY_USAGE_CATEGORY)

        elif "update-view-button-delete" in request.POST:
            view_id = request.POST.get("view_id", None)
            if view_id:
                try:
                    View.objects.get(id=view_id).delete()
                except:
                    pass
            if module_slug:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        elif "csv_download" in request.POST:
            if lang == "ja":
                history_name = "在庫のエクスポート"
            else:
                history_name = "Export Inventory"

            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="export_inventory",
                name=history_name,
            )

            view_id = request.POST.get("view_id", None)
            inventory_columns = request.POST.get("column", None)
            inventory_ids = request.POST.getlist("item_ids")
            record_ids = request.POST.getlist("record_ids")
            encoded_format = request.POST.get("encoded_format", "utf-8")

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }

            if record_ids:
                filter_dictionary["id"] = {
                    "key": "include",
                    "value": ",".join(record_ids),
                }

            filter_dictionary = json.dumps(filter_dictionary)
            payload = ExportCSVInventoryPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                view_id=str(view_id),
                history_id=str(history.id),
                inventory_columns=inventory_columns,
                inventory_ids=inventory_ids,
                record_ids=record_ids,
                filter_dictionary=filter_dictionary,
                encoded_format=encoded_format,
                language=lang,
            )

            job_id = create_bg_job(
                workspace=workspace,
                user=request.user,
                function_name="export_csv_inventory",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id

            # Log export job parameters for debugging
            logger.info(
                f"EXPORT_JOB: Starting inventory export for user {request.user.email} in workspace {workspace.id}"
            )
            logger.info(
                f"EXPORT_JOB: Export parameters - function: export_csv_inventory, job_id: {payload.background_job_id}"
            )
            logger.info(
                f"EXPORT_JOB: View ID: {view_id}, History ID: {history.id}, Language: {lang}"
            )
            logger.info(f"EXPORT_JOB: Inventory IDs: {inventory_ids}")
            logger.info(f"EXPORT_JOB: Record IDs: {record_ids}")
            logger.info(f"EXPORT_JOB: Columns: {inventory_columns}")
            logger.info(f"EXPORT_JOB: Filter dictionary: {filter_dictionary}")

            try:
                ref = export_csv_inventory.run_no_wait(input=payload)
            except Exception as e:
                logger.error(
                    f"EXPORT_JOB: Exception occurred during export_csv_inventory: {str(e)}",
                    exc_info=True,
                )
                ref = None

            is_running = None
            if ref:
                logger.info(
                    f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}"
                )
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit background job for user {request.user.email}"
                )
                is_running = False

            if is_running:
                logger.info(
                    f"EXPORT_JOB: Successfully submitted inventory export job for user {request.user.email}"
                )
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, csv will be sent to your email.",
                        type="success",
                    )
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit inventory export job for user {request.user.email} in workspace {workspace.id}"
                )
                logger.error(
                    f"EXPORT_JOB: Parameters used: {payload.background_job_id}"
                )
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブの送信中にエラーが発生しました。サポートに連絡してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="There is an error while submitting the export job. Please contact support.",
                        type="error",
                    )
                history.delete()

        elif "push_inventories" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            # logger.debug("=== shopturbo.py -- 320 Push item for select_integration_ids", select_integration_ids, len(select_integration_ids), type(select_integration_ids))
            inventory_ids = request.POST.getlist("inventory_ids", [])
            if len(select_integration_ids) == 0:
                return HttpResponse(status=200)
            channels = Channel.objects.filter(id__in=select_integration_ids)

            filter_id = request.POST.get("filter", None)
            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            logger.debug(
                "=== shopturbo.py -- 322 Sync item for filter_items", filter_items
            )

            if inventory_ids:
                inventories = ShopTurboInventory.objects.filter(id__in=inventory_ids)
            else:
                inventories = ShopTurboInventory.objects.filter(
                    workspace=workspace, status="active"
                )

            if filter_items:
                inventories = inventories.filter(item__in=filter_items)

            for channel in channels:
                if channel.integration.slug == "shopify":
                    for inventory in inventories:
                        push_shopify_inventories(channel.id, inventory)
                elif channel.integration.slug == "yahoo-shopping":
                    push_yahoo_shopping_inventories(channel.id, inventories)

        if module_slug:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def create_inventory_transaction(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                logger.debug("Action node does not exist")
                return HttpResponse(status=404)

        selected_inventory = None
        transaction_type = None
        move_destination = None
        use_prev_data = False
        subtract_from_components = False
        component_layer = "all"
        quantity = None
        if node and node.input_data:
            if "use_prev_data" in node.input_data:
                use_prev_data = node.input_data["use_prev_data"]
            if "subtract_from_components" in node.input_data:
                subtract_from_components = node.input_data["subtract_from_components"]
            if "component_layer" in node.input_data:
                component_layer = node.input_data["component_layer"]
            if "transaction_type" in node.input_data:
                transaction_type = node.input_data["transaction_type"]
            if "move_destination" in node.input_data:
                move_destination = node.input_data["move_destination"]
            if "inventory" in node.input_data:
                selected_inventory = node.input_data["inventory"]
            if "quantity" in node.input_data:
                quantity = node.input_data["quantity"]

        inventory = ShopTurboInventory.objects.filter(
            workspace=workspace, status="active"
        )

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "inventory": inventory,
            "selected_inventory": selected_inventory,
            "subtract_from_components": subtract_from_components,
            "component_layer": component_layer,
            "transaction_type": transaction_type,
            "move_destination": move_destination,
            "use_prev_data": use_prev_data,
            "quantity": quantity,
        }
        return render(
            request,
            "data/shopturbo/inventory/create-inventory-transaction-action-form.html",
            context,
        )

    # POST
    logger.debug(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            logger.debug("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            logger.debug("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            logger.debug("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    use_prev_data = request.POST.get("use_prev_data" + postfix)
    subtract_from_components = request.POST.get("subtract_from_components" + postfix)
    transaction_type = request.POST.get("transaction_type" + postfix)
    move_destination = None
    if transaction_type == "move":
        move_destination = request.POST.get("move-destination" + postfix)
    selected_inventory = request.POST.get("inventory" + postfix)
    quantity = request.POST.get("quantity" + postfix)

    object_type = request.POST.get("object_type" + postfix)
    object_name = request.POST.get("object_name" + postfix)

    module_slug = request.POST.get("module")
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
    if not module_slug:
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = module.slug
    redirect_url = reverse(
        "load_object_page",
        host="app",
        kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
    )

    if submit_option == "save":
        node.valid_to_run = True
        input_data = {}
        if not use_prev_data:
            if selected_inventory:
                input_data["inventory"] = selected_inventory
            else:
                logger.debug("no inventory selected", selected_inventory)
                node.valid_to_run = False
            if quantity:
                input_data["quantity"] = int(quantity)
            else:
                logger.debug("not quantity data", quantity)
                node.valid_to_run = False
        else:
            input_data["use_prev_data"] = use_prev_data
        if subtract_from_components:
            input_data["subtract_from_components"] = subtract_from_components
            component_layer = request.POST.get("component_layer" + postfix)
            if component_layer not in ["all", "1", "2", "3"]:
                node.valid_to_run = False
                component_layer = "all"
            input_data["component_layer"] = component_layer
        if not transaction_type or transaction_type not in [
            "stock_in",
            "stock_out",
            "move",
        ]:
            logger.debug(
                "no transaction type selected or it is invalid", transaction_type
            )
            node.valid_to_run = False
            transaction_type = None
        else:
            input_data["transaction_type"] = transaction_type
            if transaction_type == "move" and not move_destination:
                logger.debug("no move destination selected", move_destination)
                node.valid_to_run = False
                input_data["move_destination"] = None
            else:
                input_data["move_destination"] = move_destination
        if object_type:
            input_data["object_type"] = object_type
        if object_name:
            input_data["object_name"] = object_name

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()
    else:
        if not node:
            return HttpResponse(status=400)
        order = None
        use_prev_data = None
        if "use_prev_data" in node.input_data:
            use_prev_data = node.input_data["use_prev_data"]
        subtract_from_components = None
        if "subtract_from_components" in node.input_data:
            subtract_from_components = node.input_data["subtract_from_components"]
        component_layer = "all"
        if subtract_from_components:
            if "component_layer" in node.input_data:
                component_layer = node.input_data["component_layer"]
        transaction_type = None
        if "transaction_type" in node.input_data:
            transaction_type = node.input_data["transaction_type"]
        move_destination = None
        if "move_destination" in node.input_data:
            move_destination = node.input_data["move_destination"]
        selected_inventory = None
        if "inventory" in node.input_data:
            selected_inventory = node.input_data["inventory"]
        quantity = None
        if "quantity" in node.input_data:
            quantity = node.input_data["quantity"]

        output_data = {"transactions": []}

        inventory_list = []
        if use_prev_data:
            inventory_transaction = None
            if (
                at.input_data
                and "order" in at.input_data
                and "order_id" in at.input_data["order"]
            ):
                order = ShopTurboOrders.objects.filter(
                    workspace=workspace,
                    status="active",
                    order_id=at.input_data["order"]["order_id"],
                ).first()
                if not order:
                    logger.debug("Order does not exist.")
                    return HttpResponse(status=400)
            elif at.input_data and "inventory_transaction_id" in at.input_data:
                inventory_transaction = InventoryTransaction.objects.filter(
                    workspace=workspace,
                    usage_status="active",
                    id=at.input_data["inventory_transaction_id"],
                ).first()

            if order:
                item_orders = order.shopturboitemsorders_set.all()
                quantity = 1
                for item_order in item_orders:
                    if subtract_from_components:
                        max_depth = 3
                        all_components = False
                        if component_layer == "all":
                            all_components = True
                        else:
                            try:
                                max_depth = int(component_layer)
                            except:
                                pass
                        for inventory_dict in get_components_inventory(
                            item=item_order.item,
                            max_depth=max_depth,
                            all_components=all_components,
                        ):
                            inventory_list.append(
                                {
                                    "inventory": inventory_dict["inventory"],
                                    "amount": inventory_dict["amount"]
                                    * item_order.number_item,
                                    "transaction_at": timezone.now(),
                                }
                            )
                    else:
                        inventory = (
                            ShopTurboInventory.objects.filter(
                                item=item_order.item, status="active"
                            )
                            .order_by("created_at")
                            .first()
                        )
                        if inventory:
                            inventory_list.append(
                                {
                                    "inventory": inventory,
                                    "amount": item_order.number_item,
                                    "transaction_at": timezone.now(),
                                }
                            )
            elif inventory_transaction and subtract_from_components:
                inventory = inventory_transaction.inventory
                item = inventory.item.first()
                max_depth = 3
                all_components = False
                if component_layer == "all":
                    all_components = True
                else:
                    try:
                        max_depth = int(component_layer)
                    except:
                        pass
                for inventory_dict in get_components_inventory(
                    item=item,
                    max_depth=max_depth,
                    all_components=all_components,
                ):
                    inventory_list.append(
                        {
                            "inventory": inventory_dict["inventory"],
                            "amount": inventory_transaction.amount
                            * inventory_dict["amount"],
                            "transaction_at": inventory_transaction.transaction_date,
                        }
                    )
        else:
            inventory = ShopTurboInventory.objects.get(
                id=selected_inventory, workspace=at.workspace
            )
            inventory_list.append(
                {
                    "inventory": inventory,
                    "amount": quantity,
                    "transaction_at": timezone.now(),
                }
            )

        for inventory_d in inventory_list:
            inventory = inventory_d["inventory"]
            amount = inventory_d["amount"]
            transaction_at = inventory_d["transaction_at"]

            if transaction_type == "move":
                to_inventory = ShopTurboInventory.objects.filter(
                    item=inventory.item.first(),
                    workspace=inventory.workspace,
                    inventory_status=move_destination,
                    status="active",
                ).first()
                if not to_inventory:
                    if not has_quota(workspace, INVENTORY_USAGE_CATEGORY):
                        at.status = "failed"
                        at.output_data = output_data
                        at.completed_at = timezone.now()
                        at.save()

                        wat.status = "failed"
                        wat.save()

                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                message="在庫割当が上限に達しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                message="Inventory quota has reached its limit",
                                type="error",
                            )
                        return redirect(redirect_url)

                    # Create new inventory
                    to_inventory = ShopTurboInventory.objects.create(
                        workspace=inventory.workspace,
                        inventory_status=move_destination,
                        unit_price=inventory.unit_price,
                        currency=inventory.currency,
                        status="active",
                        initial_value=amount,
                        total_inventory=amount,
                        date=timezone.now(),
                    )
                    to_inventory.item.add(inventory.item.first())

                if not has_quota(workspace, INVENTORY_TRANSACTION_USAGE_CATEGORY):
                    at.status = "failed"
                    at.output_data = output_data
                    at.completed_at = timezone.now()
                    at.save()

                    wat.status = "failed"
                    wat.save()
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            message="在庫取引の割り当てが上限に達しました",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            message="Inventory Transaction quota has reached its limit",
                            type="error",
                        )
                    return redirect(redirect_url)

                from_inventory_stock = InventoryTransaction.objects.create(
                    inventory=inventory,
                    transaction_type="out",
                    amount=amount,
                    workspace=workspace,
                    user=request.user,
                )
                to_inventory_stock = InventoryTransaction.objects.create(
                    inventory=to_inventory,
                    transaction_type="in",
                    transaction_amount=amount,
                    amount=amount,
                    workspace=workspace,
                    user=request.user,
                )

                if order:
                    order.inventory_transactions.add(from_inventory_stock)
                    order.inventory_transactions.add(to_inventory_stock)
                recalculate_transactions_after(inventory, from_inventory_stock, request)
                recalculate_transactions_after(
                    to_inventory, to_inventory_stock, request
                )
                inventory.refresh_from_db()
                to_inventory.refresh_from_db()
                update_inventory_stock_price(inventory, request)
                update_inventory_stock_price(to_inventory, request)
                inventory.refresh_from_db()
                to_inventory.refresh_from_db()
                re_calculate_inventory_stock(inventory)
                re_calculate_inventory_stock(to_inventory)
                inventory.refresh_from_db()
                to_inventory.refresh_from_db()
                output_data["transactions"].append(
                    {
                        "transaction_id": from_inventory_stock.transaction_id,
                        "transaction_type": from_inventory_stock.transaction_type,
                        "transaction_amount": from_inventory_stock.amount,
                    }
                )
                output_data["transactions"].append(
                    {
                        "transaction_id": to_inventory_stock.transaction_id,
                        "transaction_type": to_inventory_stock.transaction_type,
                        "transaction_amount": to_inventory_stock.amount,
                    }
                )
            else:
                transaction = InventoryTransaction.objects.create(
                    workspace=inventory.workspace,
                    inventory=inventory,
                    transaction_type="out" if transaction_type == "stock_out" else "in",
                    amount=amount,
                    transaction_date=transaction_at,
                    user=request.user,
                )
                if inventory.unit_price:
                    transaction.price = inventory.unit_price
                else:
                    latest_transaction = (
                        InventoryTransaction.objects.exclude(pk=transaction.id)
                        .exclude(usage_status="archived")
                        .filter(
                            inventory=inventory, transaction_date__lte=transaction_at
                        )
                        .order_by("-transaction_date", "-created_at")
                        .first()
                    )
                    if latest_transaction:
                        transaction.price = latest_transaction.average_price
                    else:
                        # Fallback to 0 if no previous transactions exist
                        transaction.price = 0
                if transaction.price and amount:
                    transaction.total_price = transaction.price * int(amount)
                transaction.save()
                if order:
                    order.inventory_transactions.add(transaction)
                recalculate_transactions_after(inventory, transaction, request)
                inventory.refresh_from_db()
                update_inventory_stock_price(inventory, request)
                inventory.refresh_from_db()
                re_calculate_inventory_stock(inventory)
                inventory.refresh_from_db()
                output_data["transactions"].append(
                    {
                        "transaction_id": transaction.transaction_id,
                        "transaction_type": transaction.transaction_type,
                        "transaction_amount": transaction.amount,
                    }
                )

        at.status = "success"
        at.output_data = output_data
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

        return redirect(redirect_url)


@login_or_hubspot_required
def item_purchase_prices(request):
    item_id = request.GET.get("item_id")
    is_new = request.GET.get("is_new")
    workspace = get_workspace(request.user)
    item_purchase_prices = []
    item = ShopTurboItems.objects.filter(
        id=item_id, workspace=workspace, status="active"
    ).first()
    item_purchase_prices = []
    if item:
        item_purchase_prices = ItemPurchasePrice.objects.filter(item=item).order_by(
            "-created_at"
        )

    new_id = None
    if is_new or not item_id or not item_purchase_prices:
        new_id = uuid.uuid4()
    context = {
        "item_purchase_prices": item_purchase_prices,
        "item": item,
        "new_id": new_id,
    }
    return render(request, "data/shopturbo/items/item-purchase-prices.html", context)


@login_or_hubspot_required
def inventory_bulk_action_drawer(request):
    workspace = get_workspace(request.user)
    section = request.GET.get("section", None)
    if section == "action_history":
        section = request.GET.get("section", None)
        action_tab = request.GET.get("action_tab", None)

        context = {
            "inventory_ids": request.GET.getlist("inventory_ids", []),
            "action_tab": action_tab,
            "object_type": TYPE_OBJECT_INVENTORY,
            "drawer_url": "inventory_bulk_action_drawer",
            "open_drawer": request.GET.get("open_drawer"),
        }
        return render(
            request,
            "data/shopturbo/manage-sync-actions-settings-inventory.html",
            context,
        )

    inventory_ids = request.GET.get("inventory_ids")
    try:
        inventory_ids = json.loads(inventory_ids)
    except:
        try:
            inventory_ids = ast.literal_eval(inventory_ids)
        except:
            inventory_ids = []
    inventory_id_list = ShopTurboInventory.objects.filter(
        id__in=inventory_ids, workspace=workspace
    ).values_list("id", flat=True)
    context = {
        "object_action": {
            "additional_params": {
                "inventory_ids": ",".join([str(id_) for id_ in inventory_id_list])
            },
        },
        "object_type": TYPE_OBJECT_INVENTORY,
    }
    return render(request, "data/shopturbo/shopturbo-manage-action.html", context)


@login_or_hubspot_required
def bulk_inventory_transaction(request):
    workspace = get_workspace(request.user)
    if request.method == "GET":
        inventory_ids = request.GET.get("inventory_ids")
        try:
            if inventory_ids:
                inventory_ids = inventory_ids.split(",")
        except Exception as e:
            logger.debug(e)
            return HttpResponse(status=400)
        inventory = []
        if inventory_ids:
            inventory = ShopTurboInventory.objects.filter(
                id__in=inventory_ids, workspace=workspace, status="active"
            )
        context = {
            "inventory_list": inventory,
            "now": timezone.now(),
            "action_id": request.GET.get("action_id"),
        }
        return render(
            request,
            "data/shopturbo/inventory/bulk-inventory-transaction-action.html",
            context,
        )

    module_slug = request.POST.get("module")
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY]
    if not module_slug:
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_INVENTORY
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = module.slug

    inventory_ids = request.POST.getlist("inventory_ids")
    if not inventory_ids:
        return HttpResponse(status=400)
    workspace = get_workspace(request.user)
    inventory = ShopTurboInventory.objects.filter(
        id__in=inventory_ids, workspace=workspace, status="active"
    )

    action_id = request.POST.get("action_id", None)
    action = Action.objects.filter(slug=action_id).first()
    history = ActionHistory.objects.create(
        workspace=workspace,
        action=action,
        status="initialized",
        created_by=request.user,
    )
    input_data = dict(request.POST)
    input_data = {
        key: value[0] if len(value) == 1 else value for key, value in input_data.items()
    }
    lang = request.LANGUAGE_CODE
    transactions = []
    for inventory_ in inventory:
        history.status = "running"
        history.save()
        tx_param_ids = request.POST.getlist(f"transaction-id-{inventory_.id}")
        if not tx_param_ids:
            logger.debug("no transaction id for inventory:", inventory_.id)
            continue

        for tx_param_id in tx_param_ids:
            transfer_history = ActionTaskHistory.objects.create(
                workspace=history.workspace,
                action_history=history,
                status="initialized",
                input_data=input_data,
            )
            transaction_type_ = request.POST.get(
                f"transaction_type_{inventory_.id}-{tx_param_id}"
            )
            transaction_time_ = request.POST.get(
                f"transaction_time_{inventory_.id}-{tx_param_id}"
            )
            try:
                amount_ = int(request.POST.get(f"amount_{inventory_.id}-{tx_param_id}"))
            except Exception as e:
                logger.debug(e)
                continue
            if (
                not amount_
                and transaction_type_ not in ["in", "out"]
                and not transaction_time_
            ):
                logger.debug("skip input is invalid")
                continue

            transaction_date = timezone.now()
            transaction = InventoryTransaction.objects.create(
                workspace=workspace,
                inventory=inventory_,
                transaction_type=transaction_type_,
                amount=amount_,
                transaction_date=transaction_date,
                user=request.user,
            )
            latest_transaction = (
                InventoryTransaction.objects.exclude(pk=transaction.id)
                .exclude(usage_status="archived")
                .filter(inventory=inventory_, transaction_date__lte=transaction_date)
                .order_by("-transaction_date", "-created_at")
                .first()
            )
            if latest_transaction:
                transaction.price = latest_transaction.average_price
            else:
                # Fallback to inventory unit price or 0 if no previous transactions exist
                transaction.price = getattr(inventory_, "unit_price", 0) or 0
            transaction.save()

            workflows = get_workflows_by_first_action_trigger(
                "record-created",
                workspace,
                TYPE_OBJECT_INVENTORY_TRANSACTION,
                additional_conditions={
                    "input_data__transaction_type": transaction.transaction_type
                },
            )
            for workflow in workflows:
                param = {
                    "function": "trigger_workflow_when_inventory_transaction_created",
                    "job_id": str(uuid.uuid4()),
                    "workspace_id": str(workspace.id),
                    "user_id": None,
                    "args": [
                        f"--inventory_transaction_id={transaction.id}",
                        f"--lang={lang}",
                        f"--workflow_id={workflow.id}",
                    ],
                }
                trigger_bg_job(param)
            recalculate_transactions_after(inventory_, transaction, request)
            inventory_.refresh_from_db()
            update_inventory_stock_price(inventory_, request)
            inventory_.refresh_from_db()
            re_calculate_inventory_stock(inventory_)
            # if transaction_type_ == 'out':
            #     inventory_.initial_value -= amount_
            #     inventory_.total_inventory -= amount_
            # else:
            #     inventory_.initial_value += amount_
            #     inventory_.total_inventory += amount_
            # inventory_.save()
            logger.debug(inventory_.initial_value)
            transactions.append(transaction.id)

            transaction_dict = model_to_dict(transaction)
            serialized_data = json.dumps(transaction_dict, default=custom_serializer)
            transfer_history.output_data = json.loads(serialized_data)
            transfer_history.completed_at = timezone.now()
            transfer_history.status = "success"
            transfer_history.save()

    history.status = "success"
    history.completed_at = timezone.now()
    history.save()
    if not transactions:
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                type="error",
                message="入出庫の一括作成: 入力が不正なためトランザクションは作成されませんでした",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                type="error",
                message="Bulk Create Inventory Transactions: no transaction was created due malformed input",
            )
        if module_slug:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + "?open_drawer=action_drawer_history"
            )
        return redirect(reverse("main", host="app"))
    else:
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                type="error",
                message="入出庫の一括作成が正常に実行されました。",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                type="error",
                message="Bulk Create Inventory Transactions was run successfully.",
            )
    if module_slug:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + "?open_drawer=action_drawer_history"
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
@require_GET
def get_inventory_item(request, inventory_id):
    source = request.GET.get("source")
    workspace = get_workspace(request.user)
    item = ShopTurboItems.objects.filter(
        workspace=workspace, shopturboinventory__id=inventory_id
    ).first()

    if not item:
        logger.debug("no item found")
        return HttpResponse(status=404)

    res_ = f"#{item.item_id:04d} {item.name}"
    context = {
        "item": item,
        "item_display": res_,
        "source": source,
    }

    try:
        om, created = ObjectManager.objects.get_or_create(
            workspace=workspace, page_group_type=TYPE_OBJECT_ITEM
        )
        if created:
            om.column_display = "item_id"
            om.save()
    except Exception as e:
        logger.debug(e)
        return render(request, "data/shopturbo/items/item-display-column.html", context)

    try:
        columns = om.column_display.split(",")
    except:
        return render(request, "data/shopturbo/items/item-display-column.html", context)

    try:
        res = get_object_display_based_columns(
            "commerce_items", item, columns, workspace.timezone, request.LANGUAGE_CODE
        )
    except Exception:
        DiscordNotification().send_message(
            f"Display item inventory based on column error: {traceback.format_exc()}"
        )
        return render(request, "data/shopturbo/items/item-display-column.html", context)

    context["item_display"] = res
    return render(request, "data/shopturbo/items/item-display-column.html", context)


@login_or_hubspot_required
def item_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    q = request.GET.get("q", "")
    page = request.GET.get("page", 1)

    purpose = request.GET.get("purpose", "")

    filter_conditions = Q(workspace=workspace, status="active")

    if "custom_property" in purpose:
        if "custom_property_text_list" in purpose:
            filter_conditions = Q(workspace=workspace, type="text")
        elif "custom_property_company_list" in purpose:
            filter_conditions = Q(workspace=workspace, type="company")
        elif "custom_property_number_list" in purpose:
            filter_conditions = Q(workspace=workspace, type="number")
        else:
            filter_conditions = Q(workspace=workspace)

        if q:
            filter_conditions &= Q(name__icontains=q)

        cf_name = ShopTurboItemsNameCustomField.objects.filter(
            filter_conditions
        ).order_by("-created_at")

        res = []
        ITEMS_PER_PAGE = 30
        cf_name_paginator = Paginator(cf_name, ITEMS_PER_PAGE)
        paginated_cf_name = []
        more_pagination = False
        if page:
            try:
                cf_name_page_content = cf_name_paginator.page(page if page else 1)
                paginated_cf_name = cf_name_page_content.object_list
                more_pagination = cf_name_page_content.has_next()
            except EmptyPage:
                pass

        for cf in paginated_cf_name:
            try:
                text = f"{cf.name}"
            except Exception:
                pass

            res.append(
                {
                    "id": str(cf.id),
                    "text": text,
                }
            )
    else:
        try:
            SHOPTURBO_APP_TARGET = "shopturbo"
            app_setting, _ = AppSetting.objects.get_or_create(
                workspace=workspace, app_target=SHOPTURBO_APP_TARGET
            )
            if not app_setting.search_setting_item:
                app_setting.search_setting_item = "name"
                app_setting.save()
        except Exception as e:
            logger.debug("[DEBUG] Error in getting app setting:", e)
            app_setting = None

        if (
            purpose == "purchase_order"
            and app_setting
            and app_setting.purchase_order_supplier_lock
            and app_setting.purchase_order_supplier_properties
        ):
            supplier_id = request.GET.get("supplier")
            if supplier_id:
                filter_conditions &= Q(
                    shopturbo_item_custom_field_relations__field_name__type__in=[
                        "contact",
                        "company",
                    ],
                    shopturbo_item_custom_field_relations__field_name__id=app_setting.purchase_order_supplier_properties,
                    shopturbo_item_custom_field_relations__value=supplier_id,
                )
                logger.debug(filter_conditions)

        get_item_choosen = request.GET.getlist("get_item_choosen", [])
        exclude_current_item = request.GET.get("exclude_current_item", "")

        if q:
            match_special_char = re.search(r"#(\d+)", q)
            if match_special_char:
                number = match_special_char.group(1)
                if number:
                    from utils.utility import get_item_id_search_filter

                    filter_conditions &= get_item_id_search_filter(number)
            else:
                search_ = q.split()
                for search_key in search_:
                    from utils.utility import get_item_id_search_filter

                    filter_conditions &= apply_item_search_setting(
                        app_setting, search_key.lower()
                    ) | get_item_id_search_filter(search_key.lower())

        # Exclude current item to prevent self-reference in components
        if exclude_current_item:
            filter_conditions &= ~Q(id=exclude_current_item)

        if get_item_choosen:
            if get_item_choosen != [""]:
                filter_conditions &= ~Q(id__in=get_item_choosen)

        try:
            shopturbo_items = (
                ShopTurboItems.objects.filter(filter_conditions)
                .order_by("-item_id")
                .distinct()
            )
        except Exception as e:
            logger.debug("[DEBUG] Error in getting items:", e)
            # There is a case when distinct() is not working properly - just to prevent the error
            shopturbo_items = ShopTurboItems.objects.filter(filter_conditions).order_by(
                "-item_id"
            )

        res = []
        ITEMS_PER_PAGE = 30
        items_paginator = Paginator(shopturbo_items, ITEMS_PER_PAGE)
        paginated_items = []
        more_pagination = False
        if page:
            try:
                item_page_content = items_paginator.page(page if page else 1)
                paginated_items = item_page_content.object_list
                more_pagination = item_page_content.has_next()
            except EmptyPage:
                pass

        # Get or create ObjectManager once outside the loop for efficiency
        om, created = ObjectManager.objects.get_or_create(
            workspace=workspace, page_group_type=TYPE_OBJECT_ITEM
        )

        # Get or create ObjectManager once outside the loop for efficiency

        if created or not om.column_display:
            om.column_display = "item_id"
            om.save()

        try:
            columns = om.column_display.split(",")
        except Exception:
            columns = ["item_id"]

        for item in paginated_items:
            text = ""
            try:
                text = get_object_display_based_columns(
                    TYPE_OBJECT_ITEM, item, columns, workspace.timezone, lang
                )
            except Exception:
                # Fallback to just item name
                text = item.name

            if purpose == TYPE_OBJECT_PURCHASE_ORDER:
                # Purchase order item options have different value format
                count_item = len(get_item_choosen) + 1
                res.append(
                    {
                        "id": f"{item.name}|{item.purchase_price}|{item.id}|{count_item}"
                        if item.purchase_price
                        else f"{item.name}|{item.price}|{item.id}|{count_item}",
                        "text": text,
                    }
                )
            elif purpose == TYPE_OBJECT_BILL:
                div_total = request.GET.get("div_total", 0)
                res.append(
                    {
                        "id": f"{item.id}|{item.price}|{div_total}",
                        "text": text,
                    }
                )
            else:
                res.append(
                    {
                        "id": str(item.id),
                        "text": text,
                    }
                )

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


@login_or_hubspot_required
def inventory_items_options(request):
    """
    Retrieve and paginate inventory's items for a given workspace.

    This function fetches ShopTurboItems that have associated inventory,
    paginates them, and prepares them for display in a select2 dropdown.

    Returns:
        JsonResponse: A JSON response containing paginated inventory items and pagination information.
    """
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    page = request.GET.get("page", 1)
    q_search = request.GET.get("q")

    inventory_items = (
        ShopTurboItems.objects.filter(
            workspace=workspace, shopturboinventory__isnull=False
        )
        .distinct()
        .order_by("item_id")
    )
    if q_search:
        filter_conditions = None
        match_special_char = re.search(r"#(\d+)", q_search)
        if match_special_char:
            number = match_special_char.group(1)
            if number:
                from utils.utility import get_item_id_search_filter

                filter_conditions = get_item_id_search_filter(number)
        else:
            try:
                SHOPTURBO_APP_TARGET = "shopturbo"
                app_setting, _ = AppSetting.objects.get_or_create(
                    workspace=workspace, app_target=SHOPTURBO_APP_TARGET
                )
                if not app_setting.search_setting_item:
                    app_setting.search_setting_item = "name"
                    app_setting.save()
            except Exception as e:
                logger.debug("[DEBUG] Error in getting app setting:", e)
                app_setting = None
            if app_setting:
                search_ = q_search.split()
                filter_conditions = Q()
                for search_key in search_:
                    from utils.utility import get_item_id_search_filter

                    filter_conditions &= apply_item_search_setting(
                        app_setting, search_key.lower()
                    ) | get_item_id_search_filter(search_key.lower())
        if filter_conditions:
            inventory_items = inventory_items.filter(filter_conditions)

    res = []
    ITEMS_PER_PAGE = 30
    items_paginator = Paginator(inventory_items, ITEMS_PER_PAGE)
    paginated_items = []
    more_pagination = False
    try:
        item_page_content = items_paginator.page(page if page else 1)
        paginated_items = item_page_content.object_list
        more_pagination = item_page_content.has_next()
    except EmptyPage:
        pass

    for item in paginated_items:
        om, created = ObjectManager.objects.get_or_create(
            workspace=workspace, page_group_type=TYPE_OBJECT_ITEM
        )
        if created:
            om.column_display = "item_id"
            om.save()
        text = ""
        try:
            columns = om.column_display.split(",")
            text = get_object_display_based_columns(
                TYPE_OBJECT_ITEM, item, columns, workspace.timezone, lang
            )
        except:
            pass
        res.append(
            {
                "id": str(item.id),
                "text": text,
            }
        )
    context = {"results": res, "pagination": {"more": more_pagination}}
    logger.debug(res)
    return JsonResponse(context)


@login_or_hubspot_required
def item_purchase_price(request, id):
    workspace = get_workspace(request.user)
    try:
        item = ShopTurboItems.objects.get(workspace=workspace, id=id, status="active")
    except ShopTurboItems.DoesNotExist:
        return HttpResponse(status=400)

    if item.purchase_price:
        return JsonResponse({"item_purchase_price": item.purchase_price})
    return JsonResponse({"item_purchase_price": 0})


@login_or_hubspot_required
def inventory_options(request):
    workspace = get_workspace(request.user)
    q = request.GET.get("q")
    page = request.GET.get("page", 1)
    # comma separated item ids to limit the inventory options
    items = request.GET.get("items", "")
    # comma separated item ids to exclude from the inventory options
    exclude_items = request.GET.get("exclude_items", "")
    # comma separated inventory ids to exclude from the inventory options
    exclude_ids = request.GET.get("exclude_ids", "")

    filter_conditions = Q(workspace=workspace, status="active")
    if items:
        items = items.split(",")
        items = [item for item in items if item.strip()]
        if items:
            # Check if the UUID is valid
            valid_items = [uuid.UUID(item) for item in items if is_valid_uuid(item)]
            if valid_items:
                filter_conditions &= Q(item__id__in=valid_items)

    if exclude_items:
        exclude_items = exclude_items.split(",")
        # Filter out empty strings
        exclude_items = [item for item in exclude_items if item.strip()]
        if exclude_items:
            # Check if the UUID is valid
            valid_exclude_items = [
                uuid.UUID(item) for item in exclude_items if is_valid_uuid(item)
            ]
            if valid_exclude_items:
                filter_conditions &= ~Q(item__id__in=valid_exclude_items)

    if exclude_ids:
        exclude_ids = exclude_ids.split(",")
        # Filter out empty strings
        exclude_ids = [id for id in exclude_ids if id.strip()]
        if exclude_ids:
            # Check if the UUID is valid
            valid_exclude_ids = [
                uuid.UUID(id_val) for id_val in exclude_ids if is_valid_uuid(id_val)
            ]
            if valid_exclude_ids:
                filter_conditions &= ~Q(id__in=valid_exclude_ids)

    if q:
        q_id = q

        try:
            if q[0] == "#" or q[0] == "0":
                q_id = int(q[1:].replace(" ", ""))
        except Exception as e:
            logger.debug(e)

        filter_conditions &= Q(inventory_id__icontains=q_id) | Q(item__name__icontains=q_id)

    inventories = ShopTurboInventory.objects.filter(filter_conditions).order_by(
        "-inventory_id"
    )

    res = []
    ITEMS_PER_PAGE = 30
    inventory_paginator = Paginator(inventories, ITEMS_PER_PAGE)
    paginated_inventory = []
    more_pagination = False
    if page:
        try:
            inventory_page_content = inventory_paginator.page(page if page else 1)
            paginated_inventory = inventory_page_content.object_list
            more_pagination = inventory_page_content.has_next()
        except EmptyPage:
            pass

    for inventory in paginated_inventory:
        try:
            om, created = ObjectManager.objects.get_or_create(
                workspace=workspace, page_group_type=TYPE_OBJECT_INVENTORY
            )
            if created:
                om.column_display = "inventory_id"
                om.save()
            columns = om.column_display.split(",")
            text = get_object_display_based_columns(
                TYPE_OBJECT_INVENTORY,
                inventory,
                columns,
                workspace.timezone,
                request.LANGUAGE_CODE,
            )
        except Exception:
            text = f"#{inventory.inventory_id:04d}"

        res.append(
            {
                "id": str(inventory.id),
                "text": text,
            }
        )

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


@login_or_hubspot_required
def inventory_components(request):
    workspace = get_workspace(request.user)
    property_id = request.GET.get("property_id")
    inventory_id = request.GET.get("inventory_id")
    if not property_id:
        return HttpResponse(status=400)
    add_item = request.GET.get("add_item", False)
    # comma separated inventory ids to exclude from the inventory options
    exclude_ids = request.GET.get("exclude_ids", "")
    components = []
    if property_id and inventory_id:
        custom_prop_value = ShopTurboInventoryValueCustomField.objects.filter(
            field_name__id=property_id,
            inventory__id=inventory_id,
            field_name__type="components",
        ).first()
        if custom_prop_value and custom_prop_value.value:
            component_ids = custom_prop_value.value.split(",")
            # Filter out empty strings
            component_ids = [id for id in component_ids if id.strip()]
            if component_ids:
                components = ShopTurboInventory.objects.filter(
                    id__in=component_ids, workspace=workspace, status="active"
                )
    context = {
        "components": components,
        "add_item": add_item,
        "exclude_ids": exclude_ids,
        "property_id": property_id,
    }
    return render(request, "data/property/inventory-component-selector.html", context)


# Workflow Action


@login_or_hubspot_required
def auto_inventory_email(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if not lang:
        if request.user.verification.language:
            lang = request.user.verification.language
        else:
            lang = "ja"

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                logger.debug("Action node does not exist")
                return HttpResponse(status=404)

        input_data = {}
        if node:
            input_data = node.input_data

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "input_data": input_data,
        }

        return render(
            request,
            "data/shopturbo/inventory/auto-inventory-email-action-form.html",
            context,
        )

    if request.method == "POST":
        logger.debug("[DEBUG] POST request:", request.POST)

        submit_option = request.POST.get("submit-option")
        action_index = request.POST.get("action_index")
        action_node_id = request.POST.get("action_node_id")
        action_slug = request.POST.get("action_slug")
        at_id = request.POST.get("action_tracker_id")
        wat_id = request.POST.get("workflow_action_tracker_id")

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                logger.debug("ActionNode does not exist")
                return HttpResponse(status=404)
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                logger.debug("ActionTracker does not exist")
                return HttpResponse(status=404)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                logger.debug("WorkflowActionTracker does not exist")
                return HttpResponse(status=404)

        postfix = ""
        if action_index:
            postfix = "-" + action_index

        email = request.POST.get("email" + postfix, None)
        item_stock_level_number_property = request.POST.get(
            "item_stock_level_number_property" + postfix, None
        )
        items_ideal_stock_cf = None
        action_name = request.POST.get("action_name" + postfix, None)

        if submit_option == "save":
            node.valid_to_run = True

            required_fields = [email, item_stock_level_number_property]
            if not all(required_fields):
                logger.debug("[DEBUG] missing required fields")
                node.valid_to_run = False

            # Email Validation
            if not re.match(r"[^@]+@[^@]+\.[^@]+", email):
                logger.debug("[DEBUG] invalid email")
                node.valid_to_run = False

            # CustomField Validation
            items_ideal_stock_cf = ShopTurboItemsNameCustomField.objects.filter(
                id=item_stock_level_number_property, workspace=workspace
            ).first()
            if not items_ideal_stock_cf:
                logger.debug("[DEBUG] invalid item_stock_level_number_property")
                node.valid_to_run = False

            input_data = {
                "email": email,
                "item_stock_level_number_property": item_stock_level_number_property,
                "action_name": action_name,
            }

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            return HttpResponse(status=200)

        else:
            if submit_option == "run":
                email = node.input_data.get("email", None)
                item_stock_level_number_property = node.input_data.get(
                    "item_stock_level_number_property", None
                )

                if not email or not item_stock_level_number_property:
                    logger.debug("[DEBUG] missing required fields")
                    return HttpResponse(status=400)

                items_ideal_stock_cf = ShopTurboItemsNameCustomField.objects.filter(
                    id=item_stock_level_number_property, workspace=workspace
                ).first()
                if not items_ideal_stock_cf:
                    logger.debug("[DEBUG] invalid item_stock_level_number_property")
                    return HttpResponse(status=400)
            else:
                required_fields = [email, item_stock_level_number_property]
                if not all(required_fields):
                    logger.debug("[DEBUG] missing required fields")
                    return HttpResponse(status=400)

                # Email Validation
                if not re.match(r"[^@]+@[^@]+\.[^@]+", email):
                    logger.debug("[DEBUG] invalid email")
                    return HttpResponse(status=400)

                items_ideal_stock_cf = ShopTurboItemsNameCustomField.objects.filter(
                    id=item_stock_level_number_property, workspace=workspace
                ).first()
                if not items_ideal_stock_cf:
                    logger.debug("[DEBUG] invalid item_stock_level_number_property")
                    return HttpResponse(status=400)

            if not items_ideal_stock_cf:
                if lang == "ja":
                    message = (
                        "商品の理想在庫数のカスタムフィールドが見つかりませんでした"
                    )
                else:
                    message = "Item Ideal Stock Custom Field not found"

                at.output_data = {"status": "error", "message": message}
                at.save()
                return_fn = get_redirect_workflow(
                    submit_option, node, workspace, at, wat, status="failed"
                )
                return redirect(return_fn)

            # Run Action
            if lang == "ja":
                dataframe_dict = {
                    "商品ID": [],
                    "商品名": [],
                    "在庫ID": [],
                    "適正在庫": [],
                    "現在在庫": [],
                    "不足分": [],
                }
            else:
                dataframe_dict = {
                    "Item ID": [],
                    "Item Name": [],
                    "Inventory ID": [],
                    "Ideal Stock Level": [],
                    "Current Inventory": [],
                    "Shortage": [],
                }

            inventories = ShopTurboInventory.objects.filter(
                workspace=workspace, status="active"
            )
            for inventory in inventories:
                items = inventory.item.all()
                for item in items:
                    item_cf_value = ShopTurboItemsValueCustomField.objects.filter(
                        items=item,
                        field_name=items_ideal_stock_cf,
                    ).first()

                    if item_cf_value:
                        try:
                            if item_cf_value.value_number:
                                ideal_stock_level = float(item_cf_value.value_number)
                            elif item_cf_value.value:
                                ideal_stock_level = float(item_cf_value.value)
                            else:
                                # Skip this item
                                continue
                        except:
                            # Skip This Item
                            continue
                    else:
                        continue

                    current_stock_level = inventory.total_inventory
                    # Need to restock
                    if current_stock_level <= ideal_stock_level:
                        if lang == "ja":
                            dataframe_dict["商品ID"].append(item.item_id)
                            dataframe_dict["商品名"].append(item.name)
                            dataframe_dict["在庫ID"].append(inventory.inventory_id)
                            dataframe_dict["適正在庫"].append(ideal_stock_level)
                            dataframe_dict["現在在庫"].append(current_stock_level)
                            dataframe_dict["不足分"].append(
                                ideal_stock_level - current_stock_level
                            )
                        else:
                            dataframe_dict["Item ID"].append(item.item_id)
                            dataframe_dict["Item Name"].append(item.name)
                            dataframe_dict["Inventory ID"].append(
                                inventory.inventory_id
                            )
                            dataframe_dict["Ideal Stock Level"].append(
                                ideal_stock_level
                            )
                            dataframe_dict["Current Inventory"].append(
                                current_stock_level
                            )
                            dataframe_dict["Shortage"].append(
                                ideal_stock_level - current_stock_level
                            )
                    else:
                        continue

            df = pd.DataFrame(dataframe_dict)
            csv_buffer = StringIO()

            csv_buffer = StringIO()
            df.to_csv(csv_buffer, index=False)
            csv_data = csv_buffer.getvalue()

            if lang == "ja":
                curr_datetime = timezone.now().strftime("%Y年%m月%d日_%H:%M:%S")
                filename = f"自動メール在庫_{curr_datetime}.csv"
                mail_subject = "Sanka - 自動在庫メール"
                message = render_to_string("data/email/auto-inventory-ja.html")
            else:
                curr_datetime = timezone.now().strftime("%Y-%m-%d_%H:%M:%S")
                filename = f"Auto_Email_Inventory_{curr_datetime}.csv"
                mail_subject = "Sanka - Auto Email Inventory"
                message = render_to_string("data/email/auto-inventory.html")

            from_email = "Sanka <<EMAIL>>"
            to_email = [email]
            email_message = EmailMessage(mail_subject, message, from_email, to_email)

            if lang == "ja":
                csv_data_encoded = csv_data.encode("shift-jis")
                email_message.attach(filename, csv_data_encoded, "text/csv")
            else:
                email_message.attach(filename, csv_data, "text/csv")

            email_message.send(fail_silently=False)

            at.output_data = {
                "status": "success",
                "message": "Inventory Email Report Sent Successfully",
            }
            at.save()

            return_fn = get_redirect_workflow(
                submit_option, node, workspace, at, wat, status="success"
            )
            return redirect(return_fn)


@login_or_hubspot_required
def autocomplete_inventory(request):
    workspace = get_workspace(request.user)
    search = request.GET.get("search", "")
    ids = request.GET.getlist("ids[]", "")

    # Filter out invalid IDs (empty strings, "None", etc.) and validate UUIDs
    valid_ids = []
    if ids:
        for id_value in ids:
            if id_value and id_value != "None" and is_valid_uuid(id_value):
                valid_ids.append(id_value)

    # Handle status filter for archived items
    status = request.GET.get("status")
    if status == "archived":
        filter_condition = Q(workspace=workspace, status="archived")
    else:
        # By default, show only active items
        filter_condition = Q(workspace=workspace, status="active")

    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_INVENTORY, user=request.user)
    filter_condition &= get_permission_filter(
        permission, request.user, permission_type="edit"
    )

    page = int(request.GET.get("page", 1))

    results_per_page = 10 if len(valid_ids) <= 0 else len(valid_ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    filter_conditions = Q()
    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target="shopturbo"
        ).first()

        if app_setting:
            from utils.utility import get_item_id_search_filter

            filter_conditions &= apply_item_search_setting(
                app_setting, search.lower()
            ) | get_item_id_search_filter(search.lower())

    if valid_ids:
        filter_condition &= Q(id__in=valid_ids)

    obj, created = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=TYPE_OBJECT_INVENTORY
    )
    if created:
        try:
            col_display = ",".join(DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_INVENTORY])
        except:
            col_display = "inventory_id"
        obj.column_display = col_display
        obj.save()
    else:
        col_display = obj.column_display if obj.column_display else "inventory_id"

    columns = col_display.split(",")
    results = ShopTurboInventory.objects.filter(filter_condition).order_by(
        "-created_at"
    )[start:end]
    data = [
        {
            "id": item.id,
            "text": get_object_display_based_columns(
                TYPE_OBJECT_INVENTORY,
                item,
                columns,
                workspace.timezone,
                request.LANGUAGE_CODE,
            ),
        }
        for item in results
    ]

    return JsonResponse({"results": data, "pagination": {"more": results.exists()}})


@login_or_hubspot_required
@require_GET
def purchase_price_currency_options(request):
    id_ = request.GET.get("id")
    if not id_:
        return HttpResponse(status=400)

    workspace = get_workspace(request.user)
    try:
        item = ShopTurboItems.objects.get(workspace=workspace, id=id_, status="active")
    except ShopTurboItems.DoesNotExist:
        return HttpResponse(status=404)

    try:
        currencies = (
            item.purchase_prices.all().values_list("currency", flat=True).distinct()
        )
        default_currency = item.purchase_prices.filter(default=True).first().currency
    except:
        currencies = [item.currency]
        default_currency = item.currency

    try:
        if len(currencies) == 1:
            if not currencies[0]:
                if workspace.currencies:
                    currencies = ast.literal_eval(workspace.currencies)
                else:
                    if request.LANGUAGE_CODE == "ja":
                        currencies = ["JPY"]
                    else:
                        currencies = ["USD"]

        if not default_currency:
            if workspace.currencies:
                default_currency = ast.literal_eval(workspace.currencies)[0]
            else:
                if request.LANGUAGE_CODE == "ja":
                    default_currency = "JPY"
                else:
                    default_currency = "USD"
    except:
        if request.LANGUAGE_CODE == "ja":
            currencies = ["JPY"]
            default_currency = "JPY"
        else:
            currencies = ["USD"]
            default_currency = "USD"

    context = {
        "currencies": currencies,
        "default_currency": default_currency,
    }
    return render(
        request, "data/shopturbo/inventory/unit-price-currency-selector.html", context
    )


@login_or_hubspot_required
def shopturbo_create_inventory(request):
    target = "commerce_inventory"
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    source_url = request.POST.get("source_url")
    module = None

    if request.method == "POST":
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_INVENTORY
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug
        if "single-entry" in request.POST:
            owner = request.POST.get("owner", None)
            item_ids = request.POST.getlist("item", None)
            inventory_status = request.POST.get("inventory_status", None)
            if inventory_status not in ["available", "committed", "unavailable"]:
                inventory_status = "available"
            initial_value = request.POST.get("initial_value", 0)
            logger.debug(initial_value)
            inventory_date_str_from_post = request.POST.get("inventory-date", None)

            if inventory_date_str_from_post:
                # Ensure it's a string before replace; POST values are usually strings or None.
                # The str() call handles cases where it might be some other type unexpectedly.
                temp_date_str = (
                    str(inventory_date_str_from_post)
                    .replace("年", "-")
                    .replace("月", "-")
                    .replace("日", "")
                )
                try:
                    # Parse the processed string
                    inventory_date = datetime.strptime(temp_date_str, "%Y-%m-%d %H:%M")

                    # Apply timezone if configured
                    if workspace.timezone:
                        # Assuming pytz is imported globally as 'pytz', as per original code's apparent expectation.
                        # If 'pytz' is not globally available and workspace.timezone is True,
                        # a NameError for 'pytz' would occur here (a pre-existing potential issue).
                        local_tz = pytz.timezone(workspace.timezone)
                        localized_time = local_tz.localize(inventory_date)
                        inventory_date = localized_time.astimezone(pytz.utc)
                    # If no workspace.timezone, inventory_date remains a naive datetime.
                except ValueError:
                    # Optionally log this error, e.g.:
                    # logger.debug(f"Warning: Malformed inventory-date '{inventory_date_str_from_post}'. Defaulting to current time.")
                    inventory_date = (
                        timezone.now()
                    )  # Default to current time if parsing fails
            else:
                # If 'inventory-date' was not provided in POST or was an empty string
                inventory_date = timezone.now()  # Default to current time

            currency = request.POST.get("currency", None)
            unit_price = request.POST.get("unit_price", 0)
            warehouse_id = request.POST.get("warehouse")
            if not has_quota(workspace, INVENTORY_USAGE_CATEGORY):
                msg = "Inventory could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing inventories to free up space."
                if lang == "ja":
                    msg = "制限を超えたため、在庫を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存の在庫の一部をアーカイブしてスペースを解放してください。"
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message=msg,
                    type="error",
                )
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))
            inventory = ShopTurboInventory.objects.create(
                workspace=workspace, status="active", date=inventory_date
            )

            # association
            association_label = AssociationLabel.objects.filter(
                workspace=workspace,
                object_source=TYPE_OBJECT_INVENTORY,
                label__iexact=TYPE_OBJECT_ITEM,
            ).first()
            if association_label:
                AssociationLabelObject.reset_associations_for_object(
                    inventory, workspace, association_label
                )
            for item_id in item_ids:
                shopturbo_item = ShopTurboItems.objects.get(id=item_id)

                inventory.item.add(shopturbo_item)
                if association_label:
                    AssociationLabelObject.create_association(
                        inventory, shopturbo_item, workspace, association_label
                    )

            inventory.inventory_status = inventory_status
            if initial_value is not None:
                inventory.initial_value = initial_value
                inventory.total_inventory = inventory.initial_value
            else:
                inventory.initial_value = 0
            if currency:
                inventory.currency = currency
            else:
                inventory.currency = inventory.item.first().currency
            if unit_price:
                inventory.unit_price = unit_price
            if warehouse_id:
                inventory.warehouse_id = warehouse_id

            assign_object_owner(inventory, owner, request, TYPE_OBJECT_INVENTORY)

            inventory.save(
                log_data={
                    "user": request.user,
                    "status": "create",
                    "workspace": workspace,
                }
            )
            trigger_workflow_by_inventory_less_than(inventory)

            save_custom_property(request, inventory)
            save_association_label(request, inventory, TYPE_OBJECT_INVENTORY)

            if request.POST.get("source") != TYPE_OBJECT_INVENTORY_TRANSACTION:
                inventory_transaction = create_inventory_transaction_helper(
                    inventory, request.user, lang=lang
                )
                if inventory_transaction:
                    # association
                    association_label = AssociationLabel.objects.filter(
                        workspace=workspace,
                        object_source=TYPE_OBJECT_INVENTORY,
                        label__iexact=TYPE_OBJECT_INVENTORY_TRANSACTION,
                    ).first()
                    if association_label:
                        AssociationLabelObject.reset_associations_for_object(
                            inventory, workspace, association_label
                        )
                        AssociationLabelObject.create_association(
                            inventory,
                            inventory_transaction,
                            workspace,
                            association_label,
                        )

            # association related
            if request.POST.get("type_association", "") == "create-association":
                source = request.POST.get("source")
                module_object_slug = OBJECT_TYPE_TO_SLUG[source]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=source
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug

                if source == TYPE_OBJECT_INVENTORY_WAREHOUSE:
                    object_id = request.POST.get("source_object_id")
                    warehouse = InventoryWarehouse.objects.filter(id=object_id).first()
                    if warehouse:
                        inventory.warehouse = warehouse
                        inventory.save()

                if source == TYPE_OBJECT_ITEM:
                    object_id = request.POST.get("source_object_id")
                    item = ShopTurboItems.objects.filter(id=object_id).first()
                    if item:
                        inventory.item.add(item)
                        inventory.save()

                if source == TYPE_OBJECT_INVENTORY_TRANSACTION:
                    object_id = request.POST.get("source_object_id")
                    transaction = InventoryTransaction.objects.filter(
                        id=object_id
                    ).first()
                    if transaction:
                        items = inventory.item.all()
                        price = inventory.unit_price
                        average_price = price
                        if len(items) > 0 and not price:
                            item = items[0]
                            item_price = ShopTurboItemsPrice.objects.filter(
                                item=item, default=True
                            ).first()
                            if item_price:
                                price = item_price.price
                                average_price = item_price.price

                        transaction.amount = int(inventory.initial_value)
                        transaction.price = price
                        transaction.average_price = average_price
                        transaction.transaction_amount = int(inventory.initial_value)
                        transaction.transaction_date = inventory.date
                        transaction.inventory = inventory

                        transaction.save()

                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?target={source}&transaction_id={object_id}"
                    )

            update_inventory_stock_price(inventory, request)
            inventory.refresh_from_db()

            if source_url:
                source_url = update_query_params_url(
                    source_url, {"target": target, "id": [str(inventory.id)]}
                )
                return redirect(source_url)
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?inventory_id={inventory.id}"
                )
            return redirect(reverse("main", host="app"))

        elif "submit_csv_upload" in request.POST:
            csv = request.FILES.get("csv_upload", False)
            if not csv:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Failed to retreive the CSV file.",
                    type="error",
                )
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))

            file_columns = request.POST.getlist("inventory-file-column")
            sanka_properties = request.POST.getlist("inventory-sanka-properties")
            ignores = request.POST.getlist("inventory-ignore")

            import_method_inventory = request.POST.get("import-method-inventory")
            # inventory_key_field = request.POST.getlist('inventory-key-field')
            try:
                df = read_csv(csv)

                inventory_fields = [
                    f.name for f in ShopTurboInventory._meta.get_fields()
                ] + ["item_id"]
                df = df.astype(str)
                for _, row in df.iterrows():
                    create_json_inventory = {}
                    create_json_inventory_custom_field = {}
                    for idx, file_column in enumerate(file_columns):
                        # check ignore
                        if ignores[idx] == "True":
                            continue

                        if is_valid_uuid(sanka_properties[idx]):
                            custom_field = (
                                ShopTurboInventoryNameCustomField.objects.filter(
                                    id=sanka_properties[idx], workspace=workspace
                                ).first()
                            )
                            create_json_inventory_custom_field[custom_field.name] = row[
                                file_column
                            ]
                        else:
                            obj = row[file_column]
                            row[file_column] = obj
                            if sanka_properties[idx] == "id_iw":
                                try:
                                    warehouse_id = row[file_column]
                                    if warehouse_id == "nan":
                                        continue
                                    warehouse = InventoryWarehouse.objects.filter(
                                        workspace=workspace, id_iw=int(warehouse_id)
                                    ).first()
                                    if warehouse:
                                        create_json_inventory["warehouse_id"] = (
                                            warehouse.id
                                        )
                                    else:
                                        logger.debug(
                                            "cannot find warehouse", warehouse_id
                                        )
                                except Exception as e:
                                    logger.debug(
                                        f"... ERROR === shopturbo.py -- 6223: {e}"
                                    )
                            else:
                                if sanka_properties[idx] not in inventory_fields:
                                    continue
                                if sanka_properties[idx] in [
                                    "available",
                                    "unavailable",
                                    "committed",
                                ]:
                                    # Use pandas to check for NaN
                                    if (
                                        pd.notna(row[file_column])
                                        and row[file_column] != "nan"
                                    ):
                                        create_json_inventory[sanka_properties[idx]] = (
                                            int(row[file_column])
                                        )
                                    else:
                                        # or handle as needed
                                        create_json_inventory[sanka_properties[idx]] = 0
                                else:
                                    create_json_inventory[sanka_properties[idx]] = row[
                                        file_column
                                    ]
                    logger.debug(f"{create_json_inventory=}")

                    if import_method_inventory == "create":
                        if "inventory_id" in create_json_inventory:
                            create_json_inventory.pop("inventory_id")

                        item_id = None
                        if "item_id" in create_json_inventory:
                            item_id = int(create_json_inventory.pop("item_id"))

                        if not has_quota(workspace, INVENTORY_USAGE_CATEGORY):
                            msg = "One or more inventories could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing inventories to free up space."
                            if lang == "ja":
                                msg = "制限を超えたため、1 つ以上のインベントリを作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のインベントリの一部をアーカイブしてスペースを解放してください。"
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=msg,
                                type="error",
                            )
                            break

                        inventory = ShopTurboInventory.objects.create(
                            **create_json_inventory,
                            status="active",
                            workspace=workspace,
                            date=timezone.now(),
                        )
                        if item_id:
                            items = ShopTurboItems.objects.filter(
                                item_id=item_id, workspace=workspace, status="active"
                            )
                            if items:
                                for item in items:
                                    inventory.item.add(item)

                        for create_json_inventory_key in create_json_inventory:
                            setattr(
                                inventory,
                                create_json_inventory_key,
                                create_json_inventory[create_json_inventory_key],
                            )

                        inventory.save(
                            log_data={
                                "user": request.user,
                                "status": "create",
                                "target": "commerce_inventory",
                                "workspace": workspace,
                            }
                        )

                        # Write Custom Field
                        for (
                            create_json_inventory_cs_field
                        ) in create_json_inventory_custom_field:
                            inventory_name_custom_field = (
                                ShopTurboInventoryNameCustomField.objects.get(
                                    name=create_json_inventory_cs_field,
                                    workspace=workspace,
                                )
                            )
                            CustomFieldValue, _ = (
                                ShopTurboInventoryValueCustomField.objects.get_or_create(
                                    field_name=inventory_name_custom_field,
                                    inventory=inventory,
                                )
                            )
                            CustomFieldValue.value = create_json_inventory_custom_field[
                                create_json_inventory_cs_field
                            ]
                            CustomFieldValue.save()

                        create_inventory_transaction_helper(
                            inventory, request.user, lang=lang
                        )

                    elif import_method_inventory == "update":
                        if "inventory_id" not in create_json_inventory:
                            continue

                        inventory_id = create_json_inventory.pop("inventory_id")

                        item_id = None
                        if "item_id" in create_json_inventory:
                            item_id = int(create_json_inventory.pop("item_id"))

                        # if Data is not exist, Skip
                        inventory = ShopTurboInventory.objects.filter(
                            inventory_id=inventory_id,
                            workspace=workspace,
                            status="active",
                        ).first()
                        logger.debug("update inventory", inventory)
                        if not inventory:
                            if not has_quota(workspace, INVENTORY_USAGE_CATEGORY):
                                msg = "One or more inventory could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing inventories to free up space."
                                if lang == "ja":
                                    msg = "制限を超えたため、1 つ以上の在庫を作成できませんでした。サブスクリプションプランをアップグレードして割り当てを増やすか、既存のインベントリの一部をアーカイブしてスペースを解放してください。"
                                Notification.objects.create(
                                    workspace=get_workspace(request.user),
                                    user=request.user,
                                    message=msg,
                                    type="error",
                                )
                                break
                            inventory = ShopTurboInventory.objects.create(
                                **create_json_inventory,
                                workspace=workspace,
                                status="active",
                                date=timezone.now(),
                            )

                            create_inventory_transaction_helper(
                                inventory, request.user, lang=lang
                            )

                        if item_id:
                            items = ShopTurboItems.objects.filter(
                                item_id=item_id, workspace=workspace
                            )
                            if items:
                                inventory.item.clear()
                                for item in items:
                                    inventory.item.add(item)

                        # Write Custom Field
                        for (
                            create_json_inventory_cs_field
                        ) in create_json_inventory_custom_field:
                            inventory_name_custom_field = (
                                ShopTurboInventoryNameCustomField.objects.get(
                                    name=create_json_inventory_cs_field,
                                    workspace=workspace,
                                )
                            )
                            CustomFieldValue, _ = (
                                ShopTurboInventoryValueCustomField.objects.get_or_create(
                                    field_name=inventory_name_custom_field,
                                    inventory=inventory,
                                )
                            )
                            CustomFieldValue.value = create_json_inventory_custom_field[
                                create_json_inventory_cs_field
                            ]
                            CustomFieldValue.save()

                        # Populate rest of the key
                        for create_json_inventory_key in create_json_inventory:
                            setattr(
                                inventory,
                                create_json_inventory_key,
                                create_json_inventory[create_json_inventory_key],
                            )

                        inventory.save(
                            log_data={
                                "user": request.user,
                                "target": "commerce_inventory",
                                "workspace": workspace,
                            }
                        )

            except Exception:
                traceback.logger.debug_exc()
                DiscordNotification().send_message(
                    f"[SHOPTURBO] [ERROR] - {traceback.format_exc()}",
                    mention_owner=True,
                )
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Wrong format uploaded, Please use this format",
                    message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                    cta_text="Template",
                    cta_text_ja="テンプレート",
                    cta_target=TEMPLATE_FILE["commerce_inventory"][lang],
                    type="error",
                )

    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def shopturbo_create_inventory_transaction_select_price(request):
    workspace = get_workspace(request.user)
    inventory_id = request.GET.get("inventory", None)
    transaction_id = request.GET.get("transaction", None)
    # Use empty string instead of None for Custom Price to ensure it passes the template condition check
    price_data = [{"price": ""}]
    if inventory_id:
        inventory = ShopTurboInventory.objects.get(pk=inventory_id)
        item = inventory.item.all()
        if not item:
            return JsonResponse({"html": ""})
        item = item[0]
        item_prices = item.purchase_prices.all().order_by("default", "-created_at")
        price_data = []
        for price in item_prices:
            data = {
                "type": "item_price",
                "name": "Item Purchase Price",
                "name_jp": "製品購入価格",
                "currency": price.currency,
                "price": price.price,
            }
            price_data.append(data)
        # Use empty string instead of None for Custom Price to ensure it passes the template condition check
        price_data.append({"price": ""})
        custom_fields = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace, type="purchase_item"
        )
        if len(custom_fields) > 0:
            for custom_field in custom_fields:
                purchase_items_custom_values = (
                    ShopTurboItemsValueCustomField.objects.filter(
                        items=item, field_name=custom_field
                    )
                )
                for purchase_item in purchase_items_custom_values:
                    try:
                        purchase_item = PurchaseItems.objects.get(
                            pk=purchase_item.value
                        )
                        data = {
                            "type": "purchase_item",
                            "name": "Purchase Item Price",
                            "name_jp": "仕入品価格",
                            "currency": purchase_item.currency,
                            "price": purchase_item.amount,
                        }
                        price_data.append(data)
                    except:
                        continue

    context = {"price_data": price_data}
    if transaction_id:
        transaction = InventoryTransaction.objects.get(pk=transaction_id)
        context["transaction"] = transaction
    rendered_template = render(
        request,
        "data/shopturbo/shopturbo-create-inventory-transaction-select-price.html",
        context,
    ).content.decode("utf-8")

    return JsonResponse({"html": rendered_template})


@login_or_hubspot_required
def inventory_transactions_options(request):
    workspace = get_workspace(request.user)
    q = request.GET.get("q")
    page = request.GET.get("page", 1)
    purpose = request.GET.get("purpose", "")

    # Handle status filter for archived items
    status = request.GET.get("status")
    if status == "archived":
        filter_conditions = Q(workspace=workspace, usage_status="archived")
    else:
        # By default, show only active items
        filter_conditions = Q(workspace=workspace, usage_status="active")

    if "custom_property" in purpose:
        if purpose == "custom_property_text_list":
            filter_conditions = Q(workspace=workspace, type="text")
        elif purpose == "custom_property_user_management_list":
            filter_conditions = Q(workspace=workspace, type="user_management")
        else:
            filter_conditions = Q(workspace=workspace)

        if q:
            filter_conditions &= Q(name__icontains=q)

        cf_name = InventoryTransactionNameCustomField.objects.filter(
            filter_conditions
        ).order_by("-created_at")

        res = []
        ITEMS_PER_PAGE = 30
        cf_name_paginator = Paginator(cf_name, ITEMS_PER_PAGE)
        paginated_cf_name = []
        more_pagination = False
        if page:
            try:
                cf_name_page_content = cf_name_paginator.page(page if page else 1)
                paginated_cf_name = cf_name_page_content.object_list
                more_pagination = cf_name_page_content.has_next()
            except EmptyPage:
                pass

        for cf in paginated_cf_name:
            try:
                text = f"{cf.name}"
            except Exception:
                pass

            res.append(
                {
                    "id": str(cf.id),
                    "text": text,
                }
            )
    else:
        if q:
            q_id = q
            try:
                if q[0] == "#" or q[0] == "0":
                    q_id = int(q[1:].replace(" ", ""))
            except Exception as e:
                logger.debug(e)
            # Only filter by transaction_id as 'name' field doesn't exist in InventoryTransaction model
            filter_conditions &= Q(transaction_id__icontains=q_id)

        inventory_transactions = InventoryTransaction.objects.filter(
            filter_conditions
        ).order_by("-transaction_id")

        res = []
        ITEMS_PER_PAGE = 30
        it_paginator = Paginator(inventory_transactions, ITEMS_PER_PAGE)
        paginated_it = []
        more_pagination = False
        if page:
            try:
                it_page_content = it_paginator.page(page if page else 1)
                paginated_it = it_page_content.object_list
                more_pagination = it_page_content.has_next()
            except EmptyPage:
                pass

        for it in paginated_it:
            try:
                om, created = ObjectManager.objects.get_or_create(
                    workspace=workspace,
                    page_group_type=TYPE_OBJECT_INVENTORY_TRANSACTION,
                )
                if created:
                    om.column_display = "transaction_id"
                    om.save()
                columns = om.column_display.split(",")
                text = get_object_display_based_columns(
                    TYPE_OBJECT_INVENTORY_TRANSACTION,
                    it,
                    columns,
                    workspace.timezone,
                    request.LANGUAGE_CODE,
                )
            except Exception:
                text = f"#{it.transaction_id:04d}"

            res.append(
                {
                    "id": str(it.id),
                    "text": text,
                }
            )

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


@login_or_hubspot_required
def generate_move_to_inventory(request):
    workspace = get_workspace(request.user)
    is_from = request.GET.get("is_from", None)
    if is_from:
        from_inventory = request.GET.get("from_inventory", None)
        from_quantity = request.GET.get("from_stock_change")
        if not from_inventory:
            return HttpResponse(status=400)

        from_inventory = ShopTurboInventory.objects.get(
            ~Q(status="archived"), pk=from_inventory, workspace=workspace
        )
        from_items = from_inventory.item.all()
        to_inventory = ShopTurboInventory.objects.filter(
            ~Q(status="archived") & ~Q(id=from_inventory.id),
            workspace=workspace,
            item__in=from_items,
        ).first()
        to_quantity = None
        if not from_quantity or from_quantity == "null":
            from_quantity = 0
        context = {
            "items": ",".join(
                [str(val) for val in from_items.values_list("id", flat=True)]
            ),
            "from_inventory": from_inventory,
            "to_inventory": to_inventory,
            "amount": from_quantity,
        }
        return render(
            request,
            "data/shopturbo/shopturbo-move-inventory-transactions.html",
            context,
        )
    else:
        to_inventory = request.GET.get("inventory", None)
        quantity = request.GET.get("quantity", None)

        to_inventory = ShopTurboInventory.objects.get(pk=to_inventory)
        if not quantity or quantity == "null":
            quantity = 0

        to_quantity = (
            to_inventory.total_inventory + int(quantity)
            if to_inventory.total_inventory
            else int(quantity)
        )
        return JsonResponse({"result": to_quantity})


def fill_forecast_data(forecast_data):
    # Determine the range of dates
    all_dates = sorted(forecast_data.keys())
    start_date = datetime.strptime(all_dates[0], "%Y-%m-%d").date()
    end_date = datetime.strptime(all_dates[-1], "%Y-%m-%d").date()

    delta = end_date - start_date
    full_date_range = [
        (start_date + timedelta(days=i)).strftime("%Y-%m-%d")
        for i in range(delta.days + 1)
    ]

    # logger.debug("full_date_range ==========\n", full_date_range)

    all_inventory_ids = set()
    for date, inventories in forecast_data.items():
        all_inventory_ids.update(inventories.keys())

    # Create a new dictionary to hold the filled data
    filled_forecast_data = {}
    previous_data = {
        inv_id: {
            "available": {"value": 0, "transaction_id": None},
            "committed": {"value": 0, "transaction_id": None},
            "unavailable": {"value": 0, "transaction_id": None},
        }
        for inv_id in all_inventory_ids
    }

    for date in full_date_range:
        filled_forecast_data[date] = {}
        if date in forecast_data:
            current_data = forecast_data[date]
        else:
            current_data = {}

        for inv_id in all_inventory_ids:
            if inv_id in current_data:
                filled_forecast_data[date][inv_id] = current_data[inv_id]
                previous_data[inv_id] = current_data[inv_id]
            else:
                filled_forecast_data[date][inv_id] = previous_data[inv_id]

    return filled_forecast_data


@login_or_hubspot_required
def shopturbo_inventory_warehouse(request):
    lang = request.LANGUAGE_CODE
    if lang == "ja":
        page_title = "ロケーション"
    else:
        page_title = "Locations"

    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse("start", host="app"))

    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=workspace, app_target=SHOPTURBO_APP_TARGET
    )

    location_ids = request.POST.getlist("checkbox")

    page_obj = get_page_object(TYPE_OBJECT_INVENTORY_WAREHOUSE, lang)
    base_model = page_obj["base_model"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    id_field = page_obj["id_field"]

    if request.method == "GET":
        config_view = None
        archive = None
        view_id = request.GET.get("view_id", None)

        permission = get_permission(
            object_type=TYPE_OBJECT_INVENTORY_WAREHOUSE, user=request.user
        )
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == "hide":
            context = {
                "permission": permission,
                "page_title": page_title,
            }
            return render(request, "data/locations/locations.html", context)

        if view_id:
            view = View.objects.filter(id=view_id).first()
        else:
            # Create Main View
            view, _ = View.objects.get_or_create(
                workspace=workspace,
                title="main",
                target=TYPE_OBJECT_INVENTORY_WAREHOUSE,
            )
            view_id = view.id

        views = View.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_INVENTORY_WAREHOUSE
        ).order_by("created_at")
        view_filter, _ = ViewFilter.objects.get_or_create(view=view, view_type="list")
        if not view_filter.column:
            columns = DEFAULT_COLUMNS_WAREHOUSE.copy()
            if "checkbox" in columns:
                columns.remove("checkbox")
            view_filter.column = columns
            view_filter.save()
        config_view = view_filter.view_type
        archive = view_filter.archive

        pagination_number = view_filter.pagination
        if not pagination_number:
            pagination_number = 20
        LOCATION_POSTS_PER_PAGE = pagination_number
        LOCATION_PER_PAGE_BEGIN = LOCATION_POSTS_PER_PAGE - 1

        filter_conditions = Q(workspace=workspace)

        filter_conditions &= get_permission_filter(permission, request.user)
        # Handle status filter for archived items
        status = request.GET.get("status")
        if status == "archived":
            filter_conditions &= Q(usage_status="archived")
        else:
            # By default, show only active items
            filter_conditions &= Q(usage_status="active")

        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace,
            object_type=TYPE_OBJECT_INVENTORY_WAREHOUSE,
            type="default",
        ).first()
        if not advance_search:
            advance_search = None

        if not app_setting.search_setting_warehouse:
            app_setting.search_setting_warehouse = "location,warehouse"
            app_setting.save()

        if advance_search:
            if advance_search.search_settings:
                app_setting.search_setting_warehouse = advance_search.search_settings
                app_setting.save()
        logger.debug("search settings:", app_setting.search_setting_warehouse)

        search_q = request.GET.get("q")
        if search_q:
            search_q = search_q.lower()
            search_filters = Q()
            search_filters |= Q(id_iw__icontains=search_q)
            if app_setting.search_setting_warehouse:
                search_fields = app_setting.search_setting_warehouse.split(",")
                for s_field in search_fields:
                    search_filters |= apply_search_setting(
                        "commerce_inventory_warehouse", view_filter, s_field, search_q
                    )
            filter_conditions &= search_filters

        advance_search_filter = None
        if advance_search:
            if advance_search.is_active:
                advance_search_filter = advance_search.search_filter
                advance_search_filter_status = advance_search.search_filter_status
                if advance_search_filter_status:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                        and advance_search_filter_status.get(k) != "False"
                    }
                else:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }
                try:
                    if (
                        advance_search_filter.get("usage_status", {}).get("value")
                        == "all"
                    ):
                        del advance_search_filter["usage_status"]
                except KeyError:
                    pass

                view_filter.filter_value = advance_search_filter

        filter_conditions = build_view_filter(
            filter_conditions,
            view_filter,
            TYPE_OBJECT_INVENTORY_WAREHOUSE,
            request=request,
        )
        logger.debug(
            f"... INFO === shopturbo.py -- 2885: Inventory filter - {filter_conditions}"
        )
        shopturbo_warehouses = InventoryWarehouse.objects.filter(
            filter_conditions
        ).order_by("-created_at")

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_INVENTORY_WAREHOUSE
        ).order_by("created_at")

        set_id = request.GET.get("set_id")
        if view_filter.view.form:
            set_id = view_filter.view.form.id
        else:
            if not set_id:
                property_set_default = property_sets.filter(as_default=True).first()
                if property_set_default:
                    set_id = property_set_default.id

        paginator = Paginator(shopturbo_warehouses, LOCATION_POSTS_PER_PAGE)
        page = request.GET.get("page", 1)
        if page:
            page_content = paginator.page(page)
            shopturbo_warehouses = page_content.object_list
            paginator_item_begin = (
                LOCATION_POSTS_PER_PAGE * int(page)
            ) - LOCATION_PER_PAGE_BEGIN
            paginator_item_end = LOCATION_POSTS_PER_PAGE * int(page)
        else:
            page_content = paginator.page(1)
            paginator_item_begin = LOCATION_POSTS_PER_PAGE * 1 - LOCATION_PER_PAGE_BEGIN
            paginator_item_end = LOCATION_POSTS_PER_PAGE * 1

        warehouse_id = request.GET.get("warehouse_id", "")

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list("id", flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ",".join(str(id) for id in members)

        context = {
            "page_title": page_title,
            "search_q": search_q,
            # NOTE: Use Object Type
            "app_slug": SHOPTURBO_APP_SLUG,
            "shopturbo_warehouses": shopturbo_warehouses,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "paginator": paginator,
            "config_view": config_view,
            "view_filter": view_filter,
            "view_id": view_id,
            "target": TYPE_OBJECT_INVENTORY_WAREHOUSE,
            "views": views,
            "current_view": view,
            "permission": permission,
            "warehouse_id": warehouse_id,
            "advance_search": advance_search,
            "set_id": set_id,
            "advance_search_filter": advance_search_filter,
            "group_members": members,
        }
        return render(request, "data/locations/locations.html", context)

    # Post Functions
    else:
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY_WAREHOUSE]
        module_slug = request.POST.get("module")
        if not module_slug:
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_INVENTORY
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
        view_id = request.POST.get("view_id", None)
        if "update-view-button" in request.POST:
            view_name = request.POST.get("view_name", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column = request.POST.get("column", None)
            status_selector = request.POST.get("status-selector", None)

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
            # create
            else:
                view = View.objects.create(
                    workspace=workspace, title=view_name, target="commerce_inventory"
                )

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            if view_name:
                view.title = view_name

            if view_table:
                view_filter.view_type = view_table

            view_filter.archive = archive

            # convert to normal List
            column = column.split(",")
            if view and column[0]:
                view_filter.column = column
            else:
                view_filter.column = None

            if view_table and status_selector:
                if view_table == "kanban":
                    view_filter.choice_customfield = status_selector
                else:
                    view_filter.choice_customfield = None

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            # Handle "between" operation (have two filter values)
            filter_values2 = request.POST.getlist("filter_value-2", None)
            idx_filter_values2 = 0
            for idx, filter_type in enumerate(filter_types):
                value = filter_values[idx]
                if filter_options[idx] == "between":
                    try:
                        value = (
                            f"{filter_values[idx]}-{filter_values2[idx_filter_values2]}"
                        )
                    except:
                        value = ""
                    idx_filter_values2 += 1
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": value,
                }
            view_filter.filter_value = filter_dictionary
            view_filter.save()
            view.save()

            if module_slug:
                if view.title:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + "?view_id="
                        + str(view.id)
                    )
                else:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
            return redirect(reverse("main", host="app"))

        elif "bulk_delete_items" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title="main", target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            objects.update(usage_status="archived")
            sync_usage(workspace, WAREHOUSE_USAGE_CATEGORY)

        elif "bulk_restore_items" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title="main", target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            available_storage = get_workspace_available_storage(
                workspace, WAREHOUSE_USAGE_CATEGORY
            )
            sync_usage(workspace, INVENTORY_USAGE_CATEGORY)

            if available_storage is None or available_storage > 0:
                objects.update(usage_status="active")
                sync_usage(workspace, INVENTORY_USAGE_CATEGORY)

        elif "update-view-button-delete" in request.POST:
            view_id = request.POST.get("view_id", None)
            if view_id:
                View.objects.get(id=view_id).delete()
            if module_slug:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "csv_download" in request.POST or "download" in request.POST:
            # Handle locations export via background job
            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="export_inventory_warehouse",
            )

            # Get columns and parameters
            transaction_columns = request.POST.get(
                "column", "name,location,updated_at,created_at"
            )
            encoded_format = request.POST.get("encoded_format", "utf-8")

            # Import the background job
            from data.inventory.background.export_csv_locations import (
                ExportCSVLocationsPayload,
                export_csv_locations,
            )
            from utils.bgjobs.handler import create_bg_job, add_hatchet_run_id

            # Create payload for background job
            payload = ExportCSVLocationsPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                history_id=str(history.id),
                locations_columns=transaction_columns,
                encoded_format=encoded_format,
                language=lang,
            )

            # Create background job
            job_id = create_bg_job(
                workspace=workspace,
                user=request.user,
                function_name="export_csv_locations",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id

            # Log export job parameters for debugging
            logger.info(
                f"EXPORT_JOB: Starting locations export for user {request.user.email} in workspace {workspace.id}"
            )
            logger.info(
                f"EXPORT_JOB: Export parameters - function: export_csv_locations, job_id: {job_id}"
            )
            logger.info(f"EXPORT_JOB: History ID: {history.id}, Language: {lang}")
            logger.info(f"EXPORT_JOB: Columns: {transaction_columns}")

            # Submit background job
            try:
                ref = export_csv_locations.run_no_wait(input=payload)
            except Exception as e:
                logger.error(
                    f"EXPORT_JOB: Exception occurred during export_csv_locations: {str(e)}",
                    exc_info=True,
                )
                ref = None

            is_running = None
            if ref:
                logger.info(
                    f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}"
                )
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit background job for user {request.user.email}"
                )
                is_running = False

            if is_running:
                logger.info(
                    f"EXPORT_JOB: Successfully submitted export job for user {request.user.email}"
                )
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, csv will be sent to your email.",
                        type="success",
                    )
            else:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートに失敗しました。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export failed.",
                        type="error",
                    )

            return redirect(request.META.get("HTTP_REFERER", "/"))

        elif "task_id" in request.POST:
            task = TransferHistory.objects.filter(
                workspace=workspace, id=request.POST.get("task_id")
            ).first()
            if task:
                task.status = "canceled"
                task.save()

        elif "bulk_duplicate" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title="main", target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            location_ids = objects.values_list("id", flat=True)
            sync_usage(workspace, WAREHOUSE_USAGE_CATEGORY)
            available_storage = get_workspace_available_storage(
                workspace, WAREHOUSE_USAGE_CATEGORY
            )
            objects = base_model.objects.filter(id__in=location_ids)[:available_storage]
            for obj in objects:
                customFields = custom_value_model.objects.filter(
                    **{custom_value_relation: obj}
                )

                obj.id = None
                setattr(obj, id_field, None)
                obj.created_at = timezone.now()
                obj.save()

                for field in customFields:
                    field.id = None
                    setattr(field, custom_value_relation, obj)
                    field.save()
            sync_usage(workspace, WAREHOUSE_USAGE_CATEGORY)

        if view_id:
            if module_slug:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + "?view_id="
                    + str(view_id)
                )
        if module_slug:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def inventory_warehouse_options(request):
    workspace = get_workspace(request.user)
    q = request.GET.get("q")
    page = request.GET.get("page", 1)
    channel_id = request.GET.get("channel_id", None)

    filter_conditions = Q(workspace=workspace, usage_status="active")
    if q:
        q_id = q

        try:
            if q[0] == "#" or q[0] == "0":
                q_id = int(q[1:].replace(" ", ""))
        except Exception as e:
            logger.debug(e)

        filter_conditions &= Q(id_iw__icontains=q_id)

    if channel_id:
        filter_conditions &= Q(inventory_warehouse_platforms__channel__id=channel_id)

    locations = InventoryWarehouse.objects.filter(filter_conditions).order_by("-id_iw")

    res = []
    ITEMS_PER_PAGE = 30
    locations_paginator = Paginator(locations, ITEMS_PER_PAGE)
    paginated_locations = []
    more_pagination = False
    if page:
        try:
            locations_page_content = locations_paginator.page(page if page else 1)
            paginated_locations = locations_page_content.object_list
            more_pagination = locations_page_content.has_next()
        except EmptyPage:
            pass

    for location in paginated_locations:
        try:
            text = f"#{location.id_iw:04d} - {location.location}"
        except Exception:
            pass

        res.append(
            {
                "id": str(location.id),
                "text": text,
            }
        )

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


@login_or_hubspot_required
def shopify_sync_map_locations(request):
    action_index = request.GET.get("action_index", None)
    channel_ids = request.GET.getlist("channel_id", [])
    all_map_id = request.GET.getlist("all_map_id", [])

    method = request.GET.get("method", None)

    channel_list = []
    if channel_ids:
        for channel_id in channel_ids:
            channel = Channel.objects.filter(id=channel_id).first()
            if channel:
                if not all_map_id:
                    pull_shopify_warehouse(channel_id)
                elif method == "sync":
                    pull_shopify_warehouse(channel_id)

                channel_list.append(channel)

    if method == "sync":
        return HttpResponse()

    if not channel_list:
        return JsonResponse({"status": 400, "message": "No channel selected"})

    latest_id = 0
    for map_id in all_map_id:
        id = map_id.replace("mapping-", "")
        if int(id) > latest_id:
            latest_id = int(id)

    context = {
        "channel_list": channel_list,
        "action_index": action_index,
        "n_map": latest_id + 1,
    }

    return render(request, "data/locations/shopify_map_locations.html", context)


def pull_shopify_warehouse(channel_id):
    channel = Channel.objects.get(id=channel_id)
    api_key = channel.api_key
    access_token = channel.access_token
    shop_name = channel.account_id

    all_locations, page = get_all_shopify_locations(api_key, access_token, shop_name)
    insert_shopify_warehouse(all_locations, channel_id)
    while page and page.has_next_page():
        all_locations, page = get_all_shopify_locations(
            api_key, access_token, shop_name, all_locations
        )
        insert_shopify_warehouse(all_locations, channel_id)


def insert_shopify_warehouse(all_locations, channel_id):
    channel = Channel.objects.get(id=channel_id)
    workspace = channel.workspace

    for location in all_locations:
        try:
            location_id = location["id"]
            location_name = location["name"]

            logger.debug("[DEBUG] insert_shopify_warehouse -- Location ID", location_id)
            logger.debug(
                "[DEBUG] insert_shopify_warehouse -- Location Name", location_name
            )

            warehouse_platform = InventoryWarehousePlatforms.objects.filter(
                channel=channel,
                location_id=location_id,
                component_type="child",
            ).first()

            logger.debug(
                "[DEBUG] insert_shopify_warehouse -- Warehouse Platform",
                warehouse_platform,
            )

            if not warehouse_platform:
                warehouse_platform, _ = (
                    InventoryWarehousePlatforms.objects.get_or_create(
                        channel=channel,
                        location_id=location_id,
                        component_type="child",
                    )
                )

            if warehouse_platform.warehouse:
                warehouse = warehouse_platform.warehouse
            else:
                warehouse = InventoryWarehouse.objects.create(
                    workspace=workspace,
                    warehouse=location_name,
                )

            logger.debug("[DEBUG] insert_shopify_warehouse -- Warehouse", warehouse)

            warehouse.warehouse = location_name
            warehouse.save()
            warehouse_platform.warehouse = warehouse
            warehouse_platform.save()

            logger.debug(
                "[DEBUG] insert_shopify_warehouse -- Warehouse Platform Created"
            )
        except Exception as e:
            logger.debug("[DEBUG] insert_shopify_warehouse -- Error ", e)
            continue


def get_all_shopify_locations(api_key, api_access_key, account_id, page=None):
    locations = []
    page = None  # Initialize page to None to handle exception cases
    try:
        shop_url = account_id
        session = shopify.Session(shop_url, api_version, api_access_key)
        shopify.ShopifyResource.activate_session(session)

        if page:
            shopify_locs = page.next_page()
        else:
            shopify_locs = shopify.Location.find()

        page = shopify_locs

        for loc in shopify_locs:
            loc_data = {
                "id": loc.id,
                "name": loc.name,
            }

            logger.debug("[DEBUG] loc_data:", loc_data)
            locations.append(loc_data)

        logger.debug("Shopify API version:", shopify.ShopifyResource.version)
        shopify.ShopifyResource.clear_session()
    except:
        traceback.logger.debug_exc()
        logger.debug("Failed to fetch shopify locations: ")
        page = None  # Explicitly set page to None on exception

    return locations, page


@login_or_hubspot_required
def sync_item_and_inventory(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if not lang:
        lang = "ja"

    if request.method == "GET":
        # Required
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                logger.debug("Action node does not exist")
                return HttpResponse(status=404)

        input_data = {}
        if node:
            input_data = node.input_data

        platforms = ["shopify", "rakuten", "yahoo-shopping", "amazon"]
        channels = Channel.objects.filter(
            workspace=get_workspace(request.user), integration__slug__in=platforms
        ).exclude(status="draft")

        cf_filters = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace, type="choice"
        )

        channels = channels.exclude(name__icontains="Hubspot Power Inventory")

        for name in EXCLUDE_SYNC_CHANNEL_NAME:
            channels = channels.exclude(name__icontains=name)

        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ITEM
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        context = {
            "channels": channels,
            "drawer_type": "shopturbo-view-sync-items",
            "item_ids": "",
            "object_type": TYPE_OBJECT_ITEM,
            "filters": cf_filters,
            "menu_key": module_slug,
            "import_export_type": "export",
            "action_index": action_index,
            "action_slug": action_slug,
            "input_data": input_data,
        }
        return render(request, "data/shopturbo/sync-items-action.html", context)

    if request.method == "POST":
        logger.debug("[DEBUG] POST", request.POST)

        submit_option = request.POST.get("submit-option")
        action_index = request.POST.get("action_index")
        action_node_id = request.POST.get("action_node_id")
        action_slug = request.POST.get("action_slug")
        at_id = request.POST.get("action_tracker_id")
        wat_id = request.POST.get("workflow_action_tracker_id")

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                logger.debug("ActionNode does not exist")
                return HttpResponse(status=404)
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                logger.debug("ActionTracker does not exist")
                return HttpResponse(status=404)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                logger.debug("WorkflowActionTracker does not exist")
                return HttpResponse(status=404)

        # Text Field
        postfix = ""
        if action_index:
            postfix = "-" + action_index
        action_name = request.POST.get("action_name" + postfix, None)

        try:
            integration_ids = request.POST.getlist(
                "select_integration_ids" + postfix, []
            )

            item_file_columns = request.POST.getlist("item-file-column" + postfix, [])
            item_sanka_properties = request.POST.getlist(
                "sanka-item-properties" + postfix, []
            )
            item_ignores = request.POST.getlist("item-ignore" + postfix, [])
        except:
            integration_ids = []

            item_file_columns = []
            item_sanka_properties = []
            item_ignores = []

        filter_method = request.POST.get("filter-method" + postfix, None)

        filter_type = (
            request.POST.get("filter-type" + postfix, None)
            if filter_method == "choice_filter"
            else None
        )
        filter_id = (
            request.POST.get("filter" + postfix, None)
            if filter_method == "choice_filter"
            else None
        )
        filter_choice = (
            request.POST.get("filter-choice" + postfix, None)
            if filter_method == "choice_filter"
            else None
        )
        filter_sku = (
            request.POST.get("sku-filter" + postfix, None)
            if filter_method == "sku"
            else None
        )

        sync_key = request.POST.get(
            "sync-key" + postfix, TYPE_SHOPIFY_SYNC_KEY_PLATFORM
        )

        # shopify_mapping|map_number|channel_id|postfix
        shopify_location_mapping = {}
        for query in request.POST:
            if "shopify_mapping" in query:
                if postfix.replace("-", "") == query.split("|")[-1]:
                    map_id = query.split("|")[1]
                    channel_id = query.split("|")[2]
                    value = request.POST.get(query)
                    if map_id not in shopify_location_mapping:
                        shopify_location_mapping[map_id] = {}

                    shopify_location_mapping[map_id][channel_id] = value

        item_mapping = {}
        if "save_item_mapping" + postfix in request.POST:
            mapping_item_custom_fields = {}
            for idx, file_column in enumerate(item_file_columns):
                # check ignore
                item_mapping[file_column] = item_sanka_properties[idx]
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            mapping, _ = ShopTurboItemsMappingFields.objects.get_or_create(
                workspace=workspace, platform="shopify", mapping_type__isnull=True
            )
            data = {}
            for i, field in enumerate(item_mapping):
                field_data = {"value": item_mapping[field], "skip": item_ignores[i]}
                data[field] = field_data
            mapping.input_data = data
            mapping.save()

            return HttpResponse(status=200)
        if submit_option == "save":
            node.valid_to_run = True

            required_fields = [integration_ids, sync_key]
            if not all(required_fields):
                logger.debug("Required fields are missing")
                node.valid_to_run = False

            input_data = {
                "integration_ids": integration_ids,
                "item_file_columns": item_file_columns,
                "item_sanka_properties": item_sanka_properties,
                "item_ignores": item_ignores,
                "filter_method": filter_method,
                "filter_type": filter_type,
                "filter_id": filter_id,
                "filter_choice": filter_choice,
                "filter_sku": filter_sku,
                "sync_key": sync_key,
                "shopify_location_mapping": shopify_location_mapping,
                "action_name": action_name,
            }

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            return HttpResponse(status=200)
        else:
            if submit_option == "run":
                integration_ids = node.input_data.get("integration_ids", [])

                item_file_columns = node.input_data.get("item_file_columns", [])
                item_sanka_properties = node.input_data.get("item_sanka_properties", [])
                item_ignores = node.input_data.get("item_ignores", [])

                filter_method = node.input_data.get("filter_method", None)
                filter_type = node.input_data.get("filter_type", None)
                filter_id = node.input_data.get("filter_id", None)
                filter_choice = node.input_data.get("filter_choice", None)
                filter_sku = node.input_data.get("filter_sku", None)

                shopify_location_mapping = node.input_data.get(
                    "shopify_location_mapping", {}
                )

                sync_key = node.input_data.get(
                    "sync_key", TYPE_SHOPIFY_SYNC_KEY_PLATFORM
                )
            else:
                required_fields = [integration_ids, sync_key]
                if not all(required_fields):
                    logger.debug("Required fields are missing")
                    return HttpResponse(status=400)

            # Get Channels
            channels = Channel.objects.filter(id__in=integration_ids)
            channel_ids = [str(channel.id) for channel in channels]
            is_shopify = any(
                [channel.integration.slug == "shopify" for channel in channels]
            )

            # Get Items and inventory
            items = ShopTurboItems.objects.filter(workspace=workspace, status="active")
            inventories = ShopTurboInventory.objects.filter(
                item__in=items, workspace=workspace
            )

            filter_items = None
            if filter_method == "choice_filter":
                if filter_id:
                    cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                        id=filter_id
                    ).first()
                    if cf_filter:
                        value = filter_choice
                        if value:
                            cf_value_items = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    field_name=cf_filter, value=value
                                )
                            )
                            if cf_value_items:
                                filter_items = cf_value_items.values_list(
                                    "items", flat=True
                                )
                                filter_items = filter_items.distinct()

                if filter_items:
                    if filter_type == "exclude":
                        items = items.exclude(id__in=filter_items)
                        inventories = inventories.exclude(item__in=items)
                    else:
                        items = items.filter(id__in=filter_items)
                        inventories = inventories.filter(item__in=items)
            elif filter_method == "sku":
                if filter_sku:
                    filter_sku = filter_sku
                else:
                    filter_sku = None
            else:
                filter_items = None
                filter_sku = None

            channel_name = [channel.name for channel in channels]
            channel_name = ", ".join(channel_name)
            if lang == "ja":
                task_name = f"({channel_name}) の商品と在庫の同期ワークフロー"
            else:
                task_name = (
                    f"Sync Item and Inventory Workflow for Channels ({channel_name})"
                )

            background_job = BackgroundJob.objects.filter(
                workspace_id=str(workspace.id),
                status=BackgroundJob.BackgroundJobStatus.RUNNING,
                payload__function="shopify_sync_items",
            ).first()

            if background_job:
                message = "Another sync item job is running. Cancel or Wait until its completed"
                if lang == "ja":
                    message = "別の同期アイテムジョブがまだ実行中です。キャンセルするか、完了するまでお待ちください。"

                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=message,
                    type="error",
                )
                at.output_data = {"status": "error", "message": message}
                return_fn = get_redirect_workflow(
                    submit_option, node, workspace, at, wat, status="failed"
                )
                return redirect(return_fn)

            if is_shopify and channel_ids:
                if filter_items:
                    filter_items = [str(item_id) for item_id in filter_items]

                channel = Channel.objects.filter(id__in=channel_ids).first()
                if not channel:
                    channel = None

                import_history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="import_item",
                    name=task_name,
                    channel=channel or None,
                )
                export_history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="export_item",
                    name=task_name,
                    channel=channel or None,
                )

                run_bg_job = is_run_bg_job(
                    "shopify_sync_items",
                    workspace,
                )

                if not run_bg_job:
                    if lang == "ja":
                        message = "Shopifyのアイテムと在庫の同期ジョブがまだ実行中です。完了するまでお待ちください。"
                    else:
                        message = "Shopify sync item and inventory job is still running, please wait until it is completed."

                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=message,
                        type="error",
                    )
                    at.output_data = {"status": "error", "message": message}
                    return_fn = get_redirect_workflow(
                        submit_option, node, workspace, at, wat, status="failed"
                    )
                    return redirect(return_fn)

                payload = ShopifySyncItemsPayload(
                    user_id=str(request.user.id),
                    workspace_id=str(workspace.id),
                    channel_ids=channel_ids,
                    item_mapping=item_mapping,
                    sync_method=sync_key,
                    filter_method=filter_method,
                    filter_items=filter_items,
                    filter_sku=filter_sku,
                    import_history_id=str(import_history.id),
                    export_history_id=str(export_history.id),
                    language=lang,
                    filter_type=filter_type,
                    shopify_location_mapping=shopify_location_mapping,
                )

                job_id = create_bg_job(
                    workspace,
                    request.user,
                    "shopify_sync_items",
                    transfer_history=import_history,
                    payload=payload.model_dump(mode="json"),
                )
                payload.background_job_id = job_id

                ref = None
                try:
                    ref = shopify_sync_dag.run_no_wait(input=payload)
                except Exception as e:
                    logger.error(
                        f"EXPORT_JOB: Exception occurred during shopify_sync_items: {str(e)}",
                        exc_info=True,
                    )
                    ref = None

                is_running = None
                if ref:
                    logger.info(
                        f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}"
                    )
                    add_hatchet_run_id(job_id, ref)
                    is_running = True
                else:
                    logger.error(
                        f"EXPORT_JOB: Failed to submit background job for user {request.user.email}"
                    )
                    is_running = False

                if is_running:
                    if lang == "ja":
                        message = "Shopifyのアイテムと在庫の同期ジョブが正常に送信されました。"
                    else:
                        message = "Shopify sync item and inventory job submitted successfully."

                    at.output_data = {"status": "run", "message": message}
                    return_fn = get_redirect_workflow(
                        submit_option, node, workspace, at, wat, status="success"
                    )
                    return redirect(return_fn)
                else:
                    if lang == "ja":
                        message = "Shopifyのアイテムと在庫の同期ジョブが失敗しました。"
                    else:
                        message = "Shopify sync item and inventory job failed."

                    import_history.status = "failed"
                    import_history.save()
                    export_history.status = "failed"
                    export_history.save()

                    at.output_data = {"status": "error", "message": message}
                    return_fn = get_redirect_workflow(
                        submit_option, node, workspace, at, wat, status="failed"
                    )
                    return redirect(return_fn)

            if lang == "ja":
                at.output_data = {
                    "status": "error",
                    "message": "実行するものがありません。設定を確認してください",
                }
            else:
                at.output_data = {
                    "status": "error",
                    "message": "Nothing to run, please check your configuration",
                }
            return_fn = get_redirect_workflow(
                submit_option, node, workspace, at, wat, status="failed"
            )
            return redirect(return_fn)

    return HttpResponse(status=200)


@login_or_hubspot_required
def sync_item_shopify_webhook(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if not lang:
        lang = "ja"

    if request.method == "GET":
        # Required
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                logger.debug("Action node does not exist")
                return HttpResponse(status=404)

        input_data = {}
        if node:
            input_data = node.input_data

        platforms = ["shopify"]
        channels = Channel.objects.filter(
            workspace=get_workspace(request.user), integration__slug__in=platforms
        ).exclude(status="draft")

        channels = channels.exclude(name__icontains="Hubspot Power Inventory")

        for name in EXCLUDE_SYNC_CHANNEL_NAME:
            channels = channels.exclude(name__icontains=name)

        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ITEM
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        context = {
            "channels": channels,
            "drawer_type": "shopturbo-view-sync-items",
            "item_ids": "",
            "object_type": TYPE_OBJECT_ITEM,
            "menu_key": module_slug,
            "import_export_type": "export",
            "action_index": action_index,
            "action_slug": action_slug,
            "input_data": input_data,
        }
        return render(
            request, "data/shopturbo/sync-items-shopify-webhook.html", context
        )

    if request.method == "POST":
        logger.debug("[DEBUG] POST", request.POST)

        submit_option = request.POST.get("submit-option")
        action_index = request.POST.get("action_index")
        action_node_id = request.POST.get("action_node_id")
        action_slug = request.POST.get("action_slug")
        at_id = request.POST.get("action_tracker_id")
        wat_id = request.POST.get("workflow_action_tracker_id")

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                logger.debug("ActionNode does not exist")
                return HttpResponse(status=404)
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                logger.debug("ActionTracker does not exist")
                return HttpResponse(status=404)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                logger.debug("WorkflowActionTracker does not exist")
                return HttpResponse(status=404)
        try:
            webhook_data = at.input_data["data"]
        except:
            webhook_data = None

        # Text Field
        postfix = ""
        if action_index:
            postfix = "-" + action_index
        action_name = request.POST.get("action_name" + postfix, None)

        try:
            integration_ids = request.POST.getlist(
                "select_integration_ids" + postfix, []
            )
        except:
            integration_ids = []

        source_integration_id = request.POST.get(
            "source_integration_id" + postfix, None
        )

        if submit_option == "save":
            node.valid_to_run = True

            required_fields = [integration_ids, source_integration_id]
            if not all(required_fields):
                logger.debug("Required fields are missing")
                node.valid_to_run = False

            input_data = {
                "integration_ids": integration_ids,
                "source_integration_id": source_integration_id,
                "action_name": action_name,
            }

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            return HttpResponse(status=200)
        else:
            if submit_option == "run":
                integration_ids = node.input_data.get("integration_ids", [])
                source_integration_id = node.input_data.get(
                    "source_integration_id", None
                )
            else:
                required_fields = [integration_ids, source_integration_id]
                if not all(required_fields):
                    logger.debug("Required fields are missing")
                    return HttpResponse(status=400)

            # Get Channels
            channels = Channel.objects.filter(id__in=integration_ids)
            channel_ids = [str(channel.id) for channel in channels]

            channel_name = [channel.name for channel in channels]
            channel_name = ", ".join(channel_name)

            # background_job = BackgroundJob.objects.filter(
            #     workspace_id=str(workspace.id),
            #     status=BackgroundJob.BackgroundJobStatus.RUNNING,
            #     payload__function="shopify_sync_items",
            # ).first()

            # if background_job:
            #     message = "Another sync item job is running. Cancel or Wait until its completed"
            #     if lang == "ja":
            #         message = "別の同期アイテムジョブがまだ実行中です。キャンセルするか、完了するまでお待ちください。"

            #     Notification.objects.create(
            #         workspace=workspace,
            #         user=request.user,
            #         message=message,
            #         type="error",
            #     )
            #     at.output_data = {"status": "error", "message": message}
            #     return_fn = get_redirect_workflow(
            #         submit_option, node, workspace, at, wat, status="failed"
            #     )
            #     return redirect(return_fn)

            if channel_ids and webhook_data:
                try:
                    logger.info("[SHOPIFY SYNC] Initial webhook_data: %s", webhook_data)
                    webhook_data = ast.literal_eval(webhook_data)
                except:
                    logger.warning(
                        "[SHOPIFY SYNC] ast.literal_eval failed, falling back to JSON parsing"
                    )
                    webhook_data = json.loads(webhook_data)
                parent_channel = None

                logger.info(f"[SHOPIFY SYNC] webhook_data: {webhook_data}")

                if "vendor" in webhook_data:
                    # account_id = webhook_data["vendor"] + ".myshopify.com"
                    vendor_name = f"Shopify - {webhook_data['vendor']}"

                    if source_integration_id:
                        parent_channel = Channel.objects.filter(
                            workspace=workspace, id=source_integration_id
                        ).first()
                        logger.info(
                            f"[SHOPIFY SYNC] Found parent channel by ID: {parent_channel}"
                        )

                    logger.info(
                        f"[SHOPIFY SYNC] Fetching parent channel for vendor: {vendor_name}"
                    )
                    new_parent_channel = Channel.objects.filter(
                        workspace=workspace, name="abcd"
                    ).first()

                    if new_parent_channel:
                        parent_channel = new_parent_channel
                        logger.info(
                            f"[SHOPIFY SYNC] Found parent channel by vendor: {parent_channel}"
                        )

                    logger.info(
                        f"[SHOPIFY SYNC] parent_channel:, {vendor_name}, {parent_channel}"
                    )

                    if "id" in webhook_data and parent_channel:
                        if str(parent_channel.id) in channel_ids:
                            channel_ids.remove(str(parent_channel.id))
                        related_channels = channel_ids
                        logger.info(f"[SHOPIFY SYNC] Log 1: {related_channels}")
                        push_shopify_webhook_items(
                            parent_channel.id, related_channels, webhook_data
                        )

                else:
                    item_platform = ShopTurboItemsPlatforms.objects.filter(
                        channel__id__in=channel_ids,
                        platform_id=webhook_data["id"],
                        platform_type="external",
                    ).first()
                    logger.info(f"[SHOPIFY SYNC] Log 2: {item_platform}")

                    parent_channel = item_platform.channel if item_platform else None
                    if str(parent_channel.id) in channel_ids:
                        channel_ids.remove(str(parent_channel.id))
                    related_channels = channel_ids

                    if parent_channel:
                        delete_shopify_webhook_items(
                            parent_channel.id, related_channels, webhook_data
                        )

            elif lang == "ja":
                at.output_data = {
                    "status": "error",
                    "message": "実行するものがありません。設定を確認してください",
                }
            else:
                at.output_data = {
                    "status": "error",
                    "message": "Nothing to run, please check your configuration",
                }
            return_fn = get_redirect_workflow(
                submit_option, node, workspace, at, wat, status="success"
            )
            return redirect(return_fn)

    return HttpResponse(status=200)


@require_POST
@login_or_hubspot_required
def manage_inventory_transaction(request, id):
    source_url = request.POST.get("source_url")
    module_slug = request.POST.get("module")
    page = request.POST.get("page", 1)
    view_id = request.POST.get("view_id")
    workspace = get_workspace(request.user)
    if source_url:
        source_url = update_query_params_url(
            source_url, {"target": TYPE_OBJECT_INVENTORY_TRANSACTION, "id": id}
        )

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY_TRANSACTION]
    inventory_transaction = InventoryTransaction.objects.get(workspace=workspace, id=id)
    if "update-transaction" in request.POST:
        owner = request.POST.get("owner", None)
        transaction_type = request.POST.get("stock_type", None)
        inventory_id = request.POST.get("inventory", None)
        amount = request.POST.get("stock_change", None)
        select_price = request.POST.get("select-price", None)
        price = request.POST.get("price", None)
        transaction_time = request.POST.get("transaction_time", None)
        transaction_time = (
            transaction_time.replace("年", "-").replace("月", "-").replace("日", "")
        )
        transaction_time = datetime.strptime(transaction_time, "%Y-%m-%d %H:%M")
        if workspace.timezone:
            local_tz = pytz.timezone(workspace.timezone)
            localized_time = local_tz.localize(transaction_time)
            transaction_time = localized_time.astimezone(pytz.utc)

        is_update = False
        if str(inventory_transaction.inventory.id) != inventory_id:
            is_update = True
            inventory_transaction.inventory = ShopTurboInventory.objects.get(
                id=inventory_id
            )

        association_label = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_INVENTORY_TRANSACTION,
            label__iexact=TYPE_OBJECT_INVENTORY,
        ).first()
        AssociationLabelObject.reset_associations_for_object(
            inventory_transaction, workspace, association_label
        )
        AssociationLabelObject.create_association(
            inventory_transaction,
            inventory_transaction.inventory,
            workspace,
            association_label,
        )

        if not is_update:
            if (
                inventory_transaction.amount != int(amount)
                or inventory_transaction.transaction_type != transaction_type
                or inventory_transaction.transaction_date != transaction_time
            ):
                is_update = True

        if transaction_type:
            inventory_transaction.transaction_type = transaction_type

        if transaction_time:
            inventory_transaction.transaction_date = transaction_time

        if select_price and price and price != "None":
            inventory_transaction.price = price
            inventory_transaction.use_unit_value = True
        else:
            latest_transaction = (
                InventoryTransaction.objects.exclude(
                    pk=inventory_transaction.id, usage_status="archived"
                )
                .filter(
                    inventory=inventory_transaction.inventory,
                    transaction_date__lte=inventory_transaction.transaction_date,
                )
                .order_by("-transaction_date", "-created_at")
                .first()
            )
            if latest_transaction:
                inventory_transaction.price = latest_transaction.average_price
            else:
                # Fallback to inventory unit price or 0 if no previous transactions exist
                inventory_transaction.price = (
                    getattr(inventory_transaction.inventory, "unit_price", 0) or 0
                )
            inventory_transaction.use_unit_value = False

        if amount:
            inventory_transaction.amount = int(amount)
            if inventory_transaction.price:
                inventory_transaction.total_price = float(
                    inventory_transaction.price
                ) * int(amount)

        assign_object_owner(
            inventory_transaction, owner, request, TYPE_OBJECT_INVENTORY_TRANSACTION
        )
        inventory_transaction.save()

        old_transaction = (
            InventoryTransaction.objects.filter(
                inventory=inventory_transaction.inventory
            )
            .order_by("transaction_date", "created_at")
            .first()
        )
        if not old_transaction:
            old_transaction = InventoryTransaction.objects.create(
                workspace=inventory_transaction.workspace,
                inventory=inventory_transaction.inventory,
                transaction_type="in",
                amount=0,
                transaction_date=inventory_transaction.inventory.date,
                transaction_amount=0,
            )
        old_transaction = recalculate_transactions_after(
            inventory_transaction.inventory, old_transaction, request
        )
        inventory_transaction.inventory.refresh_from_db()
        update_inventory_stock_price(inventory_transaction.inventory, request)
        inventory_transaction.inventory.refresh_from_db()
        re_calculate_inventory_stock(inventory_transaction.inventory)

        save_custom_property(request, inventory_transaction)
        save_association_label(
            request, inventory_transaction, TYPE_OBJECT_INVENTORY_TRANSACTION
        )

    elif "toggle-transaction" in request.POST:
        no_redirect = request.POST.get("no-redirect")
        update_inventory = False
        if inventory_transaction.usage_status == "archived":
            if has_quota(
                get_workspace(request.user), INVENTORY_TRANSACTION_USAGE_CATEGORY
            ):
                inventory_transaction.usage_status = "active"
                inventory_transaction.save(
                    log_data={"user": request.user, "workspace": workspace}
                )
                recalculate_transactions_after(
                    inventory_transaction.inventory, inventory_transaction, request
                )
                update_inventory = True
        else:
            inventory_transaction.usage_status = "archived"
            inventory_transaction.save(
                log_data={"user": request.user, "workspace": workspace}
            )
            recalculate_transactions_after(
                inventory_transaction.inventory, inventory_transaction
            )
            update_inventory = True
        inventory_transaction.inventory.refresh_from_db()
        update_inventory_stock_price(inventory_transaction.inventory, request)

        if update_inventory:
            inventory = inventory_transaction.inventory
            inventory.refresh_from_db()
            re_calculate_inventory_stock(inventory)

        if no_redirect:
            return HttpResponse()

    elif "add_association" in request.POST:
        target_association = request.POST.get("add_association")
        if target_association == "purchaseorder":
            po_ids = request.POST.getlist("purchase_orders")
            inventory_transaction.purchaseorders_set.clear()
            pos = PurchaseOrders.objects.filter(
                id__in=po_ids, usage_status="active", workspace=workspace
            )
            for po in pos:
                inventory_transaction.purchaseorders_set.add(po)

    elif "update_inventories" in request.POST:
        object_id = request.POST.get("object_id")
        transaction = InventoryTransaction.objects.get(id=object_id)

        object_id = request.POST.get("object_id")
        transaction = InventoryTransaction.objects.filter(id=object_id).first()
        inventory_id = request.POST.get("inventories", [])
        inventory = ShopTurboInventory.objects.filter(id=inventory_id).first()

        if transaction and inventory:
            items = inventory.item.all()
            price = inventory.unit_price
            average_price = price
            if len(items) > 0 and not price:
                item = items[0]
                item_price = ShopTurboItemsPrice.objects.filter(
                    item=item, default=True
                ).first()
                if item_price:
                    price = item_price.price
                    average_price = item_price.price

            transaction.amount = int(inventory.initial_value)
            transaction.price = price
            transaction.average_price = average_price
            transaction.transaction_amount = int(inventory.initial_value)
            transaction.transaction_date = inventory.date
            transaction.inventory = inventory

            transaction.save()

    if source_url:
        return redirect(source_url)
    if module_slug:
        return redirect(
            build_redirect_url(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                ),
                view_id=view_id,
                page=page,
                transaction_id=id,
                target=TYPE_OBJECT_INVENTORY_TRANSACTION,
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def calculate_inventory_stock(request):
    inventory_id = request.GET.get("inventory", None)
    quantity = request.GET.get("quantity", None)
    exchange_type = request.GET.get("exchange_type", None)
    result = 0
    price = 0
    if inventory_id:
        inventory = ShopTurboInventory.objects.get(pk=inventory_id)

        result = inventory.total_inventory
        if exchange_type == "in":
            result += int(quantity)
        elif exchange_type == "out":
            result -= int(quantity)

        price = ""
        # Get the latest transaction based on the status parameter
        status = request.GET.get("status")
        if status == "archived":
            latest_transaction = InventoryTransaction.objects.filter(
                inventory=inventory, usage_status="archived"
            ).order_by("-transaction_date", "-created_at")
        else:
            latest_transaction = InventoryTransaction.objects.filter(
                inventory=inventory, usage_status="active"
            ).order_by("-transaction_date", "-created_at")
        if latest_transaction:
            latest_transaction = latest_transaction.first()
            if latest_transaction:
                price = latest_transaction.average_price
            else:
                price = 0
        else:
            inventory_items = inventory.item.all()
            if inventory_items:
                inventory_item = inventory_items.first()
                price = inventory_item.price if inventory_item else 0
            else:
                price = 0

    return JsonResponse({"result": result, "price": price})


@login_or_hubspot_required
@require_POST
def shopturbo_create_inventory_warehouse(request):
    lang = request.LANGUAGE_CODE
    target = "commerce_inventory_warehouse"
    workspace = get_workspace(request.user)

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY_WAREHOUSE]
    module_slug = request.POST.get("module")

    if not has_quota(workspace, WAREHOUSE_USAGE_CATEGORY):
        if module_slug:
            return reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        return redirect(reverse("main", host="app"))

    if "create_inventory_location" in request.POST:
        # inventory_id = request.POST.get('inventory',None)
        # if inventory_id:
        # inventory = ShopTurboInventory.objects.get(pk=inventory_id)

        source_url = request.POST.get("source_url")
        warehouse_ = request.POST.get("warehouse")
        floor = request.POST.get("floor")
        zone = request.POST.get("zone")
        aisle = request.POST.get("aisle")
        rack = request.POST.get("rack")
        shelf = request.POST.get("shelf")
        bin = request.POST.get("bin")
        source_object_type = request.POST.get("source_object_type")
        object_id = request.POST.get("object_id")
        owner = request.POST.get("owner", None)

        warehouse = InventoryWarehouse.objects.create(
            workspace=workspace,
        )
        warehouse.save(
            log_data={"user": request.user, "status": "create", "workspace": workspace}
        )

        location = ""
        if warehouse_:
            warehouse.warehouse = warehouse_
            location += "-" + warehouse_
        if floor:
            warehouse.floor = floor
            location += "-" + floor
        if zone:
            warehouse.zone = zone
            location += "-" + zone
        if aisle:
            warehouse.aisle = aisle
            location += "-" + aisle
        if rack:
            warehouse.rack = rack
            location += "-" + rack
        if shelf:
            warehouse.shelf = shelf
            location += "-" + shelf
        if bin:
            warehouse.bin = bin
            location += "-" + bin
        if location:
            if location[0] == "-":
                location = location[1:]
            warehouse.location = location

        assign_object_owner(warehouse, owner, request, TYPE_OBJECT_INVENTORY_WAREHOUSE)

        warehouse.save(log_data={"user": request.user, "workspace": workspace})

        save_custom_property(request, warehouse)

        if source_object_type and object_id:
            page_object = get_page_object(source_object_type)
            source_model = page_object["base_model"]
            source_custom_value_model = page_object["custom_value_model"]
            source_custom_model = page_object["custom_model"]
            source_custom_value_relation = page_object["custom_value_relation"]

            source_custom_object = source_custom_model.objects.filter(
                workspace=workspace, type="warehouse_objects"
            ).first()
            if source_custom_object:
                try:
                    source_object = source_model.objects.get(
                        workspace=workspace, id=object_id
                    )
                    source_custom_value_model.objects.create(
                        field_name=source_custom_object,
                        **{source_custom_value_relation: source_object},
                    )
                except:
                    pass
        # association related
        if request.POST.get("type_association", "") == "create-association":
            source = request.POST.get("source")
            module_object_slug = OBJECT_TYPE_TO_SLUG[source]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=source
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug

            if source == TYPE_OBJECT_INVENTORY:
                object_id = request.POST.get("source_object_id")
                inventory_obj = ShopTurboInventory.objects.filter(id=object_id).first()
                if inventory_obj:
                    inventory_obj.warehouse = warehouse
                    inventory_obj.save()

        if warehouse:
            if source_url:
                source_url = update_query_params_url(
                    source_url, {"target": target, "id": [str(warehouse.id)]}
                )
            return redirect(source_url)
    elif "submit_csv_upload" in request.POST:
        csv = request.FILES.get("csv_upload", False)
        if not csv:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Failed to retreive the CSV file.",
                type="error",
            )
            if module_slug:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        file_columns = request.POST.getlist("inventory-file-column")
        sanka_properties = request.POST.getlist("inventory-sanka-properties")
        ignores = request.POST.getlist("inventory-ignore")

        try:
            mapping_storage, _ = ImportMappingFields.objects.get_or_create(
                workspace=workspace, object_type=TYPE_OBJECT_INVENTORY_WAREHOUSE
            )
            mapping = {
                sanka_prop: file_col
                for sanka_prop, file_col in zip(sanka_properties, file_columns)
            }
            mapping_storage.input_pair = mapping
            mapping_storage.save()

            if lang == "ja":
                task_name = "ロケーションのインポート"
            else:
                task_name = "Import Location"

            transfer_history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="import_inventory_warehouse",
                name=task_name,
            )

            with csv.open() as f:
                read_data = f.read()
                result = chardet.detect(read_data)
                encoding = result["encoding"]
                # #Reset Pointer
                f.seek(0)
                if encoding.lower() == "shift_jis":
                    content = f.read()
                    decoded_content = content.decode("shift_jis")
                    string_data = StringIO(decoded_content)
                    df = pd.read_csv(
                        string_data, sep=",", encoding="shift_jis", dtype=str
                    )
                else:
                    df = pd.read_csv(f, dtype=str)
                df = df.dropna(axis=1, how="all")
                df = df.dropna(axis=0, how="all")
                df = df.reset_index(drop=True)

            df = df.astype(str)
            for index, row in df.iterrows():
                create_json_inventory_location_custom_field = {}
                create_json_inventory_location = {}
                for idx, file_column in enumerate(file_columns):
                    # check ignore
                    if ignores[idx] == "True":
                        continue

                    if is_valid_uuid(sanka_properties[idx]):
                        custom_field = InventoryWarehouseNameCustomField.objects.filter(
                            id=sanka_properties[idx], workspace=workspace
                        ).first()
                        create_json_inventory_location_custom_field[
                            custom_field.name
                        ] = row[file_column]
                    else:
                        create_json_inventory_location[sanka_properties[idx]] = row[
                            file_column
                        ]

                warehouse = InventoryWarehouse.objects.create(workspace=workspace)

                # Populate rest of the key
                for create_json_order_key in create_json_inventory_location:
                    setattr(
                        warehouse,
                        create_json_order_key,
                        create_json_inventory_location[create_json_order_key],
                    )

                warehouse.save()

                for (
                    create_json_inventory_cs_field
                ) in create_json_inventory_location_custom_field:
                    inventory_name_custom_field = (
                        InventoryWarehouseNameCustomField.objects.get(
                            name=create_json_inventory_cs_field, workspace=workspace
                        )
                    )
                    CustomFieldValue, _ = (
                        InventoryWarehouseValueCustomField.objects.get_or_create(
                            field_name=inventory_name_custom_field, warehouse=warehouse
                        )
                    )
                    CustomFieldValue.value = (
                        create_json_inventory_location_custom_field[
                            create_json_inventory_cs_field
                        ]
                    )
                    CustomFieldValue.save()

                progress = 100 * (index + 1) / df.shape[0]
                transfer_history.progress = progress
                transfer_history.save()

                if transfer_history.status == "canceled":
                    break

            if transfer_history.status != "canceled":
                transfer_history.status = "completed"
                transfer_history.save()

        except Exception as e:
            traceback.logger.debug_exc()
            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="CSV入力エラー: "
                    + str(e)
                    + " | ヘッダー情報を確認してください。テンプレートと同じフォーマットである必要があります。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Error CSV input: "
                    + str(e)
                    + " | Please check your column headers. It should be same as the template.",
                    type="error",
                )

    if module_slug:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
@require_POST
def manage_inventory(request, id):
    target = "commerce_inventory"
    view_id = None
    source_url = request.POST.get("source_url")
    workspace = get_workspace(request.user)
    if source_url:
        source_url = update_query_params_url(
            source_url, {"target": target, "id": id}, overwrite=False
        )

    module = request.POST.get("module")
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY]

    shopturbo_inventory = ShopTurboInventory.objects.get(id=id)
    view_id = request.POST.get("view_id")
    page = request.POST.get("page", 1)
    if "update-inventory" in request.POST:
        owner = request.POST.get("owner", None)
        item_ids = request.POST.getlist("item", None)
        warehouse_id = request.POST.get("warehouse", None)
        if item_ids:
            # association
            association_label = AssociationLabel.objects.filter(
                workspace=workspace,
                object_source=TYPE_OBJECT_INVENTORY,
                label__iexact=TYPE_OBJECT_ITEM,
            ).first()
            if association_label:
                AssociationLabelObject.reset_associations_for_object(
                    shopturbo_inventory, workspace, association_label
                )
            shopturbo_inventory.item.clear()
            for item_id in item_ids:
                shiopturbo_item = ShopTurboItems.objects.get(id=item_id)
                shopturbo_inventory.item.add(shiopturbo_item)
                if association_label:
                    AssociationLabelObject.create_association(
                        shopturbo_inventory,
                        shiopturbo_item,
                        workspace,
                        association_label,
                    )

        inventory_date = request.POST.get("inventory-date", None)
        if inventory_date:
            inventory_date = str(inventory_date)
            inventory_date = (
                inventory_date.replace("年", "-").replace("月", "-").replace("日", "")
            )
            inventory_date = datetime.strptime(inventory_date, "%Y-%m-%d %H:%M")
            if workspace.timezone:
                local_tz = pytz.timezone(workspace.timezone)
                localized_time = local_tz.localize(inventory_date)
                inventory_date = localized_time.astimezone(pytz.utc)
            shopturbo_inventory.date = inventory_date

        assign_object_owner(shopturbo_inventory, owner, request, TYPE_OBJECT_INVENTORY)
        shopturbo_inventory.save()

        initial_value = request.POST.get("initial_value", None)
        inventory_status = request.POST.get("inventory_status", None)
        unit_price = request.POST.get("unit_price", None)
        currency = request.POST.get("currency", None)

        if unit_price:
            # Handle currency assignment
            if currency:
                # Use currency from form (could be workspace default or item currency)
                shopturbo_inventory.currency = currency
                logger.debug(
                    f"[DEBUG inventory] Setting currency from form: {currency}"
                )
            elif not shopturbo_inventory.currency:
                # Fallback: try to get currency from item
                try:
                    item_currency = shopturbo_inventory.item.first().currency
                    if item_currency:
                        shopturbo_inventory.currency = item_currency
                        logger.debug(
                            f"[DEBUG inventory] Setting currency from item: {item_currency}"
                        )
                    else:
                        # Final fallback: use workspace default currency
                        workspace_currencies = workspace.currencies
                        if workspace_currencies:
                            default_currency = workspace_currencies[0]
                            shopturbo_inventory.currency = default_currency
                            logger.debug(
                                f"[DEBUG inventory] Setting workspace default currency: {default_currency}"
                            )
                except Exception as e:
                    logger.debug(f"[ERROR inventory] Error setting currency: {e}")
                    # Final fallback: use workspace default currency
                    try:
                        workspace_currencies = workspace.currencies
                        if workspace_currencies:
                            default_currency = workspace_currencies[0]
                            shopturbo_inventory.currency = default_currency
                            logger.debug(
                                f"[DEBUG inventory] Setting workspace default currency as final fallback: {default_currency}"
                            )
                    except:
                        pass

            shopturbo_inventory.unit_price = unit_price
        if inventory_status in ["available", "unavailable", "committed"]:
            shopturbo_inventory.inventory_status = inventory_status
        re_calculate_stock = False
        if initial_value is not None:
            shopturbo_inventory.initial_value = initial_value
            initial_transaction = (
                InventoryTransaction.objects.filter(inventory=shopturbo_inventory)
                .order_by("transaction_date", "created_at")
                .first()
            )
            if initial_transaction:
                if (
                    inventory_date
                ):  # Only update transaction_date if inventory_date was processed
                    initial_transaction.transaction_date = inventory_date
                initial_transaction.save()
                if initial_transaction.amount != initial_value:
                    re_calculate_stock = True
                    initial_transaction.amount = initial_value
                    initial_transaction.transaction_amount = initial_value
                    initial_transaction.save()

        if warehouse_id:
            try:
                warehouse = InventoryWarehouse.objects.get(pk=warehouse_id)
                shopturbo_inventory.warehouse = warehouse
            except:
                logger.debug("manage_inventory ERROR: Warehouse not exist")
        else:
            shopturbo_inventory.warehouse = None
        shopturbo_inventory.save(
            log_data={"user": request.user, "workspace": workspace}
        )

        if re_calculate_stock:
            update_inventory_stock_price(shopturbo_inventory, request)
            shopturbo_inventory.refresh_from_db()
            re_calculate_all_inventory_transactions(shopturbo_inventory)

        shopturbo_inventory.refresh_from_db()

        save_custom_property(request, shopturbo_inventory)
        save_association_label(request, shopturbo_inventory, TYPE_OBJECT_INVENTORY)

    elif "delete-inventory" in request.POST:
        shopturbo_inventory.status = "archived"
        shopturbo_inventory.save(
            log_data={
                "user": request.user,
                "target": "commerce_inventory",
                "workspace": workspace,
            }
        )

    elif "restore-inventory" in request.POST:
        if has_quota(get_workspace(request.user), INVENTORY_USAGE_CATEGORY):
            shopturbo_inventory.status = "active"
            shopturbo_inventory.save(
                log_data={
                    "user": request.user,
                    "target": "commerce_inventory",
                    "workspace": workspace,
                }
            )

    elif "update_inventory_transactions" in request.POST:
        inventory_transaction_ids = request.POST.getlist("inventory_transactions", [])
        InventoryTransaction.objects.filter(inventory=shopturbo_inventory).update(
            inventory=None
        )

        if shopturbo_inventory and inventory_transaction_ids != [""]:
            InventoryTransaction.objects.filter(
                id__in=inventory_transaction_ids
            ).update(inventory=shopturbo_inventory)

    elif "update_items" in request.POST:
        item_id = request.POST.get("items", [])
        if item_id != [""]:
            shopturbo_inventory.item.clear()
            item_objs = ShopTurboItems.objects.filter(id=item_id)
            shopturbo_inventory.item.add(*item_objs)
        else:
            shopturbo_inventory.item.clear()

    elif "update_locations" in request.POST:
        location_id = request.POST.get("locations", None)
        shopturbo_inventory.warehouse = None

        if location_id:
            location_obj = InventoryWarehouse.objects.filter(id=location_id).first()
            shopturbo_inventory.warehouse = location_obj
        shopturbo_inventory.save()

    if source_url:
        return redirect(source_url)
    if module:
        return redirect(
            build_redirect_url(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={"module_slug": module, "object_slug": module_object_slug},
                ),
                view_id=view_id,
                id=id,
                page=page,
                target=target,
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
@require_POST
def manage_inventory_warehouse(request, id):
    workspace = get_workspace(request.user)
    target = "commerce_inventory_warehouse"
    source_url = request.POST.get("source_url")
    view_id = request.POST.get("view_id")
    module_slug = request.POST.get("module")
    page = request.POST.get("page", 1)
    if source_url:
        source_url = update_query_params_url(
            source_url,
            {
                "target": target,
                "warehouse_id": id,
                "page": page,
            },
        )

    inventory_warehouse = InventoryWarehouse.objects.get(workspace=workspace, id=id)
    if "update-warehouse" in request.POST:
        logger.debug(request.POST)

        warehouse_ = request.POST.get("warehouse")
        floor = request.POST.get("floor")
        zone = request.POST.get("zone")
        aisle = request.POST.get("aisle")
        rack = request.POST.get("rack")
        shelf = request.POST.get("shelf")
        bin = request.POST.get("bin")
        owner = request.POST.get("owner", None)

        location = ""
        if warehouse_:
            inventory_warehouse.warehouse = warehouse_
            location = "-" + warehouse_
        else:
            inventory_warehouse.warehouse = None
        if floor:
            inventory_warehouse.floor = floor
            location += "-" + floor
        else:
            inventory_warehouse.floor = None
        if zone:
            inventory_warehouse.zone = zone
            location += "-" + zone
        else:
            inventory_warehouse.zone = zone
        if aisle:
            inventory_warehouse.aisle = aisle
            location += "-" + aisle
        else:
            inventory_warehouse.aisle = None
        if rack:
            inventory_warehouse.rack = rack
            location += "-" + rack
        else:
            inventory_warehouse.rack = None
        if shelf:
            inventory_warehouse.shelf = shelf
            location += "-" + shelf
        else:
            inventory_warehouse.shelf = None
        if bin:
            inventory_warehouse.bin = bin
            location += "-" + bin
        else:
            inventory_warehouse.bin = None
        if location:
            if location[0] == "-":
                location = location[1:]
            inventory_warehouse.location = location

        assign_object_owner(
            inventory_warehouse, owner, request, TYPE_OBJECT_INVENTORY_WAREHOUSE
        )

        inventory_warehouse.save(
            log_data={"user": request.user, "workspace": workspace}
        )

        save_custom_property(request, inventory_warehouse)

    elif "toggle-warehouse" in request.POST:
        if inventory_warehouse.usage_status == "archived":
            if has_quota(get_workspace(request.user), WAREHOUSE_USAGE_CATEGORY):
                inventory_warehouse.usage_status = "active"
        else:
            inventory_warehouse.usage_status = "archived"
        inventory_warehouse.save(
            log_data={"user": request.user, "workspace": workspace}
        )

    elif "update_inventories" in request.POST:
        source = request.POST.get("source")
        object_id = request.POST.get("object_id")
        if source == TYPE_OBJECT_INVENTORY_WAREHOUSE:
            warehouse = InventoryWarehouse.objects.filter(id=object_id).first()
            inventory_ids = request.POST.getlist("inventories", [])
            ShopTurboInventory.objects.filter(warehouse=warehouse).update(
                warehouse=None
            )

            if warehouse:
                ShopTurboInventory.objects.filter(id__in=inventory_ids).update(
                    warehouse=warehouse
                )

    if source_url:
        return redirect(source_url)

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVENTORY_WAREHOUSE]
    if module_slug:
        return redirect(
            build_redirect_url(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                ),
                view_id=view_id,
                warehouse_id=id,
                target=target,
                page=page,
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
@require_GET
def create_transaction_form(request):
    workspace = get_workspace(request.user)
    transaction_type = request.GET.get("transaction_type")
    if transaction_type not in ["create", "adjust", "move"]:
        return HttpResponse(status=400)

    source_url = request.GET.get("source_url")
    module_slug = request.GET.get("module")
    items = request.GET.get("items", "")
    order_id = request.GET.get("order_id", "")
    purchase_id = request.GET.get("purchase_id", "")
    item = None
    now = timezone.now()
    context = {
        "item": item,
        "items": items,
        "order_id": order_id,
        "purchase_id": purchase_id,
        "current_time": now,
        "InventoryTransactionNameCustomField": InventoryTransactionNameCustomField.objects.filter(
            workspace=workspace
        ),
        "object_type": TYPE_OBJECT_INVENTORY_TRANSACTION,
        "source_url": source_url,
        "module": module_slug,
    }
    if transaction_type == "create":
        return render(
            request,
            "data/shopturbo/inventory-transaction/create-inventory-transaction-form.html",
            context,
        )
    elif transaction_type == "adjust":
        return render(
            request,
            "data/shopturbo/inventory-transaction/adjust-inventory-form.html",
            context,
        )
    else:
        return render(
            request,
            "data/shopturbo/inventory-transaction/move-inventory-form.html",
            context,
        )


@login_or_hubspot_required
def inventory_detail(request, id):
    workspace = get_workspace(request.user)

    inventory = (
        ShopTurboInventory.objects.filter(id=id)
        .select_related("warehouse")
        .prefetch_related(
            "item",
            Prefetch(
                "shopturbo_inventory_custom_field_relations",
                queryset=ShopTurboInventoryValueCustomField.objects.select_related(
                    "field_name"
                ),
            ),
        )
        .first()
    )

    view_id = request.GET.get("view_id", None)
    target = TYPE_OBJECT_INVENTORY
    if view_id:
        view = View.objects.filter(id=view_id).first()
    else:
        # Create Main View
        view, _ = View.objects.get_or_create(
            workspace=workspace, title__isnull=True, target=target
        )
        view_id = view.id

    shopturbo_inventory_columns = request.GET.get("shopturbo_inventory_columns", [])
    shopturbo_inventory_columns = ast.literal_eval(shopturbo_inventory_columns)

    # DEBUG: Add logging to understand the column structure
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f"DEBUG inventory_detail: inventory_id={id}")
    logger.info(
        f"DEBUG inventory_detail: shopturbo_inventory_columns={shopturbo_inventory_columns}"
    )
    logger.info(
        f"DEBUG inventory_detail: columns count={len(shopturbo_inventory_columns)}"
    )

    context = {
        "inventory": inventory,
        "shopturbo_inventory_columns": shopturbo_inventory_columns,
        "view_id": view_id,
        "page": request.GET.get("page", 1),
        "module": request.GET.get("module", None),
    }
    return render(request, "data/shopturbo/inventory-row-partial-loop.html", context)


def inventory_association_drawer(request):
    if request.method == "GET":
        source = request.GET.get("source", None)
        object_id = request.GET.get("object_id", None)
        page = request.GET.get("page", 1)
        view_id = request.GET.get("view_id", None)
        module = request.GET.get("module", None)
        object_type = request.GET.get("object_type", None)
        order_id = request.GET.get("order_id", None)
        purchase_id = request.GET.get("purchase_id", None)
        items = request.GET.get("items", None)
        from_object = request.GET.get("from", None)

        logger.debug("[DEBUG] module inventory_association", module)

        if source == TYPE_OBJECT_INVENTORY_WAREHOUSE:
            obj = None
            warehouse = InventoryWarehouse.objects.get(id=object_id)
            inventory_objs = ShopTurboInventory.objects.filter(warehouse=warehouse)
        elif source == TYPE_OBJECT_ITEM:
            obj = None
            item = ShopTurboItems.objects.get(id=object_id)
            inventory_objs = ShopTurboInventory.objects.filter(item=item)
        elif source == TYPE_OBJECT_PURCHASE_ORDER:
            obj = PurchaseOrders.objects.get(id=object_id)
        elif source == TYPE_OBJECT_INVENTORY_TRANSACTION:
            obj = InventoryTransaction.objects.get(id=object_id)
        elif source == TYPE_OBJECT_INVENTORY:
            obj = None
            transaction_objs = None
            inventory_obj = ShopTurboInventory.objects.get(id=object_id)
            if object_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
                transaction_objs = InventoryTransaction.objects.filter(
                    inventory=inventory_obj
                )
            if object_type == TYPE_OBJECT_ITEM:
                obj = inventory_obj
            if object_type == TYPE_OBJECT_INVENTORY_WAREHOUSE:
                obj = inventory_obj
        elif source == TYPE_OBJECT_SUBSCRIPTION:
            obj = ShopTurboSubscriptions.objects.get(id=object_id)
        elif source == TYPE_OBJECT_ORDER:
            obj = ShopTurboOrders.objects.get(id=object_id)
        else:
            return HttpResponse(status=400)

        context = {
            "source": source,
            "object_id": object_id,
            "page": page,
            "view_id": view_id,
            "obj": obj,
            "module": module,
        }

        if from_object:
            context["from"] = from_object

        if source in [TYPE_OBJECT_ITEM, TYPE_OBJECT_INVENTORY_WAREHOUSE]:
            context["inventory_objs"] = inventory_objs

        if (
            source == TYPE_OBJECT_INVENTORY
            and object_type == TYPE_OBJECT_INVENTORY_TRANSACTION
        ):
            context["transaction_objs"] = transaction_objs

        if object_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
            context["order_id"] = order_id
            context["purchase_id"] = purchase_id
            try:
                context["items"] = ast.literal_eval(items)
            except:
                context["items"] = items

            return render(
                request,
                "data/association/default-create-add/inventory-transaction.html",
                context,
            )
        elif object_type == TYPE_OBJECT_ITEM:
            return render(
                request, "data/association/default-create-add/item.html", context
            )

        elif object_type == TYPE_OBJECT_INVENTORY_WAREHOUSE:
            return render(
                request, "data/association/default-create-add/location.html", context
            )

        return render(
            request, "data/association/default-create-add/inventory.html", context
        )
    else:
        return HttpResponse(200)


@login_or_hubspot_required
def get_inventory_object_options(request):
    workspace = get_workspace(request.user)
    q = request.GET.get("q")
    page = request.GET.get("page", 1)

    filter_conditions = Q(workspace=workspace, status="active")
    if q:
        q_id = q
        try:
            if q[0] == "#" or q[0] == "0":
                q_id = int(q[1:].replace(" ", ""))
        except Exception as e:
            logger.debug(e)
        filter_conditions &= Q(inventory_id__icontains=q_id)
    inventory_objs = ShopTurboInventory.objects.filter(filter_conditions).order_by(
        "-inventory_id"
    )

    res = []
    ITEMS_PER_PAGE = 30
    inventories_paginator = Paginator(inventory_objs, ITEMS_PER_PAGE)
    paginated_inventories = []
    more_pagination = False
    if page:
        try:
            inventory_page_content = inventories_paginator.page(page if page else 1)
            paginated_inventories = inventory_page_content.object_list
            more_pagination = inventory_page_content.has_next()
        except EmptyPage:
            pass

    for inventory in paginated_inventories:
        inventory_dict = {
            "id": str(inventory.id),
            "text": f"#{inventory.inventory_id:04d} | {inventory.name}",
        }

        res.append(inventory_dict)

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


def apply_inventory_search_setting(
    page_group_type,
    search_key,
    search_value,
    view_filter=None,
    is_uuid_key=False,
    force_filter_list=[],
):
    # Log search setting application details for inventory
    logger.info(
        "apply_inventory_search_setting called - page_group_type: %s, search_key: %s, search_value: %s, view_filter_view_id: %s, is_uuid_key: %s",
        page_group_type,
        search_key,
        search_value,
        str(view_filter.view.id) if view_filter and view_filter.view else None,
        is_uuid_key,
    )

    dummy_view_filter = ViewFilter(
        view=view_filter.view if view_filter else None,
        filter_value={search_key: {"key": "contains", "value": search_value}},
    )

    logger.debug(
        "Created dummy ViewFilter for inventory - filter_value: %s, force_filter_list: %s",
        dummy_view_filter.filter_value,
        force_filter_list,
    )

    result = build_view_filter(
        Q(), dummy_view_filter, page_group_type, force_filter_list=force_filter_list
    )

    logger.debug("build_view_filter result for inventory - query_str: %s", str(result))

    return result


def export_items(request):  # use in workflow
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")

        action_node_id = request.GET.get("action_node_id")
        at_id = request.GET.get("action_tracker_id")
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                logger.debug("ActionTracker does not exist")
                return HttpResponse(status=404)

        node = ActionNode.objects.filter(id=action_node_id).first()
        node.valid_to_run = True
        node.save()

        item_ids = ShopTurboItems.objects.filter(
            workspace=workspace, status="active"
        ).values_list("item_id", flat=True)

        platforms = ["rakuten", "yahoo-shopping", "amazon", "makeshop"]

        channels = Channel.objects.filter(
            Q(workspace=get_workspace(request.user), integration__slug__in=platforms)
        ).exclude(status="draft")

        for name in EXCLUDE_SYNC_CHANNEL_NAME:
            channels = channels.exclude(name__icontains=name)
        # count_items = ShopTurboItems.objects.filter(
        #     workspace=workspace, status="active"
        # ).count()
        item_filters = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace, type="choice"
        )

        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ITEM
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        history = TransferHistory.objects.filter(workspace=workspace).order_by(
            "-created_at"
        )[0:10]
        if lang == "ja":
            task_status = dict(TASK_STATUS_JA)
        else:
            task_status = dict(TASK_STATUS)

        # columns = DEFAULT_COLUMNS_ITEM.copy()
        # columns = [col for col in columns if col in {'name'}]

        all_columns = get_list_view_columns(TYPE_OBJECT_ITEM, workspace)

        columns = []
        itemsnamecustomfield = (
            ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, unique=True
            )
            .exclude(type__in=["image", "user", "formula"])
            .values_list("name", flat=True)
        )
        columns.extend(itemsnamecustomfield)
        item_all_filters = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace
        )

        all_channels = Channel.objects.filter(workspace=workspace).exclude(
            status="draft"
        )
        distinct_slugs = {
            channel.integration.slug
            for channel in all_channels
            if channel.integration.slug != "hubspot"
        }
        additional_action_channels = Channel.objects.filter(
            workspace=workspace, integration__slug="hubspot"
        ).exclude(status="draft")

        context = {
            "node": node,
            "channels": channels,
            "filters": item_filters,
            "object_type": TYPE_OBJECT_ITEM,
            "menu_key": module_slug,
            "history": history,
            "task_status": task_status,
            "import_export_type": "export",
            "columns": columns,
            "all_columns": all_columns,
            "distinct_slugs": distinct_slugs,
            "nextengine_filters": item_all_filters,
            "additional_action_channels": additional_action_channels,
            "action_index": action_index,
            "view_page": "items",
        }

        if node.input_data:
            context["active_channel"] = node.input_data.get("channel", None)
            context["active_set_contact_as_company"] = node.input_data.get(
                "set-contact-as-company", None
            )
            context["active_nextengine_hubspot_filter"] = node.input_data.get(
                "nextengine_hubspot_filter", None
            )
            context["active_additional_action_channel_choice"] = node.input_data.get(
                "additional-action-channel-choice", None
            )
            context["active_filter_output"] = node.input_data.get("filter-output", None)
            context["active_hubspot_export_platform_match"] = node.input_data.get(
                "hubspot-export-platform-match", None
            )
            context["active_update_inventory_toggle"] = node.input_data.get(
                "update-inventory-toggle", None
            )
            context["active_filter_dictionary"] = node.input_data.get(
                "filter_dictionary", None
            )

        return render(request, "data/shopturbo/export-items-action.html", context)

    elif request.method == "POST":
        logger.debug(request.POST)
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ITEM]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ITEM
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        action_index = request.POST.get("action_index")
        action_node_id = request.POST.get("action_node_id")
        at_id = request.POST.get("action_tracker_id")
        wat_id = request.POST.get("workflow_action_tracker_id")
        node = None

        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                logger.debug("ActionNode does not exist")
                return HttpResponse(status=404)
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                logger.debug("ActionTracker does not exist")
                return HttpResponse(status=404)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                logger.debug("WorkflowActionTracker does not exist")
                return HttpResponse(status=404)

        postfix = ""
        if action_index:
            postfix = "-" + action_index
        action_name = request.POST.get("action_name" + postfix)

        item_file_columns = request.POST.getlist("item-file-column" + postfix, [])
        item_sanka_properties = request.POST.getlist(
            "sanka-item-properties" + postfix, []
        )
        item_ignores = request.POST.getlist("item-ignore" + postfix, [])

        mapping_custom_fields = {}
        item_mapping = {}

        for idx, file_column in enumerate(item_file_columns):
            # check ignore
            item_mapping[file_column] = item_sanka_properties[idx]
            if item_ignores[idx] == "True":
                continue
            mapping_custom_fields[file_column] = item_sanka_properties[idx]

        if "save_item_mapping" in request.POST:
            platform = request.POST.get(
                "platform", request.POST.get("select_integration_ids")
            )
            mapping, _ = ShopTurboItemsMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )

            data = {}
            for i, field in enumerate(item_mapping):
                field_data = {"value": item_mapping[field], "skip": item_ignores[i]}
                data[field] = field_data

            mapping.input_data = data
            mapping.save()

        else:
            action_node_id = request.POST.get("action_node_id")
            node = ActionNode.objects.filter(id=action_node_id).first()
            node.valid_to_run = True

            if request.POST.get("submit-option") == "save":  # Action Workflow
                platform = request.POST.get(
                    "platform",
                )
                integration = request.POST.get("select_integration_ids" + postfix)
                update_inventory = request.POST.get("update_inventory" + postfix)

                channel = Channel.objects.get(id=integration)
                if channel:
                    if request.POST.get("set-contact-as-company"):
                        channel.ms_refresh_token = 1
                        channel.save()
                    else:
                        channel.ms_refresh_token = None
                        channel.save()

                filter_dictionary = {}
                filter_types = request.POST.getlist("filter_type", None)
                filter_options = request.POST.getlist("filter_options", None)
                filter_values = request.POST.getlist("filter_value", None)
                # Handle "between" operation (have two filter values)
                filter_values2 = request.POST.getlist("filter_value-2", None)
                idx_filter_values2 = 0
                for idx, filter_type in enumerate(filter_types):
                    value = filter_values[idx]
                    if filter_options[idx] == "between":
                        try:
                            value = f"{filter_values[idx]}-{filter_values2[idx_filter_values2]}"
                        except:
                            value = ""
                        idx_filter_values2 += 1

                    if (
                        str(filter_type) not in filter_dictionary
                        and filter_types.count(str(filter_type)) > 1
                    ):
                        filter_dictionary[str(filter_type)] = {
                            "key": [filter_options[idx]],
                            "value": [value],
                        }
                    elif str(filter_type) in filter_dictionary:
                        filter_dictionary[str(filter_type)]["key"].append(
                            filter_options[idx]
                        )
                        filter_dictionary[str(filter_type)]["value"].append(value)
                    else:
                        filter_dictionary[str(filter_type)] = {
                            "key": filter_options[idx],
                            "value": value,
                        }

                input_data = {
                    "channel": integration,
                    "additional-action-channel-choice": request.POST.get(
                        "additional-action-channel-choice", None
                    ),
                    "filter-output": request.POST.get("filter-output", None),
                    "action_name": action_name,
                    "update-inventory-toggle": update_inventory,
                    "filter_dictionary": filter_dictionary,
                }

                node.input_data = input_data
                node.save()

                mapping, _ = ShopTurboItemsMappingFields.objects.get_or_create(
                    workspace=workspace, platform=channel.integration.slug
                )

                data = {}
                for i, field in enumerate(item_mapping):
                    field_data = {"value": item_mapping[field], "skip": item_ignores[i]}
                    data[field] = field_data

                mapping.input_data = data
                mapping.save()
            else:
                update_inventory_toggle = request.POST.get(
                    "update_inventory" + postfix, None
                )

                filter_id = request.POST.get("filter", None)

                item_ids = ShopTurboItems.objects.filter(
                    workspace=workspace, status="active"
                ).values_list("id", flat=True)
                channel = Channel.objects.get(id=node.input_data.get("channel"))

                filter_items = None
                if filter_id:
                    cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                        id=filter_id
                    ).first()
                    if cf_filter:
                        value = request.POST.get("filter-choice", None)
                        if value:
                            cf_value_items = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    field_name=cf_filter, value=value
                                )
                            )
                            if cf_value_items:
                                filter_items = cf_value_items.values_list(
                                    "items", flat=True
                                )
                                filter_items = filter_items.distinct()
                logger.debug(
                    "=== shopturbo.py -- 322 Sync item for filter_items", filter_items
                )

                filter_dictionary = {}
                filter_types = request.POST.getlist("filter_type", None)
                filter_options = request.POST.getlist("filter_options", None)
                filter_values = request.POST.getlist("filter_value", None)
                for idx, filter_type in enumerate(filter_types):
                    filter_value = filter_values[idx]

                    if "id" in filter_type:
                        try:
                            value = str(filter_value).split(",")[0]
                            _ = uuid.UUID(str(value))
                            filter_type = "id"
                        except (ValueError, TypeError):
                            pass

                    filter_dictionary[str(filter_type)] = {
                        "key": filter_options[idx],
                        "value": filter_value,
                    }

                filter_conditions = Q(workspace=workspace, status="active")

                if item_ids:
                    filter_conditions &= Q(id__in=item_ids)

                view, _ = View.objects.get_or_create(
                    workspace=workspace, title__isnull=True, target=TYPE_OBJECT_ITEM
                )

                view_filter, is_new_view_filter = ViewFilter.objects.get_or_create(
                    view=view
                )
                if is_new_view_filter:
                    view_filter.view_type = "list"
                    view_filter.save()
                else:
                    view_filter.save()

                temp_view_filter = view_filter.filter_value
                view_filter.filter_value = filter_dictionary
                view_filter.save()
                view.save()

                filter_conditions = apply_shopturboitems_view_filter(
                    filter_conditions, view_filter, request=request
                )
                logger.debug(
                    "=== items.py -- 322 Sync item for filter_dictionary",
                    filter_dictionary,
                )

                view_filter.filter_value = temp_view_filter
                view_filter.save()
                view.save()

                items = ShopTurboItems.objects.filter(filter_conditions)

                inventories = ShopTurboInventory.objects.filter(
                    item__in=items, workspace=workspace
                )

                if filter_items:
                    if request.POST.get("filter-type") == "exclude":
                        items = items.exclude(id__in=filter_items)
                        inventories = inventories.exclude(item__in=items)
                    else:
                        items = items.filter(id__in=filter_items)
                        inventories = inventories.filter(item__in=items)

                items = list(items.values_list("id", flat=True))

                mapping = ShopTurboItemsMappingFields.objects.filter(
                    workspace=workspace,
                    platform=channels[0].integration.slug,
                    mapping_type__isnull=True,
                ).first()
                if channel:
                    if channel.integration.slug == "rakuten":
                        push_rakuten_items(
                            request,
                            mapping.input_data,
                            item_ids,
                            channel.id,
                            update_inventory_toggle,
                        )
                    elif channel.integration.slug == "amazon":
                        push_amazon_items(
                            item_ids,
                            channel.id,
                            mapping.input_data,
                            update_inventory_toggle,
                        )
                    elif channel.integration.slug == "makeshop":
                        push_makeshop_items(
                            item_ids,
                            channel.id,
                            mapping.input_data,
                            update_inventory_toggle,
                        )
                at.status = "success"
                at.completed_at = timezone.now()
                at.save()

                next_node = None
                if node:
                    next_node = node.next_node
                    at = ActionTracker.objects.filter(id=at.id).first()
                if next_node:
                    trigger_next_action(
                        current_action_tracker=at,
                        workflow_action_tracker=wat,
                        current_node=node,
                        user_id=str(request.user.id),
                        lang=lang,
                    )

                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
                    )

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
    return redirect(reverse("main", host="app"))


def get_export_import_item_configuration(request):
    workspace = get_workspace(request.user)
    platform = request.GET.get("platform", "")
    object_type = request.GET.get("object_type")
    export_import_configuration = ExportImportConfiguration.objects.filter(
        workspace=workspace,
        object_type=object_type,
        platform=platform,
    ).first()
    return (
        JsonResponse(export_import_configuration.input_data, safe=False)
        if export_import_configuration
        else JsonResponse({}, safe=False)
    )
