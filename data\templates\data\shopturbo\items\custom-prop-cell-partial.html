{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}

{% if row_type in customfields_map_id %}
    {% with CustomFieldName=customfields_map_id|get_attr:row_type %}
        {% if CustomFieldName.type == 'formula' %}
            <div class="d-none"
                hx-post="{% url 'get_formula_result' %}"
                hx-vals='{"obj_id":"{{item.id}}","custom_field_id":"{{CustomFieldName.id}}","object_type":"{{constant.TYPE_OBJECT_ITEM}}"}'
                hx-target="#shopturbo-formula-value-{{item.id}}-{{CustomFieldName.id}}"
                hx-indicator=".loading-spinner"
                hx-trigger="load"
                hx-swap="innerHTML"
            ></div>

            <div class="loading-spinner ms-2 mb-2">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <div id="shopturbo-formula-value-{{item.id}}-{{CustomFieldName.id}}" ></div>
        {% endif %}
    {% endwith %}
{% endif %}
{% for custom_field in item.shopturbo_item_custom_field_relations.all %}
    {% if custom_field.field_name.id|stringify == row_type|split:'|'|first %}
        {% if custom_field.field_name.type == 'formula' %}
            {% comment %} htmx {% endcomment %}
            <div class="d-none"
                hx-post="{% url 'shopturbo_item_api_formula' %}"
                hx-vals = '{"item_id":"{{item.id}}", "custom_field.field_name_id":"{{custom_field.field_name.id}}" }'
                hx-target="#shopturbo-formula-value-{{item.id}}-{{custom_field.field_name.id}}"
                hx-indicator=".loading-spinner"
                hx-trigger="load"
            ></div>
            <div id="shopturbo-formula-value-{{item.id}}-{{custom_field.field_name.id}}" ></div>
        {% endif %}
    {% endif %}

    {% if custom_field.field_name.id|stringify == row_type|split:'|'|first %}
        {% if custom_field.field_name.type == 'image_group' %}
            <div class="ms-2">
                <div class="d-flex my-4" >
                    {% for custom_field_value_file in custom_field.shopturboitemsvaluefile_set.all|dictsortreversed:"created_at" %}
                        {% if forloop.counter < 4 %}
                            <a href="{{custom_field_value_file.file.url}}" class="me-2">
                                <img width="auto" height="30" src="{{custom_field_value_file.file.url}}" />
                            </a>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        {% elif custom_field.field_name.type == 'number' %}
            <div class="fw-bold">

                {% if custom_field.field_name.number_format == '%' %}
                    {{ custom_field.value }} {{custom_field.field_name.number_format|upper}}
                {% elif custom_field.field_name.number_format == 'number' %}
                    {{ custom_field.value }}
                {% elif custom_field.value is not None and custom_field.value != '' %}
                    {{ custom_field.field_name.number_format|upper|get_currency_symbol }} {{ custom_field.value|use_thousand_separator_string_with_currency:custom_field.field_name.number_format }}
                {% endif %}
            </div>
        {% elif custom_field.field_name.type == 'image' %}
            {% for custom_field_value_file in custom_field.shopturboitemsvaluefile_set.all|dictsortreversed:"created_at" %}
                {% if forloop.counter < 2 %}
                <div class="w-100">
                    <a href="{{custom_field_value_file.file.url}}" class="me-2">
                        <img class="mw-200px min-h-120px" src="{{custom_field_value_file.file.url}}" />
                    </a>
                </div>
                {% endif %}
            {% endfor %}
        {% elif custom_field.field_name.type == 'user' %}

            {% if custom_field.value %}
                <div id="cf-user-{{item.id}}"
                    hx-get="{% host_url 'shopturbo_item_row_column_detail' host 'app' %}"
                    hx-vals='{"column_type": "{{custom_field.field_name.type}}", "column_value": "{{custom_field.value}}"}'
                    hx-trigger="load"
                    hx-indicator=".cf_user_row_load-{{item.id}}">
                    <div class="d-flex justify-content-center tw-w-[125px]">
                        {% comment %} <span class="">{% if LANGUAGE_CODE == 'ja' %}ユーザー{% else %}User{% endif %}</span> {% endcomment %}
                        <style>
                            /* Styles for the spinner */
                            .cf_user_row_load-{{item.id}} {
                                display: none; /* Initially hidden */
                            }
                            .htmx-request .cf_user_row_load-{{item.id}},
                            .htmx-request.cf_user_row_load-{{item.id}} {
                                display: inline-block; /* Display during htmx request */
                            }
                        </style>
                        <!-- Spinner icon -->
                        <span class="spinner-border spinner-border-lg text-secondary cf_user_row_load-{{item.id}}" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </span>
                    </div>
                </div>
            {% endif %}
        {% elif custom_field.field_name.type == 'date' %}
            <div class="fw-bold">

                {% if custom_field.value_time %}
                    {% date_format custom_field.value_time %}
                {% endif %}

            </div>
        {% elif custom_field.field_name.type == 'date_time' %}
            <div class="fw-bold">
                {% if custom_field.value_time %}
                    {% date_format custom_field.value_time 1 %}
                {% endif %}
            </div>
        {% elif custom_field.field_name.type == 'contact' %}
            {% if custom_field.value|is_uuid %}
                <div id="cf-contact-{{item.id}}"
                    hx-get="{% host_url 'shopturbo_item_row_column_detail' host 'app' %}"
                    hx-vals='{"column_type": "{{custom_field.field_name.type}}", "column_value": "{{custom_field.value}}"}'
                    hx-trigger="load"
                    hx-indicator=".cf_contact_row_load-{{item.id}}">
                    <div class="d-flex justify-content-center tw-w-[125px]">
                        <span class="">{% if LANGUAGE_CODE == 'ja' %}連絡先{% else %}Contact{% endif %}</span>
                        <style>
                            /* Styles for the spinner */
                            .cf_contact_row_load-{{item.id}} {
                                display: none; /* Initially hidden */
                            }
                            .htmx-request .cf_contact_row_load-{{item.id}},
                            .htmx-request.cf_contact_row_load-{{item.id}} {
                                display: inline-block; /* Display during htmx request */
                            }
                        </style>
                        <!-- Spinner icon -->
                        <span class="spinner-border spinner-border-lg text-secondary cf_contact_row_load-{{item.id}}" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </span>
                    </div>
                </div>
            {% endif %}
        {% elif custom_field.field_name.type == 'company' %}
            {% if custom_field.value|is_uuid %}
                <div id="cf-company-{{item.id}}"
                    hx-get="{% host_url 'shopturbo_item_row_column_detail' host 'app' %}"
                    hx-vals='{"column_type": "{{custom_field.field_name.type}}", "column_value": "{{custom_field.value}}"}'
                    hx-trigger="load"
                    hx-indicator=".cf_company_row_load-{{item.id}}">
                    <div class="d-flex justify-content-center tw-w-[125px]">
                        <span class="">{% if LANGUAGE_CODE == 'ja' %}企業{% else %}Company{% endif %}</span>
                        <style>
                            /* Styles for the spinner */
                            .cf_company_row_load-{{item.id}} {
                                display: none; /* Initially hidden */
                            }
                            .htmx-request .cf_company_row_load-{{item.id}},
                            .htmx-request.cf_company_row_load-{{item.id}} {
                                display: inline-block; /* Display during htmx request */
                            }
                        </style>
                        <!-- Spinner icon -->
                        <span class="spinner-border spinner-border-lg text-secondary cf_company_row_load-{{item.id}}" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </span>
                    </div>
                </div>
            {% endif %}
        {% elif custom_field.field_name.type == 'invoice_objects' %}
            {% if custom_field.value|is_uuid %}
                <div id="cf-invoice-{{item.id}}"
                    hx-get="{% host_url 'shopturbo_item_row_column_detail' host 'app' %}"
                    hx-vals='{"column_type": "{{custom_field.field_name.type}}", "column_value": "{{custom_field.value}}"}'
                    hx-trigger="load"
                    hx-indicator=".cf_invoice_row_load-{{item.id}}">
                    <div class="d-flex justify-content-center tw-w-[125px]">
                        <span class="">{% if LANGUAGE_CODE == 'ja' %}売上請求書{% else %}Invoices{% endif %}</span>
                        <style>
                            /* Styles for the spinner */
                            .cf_invoice_row_load-{{item.id}} {
                                display: none; /* Initially hidden */
                            }
                            .htmx-request .cf_invoice_row_load-{{item.id}},
                            .htmx-request.cf_invoice_row_load-{{item.id}} {
                                display: inline-block; /* Display during htmx request */
                            }
                        </style>
                        <!-- Spinner icon -->
                        <span class="spinner-border spinner-border-lg text-secondary cf_invoice_row_load-{{item.id}}" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </span>
                    </div>
                </div>
            {% endif %}

        {% elif custom_field.field_name.type == 'choice' %}
            {% for choice in custom_field.field_name.choice_value|string_list_to_list %}
                {% if ';' in custom_field.value and custom_field.field_name.multiple_select %}
                    {% for val in custom_field.value|split:";" %}
                        {% if choice.value == val %}
                            {% if custom_field.field_name.show_badge %}
                            <div class="fw-bold">
                                <span class="d-inline-flex align-items-center">
                                    <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                                    {{ choice.label }}
                                </span>
                             </div>
                            {% else %}
                            <div class="fw-bold">
                                {{ choice.label }}
                            </div>
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                {% else %}
                    {% if choice.value == custom_field.value %}
                        <div class="fw-bold">
                            {% if custom_field.field_name.show_badge %}
                            <span class="d-inline-flex align-items-center">
                                <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                                {{ choice.label }}
                            </span>
                            {% else %}
                            {{ choice.label }}
                            {% endif %}
                        </div>
                    {% endif %}
                {% endif %}
            {% endfor %}
        {% elif custom_field.field_name.type == 'svg' %}
            <div class="w-full">
                <div class="tw-max-h-[100px] svg-container">
                    {% if custom_field.value %}
                        {{custom_field.value|safe}}
                    {% endif %}
                </div>
            </div>
        {% elif custom_field.field_name.type == 'tag' %}
            <div class="">
                {% for tag in custom_field.value|get_json %}
                <div class="fw-bold mb-1">
                    <span class="badge bg-gray-500">{{tag.value}}</span>
                </div>
                {% endfor %}
            </div>
        {% elif custom_field.field_name.type == 'components' %}
            <div class="fw-bold mw-sm-500px">
                {% for component in custom_field.components.all %}
                    <div>
                        <a id="profile_wizard_button" class="{% include "data/utility/table-link.html" %} manage-full-wizard-button"
                            hx-get="{% url 'shopturbo_load_drawer' %}"
                            hx-vals = '{"drawer_type":"item-manage", "item_id":"{{component.item_component.id}}", "view_id":"{{view_id}}" }'
                            hx-target="#manage-full-drawer-content"
                            hx-indicator=".loading-drawer-spinner"
                            hx-trigger="click"
                            >
                            {% get_object_display component.item_component 'commerce_items' %}
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% elif custom_field.field_name.type == 'process_master' %}
            <div class="fw-bold">
                {% with process_master=custom_field.value|to_list %}
                    {% if process_master %}
                        {% for process in process_master %}
                        {{process.label}} : {{process.value}} <br>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
        {% else %}
            <div class="fw-bold">
                {% include "data/common/custom_field/row-partial-list.html" with CustomFieldName=custom_field.field_name obj_id=custom_field.value value=custom_field.value %}
            </div>
        {% endif %}
    {% endif %}
{% endfor %}