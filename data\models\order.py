import ast
import uuid

import requests
from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone
from googletrans import Translator

from data.constants.constant import *
from data.models import ObjectManager
from data.models.base import (
    CustomQuerySet,
    save_tags_values,
    validate_value_custom_field,
)
from data.models.constant import *

SHOPTURBO_ORDER_STATUS = [
    ("active", "Active"),
    ("completed", "Completed"),
    ("archived", "Archived"),
    ("canceled", "Canceled"),
    ("draft", "Draft"),
]
SHOPTURBO_ORDER_TYPE = [
    ("manual_order", "Manual Order"),
    ("item_order", "Item Order"),
]

SHOPTURBO_ORDER_PLATFORM = [
    ("sanka", "SANKA"),
    ("shopify", "Shopify"),
    ("square", "Square"),
    ("amazon", "Amazon"),
    ("other", "Other"),
]

SHOPTURBO_ORDER_DELIVERY_STATUS = [
    ("draft", "Draft"),
    ("order_received", "Order Received"),
    ("order_delivered", "Order Delivered"),
    ("invoice_created", "Invoice Sent"),
    ("payment_received", "Payment Received"),
    ("cancellation_processed", "Cancellation Processed"),
]


class ShopTurboOrdersManager(models.Manager):
    def get_queryset(self):
        return CustomQuerySet(self.model, using=self._db)

    def paginate(self, page_number, page_size):
        return self.get_queryset().paginate(page_number, page_size)


class ShopTurboOrdersNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    taxes = models.CharField(max_length=800, null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    is_association = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    sub_property = models.TextField(null=True, blank=True)
    source_property = models.TextField(null=True, blank=True)
    pair_property = models.TextField(null=True, blank=True)
    sync_conditions = models.TextField(null=True, blank=True)

    class Meta:
        indexes = [
            # Most common query: filter by workspace and name__isnull=False, order by 'order'
            models.Index(
                fields=["workspace", "name", "order"], name="sto_ncf_ws_name_order_idx"
            ),
            # Alternative ordering by created_at
            models.Index(
                fields=["workspace", "name", "-created_at"],
                name="sto_ncf_ws_name_created_idx",
            ),
            # Filter by workspace and is_association
            models.Index(
                fields=["workspace", "is_association"], name="sto_ncf_ws_assoc_idx"
            ),
            # General workspace filtering (most common)
            models.Index(fields=["workspace"], name="sto_ncf_ws_idx"),
        ]

    def __str__(self):
        return (
            str(self.workspace.name)
            + " - "
            + str(self.type)
            + " - "
            + str(self.name)
            + " - "
            + str(self.id)
        )


class ShopTurboOrders(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    company = models.ForeignKey(
        "Company", on_delete=models.SET_NULL, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, null=True, blank=True
    )
    companies = models.ManyToManyField("Company", blank=True, related_name="companies")
    contacts = models.ManyToManyField("Contact", blank=True, related_name="contacts")
    subscription = models.ForeignKey(
        "ShopTurboSubscriptions", on_delete=models.SET_NULL, null=True, blank=True
    )
    number_item = models.IntegerField(null=True, blank=True)
    item_price_order = models.FloatField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    tax = models.FloatField(null=True, blank=True)
    tax_applied_to = models.CharField(max_length=200, null=True, blank=True)
    discounts = models.ForeignKey(
        "ShopTurboItemsDiscount",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    discount_tax_option = models.CharField(
        max_length=512, null=False, blank=True, default=""
    )
    cost_option = models.CharField(max_length=512, null=True, blank=True)

    shipping_cost = models.ForeignKey(
        "ShopTurboShippingCost",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    shipping_cost_tax_status = models.CharField(max_length=200, null=True, blank=True)

    order_type = models.CharField(
        choices=SHOPTURBO_ORDER_TYPE, max_length=30, null=True, blank=True
    )
    status = models.CharField(
        choices=SHOPTURBO_ORDER_STATUS,
        max_length=30,
        null=True,
        blank=True,
        default="active",
    )
    delivery_status = models.CharField(
        choices=SHOPTURBO_ORDER_DELIVERY_STATUS, max_length=30, null=True, blank=True
    )

    # Association
    inventory_transactions = models.ManyToManyField("InventoryTransaction", blank=True)
    invoice = models.ManyToManyField("Invoice", blank=True)
    receipt = models.ManyToManyField("Receipt", blank=True)
    estimate = models.ManyToManyField("Estimate", blank=True)
    purchase_orders = models.ManyToManyField("PurchaseOrders", blank=True)
    cases = models.ManyToManyField("Deals", blank=True, related_name="orders")
    item = models.ManyToManyField("ShopTurboItems", blank=True)
    deliveryslip = models.ManyToManyField("DeliverySlip", blank=True)

    line_item_type = models.CharField(
        max_length=200, null=True, blank=True, default="record_based_task"
    )
    tax_inclusive = models.BooleanField(null=True, blank=True, default=False)

    order_at = models.DateTimeField(default=timezone.now)
    platform = models.CharField(
        choices=SHOPTURBO_ORDER_PLATFORM, max_length=30, null=True, blank=True
    )
    platform_payment_link = models.TextField(blank=True, null=True)
    platform_payment_id = models.CharField(max_length=500, null=True, blank=True)
    platform_payment_postal_code_field = models.CharField(
        max_length=50, null=True, blank=True
    )
    platform_payment_expires_at = models.IntegerField(null=True, blank=True)
    platform_payment_channel_id = models.CharField(max_length=50, null=True, blank=True)
    platform_payment_allow_shipping_fee = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    property_set = models.ForeignKey(
        "PropertySet", on_delete=models.SET_NULL, null=True, blank=True
    )
    kanban_order = models.IntegerField(null=True, blank=True)
    memo = models.TextField(null=True, blank=True)

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if not self.order_id:
            # Generate a new order_id if it doesn't exist
            last_order = (
                ShopTurboOrders.objects.filter(workspace=self.workspace)
                .order_by("-order_id")
                .first()
            )
            if last_order:
                if last_order.order_id:
                    try:
                        last_order_id = int(last_order.order_id)
                    except:
                        last_order_id = 0

                    next_order_id = last_order_id + 1
                else:
                    next_order_id = 1
            else:
                # If it's the first order, start with 1
                next_order_id = 1

            self.order_id = next_order_id

        try:
            if self.contact:
                self.contacts.add(self.contact)
            if self.company:
                self.companies.add(self.company)
        except:
            pass

        try:  # Property Sync order
            property_sync_fields = ShopTurboOrdersNameCustomField.objects.filter(
                workspace=self.workspace,
                type="property_sync",
                source_property__isnull=False,
            )

            if property_sync_fields:
                for property_sync_field in property_sync_fields:
                    source_property = property_sync_field.source_property
                    pair_property = property_sync_field.pair_property
                    sync_conditions = property_sync_field.sync_conditions
                    source_property_value = getattr(self, source_property)

                    if pair_property:
                        pair = ShopTurboOrdersNameCustomField.objects.get(
                            id=pair_property
                        )
                    else:
                        pair = property_sync_field
                    if sync_conditions:
                        sync_conditions = ast.literal_eval(sync_conditions)
                        custom_field_value, _ = (
                            ShopTurboOrdersValueCustomField.objects.get_or_create(
                                field_name=pair, orders=self
                            )
                        )
                        for sync_condition in sync_conditions:
                            if sync_condition[0] == "is":
                                if sync_condition[1] == source_property_value:
                                    custom_field_value.value = sync_condition[2]
                                    custom_field_value.save()
                                    break
                            elif sync_condition[0] == "contains":
                                if sync_condition[1] in source_property_value:
                                    custom_field_value.value = sync_condition[2]
                                    custom_field_value.save()
                                    break
        except:
            pass

        try:  # Language Sync order
            language_fields = ShopTurboOrdersNameCustomField.objects.filter(
                workspace=self.workspace, type="language", source_property__isnull=False
            )

            if language_fields:
                for language_field in language_fields:
                    source_property = language_field.source_property
                    pair_property = language_field.pair_property
                    sync_conditions = language_field.sync_conditions
                    source_property_value = getattr(self, source_property)

                    if pair_property:
                        pair = ShopTurboOrdersNameCustomField.objects.get(
                            id=pair_property
                        )
                    else:
                        pair = language_field
                    if sync_conditions:
                        sync_conditions = ast.literal_eval(sync_conditions)
                        custom_field_value, _ = (
                            ShopTurboOrdersValueCustomField.objects.get_or_create(
                                field_name=pair, orders=self
                            )
                        )
                        source_language = sync_conditions[0]
                        target_language = sync_conditions[1]

                        if source_language and target_language:
                            translator = Translator()
                            translation = translator.translate(
                                source_property_value,
                                src=source_language,
                                dest=target_language,
                            )
                            custom_field_value.value = translation.text
                            custom_field_value.save()
        except:
            pass

        try:  # Currency Sync order
            currency_fields = ShopTurboOrdersNameCustomField.objects.filter(
                workspace=self.workspace, type="currency", source_property__isnull=False
            )

            if currency_fields:
                for currency_field in currency_fields:
                    source_property = currency_field.source_property
                    pair_property = currency_field.pair_property
                    sync_conditions = currency_field.sync_conditions
                    source_property_value = getattr(self, source_property)

                    if pair_property:
                        pair = ShopTurboOrdersNameCustomField.objects.get(
                            id=pair_property
                        )
                    else:
                        pair = currency_field
                    if sync_conditions:
                        custom_field_value, _ = (
                            ShopTurboOrdersValueCustomField.objects.get_or_create(
                                field_name=pair, orders=self
                            )
                        )
                        currency_to_convert = sync_conditions

                        if currency_to_convert:
                            base_value = float(source_property_value)
                            result_value = ast.literal_eval(
                                requests.get(
                                    f"https://api.frankfurter.dev/v1/latest?base={self.currency}&symbols={currency_to_convert}&amount={base_value}"
                                ).text
                            )["rates"][currency_to_convert]
                            custom_field_value.value = result_value
                            custom_field_value.save()
        except:
            pass

        super().save(*args, **kwargs)

    def get_display_name(self):
        platforms = self.order_platforms.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.order_display_name
        return data

    def get_platform_order_id(self):
        platforms = self.order_platforms.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.platform_order_id
        return data

    def get_platform_order_status(self):
        platforms = self.order_platforms.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.is_active
        return data

    def get_platform_fulfillment_status(self):
        platforms = self.order_platforms.all()
        data = {}
        for platform in platforms:
            if platform.fulfillment_status is None:
                data[platform.channel.name.lower()] = ""
            else:
                data[platform.channel.name.lower()] = platform.fulfillment_status
        return data

    def get_platform_payment_status(self):
        platforms = self.order_platforms.all()
        data = {}
        for platform in platforms:
            if platform.payment_status is None:
                data[platform.channel.name.lower()] = ""
            else:
                data[platform.channel.name.lower()] = platform.payment_status
        return data

    def get_access_link(self):
        from django_hosts.resolvers import reverse

        from sanka.settings import LOCAL

        if LOCAL:
            type_http = "http:"
        else:
            type_http = "https:"
        url = type_http + reverse("open_order", host="app", kwargs={"id": self.id})
        return url

    def get_platform_paid_at(self):
        platforms = self.order_platforms.all()
        data = {}
        for platform in platforms:
            if platform.paid_at is None:
                data[platform.channel.name.lower()] = ""
            else:
                data[platform.channel.name.lower()] = platform.paid_at
        return data


class ShopTurboOrdersPlatforms(models.Model):
    order = models.ForeignKey(
        ShopTurboOrders,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="order_platforms",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform_order_id = models.CharField(max_length=200, null=True, blank=True)
    order_display_name = models.CharField(max_length=200, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    payment_status = models.CharField(max_length=200, null=True, blank=True)
    fulfillment_status = models.CharField(max_length=200, null=True, blank=True)
    return_status = models.CharField(max_length=200, null=True, blank=True)
    payment_link = models.TextField(blank=True, null=True)
    platform_payment_id = models.CharField(max_length=500, null=True, blank=True)
    payment_postal_code_field = models.CharField(max_length=50, null=True, blank=True)
    payment_expires_at = models.IntegerField(null=True, blank=True)
    paid_at = models.DateTimeField(null=True, blank=True)
    payment_allow_shipping_fee = models.BooleanField(default=False)
    order_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class ShopTurboItemsOrders(models.Model):
    # main foreng key to order
    order = models.ForeignKey(
        ShopTurboOrders, on_delete=models.CASCADE, null=True, blank=True
    )

    # item foreign key
    item = models.ForeignKey(
        "ShopTurboItems",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_item_orders",
    )
    item_variant = models.ForeignKey(
        "ShopTurboItemsVariations", on_delete=models.CASCADE, null=True, blank=True
    )
    item_price = models.ForeignKey(
        "ShopTurboItemsPrice", on_delete=models.CASCADE, null=True, blank=True
    )
    customer_item_price = models.ForeignKey(
        "ShopTurboCustomerItemsPrice", on_delete=models.CASCADE, null=True, blank=True
    )
    order_platform = models.ForeignKey(
        ShopTurboOrdersPlatforms, on_delete=models.CASCADE, null=True, blank=True
    )

    # custom item
    custom_item_name = models.CharField(max_length=500, null=True, blank=True)

    platform_item_id = models.CharField(max_length=200, null=True, blank=True)
    number_item = models.FloatField(null=True, blank=True)
    item_price_order = models.FloatField(null=True, blank=True)
    item_price_order_tax = models.FloatField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    order_view = models.IntegerField(null=True, blank=True)
    rakuten_option = models.CharField(max_length=500, null=True, blank=True)
    item_status = models.CharField(max_length=500, null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if not self.custom_item_name:
            if self.item:
                self.custom_item_name = self.item.name

        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)

    def __str__(self):
        if self.item:
            if self.item.name:
                return f"{self.item.name}"
            else:
                return f"{self.item.item_id}"
        else:
            return f"{self.id}"


class ShopTurboPurchaseItemsOrders(models.Model):
    # main foreng key to order
    order = models.ForeignKey(
        ShopTurboOrders, on_delete=models.CASCADE, null=True, blank=True
    )

    # item foreign key
    item = models.ForeignKey(
        "ShopTurboItems",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_purchase_item_orders",
    )
    item_variant = models.ForeignKey(
        "ShopTurboItemsVariations", on_delete=models.CASCADE, null=True, blank=True
    )
    item_price = models.ForeignKey(
        "ItemPurchasePrice", on_delete=models.CASCADE, null=True, blank=True
    )
    customer_item_price = models.ForeignKey(
        "ShopTurboCustomerItemsPrice", on_delete=models.CASCADE, null=True, blank=True
    )
    order_platform = models.ForeignKey(
        ShopTurboOrdersPlatforms, on_delete=models.CASCADE, null=True, blank=True
    )

    # custom item
    custom_item_name = models.CharField(max_length=500, null=True, blank=True)

    platform_item_id = models.CharField(max_length=200, null=True, blank=True)
    number_item = models.FloatField(null=True, blank=True)
    item_price_order = models.FloatField(null=True, blank=True)
    item_price_order_tax = models.FloatField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    order_view = models.IntegerField(null=True, blank=True)
    rakuten_option = models.CharField(max_length=500, null=True, blank=True)
    item_status = models.CharField(max_length=500, null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if not self.custom_item_name:
            if self.item:
                self.custom_item_name = self.item.name

        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)

    def __str__(self):
        if self.item:
            if self.item.name:
                return f"{self.item.name}"
            else:
                return f"{self.item.item_id}"
        else:
            return f"{self.id}"


LINE_ITEMS_CF_TYPE = [
    ("text", "Text"),
    ("choice", "Choice"),
    ("number", "Number"),
    ("date_time", "Date Time"),
    ("discount", "Discount"),
]

LINE_ITEMS_STATUS = [
    ("active", "Active"),
    ("inactive", "Inactive"),
]

# ShopTurboShopTurbo
SHOPTURBO_NUMBER_FORMAT = [
    ("number", "Number"),
    ("%", "%"),
    ("usd", "USD"),
    ("jpy", "JPY"),
    ("idr", "IDR"),
]


class ShopTurboItemsOrdersNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=LINE_ITEMS_CF_TYPE, max_length=20, null=True, blank=True
    )
    choice_value = models.TextField(null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class ShopTurboItemsOrdersValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    field_name = models.ForeignKey(
        ShopTurboItemsOrdersNameCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    item_order = models.ForeignKey(
        ShopTurboItemsOrders, on_delete=models.CASCADE, null=True, blank=True
    )
    value = models.TextField(null=True, blank=True)

    value_number_format = models.CharField(max_length=20, null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)


class ShopturboOrderNotes(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    order = models.ForeignKey(
        ShopTurboOrders, on_delete=models.CASCADE, null=True, blank=True
    )
    description = models.TextField(null=True, blank=True)
    date = models.DateTimeField(
        null=True,
        blank=True,
    )
    assignee = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(default=timezone.now)


class Notes(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    target = models.CharField(max_length=500, null=True, blank=True)  # Page group type
    order = models.ForeignKey(
        ShopTurboOrders, on_delete=models.CASCADE, null=True, blank=True
    )
    deal = models.ForeignKey("Deals", on_delete=models.CASCADE, null=True, blank=True)
    contact = models.ForeignKey(
        "Contact", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, null=True, blank=True
    )
    message = models.ForeignKey(
        "MessageThread", on_delete=models.CASCADE, null=True, blank=True
    )

    description = models.TextField(null=True, blank=True)
    date = models.DateTimeField(
        null=True,
        blank=True,
    )
    assignee = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(default=timezone.now)


class ShopTurboOrdersValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ShopTurboOrdersNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    orders = models.ForeignKey(
        ShopTurboOrders,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="shopturbo-orders-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = [["field_name", "orders"]]

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)
    value_time_end = models.DateTimeField(
        null=True, blank=True
    )  # Use for date_range field

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)

        try:
            if (
                self.field_name.type == "property_sync"
                and not self.field_name.source_property
            ):
                property_sync_field = self.field_name
            else:
                property_sync_field = None

            if property_sync_field:
                source_property = property_sync_field.source_property
                pair_property = property_sync_field.pair_property
                sync_conditions = property_sync_field.sync_conditions
                if not source_property:
                    source_property_value = self.value
                else:
                    source_property_value = getattr(self.orders, source_property)

                if pair_property:
                    pair = ShopTurboOrdersNameCustomField.objects.get(id=pair_property)
                else:
                    pair = property_sync_field
                if sync_conditions:
                    sync_conditions = ast.literal_eval(sync_conditions)
                    custom_field_value, _ = (
                        ShopTurboOrdersValueCustomField.objects.get_or_create(
                            field_name=pair, orders=self.orders
                        )
                    )
                    for sync_condition in sync_conditions:
                        if sync_condition[0] == "is":
                            if sync_condition[1] == source_property_value:
                                custom_field_value.value = sync_condition[2]
                                custom_field_value.save()
                                break
                        elif sync_condition[0] == "contains":
                            if sync_condition[1] in source_property_value:
                                custom_field_value.value = sync_condition[2]
                                custom_field_value.save()
                                break
        except:
            pass

        try:
            if (
                self.field_name.type == "language"
                and not self.field_name.source_property
            ):
                language_field = self.field_name
            else:
                language_field = None

            if language_field:
                source_property = language_field.source_property
                pair_property = language_field.pair_property
                sync_conditions = language_field.sync_conditions
                if not source_property:
                    source_property_value = self.value
                else:
                    source_property_value = getattr(self.orders, source_property)

                if pair_property:
                    pair = ShopTurboOrdersNameCustomField.objects.get(id=pair_property)
                else:
                    pair = language_field
                if sync_conditions:
                    sync_conditions = ast.literal_eval(sync_conditions)
                    custom_field_value, _ = (
                        ShopTurboOrdersValueCustomField.objects.get_or_create(
                            field_name=pair, orders=self.orders
                        )
                    )
                    source_language = sync_conditions[0]
                    target_language = sync_conditions[1]

                    if source_language and target_language:
                        translator = Translator()
                        translation = translator.translate(
                            source_property_value,
                            src=source_language,
                            dest=target_language,
                        )
                        custom_field_value.value = translation.text
                        custom_field_value.save()
        except:
            pass

        try:
            if (
                self.field_name.type == "currency"
                and not self.field_name.source_property
            ):
                currency_field = self.field_name
            else:
                currency_field = None

            if currency_field:
                source_property = currency_field.source_property
                pair_property = currency_field.pair_property
                sync_conditions = currency_field.sync_conditions
                if not source_property:
                    source_property_value = self.value
                else:
                    source_property_value = getattr(self.orders, source_property)

                if pair_property:
                    pair = ShopTurboOrdersNameCustomField.objects.get(id=pair_property)
                else:
                    pair = currency_field
                if sync_conditions:
                    custom_field_value, _ = (
                        ShopTurboOrdersValueCustomField.objects.get_or_create(
                            field_name=pair, orders=self.orders
                        )
                    )
                    currency_to_convert = sync_conditions

                    if currency_to_convert:
                        base_value = float(source_property_value)
                        result_value = ast.literal_eval(
                            requests.get(
                                f"https://api.frankfurter.dev/v1/latest?base={self.orders.currency}&symbols={currency_to_convert}&amount={base_value}"
                            ).text
                        )["rates"][currency_to_convert]
                        custom_field_value.value = result_value
                        custom_field_value.save()
        except:
            pass


class ShopTurboOrdersValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Shopturbo Order Custom Field file",
        upload_to="shopturbo-order-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        ShopTurboOrdersValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


SHOPTURBO_ITEMS_CF = OBJECTS_CF_TYPE + [
    ("svg", "SVG"),
    ("components", "Components"),
    ("process_master", "Process Master"),
]


class OrderGroupPayment(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    orders = models.ManyToManyField(ShopTurboOrders, related_name="group_payment")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
