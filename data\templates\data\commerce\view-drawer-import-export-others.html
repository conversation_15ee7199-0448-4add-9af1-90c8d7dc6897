{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}
{% comment %} ORDERS / ITEMS / SUBSCRIPTIONS / Invoice / Estimate {% endcomment %}
<div class="bg-white border-0 shadow-none card rounded-0">

    <div {% if change_view_card_body %} class="card-body pt-0" {% else %} class="" {% endif %}>
        <div id="change-stamp-section" class="mb-10">
            <div class="">
                <form method="POST" 
                    {% if download_form_url and download_view %}
                        action="{% host_url download_form_url host 'app' %}"
                    {% else %}
                        action="{% host_url 'commerce_view_setting' host 'app' %}"
                    {% endif %}
                    id="manage-view-form"
                    >
                    {% csrf_token %}


                    {% if object_type == 'slips' %}
                    <div class="mt-4 min-w-200px" id="record-component" hidden>
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            レコードID
                            {% else %}
                            Record ID
                            {% endif %}
                        </span>
                        
                        <style>
                            .selection option {
                                display: none;
                            }    
                        </style>
                        <select class="form-control record-ids-select2-this" name="record_ids" multiple="multiple" id="record_ids">
                        </select>
                
                        <script>
                            $(document).ready(function(e) {
                                {% if object_type == 'customer_case' %}
                                    var url = "{% host_url 'auto_complete_case' host 'app' %}"
                                {% elif object_type == 'slips' %}
                                    var url = "{% host_url 'auto_complete_slips' host 'app' %}"
                                {% endif %}
                            
                                $(".record-ids-select2-this").select2({
                                    placeholder: "{% if LANGUAGE_CODE == 'ja' %}レコードを選択{% else %}Select Record{% endif %}",
                                    allowClear: true,
                                    ajax: {
                                        url: url,
                                        dataType: "json",
                                        delay: 250,
                                        data: function (params) {
                                            return {
                                                search: params.term,
                                                page: params.page || 1
                                            };
                                        },
                                        processResults: function (data, params) {
                                            params.page = params.page || 1;
                        
                                            return {
                                                results: data.results,
                                                pagination: {
                                                    more: data.pagination.more,
                                                }
                                            };
                                        },
                                        cache: true
                                    }
                                })
                        
                                var selectedObjectId = '';
                                {% if record_ids %}
                                    selectedObjectId = [{% for id in record_ids|convert_to_list %}
                                        "{{id}}",
                                    {% endfor %}]
                                console.log("selectedObjectId: ", selectedObjectId)
                                {% endif %}
                             
                                if (selectedObjectId !== undefined && selectedObjectId != '') {
                                    if (selectedObjectId.length > 0) {
                                        $('#record-component').removeAttr('hidden');
                                        $.ajax({
                                            url: url,
                                            type: "GET",
                                            data: { ids: selectedObjectId },
                                            success: function(response) {
                                                response.results.forEach(function(item) {
                                                    var preselectedOption = new Option(item.text, item.id, true, true); 
                                                    $('.record-ids-select2-this').append(preselectedOption)
                                                })
                            
                                                $('.record-ids-select2-this').trigger('change');
                                                $('.record-ids-select2-this').trigger({
                                                    type: 'select2:select',
                                                    params: {
                                                        data: response.results
                                                    }
                                                });
                            
                                                // Prevent the dropdown from opening or closing
                                                // Avoid user changes the preselected orderid
                                                $('.record-ids-select2-this').on('select2:opening select2:closing', function(e) {
                                                    e.preventDefault();
                                                });
                                            },
                                            error: function(xhr, status, error) {
                                                console.error("Error fetching preselected option:", error);
                                            }
                                        });
                                    }
                                }
                            })
                        </script>
                    </div>
                    {% endif %}

                    {% if view %}
                        <input type="hidden" name="view_id" {% if view.id %} value="{{view.id}}" {% endif %} /> 
                    {% endif %}

                    {% if p_id %}
                        <input type="hidden" name="p_id" {% if p_id %} value="{{p_id}}" {% endif %} />
                    {% endif %}
                    
                    <input hidden id="object_type" name="object_type" value="{{object_type}}" />
                    <input hidden name="download_formats" value="{{download_formats|join:','}}" />

                    {% if view.title == "main" %}
                        <input type="hidden" name="name" value="{{view.title}}">
                    {% elif view.title or not view %} 
                        {% if not download_view %}
                        <div class="mt-4 min-w-200px">
                            <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0 required">
                                {% if LANGUAGE_CODE == 'ja'%}
                                名前
                                {% else %}
                                Name
                                {% endif %}
                            </span>
                        
                            <input required class="form-control" type="text" name="name" {% if view.title == "main"%}disabled{% endif %}
                                placeholder="{% if LANGUAGE_CODE == 'ja' %}名前{% else %}Name{% endif %}"

                                {% if view and view.title == "main" %}
                                    value="main"
                                {% elif view and view.title %}
                                    value="{{view.title}}"
                                {% endif %}
                            /> 
                        </div>
                        {% endif %}
                    {% endif %} 

                    <div class="mt-4 min-w-200px {% if download_view %}d-none{% endif %}">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            タイプ
                            {% else %}
                            Type
                            {% endif %}
                        </span>
                
                        <select required id="view-field" class="bg-white border min-h-40px form-select form-select-solid select2-this" data-control="select2" name="view_type" onchange="select_onchange(this)" placeholder="{% if LANGUAGE_CODE == 'ja' %}ビュー{% else %}View{% endif %}">
                            {% for vt in view_types %}
                                <option value="{{vt.0}}" {% if view_filter.view_type == vt.0 %}selected{% endif %}> {% if LANGUAGE_CODE == 'ja' %}{{vt.2}}{% else %}{{vt.1}}{% endif %} </option>
                            {% endfor %}
                        </select>

                        <script>
                            $(document).ready(function() {
                                var view_field_elm = document.getElementById('view-field')
                                select_onchange(view_field_elm)
                            });
                        </script>

                    </div>


                    {% if not download_view %}
                        {% if object_type == "slips" or object_type == "purchaseorder"%}
                            <div class="mb-4"
                            hx-get="{% host_url 'form_sets' host 'app' %}"
                            hx-vals='{"page_group_type": "{{object_type}}", "view_id":"{{view_filter.view.id}}" }'
                            hx-trigger="load"
                            hx-swap="#property-sets-table"
                        >
                            <div id="property-sets-table"></div>
                        </div> 
                        {% endif %}
                    {% endif %}
                    
                    {% if export_file_type == 'csv' %}
                        <div id="column-field" class="mt-4 min-w-200px">
                            <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                                {% if LANGUAGE_CODE == 'ja'%}
                                カラム
                                {% else %}
                                Columns
                                {% endif %}
                            </span>
                        
                            <input class="form-control" type="text" name="column"
                                placeholder="{% if LANGUAGE_CODE == 'ja' %}ビュー{% else %}View{% endif %}"
                                id="column"
                            />
                        </div>  
                    {% endif %}

                    {% if object_type == 'purchaseorder' %}
                    <div id="select-sort" class="mt-5">
                        <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            並び替え
                            {% else %}
                            Sort By
                            {% endif %}
                        </span>
                        
                        <select
                            name="order-by" 
                            class="bg-white border min-h-40px form-select form-select-solid select2-this" 
                            data-control="select2"
                            data-allow-clear="true"
                            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}並び替え{% else %}Order By{% endif %}"
                            >
                            <option value=""></option>
                            <option value="id_po" {% if view_filter.sort_order_by == 'id_po' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}発注ID{% else %}Purchase Order ID{% endif %}</option>
                            {% for column in columns %}
                                {% if not 'id' in column and id_field != column %}
                                    {% with args=column|add:'|'|add:object_type %} 
                                        {% with column_display=args|get_column_display:request %}
                                            <option value="{{column_display.id|stringify}}" {% if view_filter.sort_order_by == column_display.id|stringify %}selected{% endif %}>{{column_display.name}}</option>
                                        {% endwith %}
                                    {% endwith %}
                                {% endif %}
                            {% endfor %}
                        </select>

                        <div class="mt-5">
                            <span class="py-0 mb-3 border-0 fs-5 fw-bolder text-active-primary ms-0">
                                {% if LANGUAGE_CODE == 'ja'%}
                                並び替え方法
                                {% else %}
                                Sort Method
                                {% endif %}
                            </span>
                            
                            <select
                                name="sort-method" 
                                class="bg-white border min-h-40px form-select form-select-solid select2-this" 
                                data-control="select2"
                                data-allow-clear="true"
                                data-placeholder="{% if LANGUAGE_CODE == 'ja' %}並び替え方法{% else %}Sort Method{% endif %}"
                                data-hide-search="true"
                                >
                                <option value=""></option>
                                <option value="asc" {% if view_filter.sort_order_method == 'asc' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}昇順{% else %}Ascending{% endif %}</option>
                                <option value="desc" {% if view_filter.sort_order_method == 'desc' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}降順{% else %}Descending{% endif %}</option>
                            </select>
                        </div>
                    </div>
                    
                    {% endif%}
                    
                    
                    
                    <div id="advanced-filter" class="mt-5">
                        <div class="mb-2 fs-4 justify-content-start fw-bolder">
                            <span>
                                {% if LANGUAGE_CODE == 'ja' %}フィルター{% else %}Filters{% endif %}
                            </span>

                            <select id="filter-select-manage" class="bg-white border min-h-40px form-select form-select-solid w-100"      
                            data-control="select2" 
                            hx-get="{% host_url 'commerce_view_setting' host 'app' %}"
                            hx-target="#data-selector-content-manage"
                            hx-trigger="filterChanged"
                            onchange="onChangeFilterSelect(this);"
                            hx-vals='{"type":"advance-filter", "data_filter":"{{column}}", "object_type":"{{object_type}}"}'
                            hx-swap="beforeend" 
                            >
                                <option value=''></option>
                                {% for column in columns %} 
                                    {% if object_type == 'purchaseorder' and column == 'item_price' or object_type == 'purchaseorder' and column == 'item_price_without_tax'  %}
                                    {% else %}
                                        {% if column|split:'|'|length > 1 and '_platform|' not in column %} {% comment %} Handle value such as customer|contact|name {% endcomment %}
                                    
                                            {% with column_display=column|split:'|'|search_custom_field_object_order_customer:request %}
                                                <option value="{{column}}" {% if view_filter.sort_order_by == column %}selected{% endif %}>{{column_display.name}}</option>
                                            {% endwith %}        
                                        
                                        {% else %}
                                            <option value="{{column}}">
                                                {% with args=column|add:'|'|add:object_type %} 
                                                    {% with column_display=args|get_column_display:request %}
                                                        {{column_display.name}}
                                                    {% endwith %}
                                                {% endwith %}   
                                            </option>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div id="data-selector-content-manage" ></div>
                    {% for predefined_data_filter in view_filter.filter_value %}
                    <div                                 
                        hx-get="{% host_url 'commerce_view_setting' host 'app' %}"
                        hx-target="#data-selector-content-manage"
                        hx-trigger="load"
                        hx-vals='{"type":"advance-filter", "data_filter":"{{ predefined_data_filter|safe }}", "predefined_data_filter":"{{view_filter.filter_value|get_attr:predefined_data_filter}}", "object_type":"{{object_type}}"}'
                        hx-swap="beforeend" 
                    >
                    </div>
                    {% endfor %}
                    {% if object_type == 'commerce_inventory_transaction'%}
                    <div id="select-item-stock-ledger" class="mt-5 d-none">
                        <div class="mb-2 fs-4 justify-content-start fw-bolder">
                            <div class=>
                                {% if LANGUAGE_CODE == 'ja' %}商品{% else %}Item{% endif %}
                            </div>
                            <select id="inventory-item" class="bg-white border min-h-40px form-select form-select-solid w-100 select2-this"  data-control="select2" name="inventory_item" placeholder="{% if LANGUAGE_CODE == 'ja' %}商品{% else %}Item{% endif %}">
                                {% for item in iventory_items%}
                                    <option value="{{item.id}}" {%if item_id == item.id|to_str%} selected {%endif%}> {% get_object_display item 'commerce_items' %} </option>
                                {% endfor%}
                            </select>
                        </div>
                        <div class="pt-2 mb-2 fs-4 justify-content-start fw-bolder">
                            <div class=>
                                {% if LANGUAGE_CODE == 'ja' %}日付範囲{% else %}Date Range{% endif %}
                            </div>
                            <div class="me-2">
                                <div class="max-w-150px">
                                <input {% if view_filter.view_type == 'stock_ledger' or view_filter.view_type == 'fifo_ledger' %}required{% endif %} id="transaction-date-range" class="form-control d-flex" name="transaction_date_range"
                                        {% if transaction_date_range%}
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            value="{% local_time_range  transaction_date_range workspace.timezone '%Y年%m月%d日' %}" 
                                            {% else %}
                                            value="{% local_time_range  transaction_date_range workspace.timezone '%Y-%m-%d' %}" 
                                            {% endif %}
                                        {%endif%}
                                    />
                                </div>
                            </div>
                            <script>
                                $('#transaction-date-range').attr('autocomplete', 'off');
                                $('#transaction-date-range').daterangepicker({
                                    singleDatePicker: false, // Enable range selection
                                    
                                    {% if data_type == "date" %}
                                    timePicker: false, // Disable time picker for date only
                                    {% elif data_type == "date_time" %}
                                    timePicker: true, // Enable time picker for date_time
                                    {% endif %}
                                
                                    autoUpdateInput: false,
                                    showDropdowns: true,
                                    drops: "auto",
                                    locale: {
                                        {% if LANGUAGE_CODE == 'ja' %}
                                            cancelLabel: 'クリア',
                                            format: 'YYYY年MM月DD日 HH:mm', // Japanese date format
                                            separator: ' 〜 ',
                                            applyLabel: '選択',
                                            cancelLabel: 'キャンセル',
                                            fromLabel: 'From',
                                            toLabel: 'To',
                                            customRangeLabel: 'カスタム範囲',
                                            daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                                            monthNames: [
                                                '1月', '2月', '3月', '4月', '5月', '6月',
                                                '7月', '8月', '9月', '10月', '11月', '12月'
                                            ],
                                        {% else %}
                                            format: "Y-M-DD HH:mm",
                                        {% endif %}
                                        separator: " - ", // Define the separator for the range
                                    }
                                });
                                
                                $('#transaction-date-range').on('apply.daterangepicker', function(ev, picker) {
                                    {% if LANGUAGE_CODE == 'ja' %}
                                        $(this).val(picker.startDate.format('YYYY年MM月DD日') + ' - ' + picker.endDate.format('YYYY年MM月DD日'));
                                    {% else %}
                                        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
                                    {% endif %}
                                });
                                
                                $('#transaction-date-range').on('cancel.daterangepicker', function(ev, picker) {
                                    $(this).val('');
                                });
                            </script>      
    
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-8"></div>
                    
                    {% if download_view %}
                        {% if object_type == constant.TYPE_OBJECT_EXPENSE or object_type == constant.TYPE_OBJECT_BILL or object_type == constant.TYPE_OBJECT_INVOICE or object_type == constant.TYPE_OBJECT_ESTIMATE or object_type == constant.TYPE_OBJECT_DELIVERY_NOTE or object_type == "receipts" or object_type == constant.TYPE_OBJECT_SLIP %}
                            {% if export_file_type != 'csv' %}
                                <button name="download_zip" type="submit" class="btn btn-dark">
                                    {% if object_type == constant.TYPE_OBJECT_EXPENSE %}
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        経費ファイルをダウンロード
                                        {% else %}
                                        Download Expenses Files
                                        {% endif %}
                                    {% elif object_type == constant.TYPE_OBJECT_BILL %}
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        請求ファイルをダウンロード
                                        {% else %}
                                        Download Billing Files
                                        {% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        PDFをダウンロード
                                        {% else %}
                                        Download PDF Files
                                        {% endif %}
                                    {% endif %}
                                </button>
                            {% else %}
                                <button name="download_csv" type="submit" class="btn btn-dark">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    CSVをダウンロード
                                    {% else %}
                                    Download CSV File
                                    {% endif %}
                                </button>
                            {% endif %}
                        {% else %}


                            {% if object_type|in_list:'customer_case' %}
                            <div class="my-5">
                                <div class="mb-2 fs-4 justify-content-start fw-bolder">
                                    <div class=>
                                        {% if LANGUAGE_CODE == 'ja' %}エンコード形式{% else %}Encode Format {% endif %}
                                    </div>
                                    <select name="encoded_format" class="bg-white border min-h-40px form-select form-select-solid w-100 select2-this"      >
                                        <option value='utf-8' selected>{% if LANGUAGE_CODE == 'ja' %}UTF-8{% else %}UTF-8 {% endif %}</option>
                                        <option value='shift-jis'>{% if LANGUAGE_CODE == 'ja' %}SHIFT-JIS{% else %}SHIFT-JIS {% endif %}</option>
                                    </select>
                                </div>
                            </div>
                            {% endif %}


                            <button name="download" type="submit" class="btn btn-dark">
                                {% if LANGUAGE_CODE == 'ja'%}
                                ダウンロード
                                {% else %}
                                Download
                                {% endif %}
                            </button>
                        {% endif %}
                    {% else %}


                        <div class="separator separator-dashed mt-2 mb-2"></div>

                        <button name="update-view-button" type="submit" class="btn btn-dark w-100">
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    {% endif %}                    
                </form>

                {% if view and not download_view %}
                
                    {% if view.title and view.title != "main" %}
                    <div class="mt-4">
                        <div class="fv-rowd-flex flex-column">
                            <form method="POST" action="{% host_url 'commerce_view_setting' host 'app' %}">
                                {% csrf_token %}
                                <input type="hidden" name="view_id" {% if view.id %} value="{{view.id}}" {% endif %} /> 
                                <input hidden name="object_type" value="{{object_type}}" />

                                <div class="border-0">
                                    <button type="submit" name="delete-view" class="btn btn-danger">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        削除
                                        {% else %}
                                        Delete
                                        {% endif %}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    {% endif %}

                {% endif %}

            </div>

        </div>

    </div>
</div>




<script>
    $('.select2-this').select2();
    $('#filter-select-manage').select2({
        placeholder: "{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}"
    })
    $('#sort-select-manage').select2({
        placeholder: "{% if LANGUAGE_CODE == 'ja' %}並べ替えを選択{% else %}Select Sorts{% endif %}"
    })

    function onChangeFilterSelect(element) {
        var currentHxVals = element.getAttribute('hx-vals');
        var currentHxValsObject = JSON.parse(currentHxVals);
        currentHxValsObject.data_filter = element.value
        element.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
        htmx.trigger('#filter-select-manage', 'filterChanged');

        element.value = ''
        $('#filter-select-manage').select2({
            placeholder: "{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}"
        })
    }

    function onChangeSortSelect(element) {
        var currentHxVals = element.getAttribute('hx-vals');
        var currentHxValsObject = JSON.parse(currentHxVals);
        currentHxValsObject.data_sort = element.value
        element.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
        htmx.trigger('#sort-select-manage', 'sortChanged');

        element.value = ''
        $('#sort-select-manage').select2({
            placeholder: "{% if LANGUAGE_CODE == 'ja' %}並べ替えを選択{% else %}Select Sorts{% endif %}"
        })
    }

    {% if export_file_type == 'csv' %}
        // Contacts Tagify
        var input_item_elem = document.querySelector("#column");
        var tagify = new Tagify(input_item_elem, {
            whitelist: [
            {% for column in columns %}
                {% if export_file_type == 'csv' or not 'id' in column and id_field != column %}
                    {% with args=column|add:'|'|add:object_type %}
                        {% with column_display=args|get_column_display:request %}
                            {% if column|split:'|'|length > 1 %}
                                {% with column_display=column|split:'|'|search_custom_field_object_order_customer:request %}
                                    { "value": "{{column_display.name}}", "id": "{{column_display.id}}" },
                                {% endwith %}
                            {% else %}
                                { "value": "{{column_display.name}}", "id": "{{column_display.id}}" },
                            {% endif %}
                        {% endwith %}
                    {% endwith %}
                {% endif %}
            {% endfor %}
            ],
            maxTags: {{ columns|safe|length }},
            searchKeys: ['value'], 
            enforceWhitelist: true,
            dropdown: {
                maxItems: {{ columns|safe|length }},           // <- mixumum allowed rendered suggestions
                classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                enabled: 0,             // <- show suggestions on focus
                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
            },
            originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
        });
        
        {% if view_filter %}
            {% for column in view_filter.column|safe|string_list_to_list %}
                {% if export_file_type == 'csv' or not 'id' in column and id_field != column %}
                    {% with args=column|add:'|'|add:object_type %}
                        {% with column_display=args|get_column_display:request %}
                            {% if column|split:'|'|length > 1 %}
                                {% with column_display=column|split:'|'|search_custom_field_object_order_customer:request %}
                                    tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                                {% endwith %}
                            {% else %}
                                tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                            {% endif %}
                        {% endwith %}
                    {% endwith %}
                {% endif %}
            {% endfor %}
        {% else %}
            {% for column in default_columns|safe|string_list_to_list %}
                {% if export_file_type == 'csv' or not 'id' in column and id_field != column %}
                    {% with args=column|add:'|'|add:object_type %}
                        {% with column_display=args|get_column_display:request %}
                            tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                        {% endwith %}
                    {% endwith %}
                {% endif %}
            {% endfor %}
        {% endif %}
    {% endif %}

    // using 3-party script "dragsort"
    // https://github.com/yairEO/dragsort
    var dragsort = new DragSort(tagify.DOM.scope, {
        selector:'.'+tagify.settings.classNames.tag,
        callbacks: {
            dragEnd: onDragEnd
        }
    })

    function onDragEnd(elm){
        tagify.updateValueByDOMTags()
    }


    function select_onchange(elm){
        var value = elm.value;
        var status_selector = document.getElementById('status-selector');
        var item_selection = document.getElementById('select-item-stock-ledger');
        var transaction_date_range = document.getElementById('transaction-date-range');
        var advanced_filter = document.getElementById('advanced-filter');
        var view_page = document.getElementById('object_type');
        var column_field = document.getElementById('column-field');


        if (status_selector){
            if (value == 'kanban'){
                status_selector.classList.remove('d-none')
            }else{
                status_selector.classList.add('d-none')
            }
        }

        
        if (view_page && view_page.value == 'commerce_inventory_transaction'){
            if (value == 'stock_ledger' || value == 'fifo_ledger'){
                item_selection.classList.remove('d-none')
                transaction_date_range.setAttribute('required', '')
                column_field.classList.add('d-none')
                advanced_filter.classList.add('d-none')

            } else {
                item_selection.classList.add('d-none')
                transaction_date_range.removeAttribute('required')
                advanced_filter.classList.remove('d-none')
                column_field.classList.remove('d-none')

            }
        } else if (view_page.value == 'commerce_inventory' ) {
            var column_field = document.getElementById('column-field')
            if (value == 'forecast'){
                column_field.classList.add('d-none')
            }else{
                column_field.classList.remove('d-none')
            }
        }
        
        
    }



    function delete_form(elm){
        elm.parentElement.parentElement.remove()
    }

    {% if object_type == 'campaigns' %}
    $('#view-field').select2().on('select2:select', function(){
        if (this.value == 'calendar') {
            document.getElementById('column-field').classList.add('d-none')
        } else {
            document.getElementById('column-field').classList.remove('d-none')
        }
    })
        {% if view_filter.view_type == 'calendar' %}
            document.getElementById('column-field').classList.add('d-none')
        {% endif %}
    {% endif %}

</script>
