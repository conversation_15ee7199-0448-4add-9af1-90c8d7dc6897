import ast
import json
import uuid

from django.conf import settings
from django.core.paginator import Paginator
from django.db.models import BooleanField, Case, OuterRef, Q, Subquery, Value, When
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse

from data.commerce.advanced_search_check import advanced_search_check
from data.constants.commerce_constant import COMMERCE_APP_SLUG, COMMERCE_APP_TARGET
from data.constants.constant import DEFAULT_PERMISSION
from data.constants.properties_constant import OBJECT_TYPE_TO_SLUG, TYPE_OBJECT_INVOICE
from data.invoice import push_freee_invoices
from data.invoice.background.export_csv_invoices import (
    ExportCSVInvoicesPayload,
    export_csv_invoices,
)
from data.invoice.moneyforward import (
    pull_moneyforward_invoices,
    push_moneyforward_invoices,
)
from data.models import (
    AppSetting,
    AppSettingChild,
    Channel,
    HubspotUser,
    InvoiceMappingFields,
    Module,
    Notification,
    PropertySet,
    TransferHistory,
    View,
    ViewFilter,
)
from utils.bgjobs.handler import add_hatchet_run_id, create_bg_job
from utils.bgjobs.runner import trigger_bg_job
from utils.decorator import login_or_hubspot_required
from utils.filter import build_view_filter
from utils.logger import logger
from utils.project import get_ordered_views
from utils.properties.properties import get_default_property_set, get_page_object
from utils.search.billing_search import apply_search_setting
from utils.utility import (
    get_permission_filter,
    get_workspace,
    is_valid_uuid,
    is_valid_view_id,
    normalize_q_filter,
)
from utils.workspace import get_permission

type_http = settings.SITE_URL.split("//")[0]


@login_or_hubspot_required
def invoices(request, view="main", id=None, return_context=False):
    # view for billing page
    try:
        show_per_page = int(request.GET.get("show_per_page", 30))
    except:
        show_per_page = 30

    page = request.GET.get("page")
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    property_set = None
    object_type = TYPE_OBJECT_INVOICE
    # View Menu
    page_obj = get_page_object(TYPE_OBJECT_INVOICE, lang)
    id_field = page_obj["id_field"]  # id_inv, id_est, id_rcp, id_ds
    page_title = page_obj["page_title"]  # Invoices etc
    # columns_display = page_obj["columns_display"]
    default_columns = page_obj["default_columns"]
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj["base_model"]  # Invoice
    # field_item_name = page_obj["field_item_name"]  # Invoice
    custom_model = page_obj["custom_model"]  # InvoiceNameCustomField etc
    # InvoiceNameCustomField etc
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    # custom_relation = page_obj["custom_relation"]  # invoice_object
    # item_model = page_obj["item_model"]  # invoice_model item
    additional_filter_fields = page_obj["additional_filter_fields"]

    permission = get_permission(object_type=TYPE_OBJECT_INVOICE, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    if permission == "hide":
        context = {
            "permission": permission,
            "page_title": page_title,
            "app_slug": COMMERCE_APP_SLUG,
            "target": TYPE_OBJECT_INVOICE,
        }
        return render(request, "data/invoice/invoice.html", context)

    source_integration = request.GET.get("source_integration", None)
    if source_integration == "hubspot":
        userId = request.GET.get("userId")
        channel_id = request.GET.get("channel_id")
        channel = Channel.objects.filter(id=channel_id).first()
        hs_user = HubspotUser.objects.filter(channel=channel, hubspot_id=userId).first()
        workspace = channel.workspace
        user = hs_user.sanka_user.user
        permission = get_permission(object_type=TYPE_OBJECT_INVOICE, user=user)

    view_id = request.GET.get("view_id", None)
    view = None
    view_filter = None
    if not View.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_INVOICE
    ).exists():
        view = View.objects.create(
            workspace=workspace,
            title="main",
            target=TYPE_OBJECT_INVOICE,
        )
        view_filter = ViewFilter.objects.create(
            view=view,
            column=default_columns,
            view_type="list",
        )

    views = get_ordered_views(
        workspace=workspace, object_target=TYPE_OBJECT_INVOICE, user=request.user
    )
    try:
        view_id = is_valid_view_id(view_id)
        if view_id:
            view = View.objects.filter(id=view_id).first()
            if not view or (
                view.is_private and (not request.user or view.user != request.user)
            ):
                view, _ = View.objects.get_or_create(
                    workspace=workspace,
                    title="main",
                    target=TYPE_OBJECT_INVOICE,
                )
                view_id = view.id
        else:
            view = View.objects.filter(
                workspace=workspace, title="main", target=TYPE_OBJECT_INVOICE
            ).first()

            view_id = view.id
    except Exception as e:
        print(f"ERROR === invoices.py -- 186: {e}")

    if not view:
        view = View.objects.filter(title="main", target=TYPE_OBJECT_INVOICE).first()
    view_filter = view.viewfilter_set.first()

    show_per_page = 25
    if view_filter and view_filter.pagination:
        show_per_page = view_filter.pagination

    app_setting = AppSetting.objects.filter(
        workspace=workspace, app_target=COMMERCE_APP_TARGET
    ).first()
    if not app_setting:
        app_setting = AppSetting.objects.create(
            workspace=workspace, app_target=COMMERCE_APP_TARGET
        )

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting,
        target_name="invoice_search_setting",
        property_set=property_set,
    ).first()
    if not app_setting_child:
        app_setting_child = AppSettingChild.objects.create(
            app_setting=app_setting,
            target_name="invoice_search_setting",
            property_set=property_set,
        )

    filter_dictionary = app_setting_child.value
    filter_conditions = Q(workspace=workspace)

    filter_conditions &= get_permission_filter(permission, request.user)
    advance_search = advanced_search_check(
        workspace=workspace, object_type=TYPE_OBJECT_INVOICE
    )
    if advance_search.search_settings:
        filter_dictionary = advance_search.search_settings

    # Handle status filter for archived items
    status = request.GET.get("status")
    if status == "archived":
        filter_conditions &= Q(usage_status="archived")
    else:
        # By default, show only active items
        filter_conditions &= Q(usage_status="active")

    search_q = request.GET.get("q")
    if search_q:
        if filter_dictionary and "customers__" in filter_dictionary:
            filter_dictionary = filter_dictionary.replace("customers__", "")

        search_ = search_q.split()
        for search in search_:
            try:
                int(search)
                filter_conditions &= apply_search_setting(
                    custom_model, filter_dictionary, TYPE_OBJECT_INVOICE, search.lower()
                ) | Q(**{id_field: search.lower()})
            except:
                filter_conditions &= apply_search_setting(
                    custom_model, filter_dictionary, TYPE_OBJECT_INVOICE, search.lower()
                )

    if advance_search:
        if advance_search.is_active:
            advance_search_filter = advance_search.search_filter
            advance_search_filter_status = advance_search.search_filter_status
            if advance_search_filter_status:
                advance_search_filter = {
                    k: v
                    for k, v in advance_search_filter.items()
                    if v["value"] != ""
                    and advance_search_filter_status.get(k) != "False"
                }
            else:
                advance_search_filter = {
                    k: v for k, v in advance_search_filter.items() if v["value"] != ""
                }
            try:
                if advance_search_filter.get("usage_status", {}).get("value") == "all":
                    del advance_search_filter["usage_status"]
            except KeyError:
                pass

            if view_filter:
                view_filter.filter_value = advance_search_filter

    filter_conditions = build_view_filter(
        filter_conditions,
        view_filter,
        TYPE_OBJECT_INVOICE,
        force_filter_list=additional_filter_fields,
        request=request,
    )

    if "download_zip" in request.POST:
        if lang == "ja":
            history_name = "売上請求レコードのエクスポート"
        else:
            history_name = "Export Invoice Records"

        history = TransferHistory.objects.create(
            workspace=workspace,
            user=request.user,
            status="running",
            type=f"export_{object_type}",
            name=history_name,
        )
        record_ids = (
            ",".join(request.POST.getlist("record_ids"))
            if request.POST.get("record_ids")
            else ""
        )
        param = {
            "function": "export_pdf_billings",
            "job_id": str(uuid.uuid4()),
            "workspace_id": str(workspace.id),
            "user_id": str(request.user.id),
            "args": [
                f"--user_id={str(request.user.id)}",
                f"--workspace={str(workspace.id)}",
                f"--view_id={view_id}",
                f"--history_id={str(history.id)}",
                f"--object_type={TYPE_OBJECT_INVOICE}",
                f"--language={lang}",
                f"--record_ids={record_ids}",
            ],
        }

        is_running = trigger_bg_job(param, transfer_history=history)
        if is_running:
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"{history_name}のジョブが正常に送信されました。ファイルはメールで送信されます。",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"{history_name} job submitted successfully, file will be sent to your email.",
                    type="success",
                )
        else:
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"{history_name}ジョブの送信中にエラーが発生しました。サポートに連絡してください。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"There is an error while submitting the {history_name} job. Please contact support.",
                    type="error",
                )
            history.delete()

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVOICE]
        module = Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_INVOICE
        ).order_by("order", "created_at")

        module_slug = request.POST.get("module", "")
        if module_slug:
            module = module.filter(slug=module_slug)

        if module:
            module = module.first()
            module_slug = module.slug

        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )

    if "download_csv" in request.POST:
        logger.info(f"req POST: {request.POST}")
        if lang == "ja":
            history_name = "請求書のエクスポート"
        else:
            history_name = "Export Invoices"

        history = TransferHistory.objects.create(
            workspace=workspace,
            user=request.user,
            status="running",
            type="export_invoice",
            name=history_name,
        )

        view_id = request.POST.get("view_id", None)
        columns = request.POST.get("column", None)
        encoded_format = request.POST.get("encoded_format", "utf-8")

        filter_dictionary = {}
        filter_types = request.POST.getlist("filter_type", None)
        filter_options = request.POST.getlist("filter_options", None)
        filter_values = request.POST.getlist("filter_value", None)

        for idx, filter_type in enumerate(filter_types):
            filter_dictionary[str(filter_type)] = {
                "key": filter_options[idx],
                "value": filter_values[idx],
            }

        record_ids = None
        if request.POST.get("record_ids"):
            record_ids = ",".join(request.POST.getlist("record_ids"))
            filter_dictionary["id"] = {"key": "include", "value": record_ids}

        filter_dictionary = json.dumps(filter_dictionary)

        payload = ExportCSVInvoicesPayload(
            user_id=str(request.user.id),
            workspace_id=str(workspace.id),
            view_id=str(view_id),
            history_id=str(history.id),
            columns=columns,
            filter_dictionary=filter_dictionary,
            encoded_format=encoded_format,
            language=lang,
            record_ids=record_ids,
        )

        job_id = create_bg_job(
            workspace,
            request.user,
            "export_csv_invoices",
            transfer_history=history,
            payload=payload.model_dump(mode="json"),
        )
        payload.background_job_id = job_id

        # Log export job parameters for debugging
        logger.info(
            f"EXPORT_JOB: Starting invoices export for user {request.user.email} in workspace {workspace.id}"
        )
        logger.info(f"EXPORT_JOB: Export parameters - job_id: {job_id}")
        logger.info(
            f"EXPORT_JOB: View ID: {view_id}, History ID: {history.id}, Language: {lang}"
        )
        logger.info(f"EXPORT_JOB: Columns: {columns}")
        logger.info(f"EXPORT_JOB: Filter dictionary: {filter_dictionary}")

        ref = None
        try:
            ref = export_csv_invoices.run_no_wait(input=payload)
        except Exception as e:
            logger.error(
                f"EXPORT_JOB: Exception occurred during export_csv_invoices: {str(e)}",
                exc_info=True,
            )
            logger.info(
                "EXPORT_JOB: Falling back to synchronous processing due to Hatchet unavailability"
            )

            # Fallback to synchronous processing - create a simple CSV export
            try:
                # Import the necessary modules for CSV generation
                import csv
                import io
                from django.core.mail import EmailMessage
                from django.template.loader import render_to_string
                from django.utils import timezone
                from utils.contact import (
                    get_company_downloadable_value,
                    get_contact_downloadable_value,
                )
                from utils.date import format_date

                # Get the data we need
                workspace = get_workspace(request.user)
                user = request.user

                # Build filter conditions (simplified version)
                filter_conditions = Q(workspace=workspace, usage_status="active")

                # Parse record IDs if provided
                if request.POST.get("record_ids"):
                    record_ids = request.POST.getlist("record_ids")
                    filter_conditions &= Q(id__in=record_ids)

                # Get invoices
                from data.models import Invoice

                invoices = Invoice.objects.filter(filter_conditions).order_by(
                    "-created_at"
                )

                # Parse columns
                columns_str = request.POST.get(
                    "column", "id_inv,total_price,currency,status"
                )
                column_list = (
                    columns_str.split(",")
                    if columns_str
                    else ["id_inv", "total_price", "currency", "status"]
                )

                # Create CSV
                csv_buffer = io.StringIO()
                writer = csv.writer(csv_buffer)

                # Write header
                header_row = []
                for column in column_list:
                    if column == "id_inv":
                        header_row.append("Invoice ID" if lang == "en" else "請求書ID")
                    elif column == "total_price":
                        header_row.append("Total Price" if lang == "en" else "合計金額")
                    elif column == "currency":
                        header_row.append("Currency" if lang == "en" else "通貨")
                    elif column == "status":
                        header_row.append("Status" if lang == "en" else "ステータス")
                    elif column == "company":
                        header_row.append("Company" if lang == "en" else "会社")
                    elif column == "contact":
                        header_row.append("Contact" if lang == "en" else "連絡先")
                    elif column == "created_at":
                        header_row.append("Created Date" if lang == "en" else "作成日")
                    elif column == "due_date":
                        header_row.append("Due Date" if lang == "en" else "支払期限")
                    else:
                        header_row.append(column)

                writer.writerow(header_row)

                # Write data rows
                for invoice in invoices:
                    row = []
                    for column in column_list:
                        if column == "id_inv":
                            row.append(invoice.id_inv or "")
                        elif column == "total_price":
                            row.append(invoice.total_price or 0)
                        elif column == "currency":
                            row.append(invoice.currency or "")
                        elif column == "status":
                            row.append(invoice.status or "")
                        elif column == "company":
                            if invoice.company:
                                row.append(
                                    get_company_downloadable_value(
                                        invoice.company, workspace
                                    )
                                )
                            else:
                                row.append("")
                        elif column == "contact":
                            if invoice.contact:
                                row.append(
                                    get_contact_downloadable_value(
                                        invoice.contact, workspace
                                    )
                                )
                            else:
                                row.append("")
                        elif column == "created_at":
                            if invoice.created_at:
                                row.append(format_date(invoice.created_at, workspace))
                            else:
                                row.append("")
                        elif column == "due_date":
                            if hasattr(invoice, "due_date") and invoice.due_date:
                                row.append(format_date(invoice.due_date, workspace))
                            else:
                                row.append("")
                        else:
                            try:
                                value = getattr(invoice, column, "")
                                row.append(str(value) if value is not None else "")
                            except:
                                row.append("")

                    writer.writerow(row)

                # Send email
                sending_data = csv_buffer.getvalue()
                encoded_format = request.POST.get("encoded_format", "utf-8")

                if isinstance(sending_data, str):
                    sending_data = sending_data.encode(encoded_format)

                if lang == "ja":
                    mail_subject = "Sanka - エクスポートCSV - 請求書"
                    curr_datetime = timezone.now().strftime("%Y年%m月%d日_%H:%M:%S")
                    filename = f"Sanka_請求書エクスポート_{curr_datetime}.csv"
                else:
                    mail_subject = "Sanka - Export CSV - Invoices"
                    curr_datetime = timezone.now().strftime("%Y-%m-%d_%H:%M:%S")
                    filename = f"Sanka_Export_Invoices_{curr_datetime}.csv"

                if lang == "ja":
                    message = render_to_string("data/email/export-orders-ja.html")
                else:
                    message = render_to_string("data/email/export-orders.html")

                email_message = EmailMessage(
                    mail_subject, message, "Sanka <<EMAIL>>", [user.email]
                )
                email_message.attach(filename, sending_data, "text/csv")
                email_message.send(fail_silently=False)

                # Update history
                if history:
                    if lang == "ja":
                        history.name = f"請求書エクスポート: {invoices.count()}件のレコード (同期処理)"
                    else:
                        history.name = (
                            f"Export Invoices: {invoices.count()} records (sync)"
                        )
                    history.status = "completed"
                    history.save()

                ref = "sync_fallback"
                logger.info(
                    f"EXPORT_JOB: Synchronous fallback completed successfully - exported {invoices.count()} invoices"
                )

            except Exception as sync_error:
                logger.error(
                    f"EXPORT_JOB: Synchronous fallback also failed: {str(sync_error)}",
                    exc_info=True,
                )
                ref = None

        is_running = None
        if ref:
            if ref == "sync_fallback":
                logger.info(
                    f"EXPORT_JOB: Synchronous export completed successfully for user {request.user.email}"
                )
            else:
                logger.info(
                    f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}"
                )
                add_hatchet_run_id(job_id, ref)
            is_running = True
        else:
            logger.error(
                f"EXPORT_JOB: Failed to submit background job for user {request.user.email}"
            )
            is_running = False

        if is_running:
            logger.info(
                f"EXPORT_JOB: Successfully submitted invoices export job for user {request.user.email}"
            )
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="エクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Export job submitted successfully, csv will be sent to your email.",
                    type="success",
                )
        else:
            logger.error(
                f"EXPORT_JOB: Failed to submit invoices export job for user {request.user.email}"
            )
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"{history_name}ジョブの送信中にエラーが発生しました。サポートにお問い合わせください。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"There is an error while submitting the {history_name} job. Please contact support.",
                    type="error",
                )
            history.delete()

        module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
        module = Module.objects.filter(
            workspace=workspace, object_values__contains=object_type
        ).order_by("order", "created_at")

        module_slug = request.POST.get("module", "")
        if module_slug:
            module = module.filter(slug=module_slug)

        if module:
            module = module.first()
            module_slug = module.slug

        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )

    try:
        filter_conditions = normalize_q_filter(filter_conditions)
    except:
        pass

    if "select_integration_ids" in request.POST:
        import_export_type = request.POST.get("import_export_type", None)
        channel_id = request.POST.get("select_integration_ids", None)
        channel = Channel.objects.filter(id=channel_id).first()
        if channel:
            platform = channel.integration.slug
        else:
            platform = request.POST.get("platform", None)
        if platform == "freee":
            try:
                invoice_ids = request.POST.getlist("item_ids", [])
                channel_id = request.POST.get("select_integration_ids", None)
                contact_file_columns = request.POST.getlist("file-column", [])
                contact_sanka_properties = request.POST.getlist("sanka-properties", [])
                contact_ignores = request.POST.getlist("ignore", [])

                mapping_contact_custom_fields = {}
                contact_mapping = {}

                for idx, file_column in enumerate(contact_file_columns):
                    # check ignore
                    contact_mapping[file_column] = contact_sanka_properties[idx]
                    if contact_ignores[idx] == "True":
                        continue
                    mapping_contact_custom_fields[file_column] = (
                        contact_sanka_properties[idx]
                    )

                push_freee_invoices(
                    request, invoice_ids, channel_id, mapping_contact_custom_fields
                )
            except Exception as e:
                print(f"ERROR === invoices.py -- 682: {e}")
        elif platform == "hubspot":
            try:
                invoice_ids = request.POST.getlist("item_ids", [])
                channel_id = request.POST.get("select_integration_ids", None)
                channel = Channel.objects.filter(id=channel_id).first()

                platform = request.POST.get("platform")
                platform_object_type_id = request.POST.get("platform_object_type_id")
                platform_columns = request.POST.get("platform_columns")
                header_list = request.POST.get("header_list")
                hubspot_group_name = request.POST.get("hubspot_group_name", "")

                header_list = ast.literal_eval(header_list)
                platform_columns = ast.literal_eval(platform_columns)

                mapping, _ = InvoiceMappingFields.objects.get_or_create(
                    workspace=workspace, platform=platform
                )

                file_columns = request.POST.getlist("custom-object-file-column", [])
                # file_columns_name = request.POST.getlist(
                #     "custom-object-file-column-name", []
                # )
                sanka_properties = request.POST.getlist(
                    "custom-object-sanka-properties", []
                )
                ignores = request.POST.getlist("custom-object-ignore", [])

                mapping_custom_fields = {}

                custom_object_mapping = {}

                for idx, file_column in enumerate(file_columns):
                    # check ignore
                    custom_object_mapping[file_column] = sanka_properties[idx]
                    if ignores[idx] == "True":
                        continue
                    mapping_custom_fields[file_column] = sanka_properties[idx]
                if mapping.input_data:
                    data = mapping.input_data
                else:
                    data = {}
                for i, field in enumerate(custom_object_mapping):
                    field_data = {
                        "value": custom_object_mapping[field],
                        "skip": ignores[i],
                    }
                    data[field] = field_data
                mapping.input_data = data
                mapping.save()

                # added sanka_id
                platform_object_label = request.POST.get("platform_object_label")
                mapping_custom_fields[f"sanka_id|string|{platform_object_label}"] = "id"

                if lang == "ja":
                    task_name = f"{channel.integration.slug} の 請求書をエクスポート"
                else:
                    task_name = f"Export {channel.integration.slug} invoice"

                history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="export_invoice",
                    name=task_name,
                    channel=channel or None,
                )
                logger.info(f"Passing mapping field = {mapping_custom_fields}")

                param = {
                    "function": "export_invoice_custom_object",
                    "job_id": str(uuid.uuid4()),
                    "workspace_id": str(workspace.id),
                    "user_id": str(request.user.id),
                    "args": [
                        f"--user={request.user.id}",
                        f"--platform={channel.integration.slug}",
                        f"--channel_id={channel_id}",
                        f"--mapping_custom_fields={mapping_custom_fields}",
                        f"--platform_object_type_id={platform_object_type_id}",
                        f"--hubspot_group_name={hubspot_group_name}",
                        f"--invoice_ids={invoice_ids}",
                        f"--lang={lang}",
                        f"--history_id={history.id}",
                    ],
                }
                is_running = trigger_bg_job(param, transfer_history=history)
                if is_running:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="請求書をHubspotにエクスポートしています。",
                            type="success",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Invoice being exported to Hubspot.",
                            type="success",
                        )
                else:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="請求書をHubspotにエクスポート中にエラーが発生しました。",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="There is an error while exporting invoice to Hubspot.",
                            type="error",
                        )

            except Exception as e:
                print(f"ERROR === invoices.py -- 682: {e}")
        elif platform == "moneyforward":
            invoice_ids = request.POST.getlist("item_ids", [])
            channel_id = request.POST.get("select_integration_ids", None)
            channel = None
            if channel_id:
                channel = Channel.objects.filter(id=channel_id).first()
                if not channel:
                    channel = None

            if import_export_type == "import":
                if lang == "ja":
                    task_name = "MoneyForward の 請求書をインポート"
                else:
                    task_name = "Import MoneyForward invoice"

                TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="import_invoice",
                    name=task_name,
                    channel=channel or None,
                )

                mapping_custom_fields_status = {}
                # Mapping Status
                file_columns_status = request.POST.getlist(
                    "order-status-file-column", []
                )
                # file_columns_name_status = request.POST.getlist(
                #     "order-status-file-column-name", []
                # )
                sanka_properties_status = request.POST.getlist(
                    "order-status-sanka-properties", []
                )
                for idx, file_column in enumerate(file_columns_status):
                    mapping_custom_fields_status[file_column] = sanka_properties_status[
                        idx
                    ]

                pull_moneyforward_invoices(channel_id, mapping_custom_fields_status)
            else:
                if lang == "ja":
                    task_name = "MoneyForward の 請求書をエクスポート"
                else:
                    task_name = "Export MoneyForward invoice"

                TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="export_invoice",
                    name=task_name,
                    channel=channel or None,
                )

                push_moneyforward_invoices(channel_id, invoice_ids)

        elif platform == "salesforce":
            if import_export_type == "import":
                from data.invoice.background.import_salesforce_invoices import (
                    ImportSalesforceInvoicesPayload,
                    import_salesforce_invoices_task,
                )

                try:
                    # Get custom object selection
                    selected_custom_object = request.POST.get(
                        "custom_object_options", None
                    )
                    if not selected_custom_object:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="カスタムオブジェクトを選択してください。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Please select a custom object.",
                                type="error",
                            )
                        return redirect(
                            reverse(
                                "load_object_page",
                                kwargs={
                                    "module_slug": "invoices",
                                    "object_slug": "invoices",
                                },
                            )
                        )

                    # Create transfer history
                    if lang == "ja":
                        task_name = f"Salesforce カスタムオブジェクト ({selected_custom_object}) から請求書をインポート"
                    else:
                        task_name = f"Import invoices from Salesforce custom object ({selected_custom_object})"

                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_invoices",
                        name=task_name,
                        channel=channel,
                    )

                    # Get field mapping
                    invoice_mapping = {}
                    file_columns = request.POST.getlist("custom-object-file-column", [])
                    sanka_properties = request.POST.getlist(
                        "custom-object-sanka-properties", []
                    )
                    ignores = request.POST.getlist("custom-object-ignore", [])

                    for idx, file_column in enumerate(file_columns):
                        if idx < len(sanka_properties) and idx < len(ignores):
                            if ignores[idx] != "True":
                                invoice_mapping[file_column] = sanka_properties[idx]

                    invoice_mapping_str = json.dumps(invoice_mapping)

                    # Get filter settings
                    enable_salesforce_filter = request.POST.get(
                        "enable_salesforce_filter", False
                    )
                    salesforce_field = request.POST.get("salesforce_field", None)
                    salesforce_filter = request.POST.get("salesforce_filter", None)

                    # Create payload for background job
                    payload = ImportSalesforceInvoicesPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        custom_object_name=selected_custom_object,
                        history_id=str(history.id),
                        invoice_mapping=invoice_mapping_str,
                        language=lang,
                        enable_salesforce_filter=enable_salesforce_filter,
                        salesforce_field=salesforce_field,
                        salesforce_filter=salesforce_filter,
                        salesforce_checkpoint=0,
                    )

                    # Create background job
                    job_id = create_bg_job(
                        workspace=workspace,
                        user=request.user,
                        function_name="import_salesforce_invoices",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id

                    # Log import job parameters for debugging
                    logger.info(
                        f"IMPORT_SALESFORCE_INVOICES: Starting invoice import for user {request.user.email} in workspace {workspace.id}"
                    )
                    logger.info(
                        f"IMPORT_SALESFORCE_INVOICES: Import parameters - function: import_salesforce_invoices, job_id: {job_id}"
                    )
                    logger.info(
                        f"IMPORT_SALESFORCE_INVOICES: Channel ID: {channel.id}, History ID: {history.id}, Language: {lang}"
                    )
                    logger.info(
                        f"IMPORT_SALESFORCE_INVOICES: Custom Object: {selected_custom_object}"
                    )
                    logger.info(
                        f"IMPORT_SALESFORCE_INVOICES: Mapping: {invoice_mapping_str}"
                    )
                    logger.info(
                        f"IMPORT_SALESFORCE_INVOICES: Filter - enabled: {enable_salesforce_filter}, field: {salesforce_field}, value: {salesforce_filter}"
                    )

                    try:
                        ref = import_salesforce_invoices_task.run_no_wait(input=payload)
                    except Exception as e:
                        logger.error(
                            f"IMPORT_SALESFORCE_INVOICES: Exception occurred during import_salesforce_invoices_task: {str(e)}",
                            exc_info=True,
                        )
                        ref = None

                    is_running = None
                    if ref:
                        logger.info(
                            f"IMPORT_SALESFORCE_INVOICES: Background job submitted successfully for user {request.user.email}"
                        )
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        logger.error(
                            f"IMPORT_SALESFORCE_INVOICES: Failed to submit background job for user {request.user.email}"
                        )
                        is_running = False

                    if is_running:
                        logger.info(
                            f"IMPORT_SALESFORCE_INVOICES: Successfully submitted import job for user {request.user.email}"
                        )
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message=f"Salesforce カスタムオブジェクト ({selected_custom_object}) から請求書をインポートしています。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message=f"Importing invoices from Salesforce custom object ({selected_custom_object}).",
                                type="success",
                            )
                    else:
                        logger.error(
                            f"IMPORT_SALESFORCE_INVOICES: Background job submission failed for user {request.user.email}"
                        )
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="請求書のインポートジョブの開始に失敗しました。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Failed to start invoice import job.",
                                type="error",
                            )

                except Exception as e:
                    logger.error(
                        f"IMPORT_SALESFORCE_INVOICES: General exception in Salesforce invoice import: {str(e)}",
                        exc_info=True,
                    )
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message=f"請求書のインポート中にエラーが発生しました: {str(e)}",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message=f"Error occurred during invoice import: {str(e)}",
                            type="error",
                        )
            else:
                # Handle export if needed (currently not implemented)
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Salesforce への請求書エクスポートは現在サポートされていません。",
                        type="info",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Invoice export to Salesforce is not currently supported.",
                        type="info",
                    )
        elif platform == "stripe":
            if import_export_type == "import":
                module_slug = request.POST.get("module", "")
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVOICE]
                module = Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_INVOICE
                ).order_by("order", "created_at")
                if module_slug:
                    module = module.filter(slug=module_slug).first()
                else:
                    module = module.first()
                module_slug = module.slug

                request.POST._mutable = True
                request.POST["action_slug"] = "import-stripe-invoices"
                request.POST["redirect_url"] = reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                request.POST["bulk_action"] = True
                request.POST["object_type"] = TYPE_OBJECT_INVOICE

                request.POST._mutable = False

                from data.actions import run_action_view_bg

                return run_action_view_bg(request)

    # Collecting no id of item to obtain its id
    invoices_no_id = base_model.objects.filter(
        **{id_field + "__isnull": True, "workspace": workspace}
    ).order_by("created_at")
    if invoices_no_id:
        for invoice in invoices_no_id:
            invoice.save()

    invoices = base_model.objects.filter(filter_conditions)

    try:
        if view_filter.sort_order_by:
            order_method = view_filter.sort_order_method
            order_by = view_filter.sort_order_by
            if is_valid_uuid(order_by):
                field_name = custom_model.objects.filter(id=order_by).first()
                if field_name:
                    custom_value_subquery = custom_value_model.objects.filter(
                        **{
                            custom_value_relation: OuterRef("pk"),
                            "field_name": field_name,
                        }
                    )
                    if field_name.type in ["date", "date_time"]:
                        custom_value_subquery = custom_value_subquery.values(
                            "value_time"
                        )[:1]
                    else:
                        custom_value_subquery = custom_value_subquery.values("value")[
                            :1
                        ]

                    invoices = invoices.annotate(
                        custom_value=Subquery(custom_value_subquery)
                    )

                    if field_name.type in ["date", "date_time"]:
                        invoices = invoices.annotate(
                            null_field=Case(
                                When(custom_value__isnull=True, then=Value(1)),
                                default=Value(0),
                                output_field=BooleanField(),
                            )
                        )
                    else:
                        invoices = invoices.annotate(
                            null_field=Case(
                                When(**{"custom_value": ""}, then=Value(1)),
                                default=Value(0),
                                output_field=BooleanField(),
                            )
                        )

                    if order_method == "asc":
                        invoices = invoices.order_by("null_field", "custom_value")
                    else:
                        invoices = invoices.order_by("null_field", "-custom_value")

            else:
                # process converting order_by customers__contact__first_name to customer|contact|first_name
                if "customers__" in order_by:
                    order_by = order_by.replace("__", "|").replace(
                        "customers", "customer"
                    )

                if order_by == "customers":
                    invoices = invoices.annotate(
                        null_field_contact=Case(
                            When(contact__name__isnull=True, then=Value(1)),
                            default=Value(0),
                            output_field=BooleanField(),
                        ),
                        null_field_company=Case(
                            When(company__name__isnull=True, then=Value(1)),
                            default=Value(0),
                            output_field=BooleanField(),
                        ),
                    )

                    if order_method == "asc":
                        invoices = invoices.order_by(
                            "null_field_contact",
                            "contact__name",
                            "null_field_company",
                            "company__name",
                        )
                    else:
                        invoices = invoices.order_by(
                            "-null_field_contact",
                            "-contact__name",
                            "-null_field_company",
                            "-company__name",
                        )
                elif "customer|" in order_by:
                    obj_type = order_by.split("|")[1]
                    obj_column = order_by.split("|")[2]
                    if obj_column == "first_name":
                        obj_column = "name"

                    invoices = invoices.annotate(
                        null_field_contact=Case(
                            When(contact__name__isnull=True, then=Value(1)),
                            default=Value(0),
                            output_field=BooleanField(),
                        ),
                        null_field_company=Case(
                            When(company__name__isnull=True, then=Value(1)),
                            default=Value(0),
                            output_field=BooleanField(),
                        ),
                    )

                    sub_order_by = ""
                    sub_order_null_field = ""
                    if obj_type == "contact":
                        sub_order_by = "contact__" + obj_column
                        sub_order_null_field = "null_field_contact"
                        invoices = invoices.order_by(sub_order_null_field, sub_order_by)
                    elif obj_type == "company":
                        sub_order_by = "company__" + obj_column
                        sub_order_null_field = "null_field_company"
                        invoices = invoices.order_by(sub_order_null_field, sub_order_by)

                    if order_method == "asc":
                        invoices = invoices.order_by(sub_order_null_field, sub_order_by)
                    else:
                        invoices = invoices.order_by(
                            sub_order_null_field, "-" + sub_order_by
                        )

                else:
                    invoices = invoices.annotate(
                        null_field=Case(
                            When(**{f"{order_by}__isnull": True}, then=Value(1)),
                            default=Value(0),
                            output_field=BooleanField(),
                        )
                    )

                    if order_method == "asc":
                        invoices = invoices.order_by("null_field", order_by)
                    else:
                        invoices = invoices.order_by("null_field", "-" + order_by)
        else:
            if id_field:
                invoices = invoices.distinct("id", f"{id_field}").order_by(
                    f"-{id_field}"
                )
            else:
                invoices = invoices.distinct("id").order_by("-created_at")
    except Exception:
        if id_field:
            invoices = invoices.distinct("id", f"{id_field}").order_by(f"-{id_field}")
        else:
            invoices = invoices.distinct("id").order_by("-created_at")

    total_invoices = len(invoices)
    paginator_item_begin = 1
    paginator_item_end = 30
    page_content = None
    paginator = None
    page_location = page_obj["page_location"]
    paginator = Paginator(invoices, show_per_page)
    more_pagination = False
    if invoices:
        if page:
            page_content = paginator.page(int(page))
            invoices = page_content.object_list
            paginator_item_begin = (show_per_page * int(page)) - (show_per_page - 1)
            paginator_item_end = show_per_page * int(page)
            more_pagination = page_content.has_next()
        else:
            page_content = paginator.page(1)
            invoices = page_content.object_list
            paginator_item_begin = show_per_page * 1 - (show_per_page - 1)
            paginator_item_end = show_per_page * 1
            more_pagination = page_content.has_next()

    if "json_response" in request.GET:
        res = []
        for invoice in invoices:
            invoice_id = getattr(invoice, id_field, None)
            invoice_id = f"{invoice_id:04d}"
            customers = ""
            if invoice.contact:
                customers = invoice.contact.name
                if invoice.contact.last_name:
                    if lang == "ja":
                        customers = invoice.contact.last_name + " " + customers
                    else:
                        customers = customers + " " + invoice.contact.last_name
            elif invoice.company:
                customers = invoice.company.name

            customers_display = f"#{invoice_id} - {customers}"
            res.append(
                {
                    "id": str(invoice.id),
                    "text": customers_display,
                }
            )

        context = {"results": res, "pagination": {"more": more_pagination}}
        return JsonResponse(context)

    try:
        app_setting = AppSetting.objects.get(
            workspace=workspace, app_target=COMMERCE_APP_TARGET
        )
    except AppSetting.DoesNotExist:
        app_setting = None

    isopen = None
    contact_id = None
    company_id = None
    if id:
        isopen = id
    if request.GET.get("id"):
        target = request.GET.get("target", None)
        if not target:
            isopen = request.GET.get("id")
        else:
            if target == "company":
                company_id = request.GET.get("id")
            if target == "contacts":
                contact_id = request.GET.get("id")

    est_id = request.GET.get("est_id", None)
    inv_id = request.GET.get("inv_id", None)
    receipt_id = request.GET.get("receipt_id", None)

    menu_key = None
    object_slug = None
    if request.path.startswith("/modules/") or request.path.startswith("/ja/modules/"):
        try:
            menu_key = request.path.split("/")[-3]
            object_slug = request.path.split("/")[-2]
        except:
            pass
    else:
        object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVOICE]
        module = Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_INVOICE
        ).order_by("order", "created_at")

        module_slug = request.POST.get("module", "")
        if module_slug:
            module = module.filter(slug=module_slug)

        if module:
            module = module.first()
            menu_key = module.slug

    temp_view_filter_col = view_filter.column if view_filter else None
    if temp_view_filter_col:
        temp_view_filter_col = ast.literal_eval(temp_view_filter_col)
        if "usage_status" in temp_view_filter_col:
            temp_view_filter_col.remove("usage_status")
            view_filter.column = json.dumps(temp_view_filter_col)
            view_filter.save()

    # Handle undefined default property
    # Checking and creating
    get_default_property_set(TYPE_OBJECT_INVOICE, workspace, lang)
    property_sets = PropertySet.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_INVOICE
    ).order_by("created_at")

    set_id = request.GET.get("set_id")
    if view_filter and view_filter.view and view_filter.view.form:
        set_id = view_filter.view.form.id
    else:
        if not set_id:
            property_set_default = property_sets.filter(as_default=True).first()
            if property_set_default:
                set_id = property_set_default.id

    members = []
    groups = request.user.group_set.all()
    for group in groups:
        ids = group.user.all().values_list("id", flat=True)
        members.extend(ids)

    members = list(set(members))
    members = ",".join(str(id) for id in members)

    context = {
        "paginator_item_begin": paginator_item_begin,
        "paginator_item_end": paginator_item_end,
        "page_content": page_content,
        "page": page,
        "page_title": page_title,
        "paginator": paginator,
        "app_slug": COMMERCE_APP_SLUG,
        "workspace": workspace,
        "total": total_invoices,
        "app_setting": app_setting,
        # NOTE: @hira29 naming convention
        "invoices": invoices,
        "view": view,
        "isopen": isopen,
        "contact_id": contact_id,
        "company_id": company_id,
        "NameCustomField": custom_model.objects.filter(workspace=workspace).order_by(
            "order"
        ),
        "search_q": search_q,
        "views": views,
        "current_view": view,
        "view_filter": view_filter,
        "view_id": view.id,
        "status": request.GET.get("status"),
        "est_id": est_id,
        "inv_id": inv_id,
        "receipt_id": receipt_id,
        "permission": permission,
        "open_drawer": request.GET.get("open_drawer", None),
        "pagination_url": reverse(
            "load_object_page",
            host="app",
            kwargs={"module_slug": menu_key, "object_slug": object_slug},
        ),
        "side_drawer": request.GET.get("sidedrawer"),
        "module": menu_key,
        "advance_search": advance_search,
        "group_members": members,
    }

    if "source_integration" in request.GET:
        context["object_type"] = request.GET.get("object_type")
        context["source_integration"] = request.GET.get("source_integration")
        context["channel_id"] = channel.id
        context["userId"] = userId
        context["hs_language"] = request.GET.get("hs_language")

    if "page_request" in request.GET:
        return render(request, page_location, context)

    if return_context:
        return context
    return render(request, "data/invoice/invoice.html", context)


def sync_invoice_header_extractor(request):
    """
    Extract headers for Salesforce invoice import mapping.
    Similar to sync_case_header_extractor but for invoices.
    """
    from utils.salesforce.invoices import get_invoice_mapping_fields
    from data.models import InvoiceMappingFields, InvoiceNameCustomField

    workspace = get_workspace(request.user)

    if request.method == "POST":
        selected_custom_object = request.POST.get("selected_custom_object", None)

        if not selected_custom_object:
            return HttpResponse("Custom object not selected", status=400)

        mapping_type = request.POST.get("mapping_type", "custom_object")
        platform_columns = []

        if "action_index" in request.POST:
            action_index = request.POST.get("action_index")
            select_integration_ids = request.POST.get(
                f"select_integration_ids-{action_index}"
            )
        else:
            select_integration_ids = request.POST.get("select_integration_ids")

        try:
            channel = Channel.objects.get(id=select_integration_ids)
        except Exception:
            return HttpResponse(404)

        header_list = []
        function_type = request.POST.get("function_type", None)
        default_mapping_field = None

        if mapping_type == "custom_object":
            if channel.integration.slug == "salesforce":
                header_list = []
                mapping_fields = None

                if selected_custom_object:
                    mapping_fields = get_invoice_mapping_fields(
                        str(channel.id), selected_custom_object
                    )

                if mapping_fields:
                    # Convert invoice mapping fields to header_list format
                    for field in mapping_fields:
                        field_name = field.get("name", "")
                        field_label = field.get("label", field_name)

                        # Skip system fields and certain lookup fields
                        if field_name not in ["sanka_id", "platform", "platform_id"]:
                            default_value = ""

                            # Set default mappings for common invoice fields
                            if field_name in ["Name", "Subject", "Title"]:
                                default_value = "create_new"  # Most custom objects use custom field names
                            elif field_name in ["Amount", "Total", "Amount__c"]:
                                default_value = "total_price"
                            elif field_name in ["DueDate", "Due_Date__c", "CloseDate"]:
                                default_value = "due_date"
                            elif field_name in ["Account", "AccountId", "Company__c"]:
                                default_value = "company"
                            elif field_name in ["Contact", "ContactId", "Contact__c"]:
                                default_value = "contact"
                            elif field_name in ["Status", "Stage", "Status__c"]:
                                default_value = "status"
                            elif field_name in ["OwnerId"]:
                                default_value = "owner"
                            elif field_name in ["CreatedDate", "Created_Date__c"]:
                                default_value = "start_date"
                            elif field_name in [
                                "Description",
                                "Notes",
                                "Description__c",
                            ]:
                                default_value = "notes"
                            elif field_name in ["Currency", "CurrencyIsoCode"]:
                                default_value = "currency"
                            else:
                                # Custom fields will be created as new
                                default_value = "create_new"

                            header_list.append(
                                {
                                    "value": field_name,
                                    "name": field_label,
                                    "name_ja": field_label,  # Using same label for Japanese
                                    "skip": False,
                                    "default": default_value,
                                }
                            )

            # Platform columns for invoices
            platform_columns = [
                "create_new",
                "customer",
                "item_name",
                "item_price",
                "item_amount",
                "currency",
                "total_price",
                "due_date",
                "company",
                "contact",
                "status",
                "notes",
                "currency",
                "start_date",
            ]
            invoicenamecustomfield = (
                InvoiceNameCustomField.objects.filter(workspace=workspace)
                .exclude(type__in=["image", "user", "formula"])
                .values_list("name", flat=True)
            )
            platform_columns.extend(invoicenamecustomfield)

            # Sub Property (none for invoices currently)
            sub_property_columns = {}

        try:
            mapping, _ = InvoiceMappingFields.objects.get_or_create(
                workspace=workspace, platform=channel.integration.slug
            )
            if mapping.input_data:
                for item in header_list:
                    if item["value"] in mapping.input_data:
                        item["default"] = mapping.input_data[item["value"]]["value"]
                        item["skip"] = mapping.input_data[item["value"]]["skip"]
        except Exception:
            pass

        template_path = "data/shopturbo/sync-invoices-import-mapping.html"
        context = {
            "header_list": header_list,
            "platform_columns": platform_columns,
            "sub_property_columns": sub_property_columns,
            "default_mapping_field": default_mapping_field,
            "function_type": function_type,
            "mapping_type": mapping_type,
            "object_type": "invoices",
            "channel": channel,
            "selected_custom_object": selected_custom_object,
            "platform": channel.integration.slug,
        }

        return render(request, template_path, context)

    return HttpResponse("Method not allowed", status=405)
