from django.db.models import QuerySet
from data.models import ShopTurboOrders, ShopTurboDecimalPoint
from data.models.workspace import Workspace
import math
from typing import List
from django.db import transaction

def order_total_fix(order: ShopTurboOrders, workspace: Workspace):
    # NOTE: Checking Generate Decimal Point Mode. This is to fix the amount issue when Decimal Point got updated.
    general_decimal_point = ShopTurboDecimalPoint.objects.filter(
        workspace=workspace, app_name="general"
    ).first()
    
    line_items = order.shopturboitemsorders_set.all()
    
    if general_decimal_point:
        total_price = 0
        total_price_tax = 0
        # Need Fast Calculation
        if line_items:
            for line_item in line_items:
                if order.line_item_type == 'item_based_tax':
                    if order.tax_inclusive:
                        # Tax-inclusive: derive tax-exclusive price from inclusive price
                        line_item_total_price_tax = line_item.item_price_order * line_item.number_item
                        line_item_total_price = line_item_total_price_tax / (1 + line_item.item_price_order_tax/100)
                    else:
                        # Tax-exclusive: calculate inclusive price from exclusive price
                        line_item_total_price = line_item.item_price_order * line_item.number_item
                        line_item_total_price_tax = line_item_total_price * (1 + line_item.item_price_order_tax/100)
                else:
                    tax_rate = order.tax/100 if order.tax else 0
                    
                    if order.tax_inclusive:
                        # Tax-inclusive: derive tax-exclusive price from inclusive price
                        line_item_total_price_tax = line_item.total_price
                        line_item_total_price = line_item_total_price_tax / (1 + tax_rate) if tax_rate > 0 else line_item_total_price_tax
                    else:
                        # Tax-exclusive: calculate inclusive price from exclusive price
                        line_item_total_price = line_item.total_price
                        line_item_total_price_tax = line_item_total_price * (1 + tax_rate)
                
                # Apply decimal point formatting if needed
                if general_decimal_point.type in ['line_item_cut_off', 'line_item_cut_over']:
                    math_func = math.floor if general_decimal_point.type == 'line_item_cut_off' else math.ceil
                    line_item_total_price = math_func(line_item_total_price)
                    line_item_total_price_tax = math_func(line_item_total_price_tax)
                
                total_price += line_item_total_price
                total_price_tax += line_item_total_price_tax
        else:
            tax_rate = order.tax/100 if order.tax else 0
            
            if order.tax_inclusive:
                # Tax-inclusive: derive tax-exclusive price from inclusive price
                total_price_tax = order.total_price_without_tax  # This field name might be misleading if tax_inclusive=True
                total_price = total_price_tax / (1 + tax_rate) if tax_rate > 0 else total_price_tax
            else:
                # Tax-exclusive: calculate inclusive price from exclusive price
                total_price = order.total_price_without_tax
                total_price_tax = total_price * (1 + tax_rate)
                
        if general_decimal_point.type == 'cut_off':
            total_price = math.floor(total_price)
            total_price_tax = math.floor(total_price_tax)
        elif general_decimal_point.type == 'cut_over':
            total_price = math.ceil(total_price)
            total_price_tax = math.ceil(total_price_tax)
            
        # Check if total_price_without_tax and total_price are equal
        if order.total_price_without_tax != total_price or order.total_price != total_price_tax:
            order.total_price_without_tax = total_price
            order.total_price = total_price_tax
            order.save(update_fields=["total_price_without_tax", "total_price"])
            
    return order


async def orders_total_fix_bulk_async(orders: QuerySet[ShopTurboOrders], workspace: Workspace) -> List[ShopTurboOrders]:
    """
    Async version of orders_total_fix_bulk for efficient processing of multiple orders.
    Uses abulk_update to minimize database queries and native async operations.
    
    Args:
        orders: QuerySet of ShopTurboOrders to fix
        workspace: Workspace instance
        
    Returns:
        List of updated orders
    """
    # NOTE: Checking Generate Decimal Point Mode. This is to fix the amount issue when Decimal Point got updated.
    general_decimal_point = await ShopTurboDecimalPoint.objects.filter(
        workspace=workspace, app_name="general"
    ).afirst()
    
    if not general_decimal_point:
        return [order async for order in orders]
    
    # Prefetch related line items to avoid N+1 queries (async)
    orders_with_prefetch = orders.prefetch_related('shopturboitemsorders_set')
    
    orders_to_update = []
    updated_orders = []
    
    # Async iteration over QuerySet
    async for order in orders_with_prefetch:
        # Get line items asynchronously
        line_items = [item async for item in order.shopturboitemsorders_set.all()]
        
        total_price = 0
        total_price_tax = 0
        
        # Need Fast Calculation
        if line_items:
            for line_item in line_items:
                if order.line_item_type == 'item_based_tax':
                    if order.tax_inclusive:
                        # Tax-inclusive: derive tax-exclusive price from inclusive price
                        line_item_total_price_tax = line_item.item_price_order * line_item.number_item
                        line_item_total_price = line_item_total_price_tax / (1 + line_item.item_price_order_tax/100)
                    else:
                        # Tax-exclusive: calculate inclusive price from exclusive price
                        line_item_total_price = line_item.item_price_order * line_item.number_item
                        line_item_total_price_tax = line_item_total_price * (1 + line_item.item_price_order_tax/100)
                else:
                    tax_rate = order.tax/100 if order.tax else 0
                    
                    if order.tax_inclusive:
                        # Tax-inclusive: derive tax-exclusive price from inclusive price
                        line_item_total_price_tax = line_item.total_price
                        line_item_total_price = line_item_total_price_tax / (1 + tax_rate) if tax_rate > 0 else line_item_total_price_tax
                    else:
                        # Tax-exclusive: calculate inclusive price from exclusive price
                        line_item_total_price = line_item.total_price
                        line_item_total_price_tax = line_item_total_price * (1 + tax_rate)
                
                # Apply decimal point formatting if needed
                if general_decimal_point.type in ['line_item_cut_off', 'line_item_cut_over']:
                    math_func = math.floor if general_decimal_point.type == 'line_item_cut_off' else math.ceil
                    line_item_total_price = math_func(line_item_total_price)
                    line_item_total_price_tax = math_func(line_item_total_price_tax)
                
                total_price += line_item_total_price
                total_price_tax += line_item_total_price_tax
        else:
            tax_rate = order.tax/100 if order.tax else 0
            
            if order.tax_inclusive:
                # Tax-inclusive: derive tax-exclusive price from inclusive price
                total_price_tax = order.total_price_without_tax  # This field name might be misleading if tax_inclusive=True
                total_price = total_price_tax / (1 + tax_rate) if tax_rate > 0 else total_price_tax
            else:
                # Tax-exclusive: calculate inclusive price from exclusive price
                total_price = order.total_price_without_tax
                total_price_tax = total_price * (1 + tax_rate)
                
        if general_decimal_point.type == 'cut_off':
            total_price = math.floor(total_price)
            total_price_tax = math.floor(total_price_tax)
        elif general_decimal_point.type == 'cut_over':
            total_price = math.ceil(total_price)
            total_price_tax = math.ceil(total_price_tax)
            
        # Check if updates are needed and prepare for bulk update
        needs_update = False
        if order.total_price_without_tax != total_price:
            order.total_price_without_tax = total_price
            needs_update = True
        
        if order.total_price != total_price_tax:
            order.total_price = total_price_tax
            needs_update = True
            
        if needs_update:
            orders_to_update.append(order)
            
        updated_orders.append(order)
    
    # Perform bulk update if there are orders to update (async)
    if orders_to_update:
        await ShopTurboOrders.objects.abulk_update(
            orders_to_update, 
            ['total_price_without_tax', 'total_price'],
            batch_size=1000  # Process in batches to avoid memory issues
        )
            
    return updated_orders