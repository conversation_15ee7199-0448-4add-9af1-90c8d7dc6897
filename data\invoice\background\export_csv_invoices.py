import ast
import asyncio
import csv
import io
import json
from datetime import timed<PERSON><PERSON>
from typing import Optional

from django.core.mail import EmailMessage
from django.db.models import Q
from django.template.loader import render_to_string
from django.utils import timezone
from pydantic import BaseModel, Field

from data.constants.constant import INVOICE_COLUMNS_DISPLAY
from data.constants.properties_constant import TYPE_OBJECT_INVOICE
from data.models import (
    CompanyNameCustomField,
    Contact,
    ContactsNameCustomField,
    CustomProperty,
    Invoice,
    InvoiceItem,
    InvoiceNameCustomField,
    InvoiceValueCustomField,
    TransferHistory,
    User,
    View,
    ViewFilter,
    Workspace,
)
from data.templatetags.customtags.custom_field_tags import get_custom_property_object
from utils.contact import (
    get_company_downloadable_value,
    get_contact_downloadable_value,
    get_order_customer_custom_property,
)
from utils.date import format_date, parse_date
from utils.discord import DiscordNotification
from utils.utility import is_valid_uuid, remove_unsupported_characters, customer_converter
from utils.logger import logger
from utils.bgjobs.hatchet_client import hatchet
from hatchet_sdk import Context
from utils.bgjobs.handler import set_bg_job_running, set_bg_job_completed, set_bg_job_failed


class ExportCSVInvoicesPayload(BaseModel):
    user_id: str
    workspace_id: str
    view_id: Optional[str] = None
    history_id: Optional[str] = None
    columns: Optional[str] = None
    filter_dictionary: Optional[str] = None
    language: Optional[str] = None
    encoded_format: Optional[str] = Field(default="utf-8")
    record_ids: Optional[str] = None
    background_job_id: Optional[str] = Field(default="")
    
    # Backward compatibility (Deprecated soon)
    function: Optional[str] = Field(default="")
    workspace: Optional[str] = Field(default="")
    job_id: Optional[str] = Field(default="")
    payload: Optional[dict] = Field(default_factory=dict)


@hatchet.task(name="ExportCSVInvoices", input_validator=ExportCSVInvoicesPayload, execution_timeout=timedelta(hours=12), schedule_timeout=timedelta(hours=1))
def export_csv_invoices(input: ExportCSVInvoicesPayload, ctx: Context):
    workspace_id = input.workspace_id
    view_id = input.view_id
    history_id = input.history_id
    columns = input.columns
    lang = input.language or 'ja'
    record_ids = input.record_ids
    encoded_format = input.encoded_format or 'utf-8'
    
    if input.background_job_id or input.job_id:
        set_bg_job_running(input.background_job_id or input.job_id)
    
    try:
        logger.info(f"[DEBUG] - export_csv_invoices - Starting export for workspace {workspace_id}")
        
        # Get workspace and user
        workspace = Workspace.objects.get(id=workspace_id)
        user = User.objects.get(id=input.user_id)
        
        logger.info(f"[DEBUG] - export_csv_invoices - Found workspace: {workspace.name}, user: {user.email}")
        
        # Get history if provided
        history = None
        if history_id:
            try:
                history = TransferHistory.objects.get(id=history_id)
                logger.info(f"[DEBUG] - export_csv_invoices - Found history: {history.name}")
            except TransferHistory.DoesNotExist:
                logger.warning(f"[DEBUG] - export_csv_invoices - History not found: {history_id}")
        
        # Get view
        view = None
        if view_id and is_valid_uuid(view_id):
            try:
                view = View.objects.get(id=view_id, workspace=workspace)
                logger.info(f"[DEBUG] - export_csv_invoices - Found view: {view.title}")
            except View.DoesNotExist:
                logger.warning(f"[DEBUG] - export_csv_invoices - View not found: {view_id}")
        
        # Base model and custom field model
        base_model = Invoice
        custom_model = InvoiceNameCustomField
        item_model = InvoiceItem
        custom_value_model = InvoiceValueCustomField
        columns_display = INVOICE_COLUMNS_DISPLAY.copy()
        field_item_name = "invoice"

        namecustomfields = (
            custom_model.objects.filter(workspace=workspace)
            .order_by("order")
            .values_list("id", flat=True)
        )
        namecustomfields = [str(data) for data in namecustomfields]
        # Build filter conditions
        filter_conditions = Q(workspace=workspace, usage_status="active")
        
        # Parse and apply filter dictionary
        filter_dictionary = input.filter_dictionary or "{}"
        try:
            filter_dict = json.loads(filter_dictionary)
            logger.info(f"[DEBUG] - export_csv_invoices - Filter dictionary: {filter_dict}")
        except json.JSONDecodeError:
            logger.warning(f"[DEBUG] - export_csv_invoices - Invalid filter dictionary: {filter_dictionary}")
            filter_dict = {}
        
        # Apply view filter if available
        if view:
            view_filter, _ = ViewFilter.objects.get_or_create(view=view)
            view_filter.filter_value = filter_dictionary
            
            # Apply view filter using the generic build_view_filter function
            from utils.filter import build_view_filter
            try:
                filter_conditions = build_view_filter(
                    filter_conditions, view_filter, TYPE_OBJECT_INVOICE)
            except Exception as e:
                # Fallback if the function doesn't work
                logger.warning(f"[DEBUG] - export_csv_invoices - build_view_filter failed: {e}, using basic filter")
        
        if record_ids:
            logger.info(
                f"[DEBUG] - export_csv_invoices - Record IDs provided: {record_ids}")
            filter_conditions &= Q(id__in=record_ids.split(","))

        # Build filter with dummy view filter
        dummy_view_filter = ViewFilter(
            view=view,
            filter_value=filter_dict
        )
        filter_conditions = build_view_filter(filter_conditions, dummy_view_filter, TYPE_OBJECT_INVOICE)
        logger.info(f"[DEBUG] - export_csv_invoices - build filter view {filter_conditions}")
        
        # Get objects
        objects = base_model.objects.filter(filter_conditions).order_by("-created_at")
        
        logger.info(f"[DEBUG] - export_csv_invoices - Processing {objects.count()} invoices")
        
        # Parse columns
        if columns:
            column_list = columns.split(",")
        else:
            column_list = ["id_inv", "total_price", "currency", "status"]
        
        logger.info(f"[DEBUG] - export_csv_invoices - Columns: {column_list}")
        
        # Create CSV buffer
        csv_buffer = io.StringIO()
        writer = csv.writer(csv_buffer)
        
        # Write header
        header_row = []
        for column in column_list:
            logger.info(f"Processing column: {column}")
            if column == "id_inv":
                header_row.append("Invoice ID" if lang == "en" else "請求書ID")
            elif column == "total_price":
                header_row.append("Total Price" if lang == "en" else "合計金額")
            elif column == "currency":
                header_row.append("Currency" if lang == "en" else "通貨")
            elif column == "status":
                header_row.append("Status" if lang == "en" else "ステータス")
            elif column == "company":
                header_row.append("Company" if lang == "en" else "会社")
            elif column == "contact":
                header_row.append("Contact" if lang == "en" else "連絡先")
            elif column == "created_at":
                header_row.append("Created Date" if lang == "en" else "作成日")
            elif column == "due_date":
                header_row.append("Due Date" if lang == "en" else "支払期限")
            else:
                # Fix un-dynamic column names
                col = column
                if is_valid_uuid(col):
                    try:
                        custom_field = custom_model.objects.get(id=col)
                        col = custom_field.name
                        print(f"UUID column mapped to: {col}")
                    except Exception as e:
                        print(f"Error processing UUID column: {e}")
                        pass
                elif "customer|" in col:
                    try:
                        col_parts = col.split("|")
                        col_display = get_order_customer_custom_property(
                            col_parts, workspace, lang
                        )
                        if col_display:
                            col = col_display["name"]
                            print(
                                f"Customer column mapped to: {col}")
                    except Exception as e:
                        print(
                            f"Error in getting custom property: {e}")
                elif col in columns_display:
                    col = columns_display[col][lang]
                    print(f"Display column mapped to: {col}")
                column = col

                header_row.append(column)
        
        if lang == "ja":
            header_row = [remove_unsupported_characters(
                col) for col in header_row]
        writer.writerow(header_row)
        
        # Write data rows
        for obj in objects:
            row = []
            for col in column_list:
                col = col.lower()
                print(f"Processing column for object: {col}")
                col_data = None

                if "item" in col:
                    item_names = []
                    filter_val = Q(**{field_item_name: obj})
                    items = item_model.objects.filter(
                        filter_val).order_by("created_at")
                    for item in items:
                        if item.item_link:
                            item_names.append(
                                f"#{'%04d' % item.item_link.item_id} {item.item_link.name}"
                            )
                        elif item.item_name:
                            item_names.append(item.item_name)
                    col_data = "\n".join(item_names) if len(
                        item_names) > 0 else None

                elif "owner" in col:
                    if obj.owner and obj.owner.user:
                        col_data = obj.owner.user.first_name

                elif "customer|company|" in col and obj.company:
                    company_custom_field_ids = CompanyNameCustomField.objects.filter(
                        workspace=workspace
                    ).values_list("id", flat=True)
                    company_custom_field_ids = [
                        str(v) for v in company_custom_field_ids
                    ]

                    column = col.replace("customer|company|", "")
                    company = obj.company.__dict__
                    col_data = get_company_downloadable_value(
                        company,
                        column,
                        company_custom_field_ids,
                        workspace.timezone,
                        lang,
                    )

                elif "customer|contact|" in col and obj.contact:
                    contact_custom_field_ids = ContactsNameCustomField.objects.filter(
                        workspace=workspace
                    ).values_list("id", flat=True)
                    contact_custom_field_ids = [
                        str(v) for v in contact_custom_field_ids
                    ]
                    column = col.replace("customer|contact|", "")
                    contact = obj.contact.__dict__
                    col_data = get_contact_downloadable_value(
                        contact,
                        column,
                        contact_custom_field_ids,
                        workspace.timezone,
                        lang,
                    )

                elif "customer" in col or col == "partner":
                    print(
                        f"Processing partner/customer column for object: {obj.id}"
                    )
                    print(
                        f"Object has contact: {hasattr(obj, 'contact') and obj.contact is not None}"
                    )
                    print(
                        f"Object has company: {hasattr(obj, 'company') and obj.company is not None}"
                    )
                    col_data = customer_converter(obj, lang)
                    print(f"Partner/customer result: {col_data}")

                elif col in [str(v.name) for v in base_model._meta.fields]:
                    col_data = getattr(obj, col, None)
                    if col_data and col_data != "None":
                        if "amount" in col or "price" in col:
                            currency = getattr(obj, "currency", "JPY")
                            if currency == "JPY":
                                col_data = int(col_data)
                            else:
                                col_data = "{:.2f}".format(col_data)
                        elif col == 'status':
                            if get_custom_property_object(col, obj):
                                status_display = ast.literal_eval(
                                    get_custom_property_object(col, obj).value)
                                col_data = status_display[obj.status]
                            else:
                                from data.templatetags.custom_tags import get_default_status_display
                                status_display = get_default_status_display(
                                    obj, col)
                                col_data = status_display[obj.status][lang] if status_display else obj.status
                        elif any(item in col for item in ["_at", "date"]):
                            col_data = format_date(
                                col_data, lang, include_time=True, tz=workspace.timezone
                            )

                elif col in namecustomfields:
                    filter_val = Q(**{field_item_name: obj})
                    filter_val &= Q(field_name__id=col)
                    value_custom_field = custom_value_model.objects.filter(
                        filter_val
                    ).last()
                    if value_custom_field:
                        if value_custom_field.field_name.type == "image":
                            if value_custom_field.file:
                                col_data = value_custom_field.file.url
                        elif value_custom_field.field_name.type == "contact":
                            try:
                                _contact = Contact.objects.filter(
                                    id=value_custom_field.value
                                ).first()
                                if not _contact:
                                    continue
                                col_data = f"#{'%04d' % _contact.contact_id} "
                                if lang == "ja":
                                    if _contact.last_name:
                                        col_data += f" {_contact.last_name}"
                                    if _contact.name:
                                        col_data += f"{_contact.name}"
                                else:
                                    if _contact.name:
                                        col_data += f"{_contact.name}"
                                    if _contact.last_name:
                                        col_data += f" {_contact.last_name}"

                            except:
                                pass
                        elif value_custom_field.field_name.type in [
                            "date",
                            "date_time",
                        ]:
                            date_value = parse_date(value_custom_field.value)
                            col_data = format_date(
                                date_value,
                                lang,
                                include_time=(
                                    value_custom_field.field_name.type == "date_time"
                                ),
                            )
                        else:
                            col_data = value_custom_field.value
                row.append(col_data)

            if lang == "ja":
                row = [
                    remove_unsupported_characters(str(cell)) if cell else cell
                    for cell in row
                ]

            writer.writerow(row)
        
        logger.info(f"[DEBUG] - export_csv_invoices - CSV generation completed")
        
        # Prepare email data
        sending_data = csv_buffer.getvalue()

        # Always encode the data as bytes with the specified encoding
        if isinstance(sending_data, str):
            sending_data = sending_data.encode(encoded_format)

        mimetype = 'text/csv'

        if lang == 'ja':
            mail_subject = 'Sanka - エクスポートCSV - 請求書'
            curr_datetime = timezone.now().strftime('%Y年%m月%d日_%H:%M:%S')
            filename = f'Sanka_請求書エクスポート_{curr_datetime}.csv'
        else:
            mail_subject = 'Sanka - Export CSV - Invoices'
            curr_datetime = timezone.now().strftime('%Y-%m-%d_%H:%M:%S')
            filename = f'Sanka_Export_Invoices_{curr_datetime}.csv'

        # Send Email
        if lang == 'ja':
            message = render_to_string('data/email/export-orders-ja.html')
        else:
            message = render_to_string('data/email/export-orders.html')

        from_email = 'Sanka <<EMAIL>>'
        to_email = [user.email]

        email_message = EmailMessage(
            mail_subject,
            message,
            from_email,
            to_email
        )

        email_message.attach(filename, sending_data, mimetype)

        # Enhanced logging for email debugging
        logger.info(f"[EMAIL DEBUG] Attempting to send email...")
        logger.info(f"[EMAIL DEBUG] From: {from_email}")
        logger.info(f"[EMAIL DEBUG] To: {to_email}")
        logger.info(f"[EMAIL DEBUG] Subject: {mail_subject}")

        try:
            result = email_message.send(fail_silently=False)
            logger.info(f"[EMAIL DEBUG] Email send result: {result}")
            logger.info(f"[EMAIL DEBUG] Email sent successfully!")
        except Exception as email_error:
            logger.error(f"[EMAIL DEBUG] Email send failed: {email_error}")
            logger.error(f"[EMAIL DEBUG] Email error type: {type(email_error).__name__}")
            # Re-raise the exception to maintain original behavior
            raise email_error

        # Update history status
        if history:
            if lang == 'ja':
                history.name = f"請求書エクスポート: {objects.count()}件のレコード"
            else:
                history.name = f"Export Invoices: {objects.count()} records"
            history.status = "completed"
            history.save()
            logger.info(f"[DEBUG] - export_csv_invoices - History updated: {history.name}")

        # Mark background job as completed
        if input.background_job_id or input.job_id:
            set_bg_job_completed(input.background_job_id or input.job_id)

        logger.info(f"[DEBUG] - export_csv_invoices - Export completed successfully")

    except Exception as e:
        logger.error(f"[DEBUG] - export_csv_invoices - Error occurred: {str(e)}", exc_info=True)
        
        # Update history status to failed
        if history_id:
            try:
                history = TransferHistory.objects.get(id=history_id)
                history.status = "failed"
                history.save()
            except TransferHistory.DoesNotExist:
                pass
        
        # Mark background job as failed
        if input.background_job_id or input.job_id:
            set_bg_job_failed(input.background_job_id or input.job_id, str(e))
        
        # Send Discord notification for debugging
        try:
            discord_notification = DiscordNotification()
            discord_notification.send_message(f"Invoice CSV Export Failed: {str(e)}")
        except:
            pass
        
        raise e
