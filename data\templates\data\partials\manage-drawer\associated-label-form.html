{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}
{% generate_uuid as my_uuid %}


<div class="fv-rowd-flex flex-column mb-8">
    <label class="{% include 'data/utility/form-label.html' %}">
        <span class="">
            
            {% if not association_label.created_by_sanka %}
                {% if LANGUAGE_CODE == 'ja' %}
                    {{association_label.label_ja}}
                {% else %}
                    {{association_label.label}}
                {% endif %}
            {% else %}
                {% with args=association_label.label|stringify|add:'|'|add:page_group_type %} 
                    {% with column_display=args|get_column_display:request %}
                        {{column_display.name}}
                    {% endwith %}
                {% endwith %}
            {% endif %}
        
        </span>
    </label>
    
    <div class="mb-2">
        <select name="association_label#{{association_label.id}}" class="bg-white form-select form-select-solid border h-40px select2-this" 
        {% if LANGUAGE_CODE == 'ja' %}
        data-placeholder="アソシエーションを選択"
        {% else %}
        data-placeholder="Select Association"
        {% endif %}
        data-allow-clear="true"
        {% if association_label.association_type == 'many_to_many' %}multiple{% endif %}
        > 
        <option value=""></option>
        {% for object in companies %}
            <option value="{{object.id}}#company" {% if object.id|stringify in selected_associations %}selected{% endif %}>
               #{{object.company_id|stringformat:"04d"}} | {{object.name}} ({% if LANGUAGE_CODE == 'ja'%}企業{% else %}Company{% endif %})
            </option>
        {% endfor %}

        {% for object in contacts %}
            <option value="{{object.id}}#contacts" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.contact_id|stringformat:"04d"}} | {{object|display_contact_name:LANGUAGE_CODE}} ({% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %})
            </option>
        {% endfor %}

        {% for object in subscriptions %}
            <option value="{{object.id}}#commerce_subscription" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.subscriptions_id|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}サブスクリプション{% else %}Subscription{% endif %})
            </option>
        {% endfor %}

        {% for object in estimates %}
            <option value="{{object.id}}#estimates" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.id_est|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}見積もり{% else %}Estimate{% endif %})
            </option>
        {% endfor %}

        {% for object in purchaseorders %}
            <option value="{{object.id}}#purchaseorder" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.id_po|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}注文書{% else %}Purchase Order{% endif %})
            </option>
        {% endfor %}

        {% for object in invoices %}
            <option value="{{object.id}}#invoices" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.id_inv|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}請求書{% else %}Invoice{% endif %})
            </option>
        {% endfor %}

        {% for object in inventory_transactions %}
            <option value="{{object.id}}#commerce_inventory_transaction" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.transaction_id|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}在庫取引{% else %}Inventory Transaction{% endif %})
            </option>
        {% endfor %}

        {% for object in cases %}
            <option value="{{object.id}}#customer_case" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.deal_id|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}案件{% else %}Case{% endif %})
            </option>
        {% endfor %}

        {% for object in delivery_notes %}
            <option value="{{object.id}}#delivery_slips" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.id_ds|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}納品書{% else %}Delivery Note{% endif %})
            </option>
        {% endfor %}

        {% for object in orders %}
            <option value="{{object.id}}#commerce_orders" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.order_id|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}注文書{% else %}Order{% endif %})
            </option>
        {% endfor %}

        {% for object in custom_objects %}
            <option value="{{object.id}}#{{object.custom_object.id}}" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.row_id|stringformat:"04d"}} | ({{object.custom_object.name}})
            </option>
        {% endfor %}

        {% for object in tasks %}
            <option value="{{object.id}}#task" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                {{object.project_target.title}} | #{{ object.task_id|stringformat:"04d" }} - {{object.title}} | ({% if LANGUAGE_CODE == 'ja'%}タスク{% else %}Task{% endif %})
            </option>
        {% endfor %}

        {% for object in receipts %}
            <option value="{{object.id}}#receipts" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.id_rcp|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}支払{% else %}Payment{% endif %})
            </option>
        {% endfor %}

        {% for object in bills %}
            <option value="{{object.id}}#bill" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.id_bill|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}支払請求{% else %}Bill{% endif %})
            </option>
        {% endfor %}

        {% for object in expenses %}
            <option value="{{object.id}}#expense" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.id_pm|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}経費{% else %}Expense{% endif %})
            </option>
        {% endfor %}

        {% for object in inventory %}
            <option value="{{object.id}}#commerce_inventory" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.inventory_id|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}在庫{% else %}Inventory{% endif %})
            </option>
        {% endfor %}

        {% for object in items %}
            <option value="{{object.id}}#commerce_items" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.item_id|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}商品{% else %}Item{% endif %})
            </option>
        {% endfor %}

        {% for object in inventory_warehouses %}
            <option value="{{object.id}}#commerce_inventory_warehouse" {% if object.id|stringify in selected_associations %}selected{% endif %}>
                #{{object.id_iw|stringformat:"04d"}} | ({% if LANGUAGE_CODE == 'ja'%}倉庫{% else %}Warehouse{% endif %})
            </option>
        {% endfor %}

        </select>
    </div>
</div>

<script>
    $(document).ready(function () {
        $('.select2-this').select2();
    });

</script>