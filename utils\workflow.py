from django.db.models import Q

from data.constants.workflow_constant import DJANGO_FIELD_TYPE_MAPPING
from data.models import *


def get_nodes(obj):
    """Return ordered workflow or task nodes."""
    nodes = []
    if isinstance(obj, Workflow):
        total_nodes = len(ActionNode.objects.filter(workflow=obj))
        node = ActionNode.objects.filter(workflow=obj, is_base=True).first()
    else:
        try:
            total_nodes = len(ActionNode.objects.filter(workflow_history=obj))
            node = ActionNode.objects.filter(
                workflow_history=obj, is_base=True).first()
        except Exception:
            total_nodes = len(ActionNode.objects.filter(task=obj))
            node = ActionNode.objects.filter(
                workflow_history=obj, is_base=True).first()
    i = 1
    is_annomaly = False
    while node:
        if i > total_nodes:
            is_annomaly = True
            break
        nodes.append(node)
        if node.next_node:
            node = node.next_node
        else:
            node = None
        i += 1

    if is_annomaly:
        if isinstance(obj, Workflow):
            nodes_ = ActionNode.objects.filter(workflow=obj)
            for node in nodes_:
                if node not in nodes:
                    node.delete()
        else:
            nodes_ = ActionNode.objects.filter(workflow_history=obj)
            for node in nodes_:
                if node not in nodes:
                    node.delete()
    return nodes


def generate_action_nodes(obj, action_ids, inputs, predefined_input, previous_output_data, metadata):
    print("Generate action nodes")
    print(f'{inputs=}')
    if isinstance(obj, Workflow):
        old_nodes = ActionNode.objects.filter(workflow=obj)
    else:
        old_nodes = ActionNode.objects.filter(task=obj)

    if old_nodes:
        old_nodes.delete()

    prev_node = None
    first_action = Action.objects.get(uid=action_ids[0])
    i = 1
    if first_action.is_trigger:
        i = 0
    print(i)

    for loop_index, action_id in enumerate(action_ids):
        action = Action.objects.get(uid=action_id)
        if isinstance(obj, Workflow):
            node = ActionNode.objects.create(workflow=obj, action=action)
        else:
            node = ActionNode.objects.create(task=obj, action=action)

        if predefined_input and str(i) in predefined_input:
            node.predefined_input = predefined_input[str(i)]
        if previous_output_data and str(i) in previous_output_data:
            node.previous_output_data = previous_output_data[str(i)]
        if str(i) in inputs:

            if 'defined_input' in inputs[str(i)]:
                inputs[str(i)].pop('defined_input')
            node.input_data = inputs[str(i)]
        if str(i) in metadata:
            if metadata[str(i)]['display_name']:
                node.display_name.add(
                    *ActionDisplayName.objects.filter(id__in=metadata[str(i)]['display_name']))
            if metadata[str(i)]['user_display_name']:
                node.user_display_name = metadata[str(i)]['user_display_name']
            if metadata[str(i)]['integration']:
                try:
                    integration = Integration.objects.get(
                        id=metadata[str(i)]['integration'])
                    node.integration = integration
                except Exception:
                    pass
            if metadata[str(i)]['dummy_users']:
                dummy_users = DummyUser.objects.filter(
                    id__in=metadata[str(i)]['dummy_users'])
                node.dummy_users.add(*dummy_users)
        if loop_index == 0:
            node.is_base = True
        node.save()
        print(node.__dict__)

        if prev_node:
            prev_node.next_node = node
            prev_node.save()
        prev_node = node
        i += 1


def is_workflow_valid_to_run(workflow=None, task=None, nodes=[]):
    """Check all workflow action nodes run validity."""
    if not (workflow or task or nodes):
        raise ValueError("Require one of workflow or task or action nodes.")
    if not nodes:
        if workflow:
            nodes = get_nodes(workflow)
        else:
            nodes = get_nodes(task)

    is_workflow_valid = True
    if len(nodes) == 0:
        is_workflow_valid = False
        return is_workflow_valid
    elif len(nodes) == 1 and nodes[0].action and nodes[0].action.is_trigger:
        is_workflow_valid = False
        return is_workflow_valid

    for node_ in nodes:
        if not node_.valid_to_run:
            print(
                f'[Warning] There is action node which is invalid to run: {node_.id} {node_.action}.')
            is_workflow_valid = False
            return is_workflow_valid
    return is_workflow_valid


def model_field_to_condition_value_type(field, limited_to: list = None):
    """
    Maps a Django model field to a condition value type based on a predefined mapping.

    Parameters:
    - field: The Django model field to map.
    - limited_to (optional): A list of Django field types to limit the mapping to.

    Returns:
    - The condition value type corresponding to the field, or None if no match is found.
    """
    for django_field, custom_type in DJANGO_FIELD_TYPE_MAPPING.items():
        if limited_to and django_field not in limited_to:
            continue
        if isinstance(field, django_field):
            return custom_type
    return None


def is_action_condition_met(at: ActionTracker):
    """
    Checks if the action condition is met for the given ActionTracker instance and language.

    Parameters:
    - at (ActionTracker): The ActionTracker instance to evaluate.
    - lang (str): The language to use for the evaluation.

    Returns:
    - bool: True if the action condition is met, False otherwise.
    """
    print('checking action condition.')
    node = at.node
    if node.workflow:
        workspace = node.workflow.workspace
    else:
        workspace = node.workflow_history.workspace
    condition_groups = node.condition_group_set.all()
    if not condition_groups:
        print('no condition stage 1')
        condition_groups = node.condition_groups.all()
        if not condition_groups:
            print('no condition at all.')
            return True

    # return True if node is the first node
    if not ActionNode.objects.filter(next_node=node):
        print("Condition not met because current node is the first node.")
        return False

    # For now we only focus on single group and single condition
    condition_group = condition_groups.first()
    condition = condition_group.conditions.first()

    # For now only handle number value
    if condition.value_type not in ['number', 'text']:
        print('condition value type is not number:', condition.value_type)
        return False

    from utils.properties.properties import get_page_object
    page_obj = get_page_object(condition.object_type)
    base_model = page_obj['base_model']
    id_field = page_obj['id_field']

    if not at.input_data:
        print("Condition not met because current node doesn't have any input data.")
        return False

    # FOR NOW USE HARDCODE TO EXPECT THE INPUT FORMAT
    # TODO: revamp and standardize all the action output using Object format. {'<object_type>': {'<id_field>': <value>}}
    if 'order' not in at.input_data and id_field not in at.input_data['order']:
        print("Condition not met because target data is not in input data.")
        return False

    value = at.input_data['order'][id_field]
    objs = base_model.objects.filter(workspace=workspace, **{id_field: value})

    operator = condition.operator
    operator_mapper = {
        'is_empty': '__isnull',
        'is_not_empty': '__isnull',
        'gt': '__gt',
        'lt': '__lt',
        'equal': '',
        'in': '__in',
        'is': '',
        'is_not': 'is_not_negation',
        'contains': '__icontains',
        'does_not_contain': 'does_not_contain_negation',
        'starts_with': '__istartswith',
        'ends_with': '__iendswith',
    }
    operator_str = operator_mapper[operator]
    if operator == 'is_empty':
        value = True
    elif operator == 'is_not_empty':
        value = False
    else:
        if condition.value_type == 'number':
            value = condition.value_number
        else:
            value = condition.value_text

    if 'negation' in operator_str:
        if operator_str == 'is_not_negation':
            filter_condition = ~Q(**{condition.field_name: value})
        elif operator_str == 'does_not_contain_negation':
            filter_condition = ~Q(**{condition.field_name + '__icontains': value})
            
        res = objs.filter(filter_condition)
    else:
        res = objs.filter(
            Q(**{condition.field_name + operator_str: value})).exists()
        
    print('is action condition met result:', res)

    return res


def get_workflows_by_first_action_trigger(action_slug, workspace, action_name='', additional_conditions={}):
    """
    Get workflows that have a specific action trigger as the first node.

    Parameters:
    - action_slug (str): The slug of the action to search for.
    - workspace (Workspace): The workspace to search in.

    Returns:
    - list: A list of Workflow objects that have the specified action as the first node.
    """
    nodes = ActionNode.objects.filter(
        is_base=True,
        action__slug=action_slug,
        workflow__workspace=workspace,
        workflow__is_trigger_active=True,
    ).distinct()
    if additional_conditions:
        nodes = nodes.filter(**additional_conditions)
    if action_name:
        for node in nodes:
            if node.input_data and 'action_name' in node.input_data and node.input_data['action_name'] == action_name:
                pass
            else:
                nodes = nodes.exclude(id=node.id)

    workflows = Workflow.objects.filter(
        id__in=nodes.values_list('workflow', flat=True))
    if not workflows:
        return []
    return workflows


def duplicate_workflow(workflow, workspace, lang='ja'):
    """
    Duplicate a workflow and its associated nodes.

    Parameters:
    - workflow (Workflow): The workflow to duplicate.
    - workspace (Workspace): The workspace to associate the duplicated workflow with.

    Returns:
    - Workflow: The duplicated workflow.
    """
    # Duplicate the workflow
    duplicated_workflow = Workflow.objects.create(
        workspace=workspace,
        title=workflow.title_ja if lang == 'ja' else workflow.title,
        title_ja=workflow.title_ja,
        description=workflow.description,
        description_ja=workflow.description_ja,
        is_trigger_active=workflow.is_trigger_active,
        trigger_every=workflow.trigger_every,
        trigger_type=workflow.trigger_type,
        predefined_input=workflow.predefined_input,
        previous_output_data=workflow.previous_output_data,
        required_action_conditions=workflow.required_action_conditions,
        valid_to_run=workflow.valid_to_run,
    )

    # Duplicate the nodes
    nodes = get_nodes(workflow)
    prev_duplicated_node = None
    for node in nodes:
        duplicated_node = duplicate_node(node, workflow=duplicated_workflow)
        if prev_duplicated_node:
            prev_duplicated_node.next_node = duplicated_node
            prev_duplicated_node.save()
        prev_duplicated_node = duplicated_node

    return duplicated_workflow


def duplicate_node(node, workflow=None, task=None):
    """
    Duplicate a node and its associated nodes.

    Parameters:
    - node (ActionNode): The node to duplicate.
    - workflow (Workflow, optional): The workflow to associate the duplicated node with.
    - task (Task, optional): The task to associate the duplicated node with.

    Returns:
    - ActionNode: The duplicated node.
    """
    # Duplicate the node
    duplicated_node = ActionNode.objects.create(
        workflow=workflow,
        task=task,
        action=node.action,
        is_base=node.is_base,
        input_data=node.input_data,
        predefined_input=node.predefined_input,
        previous_output_data=node.previous_output_data,
        valid_to_run=node.valid_to_run
    )

    return duplicated_node
