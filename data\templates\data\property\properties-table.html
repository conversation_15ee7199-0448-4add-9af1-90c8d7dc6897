{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% generate_uuid as my_uuid %}

<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>

<style>
    td.drag-handle {
        cursor: grab;
    }
    td.drag-handle:active {
        cursor: grabbing;
    }
</style>

<div class="d-flex align-items-end max-md:tw-mb-0 justify-content-between mb-3">
    <h3 >
        {% if LANGUAGE_CODE == 'ja' %}
        プロパティ設定
        {% else %}
        Properties Settings
        {% endif %}
    </h3>
    
    {% if page_group_type != 'conversation' %}
        <div class="max-md:tw-hidden tw-flex me-2 d-flex justify-content-end">
            {% include 'data/static/tootip-search-wrapper.html' %}

            <div class="me-2 search-wrapper {% if search_q %}expanded{% endif %} hover-tooltip">
                <span class="search-wrapper-tooltip hover-tooltip-text">
                    {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}

                </span>
                <div class="d-flex align-items-center">
                    <div class="d-flex mb-0 position-relative align-items-center" style="height: 22px;">
                        <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch(this)">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                            </svg>
                        </span>
                        <input
                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 h-25px me-2"
                        value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                        >
                    </div>
                </div>
            </div>
            <div class="{% include "data/utility/view-menu-search.html" %}">
                <button 
                    onclick="openSearch()"
                    class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                    >
                    <span class="search-wrapper-tooltip hover-tooltip-text">
                        {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                    </span>
                    <span class="tw-flex svg-icon svg-icon-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                            <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                        </svg>
                    </span>
                </button>
                {% include 'data/property/toggleSearch.html' %}
            </div>
        
            <div data-bs-toggle="tooltip" data-bs-placement="right" data-bs-delay-show="1000" data-kt-initialized="1">
                <button class="max-md:tw-mb-3 w-100px align-items-center d-flex btn btn-dark btn-md py-1 rounded-1 view-settings-button"
                    {% if page_group_type == 'session_event' %}
                        hx-get="{% host_url 'event_properties' host 'app'  %}?property_id="
                    {% else %}
                        hx-get="{% host_url 'new_property' host 'app' %}" 
                    {% endif %}
                    hx-target="#view-settings-drawer"
                    {% if p_id %}
                        hx-vals='{"p_id":"{{p_id}}"}'
                    {% endif %}
                    hx-trigger="click"
                    type="button">
                    <span class="svg-icon svg-icon-4">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                            <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                            <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                        </svg>
                    </span>
                    <span class="fs-7 ps-1 fw-bolder w-85px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New
                        {% endif %}
                    </span>
                </button>
            </div>
        </div>

    {% endif %}

</div>

<input hidden type="text" name="page_group_type" value="{{page_group_type}}">

<table id="table-content-{{my_uuid}}" class="{% include "data/utility/table.html" %}">
    <thead class="{% include "data/utility/table-header.html" %}">
        <tr class="align-middle">  
            <th class="max-w-10px"></th>
            <th class="max-w-5px"></th>
            
            <th>
                {% if LANGUAGE_CODE == 'ja' %}名前{% else %}Name{% endif %}
            </th>

            <th>
                {% if LANGUAGE_CODE == 'ja' %}タイプ{% else %}Type{% endif %}
            </th>

            {% if page_group_type == 'session_event' %}
            <th>
                {% if LANGUAGE_CODE == 'ja' %}プロパティ値{% else %}Property Value{% endif %}
            </th>
            {% endif %}

            <th>
                {% if LANGUAGE_CODE == 'ja' %}作成日{% else %}Created At{% endif %}
            </th>

            <th>
                {% if LANGUAGE_CODE == 'ja' %}更新日{% else %}Edit At{% endif %}
            </th>

            <th>
                {% if LANGUAGE_CODE == 'ja' %}編集者{% else %}Edit By{% endif %}
            </th>
        </tr>
    </thead>

    <tbody id="property-list">
        {% for property in default_properties %}
            {% if forloop.last %}
                {% include 'data/property/property-row-partial.html' with border_bottom=True associate=False %}
            {% else %}
                {% include 'data/property/property-row-partial.html' with associate=False %}
            {% endif %}
        {% endfor %}

        {% comment %} {% for property in associate %}
            {% if forloop.last %}
                {% include 'data/property/property-row-partial.html' with border_bottom=True associate=True %}
            {% else %}
                {% include 'data/property/property-row-partial.html' with associate=True %}
            {% endif %}
        {% endfor %}

        {% for property in shopify_sync_properties %}
            {% if forloop.last %}
                {% include 'data/property/property-row-partial.html' with border_bottom=True associate=False %}
            {% else %}
                {% include 'data/property/property-row-partial.html' with associate=False %}
            {% endif %}
        {% endfor %}

        {% for property in auto_association_properties %}
            {% if forloop.last %}
                {% include 'data/property/property-row-partial.html' with border_bottom=True associate=False %}
            {% else %}
                {% include 'data/property/property-row-partial.html' with associate=False %}
            {% endif %}
        {% endfor %}
        
        {% for property in custom_properties %}
            {% include 'data/property/property-row-partial.html' with associate=False %}
        {% endfor %} {% endcomment %}
    
    </tbody>
</table>

{% comment %} Pagination Controls {% endcomment %}
{% if current_page > 1 or default_properties.has_next %}
<div class="d-flex justify-content-between align-items-center mt-4">
    <div class="text-muted">
        {% if LANGUAGE_CODE == 'ja'%}
            {{paginator_item_begin}}–{{paginator_item_end}} の {{pagination.count}} 件
        {% else %}
            Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{pagination.count}} results
        {% endif %}
    </div>

    <nav aria-label="Properties pagination">
        <ul class="pagination pagination-sm mb-0">
            {% if current_page > 1 %}
                <li class="page-item">
                    <button class="page-link"
                        hx-get="{% host_url 'properties' host 'app' %}"
                        hx-vals='{"page_group_type": "{{ page_group_type }}", "page": {{ current_page|add:"-1" }}, "per_page": {{ per_page }}{% if search_q %}, "q": "{{ search_q }}"{% endif %}{% if p_id %}, "p_id": "{{ p_id }}"{% endif %}}'
                        hx-target="#properties-table"
                        hx-swap="innerHTML"
                        hx-trigger="click"
                        type="button">
                        {% if LANGUAGE_CODE == 'ja' %}前{% else %}Previous{% endif %}
                    </button>
                </li>
            {% endif %}

            {% for page_num in pagination.page_range %}
                <li class="page-item {% if page_num == current_page %}active{% endif %}">
                    <button class="page-link"
                        hx-get="{% host_url 'properties' host 'app' %}"
                        hx-vals='{"page_group_type": "{{ page_group_type }}", "page": {{ page_num }}, "per_page": {{ per_page }}{% if search_q %}, "q": "{{ search_q }}"{% endif %}{% if p_id %}, "p_id": "{{ p_id }}"{% endif %}}'
                        hx-target="#properties-table"
                        hx-swap="innerHTML"
                        hx-trigger="click"
                        type="button">
                        {{ page_num }}
                    </button>
                </li>
            {% endfor %}

            {% if current_page < default_properties.paginator.num_pages %}
                <li class="page-item">
                    <button class="page-link"
                        hx-get="{% host_url 'properties' host 'app' %}"
                        hx-vals='{"page_group_type": "{{ page_group_type }}", "page": {{ current_page|add:1 }}, "per_page": {{ per_page }}{% if search_q %}, "q": "{{ search_q }}"{% endif %}{% if p_id %}, "p_id": "{{ p_id }}"{% endif %}}'
                        hx-target="#properties-table"
                        hx-swap="innerHTML"
                        hx-trigger="click"
                        type="button">
                        {% if LANGUAGE_CODE == 'ja' %}次{% else %}Next{% endif %}
                    </button>
                </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}

{% comment %} VIRTUAL BUTTON {% endcomment %}
<button id="send-from-info-button" hidden type="button" class="d-none w-150px align-items-center d-flex btn btn-dark btn-md py-1 rounded-1 apply-to-others-button"
                    
    hx-get="{% host_url 'commerce_settings_apply_to_others' host 'app' %}" 
    
    hx-vals='{"page_group_type":"{{page_group_type}}","button_type":"send-from-info"}'
    hx-trigger="click"
    hx-swap="innerHTML"
    hx-target="#apply-to-others-content"

>
</button> 

<button id="default-notes-button" type="button" class="d-none w-150px align-items-center d-flex btn btn-dark btn-md py-1 rounded-1 apply-to-others-button"
            
    hx-get="{% host_url 'commerce_settings_apply_to_others' host 'app' %}" 
    
    hx-vals='{"page_group_type":"{{page_group_type}}","button_type":"default-notes"}'
    hx-trigger="click"
    hx-swap="innerHTML"
    hx-target="#apply-to-others-content"

>
</button> 

<button id="default-tax-rate-button" type="button" class="d-none w-150px align-items-center d-flex btn btn-dark btn-md py-1 rounded-1 apply-to-others-button"     
    hx-get="{% host_url 'commerce_settings_apply_to_others' host 'app' %}" 
    
    hx-vals='{"page_group_type":"{{page_group_type}}","button_type":"default-tax-rate"}'
    hx-trigger="click"
    hx-swap="innerHTML"
    hx-target="#apply-to-others-content"

>
</button> 

<button id="company-logo-column-button" hidden type="button" class="d-none w-150px align-items-center d-flex btn btn-dark btn-md py-1 rounded-1 apply-to-others-button"
                    
    hx-get="{% host_url 'commerce_settings_apply_to_others' host 'app' %}" 
    
    hx-vals='{"page_group_type":"{{page_group_type}}","button_type":"company-logo"}'
    hx-trigger="click"
    hx-swap="innerHTML"
    hx-target="#apply-to-others-content"

>
</button> 

<button id="company-signature-column-button" hidden type="button" class="d-none w-150px align-items-center d-flex btn btn-dark btn-md py-1 rounded-1 apply-to-others-button"
                    
    hx-get="{% host_url 'commerce_settings_apply_to_others' host 'app' %}" 
    
    hx-vals='{"page_group_type":"{{page_group_type}}","button_type":"company-signature"}'
    hx-trigger="click"
    hx-swap="innerHTML"
    hx-target="#apply-to-others-content"

>
</button> 

{% comment %} ================= {% endcomment %}

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    $(document).ready(function() {
        Sortable.create(document.getElementById('property-list'), {
            handle: '.drag-handle',
            animation: 150,
            onEnd: function (evt) {
                // evt.oldIndex   -> original index
                // evt.newIndex   -> new index
                // evt.item       -> the <tr> element that was moved
                const formData = new FormData();

                formData.append('property_id', evt.item.id);
                formData.append('old_index', evt.oldIndex);
                formData.append('new_index', evt.newIndex);
                formData.append('object_type', "{{page_group_type}}");

                fetch("{% host_url 'manage_reorder_property' host 'app' %}", {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                    },
                    body: formData
                });
            }
        });

        // DEBUG: Log workspace and search information
        console.log('DEBUG PROPERTIES SEARCH: Initializing properties table');
        console.log('DEBUG PROPERTIES SEARCH: Current search_q value:', '{% if search_q %}{{ search_q }}{% else %}(empty){% endif %}');
        console.log('DEBUG PROPERTIES SEARCH: Page group type:', '{{ page_group_type }}');
        console.log('DEBUG PROPERTIES SEARCH: Table element exists:', $('#table-content-{{my_uuid}}').length > 0);
        console.log('DEBUG PROPERTIES SEARCH: Search input element exists:', $('#base-search-input').length > 0);

        try {
            // Check if we have pagination (indicating large dataset)
            var hasPagination = {% if pagination %}true{% else %}false{% endif %};
            var totalItems = {% if pagination %}{{ pagination.total_count }}{% else %}{{ default_properties|length }}{% endif %};

            // Use lighter configuration for large datasets
            var tableConfig = {
                scrollX: true,
                scrollCollapse: true,
                searching: !hasPagination,  // Disable search for paginated results (use server-side search instead)
                paging: false,      // Hide pagination (we handle it manually)
                info: false,        // Hide the information text
                language: {
                    emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                },
                sDom: "ltipr"
            };

            // Only add row reordering for smaller datasets to avoid performance issues
            if (totalItems < 100) {
                tableConfig.rowReorder = {
                    selector: '.task-grip',
                    update: false
                };
            }

            // Add created row callback for styling
            tableConfig.createdRow = function(row, data, dataIndex) {
                if ($(row).hasClass('cell-border-bottom')) {
                    $(row).css({
                        'border-bottom': '2px solid #ddd'
                    });
                }
            };

            var propertiesTable = $("#table-content-{{my_uuid}}").DataTable(tableConfig);

            console.log('DEBUG PROPERTIES SEARCH: DataTable initialized successfully');
            console.log('DEBUG PROPERTIES SEARCH: DataTable instance:', propertiesTable);
        } catch (error) {
            console.error('DEBUG PROPERTIES SEARCH: Error initializing DataTable:', error);
        }

        $('#table-content').on('row-reorder.dt', function(e, diff, edit) {
            var isValid = true;
        
            $.each(diff, function(index, change) {
                var row = $(change.node);
                var firstCell = row.find('td').first();
                
                if (!firstCell.hasClass('task-grip')) {
                    isValid = false;
                    return false; 
                }
            });
        
            if (!isValid) {
                propertiesTable.row(edit.row).invalidate().draw();
                $(edit.node).removeAttr('style'); 
            }
        });

        // Add search functionality with debugging
        $('#base-search-input').on('keyup', function () {
            var searchValue = this.value;
            console.log('DEBUG PROPERTIES SEARCH: Search input keyup event triggered');
            console.log('DEBUG PROPERTIES SEARCH: Search value:', searchValue);
            console.log('DEBUG PROPERTIES SEARCH: DataTable exists:', typeof propertiesTable !== 'undefined');

            try {
                if (typeof propertiesTable !== 'undefined') {
                    var result = propertiesTable.search(searchValue).draw();
                    console.log('DEBUG PROPERTIES SEARCH: Search applied successfully');
                    console.log('DEBUG PROPERTIES SEARCH: Visible rows after search:', propertiesTable.rows({ search: 'applied' }).count());
                } else {
                    console.error('DEBUG PROPERTIES SEARCH: DataTable not available for search');
                }
            } catch (error) {
                console.error('DEBUG PROPERTIES SEARCH: Error during search:', error);
            }
        });

        // Additional debugging for search input focus/blur events
        $('#base-search-input').on('focus', function() {
            console.log('DEBUG PROPERTIES SEARCH: Search input focused');
        });

        $('#base-search-input').on('blur', function() {
            console.log('DEBUG PROPERTIES SEARCH: Search input blurred, current value:', this.value);
        });

        // Log initial search state if search_q is present
        {% if search_q %}
        console.log('DEBUG PROPERTIES SEARCH: Initial search query detected, applying search');
        setTimeout(function() {
            if (typeof propertiesTable !== 'undefined') {
                propertiesTable.search('{{ search_q }}').draw();
                console.log('DEBUG PROPERTIES SEARCH: Initial search applied for query: {{ search_q }}');
            }
        }, 100);
        {% endif %}

    });
</script>

