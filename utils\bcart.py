from datetime import datetime

import requests

from data.models import *
from utils.meter import *
from utils.logger import logger
from django.core.exceptions import MultipleObjectsReturned

def import_bcart_orders(id, mapping_custom_fields=None, key_item_field=None, how_to_import=None, key_customer_field=None, how_to_import_customer=None, import_filter=None):
    channel = Channel.objects.get(id=id)
    access_token = channel.access_token + channel.page_access_token + \
        channel.refresh_token + channel.ms_refresh_token
    order_url = 'https://api.bcart.jp/api/v1/orders'
    platform = channel.integration.slug
    workspace = channel.workspace
    task = TransferHistory.objects.filter(
        workspace=workspace, type='import_order', channel=channel).order_by('-created_at').first()

    order_datas = []
    try:
        order_count = 100
        offset = 0
        break_flag = False
        while (True):
            if break_flag:
                break
            if order_count < 100:
                break_flag = True
            headers = {
                "Authorization": f"Bearer {access_token}"
            }
            order_param = {
                'limit': 100,
                'offset': offset,
                'complete': 1
            }
            start_datetime = None
            end_datetime = None
            if import_filter:
                try:
                    if import_filter.get('created_at'):
                        key = import_filter.get('created_at').get('key')
                        value = import_filter.get('created_at').get('value')
                        if isinstance(value, str):
                            value = datetime.strptime(value, '%Y-%m-%d')

                        if key == 'start_date':
                            start_datetime = value
                        elif key == 'end_date':
                            end_datetime = value
                    elif import_filter.get('updated_at'):
                        key = import_filter.get('updated_at').get('key')
                        value = import_filter.get('updated_at').get('value')
                        if isinstance(value, str):
                            value = datetime.strptime(value, '%Y-%m-%d')

                        if key == 'start_date':
                            start_datetime = value
                        elif key == 'end_date':
                            end_datetime = value
                except:
                    pass
            if start_datetime:
                order_param['ordered_at__gte'] = start_datetime.strftime('%Y-%m-%d %H:%M:%S')
            if end_datetime:
                order_param['ordered_at__lte'] = end_datetime.strftime('%Y-%m-%d %H:%M:%S')
            order_request = requests.get(
                order_url, headers=headers, params=order_param)
            print('order request: ', order_request.json())
            order_data = order_request.json()['orders']
            order_count = len(order_data)
            offset += 100
            order_datas.extend(order_data)
            print('order count: ', len(order_datas))

        print('order length: ', len(order_datas))
        if task:
            task.total_number = len(order_datas)
            task.save()

        for order in order_datas:
            try:
                task = TransferHistory.objects.filter(
                    workspace=workspace, type='import_order', channel=channel).order_by('-created_at').first()
                if task:
                    task.success_number = order_datas.index(order) + 1
                    task.progress = (order_datas.index(order) + 1) / len(order_datas) * 100
                    task.save()
                if task.status == 'canceled':
                    return False
                platform_order_id = order['code']
                if len(order['order_totals']) < 1:
                    continue
                total_price = order['order_totals'][0]['total_incl_tax']
                total_price_without_tax = order['order_totals'][0]['total']
                tax = order['order_totals'][0]['tax_rate']
                order_at = datetime.strptime(
                    order['ordered_at'], "%Y-%m-%d %H:%M:%S")

                order_platform, _ = ShopTurboOrdersPlatforms.objects.get_or_create(
                    channel=channel,
                    platform_order_id=platform_order_id
                )

                if order_platform.order:
                    shopturbo_order = order_platform.order
                else:
                    shopturbo_order = ShopTurboOrders.objects.create(
                        workspace=workspace,
                        currency='JPY',
                        platform=platform,
                        order_type='item_order',
                        status='active'
                    )
                order_platform.order = shopturbo_order
                order_platform.order_at = order_at
                order_platform.save()

                if total_price:
                    shopturbo_order.total_price = float(total_price)
                    shopturbo_order.total_price_without_tax = float(
                        total_price_without_tax)
                    shopturbo_order.item_price_order = float(total_price)
                shopturbo_order.tax = float(tax)
                shopturbo_order.currency = 'JPY'
                shopturbo_order.order_at = order_at

                contact_id = order['customer_id']
                old_contact_id = order['customer_id']
                if order['customer_ext_id']:
                    contact_id = order['customer_ext_id']
                contact_name = order['customer_name']
                contact_email = order['customer_email']
                contact_phone = order['customer_tel']
                company_name = order['customer_comp_name']

                if channel.following == 1:
                    if old_contact_id != contact_id:
                        company_platform = CompanyPlatforms.objects.filter(
                            channel=channel, platform_id=old_contact_id).first()
                        if company_platform:
                            company_platform.platform_id = contact_id
                            company_platform.save()
                        else:
                            company_platform, _ = CompanyPlatforms.objects.get_or_create(
                                channel=channel,
                                platform_id=contact_id,
                            )
                    else:
                        company_platform, _ = CompanyPlatforms.objects.get_or_create(
                            channel=channel,
                            platform_id=contact_id,
                        )
                    update_customer_value = False if how_to_import_customer == 'match' else True

                    if not key_customer_field:
                        key_customer_field = 'None'
                    if company_platform.company:
                        company = company_platform.company
                        if key_customer_field != 'None':
                            custom_field = CompanyNameCustomField.objects.filter(
                                workspace=workspace, name=key_customer_field).first()
                            custom_value = CompanyValueCustomField.objects.filter(
                                field_name=custom_field, value=contact_id).first()
                            if custom_value:
                                company_platform.company = None
                                company_platform.save()
                                company = custom_value.company
                            else:
                                custom_value, _ = CompanyValueCustomField.objects.get_or_create(
                                    field_name=custom_field, company=company)
                                custom_value.value = contact_id
                    else:
                        if key_customer_field != 'None':
                            custom_field = CompanyNameCustomField.objects.filter(
                                workspace=workspace, name=key_customer_field).first()
                            custom_value = CompanyValueCustomField.objects.filter(
                                field_name=custom_field, value=contact_id).first()
                            if custom_value:
                                company = custom_value.company
                                company.status = "active"
                            else:
                                update_customer_value = True
                                company = Company.objects.create(
                                    workspace=workspace,
                                )
                                custom_value, _ = CompanyValueCustomField.objects.get_or_create(
                                    field_name=custom_field, company=company)
                                custom_value.value = contact_id
                                custom_value.save()
                        else:
                            update_customer_value = True
                            company = Company.objects.create(
                                workspace=workspace,
                            )
                    company_platform.company = company
                    company_platform.save()

                    if update_customer_value:
                        company.name = company_name
                        company.phone_number = contact_phone
                        company.email = contact_email
                    company.status = "active"
                    company.save()

                    shopturbo_order.company = company
                    shopturbo_order.save()

                    if mapping_custom_fields and update_customer_value:
                        for field in mapping_custom_fields:
                            try:
                                if '|company' in field:
                                    sanka_field = mapping_custom_fields[field].replace(
                                        '|company', '')
                                    stripped_field = field.replace(
                                        '|company', '')
                                    value = None

                                    if stripped_field in order:
                                        value = order[stripped_field]

                                    custom_field = CompanyNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field).first()
                                    if custom_field and company:
                                        custom_value, _ = CompanyValueCustomField.objects.get_or_create(
                                            field_name=custom_field, company=company)
                                        custom_value.value = value
                                        custom_value.save()
                            except Exception as e:
                                print('Error: ', e)
                                pass
                else:
                    contactplatform, _ = ContactsPlatforms.objects.get_or_create(
                        channel=channel,
                        platform_id=contact_id,
                    )

                    if contactplatform.contact:
                        contact = contactplatform.contact
                    else:
                        contact = Contact.objects.create(
                            workspace=workspace,
                        )
                        contactplatform.contact = contact
                        contactplatform.save()

                    contact.name = contact_name
                    contact.phone_number = contact_phone
                    contact.email = contact_email
                    contact.status = "active"
                    contact.save()

                    shopturbo_order.contact = contact
                    shopturbo_order.save()

                    if mapping_custom_fields:
                        for field in mapping_custom_fields:
                            try:
                                if '|company' in field:
                                    sanka_field = mapping_custom_fields[field].replace(
                                        '|company', '')
                                    stripped_field = field.replace(
                                        '|company', '')
                                    value = None

                                    if stripped_field in order:
                                        value = order[stripped_field]

                                    custom_field = ContactsNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field).first()
                                    if custom_field and contact:
                                        custom_value, _ = ContactsValueCustomField.objects.get_or_create(
                                            field_name=custom_field, contact=contact)
                                        custom_value.value = value
                                        custom_value.save()
                            except Exception as e:
                                print('Error: ', e)
                                pass
                            
                shipping_cost_order = order['shipping_cost']
                try:
                    shipping_cost_float = float(shipping_cost_order)
                except (TypeError, ValueError):
                    shipping_cost_float = 0
                shipping_cost = None
                try:
                    shipping_cost, created = ShopTurboShippingCost.objects.get_or_create(
                        name=f"Shipping Cost {shipping_cost_float}",
                        workspace=workspace,
                        value=shipping_cost_float,
                        number_format="JPY",
                    )
                except MultipleObjectsReturned:
                    shipping_cost = ShopTurboShippingCost.objects.filter(
                        name=f"Shipping Cost {shipping_cost_float}",
                        workspace=workspace,
                        value=shipping_cost_float,
                        number_format="JPY",
                    ).first()
                    logger.warning("Multiple shipping_cost entries found. Using the first one found.")

                shipping_platform = ShopTurboShippingCostPlatforms.objects.filter(
                    shipping_cost=shipping_cost,
                    platform_id=platform_order_id,
                    channel=channel,
                    platform_type='order'
                ).first()

                if not shipping_platform:
                    shipping_platform = ShopTurboShippingCostPlatforms.objects.create(
                        channel=channel,
                        platform_type='order',
                        platform_id=platform_order_id,
                        shipping_cost=shipping_cost,
                    )

                shopturbo_order.shipping_cost = shipping_cost
                shopturbo_order.save()
                            
                commodities = order['order_products']

                # Check if it's a list, if not, convert it into a list
                if not isinstance(commodities, list):
                    commodities = [commodities]

                print('log', order)

                for item in commodities:
                    product_id = item['product_no']
                    number_of_item = int(item['order_pro_count'])
                    item_price = float(item['unit_price'])
                    shopturbo_item_order, _ = ShopTurboItemsOrders.objects.get_or_create(
                        platform_item_id=product_id,
                        order=shopturbo_order,
                        order_platform=order_platform,
                    )
                    update_value = False if how_to_import == 'match' else True

                    if number_of_item:
                        shopturbo_item_order.number_item = number_of_item
                    if item_price:
                        shopturbo_item_order.item_price_order = float(
                            item_price)
                        shopturbo_item_order.total_price = float(
                            item_price*number_of_item)
                        shopturbo_item_order.currency = 'JPY'
                    # shopturbo_item_order.custom_item_name = item['product_name']
                    shopturbo_item_order.save()

                    item_platform, _ = ShopTurboItemsPlatforms.objects.get_or_create(
                        channel=channel,
                        platform_id=product_id,
                        platform_type='default'
                    )
                    if not key_item_field:
                        key_item_field = 'None'
                    if item_platform.item:
                        shopturbo_item = item_platform.item
                        if key_item_field != 'None':
                            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                workspace=workspace, name=key_item_field).first()
                            custom_value = ShopTurboItemsValueCustomField.objects.filter(
                                field_name=custom_field, value=product_id).first()
                            if custom_value:
                                shopturbo_item.product_id = None
                                item_platform.item = None
                                shopturbo_item.save()
                                item_platform.save()
                                shopturbo_item = custom_value.items
                                shopturbo_item.product_id = product_id
                            else:
                                custom_value, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                                    field_name=custom_field, items=shopturbo_item)
                                custom_value.value = product_id
                                custom_value.save()
                    else:
                        if shopturbo_item_order.item:
                            shopturbo_item = shopturbo_item_order.item
                            if key_item_field != 'None':
                                custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                    workspace=workspace, name=key_item_field).first()
                                custom_value, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                                    field_name=custom_field, items=shopturbo_item)
                                custom_value.value = product_id
                                custom_value.save()
                        else:
                            if key_item_field != 'None':
                                custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                    workspace=workspace, name=key_item_field).first()
                                custom_value = ShopTurboItemsValueCustomField.objects.filter(
                                    field_name=custom_field, value=product_id).first()
                                if custom_value:
                                    shopturbo_item = custom_value.items
                                    shopturbo_item.product_id = product_id
                                else:
                                    update_value = True
                                    shopturbo_item = ShopTurboItems.objects.create(
                                        workspace=workspace,
                                        platform=platform,
                                        product_id=product_id,
                                    )
                                    custom_value, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                                        field_name=custom_field, items=shopturbo_item)
                                    custom_value.value = product_id
                                    custom_value.save()
                            else:
                                update_value = True
                                shopturbo_item = ShopTurboItems.objects.create(
                                    workspace=workspace,
                                    platform=platform,
                                    product_id=product_id,
                                    status='active'
                                )
                    item_name = item['product_name']
                    if update_value:
                        if item_name:
                            shopturbo_item.name = item_name
                        if item_price:
                            shopturbo_item.price = float(item_price)
                        shopturbo_item.currency = 'JPY'
                    shopturbo_item.save()

                    item_platform.platform_id = product_id
                    item_platform.item = shopturbo_item
                    item_platform.save()

                    price, _ = ShopTurboItemsPrice.objects.get_or_create(
                        item=shopturbo_item,
                        price=float(item_price),
                        currency='JPY'
                    )

                    check_default = ShopTurboItemsPrice.objects.filter(
                        item=shopturbo_item,
                        default=True
                    )
                    if len(check_default) == 0:
                        price.default = True
                    price.name = item_name
                    price.save()

                    shopturbo_item_order.item = shopturbo_item
                    shopturbo_item_order.item_price = price
                    shopturbo_item_order.save()

                    if mapping_custom_fields:
                        for field in mapping_custom_fields:
                            try:
                                if '|item' in field:
                                    sanka_field = mapping_custom_fields[field].replace(
                                        '|item', '')
                                    stripped_field = field.replace('|item', '')
                                    value = None

                                    if stripped_field in item:
                                        value = item[stripped_field]
                                    elif 'product_customs' in item:
                                        for product_custom in item['product_customs']:
                                            for key_, val_ in product_custom.items():
                                                if key_ == stripped_field:
                                                    value = val_

                                    custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field).first()
                                    if custom_field:
                                        custom_value, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                                            field_name=custom_field, items=shopturbo_item)
                                        custom_value.value = value
                                        custom_value.save()
                            except Exception as e:
                                print('Error: ', e)
                                pass

                if mapping_custom_fields:
                    for field in mapping_custom_fields:
                        try:
                            if '|item' not in field and '|company' not in field:
                                sanka_field = mapping_custom_fields[field]
                                value = None

                                if field in order:
                                    value = order[field]
                                if 'logistics_' in field:
                                    stripped_field = field.replace(
                                        'logistics_', '')
                                    if stripped_field == 'address':
                                        value = order['logistics'][0]['address1'] + \
                                            order['logistics'][0]['address2'] + \
                                            order['logistics'][0]['address3']
                                    else:
                                        value = order['logistics'][0][stripped_field]

                                custom_field = ShopTurboOrdersNameCustomField.objects.filter(
                                    workspace=workspace, name=sanka_field).first()
                                if custom_field:
                                    custom_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                                        field_name=custom_field, orders=shopturbo_order)
                                    custom_value.value = value
                                    custom_value.save()
                        except Exception as e:
                            print('Error: ', e)
                            pass
            except Exception as e:
                logger.info(f'Error import_bcart_orders: {e}')
                pass
            # return False

    except Exception as e:
        print('Error: ', e)

    return True
