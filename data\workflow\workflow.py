import ast
import json
import traceback
import uuid

import pytz
from django.conf import settings
from django.core.paginator import EmptyP<PERSON>, Paginator
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, IntegerField, Char<PERSON>ield, <PERSON><PERSON>ield, Q
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.urls import resolve
from django.utils import timezone
from django.views.decorators.http import require_GET, require_POST
from django_hosts.resolvers import reverse

from action import action as sanka_action
from action.event_trigger import FormValidator
from data.constants.constant import DEFAULT_PERMISSION, OBJECT_GROUP_TYPE
from data.constants.properties_constant import (OBJECT_TYPE_TO_SLUG,
                                                TYPE_OBJECT_BILL,
                                                TYPE_OBJECT_CONTRACT,
                                                TYPE_OBJECT_INVENTORY,
                                                TYPE_OBJECT_ITEM,
                                                TYPE_OBJECT_ORDER,
                                                TYPE_OBJECT_PURCHASE_ORDER,
                                                TYPE_OBJECT_SUBSCRIPTION,
                                                TYPE_OBJECT_TASK,
                                                TYPE_OBJECT_WORKFLOW,
                                                TYPE_OBJECT_INVOICE,
                                                TYPE_OBJECTS)
from data.constants.workflow_constant import (OPERATORS_DISPLAY,
                                              VALUE_TYPE_TO_OPERATORS)
from data.models import (Action, ActionCondition, ActionConditionGroup,
                         ActionDisplayName, ActionHistory, ActionNode,
                         ActionTracker, BackgroundJob, DummyUser, Group,
                         Integration, Invoice, InvoiceMappingFields, Module, Notification, Task, User, View,
                         ViewFilter, Workflow, WorkflowActionTracker, Channel,
                         WorkflowHistory)
from data.models.user import UserManagement
from sanka.settings import LANGUAGES, LOCAL, WORKFLOW_GALLERY_CREATOR_WORKSPACE
from utils.actions import (ActionForm, get_action_id, get_action_output_fields,
                           required_conditions_met)
from utils.decorator import login_or_hubspot_required
from utils.discord import DiscordNotification
from utils.bgjobs.runner import trigger_bg_job
from utils.bgjobs.handler import create_bg_job, add_hatchet_run_id
from data.workflow.background.run_workflow_action import run_workflow_action_task, RunWorkflowActionPayload
from utils.logger import logger
from utils.bgjobs.handler import add_bgjob_log_by_bg_job_id
from utils.project import apply_view_filter, get_ordered_items
from utils.properties.properties import (get_page_object,
                                         get_properties_with_details)
from utils.utility import get_workspace, is_valid_uuid, merge_dict_keys, get_permission_filter, assign_object_owner
from utils.workflow import (get_nodes, is_workflow_valid_to_run,
                            model_field_to_condition_value_type)
from utils.workspace import get_permission
from utils.actions import transfer_output_to_target_input
from utils.hubspot import export_invoice_hubspot_custom_object
from action.action import trigger_next_action
from data.custom_object.co_hubspot import custom_object_hubspot_save_mapping
from data.shopturbo import save_mapped_hubspot_object_name


@login_or_hubspot_required
@require_POST
def studio_workflow(request):
    print(request.POST)
    lang = request.LANGUAGE_CODE
    # Validate request
    workspace = get_workspace(request.user)

    validator = FormValidator(request)
    input = validator.studio_workflow()
    if not input['is_valid']:
        print("Invalid input")
        response = HttpResponse(status=400)
        response['HX-Trigger'] = json.dumps({
            "reloadNotifNumber": "",
            "showValidationMessage": input['error_message_ja'] if lang == 'ja' else input['error_message']
        })
        return response

    if input['data']['submit_option'] == 'duplicate':
        print(input)
        workflow = Workflow.objects.create(
            workspace=workspace,
            title=input['data']['workflow'].title if lang == 'en' else input['data']['workflow'].title_ja,
            title_ja=input['data']['workflow'].title if lang == 'en' else input['data']['workflow'].title_ja,
            status='published',
            trigger_type=input['data']['workflow'].trigger_type
        )

        action_ids = input['data']['action_ids']
        if 'manual' in action_ids:
            action_ids.remove('manual')

        # Update node predefined input
        prev_node = None
        for i, node_ in enumerate(get_nodes(input['data']['workflow'])):
            node = ActionNode.objects.create(
                workflow=workflow,
                action=node_.action,
                cost_minutes=node_.cost_minutes,
                input_data=node_.input_data,
                predefined_input=node_.predefined_input,
                previous_output_data=node_.previous_output_data,
                is_base=node_.is_base,
                valid_to_run=node_.valid_to_run,
            )
            display_name = node_.display_name.filter(language=lang)
            if display_name:
                node.user_display_name = display_name.name,
                node.save()

            print(node.__dict__)
            if prev_node:
                prev_node.next_node = node
                prev_node.save()
            prev_node = node

        msg = 'The workflow data has been successfully saved'
        if lang == 'ja':
            msg = 'ワークフロー データが正常に保存されました。'

        workflow.refresh_from_db()
        # Deactivate the trigger workflow
        if workflow and workflow.is_trigger_active:
            workflow.is_trigger_active = False
            msg = 'The workflow is deactivated and changes has been successfully saved.'
            if lang == 'ja':
                msg = 'ワークフローは非アクティブ化され、変更は正常に保存されました。'

        Notification.objects.create(
            workspace=workspace, user=request.user, message=msg, type="success")
        workflow.valid_to_run = is_workflow_valid_to_run(workflow)
        workflow.is_trigger_active = False
        print(workspace.id)
        print(WORKFLOW_GALLERY_CREATOR_WORKSPACE)
        if str(workspace.id) == WORKFLOW_GALLERY_CREATOR_WORKSPACE:
            workflow.status = 'draft'
            workflow.created_by_sanka = True
        workflow.save()

        # Redirect to home page if not from home
        response = HttpResponse()
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        try:
            module_slug = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_TASK).order_by(
                'order', 'created_at').first().slug
        except:  # noqa
            module_slug = None
        if module_slug:
            response['HX-Redirect'] = reverse('load_object_page', host='app', kwargs={
                                              'module_slug': module_slug, 'object_slug': module_object_slug}) + f"?h_workflow={workflow.id}"
        else:
            response['HX-Redirect'] = reverse('main', host='app')
        return response

    # RUN WORKFLOW (submit-option=='run')
    else:
        from_page = request.POST.get('from')
        view = input['data']['view']
        tasks = []
        if view:
            tasks = Task.objects.filter(workspace=workspace)
            if view.project:
                tasks = tasks.filter(project=view.project)
            filters = ViewFilter.objects.filter(view=view)
            tasks = apply_view_filter(filters, tasks)
            tasks = get_ordered_items(view, tasks)

        workflow_history = input['data']['workflow_history']
        nodes = get_nodes(workflow_history)

        if not is_workflow_valid_to_run(nodes=nodes):
            if lang == 'ja':
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message="ワークフローに不完全な入力または無効な入力があったため、ワークフローをアクティブ化できませんでした。", type="error")
            else:
                Notification.objects.create(workspace=get_workspace(request.user), user=request.user,
                                            message="failed to activate the workflow because the workflow had incomplete or invalid input.", type="error")
            return HttpResponse(status=400)

        params = {}
        wat = WorkflowActionTracker.objects.create(
            workspace=workspace, workflow_history=workflow_history, created_by=request.user, status='running')
        response = HttpResponse()
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
        module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_TASK).order_by(
            'order', 'created_at').first()
        module_slug = None
        if module:
            module_slug = module.slug

        if from_page == 'home':
            if module:
                response['HX-Redirect'] = reverse('load_object_page', host='app', kwargs={
                                                  'module_slug': module_slug, 'object_slug': module_object_slug})+f'?h_task={workflow_history.id}'
            else:
                response['HX-Redirect'] = reverse('main', host='app')
        else:
            response['HX-Redirect'] = reverse('taskflow_in_app', kwargs={
                                              "app_slug": from_page}, host='app')+f'?h_task={workflow_history.id}'

        # Create Action Trackers
        params = {}
        run_node_in_bg = None
        run_node_directly = None
        for i, node in enumerate(nodes, start=0):
            at = ActionTracker.objects.create(
                workspace=workspace, workflow_action_tracker=wat, node=node, status='initialized')
            if node.input_data:
                at.input_data = node.input_data
                at.initial_input_data = node.input_data
            if node.previous_output_data:
                at.previous_output_data = node.previous_output_data
            if i == 0:
                at.status = 'running'
            at.save()

            wat.refresh_from_db()
            if i == 0 or (i > 0 and run_node_directly == nodes[i-1] and wat.status == 'running'):
                params['action-tracker-id'] = str(at.id)
                params['workflow-action-tracker-id'] = str(wat.id)
                params['action-node-id'] = str(node.id)
                params['user'] = request.user.id
                params['lang'] = lang
                run_node_in_bg = node
                print('[Run task workflow] - is run directly',
                      node.action.run_directly)
                if node.action.run_directly:
                    at.status = 'running'
                    at.save()
                    try:
                        sanka_action.run_flow(action_id=str(
                            nodes[0].action.uid), single_task=True, input=params)
                    except Exception:
                        print('[Run Task Action] [ERROR]')
                        at.refresh_from_db()
                        at.status = 'failed'
                        at.save()

                        wat.status = 'failed'
                        wat.save()

                        if lang == 'ja':
                            Notification.objects.create(workspace=get_workspace(
                                request.user), user=request.user, message=f"タスクアクション実行に失敗しました: {workflow_history.title}", type="error")
                        else:
                            Notification.objects.create(workspace=get_workspace(
                                request.user), user=request.user, message=f"Task action failed to run: {workflow_history.title}", type="error")
                        print('Task Action stopped.')
                else:
                    run_node_in_bg = node
                    at.status = 'running'
                    at.save()

            elif wat.status == 'running':
                params['action-tracker-id'] = str(at.id)
                params['workflow-action-tracker-id'] = str(wat.id)
                params['action-node-id'] = str(node.id)
                params['user'] = request.user.id
                params['lang'] = lang
                run_node_in_bg = node
                at.status = 'running'
                at.save()

        if wat.status == 'failed' or not run_node_in_bg:
            return response

        at = ActionTracker.objects.get(
            workspace=workspace, workflow_action_tracker=wat, node=run_node_in_bg)
        if LOCAL:
            # Only run the first action. The rest of actions should be triggered by each of the previous action.
            print('[Run task workflow] - Run action in background',
                  run_node_in_bg.action)
            try:
                sanka_action.run_flow(action_id=str(
                    run_node_in_bg.action.uid), input=params)
            except Exception as e:
                print(f'[Run Task Action] [ERROR] - {e}')
                if at:
                    at.status = 'failed'
                    at.save()

                    wat.status = 'failed'
                    wat.save()

                    if lang == 'ja':
                        Notification.objects.create(workspace=get_workspace(
                            request.user), user=request.user, message=f"タスクアクション実行に失敗しました: {input['data']['workflow_history'].title}", type="error")
                    else:
                        Notification.objects.create(workspace=get_workspace(
                            request.user), user=request.user, message=f"Task action failed to run: {input['data']['workflow_history'].title}", type="error")
                    print('Task Action stopped.')
                    return response

            at.refresh_from_db()
            at = ActionTracker.objects.get(
                workspace=workspace, workflow_action_tracker=wat, node=nodes[0])
            if at.status == 'failed':
                wat.status = 'failed'
                wat.save()
            if at.status == 'success' and not at.node.next_node:
                wat.status = 'success'
                wat.save()
        else:
            params['user_id'] = str(request.user.id)
            # Create payload for background job
            payload = RunWorkflowActionPayload(
                action_node_id=params.get('action-node-id'),
                action_tracker_id=params.get('action-tracker-id'),
                workflow_action_tracker_id=params.get('workflow-action-tracker-id'),
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                workflow_history_id=str(workflow_history.id),
                language=lang,
            )
            
            # Create background job
            job_id = create_bg_job(
                workspace=workspace,
                user=request.user,
                function_name="run_workflow_action",
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id
            
            print(f"Adding run task queue message to bg-job: {payload.model_dump()}")
            
            try:
                ref = run_workflow_action_task.run_no_wait(input=payload)
            except Exception as e:
                logger.error(f"Run Task Workflow: Exception occurred during run_workflow_action_task: {str(e)}", exc_info=True)
                ref = None
            
            is_running = None
            if ref:
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                is_running = False

            if not is_running:
                dn = DiscordNotification()
                dn.send_message(
                    "[Trigger next action] - Send queue to run-task-action failed.")
                at.status = "failed"
                at.save()
                

        print('[Run Task Workflow] - Task running successfully')
        if lang == 'ja':
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message="タスクアクションは正常に実行されています", type="success")
        else:
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message="Task running successfully", type="success")
    return response


@require_GET
@login_or_hubspot_required
def action_form(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    workflow_id = request.GET.get('workflow_id')
    task_id = request.GET.get('task_id')
    node_id = request.GET.get('action_node_id')
    action_index = request.GET.get('action_index')
    trigger_type = request.GET.get('action_trigger_type')
    delete = request.GET.get('delete')
    indexes = request.GET.getlist('action_indexes')
    from_page = request.GET.get('from')
    action_app = request.GET.get('action_app')

    print("Action form request Index: ", action_index)

    if action_index:
        action_index = int(action_index)
    action_id = request.GET.get(f'action_id-{action_index}')
    if not (action_id or node_id or trigger_type or action_app):
        return HttpResponse("何か問題が発生しました。" if lang == 'ja' else "Something went wrong.", status=404)

    if trigger_type == 'manual':
        return HttpResponse()

    try:
        indexes = [int(val) for val in indexes]
    except:  # noqa
        return HttpResponse("何か問題が発生しました。" if lang == 'ja' else "Something went wrong.", status=400)

    if action_index == 0 and trigger_type:
        try:
            workflow = None
            if workflow_id:
                workflow = Workflow.objects.get(id=workflow_id)
                nodes = get_nodes(workflow)
            filter_conditions = (Q(use_in_workflow=True, public=True, workspace__isnull=True, trigger_type=trigger_type) |
                                 Q(use_in_workflow=True, public=True, workspace=workspace, trigger_type=trigger_type)) & ~Q(app_from="")
            actions = Action.objects.filter(
                filter_conditions).order_by('order').distinct()
            actions_app = actions.order_by('app_from', 'order')

            option_list = []
            option_list_value = []

            for action in actions_app:
                # Adding The menu of App Name For the trigger
                if action.app_from not in [TYPE_OBJECT_BILL, TYPE_OBJECT_ORDER, TYPE_OBJECT_INVENTORY, TYPE_OBJECT_SUBSCRIPTION, TYPE_OBJECT_PURCHASE_ORDER, TYPE_OBJECT_CONTRACT, TYPE_OBJECT_ITEM]:
                    continue
                if action.app_from in option_list_value:
                    continue

                option_list_value.append(action.app_from)
                option_list.append(OBJECT_GROUP_TYPE[action.app_from][lang])

            option_list_value.append('event')
            if lang == 'ja':
                option_list.append('その他のイベント')
            else:
                option_list.append('Other Events')

            node = None

            if node_id:
                try:
                    node = ActionNode.objects.get(id=node_id)
                except:  # noqa
                    pass
            context = {
                'node': node,
                'load_saved_action': True if node else False,
                'workflow': workflow,
                'trigger_type': trigger_type,
                'actions': actions.order_by('order'),
                'options': zip(option_list, option_list_value)
            }
            print(context)
            # if 'load_from'in request.GET:
            #     context['load_from'] = request.GET.get('load_from')

            if action_app or trigger_type == 'time' or trigger_type == 'event':
                print('ACTION APP or TIME TRIGGER')
                response = render(
                    request, 'data/ai/form/partial-action-action-node-action-selector.html', context)

            elif trigger_type == 'manual':
                if node_id and delete:
                    try:
                        node = ActionNode.objects.get(id=node_id)
                    except:  # noqa
                        return HttpResponse('')
                    if node.workflow:
                        nodes = get_nodes(node.workflow)
                    elif node.task:
                        nodes = get_nodes(node.task)

                    print('Deleting node')
                    node.delete()
                    if len(nodes) > 1:
                        node = nodes[1]
                        node.is_base = True
                        node.save()

                response = HttpResponse('')
                hx_trigger = {}
                for i in range(len(nodes)-1):
                    hx_trigger[f'reload_previous_output{i}'] = ''
                response['HX-Trigger'] = json.dumps(hx_trigger)
            else:
                print('ELSE RENDER')
                response = render(
                    request, 'data/ai/form/partial-action-action-node-app-selector.html', context)
            return response

        except Exception as e:
            traceback.print_exc()
            print(f'Failed : {e}')

        return HttpResponse("何か問題が発生しました。" if lang == 'ja' else "Something went wrong.", status=500)

    workflow = None
    task = None
    node = None
    prev_action = None
    prev_output_data = None
    required_conditions = None

    if node_id:
        try:
            node = ActionNode.objects.get(id=node_id)
            print(node.task)
        except ActionNode.DoesNotExist:
            print("Action Node Does Not Exist")
            return HttpResponse("何か問題が発生しました。" if lang == 'ja' else "Something went wrong.", status=404)

        if node.workflow:
            workflow = node.workflow

        if node.task:
            task = node.task

        if node.workflow_history:
            workflow_history = node.workflow_history

        if action_id:
            try:
                action = Action.objects.get(uid=action_id)
            except:  # noqa
                try:
                    action = Action.objects.get(slug=action_id)
                except Action.DoesNotExist:
                    print('Action does not exists')
                    return HttpResponse("何か問題が発生しました。" if lang == 'ja' else "Something went wrong.", status=404)
        else:
            action = node.action
            action_id = str(action.uid)
            if action.slug:
                action_id = action.slug

        # Override to valid to run (Trigger Order, Subscription)
        if node.action:
            node.valid_to_run = True
            node.save()

        if node.previous_output_data and action_index > 0:
            # print(workflow.previous_output_data)
            prev_output_data = node.previous_output_data
            prev_action = None
            if workflow:
                try:
                    prev_action = ActionNode.objects.get(
                        workflow=workflow, next_node=node).action
                except Exception as e:
                    print("[Error Alert]: ", e)
            elif workflow_history:
                prev_action = ActionNode.objects.get(
                    workflow_history=workflow_history, next_node=node).action

        if action and action.required_conditions:
            required_conditions = action.required_conditions
        print(f'{node.id=}')
        print(f'{node.predefined_input=}')
        print(f'{node.input_data=}')
        try:
            for key, value in node.predefined_input.items():
                if node.predefined_input[key]['value'] != node.input_data[key]:
                    node.input_data[key] = node.predefined_input[key]['value']
            node.save()
        except:  # noqa
            pass

    else:
        try:
            action = Action.objects.get(uid=action_id)
        except:  # noqa
            try:
                action = Action.objects.get(slug=action_id)
            except Action.DoesNotExist:
                print('Action does not exists')
                return HttpResponse("何か問題が発生しました。" if lang == 'ja' else "Something went wrong.", status=404)

        if action.is_trigger:
            nodes = []
            if workflow_id:
                try:
                    workflow = Workflow.objects.get(id=workflow_id)
                except Workflow.DoesNotExist:
                    return HttpResponse("何か問題が発生しました。" if lang == 'ja' else "Something went wrong.", status=404)
                nodes = get_nodes(workflow)
            elif task_id:
                try:
                    task = Task.objects.get(id=task_id)
                except:  # noqa
                    return HttpResponse("何か問題が発生しました。" if lang == 'ja' else "Something went wrong.", status=404)
                nodes = get_nodes(task)

            node = ActionNode.objects.create(
                action=action,
                is_base=True,
            )
            print("workflow node created")

            # Override to valid to run (Trigger Order, Trigger Draft Subs, Subs)
            node.valid_to_run = True
            node.save()

            if workflow:
                node.workflow = workflow
            # elif task:
            #     node.task = task
            if nodes:
                first_node = nodes[0]
                first_node.is_base = False
                first_node.save()
                node.next_node = first_node
                node.save()

        if action.required_conditions:
            required_conditions = action.required_conditions

    if required_conditions and from_page != 'studio':
        try:
            is_met, html, html_ja = required_conditions_met(
                request, required_conditions=required_conditions)
        except Exception as e:
            print(e)
            return HttpResponse(status=500)
        if not is_met:
            print("REQUIRED CONDITION NOT MET")
            if lang == 'ja':
                response = HttpResponse(html_ja)
            response = HttpResponse(html)
            if action_id or node:
                HxTrigger = {
                    # 'count-cost': ''
                    'getAllPrevOutput': '',
                }

                # Trigger reset next action select previous output options.
                if indexes:
                    start_adding = False
                    for val in indexes:
                        if val == action_index:
                            start_adding = True
                        elif start_adding:
                            print('adding trigger: ',
                                  f'reload_previous_output{val}')
                            HxTrigger[f'reload_previous_output{val}'] = ''
                elif node:
                    nodes = []
                    if node.workflow:
                        nodes = get_nodes(node.workflow)
                    elif node.task:
                        nodes = get_nodes(node.task)
                    if action_index < len(nodes):
                        for i in range(int(action_index)+1, len(nodes)):
                            HxTrigger[f'reload_previous_output{i}'] = ''
                print('HX-Trigger', HxTrigger)
                response['HX-Trigger'] = json.dumps(HxTrigger)
            return response

    if not action:
        return HttpResponse(status=200)

    try:
        print("Action ID:", action_id)
        print("Type Action ID:", type(action_id))

        uuid.UUID(action_id)
    except Exception:
        print("Action ID is a slug: ")
        # Map the action_slug to the view function

        view_func = resolve(reverse('action_form_mapper', host='app')[2:].replace(
            'app.'+settings.PARENT_HOST, '') + (action_id) + '/', urlconf='data.urls').func
        mutable_get = request.GET.copy()
        mutable_get.update({'action_node_id': str(node.id)})
        request.GET = mutable_get
        return view_func(request)

    context = ActionForm(request=request, action=action,
                         node=node, user=request.user).get_context()
    context['action_index'] = action_index
    context['display_names'] = ActionDisplayName.objects.all()
    context['dummy_users'] = DummyUser.objects.all()
    context['integrations'] = Integration.objects.filter()
    context['languages'] = LANGUAGES
    context['from'] = from_page
    if action_index != 1 and not prev_output_data:
        context['show_initial'] = True
    if prev_output_data:
        context['show_form'] = True
        context['input_format'] = prev_output_data
    if prev_action:
        context['output_format'] = merge_dict_keys(prev_action.output_format)
    if workflow:
        context['workflow'] = workflow
    if task:
        context['task'] = task
    if node:
        context['node'] = node
    context['required_conditions'] = required_conditions
    response = render(request, 'data/ai/form/node-action-form.html', context)
    if action_id or node:
        HxTrigger = {
            # 'count-cost': ''
            'getAllPrevOutput': '',
        }

        # Trigger reset next action select previous output options.
        if indexes:
            start_adding = False
            for val in indexes:
                if val == action_index:
                    start_adding = True
                elif start_adding:
                    print('adding trigger: ', f'reload_previous_output{val}')
                    HxTrigger[f'reload_previous_output{val}'] = ''
        elif node:
            nodes = []
            if node.workflow:
                nodes = get_nodes(node.workflow)
            elif node.task:
                nodes = get_nodes(node.task)
            if action_index < len(nodes):
                for i in range(int(action_index)+1, len(nodes)):
                    HxTrigger[f'reload_previous_output{i}'] = ''
        print('HX-Trigger', HxTrigger)
        response['HX-Trigger'] = json.dumps(HxTrigger)
    return response


@require_POST
@login_or_hubspot_required
def action_node_order(request):
    workspace = get_workspace(request.user)
    action_node_ids = request.POST.getlist('action_node_id')
    if not action_node_ids:
        return HttpResponse(status=400)

    action_nodes = ActionNode.objects.filter(id__in=action_node_ids)
    workflow = action_nodes.first().workflow
    task = action_nodes.first().task

    prev_node = None
    for i, node_id in enumerate(action_node_ids):
        for node in action_nodes:
            if str(node.id) == node_id:
                node.is_base = False
                if i == 0:
                    node.is_base = True

                if prev_node:
                    prev_node.next_node = node
                    prev_node.save()

                if i == len(action_node_ids) - 1:
                    node.next_node = None

                node.save()
                prev_node = node

    if workflow:
        nodes = get_nodes(workflow)
    if task:
        nodes = get_nodes(task)

    filter_conditions = Q(use_in_workflow=True, public=True, workspace__isnull=True) | Q(
        use_in_workflow=True, public=True, workspace=workspace)
    actions = Action.objects.filter(
        filter_conditions).order_by('order').distinct()

    context = {
        'nodes': nodes,
        'workflow': workflow,
        'task': task,
        'actions': actions,
    }
    return render(request, 'data/ai/form/partial-action-node-rows.html', context)

    # Sorting without re-rendering action forms
    # HxTrigger = {
    #     'getAllPrevOutput': '',
    #     'getNodeIndex': ''
    # }
    # for i in range(0, len(nodes)+1):
    #     HxTrigger[f'reload_previous_output{i}'] = ''
    # response = HttpResponse(status=200)
    # print(HxTrigger)
    # response['HX-Trigger'] = json.dumps(HxTrigger)
    # return response


@require_POST
@login_or_hubspot_required
def workflow_basic_data(request, id):
    workspace = get_workspace(request.user)
    try:
        workflow = Workflow.objects.get(id=id)
    except:  # noqa
        return HttpResponse(status=404)
    print(request.POST)

    input_name = request.POST.get('name')
    if input_name not in ['slug', 'status', 'title', 'users']:
        print("[Update workflow] [ERROR] - Invalid name.")
        return HttpResponse(status=400)
    if input_name == 'users':
        input_value = request.POST.getlist(input_name)
    else:
        input_value = request.POST.get(input_name)

    if input_name == 'slug':
        workflow.slug = input_value
    elif input_name == 'title':
        workflow.title = input_value
    elif input_name == 'users':
        if input_value:
            try:
                users_list = []
                groups_list = []
                for item in input_value:
                    if item.startswith('user|'):
                        users_list.append(item.replace('user|', ''))
                    elif item.startswith('group|'):
                        groups_list.append(item.replace('group|', ''))

                users = User.objects.filter(
                    workspace=workspace, username__in=users_list)
                projects = Group.objects.filter(
                    workspace=workspace, id__in=groups_list)

                workflow.assignee.clear()
                workflow.assignee.add(*users)

                workflow.project.clear()
                workflow.project.add(*projects)
            except Exception as e:
                print(e)
        else:
            workflow.project.clear()
            workflow.assignee.clear()

    else:
        if input_value not in ['draft', 'published']:
            print("[Update workflow] [ERROR] - Invalid status value.")
            return HttpResponse(status=400)
        workflow.status = input_value
    workflow.save()

    response = HttpResponse(status=200)
    response['HX-Trigger'] = json.dumps({'load_workflows_data': ''})
    return response


@login_or_hubspot_required
def create_workflow_form(request):
    """
    Renders the workflow creation or edit form with context including actions, workflow history, members, projects, suggested filters, permissions, and tab selection.
    
    Handles both new and existing workflows, applies permission logic based on workflow ownership, paginates workflow history, and determines the appropriate tab (history or review) to display. Returns the rendered workflow form template.
    """
    workflow_id = request.GET.get('workflow_id')
    view_id = request.GET.get('view_id')
    from_page = request.GET.get('from')
    check_tab = request.GET.get('check_tab')
    history_tab = request.GET.get('history_tab')
    review_tab = request.GET.get('review_tab')
    print('Set workflow form request')
    if history_tab:
        print('Get History Workflow')

    workspace = get_workspace(request.user)

    view = None
    if view_id:
        try:
            view = View.objects.get(id=view_id)
        except View.DoesNotExist:
            pass

    workflow = None
    if workflow_id:
        try:
            workflow = Workflow.objects.get(id=workflow_id)
        except Workflow.DoesNotExist:
            return HttpResponse(status=400)

    filter_conditions = Q(use_in_workflow=True, public=True, workspace__isnull=True) | Q(
        use_in_workflow=True, public=True, workspace=workspace)
    actions = Action.objects.filter(
        filter_conditions).order_by('order').distinct()
    actions = list(actions)

    projects = Group.objects.filter(workspace=workspace)
    members = workspace.user.all()
    suggested_project = None
    suggested_members = []
    if view:
        filters = ViewFilter.objects.filter(view=view)
        for view_filter in filters:
            if view_filter.property == 'assignee':
                suggested_members = view_filter.value
            if view_filter.property == 'project':
                suggested_project = view_filter.value
    all_history = WorkflowHistory.objects.filter(workflow=workflow)
    # workflow history without action tracker status 'review'
    workflow_history = all_history.exclude(
        workflowactiontracker__actiontracker__status='review').distinct().order_by('-created_at')
    paginator = Paginator(workflow_history, 5)
    workflow_history_page = paginator.page(1)
    workflow_history_list = workflow_history_page.object_list
    workflow_attentions = all_history.filter(
        workflowactiontracker__actiontracker__status='review').distinct().order_by('-created_at')

    object_group_type = 'workflow'
    permission = get_permission(
        object_type=object_group_type, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    if workflow and workflow.owner and workflow.owner.user:
        permission += f'|{workflow.owner.user.id}#{request.user.id}'
        
    context = {
        'actions': actions,
        'workflow': workflow,
        'workflow_history_page': workflow_history_page,
        'workflow_history': workflow_history_list,
        'workflow_attentions': workflow_attentions,
        'view_id': view_id,
        'members': members,
        'projects': projects,
        'suggested_members': suggested_members,
        'suggested_project': suggested_project,
        'from': from_page,
        'is_gallery_creator': str(workspace.id) == WORKFLOW_GALLERY_CREATOR_WORKSPACE,
        'permission': permission,
    }

    if not check_tab:
        if history_tab:
            context['history_tab'] = history_tab
        if review_tab:
            context['review_tab'] = review_tab
    else:
        if workflow_history:
            if workflow_attentions:
                if workflow_history[0].created_at > workflow_attentions[0].created_at:
                    context['history_tab'] = True
                else:
                    context['review_tab'] = True
            else:
                context['history_tab'] = True
        else:
            context['review_tab'] = True

    response = render(request, 'data/projects/workflow-form.html', context)

    # if not workflow_id:
    #     response['HX-Trigger'] = json.dumps({'reloadViewAllWorkflows':{'view_id':str(view_id)}})
    # response['HX-Trigger'] = json.dumps({'reloadViewAllWorkflows':{'view_id':str(view_id)}})
    return response


def load_workflow_history(request):
    workspace = get_workspace(request.user)
    page = request.GET.get('page')
    workflow_id = request.GET.get('workflow')
    if not workflow_id and not page:
        return HttpResponse(status=400)

    try:
        workflow = Workflow.objects.get(workspace=workspace, id=workflow_id)
    except Workflow.DoesNotExist:
        return HttpResponse(status=400)

    all_history = WorkflowHistory.objects.filter(workflow=workflow)
    workflow_history = all_history.exclude(
        workflowactiontracker__actiontracker__status='review').distinct().order_by('-created_at')
    paginator = Paginator(workflow_history, 5)
    workflow_history_page = paginator.page(page)
    workflow_history_list = workflow_history_page.object_list
    context = {
        'workflow': workflow,
        'workflow_history_page': workflow_history_page,
        'workflow_history': workflow_history_list,
        'start_from': 3*page
    }
    return render(request, 'data/ai/partial-workflow-history.html', context)


@login_or_hubspot_required
@require_POST
def action_node(request, id=None):
    """Create or Delete Action Node"""
    workspace = get_workspace(request.user)

    if id:
        try:
            node = ActionNode.objects.get(id=id)
        except:  # noqa
            print("Action Node does not exist.")
            return HttpResponse(status=404)

        workflow_history = node.workflow_history
        workflow = node.workflow

        if node.is_base and node.next_node:
            # Current node is the first node.
            # Set next node as the first node
            node.next_node.is_base = True
            node.next_node.save()
            node.delete()

        elif node.next_node:
            # Current node is on the middle.
            # Set the next node as previous node's next node
            try:
                prev_node = ActionNode.objects.get(next_node=node)
                prev_node.next_node = node.next_node
                prev_node.save()
            except:  # noqa
                pass
            node.delete()
        else:
            # Current node is the last node.
            # Remove the previous node's next node
            try:
                prev_node = ActionNode.objects.get(next_node=node)
                prev_node.next_node = None
                prev_node.save()
            except:  # noqa
                pass
            node.delete()
        response = HttpResponse(status=200)
        hx_trigger = {}
        hx_trigger["getNodeIndex"] = ''
        response['HX-Trigger'] = json.dumps(hx_trigger)
        return response

    # Create Action Node
    obj_type = request.POST.get('object_type')
    action_indexes = request.POST.getlist('action_indexes')
    print('Obj: ', obj_type)
    if obj_type not in ['workflow', 'task'] and not action_indexes:
        print('Wrong object type or empty action indexes.')
        return HttpResponse(status=400)

    workflow = None
    workflow_history = None
    task = None

    if obj_type == 'workflow':
        workflow_id = request.POST.get('workflow_id')
        nodes = []
        if workflow_id:
            try:
                workflow = Workflow.objects.get(id=workflow_id)
            except:  # noqa
                print("Workflow does not exist.")
                return HttpResponse(status=404)

            nodes = get_nodes(workflow)
            node = ActionNode.objects.create(workflow=workflow)
            workflow.valid_to_run = False
            workflow.save()
            if len(nodes) == 0:
                node.is_base = True
                node.save()
        else:
            node = ActionNode.objects.create()

            # Get the previous Action Node(s)
            prev_node = None
            prev_node_ids = request.POST.getlist('action_node_id')
            for prev_node_id in prev_node_ids:
                try:
                    prev_node = ActionNode.objects.get(id=prev_node_id)
                except:  # noqa
                    continue
                if prev_node not in nodes:
                    nodes.append(prev_node)

    else:
        task_id = request.POST.get('task_id')
        if not task_id:
            return HttpResponse(status=400)
        try:
            task = Task.objects.get(id=task_id)
        except:  # noqa
            print("Task does not exist.")
            return HttpResponse(status=400)

        nodes = get_nodes(task)
        node = ActionNode.objects.create(workflow_history=workflow_history)
        if len(nodes) == 0:
            node.is_base = True
            node.save()

    if nodes:
        last_node = nodes[-1]
        last_node.next_node = node
        last_node.save()

    filter_conditions = Q(use_in_workflow=True, public=True, workspace__isnull=True) | Q(
        use_in_workflow=True, public=True, workspace=workspace)
    actions = Action.objects.filter(
        filter_conditions).distinct().order_by('order')
    actions = list(actions)

    # Get Action Index
    if nodes and action_indexes:
        action_index = int(action_indexes[-1]) + 1
    else:
        action_index = 1

    context = {
        'node': node,
        'action_index': action_index,
        'actions': actions,
        'from': request.POST.get('from')
    }
    if obj_type == 'workflow':
        context['workflow'] = workflow
    else:
        context['task'] = task
    return render(request, 'data/ai/form/node-action-selector.html', context)


@login_or_hubspot_required
@require_POST
def update_node_input(request):
    validator = FormValidator(request)
    validation_res = validator.update_node_input(
        workflow_id=request.POST.get('workflow_id'),
        task_id=request.POST.get('task_id'),
        name=request.POST.get('name'),
        value_type=request.POST.get('value_type')
    )
    if not validation_res['is_valid']:
        workflow = validation_res['data']['workflow']
        if workflow and workflow.is_trigger_active:
            workflow.is_trigger_active = False
            workflow.save()
        print(validation_res)
        response = HttpResponse(status=400)
        response['HX-Trigger'] = json.dumps({
            "reloadNotifNumber": "",
            "showValidationMessage": validation_res['error_message']
        })
        return response

    node = validation_res['data']['node']
    workflow = validation_res['data']['workflow']

    # Deactivate the trigger workflow
    if workflow and workflow.is_trigger_active:
        workflow.is_trigger_active = False
        workflow.save()

    # Update node predefined input
    print(validation_res['data']['defined_input'])
    node.predefined_input = validation_res['data']['defined_input']

    # Update node input data
    node.input_data = validation_res['data']['action_input']

    # Update node previous output data
    node.previous_output_data = validation_res['data']['prev_output_data']

    # Update action node run validity
    if validation_res['data']['is_node_valid'] != node.valid_to_run:

        node.valid_to_run = validation_res['data']['is_node_valid']

    print(validation_res['data']['is_node_valid'])
    node.save()

    if workflow and node.valid_to_run:
        workflow.valid_to_run = is_workflow_valid_to_run(workflow)
        workflow.save()

    return HttpResponse(status=200)


@login_or_hubspot_required
@require_GET
def workflow_valid_action(request, id):
    """Render the action button if workflow is valid to run, otherwise render '-'."""
    view_id = request.GET.get('view_id')
    from_page = request.GET.get('from')
    is_task = request.GET.get('is_task', False)
    is_drawer = bool(request.GET.get('is_drawer', False))
    task = None
    workflow = None
    if is_task:
        try:
            task = Task.objects.get(id=id)
        except Task.DoesNotExist:
            return HttpResponse(status=404)
    else:
        try:
            workflow = Workflow.objects.get(id=id)
        except:  # noqa
            return HttpResponse(status=404)

    if is_task:
        nodes = get_nodes(task)
    else:
        nodes = get_nodes(workflow)

    is_workflow_valid = False
    node = None
    if nodes:
        node = nodes[0]
        is_workflow_valid = is_workflow_valid_to_run(nodes=nodes)
    if workflow:
        workflow.valid_to_run = is_workflow_valid
        if workflow.is_trigger_active and not is_workflow_valid:
            workflow.is_trigger_active = False
        workflow.save()
    context = {
        'workflow': workflow,
        'task': task,
        'node': node,
        'from': from_page,
        'view_id': view_id,
        'is_drawer': is_drawer,
    }
    if not is_workflow_valid:
        # print("WORKFLOW IS NOT VALID")
        context['disable_button'] = True
        if is_task:
            print('task')
            return render(request, 'data/projects/task-action-activate-btn.html', context)
        if is_drawer:
            print('drawer')
            return render(request, 'data/projects/workflow-activation-btn.html', context)
        return HttpResponse('-')

    if is_task:
        return render(request, 'data/projects/task-action-activate-btn.html', context)
    return render(request, 'data/projects/workflow-activation-btn.html', context)


@login_or_hubspot_required
def delete_workflow(request, id):
    """
    Deletes a workflow by its ID and updates the workflows list view.
    
    If the workflow does not exist, notifies the user and redirects to the workflows page. After successful deletion, reloads the workflows list filtered by user permissions, workspace, and any active view filters, then returns the updated partial template. Triggers UI updates to close the workflow drawer and refresh notification counts.
    """
    view_id = request.POST.get('view_id')
    from_page = request.POST.get('from')
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    print("Delete workflow request", request.POST)

    permission = get_permission(
        object_type=TYPE_OBJECT_WORKFLOW, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    try:
        workflow = Workflow.objects.get(id=id)
        workflow.delete()
    except Workflow.DoesNotExist:
        Notification.objects.create(workspace=get_workspace(
            request.user), user=request.user, message='Workflow does not exist.', type="error")
        response = HttpResponse()
        response['HX-Redirect'] = reverse('workflows', host='app')
        return response

    # if lang == 'ja':
    #     Notification.objects.create(workspace=get_workspace(request.user), user=request.user, message='ワークフローが削除されました', type="success")
    # else:
    #     Notification.objects.create(workspace=get_workspace(request.user), user=request.user, message='Workflow deleted', type="success")

    view = None
    if view_id:
        try:
            view = View.objects.get(id=view_id)
        except View.DoesNotExist:
            pass

    conditions = Q()
    conditions &= get_permission_filter(permission, request.user)
    is_gallery_creator = str(
        workspace.id) == WORKFLOW_GALLERY_CREATOR_WORKSPACE
    if is_gallery_creator:
        conditions &= Q(created_by_sanka=True)
    else:
        conditions &= Q(workspace=workspace)
        conditions &= Q(created_by_sanka__isnull=True)
    workflows = Workflow.objects.filter(conditions).order_by('-created_at')
    columns = ["name", "trigger"]
    if view:
        filters = ViewFilter.objects.filter(view=view)
        workflows = apply_view_filter(filters, workflows)
        workflows = get_ordered_items(view, workflows)
        view_filter = view.viewfilter_set.filter(property__isnull=True).first()
        if view_filter:
            columns += ast.literal_eval(view_filter.column)

    context = {
        'columns': columns,
        'view_id': view_id,
        'project': view.project if view else None,
        'workflows': workflows,
        'from': from_page,
        'LANGUAGE_CODE': lang,
        'is_gallery_creator': is_gallery_creator,
    }
    response = render(
        request, 'data/projects/partial-workflows-rows.html', context)

    response['HX-Trigger'] = json.dumps({
        "reloadNotifNumber": "",
        "closeWorkflowDrawer": "",
    })
    return response

@login_or_hubspot_required
def bulk_duplicate_workflow(request):
    workspace = get_workspace(request.user)
    view_id = request.POST.get('view_id')
    from_page = request.POST.get('from')
    target = request.POST.get('target')

    permission = get_permission(
        object_type=TYPE_OBJECT_WORKFLOW, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    columns = ["name", "trigger"]

    view = View.objects.filter(id=view_id).first()
    if not view:
        view = View.objects.filter(
            title=None, target=TYPE_OBJECT_WORKFLOW, workspace=workspace
        ).first()
    filters = ViewFilter.objects.filter(view=view)

    if "flag_all" in request.POST:
        workflows = Workflow.objects.filter(workspace=workspace).order_by('-created_at')

        if view:
            workflows = apply_view_filter(filters, workflows)
            workflows = get_ordered_items(view, workflows)
            view_filter = view.viewfilter_set.filter(property__isnull=True).first()
            if view_filter:
                columns += ast.literal_eval(view_filter.column)
    else:
        item_ids = get_bulk_action_workflow_ids(request, workspace)
        workflows = Workflow.objects.filter(id__in=item_ids)
    
    for obj in workflows:
        obj.id = None
        obj.created_at = timezone.now()
        obj.save()
    
    return HttpResponse(status=200)

@login_or_hubspot_required
@require_POST
def bulk_delete_workflow(request):
    """
    Deletes multiple workflows by their IDs and returns an updated list of workflows.
    
    Removes the selected workflows, applies permission and workspace-based filtering to retrieve the updated workflow list, and renders the partial template for workflow rows. Triggers UI updates for notification count, workflow drawer, and bulk action button.
    """
    view_id = request.POST.get('view_id')
    from_page = request.POST.get('from')
    workflow_ids = request.POST.getlist('selected_workflow')
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    print("Delete workflow request", request.POST)

    permission = get_permission(
        object_type=TYPE_OBJECT_WORKFLOW, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    workflows = Workflow.objects.filter(id__in=workflow_ids)
    if workflows:
        workflows.delete()

    # if lang == 'ja':
    #     Notification.objects.create(workspace=get_workspace(request.user), user=request.user, message='ワークフローが削除されました', type="success")
    # else:
    #     Notification.objects.create(workspace=get_workspace(request.user), user=request.user, message='Workflow deleted', type="success")

    view = None
    if view_id:
        try:
            view = View.objects.get(id=view_id)
        except View.DoesNotExist:
            pass

    columns = ["name", "trigger"]
    is_gallery_creator = str(
        workspace.id) == WORKFLOW_GALLERY_CREATOR_WORKSPACE
    conditions = Q()
    conditions &= get_permission_filter(permission, request.user)
    if is_gallery_creator:
        conditions &= Q(created_by_sanka=True)
    else:
        conditions &= Q(workspace=workspace)
        conditions &= Q(created_by_sanka__isnull=True)
    workflows = Workflow.objects.filter(conditions).order_by('-created_at')
    if view:
        filters = ViewFilter.objects.filter(view=view)
        workflows = apply_view_filter(filters, workflows)
        workflows = get_ordered_items(view, workflows)
        view_filter = view.viewfilter_set.filter(property__isnull=True).first()
        if view_filter:
            columns += ast.literal_eval(view_filter.column)

    context = {
        'columns': columns,
        'view_id': view_id,
        'project': view.project if view else None,
        'workflows': workflows,
        'from': from_page,
        'LANGUAGE_CODE': lang,
        'is_gallery_creator': is_gallery_creator,
    }
    response = render(
        request, 'data/projects/partial-workflows-rows.html', context)

    response['HX-Trigger'] = json.dumps({
        "reloadNotifNumber": "",
        "closeWorkflowDrawer": "",
        "hideWorkflowBulkActionBtn": "",
    })
    return response


@login_or_hubspot_required
def bulk_edit_workflow(request):
    workflow_ids = request.POST.getlist('selected_workflow')
    view_id = request.POST.get('view_id')

    if not workflow_ids:
        return HttpResponse(status=400)
    workflows = Workflow.objects.filter(id__in=workflow_ids)
    if not workflows:
        return HttpResponse(status=404)

    workspace = get_workspace(request.user)

    properties = request.POST.getlist('properties')

    if 'users' in properties:
        input_value = request.POST.getlist('users')
        for workflow in workflows:
            workflow.assignee.clear()
        if input_value:
            try:
                users = User.objects.filter(workspace=get_workspace(
                    request.user), username__in=input_value)
                for workflow in workflows:
                    workflow.assignee.add(*users)
            except:  # noqa
                pass

        for workflow in workflows:
            workflow.project.clear()
        if input_value:
            try:
                projects = Group.objects.filter(
                    workspace=get_workspace(request.user), id__in=input_value)
                for workflow in workflows:
                    workflow.project.add(*projects)
            except:  # noqa
                pass

    response = HttpResponse()
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
    module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW).order_by(
        'order', 'created_at').first()
    module_slug = None
    if module:
        module_slug = module.slug
        response['HX-Redirect'] = reverse('load_object_page', host='app', kwargs={
                                          'module_slug': module_slug, 'object_slug': module_object_slug})
    else:
        response['HX-Redirect'] = reverse('main', host='app')
    if view_id:
        try:
            View.objects.get(id=view_id)
            response['HX-Redirect'] += '?view_id='+view_id
        except View.DoesNotExist:
            pass

    return response


@login_or_hubspot_required
def reload_previous_output(request):
    print("\n\nreload previous output: ", request.GET)
    current_index = int(request.GET.get('action_index', 0))
    # current_action_id = request.GET.get(f'action_id-{current_index}')
    field_name = request.GET.get('field_name')
    input_type = request.GET.get('input_type')
    node_id = request.GET.get('action_node_id')
    action_indexes = request.GET.getlist('action_indexes')

    if not (action_indexes and current_index and field_name and input_type):
        print("required params missing")
        return HttpResponse(status=400)

    if current_index > len(action_indexes):
        print("Current index out of range")
        return HttpResponse(status=400)

    action = None
    prev_actions = []
    prev_action_id = None
    for i in action_indexes:
        action_id = request.GET.get(f'action_id-{i}', None)
        if action_id in ['manual', '', None]:
            continue
        if i == current_index:
            print('\n## Getting Action: ')
            if prev_action_id == 'manual':
                return HttpResponse(status=200)
            try:
                action = Action.objects.get(uid=action_id)
            except Exception as e:
                print('Action not found: ', e)
                return HttpResponse(status=404)
            break
        else:
            try:
                prev_action_ = Action.objects.get(uid=action_id)
                prev_actions.append(prev_action_)
            except Action.DoesNotExist:
                print('Prev action not found')
                return HttpResponse(status=404)
        prev_action_id = action_id

    if not (action and prev_actions):
        print(f'Failed: {action} - {prev_actions}')
        return HttpResponse(status=200)

    if not action.input_format:
        print(
            "Skip reloading previous output as current action does not have input format.")
        return HttpResponse(status=404)

    target_type = ''
    for k, v in action.input_format.items():
        if k == field_name:
            target_type = v['type']
            break
    if not target_type:
        print('Target type not found')
        return HttpResponse(status=404)

    output_fields = []
    for i, prev_action in enumerate(prev_actions):
        output_field_item = []
        reviewed_outputs = []
        if not prev_action.output_format:
            continue
        prev_output_fields = get_action_output_fields(
            prev_action.output_format)
        for prev_output_field in prev_output_fields:
            if prev_output_field['type'] == target_type or (target_type == 'string' and prev_output_field['type'] == 'numbers'):
                output_field_item.append(prev_output_field['field'])
        if output_field_item:
            if reviewed_outputs:
                output_fields.append(
                    {'action': prev_action, 'output': output_field_item, 'reviewed_output': reviewed_outputs})
            else:
                output_fields.append(
                    {'action': prev_action, 'output': output_field_item})

    node = None
    if node_id:
        try:
            node = ActionNode.objects.get(id=node_id)
        except ActionNode.DoesNotExist:
            pass

    context = {
        "action_index": current_index,
        "output_format": output_fields,
        'target_name': field_name,
        "input_type": input_type,
        "node": node,
    }
    return render(request, 'data/ai/form/prev-output-options.html', context)


@login_or_hubspot_required
@require_GET
def all_previous_output(request):
    node_id = request.GET.get('action_node_id')
    action_index = int(request.GET.get('action_index'))
    if action_index > 1:
        action_ids = []
        for i in range(1, action_index+1):
            action_id = request.GET.get(f'action_id-{i}')
            if action_id:
                action_ids.append(action_id)
    else:
        action_ids = [request.GET.get('action_id-0')]
    print(request.GET)

    if not (node_id or (action_ids and action_index)):
        print('[Get all previous output] [ERROR] - Require one of node_id or (action_idsq and action_index) values.')
        return HttpResponse(status=400)

    prev_action = None
    if node_id:
        try:
            node = ActionNode.objects.get(id=node_id)
        except:  # noqa
            print("[Get all previous output] [ERROR] - Action Node does not exist.")
            return HttpResponse(status=404)

        nodes = get_nodes(node.workflow)
        if len(nodes) <= 1:
            return HttpResponse(status=200)

        # Get prev node
        for i, node_ in enumerate(nodes):
            if node_ == node and i > 0:
                prev_action = nodes[i-1].action
                if not prev_action or (prev_action and not prev_action.output_format):
                    return HttpResponse(status=200)
                break
        if not prev_action:
            return HttpResponse(status=200)
    else:
        subtractor = 0
        if 'manual' in action_ids:
            action_ids.remove('manual')
            subtractor = 1
            if len(action_ids) <= 1 or int(action_index) <= 1:
                return HttpResponse(status=200)
        else:
            if int(action_index) <= 0:
                return HttpResponse(status=200)

        try:
            prev_action = Action.objects.get(
                uid=action_ids[int(action_index)-1-subtractor])
            if not prev_action.output_format:
                return HttpResponse(status=200)
        except:  # noqa
            print("[Get all previous output] [ERROR] - Action does not exist.")
            return HttpResponse(status=404)

    output_field_item = []
    prev_output_fields = get_action_output_fields(prev_action.output_format)
    for prev_output_field in prev_output_fields:
        output_field_item.append(prev_output_field['field'])

    context = {
        'output_fields': output_field_item
    }
    return render(request, 'data/ai/form/partial-all-prev-output.html', context)


@login_or_hubspot_required
def activate_workflow_trigger(request):
    lang = request.LANGUAGE_CODE
    workflow_id = request.POST.get('workflow_id')

    if not workflow_id:
        msg = 'Failed to create task. Unable to retrieve workflow information.'
        if lang == 'ja':
            msg = 'タスクの作成に失敗しました。ワークフロー情報を取得できません。'
        Notification.objects.create(workspace=get_workspace(
            request.user), user=request.user, message=msg, type="error")
        response = HttpResponse(status=404)
        response['HX-Trigger'] = json.dumps(
            {"showFailedBar": "", "reloadNotifNumber": "", })
        return response

    try:
        workflow = Workflow.objects.get(id=workflow_id)
    except Workflow.DoesNotExist:
        msg = 'Failed to create task. Unable to retrieve workflow information.'
        if lang == 'ja':
            msg = 'タスクの作成に失敗しました。ワークフロー情報を取得できません。'
        Notification.objects.create(workspace=get_workspace(
            request.user), user=request.user, message=msg, type="error")
        response = HttpResponse(status=400)
        response['HX-Trigger'] = json.dumps(
            {"showFailedBar": "", "reloadNotifNumber": "", })
        return response

    if not workflow.valid_to_run:
        response = HttpResponse(status=400)
        response['HX-Trigger'] = json.dumps(
            {"showFailedBar": "", "reloadNotifNumber": "", })
        return response

    workflow.is_trigger_active = True
    workflow.save()

    response = HttpResponse(status=200)
    response['HX-Trigger'] = json.dumps({
        "closeWorkflowDrawer": "",
    })
    return response


@login_or_hubspot_required
def toggle_workflow_trigger(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    workflow_id = request.POST.get('workflow_id')

    if not workflow_id:
        msg = 'Failed to deactivate workflow. Unable to retrieve workflow information.'
        if lang == 'ja':
            msg = 'ワークフローを非アクティブ化できませんでした。ワークフロー情報を取得できません。'
        Notification.objects.create(
            workspace=workspace, user=request.user, message=msg, type="error")
        response = HttpResponse(status=404)
        response['HX-Trigger'] = json.dumps({"reloadNotifNumber": "", })
        return response

    try:
        workflow = Workflow.objects.get(id=workflow_id)
    except Workflow.DoesNotExist:
        msg = 'Failed to deactivate workflow. Unable to retrieve workflow information.'
        if lang == 'ja':
            msg = 'ワークフローを非アクティブ化できませんでした。ワークフロー情報を取得できません。'
        Notification.objects.create(
            workspace=workspace, user=request.user, message=msg, type="error")
        response = HttpResponse(status=400)
        response['HX-Trigger'] = json.dumps({"reloadNotifNumber": "", })
        return response

    workflow.is_trigger_active = not (workflow.is_trigger_active)

    if workflow.is_trigger_active and workflow.trigger_type == 'time':
        workspace_tz = pytz.timezone(workspace.timezone)
        first_node = get_nodes(workflow)[0]

        # Set trigger_every based on trigger type - this should be an integer representing frequency
        # Try to get the frequency from the node's input_data, fallback to 1 if not found or invalid
        trigger_frequency = 1
        if 'time' in first_node.input_data:
            try:
                # For time triggers, the 'time' field might contain the frequency value
                time_value = first_node.input_data['time']
                print(
                    f"[toggle_workflow_trigger] Processing time_value: {time_value} (type: {type(time_value)})")

                if isinstance(time_value, (int, float)):
                    trigger_frequency = int(time_value)
                elif isinstance(time_value, str) and time_value.isdigit():
                    trigger_frequency = int(time_value)
                else:
                    # If 'time' contains a time string like "00:00", use default frequency
                    print(
                        f"[toggle_workflow_trigger] Time value '{time_value}' is not a valid frequency, using default: 1")
                    trigger_frequency = 1
            except (ValueError, TypeError) as e:
                print(
                    f"[toggle_workflow_trigger] Error processing time value: {e}, using default: 1")
                trigger_frequency = 1

        print(
            f"[toggle_workflow_trigger] Setting trigger_every to: {trigger_frequency}")

        workflow.trigger_every = trigger_frequency

        if str(first_node.action.uid) == 'c252738e-4977-467b-97a7-dcd7251968c1':  # days run
            workflow.last_triggered_at = timezone.now().astimezone(
                workspace_tz).replace(hour=0, minute=0, second=0, microsecond=0)
        elif str(first_node.action.uid) == 'c252738e-4977-467b-97a7-dcd7251968c3':  # hours run
            workflow.last_triggered_at = timezone.now().astimezone(
                workspace_tz).replace(minute=0, second=0, microsecond=0)
        elif str(first_node.action.uid) == 'c252738e-4977-467b-97a7-dcd7251968c4':  # minutes run
            workflow.last_triggered_at = timezone.now().astimezone(
                workspace_tz).replace(second=0, microsecond=0)

    try:
        workflow.save()
    except Exception as e:
        print(f"[toggle_workflow_trigger] Error saving workflow: {e}")
        msg = 'Failed to save workflow trigger settings.'
        if lang == 'ja':
            msg = 'ワークフロートリガー設定の保存に失敗しました。'
        Notification.objects.create(
            workspace=workspace, user=request.user, message=msg, type="error")
        response = HttpResponse(status=500)
        response['HX-Trigger'] = json.dumps({"reloadNotifNumber": "", })
        return response

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
    module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW).order_by(
        'order', 'created_at').first()
    if module:
        module_slug = module.slug
        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
    return redirect(reverse('main', host='app'))


@login_or_hubspot_required
@require_POST
def workflow(request):
    """
    Creates or updates a workflow and its action nodes based on POST data.
    
    Processes workflow field updates, assigns ownership, manages assignees and project associations, and configures the sequence of action nodes. Handles node conditions, input data, and predefined inputs. Removes deleted nodes, validates workflow readiness, and deactivates triggers if the workflow is not valid to run. Sends a success notification and redirects to the appropriate workflow page.
    """
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    view_id = request.POST.get('view_id', '')

    print(request.POST)
    response = HttpResponse(status=200)
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
    module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW).order_by(
        'order', 'created_at').first()
    module_slug = None
    if module:
        module_slug = module.slug
        response['HX-REDIRECT'] = reverse('load_object_page', host='app', kwargs={
                                          'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={view_id}'
    else:
        response['HX-REDIRECT'] = reverse('main', host='app')

    validator = FormValidator(request, skip_required_input=True)
    input = validator.studio_workflow()
    if not input['is_valid']:
        print("Invalid input")
        response = HttpResponse(status=400)
        response['HX-Trigger'] = json.dumps({
            "reloadNotifNumber": "",
        })
        return response

    if input['data']['submit_option'] != 'save':
        return response
    print('submit option: save')
    print(input)
    workflow = input['data']['workflow']
    workflow.title = input['data']['title']
    workflow.title_ja = input['data']['title_ja']
    workflow.description = input['data']['description']
    workflow.description_ja = input['data']['description_ja']

    owner = request.POST.get('owner', None)
    assign_object_owner(workflow,owner,request,TYPE_OBJECT_WORKFLOW)

    workflow.save()

    users = User.objects.filter(
        workspace=workspace, username__in=input['data']['user_usernames'])
    groups = Group.objects.filter(
        workspace=workspace, id__in=input['data']['group_usernames'])

    workflow.assignee.clear()
    workflow.assignee.add(*users)

    workflow.project.clear()
    workflow.project.add(*groups)

    prev_node = None
    prev_output_data = {}
    action_id_list = []
    action_inputs = {}
    defined_inputs = {}
    action_indexes = input['data']['action_indexes']
    action_node_ids = input['data']['action_node_ids']
    action_trigger_type = input['data']['action_trigger_type']
    print(action_indexes)
    action_node = None
    # Loop starts from the last index
    for i, action_index in enumerate(action_indexes):
        print('i', i)
        print('action_index', action_index)
        if len(action_node_ids) < i+1:
            break
        node_id = action_node_ids[i]
        print(f'{node_id=}')

        try:
            action_node = ActionNode.objects.get(id=node_id)
        except Exception as e:
            print(e)
            continue

        if prev_node:
            prev_node.next_node = action_node
            prev_node.save()

        action_node.workflow = workflow
        if (action_trigger_type == 'manual' and action_index == 1) or action_trigger_type != 'manual' and action_index == 0:
            action_node.is_base = True
        else:
            action_node.is_base = False
        action_node.save()
        prev_node = action_node

        # Action Condition
        condition_group = None
        if action_index > 0:
            is_using_condition = request.POST.get(
                f'condition_toggle-{action_index}')
            object_type = None
            condition_field = None
            condition_operator = None
            condition_value = None

            if is_using_condition:
                object_type = request.POST.get(
                    f'action_condition-object_type-{action_index}')
                condition_field = request.POST.get(
                    f'condition-field-{action_index}')
                condition_operator = request.POST.get(
                    f'condition-operator-{action_index}')
                condition_value = request.POST.get(
                    f'condition-value-{action_index}', None)
                value_type = request.POST.get(
                    f'value-type-{action_index}', "number")
                if condition_value == '':
                    condition_value = None
            else:
                condition_groups = action_node.condition_groups.all()
                if condition_groups:
                    condition_groups.delete()

            if object_type and condition_field and condition_operator and value_type:
                condition_group = ActionConditionGroup.objects.create(
                    node=action_node)
                
                action_condition = ActionCondition.objects.create(
                    group=condition_group,
                    object_type=object_type,
                    field_name=condition_field,
                    operator=condition_operator,
                    value_type=value_type,
                )
                
                if value_type == 'number':
                    action_condition.value_number=condition_value
                elif value_type == 'text':
                    action_condition.value_text=condition_value
                    
                action_condition.save()
                print('condition created')
                if action_node.is_base:
                    validator.valid_to_run = False

        action_id = request.POST.get(f'action_id-{action_index}')
        if not action_id:
            print('no action ID')
            continue
        print('action_id', action_id)
        try:
            action = Action.objects.get(uid=action_id)
            action_id_list.append(action_id)
        except Exception:
            action_slug = action_id
            try:
                action = Action.objects.get(uid=get_action_id(action_slug))
            except Exception:
                print('[Get Action] [ERROR]')
                continue

        action_node.action = action
        action_node.save()

        validator.name_postfix = f'-{action_index}'
        action_index = str(action_index)

        if action.slug:
            # NEW ACTION
            print('saving action in new way.')
            print('action slug', action.slug)
            view_func = resolve(reverse('action_form_mapper', host='app')[2:].replace(
                'app.'+settings.PARENT_HOST, '') + action.slug + '/', urlconf='data.urls').func
            mutable_post = request.POST.copy()
            mutable_post.update({'action_index': action_index})
            mutable_post.pop('action_node_id', None)
            mutable_post.update({'action_node_id': str(action_node.id)})
            mutable_post.update({'action_slug': action.slug})
            request.POST = mutable_post
            view_res = view_func(request)
            if view_res.status_code >= 400:
                # msg = '何か問題が発生しました。' if lang == 'ja' else 'Something went wrong.'
                # Notification.objects.create(workspace=workspace, user=request.user, message=msg, type="success")
                print('View function failed')
            action_node.refresh_from_db()
            continue

        else:
            print('saving action in old way.')
            action_res = validator.action_input_mapper[str(action.uid)]()
            print('action_res', action_res)

        if 'defined_input' in action_res['data']:
            defined_inputs[action_index] = action_res["data"]["defined_input"]
            defined_inputs[action_index]['names'] = [
                val for val in defined_inputs[action_index].keys()]
            defined_inputs[action_index]['target_names'] = [
                val for val in defined_inputs[action_index].keys()]

        prev_output = validator.get_previous_output_data(
            action_res['data'], action=action, action_index=action_index)
        if not validator.is_valid:
            print('Previous output data invalid.')
            response = HttpResponse(status=400)
            response['HX-Trigger'] = json.dumps({
                "reloadNotifNumber": "",
            })
            return response
        prev_output_data[action_index] = prev_output

        action_node.valid_to_run = validator.valid_to_run
        action_node.save()
        # if 'assignee' in action_res['data']:
        #     [user_usernames.append(val) for val in action_res['data']['assignee']]

        action_inputs[action_index] = action_res['data']

    if workflow:
        try:
            ActionNode.objects.filter(workflow=workflow).exclude(
                id__in=action_node_ids).delete()
        except Exception:
            print('[Delete Action Node] [ERROR]')

    workflow.save()

    # Update node predefined input
    for i, node in enumerate(get_nodes(workflow)):
        index_ = str(i+1)
        if '0' in action_inputs:
            index_ = str(i)

        print(index_)
        if index_ in defined_inputs:
            node.predefined_input = defined_inputs[index_]

        # Update node input data
        if index_ in action_inputs:
            if 'defined_input' in action_inputs[index_]:
                # removed 'defined_input' key from action_inputs[index_] dictionary
                action_inputs[index_].pop('defined_input', None)
            node.input_data = action_inputs[index_]

        # Update node previous output data
        if index_ in prev_output_data:
            node.previous_output_data = prev_output_data[index_]

        node.save()

    msg = 'The workflow data has been successfully saved'
    if lang == 'ja':
        msg = 'ワークフロー データが正常に保存されました。'

    workflow.refresh_from_db()
    # Deactivate the trigger workflow
    if workflow and workflow.is_trigger_active:
        workflow.is_trigger_active = False
        msg = 'The workflow is deactivated and changes has been successfully saved.'
        if lang == 'ja':
            msg = 'ワークフローは非アクティブ化され、変更は正常に保存されました。'

    Notification.objects.create(
        workspace=workspace, user=request.user, message=msg, type="success")
    workflow.valid_to_run = is_workflow_valid_to_run(workflow)
    workflow.is_trigger_active = False
    print(workspace.id)
    print(WORKFLOW_GALLERY_CREATOR_WORKSPACE)
    if str(workspace.id) == WORKFLOW_GALLERY_CREATOR_WORKSPACE:
        # workflow.status = 'draft'
        workflow.created_by_sanka = True
    workflow.save()
    return response


@login_or_hubspot_required
@require_GET
def action_selector(request):
    """
    Renders an action selector filtered by page group type, bulk action flag, and other query parameters.
    
    Filters available actions for workflows or bulk operations based on usage context, visibility, and additional request parameters, then renders the action selector partial template.
    """
    module = request.GET.get('module')
    page_group_type = str(request.GET.get('page_group_type', ''))
    action_index = request.GET.get('action_index')
    bulk_action = bool(request.GET.get('bulk_action', False))
    view_id = request.GET.get('view_id')

    query_filter = Q()
    if page_group_type:
        # This is to avoid the same string value for case Slips and Delivery Slips
        query_filter &= Q(page_group_type__regex=r'(^|,)' +
                          page_group_type.lower() + '(,|$)', public=True, slug__isnull=False)

    else:
        query_filter &= Q(use_in_workflow=True,
                          public=True, slug__isnull=False)

    if bulk_action:
        query_filter &= Q(use_in_bulk_action=True)
    else:
        # in some case (Commerces) there is just only available for bulk action and hide from record action so need filter this
        query_filter &= Q(use_in_record=True)

    actions = Action.objects.filter(query_filter).order_by('order')
    context = {
        'actions': actions,
        'action_index': action_index,
        'additional_params': {},
        'module': module,
        'bulk_action': bulk_action,
        'view_id': view_id,
    }
    for k, v in request.GET.items():
        context['additional_params'][k] = v
    return render(request, 'data/ai/form/new-action-selector.html', context)


@login_or_hubspot_required
def action_form_mapper(request):
    action_slug = request.GET.get('action_id')
    if not action_slug:
        return HttpResponse(status=400)

    # Map the action_slug to the view function
    view_func = resolve(reverse('action_form_mapper', host='app')[2:].replace(
        'app.'+settings.PARENT_HOST, '') + action_slug + '/', urlconf='data.urls').func
    return view_func(request)


@login_or_hubspot_required
@require_GET
def get_node_index(request, id):
    try:
        node = ActionNode.objects.get(id=id)
    except ActionNode.DoesNotExist:
        print('[Get Action Node index] [ERROR] - ActionNode does not exist.')
        return HttpResponse(status=404)

    workflow_id = request.GET.get('workflow_id')
    task_id = request.GET.get('task_id')
    nodes = []

    if workflow_id:
        try:
            workflow = Workflow.objects.get(id=workflow_id)
            nodes = get_nodes(workflow)
        except Workflow.DoesNotExist:
            print('[Get Action Node index] [ERROR] - Workflow does not exist.')
            return HttpResponse(status=404)
    elif task_id:
        try:
            task = Task.objects.get(id=task_id)
            nodes = get_nodes(task)
        except Task.DoesNotExist:
            print('[Get Action Node Index] [ERROR] - Task does not exist.')
            return HttpResponse(status=404)
    else:
        action_node_ids = request.GET.getlist('action_node_id')
        for action_node_id in action_node_ids:
            try:
                nodes.append(ActionNode.objects.get(id=action_node_id))
            except ActionNode.DoesNotExist:
                pass

    if node not in nodes:
        print('[Get Action Node index] [ERROR] - Node not found in the workflow.')
        return HttpResponse(status=400)

    action_index = -1
    for i, node_ in enumerate(nodes):
        if node_ == node:
            action_index = i
            break

    if not nodes[0].action or not nodes[0].action.is_trigger:
        action_index += 1
    return HttpResponse(f'#{action_index}')


@login_or_hubspot_required
def get_property_operator(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    # Try to get action_index directly
    action_index = request.GET.get('action_index')

    # If not found, try to extract it from action_condition-object_type-{index}
    if not action_index:
        for key in request.GET.keys():
            if key.startswith('action_condition-object_type-'):
                try:
                    action_index = key.split('-')[-1]
                    break
                except:  # noqa
                    pass

    if not action_index:
        return HttpResponse(status=400, content="Missing action_index parameter")

    condition_id = request.GET.get('condition_id')
    condition = None
    object_type = None

    # Only try to get condition if condition_id is not empty string
    if condition_id and condition_id.strip():
        try:
            condition = ActionCondition.objects.get(id=condition_id)
            object_type = condition.object_type
        except ActionCondition.DoesNotExist:
            pass

    # If no condition found, try to get object_type from request
    if not object_type:
        object_type = request.GET.get(
            f'action_condition-object_type-{action_index}')
        if not object_type:
            # Check if this is a duplicate request
            if condition_id == '':
                # Return empty response for empty condition_id
                return HttpResponse(status=200, content='')
            return HttpResponse(status=400, content="Missing object_type parameter")

    if object_type in TYPE_OBJECTS:
        properties = get_properties_with_details(object_type, workspace, lang)
        default_properties = []
        for prop in properties:
            if not is_valid_uuid(prop['id']):
                default_properties.append(prop)
        page_obj = get_page_object(object_type)
        base_model = page_obj['base_model']
        columns_display = page_obj['columns_display']

        default_properties = [val['id'] for val in default_properties]

        condition_options = {}
        operators = {
            'number': {val: OPERATORS_DISPLAY[val] for val in VALUE_TYPE_TO_OPERATORS['number']},
            'text': {val: OPERATORS_DISPLAY[val] for val in VALUE_TYPE_TO_OPERATORS['text']},
        }
        for v in base_model._meta.fields:
            if v.name not in default_properties:
                continue

            value_type = model_field_to_condition_value_type(
                v, limited_to=[FloatField, IntegerField, CharField, TextField])
            if not value_type:
                continue
            
            field_values = v.choices if v.choices else []
            def serialize_value(value):
                """Helper function to safely serialize values including __proxy__ objects"""
                if hasattr(value, '__proxy__'):
                    return str(value)
                elif hasattr(value, '_proxy____cast'):
                    return str(value)
                elif isinstance(value, (tuple, list)) and len(value) == 2:
                    # Handle Django choice tuples like (value, display)
                    return [serialize_value(v) for v in value]
                else:
                    return value

            if isinstance(field_values, dict):
                field_values = {
                    str(key): serialize_value(value)
                    for key, value in field_values.items()
                }
            elif isinstance(field_values, (list, tuple)):
                field_values = [serialize_value(value) for value in field_values]

            try:
                logger.debug(
                    f"field_values type: {type(field_values)}")
                if isinstance(field_values, (list, tuple)):
                    logger.debug(
                        f"field_values content types: {[type(item) for item in field_values]}")
                    for i, item in enumerate(field_values):
                        logger.debug(
                            f"Item {i}: {type(item)} - {repr(item)}")
                field_values = json.dumps(field_values)
            except TypeError as e:
                logger.error(f"JSON serialization error: {str(e)}")
                # Force evaluation of any lazy translation objects
                if isinstance(field_values, (list, tuple)):
                    logger.debug(
                        "Attempting to force string conversion on list items")
                    field_values = [str(item) for item in field_values]
                else:
                    logger.debug(
                        "Attempting to force string conversion on field_values")
                    field_values = str(field_values)
                # Try again with converted values
                try:
                    field_values = json.dumps(field_values)
                except TypeError as e2:
                    logger.error(f"Second JSON serialization attempt failed: {str(e2)}")
                    # Fallback to empty list if all else fails
                    field_values = json.dumps([])

            condition_options[v.name] = columns_display[v.name]
            condition_options[v.name]['type'] = value_type
            condition_options[v.name]['values'] = field_values

        context = {
            'condition_options': condition_options,
            'action_index': action_index,
            'operators': operators,
            'condition': condition,
        }
        return render(request, 'data/ai/form/partial-condition-value-operator.html', context)
    else:
        # Handle non object value (like string, number, date, datetime)
        # Skip for now
        return HttpResponse(status=400)


@login_or_hubspot_required
def record_action_history(request):
    workspace = get_workspace(request.user)
    object_type = request.GET.get('object_type')
    object_id = request.GET.get('object_id')
    if not (object_type and object_id) or object_type not in TYPE_OBJECTS:
        return HttpResponse(status=400)

    history_objs = ActionHistory.objects.filter(
        workspace=workspace, object_type=object_type, object_id=object_id).order_by('-created_at')
    has_next_page = False
    try:
        page = int(request.GET.get('page', 1))
    except (TypeError, ValueError):
        return HttpResponse(status=400)
    if history_objs:
        paginator = Paginator(history_objs, 10)
        try:
            page_content = paginator.page(page)
            history_objs = page_content.object_list
            has_next_page = page_content.has_next()
            print(page)
            page += 1
        except EmptyPage:
            history_objs = []
            page = None
    else:
        page = None

    context = {
        'history': history_objs,
        'next_page': page,
        'has_next_page': has_next_page,
        'is_record_action': True,
        'object_type': object_type,
        'object_id': object_id,
    }
    return render(request, 'data/shopturbo/parts/action-history-row.html', context)


def get_bulk_action_workflow_ids(request, workspace):
    workflow_ids = []

    # First, try to get from traditional checkbox selections
    checkbox_ids = request.POST.getlist("selected_workflow")
    if checkbox_ids:
        workflow_ids.extend(checkbox_ids)
    # Remove duplicates and validate
    workflow_ids = list(set(workflow_ids))

    # Validate that all IDs are valid UUIDs and belong to the workspace
    from utils.utility import is_valid_uuid
    valid_ids = []
    for workflow_id in workflow_ids:
        if is_valid_uuid(workflow_id):
            # Check if item exists in workspace
            if Workflow.objects.filter(id=workflow_id, workspace=workspace).exists():
                valid_ids.append(workflow_id)

    return valid_ids


def export_invoice_to_hubspot_custom_object(request):
    print("\n\nGoing to this POST: ", request.POST)
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    channels = Channel.objects.filter(
        workspace=workspace,
        integration__slug='hubspot',
    )

    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
        )
        .order_by("order", "created_at")
        .first()
    )
    module_slug = None
    if module_slug:
        module_slug = module.slug
    selected_custom_object = None
    selected_channel = None
    if request.method == 'GET':
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        # Getting Info if have action_index, it means from workflow
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)
        hubspot_custom_object = None
        objectHubspotName = None
        selected_channel = None
        if postfix:
            if node and node.input_data:
                if "objectHubspotName" in node.input_data:
                    objectHubspotName = node.input_data["objectHubspotName"]
                if "channel_id" in node.input_data:
                    selected_channel = node.input_data["channel_id"]
                if "hubspot_custom_object" in node.input_data:
                    hubspot_custom_object = node.input_data["hubspot_custom_object"]

        context = {
            "channels": channels,
            "drawer_type": None,
            "item_ids": None,
            "object_type": TYPE_OBJECT_INVOICE,
            "filters": None,
            "menu_key": module_slug,
            "selected_custom_object": hubspot_custom_object,
            "selected_channel": selected_channel,
            "import_export_type": 'export',
            "objectHubspotName": objectHubspotName,
            "channel_ids": [],
        }
        return render(request, 'data/workflow/workflow-export-invoice-to-hubspot-custom-object.html', context)

    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    channel_id = request.POST.get("select_integration_ids")
    objectHubspotName = request.POST.get("objectHubspotName")
    hubspot_group_name = request.POST.get("hubspot_group_name", "")
    hubspot_custom_object = request.POST.get("custom_object_options")
    platform_object_label = request.POST.get("platform_object_label",None)
    node = None
    hubspot_integration = None

    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    action_name = request.POST.get("action_name" + postfix)

    print("Running Export invoice to Hubspot Custom Object: ",
          hubspot_custom_object, ' + ', channel_id)
    try:
        if submit_option == "save":
            # POST
            # Add additional values to request
            mutable_post = request.POST.copy()
            mutable_post['from_invoice'] = 'true'
            mutable_post['save_mapping'] = 'true'
            request.POST = mutable_post
            try:
                save_mapped_hubspot_object_name(request)
            except:
                pass
            
            try:
                custom_object_hubspot_save_mapping(request)
            except:
                pass
            input_data = {}
            node.valid_to_run = True
            if hubspot_custom_object:
                input_data["hubspot_custom_object"] = hubspot_custom_object

            if channel_id:
                input_data["channel_id"] = channel_id

            if objectHubspotName:
                input_data["objectHubspotName"] = objectHubspotName

            if hubspot_group_name:
                input_data["hubspot_group_name"] = hubspot_group_name
                
            if platform_object_label:
                input_data["platform_object_label"] = platform_object_label

            if action_name:
                input_data["action_name"] = action_name

            mapping = InvoiceMappingFields.objects.filter(
                workspace=workspace, platform="hubspot"
            )
            if mapping:
                mapping = mapping.first()
                mapper = mapping.input_data
                mapping_custom_fields = {}
                for map_ in mapper:
                    if mapper[map_]["skip"] == "True":
                        continue
                    if platform_object_label in map_:
                        mapping_custom_fields[map_] = mapper[map_]["value"]

                mapping = mapping_custom_fields
            else:
                mapping = None
                
            if mapping:
                input_data["mapping"] = mapping
                
            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            print(node.valid_to_run)
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module_slug:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        else:
            if submit_option == "run":
                if "channel_id" in node.input_data:
                    channel_id = node.input_data["channel_id"]
                if "hubspot_custom_object" in node.input_data:
                    hubspot_custom_object = node.input_data["hubspot_custom_object"]
                if "objectHubspotName" in node.input_data:
                    objectHubspotName = node.input_data["objectHubspotName"]
                print("Action Tracker: ", at.input_data)

            invoice_conditions = Q(workspace=workspace)
            invoice_ids = Invoice.objects.filter(
                invoice_conditions).values_list("id", flat=True)
            if not invoice_ids:
                print("Invoice does not exist")
                return HttpResponse(status=404)

            mapping = InvoiceMappingFields.objects.filter(
                workspace=workspace, platform="hubspot"
            )
            mapping_custom_fields = {}
            if mapping:
                mapping = mapping.first()
                mapper = mapping.input_data
                for map_ in mapper:
                    if mapper[map_]["skip"] == "True":
                        continue
                    mapping_custom_fields[map_] = mapper[map_]["value"]

            else:
                mapping_custom_fields = None

            # added sanka_id
            platform_object_label = request.POST.get("platform_object_label")
            mapping_custom_fields[f"sanka_id|string|{platform_object_label}"] = "id"

            print("mapping_custom_fields: ",
                  mapping_custom_fields)
            export_invoice_hubspot_custom_object(channel_id, mapping_custom_fields=mapping_custom_fields, platform_object_type_id=hubspot_custom_object,
                                                 hubspot_group_name=hubspot_group_name, lang=lang, user_id=request.user.id, invoice_ids=invoice_ids)
    except Exception as e:
        print(f"Export error 3: {e}")
        if submit_option == "run":
            if node:
                at.input_data = {
                    f"{'エラーログ' if lang == 'ja' else 'Error Log'}": e,
                }
                at.output_data = {}
                at.save()
        return HttpResponse(status=500)

    if submit_option == "run":
        at.status = "success"
        at.completed_at = timezone.now()
        at.save()

        next_node = None
        if node:
            next_node = node.next_node
            at = ActionTracker.objects.filter(id=at.id).first()
            if at:
                if hubspot_integration:
                    at.output_data["hubspot_integration"] = str(
                        hubspot_integration)
                at.save()
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )
        return redirect(reverse("main", host="app"))
