# Generated by Django 4.2.20 on 2025-07-23 15:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data', '3019_merge_20250723_1010'),
    ]

    operations = [
        migrations.AddField(
            model_name='billnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='companynamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='contactsnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='dealsnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='deliveryslipnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='estimatenamecustomfield',
            name='show_badge',
            field=models.<PERSON>oleanField(default=False),
        ),
        migrations.AddField(
            model_name='expensenamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='inventorytransactionnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='inventorywarehousenamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='invoicenamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='jobnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='journalentrynamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='messagenamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='purchaseordersnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='receiptnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='shopturboinventorynamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='shopturboitemsnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='shopturboordersnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='shopturbosubscriptionsnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='slipnamecustomfield',
            name='show_badge',
            field=models.BooleanField(default=False),
        ),
    ]
