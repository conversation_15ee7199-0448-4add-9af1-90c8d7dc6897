{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}


<div class="card shadow-none rounded-0 w-100 vh-100 overflow-hidden border-0 bg-white">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if LANGUAGE_CODE == 'ja' %}
                タスクを追加
            {% else %}
                Add Task
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon me-5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    <div class="row px-10 mt-5">
        <div class="col-6">
            <div class="btn-group w-100" role="group">
                <input checked type="radio" class="w-100 text-center btn-check single-entry-section-switcher" name="switch" id="kt_switch_option_1"
                /> 
                <label class="w-100 btn btn-outline btn-outline-dashed btn-outline-default p-4" for="kt_switch_option_1">
                    <span class="text-dark fw-bolder d-block fs-6">
                        {% if LANGUAGE_CODE == 'ja'%}
                        タスクレコードの作成
                        {% else %}
                        Create Task Record
                        {% endif %}
                    </span>
                </label>
            </div>
        </div>
        <div class="col-6">
            <div class="btn-group w-100" role="group">
                <input type="radio" class="w-100 text-center btn-check upload-entry-section-switcher" name="switch" id="kt_switch_option_3"
                />
                <label class="w-100 btn btn-outline btn-outline-dashed btn-outline-default p-4" for="kt_switch_option_3">
                    <span class="text-dark fw-bolder d-block fs-6">
                        {% if LANGUAGE_CODE == 'ja'%}
                        タスクレコードを選択
                        {% else %}
                        Select Task Record
                        {% endif %}
                    </span>
                </label>
            </div>
        </div>
    </div>
    
    <div class='d-none'
        hx-get="{% host_url 'task_form' host 'app' %}"
        hx-vals='{"view_id":"{{view_id}}", "object_id":"{{object_id}}", "source":"{{source}}", "module":"{{module}}", "type":"create-association"}'
        hx-swap="beforeend"
        hx-target="#create-drawer-content" 
        hx-trigger="load"
    >
    </div>
    
    <div id="create-drawer-content" class="scroll-y px-0">
        <div class="px-10 mt-5">
            <div class="mb-3">
                <label class="fs-5 fw-bold mb-2">
                    <span class=""> 
                        {% if LANGUAGE_CODE == 'ja'%}
                        プロジェクト
                        {% else %}
                        Project
                        {% endif %}
                    </span>
                </label>
        
                <select
                    class="h-40px form-select form-select-solid border bg-white select2-this-lazy-task-cf"
                    form="task-form-create-association"
                    data-placeholder="{% if LANGUAGE_CODE == 'ja'%}プロジェクトを選択{% else %}Select Project{% endif %}"
                    name="p_id"
                    required>
                </select>
            </div>
        
            <script>
                $(document).ready(function() {
                    $('.select2-this-lazy-task-cf').select2({
                        ajax: {
                            delay: 250, // wait 250 milliseconds before triggering the request
                            dataType: 'json',
                            url: '{% host_url "projects_options" host "app" %}',
                            data: function (params) {
                                    var query = {
                                        q: params.term,
                                        page: params.page || 1,
                                        json_response: true
                                    }
                                    return query;
                                },
                            minimumInputLength: 2,
                        },
                        language: {
                            "noResults": function(){
                                return "{% if LANGUAGE_CODE == 'ja' %}プロジェクトが見つかりません{% else %}No Project is found{% endif %}";
                            },
                            searching: function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                            },
                            "loadingMore": function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                            },
                        },
                    });
                });
            </script>
        </div>
    </div>
    
    <div id="select-drawer-content" class="d-none scroll-y">
        <form id="select-tasks-form" class="px-10 mt-5" method="POST" name="" 

            {% if source == constant.TYPE_OBJECT_CASE %}
                action="{% host_url 'service_manage_deals' object_id host 'app' %}"
            {% else %}
                action=""
            {% endif %}
        >
            {% csrf_token %}
    
            <input type="hidden" name="view_id" value="{{view_id}}">
            <input type="hidden" name="object_id" value="{{object_id}}">
            <input type="hidden" name="page" value="{{page}}">
            <input type="hidden" name="module" value="{{module}}">
            <input type="hidden" name="update_tasks" value="true">
    
            <div class="{% include "data/utility/form-div.html" %}">          
                <label class="mb-2 d-flex align-items-center fs-4 fw-bold">
                    <span class="" >
                    {% if LANGUAGE_CODE == 'ja'%}
                    タスク
                    {% else %}
                    Tasks
                    {% endif %}
                    </span>
                </label>
                
                <select id="task-association-select" placeholder="Choose Tasks" class="border min-h-40px form-select select2-tasks-lazy-association" name="tasks"
                    {% if LANGUAGE_CODE == 'ja'%}
                    data-placeholder="タスク"
                    {% else %}
                    data-placeholder="Tasks"
                    {% endif %}
                    data-allow-clear="true"
                    multiple="multiple"
                    >
                    <option value=""></option>
                    {% for task in obj.tasks.all %}
                        <option value="{{task.id}}" selected>
                            {{task.project_target.title}} | #{{ task.task_id|stringformat:"04d" }} - {{task.title}}
                        </option>
                    {% endfor %}
                </select>     
            
                <script>
                    $('.select2-tasks-lazy-association').select2({
                        ajax: {
                            delay: 250, // wait 250 milliseconds before triggering the request
                            dataType: 'json',
                            url: '{% host_url "tasks_options" host "app" %}',
                            data: function (params) {
                                    var query = {
                                        q: params.term,
                                        page: params.page || 1,
                                        json_response: true,
                                    }
                                    return query;
                                },
                            minimumInputLength: 2,
                        },
                        language: {
                            "noResults": function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}タスクが見つかりません{% else %}No Tasks found{% endif %}";
                            },
                            searching: function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                            },
                            "loadingMore": function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                            },
                        },
                    })
                </script>
            </div>
    
            <button type="submit" class="btn btn-dark mt-5">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </form>
    </div>
</div>

{% block js %}
    <script>
        $(document).ready(function(){
            $('.single-entry-section-switcher').click(function(){
                $('#create-drawer-content').toggleClass('d-none');
                $('#select-drawer-content').toggleClass('d-none');
            });
            $('.upload-entry-section-switcher').click(function(){
                $('#create-drawer-content').toggleClass('d-none');
                $('#select-drawer-content').toggleClass('d-none');
            });
        });
    </script>
{% endblock %}