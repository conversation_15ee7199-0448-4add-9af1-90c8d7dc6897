import uuid
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from data.models.base import save_tags_values, validate_value_custom_field

from data.models.constant import *
from data.constants.constant import *
from data.models.deals import DEALS_NUMBER_FORMAT


# Jobs
JOB_TYPE = [
    ('full_time', "Full time"),
    ('part_time', "Part time"),
    ('contractor', "Contractor"),
    ('intern', "Intern"),
]

JOB_STATUS = [
    ('hc_approval', 'Headcount Approval'),
    ('job_requisition', 'Job Requisition'),
    ('candidate_sourcing', 'Candidate Sourcing'),
    ('interview_scheduling', 'Interview Scheduling'),
    ('job_offer', 'Job Offer'),
    ('onboarding', 'Onboarding'),
    ('job_closing', 'Job Closing'),
]


class Job(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    job_id = models.IntegerField(null=True, blank=True)
    title = models.CharField(max_length=256, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    location = models.CharField(max_length=256, null=True, blank=True)
    job_type = models.CharField(
        max_length=100, choices=JOB_TYPE, null=True, blank=True)
    status = models.CharField(
        max_length=100, choices=JOB_STATUS, null=True, blank=True)
    image = models.FileField(verbose_name="Job File",
                             upload_to="job-files", blank=True, null=True)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default='active')
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)  # Log
            self._log_data = log_data

        if not self.job_id:

            # Generate a new order_id if it doesn't exist
            last_ = Job.objects.filter(
                workspace=self.workspace, job_id__isnull=False).order_by('job_id').last()
            if last_:
                if last_.job_id:
                    try:
                        last_ = int(last_.job_id)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.job_id = f"{next_id:04d}"

        super().save(*args, **kwargs)


class JobFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    job = models.ForeignKey(
        Job, on_delete=models.CASCADE, null=True, blank=True)
    file = models.FileField(verbose_name="Job File", upload_to="job-files")
    created_at = models.DateTimeField(default=timezone.now)


class JobNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=OBJECTS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=DEALS_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)


class JobValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        JobNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    job = models.ForeignKey(Job, on_delete=models.CASCADE, null=True,
                            blank=True, related_name='job_custom_field_relations')
    value = models.TextField(null=True, blank=True)
    file = models.FileField(verbose_name="Custom Field file",
                            upload_to="job-customfield-files", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)
            self._log_data = log_data

        if self.field_name and (self.field_name.type == 'tag'):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class JobValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(verbose_name="Job Custom Field file",
                            upload_to="job-custom-field-files")
    valuecustomfield = models.ForeignKey(
        JobValueCustomField, on_delete=models.CASCADE, null=True, blank=True)
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default='image')
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class JobScore(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True)
    value = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


JOB_APPLICATION_STATUS = [
    ('listed', 'Listed'),
    ('contacted', 'Contacted'),
    ('interview', 'Interview'),
    ('onboard', 'Onboard'),
]


class JobApplication(models.Model):
    workspace = models.ManyToManyField("Workspace", blank=True)
    job_applicant_id = models.IntegerField(null=True, blank=True)
    job = models.ForeignKey(
        Job, on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=256, null=True, blank=True)
    email = models.EmailField(max_length=256, null=True, blank=True)
    current_company = models.CharField(max_length=256, null=True, blank=True)
    url = models.TextField(null=True, blank=True)
    phone_number = models.CharField(max_length=256, null=True, blank=True)
    resume = models.FileField(
        verbose_name="Resume File", upload_to="resume-files", null=True, blank=True)
    resume_link = models.TextField(null=True, blank=True)
    img = models.TextField(null=True, blank=True)
    status = models.CharField(
        choices=JOB_APPLICATION_STATUS, max_length=256, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)  # Log
            self._log_data = log_data

        if not self.job_applicant_id:

            # Generate a new order_id if it doesn't exist
            last_ = JobApplication.objects.filter(
                job__workspace=self.job.workspace, job_applicant_id__isnull=False).order_by('job_applicant_id').last()
            if last_:
                if last_.job_applicant_id:
                    try:
                        last_ = int(last_.job_applicant_id)
                    except (ValueError, TypeError):
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.job_applicant_id = f"{next_id:04d}"

        super().save(*args, **kwargs)


class JobApplicantNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=OBJECTS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=DEALS_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        return str(self.workspace.name) + ' - ' + str(self.type) + " - " + str(self.name) + " - " + str(self.id)


class JobApplicantValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        JobApplicantNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    applicant = models.ForeignKey(
        JobApplication, on_delete=models.CASCADE, null=True,
        blank=True, related_name='applicant_custom_field_relations')
    value = models.TextField(null=True, blank=True)
    file = models.FileField(verbose_name="Custom Field file",
                            upload_to="job-applicant-customfield-files", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)  # Log
            self._log_data = log_data

        if self.field_name and (self.field_name.type == 'tag'):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)
        
class JobApplicantValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Job applicant Custom Field file",
        upload_to="job-applicant-custom-field-files")
    valuecustomfield = models.ForeignKey(
        JobApplicantValueCustomField, on_delete=models.CASCADE, null=True, blank=True)
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default='image')
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)






class Interview(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    job = models.ForeignKey(
        Job, on_delete=models.CASCADE, null=True, blank=True)
    interviewee = models.ForeignKey(
        JobApplication, on_delete=models.CASCADE, null=True, blank=True)
    interviewer = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    place = models.CharField(max_length=256, null=True, blank=True)
    time = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)


class ScoreCard(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    job = models.ForeignKey(
        Job, on_delete=models.CASCADE, null=True, blank=True)
    applicant = models.ForeignKey(
        JobApplication, on_delete=models.CASCADE, null=True, blank=True)
    author = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    score = models.ForeignKey(
        JobScore, on_delete=models.SET_NULL, null=True, blank=True)
    comments = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class SectionItemEstimate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    section_type = models.CharField(
        choices=SECTION_TYPES, max_length=30, null=True, blank=True)
    estimate = models.ForeignKey("Estimate", on_delete=models.CASCADE,
                                 null=True, blank=True, related_name='estimate_section_relations')
    position = models.IntegerField(null=True, blank=True)
    value = models.TextField(null=True, blank=True)


class SectionItemDeliverySlip(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    section_type = models.CharField(
        choices=SECTION_TYPES, max_length=30, null=True, blank=True)
    deliveryslip = models.ForeignKey("DeliverySlip", on_delete=models.CASCADE,
                                     null=True, blank=True, related_name='delivery_slip_section_relations')
    position = models.IntegerField(null=True, blank=True)
    value = models.TextField(null=True, blank=True)


class SectionItemInvoice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    section_type = models.CharField(
        choices=SECTION_TYPES, max_length=30, null=True, blank=True)
    invoice = models.ForeignKey("Invoice", on_delete=models.CASCADE,
                                null=True, blank=True, related_name='invoice_section_relations')
    position = models.IntegerField(null=True, blank=True)
    value = models.TextField(null=True, blank=True)


class SectionItemReceipt(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    section_type = models.CharField(
        choices=SECTION_TYPES, max_length=30, null=True, blank=True)
    receipt = models.ForeignKey("Receipt", on_delete=models.CASCADE,
                                null=True, blank=True, related_name='receipt_section_relations')
    position = models.IntegerField(null=True, blank=True)
    value = models.TextField(null=True, blank=True)
