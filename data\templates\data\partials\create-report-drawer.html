{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

<div class="card border-0 shadow-none rounded-0 w-100 bg-white">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if LANGUAGE_CODE == 'ja' %}
            ダッシュボードを作成
            {% else %}
            Create Dashboard
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5"
                data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                            transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                            fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>
    <div class="card-body overflow-scroll">
        <!-- Loading indicator -->
        <div id="drawer-loading" class="text-center py-5" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2 text-muted">
                {% if LANGUAGE_CODE == 'ja' %}
                読み込み中...
                {% else %}
                Loading...
                {% endif %}
            </div>
        </div>

        {% if channel_by_user %}
        <div class="mb-10">
            <form method="post" action="{% host_url 'report_detail' host 'app' %}">
                {% csrf_token %}
                <input type="hidden" name="workflow" value="{{workflow_id}}">
                
                <div class="mb-10">
                    <div class="fv-rowd-flex flex-column mb-8">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja' %}
                                名前
                                {% else %}
                                Name
                                {% endif %}
                            </span>
                        </label>
                        <input type="text" class="form-control"
                        {% if LANGUAGE_CODE == 'ja' %}
                        placeholder="名前" 
                        {% else %}
                        placeholder="Name" 
                        {% endif %}

                        name="report_name" required />
                    </div>
                    <div class=" w-100">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja' %}
                                ソース
                                {% else %}
                                Source
                                {% endif %}
                            </span>
                        </label>
                    </div>
                </div>


                <div class="" id='manage-metric'>
                    {% if channels_with_metrics %}
                        <div class="mb-8">
                            <div class="row g-3">
                                {% for channel_data in channels_with_metrics %}
                                    <div class="col-md-6 col-lg-4">
                                        <div class="card border border-gray-300 h-100 channel-card"
                                             onclick="selectChannel('{{ channel_data.channel.id }}')">
                                            <div class="card-body p-4">
                                                <div class="d-flex align-items-center mb-3">
                                                    <input type="radio"
                                                           class="form-check-input me-3"
                                                           name="channel_id"
                                                           value="{{ channel_data.channel.id }}"
                                                           id="channel_{{ channel_data.channel.id }}"
                                                           data-platform="{{ channel_data.channel.integration.slug }}"
                                                           onchange="onChannelSelect(this)">
                                                    {% with channel=channel_data.channel %}
                                                        {% include 'data/reports/reports-channel-social-icon.html' %}
                                                    {% endwith %}
                                                    <label for="channel_{{ channel_data.channel.id }}"
                                                           class="form-check-label fw-bold mb-0 cursor-pointer">
                                                        {{ channel_data.channel.name }}
                                                    </label>
                                                </div>
                                                <div class="text-muted small">
                                                    {{ channel_data.channel.integration.slug|title }}
                                                </div>
                                                <div class="mt-3 metrics-section" id="metrics_{{ channel_data.channel.id }}">
                                                    <label class="form-label small fw-bold">
                                                        {% if LANGUAGE_CODE == 'ja' %}
                                                        メトリクス
                                                        {% else %}
                                                        Metrics
                                                        {% endif %}
                                                    </label>
                                                    {% for metric in channel_data.metrics %}
                                                        <div class="form-check form-check-sm">
                                                            <input class="form-check-input"
                                                                   type="checkbox"
                                                                   name="metrics"
                                                                   value="{{ metric.value }}"
                                                                   id="metric_{{ channel_data.channel.id }}_{{ metric.value }}">
                                                            <label class="form-check-label small"
                                                                   for="metric_{{ channel_data.channel.id }}_{{ metric.value }}">
                                                                {{ metric.display }}
                                                            </label>
                                                        </div>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>

            
                <button type="submit" name="add-report" class="btn btn-primary ">
                    {% if LANGUAGE_CODE == 'ja' %}
                    リポートを作成
                    {% else %}
                    Add Report
                    {% endif %}
                </button>
            </form>
        </div>
        {% else %}
        <div class="p-10">
            {% if LANGUAGE_CODE == 'ja'%}
            コンテンツを投稿するためのアカウントが登録されていません。<a href="{% host_url 'integrations' host 'app' %}">インテグレーション</a>からアカウントを追加してください。
            {% else %}
            You don't have any account to publish content. Create one on <a href="{% host_url 'integrations' host 'app' %}">Integration</a>.
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

{% include 'data/javascript/reportJS.html' %}

<style>
    .channel-card {
        transition: all 0.2s ease-in-out;
        cursor: pointer;
    }

    .channel-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .channel-card.selected {
        border-color: #007bff !important;
        background-color: #f8f9ff;
    }

    .metrics-section {
        transition: all 0.3s ease-in-out;
        max-height: 0;
        overflow: hidden;
    }

    .metrics-section.show {
        max-height: 300px;
    }

    .form-check-input:checked + .form-check-label {
        color: #007bff;
        font-weight: 600;
    }
</style>

<script>
    // Optimized channel selection handler
    function selectChannel(channelId) {
        // Update radio button
        const radioElement = document.getElementById('channel_' + channelId);
        if (radioElement) {
            radioElement.checked = true;
        }

        // Update card styling
        document.querySelectorAll('.channel-card').forEach(function(card) {
            card.classList.remove('selected');
        });

        // Hide all metric sections first
        document.querySelectorAll('.metrics-section').forEach(function(element) {
            element.classList.remove('show');
        });

        // Show metrics for selected channel
        const selectedCard = document.querySelector(`[onclick="selectChannel('${channelId}')"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        const metricsSection = document.getElementById('metrics_' + channelId);
        if (metricsSection) {
            metricsSection.classList.add('show');
        }
    }

    function onChannelSelect(radioElement) {
        if (radioElement.checked) {
            selectChannel(radioElement.value);
        }
    }

    // Show/hide loading indicator
    function showLoading() {
        const loading = document.getElementById('drawer-loading');
        if (loading) loading.style.display = 'block';
    }

    function hideLoading() {
        const loading = document.getElementById('drawer-loading');
        if (loading) loading.style.display = 'none';
    }

    // Initialize form validation and drawer functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Hide loading indicator once content is ready
        hideLoading();

        const form = document.querySelector('form[action*="report_detail"]');
        if (form) {
            form.addEventListener('submit', function(e) {
                const selectedChannel = document.querySelector('input[name="channel_id"]:checked');
                if (!selectedChannel) {
                    e.preventDefault();
                    alert('{% if LANGUAGE_CODE == "ja" %}チャネルを選択してください{% else %}Please select a channel{% endif %}');
                    return false;
                }

                const selectedMetrics = document.querySelectorAll('input[name="metrics"]:checked');
                if (selectedMetrics.length === 0) {
                    e.preventDefault();
                    alert('{% if LANGUAGE_CODE == "ja" %}少なくとも1つのメトリクスを選択してください{% else %}Please select at least one metric{% endif %}');
                    return false;
                }

                // Show loading during form submission
                showLoading();
            });
        }

        // Pre-select first channel if available
        const firstChannel = document.querySelector('input[name="channel_id"]');
        if (firstChannel) {
            selectChannel(firstChannel.value);
        }
    });
</script>