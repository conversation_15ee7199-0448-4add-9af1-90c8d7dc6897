import ast
import json
import logging
from datetime import datetime
from datetime import time as datetime_time
from utils.reports import (SUMMARY_FIELDS, create_report,
                           get_panel_columns)
from django.core.paginator import EmptyPage, Paginator
from django.db import connection
from django.db.models import Max, Q
from django.http import HttpResponse, HttpResponseNotFound, JsonResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse
from data.constants.constant import (CHART_CHANNEL_STAT, CHART_POST_STAT,
                                     CHART_TYPE_NAME, PANEL_METRIC_TITLE,
                                     PANEL_TYPE_TITLE)
from data.constants.properties_constant import *
from data.constants.reports_constant import *
from data.models import *

from data.report.views import panel_export
from data.report.utility import get_panel_form_context
from utils.decorator import login_or_hubspot_required
from utils.properties.properties import (ALIAS_TABLE, get_field,
                                         get_list_object_columns,
                                         get_list_view_columns,
                                         get_page_object, get_report_metrics)
from utils.reports_bg_jobs.reports_event import check_event_properties
from utils.utility import get_workspace, is_valid_uuid, save_custom_property, assign_object_owner

logger = logging.getLogger(__name__)

@login_or_hubspot_required
def panel_form(request):
    workspace = get_workspace(request.user)
    if request.method == 'POST':
        print(request.POST)
        lang = request.LANGUAGE_CODE
        is_template = bool(request.POST.get('is_template', False))
        hidden_rows_str = None
        sheet_date_format = None
        sheet_date_group = None
        expanding_direction = None
        show_date_each_group_item = None
        need_confirmation = None

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PANEL]
        module_slug = request.POST.get('module')
        if not module_slug:
            module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_PANEL).order_by(
                'order', 'created_at').first()
            module_slug = module.slug

        type_objects = request.POST.get('object_source', None)

        if 'delete-panel' in request.POST:
            panel_id = request.POST.get('panel_id')
            if not panel_id:
                return HttpResponse(status=400)
            try:
                panel = ReportPanel.objects.get(id=panel_id)
                panel.delete()
                if module_slug:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + '?object_type=panels')
                return redirect(reverse('main', host='app'))

            except Exception as e:
                print(f'Error: {e}')
                return HttpResponse(status=404)

        if 'bulk_delete_panel' in request.POST:
            panel_ids = request.POST.getlist(
                'panel_ids', request.POST.getlist('panel_id'))
            no_redirect = request.POST.get('no-redirect', False)
            if not panel_ids:
                return HttpResponse(status=400)
            try:
                for panel_id in panel_ids:
                    panel = ReportPanel.objects.get(id=panel_id)
                    panel.delete()

                if no_redirect:
                    panels_queryset = ReportPanel.objects.filter(
                        workspace=workspace, is_template=False, is_deleted=False).order_by('-created_at')
                    paginator = Paginator(panels_queryset, MAX_PAGE_CONTENT)
                    page = request.POST.get('page', 1)
                    try:
                        panels = paginator.page(page)
                    except EmptyPage:
                        page = paginator.num_pages
                        panels = paginator.page(page)
                    paginator_item_begin = (
                        MAX_PAGE_CONTENT*int(page))-(MAX_PAGE_CONTENT-1)
                    paginator_item_end = MAX_PAGE_CONTENT * \
                        int(page) if MAX_PAGE_CONTENT * \
                        int(page) < paginator.count else paginator.count
                    context = {
                        'panels': panels,
                        'paginator': paginator,
                        'paginator_item_begin': paginator_item_begin,
                        'paginator_item_end': paginator_item_end,
                        'not_render_template': True,
                        'PANEL_METRIC_TITLE': PANEL_METRIC_TITLE,
                    }
                    return render(request, 'data/reports/panel-table-container.html', context)

                if is_template:
                    try:
                        panel_tpl = PanelTemplate.objects.filter(
                            panel_id__in=panel_ids).delete()
                    except:
                        pass
                    return redirect(reverse('manage_data', host='app'))

                if module_slug:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + '?object_type=panels')
                return redirect(reverse('main', host='app'))

            except Exception as e:
                print(f'Error: {e}')
                return HttpResponse(status=404)

        if 'bulk_activate_panel' in request.POST:
            panel_ids = request.POST.getlist(
                'panel_ids', request.POST.getlist('panel_id'))
            if not panel_ids:
                return HttpResponse(status=400)
            try:
                for panel_id in panel_ids:
                    panel = ReportPanel.objects.get(id=panel_id)
                    panel.is_deleted = False
                    panel.save()

                if module_slug:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + '?object_type=panels')
                return redirect(reverse('main', host='app'))
            except Exception as e:
                print(f'Error: {e}')
                return HttpResponse(status=404)

        if 'bulk_duplicate_panel' in request.POST:
            panel_ids = request.POST.getlist(
                'panel_ids', request.POST.getlist('panel_id'))
            if not panel_ids:
                return HttpResponse(status=400)
            try:
                last_panel = ReportPanel.objects.order_by('-panel_id').first()
                last_panel_id = last_panel.panel_id if last_panel else 0
                for panel_id in panel_ids:
                    last_panel_id += 1
                    panel = ReportPanel.objects.get(id=panel_id)
                    report_sheet_rows = ReportPanelSheetRows.objects.filter(
                        report_panel=panel)
                    panel_sheet = ReportPanelSheet.objects.filter(
                        report_panel=panel)
                    panel_metrics = panel.metrics.all()

                    panel.id = None
                    panel.panel_id = last_panel_id
                    panel.created_by = request.user
                    panel.save()

                    for metric in panel_metrics:
                        panel.metrics.add(PanelMetric.objects.create(
                            name=metric.name,
                            metric=metric.metric,
                            metric_type=metric.metric_type,
                            role=metric.role,
                            order=metric.order,
                            filter=metric.filter,
                            created_at=metric.created_at,
                            data_source=metric.data_source,
                            sort=metric.sort,
                            meta_data=metric.meta_data,
                        ))

                    for sheet in report_sheet_rows:
                        sheet.id = None
                        sheet.report_panel = panel

                    for sheet in panel_sheet:
                        sheet.id = None
                        sheet.report_panel = panel

                if module_slug:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + '?object_type=panels')
                return redirect(reverse('main', host='app'))
            except Exception as e:
                print(f'Error: {e}')
                return HttpResponse(status=404)

        panel_id = request.POST.get('panel_id', '')
        if "download" in request.POST:
            return panel_export(request, panel_id)
        
        owner = request.POST.get('owner', None)

        name = request.POST.get('name', '')
        panel_type = request.POST.get('panel_type', '')
        data_source_type = request.POST.get('data_source_type', 'app')
        pivot_row_group_by = request.POST.get('pivot-row-display-frequency')
        pivot_row_sort_direction = request.POST.get('pivot-row-display-sort-direction')

        if panel_type == 'sheet':
            date_range = request.POST.get('sheet_date_range', '')
            start_date_str, end_date_str = date_range.split(' - ')
            start_date_str = start_date_str.replace(
                '年', '-').replace('月', '-').replace('日', '')
            end_date_str = end_date_str.replace(
                '年', '-').replace('月', '-').replace('日', '')

            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            start_time_obj = datetime.combine(start_date, datetime_time.min)
            end_time_obj = datetime.combine(end_date, datetime_time.max)

            sheet_date_format = request.POST.get('date-format')
            sheet_date_group = request.POST.get('date-group-by')
            expanding_direction = request.POST.get('expanding_direction')
            show_date_each_group_item = request.POST.get('show_date_each_group_item')
            need_confirmation = request.POST.get('need_confirmation')

            if request.POST.get('hidden_rows'):
                rows = ast.literal_eval(request.POST.get('hidden_rows'))
                hidden_rows_str = ','.join(item['value'] for item in rows)

        data_sources = []
        metrics = []

        pivot_row = None
        pivot_column = None
        pivot_value = None

        raw_query = request.POST.get('raw_query', None)
        display_raw_query = request.POST.get('display_setting_raw_query', None)

        columns = []
        iterate = 1
        pivot_values = []
        pivot_aggregates = []
        pivot_value_display = []
        for col in request.POST:
            if col.startswith('metric_category -'):
                id = col.split(' - ')[1]
                for metric_type in request.POST.getlist(col):
                    for metric in metrics:
                        if metric['id'] == id:
                            metric['metric_type'] = metric_type

            if col.startswith('column-') and not col.startswith('column-role-'):
                role_key = col.replace('column-', '', 1)
                role_key = "column-role-"+role_key
                role = request.POST.get(role_key, 'column')
                column = {
                    col: request.POST[col],
                    'order': iterate,
                    'role': role
                }
                iterate += 1
                columns.append(column)

            elif col.startswith('data_source - '):
                if panel_type in 'table' or panel_type == 'pivot_table' or (panel_type in ['chart', 'stacked_chart', 'summary_table', 'forecast'] and 'custom  -  custom - Custom Chart' in request.POST.getlist(col)):
                    data_sources = request.POST.getlist(col)

                    if panel_type == 'pivot_table':
                        pivot_row = request.POST.get('pivot-row', '')
                        pivot_row_filter_order = request.POST.get(
                            'pivot-row-filter-order', '')
                        pivot_row_filter_sort_by = request.POST.get(
                            'pivot-row-filter-sort-by', '')
                        pivot_column = request.POST.get('pivot-column', '')
                        pivot_column_filter_order = request.POST.get(
                            'pivot-column-filter-order', '')
                        pivot_column_filter_sort_by = request.POST.get(
                            'pivot-column-filter-sort-by', '')

                    id = col.split(' - ')[1]
                    continue

                id = col.split(' - ')[1]
                for data_source in request.POST.getlist(col):
                    parts = data_source.split('  -  ')
                    if len(parts) > 1:
                        data_source = parts[0]
                        axes = {
                            'id': id,
                            'x': request.POST.get(f'metric_type - {id}', 'date'),
                            'y': parts[1]
                        }

                        if (parts[1] == 'number_cases - Number of Cases'):
                            axes['x'] = 'created_at'

                        metrics.append(axes)
                        data_sources.append(data_source)

            elif col.startswith('pivot_value'):
                key, idx = col.split('-', 1)
                pivot_value = request.POST.get(col)
                aggregate_value = request.POST.get(f"pivot_aggregate-{idx}")
                display_value = request.POST.get(f"pivot-value-display-{idx}")

                pivot_values.append(pivot_value)
                pivot_aggregates.append(aggregate_value)
                pivot_value_display.append(display_value)

        panel_filter = None
        for req in request.POST:
            if req.startswith('panel-filter-select'):
                filter_select = request.POST.getlist(req)
                filter_input = request.POST.getlist('panel-filter-input')
                filter_operator = request.POST.getlist('panel-filter-operator')
                filter_type = request.POST.getlist('panel-filter-type')
                filter_source = request.POST.getlist('panel-filter-source')

                parsed_filters = []
                for i, select in enumerate(filter_select):
                    filter_input_ = filter_input[i]
                    if {'年', '月', '日', '〜'}.issubset(set(filter_input_)):
                        start, end = filter_input_.split(' 〜 ')
                        start = start.strip()
                        end = end.strip()

                        start = start.replace(
                            '年', '-').replace('月', '-').replace('日', '')
                        end = end.replace(
                            '年', '-').replace('月', '-').replace('日', '')

                        filter_input_ = f"{start} - {end}"
                    try:
                        pf = {
                            'filter_select': select,
                            'filter_input': [
                                {'value': filter_input_}
                            ],
                            'filter_operator': filter_operator[i],
                            'filter_type': filter_type[i],
                            'filter_source': filter_source[i],
                        }
                        parsed_filters.append(pf)
                    except Exception as e:
                        print(f'ERROR === reports.py - 1672: {e}')

                filter = {
                    'filters': parsed_filters,
                }
                panel_filter = filter

        filters = []
        for req in request.POST:
            if req.startswith('filter-select-'):
                id = req.split('-', 2)[2]
                filter_select = request.POST.getlist(req)
                filter_input = request.POST.getlist(f'filter-input-{id}')
                filter_operator = request.POST.getlist(f'filter-operator-{id}')
                filter_type = request.POST.getlist(f'filter-type-{id}')
                filter_source = request.POST.getlist(f'filter-source-{id}')
                if request.POST.get(f'column-{id}') not in ['date', 'dates']:
                    if filter_input:
                        filter_input = [json.loads(json.dumps(
                            [{'value': val}])) for val in filter_input]

                parsed_filters = []
                for i, select in enumerate(filter_select):
                    try:
                        pf = {
                            'filter_select': select,
                            'filter_input': filter_input[i],
                        }
                        if panel_type in ['chart', 'stacked_chart', 'funnel_chart', 'summary_table', 'forecast']:
                            pf['filter_operator'] = filter_operator[i]
                            pf['filter_type'] = filter_type[i]
                            pf['filter_source'] = filter_source[i]

                        parsed_filters.append(pf)
                    except Exception as e:
                        print(f'ERROR === reports.py - 1706: {e}')

                filter = {
                    'id': id,
                    'filters': parsed_filters,
                }

                filters.append(filter)

        print(f'INFO >>> {filters}')

        sheet_col_props = None
        for req in request.POST:
            if req.startswith('custom_rows_property_rows - '):
                id = req.split(' - ', 2)[1]
                prop_rows = request.POST.get(
                    f'custom_rows_property_rows - {id}')
                prop_display = request.POST.get(
                    f'custom_rows_property_display - {id}')
                prop_rowspan = request.POST.get(
                    f'custom_rows_property_rowspan - {id}')
                prop_bg_col = request.POST.get(
                    f'custom_rows_property_bg_color - {id}')

                if sheet_col_props is None:
                    sheet_col_props = []

                sheet_col_props.append({
                    'rows': prop_rows,
                    'display': prop_display,
                    'rowspan': prop_rowspan,
                    'bg_color': prop_bg_col,
                })

        name_ja = request.POST.get('name_ja', None)
        description = request.POST.get('description', None)
        description_ja = request.POST.get('description_ja', None)
        is_template = request.POST.get('is_template', False)
        if is_template:
            is_template = True
        if panel_id:
            try:
                panel = ReportPanel.objects.get(id=panel_id)
            except:
                if module_slug:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + '?object_type=panels')
                return redirect(reverse('main', host='app'))

            panel.name = name
            panel.name_ja = name_ja
            panel.description = description
            panel.description_ja = description_ja
            panel.data_source_type = data_source_type
            panel.data_source = ','.join(data_sources)
            panel.panel_type = panel_type
            panel.is_template = is_template

            assign_object_owner(panel,owner,request,TYPE_OBJECT_PANEL)

            if panel_type == 'sheet':
                sheet_header_hide = True if request.POST.get(
                    'hide-header') else False
                blank_space = int(request.POST.get('property_blank_space')) if request.POST.get(
                    'property_blank_space').isdigit() else 0
                sheet_template = request.POST.get('template_source', '')

                panel.start_time = start_time_obj
                panel.end_time = end_time_obj
                panel.sheet_rows = request.POST.getlist(
                    'rows_sheet_group_display[]')
                panel.sheet_group_by = request.POST.get('rows_sheet_group_by_object', None)

                panel.hide_rows = hidden_rows_str
                panel.date_format = sheet_date_format
                panel.group_by = sheet_date_group
                panel.sheet_expanding_item = expanding_direction
                panel.is_duplicate_header = show_date_each_group_item
                panel.hide_sheet_header = sheet_header_hide
                panel.blank_space_sheet = blank_space
                panel.panel_property = sheet_col_props
                panel.sheet_template = sheet_template

                if need_confirmation == 'yes':
                    ReportPanelSheet.objects.filter(
                        report_panel=panel).delete()

            panel.save()

            if panel_type == 'table' or panel_type == 'pivot_table':
                if panel_type == 'pivot_table':
                    panel.metrics.clear()
                    if pivot_values and pivot_aggregates:
                        metrics_to_add = [
                            ('pivot-row', pivot_row),
                            ('pivot-column', pivot_column),
                            ('pivot-value', pivot_values),
                            ('pivot-aggregate', pivot_aggregates),
                            ('pivot-value-display', pivot_value_display),
                        ]

                        for name, metric in metrics_to_add:
                            panel.metrics.add(PanelMetric.objects.create(
                                name=name,
                                metric=metric,
                                metric_type='normal',
                                data_source=data_sources[0]
                            ))

                        if pivot_row:
                            panel.metrics.add(PanelMetric.objects.create(
                                name='pivot-row-filter-order',
                                metric=pivot_row_filter_order,
                                metric_type='normal',
                                data_source=data_sources[0]
                            ))
                            panel.metrics.add(PanelMetric.objects.create(
                                name='pivot-row-filter-sort-by',
                                metric=pivot_row_filter_sort_by,
                                metric_type='normal',
                                data_source=data_sources[0]
                            ))

                        if pivot_column:
                            panel.metrics.add(PanelMetric.objects.create(
                                name='pivot-column-filter-order',
                                metric=pivot_column_filter_order,
                                metric_type='normal',
                                data_source=data_sources[0]
                            ))
                            panel.metrics.add(PanelMetric.objects.create(
                                name='pivot-column-filter-sort-by',
                                metric=pivot_column_filter_sort_by,
                                metric_type='normal',
                                data_source=data_sources[0]
                            ))
                else:
                    for metric in panel.metrics.all():
                        name = ''
                        val = ''
                        filters_val = {}

                        for column in columns:
                            for key in column.keys():
                                if key.startswith('column-'):
                                    id = key.split('-', 1)[1]
                                    break

                            if str(metric.id) == id:
                                parts = [part.strip()
                                         for part in column['column-'+id].split('-')]
                                ds = parts[0]
                                val = f"{parts[0]} - {parts[1]}"
                                name = ' - '.join(parts[2:]
                                                  ) if len(parts) > 2 else parts[1]
                                order = column['order']
                                role = column['role']
                                columns.remove(column)
                                break
                            else:
                                val = ''
                                order = 0

                        for filter in filters:
                            if str(metric.id) == filter['id']:
                                filters_val = filter
                                break
                            else:
                                filters_val = {}

                        if filters_val == {}:
                            metric.filter = None
                        else:
                            metric.filter = filters_val

                        if val == '':
                            for column in columns:
                                if str(metric.id) == list(column.keys())[0].split('-', 1)[1]:
                                    columns.remove(column)
                            metric.delete()
                            continue

                        metric.metric_type = 'manual'

                        metric.name = name
                        metric.metric = val
                        metric.order = order
                        metric.role = role
                        metric.data_source = ds
                        metric.save()

        else:
            panel = ReportPanel(workspace=workspace,
                                name=name,
                                name_ja=name_ja,
                                description=description,
                                description_ja=description_ja,
                                data_source_type=data_source_type,
                                data_source=','.join(data_sources),
                                panel_type=panel_type,
                                is_template=is_template,
                                created_by=request.user)

            assign_object_owner(panel,owner,request,TYPE_OBJECT_PANEL)

            if panel_type == 'sheet':
                sheet_header_hide = True if request.POST.get(
                    'hide-header') else False
                blank_space = int(request.POST.get('property_blank_space')) if request.POST.get(
                    'property_blank_space').isdigit() else 0
                sheet_template = request.POST.get('template_source', '')

                panel.start_time = start_time_obj
                panel.end_time = end_time_obj
                panel.sheet_rows = request.POST.getlist(
                    'rows_sheet_group_display[]')
                panel.sheet_group_by = request.POST.get('rows_sheet_group_by_object', None)
                panel.hide_rows = hidden_rows_str
                panel.date_format = sheet_date_format
                panel.hide_sheet_header = sheet_header_hide
                panel.group_by = sheet_date_group
                panel.sheet_expanding_item = expanding_direction
                panel.is_duplicate_header = show_date_each_group_item
                panel.blank_space_sheet = blank_space
                panel.panel_property = sheet_col_props
                panel.sheet_template = sheet_template

            panel.save()
            
        if panel.panel_type == 'sheet':
            ReportPanelSheetTab.objects.create(
                report_panel=panel,
                name="Sheet1"
            )

        if panel.panel_type == 'pivot_table':
            panel.group_by = pivot_row_group_by
            panel.row_sort_direction = pivot_row_sort_direction
        if panel_filter:
            panel.filter = panel_filter
            panel.save()
        else:
            panel.filter = None
            panel.save()

        if panel.panel_type == 'table' or panel.panel_type == 'pivot_table':
            if panel.panel_type == 'pivot_table':
                if pivot_values and pivot_aggregates:
                    pivot_metrics = [
                        ('pivot-row', pivot_row),
                        ('pivot-column', pivot_column),
                        ('pivot-value', pivot_values),
                        ('pivot-aggregate', pivot_aggregates),
                        ('pivot-value-display', pivot_value_display),
                    ]

                    for name, metric in pivot_metrics:
                        panel.metrics.add(PanelMetric.objects.create(
                            name=name,
                            metric=metric,
                            metric_type='normal',
                            data_source=data_sources[0]
                        ))

                    if pivot_row:
                        panel.metrics.add(PanelMetric.objects.create(
                            name='pivot-row-filter-order',
                            metric=pivot_row_filter_order,
                            metric_type='normal',
                            data_source=data_sources[0]
                        ))
                        panel.metrics.add(PanelMetric.objects.create(
                            name='pivot-row-filter-sort-by',
                            metric=pivot_row_filter_sort_by,
                            metric_type='normal',
                            data_source=data_sources[0]
                        ))

                    if pivot_column:
                        panel.metrics.add(PanelMetric.objects.create(
                            name='pivot-column-filter-order',
                            metric=pivot_column_filter_order,
                            metric_type='normal',
                            data_source=data_sources[0]
                        ))
                        panel.metrics.add(PanelMetric.objects.create(
                            name='pivot-column-filter-sort-by',
                            metric=pivot_column_filter_sort_by,
                            metric_type='normal',
                            data_source=data_sources[0]
                        ))
                else:
                    panel.delete()
                    if lang == 'ja':
                        Notification.objects.create(
                            workspace=workspace, user=request.user, message="ピボット行、ピボット列、ピボット値を選択。", type="error")
                    else:
                        Notification.objects.create(
                            workspace=workspace, user=request.user, message="Please select the pivot row, pivot column and pivot value.", type="error")
                    if module_slug:
                        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + '?object_type=panels')
                    return redirect(reverse('main', host='app'))
            else:
                for column in columns:
                    try:
                        for key in column.keys():
                            if key.startswith('column-'):
                                id = key.split('-', 1)[1]
                                parts = [part.strip()
                                         for part in column['column-'+id].split('-')]
                                ds = parts[0]
                                val = f"{parts[0]} - {parts[1]}"
                                name = ' - '.join(parts[2:]
                                                  ) if len(parts) > 2 else parts[1]
                                break

                        order = column['order']
                        role = column['role']
                        filter_val = {}
                        for filter in filters:
                            if id == filter['id']:
                                filter_val = filter
                                break
                            else:
                                filter_val = {}

                        panel.metrics.add(PanelMetric.objects.create(
                            role=role, name=name, metric=val, filter=filter_val if filter_val else None, order=order, data_source=ds))
                    except Exception as e:
                        print(f'ERROR === reports.py - 1811: {e}')

        elif panel.panel_type in ['chart', 'stacked_chart', 'summary_table', 'forecast'] and 'custom  -  custom - Custom Chart' in data_sources:
            print('Custom chart')
            # Reset metrics
            metrics = panel.metrics.all()
            for metric in metrics:
                metric.delete()
            panel.metrics.clear()

            formula = {'variables': {}}
            metric_name = ''
            for _key in request.POST:
                if 'variable' in _key and 'filter' not in _key:
                    _id = _key.split(' - ')[1]
                    _type = _key.split(' - ')[2]
                    _value = request.POST.get(_key)
                    if _id not in formula['variables']:
                        formula['variables'][_id] = {}
                    formula['variables'][_id][_type] = _value

                elif _key.startswith('variable_filter') and 'property' in _key:
                    print('GET Formula filter')
                    _property = request.POST.get(_key)
                    filter_id = _key.split(' - ')[1]
                    _id = _key.split(' - ')[2]
                    _operator = request.POST.get(
                        'variable_filter - '+filter_id+' - '+_id+' - operator')
                    value_type = request.POST.get(
                        'property-filter-value-type - '+filter_id+' - '+_id)
                    if value_type == 'text':
                        _filter_value = request.POST.get(
                            f"variable_filter - {filter_id} - {_id} - text_value")
                    else:
                        _filter_value = request.POST.get(
                            f"variable_filter - {filter_id} - {_id} - select_value")
                    if _id not in formula['variables']:
                        formula['variables'][_id] = {'filters': []}
                    if 'filters' not in formula['variables'][_id]:
                        formula['variables'][_id]['filters'] = []
                    formula['variables'][_id]['filters'].append({
                        "filter_select": _property,
                        "filter_operator": _operator,
                        "filter_input": [{'value': _filter_value}],
                        "filter_type": "string"
                    })
                    print(formula)

                elif 'formula' in _key:
                    _value = request.POST.get(_key).strip()
                    formula['math'] = _value.upper()

                elif 'metric_name' in _key:
                    metric_name = request.POST.get(_key)

            panel.metrics.add(PanelMetric.objects.create(
                role='y_axis', name=metric_name, metric='formula', data_source=panel.data_source, meta_data=formula
            ))

        elif 'chart' in panel.panel_type or panel.panel_type == 'summary_table' or panel.panel_type == 'forecast' or panel.panel_type == 'bar':
            # Delete unused metrics
            keep_metric_ids = []
            for i, _ in enumerate(data_sources):
                if is_valid_uuid(metrics[i]['id']):
                    keep_metric_ids.append(metrics[i]['id'])
            if len(panel.metrics.all()) > 0:
                panel.metrics.exclude(id__in=keep_metric_ids).delete()

            for i, data_source in enumerate(data_sources):
                print('...........', metrics[i])
                y_axis_metric_name = metrics[i]['y'].rsplit(' - ', 1)[0]
                if panel.panel_type in ['chart', 'stacked_chart', 'summary_table', 'forecast'] and data_source == 'events':
                    y_axis_metric_name = f'{metrics[i]["x"]}-' + \
                        y_axis_metric_name

                try:
                    y_axis_metric = PanelMetric.objects.get(
                        id=metrics[i]['id'])
                except Exception as e:
                    print('ERRORRRRRRRRRRRRRRRR', e)
                    panel.metrics.add(PanelMetric.objects.create(
                        role='x_axis', name=metrics[i]['x'], metric=metrics[i]['x'], data_source=data_source))

                    y_axis_metric = PanelMetric(role='y_axis')
                    y_axis_metric.save()
                    panel.metrics.add(y_axis_metric)
                    for filter in filters:
                        if metrics[i]['id'] == filter['id']:
                            filter['id'] = str(y_axis_metric.id)
                            break

                y_axis_metric.name = metrics[i]['y'].rsplit(' - ', 1)[1]
                y_axis_metric.metric = y_axis_metric_name
                y_axis_metric.data_source = data_source
                y_axis_metric.filter = None

                if panel.panel_type == 'summary_table':
                    y_axis_metric.raw_sql = raw_query
                    y_axis_metric.display_result = display_raw_query

                # Just linechart which have metric_type
                if panel.panel_type == 'chart':
                    y_axis_metric_type = metrics[i].get('metric_type', None)
                    y_axis_metric.metric_type = y_axis_metric_type

                y_axis_metric.save()

            for filter in filters:
                try:
                    y_axis_metric = PanelMetric.objects.get(
                        id=filter['id'].strip())
                    print('filter for ', y_axis_metric)
                    y_axis_metric.filter = filter
                    y_axis_metric.save()
                except Exception as e:
                    print('ERRORRRRRRRRRRRRRRRR', e)
                    pass

        elif panel.panel_type == 'sheet':
            for row in panel.reportpanelsheetrows_set.all():
                row.delete()

            for req in request.POST:
                if req.startswith('rows_source -'):
                    id = req.split(' - ', 2)[1]
                    display = request.POST.get(f'rows_source_display - {id}')
                    grouped_date = request.POST.get(f'date-grouped-by-{id}')
                    formula = request.POST.get(f'rows_source_formula - {id}')
                    formula = formula if formula != '' else None

                    filter_rows_select = request.POST.getlist(
                        f'filter-rows-select-{id}')
                    filter_rows_input = request.POST.getlist(
                        f'filter-rows-input-{id}')
                    filter_rows_operator = request.POST.getlist(
                        f'filter-rows-operator-{id}')
                    filter_rows_type = request.POST.getlist(
                        f'filter-rows-type-{id}')
                    filter_rows_source = request.POST.getlist(
                        f'filter-rows-source-{id}')

                    rows_filter = []
                    for i, select in enumerate(filter_rows_select):
                        try:
                            pf = {
                                'filter_select': select,
                                'filter_input': [
                                    {'value': filter_rows_input[i]}
                                ],
                                'filter_operator': filter_rows_operator[i],
                                'filter_type': filter_rows_type[i],
                                'filter_source': filter_rows_source[i],
                            }
                            rows_filter.append(pf)
                        except Exception as e:
                            print(f'ERROR === reports.py - 1672: {e}')

                    if len(rows_filter) <= 0:
                        rows_filter = None

                    try:
                        panel_sheet_row = ReportPanelSheetRows.objects.get(
                            id=id)
                        panel_sheet_row.rows_source = request.POST.get(req)
                        panel_sheet_row.rows_display = display
                        panel_sheet_row.filter = rows_filter
                        panel_sheet_row.rows_formula = formula
                        panel_sheet_row.grouped_date_field = grouped_date
                        panel_sheet_row.save()
                    except:
                        ReportPanelSheetRows.objects.create(report_panel=panel, rows_source=request.POST.get(
                            req), filter=rows_filter, rows_display=display, rows_formula=formula, grouped_date_field=grouped_date)

        if is_template and False: # Not sure the logic below
            try:
                panel_tpl = PanelTemplate.objects.get(panel=panel)
            except:
                panel_tpl = PanelTemplate()

            panel_tpl.name = request.POST.get('name', '')
            panel_tpl.name_ja = request.POST.get('name_ja', '')
            panel_tpl.category = request.POST.get('category', '')
            panel_tpl.category_ja = request.POST.get('category_ja', '')
            panel_tpl.overview = request.POST.get('overview', '')
            panel_tpl.overview_ja = request.POST.get('overview_ja', '')
            panel_tpl.description = request.POST.get('description', '')
            panel_tpl.description_ja = request.POST.get('description_ja', '')
            panel_tpl.hide = bool(request.POST.get('hide', False))
            panel_tpl.workspace_id = request.POST.get('test_workspace_id')
            panel_tpl.panel = panel
            panel_tpl.save()
            return redirect(reverse('manage_template', host='app'))

        report_ids = request.POST.getlist('report_ids')
        if report_ids:
            for report_id in report_ids:
                order = PanelReportPanel.objects.filter(
                    report__id=report_id).aggregate(Max('order'))['order__max'] or 0
                try:
                    report_panel = PanelReportPanel.objects.get(
                        report_id=report_id, panel_id=panel_id)
                    continue
                except:
                    report_panel = PanelReportPanel(
                        report_id=report_id, panel_id=panel.id, order=order+1)
                report_panel.save()

        breakdown_id = request.POST.get('breakdown_id')
        if breakdown_id:
            # event_property = EventNameCustomProperties.objects.get(id=breakdown_id)
            panel.breakdown = breakdown_id
            panel.save()
        else:
            panel.breakdown = None
            panel.save()

        x_axis = request.POST.get('x_axis_id')
        if x_axis:
            panel.x_axis = x_axis
        else:
            panel.x_axis = None
            
        is_forecast = request.POST.get('is_breakdown', False)
        is_stacked = request.POST.get('is_panel_stacked', False)
        panel.is_forecast = is_forecast
        panel.is_stacked_chart = is_stacked

        panel.type_objects = type_objects
        panel.save()
        save_custom_property(request, panel)
        if panel_id:
            PanelReportPanel.objects.filter(panel__id=panel_id).exclude(
                report_id__in=report_ids).delete()
            return redirect(reverse('panel_view', kwargs={'panel_id': panel.id}, host='app'))
        if request.POST.get('addon-dashboard', None):
            response = HttpResponse(200)
            response["HX-Trigger"] = json.dumps({
                "showDashboardDrawer": {"event_panel_id": str(panel.id)},
            })
            return response

        if module_slug:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + '?object_type=panels')
        return redirect(reverse('main', host='app'))

    elif request.method == 'GET':
        table_properties = get_panel_columns(workspace)
        table_properties['items']
        obj_trans = REPORT_OBJECT_GROUP_TYPE
        trans_filter = {}

        for table in TYPE_OBJECTS + [TYPE_OBJECT_CASE_LINE_ITEM, TYPE_OBJECT_ORDER_LINE_ITEM]:
            if table not in [TYPE_OBJECT_PANEL, TYPE_OBJECT_DASHBOARD]:
                obj = get_page_object(table)
                base_model = obj['base_model']
                custom_model = obj['custom_model']
                columns_display = obj['columns_display']
                table_properties[table] = []
                trans_filter[table] = {}
                # related_models = obj.get('related_data', None)
                related_models = []

                # Skip processing if base_model is None (object type not fully implemented)
                if base_model is None:
                    continue

                obj_trans_ = obj_trans.get(table)
                obj_trans_en = obj_trans_.get('en')
                obj_trans_ja = obj_trans_.get('ja')

                for field in base_model._meta.fields:
                    if field.is_relation:
                        related_model = field.related_model
                        related_fields = [f.name for f in related_model._meta.get_fields(
                        ) if not f.is_relation or f.one_to_one or f.many_to_one]

                        model_name = related_model.__name__
                        model_name = model_name.lower()
                        if 'shopturbo' in model_name:
                            model_name = model_name.replace(
                                "shopturbo", "commerce_")
                            if 'subscription' in model_name:
                                model_name = model_name[:-
                                                        1] if model_name.endswith("s") else name
                        elif model_name in ['invoice', 'contact']:
                            model_name = f"{model_name}s"
                        elif 'payment' == model_name:
                            model_name = TYPE_OBJECT_EXPENSE

                        model_trans = OBJECT_GROUP_TYPE.get(model_name, {
                            "en": model_name,
                            "ja": model_name,
                        })

                        for related_field in related_fields:
                            value = f"{field.name}__{related_field}"

                            trans = columns_display.get(related_field)
                            display_en = trans.get('en') if trans else value
                            display_ja = trans.get('ja') if trans else value

                            model_en = model_trans.get('en', model_name)
                            model_ja = model_trans.get('ja', model_name)

                            display_en = f"{obj_trans_en} - ({model_en}) {display_en}"
                            display_ja = f"{obj_trans_ja} - ({model_ja}) {display_ja}"

                            table_properties[table].append({
                                'value': f"{table} - {value}",
                                'display': display_en,
                                'display_ja': display_ja,
                            })

                            trans_filter[table][value] = {
                                'en': display_en,
                                'ja': display_ja
                            }
                    else:
                        value = field.name
                        trans = columns_display.get(field.name)

                        display_en = trans.get('en') if trans else value
                        display_ja = trans.get('ja') if trans else value

                        display_en = f"{obj_trans_en} - {display_en}"
                        display_ja = f"{obj_trans_ja} - {display_ja}"

                        table_properties[table].append({
                            'value': f"{table} - {value}",
                            'display': display_en,
                            'display_ja': display_ja,
                        })
                        trans_filter[table][value] = {
                            'en': display_en,
                            'ja': display_ja
                        }

                if related_models:
                    for related_model in related_models:
                        if related_model == 'order':
                            related_model = TYPE_OBJECT_ORDER
                        elif related_model == 'journal':
                            related_model = TYPE_OBJECT_JOURNAL

                        prefix = ""
                        if table == TYPE_OBJECT_ITEM and related_model == TYPE_OBJECT_ORDER:
                            prefix = "shopturbo_item_orders__"
                        if table == TYPE_OBJECT_ORDER and related_model == TYPE_OBJECT_ITEM:
                            prefix = "shopturboitemsorders__item__"
                        if table == TYPE_OBJECT_ORDER and related_model == TYPE_OBJECT_INVENTORY_TRANSACTION:
                            prefix = "inventory_transactions__"
                        related_obj_trans_ = obj_trans.get(related_model)
                        related_obj_trans_en = related_obj_trans_.get('en')
                        related_obj_trans_ja = related_obj_trans_.get('ja')
                        related_model_obj = get_page_object(related_model)
                        related_model = related_model_obj['base_model']
                        for field in related_model._meta.fields:
                            if field.is_relation:
                                value = f"{field.related_model._meta.db_table}__{field.name}"
                            else:
                                value = field.name

                            trans = columns_display.get(field.name)

                            display_en = trans.get('en') if trans else value
                            display_ja = trans.get('ja') if trans else value

                            display_en = f"{related_obj_trans_en} - {display_en}"
                            display_ja = f"{related_obj_trans_ja} - {display_ja}"

                            table_properties[table].append({
                                'value': f"{prefix}{value}",
                                'display': display_en,
                                'display_ja': display_ja,
                            })

                if custom_model:
                    for field in custom_model.objects.filter(workspace=workspace):
                        if field.name:
                            table_properties[table].append({
                                'value': f"{table} - custom_field__{field.id}",
                                'display': f"{obj_trans_en} - {field.name}",
                                'display_ja': f"{obj_trans_ja} - {field.name}",
                            })

                            trans_filter[table][f"{field.id}"] = {
                                'en': field.name,
                                'ja': field.name
                            }

        merged_dict = PANEL_FILTER.copy()
        merged_dict.update(trans_filter)
        add_on = request.GET.get('addon_source', None)
        context = get_panel_form_context(request)
        context['create_type'] = add_on
        context['download_view'] = request.GET.get('download_view')
        report_id = request.GET.get('report_id')
        context['report_id'] = request.GET.get('report_id')
        context['PANEL_METRIC_TITLE'] = PANEL_METRIC_TITLE
        context['PANEL_TYPE_TITLE'] = PANEL_TYPE_TITLE
        context['PANEL_SOURCE_TITLE'] = PANEL_SOURCE_TITLE
        context['TABLE_PANEL_COLUMNS'] = table_properties
        context['PANEL_SOURCE_TITLE_TYPE'] = PANEL_METRIC_TYPE
        context['PANEL_FILTER'] = merged_dict
        context['PANEL_METRIC_ROLE'] = CONST_PANEL_METRIC_ROLE
        context['SHEET_ROW_OPTION'] = SHEET_ROW_OPTION
        context['CURRENCY_MODEL'] = CURRENCY_MODEL
        context['module'] = request.GET.get('module')
        context['panel_id'] = request.GET.get('panel_id')
        context['NameCustomField'] = ReportPanelNameCustomField.objects.filter(
            workspace=get_workspace(request.user)).order_by("order")

        return render(request, 'data/reports/panel-form.html', context)
    
@login_or_hubspot_required
def panel_metric(request):
    lang = request.LANGUAGE_CODE
    panel_id = request.GET.get('panel_id', '')
    panel_type = request.GET.get('panel_type')
    metric_id = request.GET.get('metric_id', '')
    if metric_id:
        data_source = request.GET.get('data_source - ' + metric_id)
    else:
        data_source = request.GET.get('data_source - 0')
    print(data_source)
    panel = None
    if panel_id:
        try:
            panel = ReportPanel.objects.get(id=panel_id)
        except:
            pass

    if panel and not panel_type:
        panel_type = panel.panel_type

    print(panel_type)

    if panel_type == 'summary_table' and data_source == 'custom  -  custom - Custom Chart':
        context = get_panel_form_context(request)
        context['panel_type'] = panel_type
        context['order_statuses'] = SHOPTURBO_ORDER_DELIVERY_STATUS
        context['PANEL_METRIC_TITLE'] = PANEL_METRIC_TITLE
        context['PANEL_SOURCE_TITLE'] = PANEL_SOURCE_TITLE
        breakdowns = [
            {"value": "platform", "display": "プラットホーム" if lang == 'ja' else "Platform"},
            {"value": "order_platforms__channel__name",
                "display": "チャネル" if lang == 'ja' else "Channel"},
        ]
        context['breakdowns'] = breakdowns
        return render(request, 'data/reports/panels/custom.html', context)

    if panel_type == 'summary_table':
        context = get_panel_form_context(request)
        context['panel_type'] = panel_type
        context['order_statuses'] = SHOPTURBO_ORDER_DELIVERY_STATUS
        context['PANEL_METRIC_TITLE'] = PANEL_METRIC_TITLE
        context['PANEL_SOURCE_TITLE'] = PANEL_SOURCE_TITLE
        breakdowns = [
            {"value": "platform", "display": "プラットホーム" if lang == 'ja' else "Platform"},
            {"value": "order_platforms__channel__name",
                "display": "チャネル" if lang == 'ja' else "Channel"},
        ]
        context['breakdowns'] = breakdowns
        return render(request, 'data/reports/panels/base.html', context)

    elif panel_type in ['chart', 'stacked_chart', 'forecast'] and data_source == 'custom  -  custom - Custom Chart':
        # elif panel_type == 'custom':
        context = get_panel_form_context(request)
        context['panel_type'] = panel_type
        context['order_statuses'] = SHOPTURBO_ORDER_DELIVERY_STATUS
        context['PANEL_METRIC_TITLE'] = PANEL_METRIC_TITLE
        context['PANEL_SOURCE_TITLE'] = PANEL_SOURCE_TITLE
        context['PANEL_SOURCE_TITLE_TYPE'] = PANEL_METRIC_TYPE
        return render(request, 'data/reports/panels/custom.html', context)

    else:
        context = get_panel_form_context(request)
        context['panel_type'] = panel_type
        context['PANEL_METRIC_TITLE'] = PANEL_METRIC_TITLE
        context['SHEET_ROW_OPTION'] = SHEET_ROW_OPTION
        context['PANEL_SOURCE_TITLE'] = PANEL_SOURCE_TITLE
        context['PANEL_SOURCE_TITLE_TYPE'] = PANEL_METRIC_TYPE
        return render(request, 'data/reports/panels/base.html', context)
