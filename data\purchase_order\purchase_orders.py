import asyncio
import io
import math
import os
import platform
import re
import traceback
import uuid
from io import BytesIO, String<PERSON>
from urllib.parse import quote

import chardet
import pandas as pd
import requests
from asgiref.sync import async_to_sync
from django.core.files import File
from django.core.mail import EmailMessage
from django.core.paginator import EmptyPage, Paginator
from django.db.models import (
    BooleanField,
    Case,
    ExpressionWrapper,
    F,
    FloatField,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Value,
    When,
)
from django.db.models.functions import Coalesce
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.template.loader import get_template, render_to_string
from django.views.decorators.http import require_GET
from PIL import Image
from xhtml2pdf import pisa

from action.action import trigger_next_action
from data.association import update_associate_object
from data.constants.associate_constant import ASSOCIATE_MAP, SOURCE_ASSOCIATE_MAP
from data.constants.constant import PURCHASE_ORDER_USAGE_CATEGORY
from data.constants.date_constant import DATE_FORMAT_PARSER
from data.constants.procurement_constant import (
    DIRECTORY,
    NEXT_DIRECTORY,
    PROCUREMENT_APP_SETTING_MANAGE_APP,
    PROCUREMENT_APP_SETTING_PURCHASE_ORDER,
    PROCUREMENT_APP_SLUG,
    PROCUREMENT_APP_TARGET,
)
from data.constants.properties_constant import *
from data.custom_pdf import generate_pdf_bytes
from data.models import *
from data.property import get_default_property_set, get_properties_with_details
from data.property import properties as forward_properties
from data.templatetags.custom_tags import (
    get_currency_symbol,
    use_thousand_separator_string_with_currency,
)
from sanka.settings import AWS_LOCATION, AWS_STORAGE_BUCKET_NAME, S3_CLIENT
from utils.actions import transfer_output_to_target_input
from utils.currency_symbols.currency_symbols import CurrencySymbols
from utils.decorator import login_or_hubspot_required

# from utils.ocr import extractData #comment this to revert
from utils.filter import apply_search_setting, build_view_filter
from utils.inventory import *
from utils.inventory import create_inventory_transaction_helper
from utils.items_util import handling_items
from utils.list_action_trigger_event import (
    trigger_workflow_by_purchase_order_item_status_event,
)
from utils.meter import (
    MODELS_TO_STORAGE_USAGE,
    get_workspace_available_storage,
    has_quota,
    sync_usage,
)
from utils.project import get_ordered_views
from utils.properties.properties import (
    get_object_display_based_columns,
    get_page_object,
)
from utils.utility import (
    build_redirect_url,
    get_redirect_workflow,
    get_workspace,
    is_valid_uuid,
    natural_sort_key,
    save_custom_property,
    get_permission_filter,
)
from utils.workflow import get_workflows_by_first_action_trigger
from utils.workspace import get_permission


@login_or_hubspot_required
def purchase_orders(request, object_type=TYPE_OBJECT_PURCHASE_ORDER):
    lang = request.LANGUAGE_CODE
    manage_url = ""

    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=get_workspace(request.user), app_target=PROCUREMENT_APP_TARGET
    )
    workspace = get_workspace(request.user)

    page_obj = get_page_object(object_type, lang)

    id_field = page_obj["id_field"]
    page_title = page_obj["page_title"]
    default_columns = page_obj["default_columns"]
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj["base_model"]
    row_detail_url = page_obj["row_detail_url"]
    custom_model = page_obj["custom_model"]
    custom_relation = page_obj["custom_relation"]

    page_type = page_obj["page_type"]
    item_model = page_obj["item_model"]
    default_columns = page_obj["default_columns"]
    search_fields = page_obj["search_fields"]
    custom_value_model = page_obj["custom_value_model"]
    field_item_name = page_obj["field_item_name"]
    custom_value_relation = page_obj["custom_value_relation"]
    file_name = page_obj["file_name"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    choice_status = None

    choice_status = CustomProperty.objects.filter(
        workspace=workspace, model=base_model._meta.db_table
    )
    if choice_status:
        choice_ = []
        for choice in choice_status:
            if choice.value:
                choice_ += ast.literal_eval(choice.value)
        choice_status = choice_
    else:
        choice_status = None

    search_setting_field_name = None
    if object_type == TYPE_OBJECT_PURCHASE_ORDER:
        manage_url = "purchase_manage"
        search_setting_field_name = "search_setting_purchase_order"

    if not app_setting.search_setting_purchase_order:
        app_setting.search_setting_purchase_order = (
            "supplier__company__name,supplier__contact__name,send_from"
        )
        app_setting.save()

    isopen = None
    contact_id = None
    company_id = None
    if id:
        isopen = id
    if request.GET.get("set_id"):
        set_id = request.GET.get("set_id")
    if request.GET.get("id"):
        target = request.GET.get("target", None)
        if not target or target == "purchaseorder":
            isopen = request.GET.get("id")
        else:
            if target == "company":
                company_id = request.GET.get("id")
            if target == "contacts":
                contact_id = request.GET.get("id")

    if request.method == "GET":
        permission = get_permission(object_type=object_type, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == "hide":
            context = {
                "permission": permission,
                "page_title": page_title,
            }
            return render(request, "data/purchase_order/purchase-order.html", context)

        view_id = request.GET.get("view_id", None)

        view = None
        if not View.objects.filter(workspace=workspace, target=object_type).exists():
            print("!!! Not exist any views, let create one.")
            view = View.objects.create(
                workspace=workspace,
                title="main",
                target=object_type,
            )
            view_filter = ViewFilter.objects.create(
                view=view,
                column=default_columns,
                view_type="list",
            )

        views = get_ordered_views(
            workspace=workspace, object_target=object_type, user=request.user
        )
        try:
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if not view or (
                    view.is_private and (not request.user or view.user != request.user)
                ):
                    view = View.objects.filter(
                        workspace=workspace,
                        title="main",
                        target=object_type,
                    ).first()
                    view_id = view.id
            else:
                view = View.objects.filter(
                    workspace=workspace, title="main", target=object_type
                ).first()
            view_id = view.id
        except Exception as e:
            print(f"ERROR === procurement.py -- 2650: {e}")

        if not view:
            view = View.objects.filter(title="main", target=object_type).first()

        view_filter = view.viewfilter_set.first()

        # Adjusting for suppliers__name if showing in column
        if "suppliers__name" in view_filter.column:
            print("View filter column: ", view_filter.column)
            view_filter.column = view_filter.column.replace(
                "suppliers__name", "supplier"
            )
            view_filter.save()

        filter_condition = Q(workspace=workspace)

        filter_condition &= get_permission_filter(permission, request.user)

        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace, object_type=object_type, type="default"
        ).first()
        if not advance_search:
            advance_search = None

        if advance_search:
            if advance_search.search_settings:
                setattr(
                    app_setting,
                    search_setting_field_name,
                    advance_search.search_settings,
                )
                app_setting.save()
        print("search settings:", getattr(app_setting, search_setting_field_name))

        # Handle status filter for archived items
        status = request.GET.get("status")
        if status == "archived":
            filter_condition &= Q(usage_status="archived")
        else:
            # By default, show only active items
            filter_condition &= Q(usage_status="active")
        search_q = request.GET.get("q")
        search_filters = Q()
        if search_q and search_setting_field_name:
            search_setting_values = getattr(app_setting, search_setting_field_name)
            if search_setting_values:
                search_fields = search_setting_values.split(",")
                for s_field in search_fields:
                    search_filters |= apply_search_setting(
                        object_type, view_filter, s_field, search_q, force=True
                    )

            try:
                int(search_q)
                filter_condition &= search_filters | Q(**{id_field: search_q.lower()})
            except:
                filter_condition &= search_filters


        if advance_search:
            if advance_search.is_active:
                advance_search_filter = advance_search.search_filter
                advance_search_filter_status = advance_search.search_filter_status
                if advance_search_filter_status:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                        and advance_search_filter_status.get(k) != "False"
                    }
                else:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }
                try:
                    if (
                        advance_search_filter.get("usage_status", {}).get("value")
                        == "all"
                    ):
                        del advance_search_filter["usage_status"]
                except KeyError:
                    pass

                view_filter.filter_value = advance_search_filter
        source_filter = filter_condition
        filter_condition = build_view_filter(
            filter_condition,
            view_filter,
            object_type,
            force_filter_list=additional_filter_fields,
            request=request,
        )

        items = []

        try:
            # Collecting no id of item to obtain its id
            item_no_id = base_model.objects.filter(
                **{id_field + "__isnull": True, "workspace": workspace}
            )
            if item_no_id:
                for item in item_no_id:
                    item.save()

            items = base_model.objects.filter(filter_condition)

        except Exception as e:
            print("Filter error: ", filter_condition)
            print(f"ERROR === procurement.py -- 2683: {e}")
            items = base_model.objects.filter(source_filter)

        print("The filter: ", filter_condition)
        try:
            if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_PURCHASE_ORDER]
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_PURCHASE_ORDER,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = None
                if module.slug:
                    module_slug = module.slug
                if view_filter.sort_order_by:
                    order_method = view_filter.sort_order_method
                    order_by = view_filter.sort_order_by

                    if is_valid_uuid(order_by):
                        field_name = custom_model.objects.filter(id=order_by).first()
                        if field_name:
                            custom_value_subquery = custom_value_model.objects.filter(
                                **{
                                    custom_value_relation: OuterRef("pk"),
                                    "field_name": field_name,
                                }
                            )

                            if field_name.type in ["date", "date_time"]:
                                custom_value_subquery = custom_value_subquery.values(
                                    "value_time"
                                )[:1]
                            else:
                                custom_value_subquery = custom_value_subquery.values(
                                    "value"
                                )[:1]

                            items = items.annotate(
                                custom_value=Subquery(custom_value_subquery)
                            )

                            if field_name.type in ["date", "date_time"]:
                                items = items.annotate(
                                    null_field=Case(
                                        When(custom_value__isnull=True, then=Value(1)),
                                        default=Value(0),
                                        output_field=BooleanField(),
                                    )
                                )
                            else:
                                items = items.annotate(
                                    null_field=Case(
                                        When(**{"custom_value": ""}, then=Value(1)),
                                        default=Value(0),
                                        output_field=BooleanField(),
                                    )
                                )

                            if order_method == "asc":
                                items = items.order_by("-null_field", "custom_value")
                            else:
                                items = items.order_by("null_field", "-custom_value")

                    else:
                        if 'line_item' in order_by:
                            line_item_info = order_by.replace('line_item_', '')
                            line_item_order_by = []
                            null_field_conditions = {}
                            if 'quantity' in line_item_info and 'name' in line_item_info:
                                items = items.annotate(
                                    item_name=Coalesce('purchase_order_object__item_name', 'purchase_order_object__item__name')
                                )
                                items = items.annotate(
                                    total_amount_item=Sum('purchase_order_object__amount_item')
                                )
                                line_item_order_by = ['item_name', 'total_amount_item']
                                null_field_conditions = {
                                    'purchase_order_object__item_name__isnull': True,
                                    'purchase_order_object__amount_item__isnull': True,
                                    'purchase_order_object__item__name__isnull': True,
                                }
                            elif 'name' in line_item_info:
                                items = items.annotate(
                                    item_name=Coalesce('purchase_order_object__item_name', 'purchase_order_object__item__name')
                                )
                                line_item_order_by = ['item_name']
                                null_field_conditions = {
                                    'purchase_order_object__item_name__isnull': True,
                                    'purchase_order_object__item__name__isnull': True,
                                }   
                            elif 'quantity' in line_item_info:
                                items = items.annotate(
                                    total_amount_item=Sum('purchase_order_object__amount_item')
                                )
                                line_item_order_by = ['total_amount_item']
                                null_field_conditions = {
                                    'purchase_order_object__amount_item__isnull': True,
                                }
                            items = items.annotate(
                                null_field=Case(
                                    When(**null_field_conditions, then=Value(1)),
                                    default=Value(0),
                                    output_field=BooleanField(),
                                )
                            )

                            if order_method == "asc":
                                items = items.order_by("-null_field", *line_item_order_by)
                            else:
                                items = items.order_by("null_field", *["-" + x for x in line_item_order_by])

                        else:
                            items = items.annotate(
                                null_field=Case(
                                    When(**{f"{order_by}__isnull": True}, then=Value(1)),
                                    default=Value(0),
                                    output_field=BooleanField(),
                                )
                            )
                            if order_method == "asc":
                                items = items.order_by("-null_field", order_by)
                            else:
                                items = items.order_by("null_field", "-" + order_by)
                else:
                    items = items.order_by(
                        f"-{id_field}" if id_field else "-created_at"
                    )
            else:
                items = items.order_by(f"-{id_field}" if id_field else "-created_at")
        except Exception as e:
            print("===== Debug Error at Procurement Items =====", e)
            items = items.order_by(f"-{id_field}" if id_field else "-created_at")

        # try:
        #     show_per_page = int(request.GET.get('show_per_page', 30))
        # except:
        #     show_per_page = 30

        pagination_number = view_filter.pagination
        if not pagination_number:
            pagination_number = 20
        show_per_page = pagination_number

        paginator_item_begin = 1
        paginator_item_end = show_per_page
        page_content = None
        paginator = None

        page = None
        page = request.GET.get("page")

        paginator = Paginator(items, show_per_page)
        total = 0
        more_pagination = False
        if items:
            total = len(items)
            if page:
                page_content = paginator.page(int(page))
                items = page_content.object_list
                paginator_item_begin = (show_per_page * int(page)) - (show_per_page - 1)
                paginator_item_end = show_per_page * int(page)
                more_pagination = page_content.has_next()

            else:
                page_content = paginator.page(1)
                items = page_content.object_list
                paginator_item_begin = show_per_page * 1 - (show_per_page - 1)
                paginator_item_end = show_per_page * 1
                more_pagination = page_content.has_next()

        module_slug = None
        module_object_slug = None
        if object_type in [TYPE_OBJECT_BILL]:
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_BILL]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_BILL
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if "json_response" in request.GET:
                res = []
                for item in items:
                    res.append(
                        {
                            "id": str(item.id),
                            "text": f"#{getattr(item, id_field)} - {getattr(item, 'title')}",
                        }
                    )

                context = {"results": res, "pagination": {"more": more_pagination}}
                return JsonResponse(context)

        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
            default_property_set = get_default_property_set(
                object_type, workspace, lang
            )
            property_sets = PropertySet.objects.filter(
                workspace=workspace, target=TYPE_OBJECT_PURCHASE_ORDER
            ).order_by("created_at")

            set_id = request.GET.get("set_id")
            if view_filter.view.form:
                set_id = view_filter.view.form.id
            else:
                if not set_id:
                    property_set_default = property_sets.filter(as_default=True).first()
                    if property_set_default:
                        set_id = property_set_default.id

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list("id", flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ",".join(str(id) for id in members)

        context = {
            "total": total,
            "manage_url": manage_url,
            "paginator": paginator,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "page_title": page_title,
            "page_type": page_type,
            "items": items,
            "views": views,
            "current_view": view,
            "view_filter": view_filter,
            "app_setting": app_setting,
            "choice_status": choice_status,
            "search_q": search_q,
            "view_id": view.id,
            "view": view,
            "isopen": isopen,
            "set_id": set_id,
            "contact_id": contact_id,
            "company_id": company_id,
            "permission": permission,
            "tab": request.GET.get("tab"),
            "property_sets": property_sets,
            "module_slug": module_slug,
            "module_object_slug": module_object_slug,
            "row_detail_url": row_detail_url,
            "side_drawer": request.GET.get("sidedrawer", ""),
            "status": request.GET.get("status"),
            "advance_search": advance_search,
            "group_members": members,
        }
        return render(request, "data/purchase_order/purchase-order.html", context)

    elif request.method == "POST":
        item_ids = request.POST.getlist("items")
        view_id = request.POST.get("view_id")
        module_slug = request.POST.get("module", "")

        module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
        module = Module.objects.filter(
            workspace=workspace, object_values__contains=object_type
        ).order_by("order", "created_at")

        if module_slug:
            module = module.filter(slug=module_slug)
        if module:
            module = module.first()
            module_slug = module.slug

        try:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title="main", target=object_type, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)

                # Handle status filter for archived items
                status = request.GET.get("status")
                if status == "archived":
                    filter_conditions &= Q(usage_status="archived")
                else:
                    # By default, show only active items
                    filter_conditions &= Q(usage_status="active")
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    object_type,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
                item_ids = objects.values_list("id", flat=True)
            else:
                list_objects = request.POST.getlist("items")
                objects = base_model.objects.filter(id__in=list_objects)
                item_ids = objects.values_list("id", flat=True)

            if "bulk_archive" in request.POST:
                base_model.objects.filter(id__in=item_ids).update(
                    usage_status="archived"
                )

            elif "bulk_activate" in request.POST:
                sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])
                available_entries = get_workspace_available_storage(
                    workspace, MODELS_TO_STORAGE_USAGE[base_model]
                )
                if available_entries is None or available_entries > 0:
                    base_model.objects.filter(
                        id__in=item_ids[:available_entries]
                    ).update(usage_status="active")
                    sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])

            elif "bulk_delete" in request.POST:
                base_model.objects.filter(id__in=item_ids).delete()

            elif "bulk_duplicate" in request.POST:
                sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])
                available_entries = get_workspace_available_storage(
                    workspace, MODELS_TO_STORAGE_USAGE[base_model]
                )
                if available_entries is None or available_entries > 0:
                    objects = base_model.objects.filter(
                        id__in=item_ids[:available_entries]
                    )
                    for obj in objects:
                        customFields = custom_value_model.objects.filter(
                            **{custom_value_relation: obj}
                        )
                        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                            obj_item = item_model.objects.filter(
                                **{field_item_name: obj}
                            ).order_by("created_at")

                        obj.id = None
                        setattr(obj, id_field, None)
                        obj.created_at = timezone.now()
                        obj.save()

                        for field in customFields:
                            field.id = None
                            setattr(field, custom_value_relation, obj)
                            field.save()

                            # custom_value_model.objects.create(**{custom_value_relation:obj, 'value':field.value,'field_name':field.field_name})

                        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                            if obj_item:
                                for item_ in obj_item:
                                    item_.id = None
                                    item_.created_at = timezone.now()
                                    setattr(item_, "purchase_order", obj)
                                    item_.save()

                    sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])

        except Exception as e:
            traceback.print_exc()
            print(f"ERROR === procurement.py --2719: {e}")

        if request.POST.get("query"):
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?view_id={view_id}"
                    + "&"
                    + request.POST.get("query")
                )
        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?view_id={view_id}"
            )
        return redirect(reverse("main", host="app"))
