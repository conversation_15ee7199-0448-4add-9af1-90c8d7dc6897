import ast
from datetime import date as date_class
from datetime import datetime
import json
import re
import traceback
import uuid

from django.conf import settings
from django.core.files.base import ContentFile as DjangoContentFile
from django.core.mail import EmailMessage
from django.db import IntegrityError
from django.db.models import Q
from django.forms.models import model_to_dict
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django_hosts.resolvers import reverse
import requests
import stripe

from data.commerce.background.send_record_email_action import (
    run_send_record_email_action,
)
from data.commerce.commerce_pdf_download import commerce_pdf_download
from data.commerce.utility import create_message_object
from data.constants.commerce_constant import COMMERCE_APP_TARGET
from data.constants.constant import (
    DEFAULT_JOURNAL_CATEGORY,
    JOURNAL_ACCOUNT,
    JOURNAL_ACCOUNT_DISPLAY,
    J<PERSON><PERSON><PERSON><PERSON>_TAX_CATEGORY,
    JOURNAL_TAX_CATEGORY_DISPLAY,
    ORDER_USAGE_CATEGORY,
)
from data.constants.date_constant import DATE_FORMAT_PARSER
from data.constants.properties_constant import (
    OBJECT_TYPE_TO_SLUG,
    OBJECT_TYPE_TO_URL_NAME,
    TYPE_OBJECT_CONVERSATION,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_WORKFLOW,
)
from data.journal import generate_settlements
from data.models import SHOPTURBO_ORDER_DELIVERY_STATUS
from data.models import (
    Action,
    ActionHistory,
    ActionNode,
    ActionTaskHistory,
    ActionTracker,
    AppSetting,
    AppSettingChild,
    BackgroundJob,
    Channel,
    Company,
    CompanyNameCustomField,
    Contact,
    ContactsNameCustomField,
    CustomProperty,
    DateFormat,
    Invoice,
    InvoiceItem,
    InvoiceItemPlatform,
    InvoicePlatform,
    JournalEntry,
    Message,
    MessageBodyTemplate,
    MessageThread,
    Module,
    Notification,
    ShopTurboItemsOrders,
    ShopTurboItemsPrice,
    ShopTurboItemsVariations,
    ShopTurboOrders,
    ShopTurboSubscriptions,
)
from data.procurement import purchase_order_pdf_download
from sanka.settings import PROD
from utils.bgjobs.run_background_job import BackgroundJobPayload
from utils.date import parse_date
from utils.decorator import login_or_hubspot_required
from utils.discord import DiscordNotification
from utils.items_util import handling_items
from utils.logger import logger
from utils.meter import add_usage
from utils.properties.properties import get_page_object
from utils.serializer import custom_serializer
from utils.smtp import push_email_smtp, push_rakuten_email
from utils.stripe.stripe import retreive_stripe_invoices
from utils.utility import build_redirect_url, get_workspace, is_valid_email

type_http = settings.SITE_URL.split("//")[0]


@login_or_hubspot_required
def convert_estimate_to_orders(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if request.method == "GET":
        object_type = request.GET.get("object_type")

        selected_ids = request.GET.get("selected_ids", "[]")
        if "[" in selected_ids and "]" in selected_ids:
            selected_ids = ast.literal_eval(selected_ids)
        else:
            selected_ids = [selected_ids]

        page_obj = get_page_object(object_type, lang)
        base_model = page_obj["base_model"]
        id_field = page_obj["id_field"]

        objects = base_model.objects.filter(workspace=workspace, id__in=selected_ids)

        # Create additional_params for record actions
        additional_params = {}
        is_record_action = request.GET.get("is_record_action", "")

        print("=== CONVERT ESTIMATE TO ORDERS - GET REQUEST DEBUG ===")
        print("GET data:", dict(request.GET))
        print("Object type:", object_type)
        print("Selected IDs:", selected_ids)
        print("Is record action:", is_record_action)
        print("Action ID:", request.GET.get("action_id"))

        if is_record_action and selected_ids:
            # For record actions, pass the estimate IDs in additional_params
            if len(selected_ids) == 1:
                additional_params["estimate_id"] = selected_ids[0]
                print("Added single estimate_id to additional_params:", selected_ids[0])
            else:
                additional_params["estimate_ids"] = selected_ids
                print("Added multiple estimate_ids to additional_params:", selected_ids)

        print("Final additional_params:", additional_params)

        context = {
            "estimates": objects,
            "SHOPTURBO_ORDER_DELIVERY_STATUS": SHOPTURBO_ORDER_DELIVERY_STATUS,
            "verification": request.user.verification,
            "object_type": object_type,
            "id_field": id_field,
            "action_id": request.GET.get("action_id"),
            "is_record_action": is_record_action,
            "additional_params": additional_params,
        }

        return render(
            request, "data/commerce/action/convert-estimates-to-orders.html", context
        )

    if request.method == "POST":
        logger.info(
            f"[CONVERT_ESTIMATE_TO_ORDERS] Starting POST request for workspace {workspace.id}"
        )
        object_type = request.POST.get("object_type")

        page_obj = get_page_object(object_type, lang)
        base_model = page_obj["base_model"]
        id_field = page_obj["id_field"]
        action = Action.objects.filter(slug=request.POST.get("action_id")).first()
        is_record_action = request.POST.get("is_record_action", "")
        logger.info(
            f"[CONVERT_ESTIMATE_TO_ORDERS] Action: {action.slug if action else 'None'}, is_record_action: {is_record_action}"
        )

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ESTIMATE]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ESTIMATE
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        order_module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
        order_module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )
        order_module_slug = None
        if order_module:
            order_module_slug = order_module.slug
        selected_ids = request.POST.getlist("selected_ids")
        objects = base_model.objects.filter(workspace=workspace, id__in=selected_ids)
        logger.info(
            f"[CONVERT_ESTIMATE_TO_ORDERS] Selected IDs: {selected_ids}, Found objects: {len(objects)}"
        )

        order_at = request.POST.get("order_at", None)
        if order_at:
            tz = workspace.timezone
            order_at = parse_date(order_at, timezone=tz if tz else "UTC")

        order_status = request.POST.get("delivery_status")

        history = ActionHistory.objects.create(
            workspace=workspace,
            action=action,
            status="initialized",
            created_by=request.user,
        )
        logger.info(f"[CONVERT_ESTIMATE_TO_ORDERS] Created ActionHistory: {history.id}")

        if is_record_action:
            history.object_type = TYPE_OBJECT_ESTIMATE
            history.object_id = objects[0].id
            logger.info(
                f"[CONVERT_ESTIMATE_TO_ORDERS] Set record action - object_type: {history.object_type}, object_id: {history.object_id}"
            )
        for obj in objects:
            estimate_items = obj.estimate_object.all()

            input_data = dict(request.POST)
            input_data = {
                key: value[0] if len(value) == 1 else value
                for key, value in input_data.items()
            }
            transfer_history = ActionTaskHistory.objects.create(
                workspace=history.workspace,
                action_history=history,
                status="initialized",
                input_data=input_data,
            )
            logger.info(
                f"[CONVERT_ESTIMATE_TO_ORDERS] Created ActionTaskHistory: {transfer_history.id} for estimate {obj.id}"
            )

            currency = obj.currency.upper() if obj.currency else None

            order = ShopTurboOrders.objects.create(
                workspace=workspace,
                status="active",
                order_type="item_order",
            )

            add_usage(workspace, ORDER_USAGE_CATEGORY)

            if order_at:
                order.order_at = order_at

            order.platform = "sanka"
            order.currency = currency
            order.delivery_status = order_status

            item_total_price = 0
            item_total_price_without_tax = 0
            order_view = 0
            items_price = None

            if obj.company:
                order.company = obj.company
            if obj.contact:
                order.contact = obj.contact
            if obj.tax_rate and obj.tax_option == "unified_tax":
                order.tax = obj.tax_rate
                order.tax_applied_to = "all"

            for est_item in estimate_items:
                if est_item.amount_item:
                    quantity = est_item.amount_item
                else:
                    quantity = 0

                if est_item.item_link:
                    shopturbo_item_order = ShopTurboItemsOrders.objects.create(
                        item=est_item.item_link,
                        order=order,
                        currency=currency,
                        number_item=quantity,
                    )

                    # PRIORITY: Use estimate item's price if available (user's custom price)
                    if est_item.amount_price is not None and est_item.amount_price > 0:
                        shopturbo_item_order.item_price_order = est_item.amount_price

                        # Create or get a ShopTurboItemsPrice object with the custom price
                        # This ensures the UI dropdown shows the correct price
                        custom_items_price, created = (
                            ShopTurboItemsPrice.objects.get_or_create(
                                item=est_item.item_link,
                                price=est_item.amount_price,
                                currency=currency,
                                defaults={
                                    "workspace": workspace,
                                    "default": False,  # Don't make this the default price for the item
                                },
                            )
                        )
                        shopturbo_item_order.item_price = custom_items_price
                    else:
                        # Fallback to item's default pricing structure
                        item_variant = ShopTurboItemsVariations.objects.filter(
                            items=est_item.item_link
                        ).first()
                        if item_variant:
                            shopturbo_item_order.item_price_order = (
                                item_variant.price or 0.0
                            )
                            shopturbo_item_order.item_variant = item_variant
                        else:
                            items_price = ShopTurboItemsPrice.objects.filter(
                                item=est_item.item_link, default=True
                            ).first()
                            if items_price:
                                shopturbo_item_order.item_price = items_price
                                shopturbo_item_order.item_price_order = (
                                    items_price.price or 0.0
                                )
                            else:
                                shopturbo_item_order.item_price_order = (
                                    est_item.item_link.price or 0.0
                                )
                elif est_item.item_name:
                    shopturbo_item_order = ShopTurboItemsOrders.objects.create(
                        custom_item_name=est_item.item_name,
                        order=order,
                        currency=currency,
                        number_item=quantity,
                    )
                    shopturbo_item_order.item_price_order = est_item.amount_price or 0.0
                    shopturbo_item_order.save()  # Save immediately to ensure price is set

                else:
                    continue

                order_view += 1
                shopturbo_item_order.total_price = (
                    shopturbo_item_order.item_price_order
                    * float(shopturbo_item_order.number_item)
                )
                shopturbo_item_order.order_view = order_view

                shopturbo_item_order.save()

                item_total_price_per_item = shopturbo_item_order.total_price
                # Calculate tax in item
                if items_price:
                    if items_price.tax:
                        item_total_price_per_item = item_total_price_per_item + (
                            item_total_price_per_item * (float(items_price.tax) / 100)
                        )
                    else:
                        item_total_price_per_item = item_total_price_per_item
                elif est_item.tax_rate:
                    if est_item.tax_rate != 0:
                        item_total_price_per_item = item_total_price_per_item + (
                            item_total_price_per_item * (float(est_item.tax_rate) / 100)
                        )

                item_total_price_without_tax += shopturbo_item_order.total_price
                item_total_price += item_total_price_per_item

            if order.tax and order.tax_applied_to == "all":
                order.total_price = (
                    item_total_price_without_tax
                    + item_total_price_without_tax * (float(order.tax) / 100)
                )
            else:
                order.total_price = item_total_price
            order.item_price_order = item_total_price_without_tax
            order.total_price_without_tax = item_total_price_without_tax

            # Log at the end
            order.save(
                log_data={
                    "user": request.user,
                    "status": "create",
                    "workspace": workspace,
                }
            )
            transfer_history.completed_at = timezone.now()
            if order_module:
                result_link = (
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": order_module_slug,
                            "object_slug": order_module_object_slug,
                        },
                    )
                    + f"?id={order.id}&target=commerce_orders"
                )
                transfer_history.result_link = result_link
                logger.info(
                    f"[CONVERT_ESTIMATE_TO_ORDERS] Set result_link: {result_link}"
                )
            else:
                logger.warning(
                    "[CONVERT_ESTIMATE_TO_ORDERS] No order_module found, result_link not set"
                )
            transfer_history.status = "success"
            transfer_history.save()
            logger.info(
                f"[CONVERT_ESTIMATE_TO_ORDERS] Updated ActionTaskHistory {transfer_history.id} to success"
            )

        history.status = "success"
        history.completed_at = timezone.now()
        history.save()
        logger.info(
            f"[CONVERT_ESTIMATE_TO_ORDERS] Completed ActionHistory {history.id} with status: {history.status}"
        )
        if module_slug:
            if is_record_action:
                return redirect(
                    build_redirect_url(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        ),
                        sidedrawer="action-history",
                        id=obj.id,
                    )
                )
            return redirect(
                build_redirect_url(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    ),
                    open_drawer="action_drawer_history",
                )
            )
        return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def convert_estimate_to_invoices(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if request.method == "GET":
        object_type = request.GET.get("object_type")

        selected_ids = request.GET.get("selected_ids", "[]")
        if "[" in selected_ids and "]" in selected_ids:
            selected_ids = ast.literal_eval(selected_ids)
        else:
            selected_ids = [selected_ids]

        page_obj = get_page_object(object_type, lang)
        base_model = page_obj["base_model"]
        id_field = page_obj["id_field"]

        objects = base_model.objects.filter(workspace=workspace, id__in=selected_ids)

        # Create additional_params for record actions
        additional_params = {}
        is_record_action = request.GET.get("is_record_action", "")
        if is_record_action and selected_ids:
            # For record actions, pass the estimate IDs in additional_params
            if len(selected_ids) == 1:
                additional_params["estimate_id"] = selected_ids[0]
            else:
                additional_params["estimate_ids"] = selected_ids

        context = {
            "action_id": request.GET.get("action_id", None),
            "estimates": objects,
            "id_field": id_field,
            "object_type": object_type,
            "is_record_action": is_record_action,
            "additional_params": additional_params,
        }
        return render(
            request, "data/commerce/action/convert-estimates-to-invoices.html", context
        )

    if request.method == "POST":
        object_type = request.POST.get("object_type")
        page_obj = get_page_object(object_type, lang)
        base_model = page_obj["base_model"]
        id_field = page_obj["id_field"]
        is_record_action = request.POST.get("is_record_action", "")

        invoice_module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVOICE]
        invoice_module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_INVOICE
            )
            .order_by("order", "created_at")
            .first()
        )
        invoice_module_slug = None
        if invoice_module:
            invoice_module_slug = invoice_module.slug

        action = Action.objects.filter(slug=request.POST.get("action_id")).first()

        selected_ids = request.POST.getlist("selected_ids")
        print("=== DEBUG (Selected IDs):", selected_ids)
        objects = base_model.objects.filter(workspace=workspace, id__in=selected_ids)

        due_date = request.POST.get("freq-due", "")
        if due_date:
            try:
                if lang == "ja":
                    due_date = datetime.strptime(due_date, "%Y年%m月%d日").date()
                else:
                    due_date = datetime.strptime(due_date, "%Y-%m-%d").date()
            except:  # Format date is invalid , skip to save it
                due_date = ""

        history = ActionHistory.objects.create(
            workspace=workspace,
            action=action,
            status="initialized",
            created_by=request.user,
        )

        if is_record_action:
            history.object_type = TYPE_OBJECT_ESTIMATE
            history.object_id = objects[0].id
        for obj in objects:
            invoice_obj = Invoice.objects.create(workspace=workspace)

            input_data = dict(request.POST)
            input_data = {
                key: value[0] if len(value) == 1 else value
                for key, value in input_data.items()
            }
            transfer_history = ActionTaskHistory.objects.create(
                workspace=history.workspace,
                action_history=history,
                status="initialized",
                input_data=input_data,
            )
            estimate_items = obj.estimate_object.all()

            for est_item in estimate_items:
                InvoiceItem.objects.create(
                    invoice=invoice_obj,
                    item_name=est_item.item_name,
                    item_link=est_item.item_link,
                    amount_price=est_item.amount_price,
                    amount_item=est_item.amount_item,
                    tax_rate=est_item.tax_rate,
                    total_price=est_item.total_price,
                    total_price_without_tax=est_item.total_price_without_tax,
                )

            invoice_obj.company = obj.company
            invoice_obj.contact = obj.contact
            invoice_obj.start_date = obj.start_date
            if due_date:
                invoice_obj.due_date = due_date
            invoice_obj.status = obj.status
            invoice_obj.usage_status = "active"
            invoice_obj.notes = obj.notes
            invoice_obj.send_from = obj.send_from
            invoice_obj.currency = obj.currency
            invoice_obj.tax_rate = obj.tax_rate
            invoice_obj.tax_list = obj.tax_list
            invoice_obj.is_stamp = obj.is_stamp
            invoice_obj.tax_option = obj.tax_option
            invoice_obj.tax_inclusive = obj.tax_inclusive
            invoice_obj.discount = obj.discount
            invoice_obj.discount_option = obj.discount_option
            invoice_obj.discount_tax_option = obj.discount_tax_option
            invoice_obj.total_price = obj.total_price
            invoice_obj.total_price_without_tax = obj.total_price_without_tax

            invoice_obj.save(
                log_data={
                    "user": request.user,
                    "status": "create",
                    "workspace": workspace,
                }
            )

            obj.invoice.add(invoice_obj)
            obj.save()

            transfer_history.completed_at = timezone.now()
            if invoice_module:
                transfer_history.result_link = (
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": invoice_module_slug,
                            "object_slug": invoice_module_object_slug,
                        },
                    )
                    + f"?id={invoice_obj.id}"
                )
            transfer_history.status = "success"
            transfer_history.save()

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ESTIMATE]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ESTIMATE
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        history.status = "success"
        history.completed_at = timezone.now()
        history.save()

        if module:
            if is_record_action:
                return redirect(
                    build_redirect_url(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        ),
                        sidedrawer="action-history",
                        id=obj.id,
                    )
                )
            module_slug = module.slug
            return redirect(
                build_redirect_url(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    ),
                    open_drawer="action_drawer_history",
                )
            )
        else:
            return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def download_record_file(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    logger.info("=== DEBUG: download_record_file called ===")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request POST: {dict(request.POST)}")
    logger.info(f"History ID in POST: {request.POST.get('history_id')}")

    # If this is a POST request with history_id, it's being called from the background job
    # and should be handled by the send_record_email function
    if request.method == "POST" and request.POST.get("history_id"):
        logger.info(
            "=== DEBUG: download_record_file called from background job, redirecting to send_record_email ==="
        )
        return send_record_email(request)

    if request.method == "GET":
        object_type = request.GET.get("object_type")

        selected_ids = request.GET.get("selected_ids", "[]")
        if selected_ids == "[]":
            selected_ids = request.GET.get("order_ids", "[]")
        if "[" in selected_ids and "]" in selected_ids:
            selected_ids = ast.literal_eval(selected_ids)
        else:
            selected_ids = [selected_ids]

        if object_type is None:
            object_type = request.GET.get("page_group_type")
        page_obj = get_page_object(object_type, lang)
        base_model = page_obj["base_model"]
        id_field = page_obj["id_field"]
        custom_prop_model = page_obj["custom_model"]

        objects = base_model.objects.filter(workspace=workspace, id__in=selected_ids)
        smtp_channels = []
        if object_type in [TYPE_OBJECT_ORDER, TYPE_OBJECT_INVOICE]:
            smtp_channels = Channel.objects.filter(
                Q(workspace=workspace, integration__slug="smtp")
                | Q(
                    workspace=workspace,
                    integration__slug="rakuten",
                    service_account__isnull=False,
                    app_id__isnull=False,
                    account_id__isnull=False,
                    account_username__isnull=False,
                )
            ).exclude(
                Q(integration__slug="rakuten", service_account="")
                | Q(integration__slug="rakuten", app_id="")
                | Q(integration__slug="rakuten", account_id="")
                | Q(integration__slug="rakuten", account_username="")
            )

        const_properties = []
        properties = []
        custom_properties = []
        templates = []

        if request.GET.get("page_group_type") in [
            TYPE_OBJECT_ORDER,
            TYPE_OBJECT_INVOICE,
        ]:
            const_properties.append("customer__name")

            templates = MessageBodyTemplate.objects.filter(workspace=workspace)

        if base_model:
            properties = [
                field.name
                for field in base_model._meta.get_fields()
                if not field.is_relation
            ]

        if custom_prop_model:
            custom_props = custom_prop_model.objects.filter(workspace=workspace)
            custom_properties = [prop.name for prop in custom_props]

        if len(const_properties) > 0:
            for prop in const_properties:
                properties.append(prop)

        module_slug = None
        module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=object_type
            )
            .order_by("order", "created_at")
            .first()
        )

        # Set module_slug if module is found
        if module:
            module_slug = module.slug

        context = {
            "action_id": request.GET.get("action_id"),
            "object_type": object_type,
            "objects": objects,
            "workspace": workspace,
            "selected_ids": selected_ids,
            "id_field": id_field,
            "smtp_channels": smtp_channels,
            "properties": properties,
            "custom_properties": custom_properties,
            "templates": templates,
            "is_record_action": request.GET.get("is_record_action"),
            "is_object_action": False if request.GET.get("is_record_action") else True,
            "module": module,
            "module_slug": module_slug,
            "module_object_slug": module_object_slug,
        }

        return render(
            request, "data/commerce/action/download-record-file.html", context
        )

    # POST
    print(request.POST)
    object_type = request.POST.get("object_type")
    action_slug = request.POST.get("action_slug")
    is_object_action = request.POST.get("is_object_action", None)
    is_record_action = request.POST.get("is_record_action", None)

    if is_record_action:
        selected_ids = request.POST.get("selected_ids", [])
        if selected_ids:
            selected_ids = [selected_ids]
    else:
        selected_ids = request.POST.get("selected_ids")
        print("=== DEBUG (Selected IDs):", selected_ids)
        if isinstance(selected_ids, str):
            selected_ids = [selected_ids]
    subject = request.POST.get("subject", "")
    body = request.POST.get("body", "")
    smtp_channel_id = request.POST.get("smtp_channel_id", None)
    cc_email = request.POST.get("template-cc", None)
    print("=== DEBUG (Selected IDs):", selected_ids)

    module_slug = None
    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    module = (
        Module.objects.filter(workspace=workspace, object_values__contains=object_type)
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug
    redirect_url = reverse("main", host="app")
    if module_slug:
        redirect_url = (
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + "?open_drawer=action_drawer_history"
        )

    if is_object_action or is_record_action:
        action = Action.objects.filter(slug=action_slug).first()
        history = ActionHistory.objects.create(
            workspace=workspace,
            action=action,
            status="initialized",
            created_by=request.user,
        )

    if not (selected_ids and object_type):
        if is_record_action or is_object_action:
            history.status = "failed"
            history.completed_at = timezone.now()
            history.save()
        msg = "Malformed input."
        if lang == "ja":
            msg = "入力が不正です。"
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message=msg,
            type="error",
        )
        return redirect(redirect_url)

    page_obj = get_page_object(object_type, lang)
    base_model = page_obj["base_model"]
    id_field = page_obj["id_field"]
    page_title = page_obj["page_title"]

    if is_record_action:
        history.object_id = selected_ids[0]
        history.object_type = object_type
        history.save()

    objects = base_model.objects.filter(workspace=workspace, id__in=selected_ids)

    df_object = DateFormat.objects.filter(
        workspace=workspace, is_workspace_level=True
    ).first()
    date_format = (
        df_object.value if (df_object and df_object.value is not None) else "DD/MM/YYYY"
    )
    date_format = DATE_FORMAT_PARSER[date_format]

    setting_type = object_type
    if object_type[-1] == "s":
        setting_type = object_type[:-1]
    app_setting = AppSetting.objects.get(
        workspace=workspace, app_target=COMMERCE_APP_TARGET
    )
    default_template = None
    if setting_type != TYPE_OBJECT_PURCHASE_ORDER:
        setting_ = "_pdf_template"
        app_setting_child = AppSettingChild.objects.filter(
            app_setting=app_setting, target_name=setting_type + setting_
        ).first()
        if app_setting_child and app_setting_child.value:
            default_template = app_setting_child.value
    else:
        default_template = app_setting.purchase_order_pdf_template
    if not default_template:
        print("no default template")
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"PDFダウンロードに失敗しました。{page_title}",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Failed to send email for {page_title}. There was an error in generating the PDF.",
                type="error",
            )
        return redirect(redirect_url)

    smtp_channel = None
    if smtp_channel_id and smtp_channel_id != "sanka":
        smtp_channel = Channel.objects.filter(id=smtp_channel_id).first()
        if not smtp_channel:
            # if is_record_action or is_object_action:
            #     transfer_history.error_message = f"#{obj_id:04d} "
            #     transfer_history.error_message_ja = f"#{obj_id:04d} "
            #     transfer_history.status = "failed"
            #     transfer_history.save()

            print("No SMTP channel found")
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="SMTPの設定がありません。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="SMTP integration does not exist.",
                    type="error",
                )
            return redirect(redirect_url)

    total_success = 0

    for obj in objects:
        if is_record_action or is_object_action:
            transfer_history = ActionTaskHistory.objects.create(
                workspace=workspace,
                action_history=history,
                status="running",
            )

        customers = ""
        email = ""
        obj_id = getattr(obj, id_field, None)

        list_contacts = []
        list_companies = []
        if obj.contact:
            if obj.contact.email:
                list_contacts.append(obj.contact)
                email = obj.contact.email

            customers = obj.contact.name
            if obj.contact.last_name:
                if lang == "ja":
                    customers = obj.contact.last_name + " " + customers
                else:
                    customers = customers + " " + obj.contact.last_name
        elif obj.company:
            if obj.company.email:
                list_companies.append(obj.company)
                email = obj.company.email
            customers = obj.company.name
        else:
            transfer_history.error_message = (
                f"#{obj_id:04d} does not have customer information."
            )
            transfer_history.error_message_ja = (
                f"#{obj_id:04d} は顧客情報がありません。"
            )
            transfer_history.status = "failed"
            transfer_history.save()
            continue

        if not is_valid_email(email):
            transfer_history.error_message = (
                f"#{obj_id:04d} customer's email is invalid."
            )
            transfer_history.error_message_ja = (
                f"#{obj_id:04d} 顧客のメールアドレスが不正です。"
            )
            transfer_history.status = "failed"
            transfer_history.save()
            continue

        list_email_cc = []
        if cc_email:
            if obj.company:
                list_email_cc = obj.company.contact_set.values_list("email", flat=True)
                valid_emails = [
                    email for email in list_email_cc if is_valid_email(email)
                ]
                list_email_cc = valid_emails
        # Handle the placeholder
        placeholders = re.findall(r"\[\[([^\]]+)\]\]", body)
        for placeholder in placeholders:
            val = ""

            if (
                "company__custom_property" in placeholder
                or "contact__custom_property" in placeholder
            ):
                customer_obj = None
                if "company__" in placeholder:
                    customer_obj = obj.company
                    static_string = "company"
                elif "contact__" in placeholder:
                    customer_obj = obj.contact
                    static_string = "contact"

                if customer_obj:
                    cleaned_field_name = placeholder.replace(
                        f"{static_string}__custom_property__", "", 1
                    )

                    if static_string == "company":
                        custom_field = customer_obj.company_custom_field_relations.get(
                            field_name__name=cleaned_field_name
                        )
                    elif static_string == "contact":
                        custom_field = customer_obj.contact_custom_field_relations.get(
                            field_name__name=cleaned_field_name
                        )

                    if custom_field.field_name.type == "choice":
                        json_string = custom_field.field_name.choice_value

                        try:
                            data = ast.literal_eval(json_string)
                            value_to_label = {
                                item["value"]: item["label"] for item in data
                            }

                            arr = custom_field.value.split(";")
                            choiced_val = []
                            for val in arr:
                                val = value_to_label.get(val, "")
                                if val:
                                    choiced_val.append(val)

                            val = ", ".join(choiced_val)
                        except json.JSONDecodeError as e:
                            print(f"JSON Decode Error: {e}")
                            val = ""
                    elif custom_field.field_name.type in ["date", "date_time"]:
                        date_format = df_object.value if df_object else "DD/MM/YYYY"
                        date_format = DATE_FORMAT_PARSER[df_object.value]
                        if custom_field.field_name.type == "date_time":
                            date_format = f"{date_format} %H:%M"

                        if custom_field.value_time:
                            date = custom_field.value_time.strftime(date_format)
                            if date_format == "%Yx%my%dz":
                                date = (
                                    date.replace("x", "年")
                                    .replace("y", "月")
                                    .replace("z", "日")
                                )
                            val = date
                        else:
                            val = ""
                    else:
                        val = custom_field.value
            elif "custom_property__" in placeholder:
                cleaned_field_name = placeholder.replace("custom_property__", "", 1)
                try:
                    custom_field = obj.shopturbo_custom_field_relations.get(
                        field_name__name=cleaned_field_name
                    )

                    if custom_field.field_name.type == "choice":
                        json_string = custom_field.field_name.choice_value

                        try:
                            data = ast.literal_eval(json_string)
                            value_to_label = {
                                item["value"]: item["label"] for item in data
                            }

                            arr = custom_field.value.split(";")
                            choiced_val = []
                            for val in arr:
                                val = value_to_label.get(val, "")
                                if val:
                                    choiced_val.append(val)

                            val = ", ".join(choiced_val)
                        except json.JSONDecodeError as e:
                            print(f"JSON Decode Error: {e}")
                            val = ""
                    elif custom_field.field_name.type in ["date", "date_time"]:
                        date_format = df_object.value if df_object else "DD/MM/YYYY"
                        date_format = DATE_FORMAT_PARSER[df_object.value]
                        if custom_field.field_name.type == "date_time":
                            date_format = f"{date_format} %H:%M"

                        if custom_field.value_time:
                            date = custom_field.value_time.strftime(date_format)
                            if date_format == "%Yx%my%dz":
                                date = (
                                    date.replace("x", "年")
                                    .replace("y", "月")
                                    .replace("z", "日")
                                )
                            val = date
                        else:
                            val = ""
                    else:
                        val = custom_field.value
                except:
                    val = ""
            else:
                if placeholder == "customer__name":
                    if obj.contact:
                        val = obj.contact.name
                    elif obj.company:
                        val = obj.company.name
                else:
                    new_placeholder = ""
                    if "__" in placeholder:
                        new_obj_field, new_placeholder = placeholder.split("__")
                        new_obj = getattr(obj, new_obj_field, None)
                        if new_obj:
                            val = getattr(new_obj, new_placeholder, "")
                    else:
                        val = getattr(obj, placeholder, "")

                    if (
                        placeholder.endswith("_id")
                        or placeholder.startswith("id_")
                        or new_placeholder.endswith("_id")
                        or new_placeholder.startswith("id_")
                    ):
                        val = f"{int(val):04d}"
                    elif isinstance(val, (int, float)):
                        # Format as currency
                        val = "{:,.2f}".format(val)
                    elif isinstance(val, (date_class, datetime)):
                        val = val.strftime(date_format)
                        if date_format == "%Yx%my%dz":
                            val = (
                                val.replace("x", "年")
                                .replace("y", "月")
                                .replace("z", "日")
                            )

            if val:
                val = str(val)
                body = body.replace(f"[[{placeholder}]]", val)
            else:
                body = body.replace(
                    f"[[{placeholder}]]", ""
                )  # if no val available just remove the placeholders

        # in bytes
        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
            attachment_byte = purchase_order_pdf_download(
                request, id=obj.id, send_email=True, template_select=default_template
            )
        else:
            attachment_byte = commerce_pdf_download(
                request,
                id=obj.id,
                send_email=True,
                object_type=object_type,
                template_select=default_template,
            )

        if lang == "ja":
            mail_subject = f"新しい{page_title}を受け取りました[#{obj_id:04d}]"
            message = f"{customers}さま\n{page_title}を添付致しました。ご査収いただきますようお願い致します。"
        else:
            mail_subject = f"You received a new {page_title} [#{obj_id:04d}]"
            message = f"Hi {customers}, \nThe attached is your {page_title}. Please take a look and should you have any questions contact us. Thank you."

        if subject:
            mail_subject = subject
        if body:
            message = body

        name_pdf = f"{obj_id:04d}" + ".pdf"

        if smtp_channel:
            if smtp_channel.integration.slug == "rakuten":
                sent = push_rakuten_email(smtp_channel_id, email, mail_subject, message)
            else:  # SMTP Integration
                sent = push_email_smtp(
                    smtp_channel_id,
                    email,
                    mail_subject,
                    message,
                    name_pdf,
                    attachment_byte,
                )
            if sent:
                message_thread = MessageThread.objects.create(
                    channel=smtp_channel, workspace=workspace, message_type="email"
                )
                if obj.contact:
                    message_thread.contacts.add(obj.contact)
                message = Message.objects.create(thread=message_thread, body=body)
                message.sent_at = timezone.now()
                message.save()
            else:
                if is_record_action or is_object_action:
                    transfer_history.error_message = (
                        f"#{obj_id:04d} Failed to send email."
                    )
                    transfer_history.error_message_ja = (
                        f"#{obj_id:04d} 電子メールの送信に失敗しました。"
                    )
                    transfer_history.status = "failed"
                    transfer_history.save()

                else:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message=f"電子メールの送信に失敗しました。{page_title} - #{obj_id:04d}",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message=f"Failed to send email to {email} for {page_title} - #{obj_id:04d}",
                            type="error",
                        )
                continue
        else:
            from_email = "Sanka <<EMAIL>>"
            email = EmailMessage(
                mail_subject,
                message,
                from_email,
                [email],
                cc=list_email_cc,
            )

            if object_type != TYPE_OBJECT_ORDER:
                email.attach(name_pdf, attachment_byte)

            try:
                email.send()
            except:
                if is_record_action or is_object_action:
                    transfer_history.error_message = (
                        f"#{obj_id:04d} Failed to send email."
                    )
                    transfer_history.error_message_ja = (
                        f"#{obj_id:04d} 電子メールの送信に失敗しました。"
                    )
                    transfer_history.status = "failed"
                    transfer_history.save()
                else:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message=f"電子メールの送信に失敗しました。{page_title} - #{obj_id:04d}",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message=f"Failed to send email to {email} for {page_title} - #{obj_id:04d}",
                            type="error",
                        )
                continue

        if is_record_action or is_object_action:
            transfer_history.status = "success"
            transfer_history.completed_at = timezone.now()
            attachment_file = DjangoContentFile(attachment_byte)
            if lang == "ja":
                attachment_file.name = "売上請求" + "." + str(uuid.uuid4()) + ".pdf"
            else:
                attachment_file.name = "Invoice" + "." + str(uuid.uuid4()) + ".pdf"
            message_id = create_message_object(
                workspace,
                smtp_channel,
                mail_subject,
                message,
                list_email_cc,
                list_contacts,
                list_companies,
            )

            message_module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CONVERSATION]
            message_module = (
                Module.objects.filter(
                    workspace=workspace,
                    object_values__contains=TYPE_OBJECT_CONVERSATION,
                )
                .order_by("order", "created_at")
                .first()
            )
            if message_module:
                message_module_slug = message_module.slug

            if message_module_slug and message_id:
                result_link = (
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": message_module_slug,
                            "object_slug": message_module_object_slug,
                        },
                    )
                    + f"?id={message_id}"
                )
                if PROD:
                    result_link = (
                        "https:"
                        + reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": message_module_slug,
                                "object_slug": message_module_object_slug,
                            },
                        ).replace(settings.PARENT_HOST, "sanka.com")
                        + f"?id={message_id}"
                    )

                transfer_history.result_link = result_link
                transfer_history.save()

            # transfer_history.result_file = attachment_file
            # print(transfer_history.result_file.url)
            transfer_history.save()
        else:
            message = f"Email sent successfully. {page_title} - #{obj_id:04d}"
            if lang == "ja":
                message = f"メールは正常に送信されました. {page_title} - #{obj_id:04d}"
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message=message,
                type="success",
            )

        total_success += 1

    if is_object_action or is_record_action:
        if total_success > 0:
            history.status = "success"
        else:
            history.status = "failed"
        history.completed_at = timezone.now()
        history.save()

    return redirect(redirect_url)


@login_or_hubspot_required
def import_stripe_invoices(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_channel_integration = None
        days_ago = 1  # Initialize with default value
        prioritize_company_for_customer = True

        if node and node.input_data:
            active_channel_integration = node.input_data.get(
                "channel_integration", None
            )
            days_ago = node.input_data.get(
                "days_ago", 1
            )  # Use default value of 1 if not found
            prioritize_company_for_customer = node.input_data.get(
                "prioritize_company_for_customer", True
            )

        channels = Channel.objects.filter(
            workspace=workspace, integration__slug="stripe"
        )

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_channel_integration": active_channel_integration,
            "channels": channels,
            "days_ago": days_ago,
            "prioritize_company_for_customer": prioritize_company_for_customer,
        }

        return render(
            request, "data/invoice/actions/import_stripe_invoices.html", context
        )

    # POST
    print("import_stripe_invoices POST", request.POST)
    object_type = request.POST.get("object_type", TYPE_OBJECT_WORKFLOW)
    action_slug = request.POST.get("action_slug")

    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    module = (
        Module.objects.filter(workspace=workspace, object_values__contains=object_type)
        .order_by("order", "created_at")
        .first()
    )
    redirect_url = reverse("main", host="app")
    if module and module.slug:
        redirect_url = (
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module.slug, "object_slug": module_object_slug},
            )
            + "?open_drawer=action_drawer_history"
        )

    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    node = None
    if action_node_id:
        node = ActionNode.objects.filter(id=action_node_id).first()

    action_index = request.POST.get("action_index")
    postfix = ""
    if action_index:
        postfix = "-" + action_index

    history_id = request.POST.get("history_id")

    if request.POST.get("submit-option") == "save":
        action_name = request.POST.get("action_name" + postfix)
        if not node:
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="アクションの保存に失敗しました。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Failed to save action.",
                    type="error",
                )
            return redirect(redirect_url)

        channel_integration = request.POST.get("stripe_integration" + postfix)
        input_data = {
            "channel_integration": channel_integration,
            "action_name": action_name,
        }
        days_ago = request.POST.get("days_ago" + postfix, 1)
        try:
            days_ago = int(days_ago)
        except (ValueError, TypeError):
            days_ago = 1

        input_data["days_ago"] = days_ago
        prioritize_company_for_customer = request.POST.get(
            "prioritize_company_for_customer" + postfix, True
        )
        input_data["prioritize_company_for_customer"] = prioritize_company_for_customer

        node.valid_to_run = True
        if not channel_integration:
            node.valid_to_run = False
        node.input_data = input_data
        node.save()
        return redirect(redirect_url)

    else:  # run
        if not node:
            if at:
                at.status = "failed"
                at.save()
                print("no Node")
                return redirect(redirect_url)

        if node:
            if node.workflow:
                workspace = node.workflow.workspace
            elif node.workflow_history:
                workspace = node.workflow_history.workspace
            at.status = "running"
            at.save()

            days_ago = node.input_data.get("days_ago")
            channel_integration_id = node.input_data.get("channel_integration")
            channel_integration = Channel.objects.filter(
                id=channel_integration_id,
                workspace=workspace,
                integration__slug="stripe",
            ).first()

            prioritize_company_for_customer = node.input_data.get(
                "prioritize_company_for_customer", True
            )

        history = None
        if history_id:
            try:
                history = ActionHistory.objects.get(
                    id=history_id,
                    workspace=workspace,
                    action__slug="import-stripe-invoices",
                )
                history.status = "running"
                history.save()

                days_ago = int(request.POST.get("days_ago"))
                channel_integration_id = request.POST.get("select_integration_ids")
                channel_integration = Channel.objects.filter(
                    id=channel_integration_id,
                    workspace=workspace,
                    integration__slug="stripe",
                ).first()

                prioritize_company_for_customer = request.POST.get(
                    "prioritize_company_for_customer", True
                )
            except:  # noqa
                pass

        if not node and not history:
            return redirect(redirect_url)

        start_unix_time = int(timezone.now().timestamp()) - (days_ago * 24 * 60 * 60)
        print(start_unix_time)

    try:
        print("Pulling invoices")
        # Get invoices from Stripe
        invoices = retreive_stripe_invoices(channel_integration_id, start_unix_time)
        total_success = 0

        output_data = {
            "total": 0,
            "success": 0,
            "failed": 0,
        }

        for stripe_invoice in invoices.auto_paging_iter():
            print("stripe_invoice", stripe_invoice.id, stripe_invoice.created)
            order = None
            subscription = None

            try:
                # If invoice exists and not paid yet and attached to order or subscription object, skip it
                # because each access to sanka payment page will generate new checkout session (means new invoice also).
                if (
                    stripe_invoice.metadata
                    and stripe_invoice.metadata.get("type")
                    and stripe_invoice.metadata.type in ["order", "subscription"]
                ):
                    if not stripe_invoice.status == "paid":
                        print("skip invoice")
                        continue

                    if stripe_invoice.metadata.type == "order":
                        order = ShopTurboOrders.objects.filter(
                            workspace=workspace, id=stripe_invoice.metadata.id
                        ).first()

                    elif stripe_invoice.metadata.type == "subscription":
                        subscription = ShopTurboSubscriptions.objects.filter(
                            workspace=workspace, id=stripe_invoice.metadata.id
                        ).first()
                if (
                    stripe_invoice.parent
                    and stripe_invoice.parent.subscription_details
                    and stripe_invoice.parent.subscription_details.metadata
                    and getattr(
                        stripe_invoice.parent.subscription_details.metadata,
                        "subscription_id",
                        None,
                    )
                ):
                    if not stripe_invoice.status == "paid":
                        print("skip invoice")
                        continue
                    subscription = ShopTurboSubscriptions.objects.filter(
                        workspace=workspace,
                        id=stripe_invoice.parent.subscription_details.metadata.subscription_id,
                    ).first()

                output_data["total"] += 1

                # Get or create customer
                stripe_customer = stripe.Customer.retrieve(stripe_invoice.customer)
                # print('stripe_customer', stripe_customer)
                company = None
                contact = None

                if stripe_customer and "deleted" not in stripe_customer:
                    # Try to find existing company/contact by email
                    company = Company.objects.filter(
                        workspace=workspace, email=stripe_customer.email
                    ).first()
                    contact = Contact.objects.filter(
                        workspace=workspace, email=stripe_customer.email
                    ).first()

                    if not company and not contact:
                        # Create new company if neither exists
                        if prioritize_company_for_customer:
                            company = Company.objects.create(
                                workspace=workspace,
                                email=stripe_customer.email,
                                name=stripe_customer.name or stripe_customer.email,
                            )
                        else:
                            contact = Contact.objects.create(
                                workspace=workspace,
                                email=stripe_customer.email,
                                name=stripe_customer.name or stripe_customer.email,
                            )

                # Get or create invoice
                invoice_platform = InvoicePlatform.objects.filter(
                    integration__id=channel_integration_id,
                    platform_invoice_id=stripe_invoice.id,
                    invoice__usage_status="active",
                ).first()

                divisor = 100 if stripe_invoice.currency.lower() != "jpy" else 1
                total_price = stripe_invoice.total / divisor
                total_price_without_tax = stripe_invoice.total_excluding_tax / divisor

                if not invoice_platform:
                    invoice_platform = InvoicePlatform.objects.create(
                        integration_id=channel_integration_id,
                        platform_invoice_id=stripe_invoice.id,
                        status="paid" if stripe_invoice.status == "paid" else "draft",
                        created_at=datetime.fromtimestamp(
                            stripe_invoice.created, tz=timezone.utc
                        ),
                    )

                    # Create new invoice platform
                    invoice = Invoice.objects.create(
                        workspace=workspace,
                        usage_status="active",
                    )
                    invoice_platform.invoice = invoice
                    invoice_platform.save()
                else:
                    invoice = invoice_platform.invoice
                invoice.status = "paid" if stripe_invoice.status == "paid" else "draft"
                invoice.company = company
                invoice.contact = contact
                invoice.currency = stripe_invoice.currency.upper()
                invoice.start_date = datetime.fromtimestamp(stripe_invoice.created)
                invoice.due_date = (
                    datetime.fromtimestamp(stripe_invoice.due_date)
                    if stripe_invoice.due_date
                    else None
                )

                if invoice.total_price != total_price:
                    invoice.total_price = total_price
                if invoice.total_price_without_tax != total_price_without_tax:
                    invoice.total_price_without_tax = total_price_without_tax
                invoice.save()

                if order:
                    order.delivery_status = "payment_received"
                    order.save()
                    order.invoice.add(invoice)
                elif subscription:
                    subscription.invoices.add(invoice)

                # Create/update invoice items
                for line_item in stripe_invoice.lines.data:
                    platform_item, created = InvoiceItemPlatform.objects.get_or_create(
                        platform_invoice_item_id=line_item.id
                    )
                    platform_item.integration_id = channel_integration_id
                    invoice_item = platform_item.invoice_item
                    logger.info("invoice item: %s", invoice_item)
                    if created or not invoice_item:
                        logger.info("create")
                        invoice_item = InvoiceItem.objects.create(
                            invoice=invoice,
                        )
                        platform_item.invoice_item = invoice_item
                        platform_item.save()

                    invoice_item.item_name = line_item.description
                    invoice_item.amount_price = (
                        line_item.pricing.unit_amount_decimal
                        if line_item.pricing
                        else 0
                    )
                    invoice_item.amount_item = (
                        line_item.quantity if line_item.quantity else 1
                    )
                    invoice_item.total_price = line_item.amount / (
                        100 if stripe_invoice.currency.lower() != "jpy" else 1
                    )
                    invoice_item.total_price_without_tax = line_item.amount / (
                        100 if stripe_invoice.currency.lower() != "jpy" else 1
                    )
                    invoice_item.created_at = datetime.fromtimestamp(
                        stripe_invoice.created, tz=timezone.utc
                    )
                    if "taxes" in line_item and len(line_item.taxes) > 0:
                        stripe_tax = stripe.TaxRate.retrieve(
                            line_item.taxes[0].tax_rate_details.tax_rate
                        )
                        if stripe_tax.rate_type == "percentage":
                            invoice_item.tax_rate = stripe_tax.percentage
                        if line_item.taxes[0].tax_behavior == "exclusive":
                            invoice_item.total_price_without_tax = (
                                line_item.amount + line_item.taxes[0].amount
                            )
                        else:
                            invoice_item.total_price_without_tax = (
                                line_item.amount - line_item.taxes[0].amount
                            )
                        invoice.tax_option = "item_based_tax"
                        invoice.save()
                    invoice_item.save()
                total_success += 1

            except Exception:
                logger.error(
                    f"Error processing invoice {stripe_invoice}: {traceback.format_exc()}"
                )
                DiscordNotification().send_message(
                    f"Error processing invoice {stripe_invoice}: {traceback.format_exc()}",
                    mention_owner=True,
                )

        # Show success notification
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"{total_success}件の請求書をインポートしました。",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Successfully imported {total_success} invoices.",
                type="success",
            )
        if at:
            output_data["success"] = total_success
            output_data["failed"] = output_data["total"] - total_success
            at.output_data = output_data
            at.status = "success"
            at.save()
        if history:
            history.status = "success"
            history.save()

    except Exception as e:
        print(f"Stripe API Error: {str(e)}")
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Stripeからの請求書のインポートに失敗しました。",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Failed to import invoices from Stripe.",
                type="error",
            )

        if at:
            at.status = "failed"
            at.save()
        if history:
            history.status = "failed"
            history.save()

    return redirect(redirect_url)


@login_or_hubspot_required
def convert_invoice_to_accounting_transaction(request):
    workspace = get_workspace(request.user)
    object_type = TYPE_OBJECT_INVOICE
    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        module_slug = request.GET.get("module")
        if not module_slug:
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=object_type
                )
                .order_by("order", "created_at")
                .first()
            )
            if module:
                module_slug = module.slug

        selected_ids = request.GET.getlist("selected_ids")
        if not selected_ids:
            print("require selected_ids.")
            return HttpResponse(status=400)
        try:
            invoices = Invoice.objects.filter(
                workspace=workspace, usage_status="active", id__in=selected_ids
            ).values("id")
        except:
            return HttpResponse(status=400)

        custom_field = CustomProperty.objects.filter(
            workspace=workspace, model=Invoice._meta.db_table, name="settle_choice"
        )
        if custom_field:
            custom_field = custom_field.first()
            choices = ast.literal_eval(custom_field.value)

            choices = [{"label": choices[value], "value": value} for value in choices]

        else:
            choices = [
                {"label": JOURNAL_ACCOUNT_DISPLAY[value][lang], "value": value}
                for value, label in JOURNAL_ACCOUNT
            ]

        categories = None
        custom_property = CustomProperty.objects.filter(
            workspace=workspace, model=Invoice._meta.db_table, name="category"
        ).first()

        if custom_property:
            categories = custom_property.value
        else:
            # Convert the dictionary to the desired format
            result = []

            for key, category in DEFAULT_JOURNAL_CATEGORY.copy().items():
                category_data = {
                    # Use English name as the category label
                    "category": category[lang],
                    "choices": [
                        {
                            # Use English name as the label
                            "label": choice[lang],
                            "value": choice["value"],  # Keep the value as-is
                        }
                        for choice in category["choices"]
                    ],
                }
                result.append(category_data)
            categories = result

        custom_field = CustomProperty.objects.filter(
            workspace=workspace, model=JournalEntry._meta.db_table, name="tax_rate"
        )
        if custom_field:
            custom_field = custom_field.first()
            item_tax_list = ast.literal_eval(custom_field.value)

            item_tax_list = [
                {"label": item_tax_list[value], "value": value}
                for value in item_tax_list
            ]
        else:
            item_tax_list = [
                {"label": JOURNAL_TAX_CATEGORY_DISPLAY[value][lang], "value": value}
                for value, label in JOURNAL_TAX_CATEGORY
            ]

        context = {
            "categories": categories,
            "choices": choices,
            "objects": invoices,
            "object_type": object_type,
            "module": module_slug,
            "action_id": request.GET.get("action_id"),
            "is_record_action": request.GET.get("is_record_action", ""),
            "item_tax_list": item_tax_list,
        }
        return render(
            request,
            "data/commerce/action/convert-invoice-to-accounting-transaction.html",
            context,
        )

    # POST
    module_slug = request.POST.get("module")
    if not module_slug:
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=object_type
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug

    selected_ids = request.POST.getlist("selected_ids")
    notes = request.POST.get("notes")

    try:
        invoices = Invoice.objects.filter(
            workspace=workspace, usage_status="active", id__in=selected_ids
        )
    except:
        return HttpResponse(status=400)

    action_id = request.POST.get("action_id")
    action = Action.objects.filter(slug=action_id).first()
    input_data = dict(request.POST)
    input_data = {
        key: value[0] if len(value) == 1 else value for key, value in input_data.items()
    }

    journal_module = Module.objects.filter(
        workspace=workspace, object_values__icontains=TYPE_OBJECT_JOURNAL
    ).first()
    for invoice in invoices:
        history = ActionHistory.objects.create(
            workspace=workspace,
            action=action,
            status="initialized",
            created_by=request.user,
            object_type=TYPE_OBJECT_INVOICE,
            object_id=invoice.id,
        )

        history.status = "running"
        history.save()
        transfer_history = ActionTaskHistory.objects.create(
            workspace=history.workspace,
            action_history=history,
            status="initialized",
            input_data=input_data,
        )
        try:
            transfer_history.status = "running"
            transfer_history.save()

            amount = invoice.total_price

            journal = JournalEntry.objects.create(
                workspace=workspace,
                company=invoice.company,
                contact=invoice.contact,
                invoice=invoice,
            )

            # Invoice Issue Date is Journal Transaction Date (Date occurence) If Not Available set the issue date as occurence
            if invoice.start_date:
                journal.transaction_date = invoice.start_date
            else:
                journal.transaction_date = invoice.issue_date

            # Set Settle Category as Account Receiveable (This is wrong, but the Create Journal doing that!)
            journal.category = "account_receiveable"
            # Set Type Journal as `False`. Not sure what it is: Create Journal doing so, I just follow.
            journal.type_journal = False
            # Set Settle Select As True . I'm also not sure about this: HELP :') Create Journal doing so, I just follow.
            journal.settle_select = True

            # Lastly get the Tax ? (Not sure how to do this) lets use tax_rate = 0
            journal.tax_rate = 0

            # Journal Amount is equal to Invoice Total Price
            journal.amount = amount

            # Input the Notes Created
            if notes:
                journal.notes = notes

            journal.save()

            # Setup For Category Transaction:
            # We will use the following categories: accounts_receivable, sales (Revenue)
            category_transactions = ["accounts_receivable", "sales"]
            # Settlement Dates
            # We will use the journal.date_occurence as the settlement date
            date = journal.transaction_date.strftime("%Y-%m-%d")
            settlement_dates = [date, date]
            # amount_transactions
            # This is amount of the Invoice
            amount_transactions = [amount, amount]
            # Setup Type Transaction for both transactions
            # This will be Debit Credit Part I guess
            type_transactions = ["income", "expense"]

            # Setup Page Object
            page_obj = get_page_object(TYPE_OBJECT_JOURNAL, lang)

            generate_settlements(
                amount_transactions,
                settlement_dates,
                category_transactions,
                type_transactions,
                journal,
                page_obj,
                lang="en",
            )

            obj_dict = model_to_dict(journal)
            serialized_data = json.dumps(obj_dict, default=custom_serializer)
            transfer_history.output_data = json.loads(serialized_data)
            if journal_module:
                transfer_history.result_link = (
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": journal_module.slug,
                            "object_slug": OBJECT_TYPE_TO_URL_NAME[TYPE_OBJECT_JOURNAL],
                        },
                    )
                    + f"?id={journal.id}"
                )
            transfer_history.completed_at = timezone.now()
            transfer_history.status = "success"
            transfer_history.save()
            print("log: Invoice converted to journal entry successfully.")
            history.result_link = (
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": journal_module.slug,
                        "object_slug": OBJECT_TYPE_TO_URL_NAME[TYPE_OBJECT_JOURNAL],
                    },
                )
                + f"?id={journal.id}"
            )
            history.status = "completed"
            history.save()

        except IntegrityError:
            history.status = "重複" if lang == "ja" else "Duplicate"
            history.save()

            transfer_history.completed_at = timezone.now()
            transfer_history.status = "success"
            transfer_history.error_message = (
                f"Invoice #{invoice.id_inv:04d} is already linked to a journal entry."
            )
            transfer_history.error_message_ja = f"請求書 #{invoice.id_inv:04d} はすでに仕訳帳エントリに関連付けられています。"
            transfer_history.save()
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"請求書 #{invoice.id_inv:04d} はすでに仕訳のアソシエーションを保有しています。"
                if lang == "ja"
                else f"Invoice #{invoice.id_inv:04d} has already an Accounting Transaction association.",
                type="error",
            )

        except Exception:
            traceback.print_exc()
            transfer_history.completed_at = timezone.now()
            transfer_history.status = "failed"
            transfer_history.save()
            pass
    if module_slug:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + f"?id={invoice.id}&sidedrawer=action-history"
        )
    else:
        return redirect(reverse("main", host="app"))


def aggregate_order(
    listed_dict=[],
    customers_=None,
    object_type=TYPE_OBJECT_ORDER,
    lang="ja",
    due_date=None,
    tax_option=None,
    use_account_rec=False,
    convert_to_invoice=False,
    convert_to_purchase_order=False,
    selected_ids=[],
):
    page_obj = get_page_object(object_type, lang)
    base_model = page_obj["base_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    custom_value_model = page_obj["custom_value_model"]
    order_total_price = 0
    order_total_price_without_tax = 0
    if selected_ids and len(selected_ids) > 0:
        objects = base_model.objects.filter(id__in=selected_ids)
    else:
        objects = base_model.objects.filter(id__in=listed_dict[str(customers_.id)])
    if objects:
        object_ = objects.first()
        workspace = object_.workspace
        order_combined = base_model.objects.create(
            workspace=workspace, currency=object_.currency.upper()
        )
        for order in objects:
            order_total_price += order.total_price
            order_total_price_without_tax += order.total_price_without_tax
            items_order = order.shopturboitemsorders_set.all()
            if items_order:
                for shopturboitemsorder in items_order.order_by("order_view"):
                    if order.order_type == "item_order":
                        if shopturboitemsorder:
                            shopturboitemsorder.order = order_combined
                            shopturboitemsorder.id = None
                            shopturboitemsorder.save()
            # elif order.order_type == 'manual_order':
            #     item_price_order += order.item_price_order

            if order.order_type:
                order_combined.order_type = order.order_type

            if order.contact and not order_combined.contact:
                order_combined.contact = order.contact

            if order.company and not order_combined.company:
                order_combined.company = order.company

            if order.delivery_status:
                order_combined.delivery_status = order.delivery_status

            customFields = custom_value_model.objects.filter(
                **{custom_value_relation: objects.first()}
            )

            for field in customFields:
                custom_value_model.objects.create(
                    **{
                        custom_value_relation: order_combined,
                        "value": field.value,
                        "field_name": field.field_name,
                    }
                )
        order_combined.total_price = order_total_price
        order_combined.total_price_without_tax = order_total_price_without_tax
        order_combined.save()
        if convert_to_invoice:
            try:
                invoice_combined = convert_order_to_commerce_partial(
                    [order_combined],
                    object_type=TYPE_OBJECT_INVOICE,
                    lang=lang,
                    use_account_rec=use_account_rec,
                )
                return "Success", invoice_combined[0], len(objects)
            except Exception as e:
                traceback.print_exc()
                print("Error converting: ", e)
                return "Error converting order to invoice", "", ""
        if convert_to_purchase_order:
            try:
                purchase_order_combined = convert_order_to_commerce_partial(
                    [order_combined],
                    object_type=TYPE_OBJECT_PURCHASE_ORDER,
                    lang=lang,
                    use_account_rec=use_account_rec,
                )
                return "Success", purchase_order_combined[0], len(objects)
            except Exception as e:
                traceback.print_exc()
                print("Error converting: ", e)
                return "Error converting order to purchase order", "", ""
        return "Success", order_combined, len(objects)
    return "No Objects found", "", ""


def convert_order_to_commerce_partial(
    orders,
    object_type=None,
    lang="ja",
    use_account_rec=False,
    tax_option="item_based_tax",
):
    page_obj = get_page_object(object_type, lang)
    base_model = page_obj["base_model"]
    item_model = page_obj["item_model"]
    field_item_name = page_obj["field_item_name"]
    objects = []
    order = orders[0]
    workspace = order.workspace
    start_date = order.order_at
    due_date = order.order_at
    slip_type = None
    tax_inclusive = None

    for order in orders:
        currency = order.currency
        tax_item_rate = 0

        obj = base_model.objects.create(workspace=workspace)
        if tax_inclusive:
            obj.tax_inclusive = tax_inclusive

        try:
            if field_item_name == "purchaseorder":
                field_item_name = "purchase_order"
            obj_items = item_model.objects.filter(**{field_item_name: obj}).order_by(
                "created_at"
            )
            if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                order_items = None
            else:
                order_items = ShopTurboItemsOrders.objects.filter(order=order)
            if order_items:
                for order_item in order_items:
                    obj_item = item_model.objects.create(**{field_item_name: obj})
                    if obj_items:
                        for obj__item in obj_items:
                            try:
                                obj__item.delete()
                            except:
                                pass

                    if order_item.item:
                        obj_item.item_link = order_item.item
                    elif order_item.custom_item_name:
                        obj_item.item_name = order_item.custom_item_name

                    obj_item.amount_price = order_item.item_price_order
                    obj_item.amount_item = order_item.number_item

                    try:
                        item_price = order_item.item.shopturbo_item_price.filter(
                            default=True
                        ).first()
                        if item_price:
                            tax_item_rate = item_price.tax
                        else:
                            tax_item_rate = 0
                    except:
                        tax_item_rate = 0

                    if obj.tax_inclusive:
                        obj_item.total_price_without_tax = (
                            float(obj_item.amount_price)
                            * float(obj_item.amount_item)
                            / (1 + (float(tax_item_rate) / 100.0))
                        )
                        obj_item.total_price = float(obj_item.amount_price) * float(
                            obj_item.amount_item
                        )
                    else:
                        if tax_item_rate != 0:
                            obj_item.total_price_without_tax = float(
                                obj_item.amount_price
                            ) * float(obj_item.amount_item)
                            obj_item.total_price = (
                                float(obj_item.amount_price) * float(tax_item_rate)
                                + float(obj_item.amount_price)
                            ) * float(obj_item.amount_item)
                        else:
                            obj_item.total_price_without_tax = float(
                                obj_item.amount_price
                            ) * float(obj_item.amount_item)
                            obj_item.total_price = float(obj_item.amount_price) * float(
                                obj_item.amount_item
                            )

                    obj_item.tax_rate = tax_item_rate
                    obj_item.save()
            else:
                obj_item = item_model.objects.create(**{field_item_name: obj})
                obj_item.item_name = f"#{int(order.order_id):04d}" + " - Order"
                obj_item.amount_price = order.total_price
                obj_item.amount_item = 1
                tax_item_rate = 0

                if object_type == TYPE_OBJECT_PURCHASE_ORDER:
                    if obj.tax_option == "unified_tax":
                        tax_item_rate = obj.tax_rate if obj.tax_rate else 0
                        obj_item.total_price_without_tax = (
                            float(obj_item.amount_price)
                            * float(obj_item.amount_item)
                            / (1 + (float(tax_item_rate) / 100.0))
                        )
                        obj_item.total_price = float(obj_item.amount_price) * float(
                            obj_item.amount_item
                        )
                    else:
                        if tax_item_rate != 0:
                            obj_item.total_price_without_tax = float(
                                obj_item.amount_price
                            ) * float(obj_item.amount_item)
                            obj_item.total_price = (
                                float(obj_item.amount_price) * float(tax_item_rate)
                                + float(obj_item.amount_price)
                            ) * float(obj_item.amount_item)
                        else:
                            obj_item.total_price_without_tax = float(
                                obj_item.amount_price
                            ) * float(obj_item.amount_item)
                            obj_item.total_price = float(obj_item.amount_price) * float(
                                obj_item.amount_item
                            )
                else:
                    if obj.tax_inclusive:
                        obj_item.total_price_without_tax = (
                            float(obj_item.amount_price)
                            * float(obj_item.amount_item)
                            / (1 + (float(tax_item_rate) / 100.0))
                        )
                        obj_item.total_price = float(obj_item.amount_price) * float(
                            obj_item.amount_item
                        )
                    else:
                        if tax_item_rate != 0:
                            obj_item.total_price_without_tax = float(
                                obj_item.amount_price
                            ) * float(obj_item.amount_item)
                            obj_item.total_price = (
                                float(obj_item.amount_price) * float(tax_item_rate)
                                + float(obj_item.amount_price)
                            ) * float(obj_item.amount_item)
                        else:
                            obj_item.total_price_without_tax = float(
                                obj_item.amount_price
                            ) * float(obj_item.amount_item)
                            obj_item.total_price = float(obj_item.amount_price) * float(
                                obj_item.amount_item
                            )

                obj_item.tax_rate = tax_item_rate
                obj_item.save()

            if use_account_rec:
                print("Add User Accounting:")
                unsettle_accounts = None
                unsettle_accounts_amount = 0
                if obj.contact:
                    check_formula_field = ContactsNameCustomField.objects.filter(
                        workspace=workspace, type="formula"
                    ).first()
                    if check_formula_field:
                        if (
                            "amount_after_settlement"
                            in check_formula_field.choice_value
                        ):
                            unsettle_accounts = JournalEntry.objects.filter(
                                contact=obj.contact
                            )
                elif obj.company:
                    check_formula_field = CompanyNameCustomField.objects.filter(
                        workspace=workspace, type="formula"
                    ).first()
                    if check_formula_field:
                        if (
                            "amount_after_settlement"
                            in check_formula_field.choice_value
                        ):
                            unsettle_accounts = JournalEntry.objects.filter(
                                company=obj.company
                            )
                if unsettle_accounts:
                    for account in unsettle_accounts:
                        if account.amount_after_settlement:
                            unsettle_accounts_amount += account.amount_after_settlement
                if unsettle_accounts_amount:
                    obj_item = item_model.objects.create(**{field_item_name: obj})
                    if lang == "ja":
                        obj_item.item_name = "売掛金"
                    else:
                        obj_item.item_name = "Accounts Receivable"
                    obj_item.amount_price = unsettle_accounts_amount
                    obj_item.amount_item = 1
                    obj_item.total_price_without_tax = unsettle_accounts_amount
                    obj_item.total_price = unsettle_accounts_amount
                    obj_item.tax_rate = 0
                    obj_item.save()

        except Exception as e:
            traceback.print_exc()
            print("=== DEBUG Exception", e)

        obj.workspace = workspace
        obj.currency = currency
        obj.usage_status = "active"
        obj.status = "draft"

        # obj.send_from = send_from

        if object_type == TYPE_OBJECT_SLIP:
            obj.slip_type = slip_type

        if object_type == TYPE_OBJECT_INVOICE:
            if due_date:
                obj.due_date = due_date

        if object_type == TYPE_OBJECT_PURCHASE_ORDER:
            if start_date:
                obj.date = start_date

        if start_date:
            obj.start_date = start_date

        if order.company:
            obj.company = order.company
            obj.contact = None

        elif order.contact:
            obj.contact = order.contact
            obj.company = None

        obj.save()
        if object_type != TYPE_OBJECT_PURCHASE_ORDER:
            obj = handling_items(obj)
        print("Obj Handled 3: ")
        print(obj)
        objects.append(obj)
    return objects


def _perform_send_record_email(request, workspace, lang, history_id):
    """
    Perform the actual email sending logic when called from background job
    """
    from django.utils import timezone
    from utils.logger import logger

    logger.info(
        f"=== DEBUG: _perform_send_record_email started with history_id: {history_id} ==="
    )

    try:
        # Get the ActionHistory record
        history = ActionHistory.objects.get(id=history_id, workspace=workspace)
        logger.info(f"Found ActionHistory: {history}")

        # Update status to running
        history.status = "running"
        history.save()

        # Create ActionTaskHistory for tracking
        task_history = ActionTaskHistory.objects.create(
            workspace=workspace,
            action_history=history,
            status="running",
            input_data=dict(request.POST),
        )

        # Get form data for email sending
        object_type = request.POST.get("object_type")
        # smtp_channel_id = request.POST.get("smtp_channel_id")
        template_send_to = request.POST.get("template-send-to")
        template_cc = request.POST.get("template-cc")
        subject = request.POST.get("subject")
        body = request.POST.get("body")

        logger.info(
            f"=== DEBUG: Email parameters - object_type: {object_type}, subject: {subject} ==="
        )

        # Get the record object to send email about
        if object_type and history.object_id:
            try:
                # Get the page object to determine the model
                page_obj = get_page_object(object_type, lang)
                base_model = page_obj["base_model"]
                id_field = page_obj["id_field"]
                page_title = page_obj["page_title"]

                # Get the specific record
                obj = base_model.objects.get(workspace=workspace, id=history.object_id)
                logger.info(f"=== DEBUG: Found object: {obj} ===")

                # Get the customer email based on template_send_to
                if template_send_to == "customer__email":
                    # For invoices, check both company and contact fields
                    email = None

                    # First try to get email from contact
                    if (
                        hasattr(obj, "contact")
                        and obj.contact
                        and hasattr(obj.contact, "email")
                        and obj.contact.email
                    ):
                        email = obj.contact.email
                        logger.info(f"=== DEBUG: Found email from contact: {email} ===")
                    # Then try to get email from company
                    elif (
                        hasattr(obj, "company")
                        and obj.company
                        and hasattr(obj.company, "email")
                        and obj.company.email
                    ):
                        email = obj.company.email
                        logger.info(f"=== DEBUG: Found email from company: {email} ===")
                    # For other object types that might have customers field
                    elif hasattr(obj, "customers") and obj.customers:
                        if hasattr(obj.customers, "email") and obj.customers.email:
                            email = obj.customers.email
                            logger.info(
                                f"=== DEBUG: Found email from customers: {email} ==="
                            )
                        elif (
                            hasattr(obj.customers, "contact")
                            and obj.customers.contact
                            and hasattr(obj.customers.contact, "email")
                        ):
                            email = obj.customers.contact.email
                            logger.info(
                                f"=== DEBUG: Found email from customers.contact: {email} ==="
                            )

                    if not email:
                        logger.error(
                            f"=== DEBUG: No email found. Object has contact: {hasattr(obj, 'contact')}, company: {hasattr(obj, 'company')}, customers: {hasattr(obj, 'customers')} ==="
                        )
                        if hasattr(obj, "contact"):
                            logger.error(
                                f"=== DEBUG: Contact object: {obj.contact} ==="
                            )
                        if hasattr(obj, "company"):
                            logger.error(
                                f"=== DEBUG: Company object: {obj.company} ==="
                            )
                        raise Exception("Customer email not found")
                else:
                    # Handle other email field mappings if needed
                    raise Exception(f"Unsupported email field: {template_send_to}")

                logger.info(f"=== DEBUG: Sending email to: {email} ===")

                # Generate PDF attachment
                from data.commerce.commerce_pdf_download import commerce_pdf_download

                logger.info("=== DEBUG: Generating PDF ===")
                attachment_byte = commerce_pdf_download(
                    request=request, id=obj.id, send_email=True, object_type=object_type
                )
                logger.info("=== DEBUG: PDF generated successfully ===")

                # Prepare email
                obj_id = getattr(obj, id_field, None)
                if id_field != "id" and obj_id:
                    obj_id = f"{obj_id:04d}"

                # Use custom subject and body if provided
                if subject:
                    mail_subject = subject
                else:
                    if lang == "ja":
                        mail_subject = f"新しい{page_title}を受け取りました[#{obj_id}]"
                    else:
                        mail_subject = f"You received a new {page_title} [#{obj_id}]"

                if body:
                    message = body
                else:
                    customers_name = ""
                    # Get customer name from contact or company
                    if (
                        hasattr(obj, "contact")
                        and obj.contact
                        and hasattr(obj.contact, "name")
                    ):
                        customers_name = obj.contact.name
                    elif (
                        hasattr(obj, "company")
                        and obj.company
                        and hasattr(obj.company, "name")
                    ):
                        customers_name = obj.company.name
                    # For other object types that might have customers field
                    elif hasattr(obj, "customers") and obj.customers:
                        if hasattr(obj.customers, "company") and obj.customers.company:
                            customers_name = obj.customers.company.name
                        elif (
                            hasattr(obj.customers, "contact") and obj.customers.contact
                        ):
                            customers_name = obj.customers.contact.name

                    if lang == "ja":
                        message = f"{customers_name}さま\n{page_title}を添付致しました。ご査収いただきますようお願い致します。"
                    else:
                        message = f"Hi {customers_name}, \nThe attached is your {page_title}. Please take a look and should you have any questions contact us. Thank you."

                # Create and send email
                from django.core.mail import EmailMessage

                name_pdf = f"{page_title}_{obj_id}.pdf"
                from_email = "Sanka <<EMAIL>>"
                to_email = [email]

                # Handle CC emails
                list_email_cc = []
                if template_cc:
                    list_email_cc = [template_cc]

                email_message = EmailMessage(
                    mail_subject,
                    message,
                    from_email,
                    to_email,
                    cc=list_email_cc,
                )

                # Attach PDF
                if object_type != TYPE_OBJECT_ORDER:
                    email_message.attach(name_pdf, attachment_byte)

                logger.info("=== DEBUG: Sending email ===")
                email_message.send()
                logger.info("=== DEBUG: Email sent successfully ===")

            except Exception as e:
                logger.error(f"=== DEBUG: Error sending email: {e} ===")
                logger.error(traceback.format_exc())
                raise e
        else:
            logger.error(
                "=== DEBUG: Missing object_type or object_id for email sending ==="
            )
            raise Exception("Missing required data for email sending")

        # Update task history to success
        task_history.status = "success"
        task_history.completed_at = timezone.now()
        task_history.save()

        # Update action history to success
        history.status = "success"
        history.completed_at = timezone.now()
        history.save()

        logger.info("=== DEBUG: Email sending completed successfully ===")
        return HttpResponse(status=200)

    except ActionHistory.DoesNotExist:
        logger.error(f"ActionHistory with id {history_id} not found")
        return HttpResponse(status=404)
    except Exception as e:
        logger.error(f"Error in _perform_send_record_email: {e}")
        logger.error(traceback.format_exc())

        # Update history to failed if it exists
        try:
            history = ActionHistory.objects.get(id=history_id, workspace=workspace)
            history.status = "failed"
            history.completed_at = timezone.now()
            history.save()

            # Update task history to failed if it exists
            task_history = ActionTaskHistory.objects.filter(
                action_history=history
            ).last()
            if task_history:
                task_history.status = "failed"
                task_history.error_message = str(e)
                task_history.completed_at = timezone.now()
                task_history.save()
        except:
            pass

        return HttpResponse(status=500)


@login_or_hubspot_required
@require_http_methods(["POST"])
def send_record_email(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    action_slug = request.POST.get("action_slug")
    redirect_url = request.POST.get("redirect_url")
    bulk_action = request.POST.get("bulk_action")
    is_record_action = request.POST.get("is_record_action")
    history_id = request.POST.get("history_id")

    logger.info("=== DEBUG: send_record_email called ===")
    logger.info(f"Action slug: {action_slug}")
    logger.info(f"History ID: {history_id}")
    logger.info(f"Bulk action: {bulk_action}")
    logger.info(f"Is record action: {is_record_action}")

    # If history_id is provided, this is being called from the background job
    # and we should perform the actual email sending
    if history_id:
        logger.info(
            "=== DEBUG: Called from background job, performing actual email sending ==="
        )
        return _perform_send_record_email(request, workspace, lang, history_id)

    # Otherwise, this is the initial form submission and we should trigger the background job
    logger.info("=== DEBUG: Initial form submission, triggering background job ===")
    job_id = str(uuid.uuid4())

    history = None
    if bulk_action or is_record_action:
        logger.info(f"=== DEBUG: Looking for Action with slug: {action_slug} ===")
        action = Action.objects.filter(slug=action_slug).first()
        logger.info(f"=== DEBUG: Found action: {action} ===")

        if action:
            history = ActionHistory.objects.create(
                workspace=workspace,
                action=action,
                status="initialized",
                created_by=request.user,
            )
            logger.info(f"=== DEBUG: Created ActionHistory: {history.id} ===")
        else:
            logger.error(f"=== DEBUG: No action found for slug: {action_slug} ===")
            # List all available actions for debugging
            all_actions = Action.objects.all().values_list("slug", flat=True)
            logger.error(f"Available action slugs: {list(all_actions)}")

    param = {
        "function": "run_action_view_function",
        "job_id": job_id,
        "workspace_id": str(workspace.id),
        "user_id": str(request.user.id),
        "args": [
            f"--workspace_id={workspace.id}",
            f"--user_id={request.user.id}",
            f"--lang={lang}",
            f"--action_slug={action_slug}",
        ],
    }

    # Add tracking IDs if provided
    if bulk_action:
        param["args"].append("--bulk_action=True")

    if is_record_action:
        param["args"].append("--is_record_action=True")

    form_data = {}
    print(request.POST)
    for key in request.POST.keys():
        values = request.POST.getlist(key)

        if len(values) == 1:
            value = values[0]
            if (
                isinstance(value, str)
                and value
                and value[0] == "["
                and value[-1] == "]"
            ):
                form_data[key] = ast.literal_eval(value)
            else:
                form_data[key] = value
        else:
            form_data[key] = values
    if history:
        form_data["history_id"] = str(history.id)

    param["args"].append(f"--form_data={form_data}")

    # =====================================================================================

    is_running = False
    background_job, _ = BackgroundJob.objects.get_or_create(
        job_id=job_id,
        defaults={
            "workspace": workspace,
            "user": request.user,
            "name": "[Background Job] run_action_view_function",
            "payload": param,
            "status": BackgroundJob.BackgroundJobStatus.PENDING,
            "log": [],
        },
    )

    if settings.LOCAL:
        # Use data parameter for consistent handling
        res = requests.post(
            "http://127.0.0.1:8000/api/run-job/",
            json=param,
            headers={"Host": "app.localhost:8000"},
        )

        if res.status_code >= 202:
            is_running = True
        else:
            is_running = False
    else:
        bg_job_payload = BackgroundJobPayload(
            job_id=job_id,
            workspace_id=str(workspace.id),
            user_id=str(request.user.id),
            function="run_action_view_function",
            payload=param,
            transfer_history_id=None,
            workflow_history_id=None,
        )

        try:
            ref = run_send_record_email_action.run_no_wait(
                input=bg_job_payload,
            )
            logger.info(f"Background task triggered! Result: {ref}")
            if ref:
                background_job.hatchet_run_id = str(ref)
                background_job.save()

            is_running = True
        except Exception as e:
            logger.error(f"Error triggering background task: {e}")
            is_running = False

    # Check response status
    if not is_running:
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message="Failed to queue background job"
            if lang == "en"
            else "バックグラウンドジョブのキューに失敗しました",
            type="error",
        )
        return HttpResponse(status=500)

    # Create success notification
    Notification.objects.create(
        workspace=workspace,
        user=request.user,
        message="Background job queued successfully"
        if lang == "en"
        else "バックグラウンドジョブがキューに追加されました",
        type="success",
    )

    # Redirect to the specified URL
    return redirect(redirect_url)
