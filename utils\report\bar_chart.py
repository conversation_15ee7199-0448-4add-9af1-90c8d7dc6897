import random
import re
import traceback
import uuid
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from typing import List
from types import SimpleNamespace
from zoneinfo import ZoneInfo

from dateutil.relativedelta import relativedelta
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.paginator import Paginator
from django.db import connection
from django.db.models import (Avg, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Count, Date<PERSON>ield,
                              DateTimeField, ExpressionWrapper, F, FloatField,
                              Func, IntegerField, Max, Min, OuterRef, Q,
                              Subquery, Sum, Value, When)
from django.db.models.functions import (Cast, Coalesce, Concat, TruncDate,
                                        TruncHour, TruncMonth, TruncWeek)
from django.utils import timezone

from data.constants.constant import (CHART_CHANNEL_STAT, CHART_POST_STAT,
                                     PANEL_METRIC_TITLE)
from data.constants.date_constant import *
from data.constants.properties_constant import *
from data.models import *
from utils.currency_symbols._constants import *
from utils.currency_symbols.currency_symbols import CurrencySymbols
from utils.filter import build_filters, build_raw_filters, q_has_field
from utils.forecast import Forecast
from utils.formula import calculate_math
from utils.properties.properties import (ALIAS_TABLE, get_list_view_columns,
                                         get_object_display_based_columns,
                                         get_page_object)
from utils.utility import is_valid_uuid, to_snake_case
from utils.reports import get_line_chart_params, get_time_range, new_result, new_result_invoice_growth, new_result_detailed_growth

def fetch_bar_chart_data(panel: ReportPanel, start_time=None, stop_time=None, lang=None, group_by=None, is_realtime=None):
    workspace = panel.workspace
    label = ''
    chart_config = {
        'fill': True,
        'radius': 3
    }
    net_result = {}

    if is_realtime is not None:
        panel.start_time = start_time
        panel.end_time = stop_time
        group_by = group_by if group_by else 'day'
        panel.group_by = group_by
        is_realtime = is_realtime if is_realtime is not None else True
        panel.is_realtime = is_realtime
        panel.save()
    else:
        start_time = panel.start_time if panel.start_time else start_time
        stop_time = panel.end_time if panel.end_time else stop_time
        group_by = panel.group_by if panel.group_by else 'day'
        is_realtime = panel.is_realtime if panel.is_realtime is not None else True

    trunc_func, time_format, time_step, start_time = get_line_chart_params(
        group_by, start_time)
    data_points = get_time_range(start_time, stop_time, time_format, time_step)
    if is_realtime:  # Update stop_time by current date
        stop_time = datetime.fromtimestamp(
            (datetime.today() - timezone.timedelta(days=0)).timestamp(), tz=timezone.utc)
        start_time = stop_time - time_step*(len(data_points)-1)
        panel.end_time = stop_time
        panel.start_time = start_time
        panel.group_by = group_by
        panel.is_realtime = is_realtime
        panel.save()
        trunc_func, time_format, time_step, start_time = get_line_chart_params(
            group_by, start_time)
        data_points = get_time_range(
            start_time, stop_time, time_format, time_step)

    start_time_str = start_time.strftime('%Y-%m-%dT%H:%M:%S')
    stop_time_str = stop_time.strftime('%Y-%m-%dT%H:%M:%S')

    results = []
    x_axis = []
    for point in data_points:
        x_axis.append(point['display'])

    rule_filter = Q()
    if panel.filter and panel.filter['filters']:
        rule_filter = build_filters(panel.filter['filters'])
        print(f'=== report.py - 770: Filter for {panel.name}: {rule_filter}')

    metrics = panel.list_metric_names_and_metric_types()
    print('........... metrics', metrics)
    for metric in metrics:
        metric_name = metric['name']
        metric_type = metric['type']
        metric_filter = PanelMetric.objects.filter(
            metric__contains=metric_name, reportpanel=panel).values_list('filter', flat=True).first()
        print(metric_filter)
        print(metric_name)
        try:
            if panel.data_source == 'orders':
                dynamic_annotation = {}
                dynamic_values = ['count']
                is_date = False

                if panel.breakdown:
                    dynamic_values.append('breakdown')
                    if 'custom_field__' in panel.breakdown:
                        custom_field = panel.breakdown.replace(
                            "custom_field__", '')
                        cf_qs = (
                            ShopTurboOrdersValueCustomField.objects
                            .filter(
                                orders_id=OuterRef('pk'),
                                field_name_id=custom_field,
                            )
                            .values('value')[:1]
                        )

                        dynamic_annotation['breakdown'] = Subquery(
                            cf_qs,
                            output_field=CharField()
                        )
                    elif 'date' in panel.breakdown:
                        dynamic_annotation['breakdown'] = trunc_func(
                            panel.x_axis)
                    else:
                        dynamic_annotation['breakdown'] = F(panel.breakdown)

                if panel.x_axis:
                    dynamic_values.append('x_axis')
                    if panel.x_axis == 'timeseries':
                        is_date = True
                        dynamic_annotation['x_axis'] = trunc_func('order_at')
                    elif 'date' in panel.x_axis:
                        dynamic_annotation['x_axis'] = trunc_func(panel.x_axis)
                    elif 'custom_field__' in panel.x_axis:
                        custom_field = panel.x_axis.replace(
                            "custom_field__", '')
                        cf_qs = (
                            ShopTurboOrdersValueCustomField.objects
                            .filter(
                                orders_id=OuterRef('pk'),
                                field_name_id=custom_field,
                            )
                            .values('value')[:1]
                        )

                        dynamic_annotation['x_axis'] = Subquery(
                            cf_qs,
                            output_field=CharField()
                        )
                    else:
                        dynamic_annotation['x_axis'] = F(panel.x_axis)

                if metric_name == 'number_orders':
                    condition = Q(
                        workspace=workspace, order_at__gte=start_time_str, order_at__lte=stop_time_str)
                    condition &= ~Q(status='draft')

                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 770: Filter for {metric_name}: {condition}')

                    queryset = (
                        ShopTurboOrders.objects
                        .filter(condition)
                        .annotate(**dynamic_annotation)
                        .values(*dynamic_values[1:])
                        .annotate(count=Count('x_axis'))
                        .order_by(*dynamic_values[1:])
                    )

                elif metric_name == 'revenue':
                    currency = ShopTurboOrders.objects.filter(
                        workspace=workspace, status='active').values_list('currency', flat=True).first()
                    # label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                    condition = Q(workspace=workspace, order_at__gte=start_time_str,
                                  order_at__lte=stop_time_str, status='active', total_price__isnull=False)
                    condition &= ~Q(status='draft')

                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 1114: Filter for {metric_name}: {condition}')

                    queryset = (
                        ShopTurboOrders.objects
                        .filter(condition)
                        .annotate(**dynamic_annotation)
                        .values(*dynamic_values[1:])
                        .annotate(
                            count=Sum(
                                Case(
                                    When(tax__isnull=True,
                                         then=F('total_price')),
                                    default=F('total_price') *
                                    (1 - F('tax') / 100),
                                    output_field=FloatField()
                                )
                            )
                        )
                        .order_by(*dynamic_values[1:])
                    )
                    print(
                        f'=== report.py - 947: Query result for {metric_name} - {queryset}')

                if len(x_axis) <= 0:
                    for item in queryset:
                        label = item['x_axis']
                        if not item['x_axis']:
                            label = "他の" if lang == 'ja' else 'other'
                        if label not in x_axis:
                            x_axis.append(label)

                matrix = defaultdict(lambda: defaultdict(int))
                for row in queryset:
                    xx = row['x_axis'].strftime("%Y-%m-%d")
                    cnt = row['count']

                    xx = xx if xx else "他の" if lang == 'ja' else 'other'

                    if panel.breakdown:
                        bx = row['breakdown']
                        matrix[bx][xx] = cnt
                    else:
                        matrix[xx] = cnt
                if panel.breakdown:
                    breakdown = []
                    for row in queryset:
                        if row['breakdown'] not in breakdown:
                            breakdown.append(row['breakdown'])

                    result_counts = {
                        bx: [matrix.get(bx, {}).get(xx, 0) for xx in x_axis]
                        for bx in breakdown
                    }

                    for bx in breakdown:
                        result_count = result_counts.get(bx, None)
                        if not result_count:
                            result_count = [0] * len(x_axis)

                        results.append(new_result(
                            bx, result_count, len(results)))
                else:
                    result_counts = [matrix.get(xx, 0) for xx in x_axis]
                    results.append(new_result(
                        'データ' if lang == 'ja' else 'Data', result_counts, len(results)))

            elif panel.data_source == 'subscriptions':
                start_time_str = start_time.strftime('%Y-%m-%d')
                stop_time_str = stop_time.strftime('%Y-%m-%d')
                dynamic_annotation = {}
                dynamic_values = ['count']
                is_date = False

                if panel.breakdown:
                    if panel.breakdown not in ['subscription_growth', 'subscription_revenue']:
                        dynamic_values.append('breakdown')
                        if 'custom_field__' in panel.breakdown:
                            custom_field = panel.breakdown.replace(
                                "custom_field__", '')
                            cf_qs = (
                                ShopTurboSubscriptionsValueFile.objects
                                .filter(
                                    subscriptions_id=OuterRef('pk'),
                                    field_name_id=custom_field,
                                )
                                .values('value')[:1]
                            )

                            dynamic_annotation['breakdown'] = Subquery(
                                cf_qs,
                                output_field=CharField()
                            )
                        elif 'date' in panel.breakdown:
                            dynamic_annotation['breakdown'] = trunc_func(
                                panel.x_axis)
                        else:
                            dynamic_annotation['breakdown'] = F(
                                panel.breakdown)
                    else:
                        dynamic_annotation['contact_'] = F('contact')
                        dynamic_annotation['company_'] = F('company')
                        dynamic_annotation['invoice_total_price_'] = ArrayAgg(
                            'invoices__total_price')
                        dynamic_values.append('contact_')
                        dynamic_values.append('company_')
                        dynamic_values.append('invoice_total_price_')

                if panel.x_axis:
                    dynamic_values.append('x_axis')
                    if panel.x_axis == 'timeseries':
                        is_date = True
                        dynamic_annotation['x_axis'] = trunc_func('start_date')
                    elif 'date' in panel.x_axis:
                        is_date = True
                        dynamic_annotation['x_axis'] = trunc_func(panel.x_axis)
                    elif 'custom_field__' in panel.x_axis:
                        custom_field = panel.x_axis.replace(
                            "custom_field__", '')
                        cf_qs = (
                            ShopTurboSubscriptionsValueFile.objects
                            .filter(
                                subscriptions_id=OuterRef('pk'),
                                field_name_id=custom_field,
                            )
                            .values('value')[:1]
                        )

                        dynamic_annotation['x_axis'] = Subquery(
                            cf_qs,
                            output_field=CharField()
                        )
                    else:
                        dynamic_annotation['x_axis'] = F(panel.x_axis)

                if metric_name == 'number_subscriptions':
                    condition = Q(
                        workspace=workspace, start_date__gte=start_time_str, start_date__lte=stop_time_str)
                    condition &= ~Q(status='archived')

                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 770: Filter for {metric_name}: {condition}')

                    queryset = (
                        ShopTurboSubscriptions.objects
                        .filter(condition)
                        .annotate(**dynamic_annotation)
                        .values(*dynamic_values[1:])
                        .annotate(count=Count('x_axis'))
                        .order_by(*dynamic_values[1:])
                    )

                elif metric_name == 'revenue':
                    currency = ShopTurboOrders.objects.filter(
                        workspace=workspace, status='active').values_list('currency', flat=True).first()
                    # label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                    condition = Q(
                        workspace=workspace, start_date__gte=start_time_str, start_date__lte=stop_time_str)
                    condition &= ~Q(status='archived')

                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 1114: Filter for {metric_name}: {condition}')

                    queryset = (
                        ShopTurboSubscriptions.objects
                        .filter(condition)
                        .annotate(**dynamic_annotation)
                        .values(*dynamic_values[1:])
                        .annotate(
                            count=Sum(
                                Case(
                                    When(tax__isnull=True,
                                         then=F('total_price')),
                                    default=F('total_price') *
                                    (1 - F('tax_rate') / 100),
                                    output_field=FloatField()
                                )
                            )
                        )
                        .order_by(*dynamic_values[1:])
                    )
                    print(
                        f'=== report.py - 947: Query result for {metric_name} - {queryset}')

                elif metric_name == 'recurring_revenue':
                    condition = Q(
                        workspace=workspace, start_date__gte=start_time_str, start_date__lte=stop_time_str)
                    condition &= ~Q(status='archived')

                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 770: Filter for {metric_name}: {condition}')

                    queryset = (
                        ShopTurboSubscriptions.objects
                        .filter(condition)
                        .annotate(**dynamic_annotation)
                        .values(*dynamic_values[1:])
                        .annotate(
                            count=Sum(
                                Case(
                                    When(tax__isnull=True,
                                         then=F('total_price')),
                                    default=F('total_price') *
                                    (1 - F('tax_rate') / 100),
                                    output_field=FloatField()
                                )
                            )
                        )
                        .order_by('x_axis')
                    )

                if len(x_axis) <= 0:
                    for item in queryset:
                        label = item['x_axis']
                        if not item['x_axis']:
                            label = "他の" if lang == 'ja' else 'other'
                        if label not in x_axis:
                            x_axis.append(label)

                matrix = defaultdict(lambda: defaultdict(int))

                if panel.breakdown not in ['subscription_growth', 'subscription_revenue']:
                    for row in queryset:
                        xx = row['x_axis'] if not is_date else row['x_axis'].strftime(
                            time_format)
                        cnt = row['count']

                        xx = xx if xx else "他の" if lang == 'ja' else 'other'

                        if panel.breakdown:
                            bx = row['breakdown']
                            matrix[bx][xx] = cnt
                        else:
                            matrix[xx] = cnt

                if panel.breakdown:
                    if panel.breakdown in ['subscription_growth', 'subscription_revenue']:
                        breakdown = ['new', 'expansion',
                                     'contraction', 'churned']
                        result_counts = {}

                        group_customer = {}
                        matrix = defaultdict(lambda: defaultdict(int))
                        is_new = False
                        is_expansion = False
                        is_contraction = False
                        is_churned = False

                        for row in queryset:
                            customer = row['company_'] if row['company_'] else row['contact_']
                            customer = customer if customer else "other"

                            is_new = True

                            is_invoice = False
                            if any(isinstance(p, (int, float)) for p in row['invoice_total_price_']):
                                is_invoice = True

                            total_invoice = 0
                            for p in row['invoice_total_price_']:
                                if p is None:
                                    p = 0

                                total_invoice += p

                            if customer in group_customer:
                                last_inv = group_customer[customer]
                                if is_invoice and last_inv[-1] < total_invoice:
                                    is_contraction = True
                                elif is_invoice:
                                    is_expansion = True
                                else:
                                    total_invoice = last_inv[-1] * -1
                                    is_churned = True

                            xx = row['x_axis'] if not is_date else row['x_axis'].strftime(
                                time_format)
                            if is_expansion:
                                matrix['expansion'][xx] += total_invoice
                            elif is_contraction:
                                matrix['contraction'][xx] += total_invoice
                            elif is_churned:
                                matrix['churned'][xx] += total_invoice
                            elif is_new:
                                matrix['new'][xx] += total_invoice

                            if customer not in group_customer:
                                group_customer[customer] = []
                            group_customer[customer].append(total_invoice)

                        result_counts = {
                            bx: [matrix.get(bx, {}).get(xx, 0)
                                 for xx in x_axis]
                            for bx in breakdown
                        }

                        for bx in breakdown:
                            result_count = result_counts.get(bx, None)
                            if not result_count:
                                result_count = [0] * len(x_axis)

                            results.append(new_result(
                                bx, result_count, len(results)))
                    else:
                        breakdown = []
                        for row in queryset:
                            if row['breakdown'] not in breakdown:
                                breakdown.append(row['breakdown'])

                        result_counts = {
                            bx: [matrix.get(bx, {}).get(xx, 0)
                                 for xx in x_axis]
                            for bx in breakdown
                        }

                        for res in result_counts:
                            init = 0
                            for i, v in enumerate(result_counts[res]):
                                if v is None:
                                    v = 0
                                result_counts[res][i] = v + init
                                init = result_counts[res][i]

                        for bx in breakdown:
                            result_count = result_counts.get(bx, None)
                            if not result_count:
                                result_count = [0] * len(x_axis)

                            results.append(new_result(
                                bx, result_count, len(results)))
                else:
                    result_counts = [matrix.get(xx, 0) for xx in x_axis]
                    init = 0
                    for i, v in enumerate(result_counts):
                        if v is None:
                            v = 0
                        result_counts[i] = v + init
                        init = result_counts[i]

                    results.append(new_result(
                        'データ' if lang == 'ja' else 'Data', result_counts, len(results)))

            elif panel.data_source == 'invoices':
                start_time_str = start_time.strftime('%Y-%m-%d')
                stop_time_str = stop_time.strftime('%Y-%m-%d')
                dynamic_annotation = {}
                dynamic_values = ['count']
                is_date = False

                if panel.breakdown:
                    dynamic_values.append('breakdown')
                    if 'custom_field__' in panel.breakdown:
                        custom_field = panel.breakdown.replace(
                            "custom_field__", '')
                        cf_qs = (
                            InvoiceValueCustomField.objects
                            .filter(
                                invoices_id=OuterRef('pk'),
                                field_name_id=custom_field,
                            )
                            .values('value')[:1]
                        )

                        dynamic_annotation['breakdown'] = Subquery(
                            cf_qs,
                            output_field=CharField()
                        )
                    elif 'date' in panel.breakdown:
                        dynamic_annotation['breakdown'] = trunc_func(
                            panel.x_axis)
                    else:
                        dynamic_annotation['breakdown'] = F(panel.breakdown)

                if panel.x_axis:
                    dynamic_values.append('x_axis')
                    if panel.x_axis == 'timeseries':
                        is_date = True
                        dynamic_annotation['x_axis'] = trunc_func('start_date')
                    elif 'date' in panel.x_axis:
                        dynamic_annotation['x_axis'] = trunc_func(panel.x_axis)
                    elif 'custom_field__' in panel.x_axis:
                        custom_field = panel.x_axis.replace(
                            "custom_field__", '')
                        cf_qs = (
                            InvoiceValueCustomField.objects
                            .filter(
                                invoices_id=OuterRef('pk'),
                                field_name_id=custom_field,
                            )
                            .values('value')[:1]
                        )

                        dynamic_annotation['x_axis'] = Subquery(
                            cf_qs,
                            output_field=CharField()
                        )
                    else:
                        dynamic_annotation['x_axis'] = F(panel.x_axis)

                if metric_name == 'number_invoices':
                    condition = Q(
                        workspace=workspace, start_date__gte=start_time_str, start_date__lte=stop_time_str)
                    condition &= ~Q(usage_status='archived')

                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 770: Filter for {metric_name}: {condition}')

                    queryset = (
                        Invoice.objects
                        .filter(condition)
                        .annotate(**dynamic_annotation)
                        .values(*dynamic_values[1:])
                        .annotate(count=Count('x_axis'))
                        .order_by(*dynamic_values[1:])
                    )

                # Handle the recurring revenue breakdown by its own, the calculation is way too different
                elif metric_name == 'recurring_revenue_invoice' and panel.breakdown in ['subscription_growth', 'subscription_revenue']:
                    start_time_str = start_time.strftime('%Y-%m-%d')
                    stop_time_str = stop_time.strftime('%Y-%m-%d')

                    condition = Q(
                        workspace=workspace, start_date__gte=start_time_str, start_date__lte=stop_time_str)
                    condition &= ~Q(usage_status='archived')

                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 770: Filter for {metric_name}: {condition}')

                    queryset = (
                        Invoice.objects.filter(condition)
                        .annotate(**{
                            'contact_': F('contact'),
                            'company_': F('company'),
                            'date_period': trunc_func('start_date'),
                        })
                        .values(*['contact_', 'company_', 'date_period'])
                        .annotate(
                            count=Sum(
                                Case(
                                    When(tax_rate__isnull=True,
                                         then=F('total_price')),
                                    default=F('total_price') *
                                    (1 - F('tax_rate') / 100),
                                    output_field=FloatField()
                                )
                            )
                        )
                    )

                    # 1) Bucket your queryset rows by formatted date
                    map_invoice_by_date_and_customer = defaultdict(list)
                    for row in queryset:
                        # pick company if set, else contact, else "other"
                        customer = row.get('company_') or row.get(
                            'contact_') or "other"
                        # format the date period
                        customer = str(customer)
                        x_key = row['date_period'].strftime(time_format) if row['date_period'] else None
                        if x_key is None:
                            continue  # Skip rows with no date
                        # count is already your summed invoice total for that row
                        total_invoice = row.get('count') or 0

                        # append to a list (so you don't overwrite previous entries)
                        map_invoice_by_date_and_customer[x_key].append({
                            'customer': customer,
                            'invoice_total_price': total_invoice,
                        })

                    # Walk through calculation --- Subscription Growth ---
                    if panel.breakdown == 'subscription_growth':
                        # 2) Prepare your output containers
                        matrix = {
                            'new':        defaultdict(int),
                            'expansion':  defaultdict(int),
                            'contraction': defaultdict(int),
                            'churned':    defaultdict(int),
                        }
                        net_matrix = defaultdict(int)
                        # will hold last-period's total per customer
                        group_customer = {}

                        # 3) Walk through each period in your dynamic x_axis
                        for xx in x_axis:
                            # make sure every category has a zero baseline for this date
                            for cat in matrix:
                                matrix[cat].setdefault(xx, 0)

                            # the list of customer-totals for this date (maybe empty)
                            todays = map_invoice_by_date_and_customer.get(
                                xx, [])
                            # turn into a quick lookup: customer -> this period's total
                            curr_totals = {}
                            for d in todays:
                                if d['customer'] not in curr_totals:
                                    curr_totals[d['customer']] = 0

                                curr_totals[d['customer']
                                            ] += d['invoice_total_price']

                            # consider everyone we've ever seen plus any brand-new ones today
                            all_customers = set(
                                group_customer) | set(curr_totals)

                            for customer in all_customers:
                                prev = group_customer.get(customer, 0)
                                curr = curr_totals.get(customer, 0)

                                if prev == 0 and curr > 0:
                                    # no invoice last period -> New
                                    matrix['new'][xx] += curr

                                elif prev > 0 and curr == 0:
                                    # had invoice, now none -> Churned
                                    prev *= -1
                                    matrix['churned'][xx] += prev

                                elif curr > prev:
                                    # invoice increased -> Expansion
                                    matrix['expansion'][xx] += (curr - prev)

                                elif curr < prev:
                                    # invoice decreased -> Contraction
                                    matrix['contraction'][xx] += (
                                        (prev - curr) * -1)

                                # update for next iteration
                                group_customer[customer] = curr
                            net_matrix[xx] = matrix['new'][xx] + matrix['churned'][xx] + \
                                matrix['expansion'][xx] + \
                                matrix['contraction'][xx]
                        LABEL_MAP = {
                            'new': {
                                'en': 'New',
                                'ja': '新規',
                            },
                            'expansion': {
                                'en': 'Expansion',
                                'ja': '拡大',
                            },
                            'contraction': {
                                'en': 'Contraction',
                                'ja': '縮小',
                            },
                            'churned': {
                                'en': 'Churned',
                                'ja': '解約',
                            },
                        }

                        for bx in ['new', 'expansion', 'contraction', 'churned']:
                            result_dict = matrix[bx]
                            result_list = [result_dict[xx] for xx in x_axis]
                            trans = LABEL_MAP.get(bx, {})
                            title = trans.get(
                                'ja', bx) if lang == 'ja' else trans.get('en', bx)
                            idx_color = len(results)
                            if bx == 'new':
                                idx_color = 3
                            elif bx == 'expansion':
                                idx_color = 1
                            elif bx == 'contraction':
                                idx_color = 5
                            elif bx == 'churned':
                                idx_color = 0

                            results.append(new_result_detailed_growth(
                                title, result_list, idx_color))

                        net_matrix_list = [net_matrix[xx] for xx in x_axis]
                        net_result = new_result(
                            "ネット" if lang == 'ja' else "Net", net_matrix_list, 0)
                    # Walk through calculation --- Subscription Revenue ---
                    elif panel.breakdown == 'subscription_revenue':
                        # 2) Prepare your output containers
                        matrix = {
                            'new':      defaultdict(int),
                            'retain':   defaultdict(int),
                        }
                        net_matrix = defaultdict(int)

                        group_customer = {}

                        # 3) Walk through each period in your dynamic x_axis
                        for xx in x_axis:
                            # make sure every category has a zero baseline for this date
                            for cat in matrix:
                                matrix[cat].setdefault(xx, 0)

                            # the list of customer-totals for this date (maybe empty)
                            todays = map_invoice_by_date_and_customer.get(
                                xx, [])
                            # turn into a quick lookup: customer -> this period's total
                            curr_totals = {}
                            for d in todays:
                                if d['customer'] not in curr_totals:
                                    curr_totals[d['customer']] = 0

                                curr_totals[d['customer']
                                            ] += d['invoice_total_price']

                            # consider everyone we've ever seen plus any brand-new ones today
                            all_customers = set(
                                group_customer) | set(curr_totals)

                            for customer in all_customers:
                                prev = group_customer.get(customer, 0)
                                curr = curr_totals.get(customer, 0)

                                if prev == 0 and curr > 0:
                                    # no invoice last period -> New
                                    matrix['new'][xx] += curr

                                else:
                                    # invoice increased/decrease/similar -> Expansion/Churned/NoChanges
                                    matrix['retain'][xx] += curr

                                # update for next iteration
                                group_customer[customer] = curr

                            net_matrix[xx] = matrix['new'][xx] + \
                                matrix['retain'][xx]
                        LABEL_MAP = {
                            'new': {
                                'en': 'New',
                                'ja': '新規',
                            },
                            'retain': {
                                'en': 'Retain',
                                'ja': '継続',
                            },
                        }

                        for bx in ['retain', 'new']:
                            result_dict = matrix[bx]
                            result_list = [result_dict[xx] for xx in x_axis]
                            trans = LABEL_MAP.get(bx, {})
                            title = trans.get(
                                'ja', bx) if lang == 'ja' else trans.get('en', bx)
                            idx_color = len(results)
                            if bx == 'new':
                                idx_color = 3
                            elif bx == 'retain':
                                idx_color = 1

                            results.append(new_result_invoice_growth(
                                title, result_list, idx_color))

                        net_matrix_list = [net_matrix[xx] for xx in x_axis]
                        net_result = new_result(
                            "トータル" if lang == 'ja' else "Total", net_matrix_list, 0)
                elif metric_name == 'recurring_revenue_invoice':
                    label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                    currency = Invoice.objects.filter(
                        workspace=workspace, usage_status='active').values_list('currency', flat=True).first()
                    label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                    condition = rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                    where_clause, where_params = build_raw_filters(
                        Invoice, condition)

                    breakdown_values = ['no_breakdown']
                    if panel.breakdown:
                        panel.breakdown = panel.breakdown.replace(
                            "invoice__", "")
                        breakdown_field = panel.breakdown

                        if 'contact_custom_field_relations__value__' in breakdown_field:
                            id = breakdown_field.split("|")[1]
                            breakdown_values = (
                                ContactsValueCustomField.objects.filter(
                                    field_name_id=id,
                                    contact__workspace=workspace
                                )
                                .exclude(value__isnull=True)
                                .values_list('value', flat=True)
                                .distinct()
                            )
                            custom_field = ContactsNameCustomField.objects.get(
                                id=id)
                            if custom_field.type == 'choice':
                                labels = ast.literal_eval(
                                    custom_field.choice_value)
                                for l_ in labels:
                                    value_map[l_['value']] = l_['label']
                        elif 'company_custom_field_relations__value__' in breakdown_field:
                            id = breakdown_field.split("|")[1]
                            breakdown_values = (
                                CompanyValueCustomField.objects.filter(
                                    field_name_id=id,
                                    company__workspace=workspace
                                )
                                .exclude(value__isnull=True)
                                .values_list('value', flat=True)
                                .distinct()
                            )
                            custom_field = CompanyNameCustomField.objects.get(
                                id=id)
                            if custom_field.type == 'choice':
                                labels = ast.literal_eval(
                                    custom_field.choice_value)
                                for l_ in labels:
                                    value_map[l_['value']] = l_['label']
                        elif breakdown_field == 'line_item':
                            breakdown_values = (
                                InvoiceItem
                                .objects
                                .filter(invoice__workspace=workspace, invoice__usage_status='active')
                                .values_list('item_link__name', flat=True)
                                .distinct()
                            )
                        else:
                            breakdown_values = (
                                Invoice
                                .objects
                                .filter(workspace=workspace, usage_status='active')
                                .values_list(breakdown_field, flat=True)
                                .distinct()
                            )

                    if time_step == timedelta(days=1):
                        time_step_unit = 'day'
                    elif time_step == timedelta(weeks=1):
                        time_step_unit = 'week'
                    elif time_step == timedelta(days=30) or time_step == timedelta(days=31):
                        start_time_str = start_time_str[:8] + '01'
                        time_step_unit = 'month'
                        time_step = '1 month'
                    elif time_step == timedelta(hours=1):
                        time_step_unit = 'hour'

                    for breakdown in breakdown_values:
                        where_parameters = where_params
                        breakdown_field = None
                        join_sql = None
                        parent_table = None
                        field_name_id = None
                        if breakdown != 'no_breakdown':
                            breakdown_fields = panel.breakdown.split("__")
                            parent = "invoice"
                            if len(breakdown_fields) > 1:
                                if 'custom_field_relations' in breakdown_fields[0]:
                                    parent = breakdown_fields[0]
                                    field_name_id = breakdown_fields[2].replace(
                                        "|", "")
                                    breakdown_field = "value"
                                else:
                                    parent = breakdown_fields[0]
                                    breakdown_field = breakdown_fields[1]
                            else:
                                breakdown_field = panel.breakdown
                            join_clauses = {
                                'invoice': (
                                    "",
                                    (breakdown,),
                                    'i'
                                ),
                                'contact': (
                                    f"""
                                    LEFT JOIN data_contact AS c
                                        ON c.id   = i.contact_id
                                    """,
                                    (breakdown,),
                                    'c'
                                ),
                                'company': (
                                    f"""
                                    LEFT JOIN data_company AS co
                                        ON co.id   = i.company_id
                                    """,
                                    (breakdown,),
                                    'co'
                                ),
                                'contact_custom_field_relations': (
                                    f"""
                                    LEFT JOIN data_contactsvaluecustomfield AS cf
                                        ON cf.contact_id     = i.contact_id
                                        AND cf.field_name_id = '{field_name_id}'
                                    """,
                                    (breakdown,),
                                    'cf'
                                ),
                                'company_custom_field_relations': (
                                    f"""
                                    LEFT JOIN data_companyvaluecustomfield AS cf
                                        ON cf.company_id     = i.company_id
                                        AND cf.field_name_id = '{field_name_id}'
                                    """,
                                    (breakdown,),
                                    'cf'
                                ),
                                'line_item': (
                                    "",
                                    (),
                                    ''
                                )
                                # add more entries here as you introduce new breakdown types…
                            }

                            join_sql, join_params, parent_table = join_clauses[parent]
                            where_parameters += join_params

                        if panel.breakdown == 'line_item':
                            raw_query = f"""
                                SELECT DISTINCT
                                    dates.date,
                                    COALESCE(SUM(invoiceitem.total_price), 0) as total_price
                                FROM (
                                    SELECT generate_series(
                                        '{start_time_str}'::timestamp, 
                                        '{stop_time_str}'::timestamp, 
                                        '{time_step}'::interval
                                    ) AS date
                                ) AS dates
                                LEFT JOIN (
                                    SELECT 
                                        ii.total_price,
                                        date_trunc('{time_step_unit}', i.start_date) AS invoice_date
                                    FROM data_invoiceitem ii
                                    LEFT JOIN data_invoice i ON i.id = ii.invoice_id
                                    LEFT JOIN data_shopturboitems si ON si.id = ii.item_link_id
                                    {join_sql if join_sql else ""}
                                    WHERE i.usage_status = 'active'
                                    AND i.workspace_id = '{workspace.id}'
                                    {f"AND si.name = '{breakdown}'" if breakdown is not None else "AND si IS NULL"}
                                    {f"AND {where_clause}" if where_clause is not None else ""}
                                ) AS invoiceitem ON (
                                    invoiceitem.invoice_date = dates.date
                                )
                                GROUP BY dates.date
                                ORDER BY dates.date ASC;
                            """

                            if breakdown is None:
                                breakdown = "他の" if lang == 'ja' else 'Other'
                        else:
                            raw_query = f"""
                                SELECT DISTINCT
                                    dates.date,
                                    COALESCE(SUM(invoice.total_price), 0) as total_price
                                FROM (
                                    SELECT generate_series(
                                        '{start_time_str}'::timestamp, 
                                        '{stop_time_str}'::timestamp, 
                                        '{time_step}'::interval
                                    ) AS date
                                ) AS dates
                                LEFT JOIN (
                                    SELECT 
                                        i.total_price,
                                        date_trunc('{time_step_unit}', i.start_date) AS invoice_date
                                    FROM data_invoice i 
                                    {join_sql if join_sql else ""}
                                    WHERE i.usage_status = 'active'
                                    {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                                    AND i.workspace_id = '{workspace.id}'
                                    {f"AND {where_clause}" if where_clause is not None else ""}
                                ) AS invoice ON (
                                    invoice.invoice_date = dates.date
                                )
                                GROUP BY dates.date
                                ORDER BY dates.date ASC;
                            """

                        data = {item['display']: 0 for item in data_points}
                        with connection.cursor() as cursor:
                            cursor.execute(raw_query, list(where_parameters))
                            raw_results = cursor.fetchall()
                            print(
                                f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                            for raw_item in raw_results:
                                data[raw_item[0].strftime(
                                    time_format)] = int(raw_item[1])

                        results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else label, [
                            value for value in data.values()],  len(results)))
                        continue

                if len(x_axis) <= 0:
                    for item in queryset:
                        label = item['x_axis']
                        if not item['x_axis']:
                            label = "他の" if lang == 'ja' else 'other'
                        if label not in x_axis:
                            x_axis.append(label)

                matrix = defaultdict(lambda: defaultdict(int))
                for row in queryset:
                    xx = row['x_axis'] if not is_date else row['x_axis'].strftime(
                        "%Y-%m-%d")
                    cnt = row['count']

                    xx = xx if xx else "他の" if lang == 'ja' else 'other'

                    if panel.breakdown:
                        bx = row['breakdown']
                        matrix[bx][xx] = cnt
                    else:
                        matrix[xx] = cnt
                if panel.breakdown:
                    breakdown = []
                    for row in queryset:
                        if row['breakdown'] not in breakdown:
                            breakdown.append(row['breakdown'])

                    result_counts = {
                        bx: [matrix.get(bx, {}).get(xx, 0) for xx in x_axis]
                        for bx in breakdown
                    }

                    for bx in breakdown:
                        result_count = result_counts.get(bx, None)
                        if not result_count:
                            result_count = [0] * len(x_axis)

                        results.append(new_result(
                            bx, result_count, len(results)))
                else:
                    result_counts = [matrix.get(xx, 0) for xx in x_axis]
                    results.append(new_result(
                        'データ' if lang == 'ja' else 'Data', result_counts, len(results)))

            elif panel.data_source == 'inventory':
                if metric_name == 'inventory':
                    label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                    has_breakdown = True if panel.breakdown else False
                    inventory_data = {}
                    col_display = "inventory"
                    if has_breakdown:
                        if panel.breakdown == 'inventory_id':
                            obj, created = ObjectManager.objects.get_or_create(
                                workspace=workspace, page_group_type=TYPE_OBJECT_INVENTORY)
                            if created:
                                try:
                                    col_display = ','.join(
                                        DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_INVENTORY])
                                except:
                                    col_display = "inventory_id"
                                obj.column_display = col_display
                                obj.save()
                            else:
                                col_display = obj.column_display

                        elif panel.breakdown == 'item_id':
                            obj, created = ObjectManager.objects.get_or_create(
                                workspace=workspace, page_group_type=TYPE_OBJECT_ITEM)
                            if created:
                                try:
                                    col_display = ','.join(
                                        DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_ITEM])
                                except:
                                    col_display = 'item_id'
                                obj.column_display = col_display
                                obj.save()
                            else:
                                col_display = obj.column_display

                        elif panel.breakdown == 'warehouse_id':
                            col_display = ''

                        col_display = col_display.split(',')

                    condition = Q(workspace=workspace, transaction_date__gte=start_time_str,
                                  transaction_date__lte=stop_time_str)
                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'==== report.py - 1168: Filter for {metric_name}: {condition}')

                    queryset = (
                        InventoryTransaction.objects
                        .filter(condition)
                        .annotate(date=trunc_func('transaction_date'))
                        .values('date', 'inventory__id', 'inventory__item__id', 'inventory__warehouse__id', 'transaction_type')
                        .annotate(count=Sum('amount'))
                        .order_by('date')
                    )

                    print(
                        f'=== report.py - 985: Query result for {metric_name} - {queryset}')

                    init_data = {}
                    for item in queryset:
                        key = 'all'
                        if has_breakdown:
                            if panel.breakdown == 'inventory_id':
                                key = item['inventory__id']

                            elif panel.breakdown == 'item_id':
                                key = item['inventory__item__id']

                            elif panel.breakdown == 'warehouse_id':
                                key = item['inventory__warehouse__id']

                        if key not in inventory_data:
                            inventory_data[key] = {}

                        if item['date'].strftime(time_format) not in inventory_data[key]:
                            inventory_data[key][item['date'].strftime(time_format)] = {
                                'in': 0,
                                'out': 0
                            }

                        inventory_data[key][item['date'].strftime(
                            time_format)][item['transaction_type']] += item['count']

                    all_filter = rule_filter
                    if metric_filter:
                        all_filter &= filter_condition

                    initial_inventory = (
                        InventoryTransaction.objects
                        .filter(Q(transaction_date__lt=start_time_str, workspace=workspace) & all_filter)
                        .annotate(date=trunc_func('transaction_date'))
                        .values('inventory__id', 'inventory__item__id', 'inventory__warehouse__id', 'transaction_type')
                        .annotate(count=Sum('amount'))
                        .order_by('date')
                    )

                    for item in initial_inventory:
                        key = 'all'
                        if has_breakdown:
                            if panel.breakdown == 'inventory_id':
                                key = item['inventory__id']

                            elif panel.breakdown == 'item_id':
                                key = item['inventory__item__id']

                            elif panel.breakdown == 'warehouse_id':
                                key = item['inventory__warehouse__id']

                        if key not in init_data:
                            init_data[key] = 0

                        if item['transaction_type'] == 'in':
                            init_data[key] += item['count']
                        elif item['transaction_type'] == 'out':
                            init_data[key] -= item['count']

                    # Remove if there is no inventory_left
                    init_data = {k: v for k, v in init_data.items() if v != 0}

                    # Iterate through all keys in init_data and inventory_data
                    for key in set(init_data) | set(inventory_data):
                        data = {item['display']: 0 for item in data_points}
                        if key in inventory_data:
                            for item in inventory_data[key]:
                                data[item] = inventory_data[key][item]['in'] - \
                                    inventory_data[key][item]['out']

                        # Use cumulative count from init_data if available
                        cumulative_count = init_data.get(key, 0)

                        # Update data with cumulative count
                        for idx, _ in data.items():
                            data[idx] += cumulative_count
                            cumulative_count = data[idx]

                        label = '在庫' if lang == 'ja' else 'Inventory'
                        if panel.breakdown == 'inventory_id':
                            obj = ShopTurboInventory.objects.get(id=key)
                            label = get_object_display_based_columns(
                                TYPE_OBJECT_INVENTORY, obj, col_display, workspace.timezone, lang)
                        elif panel.breakdown == 'item_id':
                            obj = ShopTurboItems.objects.get(id=key)
                            label = get_object_display_based_columns(
                                TYPE_OBJECT_ITEM, obj, col_display, workspace.timezone, lang)
                        elif panel.breakdown == 'warehouse_id':
                            if key:
                                obj = InventoryWarehouse.objects.get(id=key)
                                label = obj.location
                            else:
                                label = "関連付けられていないロケーション" if lang == 'ja' else "No Associated Location"

                        results.append(new_result(
                            label, [value for value in data.values()], len(results)))

            elif panel.data_source == 'contacts':
                if metric_name == 'number_contacts':
                    label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                    condition = Q(
                        workspace=workspace, created_at__gte=start_time_str, created_at__lte=stop_time_str)
                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 1168: Filter for {metric_name}: {condition}')
                    queryset = (
                        Contact.objects
                        .filter(condition)
                        .annotate(date=trunc_func('created_at'))
                        .values('date')
                        .annotate(count=Count('id'))
                        .order_by('date')
                    )
                    print(
                        f'=== report.py - 985: Query result for {metric_name} - {queryset}')

                    data = {item['display']: 0 for item in data_points}
                    for item in queryset:
                        data[item['date'].strftime(
                            time_format)] = item['count']

                    # Metric type[1][0] is for shows the cumulative, using it to avoid using hard coded
                    if metric_type == METRIC_TYPE[1][0]:
                        all_filter = rule_filter
                        if metric_filter:
                            all_filter &= filter_condition

                        cumulative_count = Contact.objects.filter(
                            Q(created_at__lt=start_time_str) & all_filter).count()
                        for idx, _ in data.items():
                            data[idx] += cumulative_count
                            cumulative_count = data[idx]

                    results.append(new_result(
                        label, [value for value in data.values()], len(results)))

            elif panel.data_source == 'companies':
                if metric_name == 'number_companies':
                    label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                    condition = Q(
                        workspace=workspace, created_at__gte=start_time_str, created_at__lte=stop_time_str)
                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 1142: Filter for {metric_name}: {condition}')
                    queryset = (
                        Company.objects
                        .filter(condition)
                        .annotate(date=trunc_func('created_at'))
                        .values('date')
                        .annotate(count=Count('id'))
                        .order_by('date')
                    )
                    print(
                        f'=== report.py - 967: Query result for {metric_name} - {queryset}')

                    data = {item['display']: 0 for item in data_points}
                    for item in queryset:
                        data[item['date'].strftime(
                            time_format)] = item['count']

                    results.append(new_result(
                        label, [value for value in data.values()],  len(results)))
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(
                f'ERROR === report.py - 1491: Error in Query result for {metric_name} - {e}')

        # print(result)

    print(f'=== report.py - 2967: Data Source: {panel.data_source}')
    print(f'x-axis: {x_axis}')
    print(f'y-axis: {results}')
    chart = {
        'id': panel.id,
        'name': panel.name,
        'label': label,
        'x': x_axis,
        'net_result': net_result,
        'y': results,
        'config': chart_config,
        'group_by': group_by
    }
    return chart

