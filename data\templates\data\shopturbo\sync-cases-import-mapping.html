{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="border-0 card shadow-none rounded-0 w-100 h-100 bg-white">


    <div>
        <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
            {% if LANGUAGE_CODE == 'ja' %}
            マッピングテーブル
            {% else %}
            Mapping Table
            {% endif %}
        </span>
    
        <input hidden name="platform" value="{{platform}}">
    
        <table class="{% include "data/utility/table.html" %} px-5">
            <thead class="{% include "data/utility/table-header.html" %}">
                <tr>
                    <th class="min-w-50px">
                        <span>
                            {% if LANGUAGE_CODE == 'ja' %}
                            プラットフォームヘッダー
                            {% else %}
                            Platform Header
                            {% endif %}
                        </span>
                    </th>
                    {% if is_multi_object %}
                    <th class="min-w-50px">
                        <span>
                            {% if LANGUAGE_CODE == 'ja' %}
                            Sankaオブジェクト
                            {% else %}
                            Sanka Object
                            {% endif %}
                        </span>
                    </th>
                    {% endif %}
                    <th class="min-w-50px">
                        <span>
                            {% if LANGUAGE_CODE == 'ja' %}
                            Sankaプロパティ
                            {% else %}
                            Sanka Property
                            {% endif %}
                        </span>
                    </th>
                    <th class="min-w-50px text-center">
                        <span>
                            {% if LANGUAGE_CODE == 'ja' %}
                            除外
                            {% else %}
                            Skip
                            {% endif %}
                        </span>
                    </th>
                </tr>
            </thead>
            <tbody class="fs-6">
                {% for header in header_list %}
                    <tr>
                        <td>
                            <input name="order-file-column" type="hidden" value="{{header|parse_header_item}}"/>
                            {% if LANGUAGE_CODE == 'ja' %}
                                <input name="order-file-column-name" type="hidden" value="{{header|parse_header_item:'name_ja'}}"/>
                                {% if header|parse_header_item:'hubspot_default' == True %}{% translate_lang header|parse_header_item:'name_ja' LANGUAGE_CODE %}{% else %}{{header|parse_header_item:'name_ja'}}{% endif %}
                            {% else %}
                                <input name="order-file-column-name" type="hidden" value="{{header|parse_header_item:'name'}}"/>
                                {{header|parse_header_item:'name'}}
                            {% endif %}
                        </td>

                        {% if is_multi_object %}
                        <td>
                            <div>
                            {% if '|contact' in header.value %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}連絡先{% else %}Contact{% endif %}</span>
                            {% elif '|company' in header.value %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}企業{% else %}Company{% endif %}</span>
                            {% elif '|item' in header.value %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}商品{% else %}Item{% endif %}</span>
                            {% elif '|line_item' in header.value %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}商品項目{% else %}Line Item{% endif %}</span>
                            {% else %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}チケット{% else %}Ticket{% endif %}</span>
                            {% endif %}
                            </div>
                        </td>

                        <script>
                            function showSankaImportProperties(elm) {
                                const selectedValue = elm.value;
                                const propertySelector = elm.closest("tr").querySelector("[name='order-sanka-properties']");
                                let selectedHeader = elm.getAttribute("data-header-value"); // Get the preselected header
                                console.log(selectedHeader);

                                let options = "";

                                if (selectedValue === "case") {
                                    options = `
                                    <option value="create_new">{% if LANGUAGE_CODE == 'ja' %}新しい Sanka プロパティの作成{% else %}Create New Sanka Property{% endif %}</option>
                                    {% for column in platform_columns %}
                                        <option value="{{ column }}" ${selectedHeader === "{{ column }}" ? "selected" : ""}>
                                            {{ column|display_column_orders:request }}
                                        </option>
                                    {% endfor %}`;
                                } 
                                else if (selectedValue === "contact") {
                                    options = `
                                    <option value="create_new|contact">{% if LANGUAGE_CODE == 'ja' %}新しい Sanka プロパティの作成{% else %}Create New Sanka Property{% endif %}</option>
                                    {% for column in contacts_columns %}
                                        <option value="{{ column }}|contact" ${selectedHeader === "{{ column }}|contact" ? "selected" : ""}>
                                            {{ column|display_column_contacts:request }}
                                        </option>
                                    {% endfor %}`;
                                } 
                                else if (selectedValue === "company") {
                                    options = `
                                    <option value="create_new|company">{% if LANGUAGE_CODE == 'ja' %}新しい Sanka プロパティの作成{% else %}Create New Sanka Property{% endif %}</option>
                                    {% for column in company_columns %}
                                        <option value="{{ column }}|company" ${selectedHeader === "{{ column }}|company" ? "selected" : ""}>
                                            {{ column|display_column_company:request }}
                                        </option>
                                    {% endfor %}`;
                                }

                                propertySelector.innerHTML = options;
                            }
                        </script>
                        {% endif %}
    
                        <td>
    
                            <div name="order-sanka-properties-{{header|parse_header_item}}" class="">
                                <select name="order-sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this select2-this-order" 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    data-placeholder="ヘッダーの選択"
                                    {% else %}
                                    data-placeholder="Select Header"
                                    {% endif %}
                                    >  

                                    {% for column in platform_columns %}
                                        {% if column == 'create_new' %}
                                            <option value="create_new">{% if LANGUAGE_CODE == 'ja' %}新しい Sanka プロパティの作成{% else %}Create New Sanka Text Property{% endif %}</option>
                                        {% else %}
                                            <option {% if column == header|parse_header_item:'default'%} selected {%endif%} value='{{column}}'>
                                                {% if mapping_type == 'order' %}
                                                    {{column|display_column_orders:request}}
                                                {% elif mapping_type == 'case' %}
                                                    {{column|display_column_deals:request}}
                                                {% else %}
                                                    {{column|display_column_orders:request}}
                                                {% endif %}
                                            </option>  
                                        {% endif %}
                                    {% endfor %} 
                                    
                                    {% if header.is_sub_property %}
                                        {% for key, values in sub_property_columns.items %}
                                            {% for column in values %}
                                                <option {% if column|add:'|'|add:key|add:'|sub_property' == header|parse_header_item:'default' %} selected {% endif %} value="{{column}}|{{key}}|sub_property">
                                                    {{key}} | {{ column }}
                                                </option>
                                            {% endfor %}
                                        {% endfor %}
                                    {% endif %}
                                    
                                    {% if '|contact' in header.value or '|company' in header.value %}
                                        {% for column in contacts_columns %}
                                            <option {% if column|add:'|contact' == header|parse_header_item:'default'%} selected {%endif%} value='{{column}}|contact'>
                                                {{column|display_column_contacts:request}}
                                            </option>
                                        {% endfor %}

                                        {% for column in company_columns %}
                                            <option {% if column|add:'|company' == header|parse_header_item:'default'%} selected {%endif%} value='{{column}}|company'>
                                                {{column|display_column_company:request}}
                                            </option>
                                        {% endfor %}
                                    {% endif %}
    
                                </select>
                            </div>
                            
    
                        </td>
                        <td class="text-center">
                            <input type="checkbox" {% if header|parse_header_item:'skip' == 'True'%} checked {%endif%}class="form-check-input" onchange="handleCheckbox(this)">
                            <input type="hidden" name="order-ignore" value="{{header|parse_header_item:'skip'}}">
                        </td>
                    </tr>
                {% endfor %} 
            </tbody> 
        </table>
        <button type="button" class="btn btn-light-primary" name="save_cases_mapping"
                hx-post="{% host_url 'shopturbo' host 'app' %}"
                hx-swap="outerHTML"
                hx-indicator="#mappingLoadingIndicator"
                id="saveMappingButton"
                hx-on="htmx:afterRequest:contactCheckerChannelChanged();htmx:afterRequest:showSaveMessage()"">
            {% if LANGUAGE_CODE == 'ja'%}
            マッピングの保存
            {% else %}
            Save Mapping
            {% endif %}
        </button>

        <div id="mappingLoadingIndicator" class="d-none">
            {% if LANGUAGE_CODE == 'ja' %}保存中...{% else %}Saving...{% endif %}
        </div>

        <script>
            function contactCheckerChannelChanged() {
                if (document.getElementById('order-checker')) {
                    htmx.trigger('#order-checker', 'channelChanged');
                } else {
                    htmx.trigger('#contact-checker', 'channelChanged');
                }
            }

            function showSaveMessage() {
                const saveMessage = document.getElementById('saveMappingMessage');
                if (saveMessage) {
                    saveMessage.classList.remove('d-none'); // Show the message
                    setTimeout(() => {
                        saveMessage.classList.add('d-none'); // Hide it after 5 seconds
                    }, 5000);
                }
            }
        </script>
    
    </div>

    {% if is_multi_object %}
    <script>
        $(document).ready(function() {
            document.querySelectorAll("[name='order-sanka-objects']").forEach(select => {
                showSankaImportProperties(select);
            });
        });
    </script>
    {% endif %}
    
    <script>
        $(document).ready(function() {
            $('.select2-this').select2();
            $('.select2-this-order').each(function() {
                let selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    if (selectElement.value == 'item_id'){
                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                    }
                }
            });
    
            var string = `{% if LANGUAGE_CODE == 'ja'%}メッセージプロパティの作成{% else %}Create Case Property{% endif %}`
            var import_as = "case"
    
            var previousValues = {};
            $(`.select2-this-order`).on('select2:opening', function(e) {
                // Store the current value before change
                var selectElement = $(this).closest('select').get(0);
                if (selectElement) {
                    previousValues[selectElement.id] = selectElement.value;
                }
            }).on('select2:select', function (e) {
                var selectElement = $(this).closest('select').get(0);
                let oldValue = previousValues[selectElement.id] || '';
                let currentValue = selectElement.value;
    
                if (['order', 'inventory'].includes(import_as)) {
                    if (selectElement) {
                        // Trigger if changing TO item_id OR FROM item_id
                        if ((currentValue === 'item_id') || (oldValue === 'item_id')) {
                            selectElement.dispatchEvent(new Event('htmx-change'));
                        }
                    }
                } else {
                    // Keep your existing else condition for other cases
                    selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                }
    
                // Update the previous value after change
                previousValues[selectElement.id] = currentValue;
            });
            checkboxes = Array.from(document.querySelectorAll("input[type='checkbox']")); // Convert NodeList to Array
            lastChecked = null;

            checkboxes.forEach((checkbox) => {
                checkbox.addEventListener("click", handleShiftClick);
            });
    
        });
        
        function handleShiftClick(e) {
            console.log('shift')
            // Ensure the Shift key is held and a checkbox is clicked
            if (e.shiftKey && lastChecked) {
                let start = checkboxes.indexOf(lastChecked);
                let end = checkboxes.indexOf(this);

                // Find all checkboxes in range and toggle their checked state
                const range = checkboxes.slice(Math.min(start, end), Math.max(start, end) + 1);
                range.forEach((checkbox) => {
                    checkbox.checked = lastChecked.checked;
                    var parent = checkbox.parentNode;
                    // Find the next input element within the parent
                    var nextInput = parent.querySelector('input[type="hidden"]');
                    if (!checkbox.checked) {
                        nextInput.value = "False";
                    } else {
                        nextInput.value = "True";
                    }
                });
            }

            lastChecked = this; // Update the last checked checkbox
        }
        
        
        function handleCheckbox(checkbox) {
            // Get the parent element of the checkbox
            var parent = checkbox.parentNode;
            // Find the next input element within the parent
            var nextInput = parent.querySelector('input[type="hidden"]');
            if (!checkbox.checked) {
                nextInput.value = "False";
            } else {
                nextInput.value = "True";
            }
        }
    
        function showSankaProperties(elm){
            var header = elm.id.split("-");
            header = header[header.length - 1]
            
            var sanka_properties_contacts_id = "order-sanka-properties-"+header
            var sanka_properties_company_id = "order-sanka-properties-company-"+header
            var sanka_properties_contacts_elm = document.getElementById(sanka_properties_contacts_id)
            var sanka_properties_company_elm = document.getElementById(sanka_properties_company_id)
            
            if (elm.value == "contact"){
                if (sanka_properties_contacts_elm && sanka_properties_company_elm){
                    sanka_properties_contacts_elm.classList.remove("d-none")
                    sanka_properties_company_elm.classList.add("d-none")
                }
            }
            else if (elm.value == "company"){
                if (sanka_properties_contacts_elm && sanka_properties_company_elm){
                    sanka_properties_contacts_elm.classList.add("d-none")
                    sanka_properties_company_elm.classList.remove("d-none")
                }
            }
        }
    </script>
    
</div>
