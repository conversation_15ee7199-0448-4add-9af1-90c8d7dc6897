{% extends 'base.html' %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}
{% block content %}

<style>
    @media (max-width: 576px) { /* Adjust this value based on when you want the max-width to be removed */
        .responsive-col {
            max-width: none !important;
        }
    }
</style>

<div class="w-100">
    <div class="mt-5 mb-15 gx-5 px-lg-10 px-5">
        <div class="d-flex justify-content-between align-items-center">
            <div class="">
                <a href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app'  %}" class="fw-bolder fs-4 mb-0">
                    {% if LANGUAGE_CODE == 'ja' %}
                    すべてのダッシュボード
                    {% else %}
                    All Dashboards
                    {% endif %}
                </a>
                <div class="fw-bold fs-1">
                    {{report.name}}
                </div>
            </div>
            <div class="">
                <button class="ms-2 align-items-center d-flex btn btn-dark btn-md py-1 rounded-1 create-report-button" 
                    hx-get="{% host_url 'report_form' host 'app'  %}" 
                    hx-vals='{"type": "edit", "report_id": "{{report.id}}", "object_type": "{{object_type}}"}'
                    hx-trigger="click" 
                    hx-target="#report-wizard" 
                    hx-swap="innerHTML">
                    <span class="svg-icon svg-icon-4">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M2 4.63158C2 3.1782 3.1782 2 4.63158 2H13.47C14.0155 2 14.278 2.66919 13.8778 3.04006L12.4556 4.35821C11.9009 4.87228 11.1726 5.15789 10.4163 5.15789H7.1579C6.05333 5.15789 5.15789 6.05333 5.15789 7.1579V16.8421C5.15789 17.9467 6.05333 18.8421 7.1579 18.8421H16.8421C17.9467 18.8421 18.8421 17.9467 18.8421 16.8421V13.7518C18.8421 12.927 19.1817 12.1387 19.7809 11.572L20.9878 10.4308C21.3703 10.0691 22 10.3403 22 10.8668V19.3684C22 20.8218 20.8218 22 19.3684 22H4.63158C3.1782 22 2 20.8218 2 19.3684V4.63158Z" fill="currentColor"/>
                            <path d="M10.9256 11.1882C10.5351 10.7977 10.5351 10.1645 10.9256 9.77397L18.0669 2.6327C18.8479 1.85165 20.1143 1.85165 20.8953 2.6327L21.3665 3.10391C22.1476 3.88496 22.1476 5.15129 21.3665 5.93234L14.2252 13.0736C13.8347 13.4641 13.2016 13.4641 12.811 13.0736L10.9256 11.1882Z" fill="currentColor"/>
                            <path d="M8.82343 12.0064L8.08852 14.3348C7.8655 15.0414 8.46151 15.7366 9.19388 15.6242L11.8974 15.2092C12.4642 15.1222 12.6916 14.4278 12.2861 14.0223L9.98595 11.7221C9.61452 11.3507 8.98154 11.5055 8.82343 12.0064Z" fill="currentColor"/>
                        </svg>
                    </span>
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        編集
                        {% else %}
                        Edit
                        {% endif %}
    
                    </span>
                </button>
            </div>
        </div>
        <div class="row d-flex w-100 mx-auto" hx-get="{% host_url 'panels_list_view' report_id=report.id host 'app' %}?page=1" hx-trigger="load">
        </div>
    </div>
</div>

<div id="create-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
data-kt-drawer-toggle=".create-wizard-button"
data-kt-drawer-width="{'default':'90%','lg':'70%'}">
{% include 'data/partials/create-report-drawer.html' %}
</div>

<div id="profilewizard" class="" 
    data-kt-drawer="true" 
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle="#profile_wizard_button" 
    data-kt-drawer-close="#profilewizard_close"
    data-kt-drawer-width={'default':'90%','md':'600px'}>
    <div class="card border-0 shadow-none rounded-0 w-100 h-100 hover-scroll-overlay-y">
        <div class="card-header" id="kt_help_header">
            <h5 class="{% include "data/utility/card-header.html" %}">
                {% if LANGUAGE_CODE == 'ja'%}
                プロフィール
                {% else %}
                Profile
                {% endif %}

            </h5>
            <div class="card-toolbar">
                <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5"
                    data-kt-drawer-dismiss="true">
                    <span class="svg-icon svg-icon-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                transform="rotate(-45 6 17.3137)" fill="black"></rect>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                                fill="black"></rect>
                        </svg>
                    </span>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="manage-profile" class="mb-10">
            </div>
        </div>
    </div>
</div>


{% comment %} Settings {% endcomment %}
<div id="create-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
data-kt-drawer-toggle=".create-settings-button"
data-kt-drawer-width="{'default':'90%','lg':'70%'}"
>
    <div id="manage-settings-drawer" class="w-100"></div>
</div>


<style>
    .reports_loads{
        display:none;
    }
    .htmx-request .reports_loads{
        display:inline;
    }
    .htmx-request.reports_loads{
        display:inline;
    } 
</style>

{% endblock content %}