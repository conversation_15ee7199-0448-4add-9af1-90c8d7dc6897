"""
GraphQL queries for Shopify Order operations.

This module contains all GraphQL queries related to fetching order data,
including regular orders, draft orders, and customer-related information.
"""

# Main orders query with all required fields for import functionality
ORDERS_QUERY = """
query GetOrders($first: Int!, $after: String, $query: String) {
    orders(first: $first, after: $after, query: $query) {
        pageInfo {
            hasNextPage
            endCursor
        }
        edges {
            node {
                id
                legacyResourceId
                name
                number
                currencyCode
                createdAt
                processedAt
                taxesIncluded
                subtotalPriceSet {
                    shopMoney {
                        amount
                        currencyCode
                    }
                }
                totalPriceSet {
                    shopMoney {
                        amount
                        currencyCode
                    }
                }
                currentTotalTaxSet {
                    shopMoney {
                        amount
                        currencyCode
                    }
                }
                displayFinancialStatus
                displayFulfillmentStatus
                returnStatus
                email
                customerLocale
                customer {
                    id
                    legacyResourceId
                    firstName
                    lastName
                    email
                    phone
                    addresses {
                        id
                        firstName
                        lastName
                        company
                        address1
                        address2
                        city
                        province
                        country
                        zip
                        phone
                    }
                }
                lineItems(first: 50) {
                    edges {
                        node {
                            id
                            name
                            quantity
                            currentQuantity
                            originalUnitPriceSet {
                                shopMoney {
                                    amount
                                    currencyCode
                                }
                            }
                            variant {
                                id
                                legacyResourceId
                                sku
                                title
                                product {
                                    id
                                    legacyResourceId
                                    title
                                    handle
                                    productType
                                    vendor
                                }
                            }
                            product {
                                id
                                legacyResourceId
                                title
                                handle
                                productType
                                vendor
                            }
                            customAttributes {
                                key
                                value
                            }
                        }
                    }
                }
                shippingAddress {
                    firstName
                    lastName
                    company
                    address1
                    address2
                    city
                    province
                    country
                    zip
                    phone
                }
                shippingLine {
                    id
                    title
                    code
                    originalPriceSet {
                        shopMoney {
                            amount
                            currencyCode
                        }
                    }
                    taxLines {
                        rate
                        title
                        priceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                    }
                }
                customAttributes {
                    key
                    value
                }
                tags
            }
        }
    }
}
"""

# Draft orders query with all required fields for import functionality
DRAFT_ORDERS_QUERY = """
query GetDraftOrders($first: Int!, $after: String, $query: String) {
    draftOrders(first: $first, after: $after, query: $query) {
        pageInfo {
            hasNextPage
            endCursor
        }
        edges {
            node {
                id
                legacyResourceId
                name
                currencyCode
                createdAt
                updatedAt
                status
                email
                taxesIncluded
                taxExempt
                subtotalPriceSet {
                    shopMoney {
                        amount
                        currencyCode
                    }
                }
                totalPriceSet {
                    shopMoney {
                        amount
                        currencyCode
                    }
                }
                totalTaxSet {
                    shopMoney {
                        amount
                        currencyCode
                    }
                }
                customer {
                    id
                    legacyResourceId
                    firstName
                    lastName
                    email
                    phone
                    addresses {
                        id
                        firstName
                        lastName
                        company
                        address1
                        address2
                        city
                        province
                        country
                        zip
                        phone
                    }
                }
                lineItems(first: 50) {
                    edges {
                        node {
                            id
                            name
                            quantity
                            originalUnitPriceSet {
                                shopMoney {
                                    amount
                                    currencyCode
                                }
                            }
                            variant {
                                id
                                legacyResourceId
                                sku
                                title
                                product {
                                    id
                                    legacyResourceId
                                    title
                                    handle
                                    productType
                                    vendor
                                }
                            }
                            product {
                                id
                                legacyResourceId
                                title
                                handle
                                productType
                                vendor
                            }
                            customAttributes {
                                key
                                value
                            }
                        }
                    }
                }
                billingAddress {
                    firstName
                    lastName
                    company
                    address1
                    address2
                    city
                    province
                    country
                    zip
                    phone
                }
                shippingAddress {
                    firstName
                    lastName
                    company
                    address1
                    address2
                    city
                    province
                    country
                    zip
                    phone
                }
                shippingLine {
                    id
                    title
                    code
                    originalPriceSet {
                        shopMoney {
                            amount
                            currencyCode
                        }
                    }
                    taxLines {
                        rate
                        title
                        priceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                    }
                }
                customAttributes {
                    key
                    value
                }
                tags
            }
        }
    }
}
"""

# Combined orders and draft orders query - lightweight version for counting
ORDERS_COUNT_QUERY = """
query GetOrdersCount($query: String) {
    orders(first: 1, query: $query) {
        pageInfo {
            hasNextPage
        }
    }
    draftOrders(first: 1, query: $query) {
        pageInfo {
            hasNextPage
        }
    }
}
"""

# Get specific order by ID
ORDER_BY_ID_QUERY = """
query GetOrderById($id: ID!) {
    order(id: $id) {
        id
        legacyResourceId
        name
        currencyCode
        createdAt
        processedAt
        taxesIncluded
        subtotalPriceSet {
            shopMoney {
                amount
                currencyCode
            }
        }
        totalPriceSet {
            shopMoney {
                amount
                currencyCode
            }
        }
        currentTotalTaxSet {
            shopMoney {
                amount
                currencyCode
            }
        }
        displayFinancialStatus
        displayFulfillmentStatus
        returnStatus
        email
        customerLocale
        customer {
            id
            legacyResourceId
            firstName
            lastName
            email
            phone
            addresses(first: 10) {
                edges {
                    node {
                        id
                        firstName
                        lastName
                        company
                        address1
                        address2
                        city
                        province
                        country
                        zip
                        phone
                    }
                }
            }
        }
        lineItems(first: 50) {
            edges {
                node {
                    id
                    name
                    quantity
                    currentQuantity
                    originalUnitPriceSet {
                        shopMoney {
                            amount
                            currencyCode
                        }
                    }
                    variant {
                        id
                        legacyResourceId
                        sku
                        title
                        product {
                            id
                            legacyResourceId
                            title
                            handle
                            productType
                            vendor
                        }
                    }
                    product {
                        id
                        legacyResourceId
                        title
                        handle
                        productType
                        vendor
                    }
                    customAttributes {
                        key
                        value
                    }
                }
            }
        }
        shippingAddress {
            firstName
            lastName
            company
            address1
            address2
            city
            province
            country
            zip
            phone
        }
        shippingLine {
            id
            title
            code
            originalPriceSet {
                shopMoney {
                    amount
                    currencyCode
                }
            }
            taxLines {
                rate
                title
                priceSet {
                    shopMoney {
                        amount
                        currencyCode
                    }
                }
            }
        }
        customAttributes {
            key
            value
        }
        note2
        tags
    }
}
"""

# Get specific draft order by ID
DRAFT_ORDER_BY_ID_QUERY = """
query GetDraftOrderById($id: ID!) {
    draftOrder(id: $id) {
        id
        legacyResourceId
        name
        currencyCode
        createdAt
        updatedAt
        status
        email
        taxesIncluded
        taxExempt
        subtotalPriceSet {
            shopMoney {
                amount
                currencyCode
            }
        }
        totalPriceSet {
            shopMoney {
                amount
                currencyCode
            }
        }
        totalTaxSet {
            shopMoney {
                amount
                currencyCode
            }
        }
        customer {
            id
            legacyResourceId
            firstName
            lastName
            email
            phone
            addresses(first: 10) {
                edges {
                    node {
                        id
                        firstName
                        lastName
                        company
                        address1
                        address2
                        city
                        province
                        country
                        zip
                        phone
                    }
                }
            }
        }
        lineItems(first: 50) {
            edges {
                node {
                    id
                    name
                    quantity
                    originalUnitPriceSet {
                        shopMoney {
                            amount
                            currencyCode
                        }
                    }
                    variant {
                        id
                        legacyResourceId
                        sku
                        title
                        product {
                            id
                            legacyResourceId
                            title
                            handle
                            productType
                            vendor
                        }
                    }
                    product {
                        id
                        legacyResourceId
                        title
                        handle
                        productType
                        vendor
                    }
                    customAttributes {
                        key
                        value
                    }
                }
            }
        }
        billingAddress {
            firstName
            lastName
            company
            address1
            address2
            city
            province
            country
            zip
            phone
        }
        shippingAddress {
            firstName
            lastName
            company
            address1
            address2
            city
            province
            country
            zip
            phone
        }
        shippingLine {
            id
            title
            code
            originalPriceSet {
                shopMoney {
                    amount
                    currencyCode
                }
            }
            taxLines {
                rate
                title
                priceSet {
                    shopMoney {
                        amount
                        currencyCode
                    }
                }
            }
        }
        customAttributes {
            key
            value
        }
        note2
        tags
    }
}
"""

# Get Discount Order
DISCOUNT_ORDER_QUERY = """
query GetOrderDiscounts($id: ID!) {
    order(id: $id) {
        id
        discountApplications(first: 5) {
            edges {
                node {
                    allocationMethod
                    targetType
                    value {
                        __typename
                        ... on MoneyV2 {
                            amount
                            currencyCode
                        }
                        ... on PricingPercentageValue {
                            percentage
                        }
                    }
                }
            }
        }
    }
}
"""
