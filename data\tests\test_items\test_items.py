from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User
from django.test.utils import setup_test_environment
from django.contrib.sessions.middleware import SessionMiddleware
from unittest.mock import MagicMock, patch

from data.items import (
    shopturbo_items, 
    create_items,
    shopturbo_item_detail,
    shopturbo_column_detail,
    shopturbo_item_group_row,
    shopturbo_cascade_dropdown_parent,
    manage_items,
)
from data.models import (
    Workspace, 
    Verification, 
    View, 
    ViewFilter, 
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsValueCustomField,
    Module,
    AppSetting
)

# Django setup is handled by the test runner


class TestShopturboItems(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create workspace
        self.workspace = Workspace.objects.create(name="Test Workspace")
        
        # Create verification for user
        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            verified=True,
            language='en'
        )
        
        # Create app setting
        self.app_setting = AppSetting.objects.create(
            workspace=self.workspace,
            app_target='shopturbo',
            search_setting_item='name'
        )
        
        # Create view
        self.view = View.objects.create(
            workspace=self.workspace,
            title="Test View",
            target='item'
        )
        
        # Create view filter
        self.view_filter = ViewFilter.objects.create(
            view=self.view,
            view_type='list',
            pagination=20
        )
        
        # Create request factory
        self.factory = RequestFactory()
        
    def _setup_session(self, request):
        """Helper method to set up session for request"""
        middleware = SessionMiddleware(get_response=None)
        middleware.process_request(request)
        request.session['_language'] = 'en'
        request.session.save()
    
    @patch('data.items.get_workspace')
    def test_shopturbo_items_get_no_workspace(self, mock_get_workspace):
        """Test shopturbo_items GET request with no workspace"""
        # Mock get_workspace to return None
        mock_get_workspace.return_value = None
        
        # Create request
        request = self.factory.get('/modules/shopturbo/items/')
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view
        response = shopturbo_items(request)
        
        # Check that we're redirected to the start page
        self.assertEqual(response.status_code, 302)
        # Use assertRedirects or check the Location header instead of accessing url directly
        self.assertIn('start', response['Location'])
    
    @patch('data.items.get_workspace')
    @patch('data.items.get_permission')
    def test_shopturbo_items_get_hide_permission(self, mock_get_permission, mock_get_workspace):
        """Test shopturbo_items GET request with 'hide' permission"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace

        # Mock get_permission to return 'hide'
        mock_get_permission.return_value = 'hide'

        # Create request
        request = self.factory.get('/modules/shopturbo/items/')
        request.user = self.user
        # Set up session
        self._setup_session(request)

        # Call the view
        response = shopturbo_items(request)

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('permission', response.context)
        self.assertEqual(response.context['permission'], 'hide')

    @patch('data.items.get_workspace')
    @patch('data.items.get_permission')
    def test_shopturbo_items_group_view_all_shopturbo_items_defined(self, mock_get_permission, mock_get_workspace):
        """Test shopturbo_items GET request with group view to ensure all_shopturbo_items is defined"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace

        # Mock get_permission to return 'read'
        mock_get_permission.return_value = 'read'

        # Set view filter to group type
        self.view_filter.view_type = 'group'
        self.view_filter.save()

        # Create request
        request = self.factory.get('/modules/shopturbo/items/')
        request.user = self.user
        # Set up session
        self._setup_session(request)

        # Call the view
        response = shopturbo_items(request)

        # Check response
        self.assertEqual(response.status_code, 200)
        # Verify that all_shopturbo_items is in the context and is defined
        self.assertIn('all_shopturbo_items', response.context)
        # Should be a list (empty or with items)
        self.assertIsInstance(response.context['all_shopturbo_items'], list)
    
    # Depreciated  
    # @patch('data.items.get_workspace')
    # @patch('data.items.trigger_bg_job')
    # def test_shopturbo_items_post_download(self, mock_trigger_bg_job, mock_get_workspace):
    #     """Test shopturbo_items POST request with 'download' action"""
    #     # Mock get_workspace to return the workspace
    #     mock_get_workspace.return_value = self.workspace
        
    #     # Mock trigger_bg_job to return a response with status_code 202
    #     mock_response = MagicMock()
    #     mock_response.status_code = 202
    #     mock_trigger_bg_job.return_value = mock_response
        
    #     # Create POST request with download action
    #     post_data = {
    #         'download': 'csv',
    #         'column': 'name,description',
    #         'view_id': str(self.view.id),
    #         'encoded_format': 'utf-8',
    #         'filter_type': ['name'],
    #         'filter_options': ['contains'],
    #         'filter_value': ['test']
    #     }
    #     request = self.factory.post('/modules/shopturbo/items/', post_data)
    #     request.user = self.user
    #     # Set up session
    #     self._setup_session(request)
    #     request.META = {'HTTP_REFERER': '/modules/shopturbo/items/'}
        
    #     # Call the view
    #     response = shopturbo_items(request)
        
    #     # Check that we're redirected back to the referrer
    #     self.assertEqual(response.status_code, 302)
    #     # Use assertRedirects or check the Location header instead of accessing url directly
    #     self.assertEqual(response['Location'], '/modules/shopturbo/items/')
        
    #     # Check that trigger_bg_job was called with the correct parameters
    #     mock_trigger_bg_job.assert_called_once()
        
    @patch('data.items.get_workspace')
    def test_shopturbo_items_post_bulk_delete_items(self, mock_get_workspace):
        """Test shopturbo_items POST request with 'bulk_delete_items' action"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Create test items
        item1 = ShopTurboItems.objects.create(
            workspace=self.workspace,
            name="Test Item 1",
            status="active"
        )
        item2 = ShopTurboItems.objects.create(
            workspace=self.workspace,
            name="Test Item 2",
            status="active"
        )
        
        # Create POST request with bulk_delete_items action
        post_data = {
            'bulk_delete_items': 'true',
            'checkbox': [str(item1.id), str(item2.id)]
        }
        request = self.factory.post('/modules/shopturbo/items/', post_data)
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view with mocked sync_usage function
        with patch('data.items.sync_usage') as mock_sync_usage:
            response = shopturbo_items(request)
        
        # Refresh items from database
        item1.refresh_from_db()
        item2.refresh_from_db()
        
        # Check that items were archived
        self.assertEqual(item1.status, 'archived')
        self.assertEqual(item2.status, 'archived')
        
        # Check that sync_usage was called
        mock_sync_usage.assert_called()
        
    @patch('data.items.get_workspace')
    @patch('data.items.has_quota')
    def test_shopturbo_items_post_bulk_restore_items(self, mock_has_quota, mock_get_workspace):
        """Test shopturbo_items POST request with 'bulk_restore_items' action"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Mock has_quota to return True
        mock_has_quota.return_value = True
        
        # Create test items with archived status
        item1 = ShopTurboItems.objects.create(
            workspace=self.workspace,
            name="Test Item 1",
            status="archived"
        )
        item2 = ShopTurboItems.objects.create(
            workspace=self.workspace,
            name="Test Item 2",
            status="archived"
        )
        
        # Create POST request with bulk_restore_items action
        post_data = {
            'bulk_restore_items': 'true',
            'checkbox': [str(item1.id), str(item2.id)]
        }
        request = self.factory.post('/modules/shopturbo/items/', post_data)
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view with mocked functions
        with patch('data.items.sync_usage') as mock_sync_usage, \
             patch('utils.meter.get_workspace_available_storage') as mock_get_storage:
            # Mock get_workspace_available_storage to return a positive number
            mock_get_storage.return_value = 10
            response = shopturbo_items(request)
        
        # Refresh items from database
        item1.refresh_from_db()
        item2.refresh_from_db()
        
        # Check that items were restored to active status
        self.assertEqual(item1.status, 'active')
        self.assertEqual(item2.status, 'active')
        
        # Check that sync_usage was called
        mock_sync_usage.assert_called()


class TestCreateItems(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create workspace
        self.workspace = Workspace.objects.create(name="Test Workspace")
        
        # Create verification for user
        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            is_verified=True,
            language='en'
        )
        
        # Create module
        self.module = Module.objects.create(
            workspace=self.workspace,
            name="Test Module",
            slug="test-module",
            object_values='["item"]'
        )
        
        # Create request factory
        self.factory = RequestFactory()
        
    def _setup_session(self, request):
        """Helper method to set up session for request"""
        middleware = SessionMiddleware(get_response=None)
        middleware.process_request(request)
        request.session['_language'] = 'en'
        request.session.save()
    
    @patch('data.items.get_workspace')
    @patch('data.items.get_permission')
    @patch('data.items.get_page_object')
    def test_create_items_get(self, mock_get_page_object, mock_get_permission, mock_get_workspace):
        """Test create_items GET request"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Mock get_permission to return 'edit'
        mock_get_permission.return_value = 'edit'
        
        # Mock get_page_object to return a dictionary with required keys
        mock_get_page_object.return_value = {
            'base_model': ShopTurboItems,
            'custom_value_model': ShopTurboItemsValueCustomField,
            'custom_value_relation': 'shopturbo_item_custom_field_relations',
            'custom_value_file_model': None,
            'custom_value_file_relation': None,
            'additional_filter_fields': [],
            'id_field': 'id'
        }
        
        # Create request
        request = self.factory.get('/modules/shopturbo/items/create/')
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view
        response = create_items(request)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('page_title', response.context)
        self.assertEqual(response.context['page_title'], 'Create Item')
        
    @patch('data.items.get_workspace')
    @patch('data.items.get_permission')
    @patch('data.items.get_page_object')
    def test_create_items_post_with_valid_data(self, mock_get_page_object, mock_get_permission, mock_get_workspace):
        """Test create_items POST request with valid data"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Mock get_permission to return 'edit'
        mock_get_permission.return_value = 'edit'
        
        # Mock get_page_object to return a dictionary with required keys
        mock_get_page_object.return_value = {
            'base_model': ShopTurboItems,
            'custom_value_model': ShopTurboItemsValueCustomField,
            'custom_value_relation': 'shopturbo_item_custom_field_relations',
            'custom_value_file_model': None,
            'custom_value_file_relation': None,
            'additional_filter_fields': [],
            'id_field': 'id'
        }
        
        # Create POST request with valid data
        post_data = {
            'name': 'New Test Item',
            'description': 'Test description',
            'module': self.module.slug
        }
        request = self.factory.post('/modules/shopturbo/items/create/', post_data)
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view with mocked save_custom_property
        with patch('data.items.save_custom_property') as mock_save_custom_property:
            response = create_items(request)
        
        # Check that a new item was created
        self.assertTrue(ShopTurboItems.objects.filter(name='New Test Item').exists())
        
        # Check that we're redirected to the item detail page
        self.assertEqual(response.status_code, 302)
        self.assertTrue('items/detail' in response.url)


class TestShopturboItemDetail(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create workspace
        self.workspace = Workspace.objects.create(name="Test Workspace")
        
        # Create verification for user
        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            is_verified=True,
            language='en'
        )
        
        # Create test item
        self.item = ShopTurboItems.objects.create(
            workspace=self.workspace,
            name="Test Item",
            status="active"
        )
        
        # Create request factory
        self.factory = RequestFactory()
        
    def _setup_session(self, request):
        """Helper method to set up session for request"""
        middleware = SessionMiddleware(get_response=None)
        middleware.process_request(request)
        request.session['_language'] = 'en'
        request.session.save()
    
    @patch('data.items.get_workspace')
    def test_shopturbo_item_detail_with_valid_item_id(self, mock_get_workspace):
        """Test shopturbo_item_detail with valid item_id"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Create request with valid item_id
        request = self.factory.get(f'/modules/shopturbo/items/detail/{self.item.id}/')
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view
        response = shopturbo_item_detail(request, self.item.id)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('item', response.context)
        self.assertEqual(response.context['item'].id, self.item.id)
    
    @patch('data.items.get_workspace')
    def test_shopturbo_item_detail_with_invalid_item_id(self, mock_get_workspace):
        """Test shopturbo_item_detail with invalid item_id"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Create request with invalid item_id
        invalid_id = '00000000-0000-0000-0000-000000000000'
        request = self.factory.get(f'/modules/shopturbo/items/detail/{invalid_id}/')
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view
        response = shopturbo_item_detail(request, invalid_id)
        
        # Check that we're redirected to the items page
        self.assertEqual(response.status_code, 302)
        # Check that the response is a redirect
        self.assertTrue(response.has_header('Location'))


class TestShopturboColumnDetail(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create workspace
        self.workspace = Workspace.objects.create(name="Test Workspace")
        
        # Create verification for user
        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            is_verified=True,
            language='en'
        )
        
        # Create test item
        self.item = ShopTurboItems.objects.create(
            workspace=self.workspace,
            name="Test Item",
            status="active"
        )
        
        # Create custom field
        self.custom_field = ShopTurboItemsNameCustomField.objects.create(
            workspace=self.workspace,
            name="Test Field",
            type="text"
        )
        
        # Create request factory
        self.factory = RequestFactory()
        
    def _setup_session(self, request):
        """Helper method to set up session for request"""
        middleware = SessionMiddleware(get_response=None)
        middleware.process_request(request)
        request.session['_language'] = 'en'
        request.session.save()
    
    @patch('data.items.get_workspace')
    def test_shopturbo_column_detail_with_valid_data(self, mock_get_workspace):
        """Test shopturbo_column_detail with valid data"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Create request with valid data
        request = self.factory.get(f'/modules/shopturbo/items/column/{self.custom_field.id}/')
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view
        response = shopturbo_column_detail(request)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('field_name', response.context)
        self.assertEqual(response.context['field_name'].id, self.custom_field.id)


class TestShopturboItemGroupRow(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create workspace
        self.workspace = Workspace.objects.create(name="Test Workspace")
        
        # Create verification for user
        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            is_verified=True,
            language='en'
        )
        
        # Create test item
        self.item = ShopTurboItems.objects.create(
            workspace=self.workspace,
            name="Test Item",
            status="active"
        )
        
        # Create request factory
        self.factory = RequestFactory()
        
    def _setup_session(self, request):
        """Helper method to set up session for request"""
        middleware = SessionMiddleware(get_response=None)
        middleware.process_request(request)
        request.session['_language'] = 'en'
        request.session.save()
    
    @patch('data.items.get_workspace')
    def test_shopturbo_item_group_row_with_valid_data(self, mock_get_workspace):
        """Test shopturbo_item_group_row with valid data"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Create request with valid data
        request = self.factory.get(f'/modules/shopturbo/items/group-row/{self.item.id}/')
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view
        response = shopturbo_item_group_row(request)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('item', response.context)
        self.assertEqual(response.context['item'].id, self.item.id)


class TestShopturboCascadeDropdownParent(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create workspace
        self.workspace = Workspace.objects.create(name="Test Workspace")
        
        # Create verification for user
        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            is_verified=True,
            language='en'
        )
        
        # Create custom field
        self.custom_field = ShopTurboItemsNameCustomField.objects.create(
            workspace=self.workspace,
            name="Test Field",
            type="cascade_dropdown"
        )
        
        # Create request factory
        self.factory = RequestFactory()
        
    def _setup_session(self, request):
        """Helper method to set up session for request"""
        middleware = SessionMiddleware(get_response=None)
        middleware.process_request(request)
        request.session['_language'] = 'en'
        request.session.save()
    
    @patch('data.items.get_workspace')
    def test_shopturbo_cascade_dropdown_parent_with_valid_data(self, mock_get_workspace):
        """Test shopturbo_cascade_dropdown_parent with valid data"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Create request with valid data
        request = self.factory.get(f'/modules/shopturbo/items/cascade-dropdown/{self.custom_field.id}/')
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view
        response = shopturbo_cascade_dropdown_parent(request, self.custom_field.id)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('field_name', response.context)
        self.assertEqual(response.context['field_name'].id, self.custom_field.id)


class TestManageItems(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create workspace
        self.workspace = Workspace.objects.create(name="Test Workspace")
        
        # Create verification for user
        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            is_verified=True,
            language='en'
        )
        
        # Create module
        self.module = Module.objects.create(
            workspace=self.workspace,
            name="Test Module",
            slug="test-module",
            object_values='["item"]'
        )
        
        # Create request factory
        self.factory = RequestFactory()
        
    def _setup_session(self, request):
        """Helper method to set up session for request"""
        middleware = SessionMiddleware(get_response=None)
        middleware.process_request(request)
        request.session['_language'] = 'en'
        request.session.save()
    
    @patch('data.items.get_workspace')
    def test_manage_items_with_valid_request(self, mock_get_workspace):
        """Test manage_items with valid request"""
        # Mock get_workspace to return the workspace
        mock_get_workspace.return_value = self.workspace
        
        # Create request
        request = self.factory.get('/modules/shopturbo/items/manage/')
        request.user = self.user
        # Set up session
        self._setup_session(request)
        
        # Call the view
        # Add module_id parameter if required by the function
        response = manage_items(request, self.module.id)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIn('page_title', response.context)
        self.assertEqual(response.context['page_title'], 'Manage Items')


class TestExportItems(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create workspace
        self.workspace = Workspace.objects.create(name="Test Workspace")
        
        # Create verification for user
        self.verification = Verification.objects.create(
            user=self.user,
            workspace=self.workspace,
            is_verified=True,
            language='en'
        )
        
        # Create module
        self.module = Module.objects.create(
            workspace=self.workspace,
            name="Test Module",
            slug="test-module",
            object_values='["item"]'
        )
        
        # Create request factory
        self.factory = RequestFactory()
        
    def _setup_session(self, request):
        """Helper method to set up session for request"""
        middleware = SessionMiddleware(get_response=None)
        middleware.process_request(request)
        request.session['_language'] = 'en'
        request.session.save()
    
