FROM p16475locjapaneastsubeff9663d33.azurecr.io/sanka/main/sanka-python:0.6.0

WORKDIR /app
ADD . /app/

# PIP DEFAULT TIMEOUT
ENV PIP_DEFAULT_TIMEOUT=100

# Update Dependencies and Clean Up
RUN apt-get update \
    && apt-get install -y --no-install-recommends poppler-utils libfontconfig fonts-noto-cjk fonts-noto-color-emoji \
    && apt-get clean autoclean && apt-get autoremove --yes && rm -rf /var/lib/{apt,dpkg,cache,log}/

RUN pip install --upgrade setuptools pip && \
    pip install --no-cache-dir --prefix=/usr/local -r requirements.txt && \
    pip install --no-cache-dir --prefix=/usr/local --no-deps playwright==1.48.0


# Copy ENV VAR
COPY .env /app/.env

# Run Django CollectStatic
RUN python manage.py collectstatic --no-input

# Remove ENV VAR
RUN rm /app/.env

# Expose ports
EXPOSE 8000

# Set entrypoint to uvicorn
ENTRYPOINT ["uvicorn", "sanka.asgi:application", "--host", "0.0.0.0", "--port", "8000"]