{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% if source_integration %}
    {% apply_language_from_hubspot hs_language as LANGUAGE_CODE %}
{% endif %}
<script>
    var parentDiv_{{object_type}} = document.currentScript.closest('div');
</script>
<div class="{% include "data/utility/drawer-height.html" %}">
    <div class="mb-2 p-5 px-10 h-100 scroll-y border-gray-300 {% if not hide_associated_data %} border-end col-md-8 {% endif %}"
        {% if hide_associated_data %}
        style="width: 100%"
        {% endif %}
        >
        
        <form id="get-commerce-input-form" onsubmit="return validateForm(event)" method="POST" action="{% if obj.id %}{% host_url post_obj_link id=obj.id host 'app' %}{% else %}{% host_url post_obj_link host 'app' %}{% endif %}" enctype="multipart/form-data">
            {% csrf_token %}
            <input type="hidden" name="module" value="{{module.slug}}">
            <input type="hidden" name="object_type" value="{{object_type}}">
            {% if source_url%}
                <input type="hidden" name="source_url" value="{{source_url}}">
            {% endif %}
            <!-- association related -->
            <input hidden name="type" value="{{type}}">
            <input hidden name="source" value="{{source}}">
            <input hidden name="source_object_id" value="{{source_object_id}}">

            <input hidden name="integration_redirect_url" value="{{integration_redirect_url}}">
            <input hidden name="workspace_id" value="{{workspace_id}}">
            <input hidden name="channel_id" value="{{channel_id}}">
            <input hidden name="userId" value="{{userId}}">
            <input hidden name="source_integration" value="{{source_integration}}">
            <input hidden name="hs_language" value="{{hs_language}}">
            <input hidden name="associatedObjectId" value="{{associatedObjectId}}">


            {% comment %} <div class="billing_form_section">         {% endcomment %}

                {% if obj.id %}
                    <div class="mb-10">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span>
                                ID  {% if object_type == constant.TYPE_OBJECT_ESTIMATE %} | {% if LANGUAGE_CODE == 'ja'%}バージョン{% else %}Version{% endif %}: {% if obj.version %} {{obj.version}} {% endif %} {% endif %}
                            </span>
                        </label>
                        <span class="mt-5 fw-bolder fs-4">
                            {{obj_id}}
                        </span>
                    </div>
                {% endif %}

                {% if obj.usage_status == 'archived' %}
                    <div class="card-body mt-0 mb-4">
                        <div class="fv-rowd-flex flex-column">
                            <h3>
                                {% if LANGUAGE_CODE == 'ja'%}
                                このレコードはアーカイブされました。このレコードを利用するためにまず有効化してください。
                                {% else %}
                                This record was archived. Please activate it first to see and use this record.
                                {% endif %}
                            </h3>
                            <div class="border-0">
                                {% if permission|check_permission:'archive' %}
                                <input hidden id="submit_action" name="submit_action" value="activate">
                                <input type="hidden" name="view_id" {% if view_id %} value="{{view_id}}" {% endif %} />
                                <button onclick="changeValue(this,'activate')" type="submit" name="activate_{{object_type}}"  class="btn btn-success">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    有効化
                                    {% else %}
                                    Activate
                                    {% endif %}
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="pe-none user-select-none" hx-get="{% url 'load_drawer_orders' %}" hx-vals='{"drawer_type":"orders", "section":"blurry" }' hx-trigger="load" hx-swap="innerHTML"></div>
                {% else %}

                {% for property in properties.list_all %}
                    {% with args=property|add:'|'|add:object_type %}
                        {% with column_display=args|get_column_display:request %}
                            
                            {% if property|in_list:"customers,currency,line_item,start_date,due_date,status,send_from,notes" and column_display.name %}
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span {% if property|in_list:"start_date,line_item,currency,status" %}class="required"{% endif %}>
                                        {{column_display.name}}
                                    </span>
                                    {% if 'customer' in property %}
                                        {% if not obj.id and not source_integration and not type and source != 'company' and source != 'contact' %}
                                            <span class="svg-icon svg-icon-primary svg-icon-2x me-2">
                                                <a  id="addon-add-contact"
                                                    class="fs-7 my-0 menu-link border-transparent bg-transparent w-100 cursor-pointer d-inline"
                                                    hx-get="{% url 'addon_contact_company' %}"
                                                    hx-vals = '{"create_type":"addon-billing","type_addon":"create-contacts","object_type":"{{object_type}}"}'
                                                    hx-target="#create-new-drawer-content"
                                                    hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';document.getElementById('create-new-drawer-content').innerHTML = '';"
                                                    hx-indicator=".loading-drawer-spinner,.create_new_form"
                                                    >
                                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <polygon points="0 0 24 0 24 24 0 24"/>
                                                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                                        </g>
                                                    </svg>
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    連絡先
                                                    {% else %}
                                                    Contact
                                                    {% endif %}
                                                </a>
                                            </span>

                                            <span class="svg-icon svg-icon-primary svg-icon-2x">
                                                <a  id="addon-add-company"
                                                    class="fs-7 my-0 menu-link border-transparent bg-transparent w-100 cursor-pointer d-inline"
                                                    hx-get="{% url 'addon_contact_company' %}"
                                                    hx-vals = '{"create_type":"addon-billing","type_addon":"create-company","object_type":"{{object_type}}"}'
                                                    hx-target="#create-new-drawer-content"
                                                    hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';document.getElementById('create-new-drawer-content').innerHTML = '';"
                                                    hx-indicator=".loading-drawer-spinner,.create_new_form"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <polygon points="0 0 24 0 24 24 0 24"/>
                                                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                                        </g>
                                                    </svg>
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    企業
                                                    {% else %}
                                                    Company
                                                    {% endif %}
                                                </a>
                                            </span>
                                        {% endif %}
                                    {% endif %}
                                </label>
                            {% endif %}

                            {% if 'customers' in property %}
                                <div class="fv-rowd-flex flex-column mb-8">
                                    {% include 'data/common/dynamic-customer-select.html' with contact_preselected=contact_preselected company_preselected=company_preselected contact_selected=obj.contact company_selected=obj.company %}
                                </div> 
                            
                            {% comment %} #association label {% endcomment %}
                            {% elif property in association_label_list and property != 'customer' %}
                                {% if object_type|in_list:"estimates,receipts" %}
                                <div hx-get="{% url 'load_association_label_form' %}" 
                                    hx-vals='{"obj_id":"{{obj.id}}", "property":"{{property}}", "page_group_type":"{{object_type}}"}' 
                                    hx-trigger="load" hx-swap="outerHTML">
                                </div>
                                {% endif %}
                                
                            {% elif property == 'currency' %}
                                <div class="mb-5" id="currency-selector">
                                    <div class="w-100">
                                        <div class="mb-5">
                                            <select class="form-select h-40px select2-this currency" id="currency" name="currency" {% if obj.id %} data-placeholder="{{obj.currency}}" {% endif %} >
                                                {% if workspace.currencies %}
                                                <optgroup
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    label="デフォルト通貨"
                                                    {% else %}
                                                    label="Default Currency"
                                                    {% endif %}
                                                >
                                                    {% for currency in workspace.currencies|string_list_to_list %}
                                                        {% if forloop.counter == 1 %}
                                                            <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                        {% elif obj.currency == currency %}
                                                            <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                        {% else %}
                                                            <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                                        {% endif %}
                                                    {% endfor %}

                                                </optgroup>
                                                {% endif %}

                                                <optgroup
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    label="すべての通貨"
                                                    {% else %}
                                                    label="All Currency"
                                                    {% endif %}
                                                >
                                                {% include 'data/partials/money-currency.html' with obj=obj.currency %}
                                                </optgroup>
                                            </select>
                                        </div>
                                    </div>
                                    <script>
                                        $('.currency').on('select2:select', function (e) {
                                            var selectedOption = e.params.data.id;
                                            // Clear the previous selection if no matching option is found
                                            var selectElement = $(this).closest('select').get(0);
                                            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                            $('#discount_option').val(null).trigger('change');

                                            // Add the selected option dynamically
                                            var newOption = new Option('abs', 'selectedOption', true, true);
                                            $('#discount_option').append(newOption).trigger('change');
                                        });
                                    </script>
                                </div>
                            {% elif property == 'owner' %}
                                <div class="owner-form-{{object_type}} mb-5">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="min-w-100px">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            所有者
                                            {% else %}
                                            Owner
                                            {% endif %}
                                        </span>
                                    </label>
                                    <select data-allow-clear='true' id="owner-input-{{object_type}}" class="h-40px form-select form-select-solid border bg-white select2-this" data-control="select2" name="owner" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}所有者{% else %}Owner{% endif %}">
                                        <option></option>
                                        {% if obj.id and obj.owner and obj.owner.user %}
                                            <option value="{{obj.owner.user.username}}" selected>
                                                {{obj.owner.user.first_name}} - {{obj.owner.user.email}}
                                            </option>
                                        {% endif %}
                                        {% for member in permission|get_scope_from_permission:'edit'|get_users_by_scope:user %}
                                            {% if not obj.owner or member != obj.owner.user %}
                                            <option value="{{member.username}}">
                                                {{member.first_name}} - {{member.email}}
                                            </option>
                                            {% endif %}
                                        {% endfor %}
                                    </select>
            
                                    <script>
                                        $(parentDiv_{{object_type}}).ready(function () {    
                                            $(parentDiv_{{object_type}}.querySelector('#owner-input-{{object_type}}')).select2();
                                        });
                                    </script>
                                </div>
                            {% elif property == 'line_item' %}
                                {% if object_type == 'receipts' %}
                                    <input hidden id="{{object_type}}-entry-type-hidden" name="commerce_entry_type" value='{% if obj.id %}{{obj.entry_type}}{% else %}manual{% endif %}'/>
                                    <div class="mb-6 form-check form-switch form-check-custom form-check-solid {% if object_type != 'receipts'%}d-none{% endif %}">
                                        <input id="{{object_type}}-entry-type" name="entry_type" class="form-check-input" type="checkbox" value="manual"
                                            {% comment %} onchange="toggle_entry()" {% endcomment %}
                                            {% if obj.id %}
                                                hx-get="{% host_url manage_obj_link obj.id host 'app' %}"
                                                hx-vals = '{"set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                            {% else %}
                                                hx-get="{% url object_type|to_drawer_type|add:'_drawer' %}"
                                                hx-vals='{"drawer_type":"{{object_type|to_drawer_type}}","module":"{{menu_key}}"}'
                                            {% endif %}
                                                hx-trigger="load, change"
                                                hx-include="#{{object_type}}-entry-type-hidden,#{{object_type}}-entry-type"
                                                hx-target="#{{object_type}}-add-item-section"
                                                hx-indicator=".{{object_type}}-entry-indicator,#{{object_type}}-add-item-section"


                                            {% if not obj.id %}
                                                checked
                                            {% else %}
                                                {% if 'manual' in obj.entry_type %}
                                                    checked
                                                {% endif %}
                                            {% endif %}
                                        >
                                        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="{{object_type}}-entry-type">
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            クイックエントリー
                                            {% else %}
                                            Quick Entry
                                            {% endif %}
                                            <style>
                                                .{{object_type}}-entry-indicator{
                                                    display:none;
                                                }
                                                .htmx-request .{{object_type}}-entry-indicator{
                                                    display:inline-block;
                                                }
                                                .htmx-request.{{object_type}}-entry-indicator{
                                                    display:inline-block;
                                                }
                                                #{{object_type}}-add-item-section{
                                                    display:block;
                                                }
                                                .htmx-request #{{object_type}}-add-item-section{
                                                    display:none;
                                                }
                                                .htmx-request#{{object_type}}-add-item-section{
                                                    display:none;
                                                }
                                            </style>
                                            <span class="ms-5 spinner-border spinner-border-sm text-primary {{object_type}}-entry-indicator" style="" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </span>
                                        </label>

                                        <script>
                                            function toggle_entry() {
                                                var this_ = parentDiv_{{object_type}}.querySelector('#{{object_type}}-entry-type');
                                                var quickEntrySection = parentDiv_{{object_type}}.querySelector('#{{object_type}}_line_item_quick_entry');
                                                var dynamicEntrySection = parentDiv_{{object_type}}.querySelector('#{{object_type}}_line_item_dynamic_entry');
                                                var taxOptionSection = parentDiv_{{object_type}}.querySelector('.{{object_type}}_tax_option');
                                                var tax_section = parentDiv_{{object_type}}.querySelector('#{{object_type}}_tax_input_section');

                                                if (this_.checked) {
                                                    if (quickEntrySection){
                                                    quickEntrySection.classList.remove('d-none');}
                                                    $(".currency_item").prop('disabled', true);
                                                    $(".currency_manual").prop('disabled', false);
                                                    if(taxOptionSection){
                                                    taxOptionSection.classList.remove('d-none');}
                                                    if(dynamicEntrySection){
                                                    dynamicEntrySection.classList.add('d-none');}
                                                    tax_show();
                                                } else {
                                                    tax_show();
                                                    $(".currency_manual").prop('disabled', true);
                                                    $(".currency_item").prop('disabled', false);
                                                    if (quickEntrySection){
                                                    quickEntrySection.classList.add('d-none');}
                                                    if(taxOptionSection){
                                                    taxOptionSection.classList.add('d-none');}
                                                    if(dynamicEntrySection){
                                                    dynamicEntrySection.classList.remove('d-none');}
                                                    if(tax_section){
                                                    tax_section.classList.remove('d-none');}
                                                }
                                            };
                                        </script>
                                    </div>
                                {% else %}
                                    <div class="d-flex align-items-center justify-content-center"
                                    {% if obj.id %}
                                        hx-get="{% host_url manage_obj_link obj.id host 'app' %}"
                                        hx-vals = '{"commerce_entry_type":"item","set_id": "{{set_id}}","module":"{{module}}", "view_id":"{{view_id}}"}'
                                    {% else %}
                                        hx-get="{% url object_type|to_drawer_type|add:'_drawer' %}"
                                        hx-vals='{"commerce_entry_type":"item","drawer_type":"{{object_type|to_drawer_type}}","module":"{{menu_key}}","set_id": "{{view.form.id|to_str}}"}'
                                    {% endif %}
                                        hx-trigger="load"
                                        hx-target="#{{object_type}}-add-item-section"
                                        hx-indicator=".{{object_type}}-entry-indicator,#{{object_type}}-add-item-section">
                                    <style>
                                        .{{object_type}}-entry-indicator{
                                            display:none;
                                        }
                                        .htmx-request .{{object_type}}-entry-indicator{
                                            display:inline-block;
                                        }
                                        .htmx-request.{{object_type}}-entry-indicator{
                                            display:inline-block;
                                        }
                                        #{{object_type}}-add-item-section{
                                            display:block;
                                        }
                                        .htmx-request #{{object_type}}-add-item-section{
                                            display:none;
                                        }
                                        .htmx-request#{{object_type}}-add-item-section{
                                            display:none;
                                        }
                                    </style>
                                    <span class=" spinner-border spinner-border-sm text-primary {{object_type}}-entry-indicator" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </span>
                                    </div>
                                {% endif %}

                                <div id="{{object_type}}-add-item-section">
                                </div>
                            {% elif property == 'start_date' %}
                                <div class="mb-5">
                                    <div class="">
                                        <input autocomplete="off" id="start-date" required name="start-date" {% if obj.id %} {% if obj.start_date %} value="{{obj.start_date}}" {% endif %} {% else %} value="" {% endif %}
                                            class="form-control dateinput form-control form_date"
                                            placeholder="{% if LANGUAGE_CODE == 'ja'%}日付を選択します{% else %}Select a date{% endif %}">
                                    </div>
                                </div>
                            {% elif property == 'due_date' %}
                                <div class="mb-5">
                                    <div class="">
                                        <input autocomplete="off" id="due-date" name="freq-due" {% if obj.id %} {% if obj.due_date %} value="{{obj.due_date}}" {% endif %}  {% else %} value="" {% endif %}
                                            class="form-control dateinput form-control form_due_date"
                                            placeholder="{% if LANGUAGE_CODE == 'ja'%}日付を選択します{% else %}Select a date{% endif %}">
                                    </div>
                                </div>
                            {% elif property == 'status' %}
                                <div class="mb-5">
                                    <select class="form-control h-40px select2-this" name="status" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}ステータスを選択{% else %}Select Status{% endif %}">
                                        {% if 'status'|get_custom_property_object:obj %}
                                            {% with value_map_label='status'|get_custom_property_object:obj|get_attr:'value'|string_list_to_list %}
                                                {% for value, label in value_map_label.items %}
                                                    <option value="{{value}}" {% if obj.status == value %}selected{% endif %}>
                                                        {{label}}
                                                    </option>
                                                {% endfor %}
                                            {% endwith %}
                                        {% else %}
                                            {% with status=obj|get_default_status_display:property %}
                                                {% for key, value in  status.items %}
                                                    <option value="{{key}}" {% if obj.status ==  key %} selected {% endif %}>
                                                        {% if LANGUAGE_CODE == 'ja' %}
                                                            {{value.ja}}
                                                        {% else %}
                                                            {{value.en}}
                                                        {% endif %}
                                                    </option>
                                                {% endfor %}
                                            {% endwith %}
                                        {% endif %}
                                    </select>
                                </div>
                            {% elif property == 'send_from' %}
                                <div class="mb-5">
                                    <textarea
                                        placeholder="Sanka, Inc."

                                        name="send_from"

                                        class="rounded form-control w-100 autosize-this"
                                        style="resize: none; overflow: hidden;">{% if obj.id %}{% if obj.send_from  %}{{obj.send_from}}{% endif %}{% else %}{% if value_app_setting_send_from %}{{value_app_setting_send_from}}{% endif %}{% endif %}</textarea>
                                </div>
                            {% elif property == 'notes' %}
                                <div class="mb-5">
                                    <div class="notes-container">
                                        <textarea
                                            wrap="off"
                                            placeholder="{% if LANGUAGE_CODE == 'ja'%}銀行口座など{% else %}Bank account, etc.{% endif %}"
                                            class="rounded form-control w-100 autosize-this"
                                            style="resize: none; overflow: hidden;"
                                            name="notes">{% if obj.id %}{% if obj.notes %}{{obj.notes}}{% endif %}{% else %}{% if value_app_setting_note %}{{value_app_setting_note}}{% endif %}{% endif %}</textarea>
                                    </div>
                                </div>
                            {% else %}
                                <div class="mt-5">
                                    {% with CustomFieldName=NameCustomFieldMap|get_attr:property %}
                                        {% if CustomFieldName %}
                                            <div class="fv-rowd-flex flex-column mb-8">
                                                <div class="mb-4">
                                                    <span class="{% include 'data/utility/form-label.html' %} {% if CustomFieldName.required_field %} required {% endif%}">
                                                        {{CustomFieldName.name }}
                                                    </span>
                                                    {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=CustomFieldName obj=obj object_type=object_type form='get-commerce-input-form' %}
                                                </div>
                                            </div>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                            {% endif %}
                        
                        {% endwith %}
                    {% endwith %}
                {% endfor %}

                {% if obj.id %}
                    {% search_billing_channel_objects request obj object_type as billing_platforms %}
                    {% with billing_platforms=billing_platforms %}
                        {% if billing_platforms %}
                            {% for billing_platform in billing_platforms %}
                                <div class="d-flex align-items-center fs-5 fw-bold">
                                    <span class="{% include "data/utility/form-label.html" %}">
                                        {{billing_platform.channel.name}}
                                    </span>
                                </div>
                                <div class="d-flex flex-column mb-5 fv-row">
                                    <input type="text" disabled class="rounded form-control w-100"
                                    value="{{billing_platform.platform_id}}"
                                    />
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                {% endif %}




                <div class="my-5 border-gray-300 border-bottom-dashed">
                </div>

                <div class="mb-5 d-flex flex-row h-40px">
                    {% if permission|check_permission:'edit' %}
                    <button type="submit" onclick="changeValue(this,'submit')" class="flex-column-fluid btn btn-dark h-40px submitbtn submit-version">
                        <span class="spinner-border spinner-border-sm text-secondary d-none save-indicator" style="position:relative; right:6px" role="status">
                        </span>
                        {% if obj.id %}
                            {% if LANGUAGE_CODE == 'ja'%}
                                更新を保存
                            {% else %}
                                Save Updates
                            {% endif %}
                        {% else %}
                            {% if LANGUAGE_CODE == 'ja'%}
                                {{page_title}}レコードを作成
                            {% else %}
                                Create {{page_title}} Record
                            {% endif %}
                        {% endif %}
                    </button>
                    {% endif %}

                    {% if obj.id %}
                        <div class="ms-2 me-2">
                            <div class="btn-group w-100 min-w-250px" role="group">
                                <a target="_blank" name="downloadPDF" href="{% host_url pdf_download_link id=obj.id host 'app' %}" class="submitbtn flex-column-fluid btn btn-primary">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                        ダウンロード (PDF)
                                    {% else %}
                                        Download (PDF)
                                    {% endif %}
                                </a>
                                <button id="btnGroupDrop1" type="button" class="btn btn-light-primary dropdown-toggle p-2" data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234CAF50" width="18px" height="18px"><path d="M0 0h24v24H0z" fill="none"/><path d="M6.3 9.3L12 15l5.7-5.7c.4-.4 1-.4 1.4 0s.4 1 0 1.4l-6 6c-.2.2-.5.3-.7.3s-.5-.1-.7-.3l-6-6c-.4-.4-.4-1 0-1.4s1-.4 1.4 0z"/></svg>
                                </button>

                                <ul class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                                {% for template in list_customize_template %}
                                        <li>
                                            <a type='button'
                                                class="dropdown-item tw-text-ellipsis tw-overflow-hidden text-click-white"
                                                target="_blank" name="downloadPDF" href="{% host_url pdf_download_link id=obj.id host 'app' %}?template_select={{template.id|to_str}}"
                                                >
                                                {% if template.name %}
                                                    {{template.name}}
                                                {% else %}
                                                    {% if LANGUAGE_CODE == 'ja' %}
                                                    {{template.master_pdf.name_ja}}
                                                    {% else %}
                                                    {{template.master_pdf.name_en}}
                                                    {% endif %}
                                                {% endif %}
                                            </a>
                                        </li>
                                {% endfor %}
                                </ul>
                            </div>
                        </div>
                    {% endif %}
                </div>

                {% comment %} VALIDATION MESSAGE {% endcomment %}
                <div class="validation-container-billing bg-danger px-10 py-3 text-white d-flex align-items-center d-none w-100 position-fixed" style="bottom: 7rem;">
                    <span id="validation-msg"></span>
                    <button id="close-val-message" type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5">
                        <span class="svg-icon svg-icon-2 svg-icon-white">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                            </svg>
                        </span>
                    </button>
                </div>

                <script>
                    targetBox = parentDiv_{{object_type}}.querySelector('.validation-container-billing')
                    validationMsg = parentDiv_{{object_type}}.querySelector('#validation-msg')
                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            targetBox.classList.remove('d-none')
                        });
                    });
                    // configuration of the observer:
                    var config = { attributes: true, childList: true, characterData: true };

                    // pass in the target node, as well as the observer options
                    observer.observe(validationMsg, config);

                    parentDiv_{{object_type}}.querySelector('#close-val-message').addEventListener('click', function() {
                        targetBox.classList.add('d-none')
                    })

                    parentDiv_{{object_type}}.body.addEventListener("showValidationMessage", function(evt){
                        validationMsg.innerHTML = evt.detail.value;
                        var buttons = parentDiv_{{object_type}}.querySelectorAll('.submitbtn');

                        for (var i = 0; i < buttons.length; i++) {
                            buttons[i].classList.remove('disabled');
                        }
                        parentDiv_{{object_type}}.querySelector('.save-indicator').classList.add('d-none');
                        parentDiv_{{object_type}}.querySelector('.send-indicator').classList.add('d-none');
                        parentDiv_{{object_type}}.querySelector('.delete-indicator').classList.add('d-none');
                    })
                </script>

            {% endif %}
        </form>
    </div>

    {% if not hide_associated_data %}
        <div class="{% include "data/utility/drawer-association-div.html" %}">
            <div class="accordion accordion-icon-collapse" id="related_item">

                {% include 'data/partials/manage-drawer/associated-action-component.html' with page_group_type=object_type obj=obj %}

                {% comment %} Activities {% endcomment %}
                {% host_url 'activity_log' host 'app' as activities_hx_get %}
                {% with obj_id=obj.id|stringify %}
                    {% include 'data/partials/manage-drawer/associated-activities-component.html' with hx_get=activities_hx_get hx_vals='{"page":"1", "obj_type":"'|add:object_type|add:'", "id":"'|add:obj_id|add:'" }' %}
                {% endwith %}

                {% comment %} {% if object_type == constant.TYPE_OBJECT_RECEIPT %}
                    {% include 'data/partials/manage-drawer/associated-receipt-invoice-component.html' with receipt=obj source=source page=page view_id=view_id module=module %}
                {% endif %} {% endcomment %}

                {% if object_type == constant.TYPE_OBJECT_DELIVERY_NOTE %}
                    {% include 'data/partials/manage-drawer/associated-general-object-to-invoice-component.html' with source=object_type invoices=obj.invoices.all %}
                {% endif %}
                {% if object_type == constant.TYPE_OBJECT_ESTIMATE or object_type == constant.TYPE_OBJECT_INVOICE or object_type == constant.TYPE_OBJECT_RECEIPT %}
                    {% comment %} {% include 'data/partials/manage-drawer/associated-estimate-invoice-component.html' %}
                    {% include 'data/partials/manage-drawer/associated-estimate-case-component.html' with from=constant.TYPE_OBJECT_ESTIMATE %}
                    {% include 'data/partials/manage-drawer/associated-estimate-order-component.html' %}
                    {% include 'data/partials/manage-drawer/associated-estimate-version.html' %} {% endcomment %}

                    {% comment %} association label {% endcomment %}
                    {% for label in association_labels %}
                        {% if label.created_by_sanka %}
                        
                            {% if label.label == constant.TYPE_OBJECT_INVENTORY_TRANSACTION or label.label == constant.TYPE_OBJECT_ITEMS %} 
                            {% host_url 'inventory_association_drawer' host 'app' as add_association_url %} 
                            
                            {% elif label.label == constant.TYPE_OBJECT_INVOICE or label.label == constant.TYPE_OBJECT_ESTIMATE or label.label == constant.TYPE_OBJECT_DELIVERY_NOTE %} 
                            {% host_url 'biling_association_drawer' host 'app' as add_association_url %} 
                            
                            {% elif label.label == constant.TYPE_OBJECT_CASE %} 
                            {% host_url 'cases_to_order_association_drawer' host 'app' as add_association_url %}

                            {% elif label.label == constant.TYPE_OBJECT_PURCHASE_ORDER %}
                            {% host_url 'purchase_orders_to_order_drawer' host 'app' as add_association_url %}
                            
                            {% elif label.label == constant.TYPE_OBJECT_SUBSCRIPTION %}
                            {% host_url 'subscription_association_drawer' host 'app' as add_association_url %}
                            
                            {% endif %}

                        {% endif %}

                        {% include 'data/partials/manage-drawer/associated-label-component.html' with obj=obj from=object_type label=label add_association_url=add_association_url%}
                    {% endfor %}


                {% endif %}

                {% comment %} {% if object_type == constant.TYPE_OBJECT_INVOICE %}
                    {% include 'data/partials/manage-drawer/associated-invoice-estimate-component.html' %}
                    {% with invoice_id=obj.id|stringify %}
                        {% include 'data/partials/manage-drawer/associated-invoice-case-component.html'  with drawer_query_params='?target=invoices&id='|add:invoice_id %}
                    {% endwith%}

                    {% include 'data/partials/manage-drawer/associated-invoice-order-component.html' with from=constant.TYPE_OBJECT_INVOICE %}
                    {% include 'data/partials/manage-drawer/associated-invoice-subscription-component.html' with from=constant.TYPE_OBJECT_INVOICE %}
                    {% include 'data/partials/manage-drawer/associated-invoice-receipt-component.html' with invoice=obj source=object_type page=page view_id=view_id module=module %}
                {% endif %} {% endcomment %}

            </div>
        </div>
    {% endif %}

</div>

<style>
.submit-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: inherit;
    background-color: white;
    padding: 15px;
    border-top: 1px solid #eee;
    z-index: 100;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 0;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

#get-commerce-input-form {
    padding-bottom: 70px;
    position: relative;
}
</style>

<script>
// This script positions the button container to match its parent column width
parentDiv_{{object_type}}.addEventListener('DOMContentLoaded', function() {
    const formElement = parentDiv_{{object_type}}.querySelector('#get-commerce-input-form');
    const buttonContainer = parentDiv_{{object_type}}.querySelector('.submit-button-container');

    if (formElement && buttonContainer) {
        function adjustButtonContainer() {
            // Get the width and left position of the form/column
            const formRect = formElement.getBoundingClientRect();

            // Set the button container width and left position to match the form
            buttonContainer.style.width = formRect.width + 'px';
            buttonContainer.style.left = formRect.left + 'px';
        }

        // Run initially
        adjustButtonContainer();

        // Run on window resize
        window.addEventListener('resize', adjustButtonContainer);
    }
});
</script>