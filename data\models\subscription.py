import uuid

from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from data.constants.constant import OBJECTS_CF_TYPE, SHOPTURBO_NUMBER_FORMAT
from data.models import ObjectManager
from data.models.base import save_tags_values, validate_value_custom_field
from data.models.constant import POST_MEDIA_TYPE

FREQ_TIME = [
    ("days", "Days"),
    ("weeks", "Weeks"),
    ("months", "Months"),
    ("years", "Years"),
]

PRIOR_FREQ_TIME = [("days", "Days"), ("weeks", "Weeks")]

BILLING_TIMING = [
    ("last_day", _("Last Day of Billing Period")),
    ("first_day", _("First Day of Billing Period")),
]

SHOPTURBO_SUBSCRIPTIONS_USAGE_STATUS = [("active", "Active"), ("archived", "Archived")]

SHOPTURBO_SUBSCRIPTIONS_STATUS = [
    ("draft", "Draft"),
    ("active", "Active"),
    ("paused", "Paused"),
    ("canceled", "Canceled"),
    ("expired", "Expired"),
]


class ShopTurboSubscriptions(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    subscriptions_id = models.IntegerField(null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform_display_name = models.CharField(max_length=500, null=True, blank=True)
    item = models.ForeignKey(
        "ShopTurboItems", on_delete=models.CASCADE, null=True, blank=True
    )
    item_variant = models.ForeignKey(
        "ShopTurboItemsVariations", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.SET_NULL, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, null=True, blank=True
    )
    status = models.CharField(
        choices=SHOPTURBO_SUBSCRIPTIONS_USAGE_STATUS,
        max_length=30,
        null=True,
        blank=True,
    )
    subscription_status = models.CharField(
        choices=SHOPTURBO_SUBSCRIPTIONS_STATUS, max_length=30, null=True, blank=True
    )
    number_item = models.IntegerField(null=True, blank=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    frequency = models.IntegerField(null=True, blank=True)
    frequency_time = models.CharField(
        max_length=10, choices=FREQ_TIME, null=True, blank=True
    )
    prior_to_next = models.FloatField(null=True, blank=True)
    prior_to_time = models.CharField(
        max_length=10, choices=PRIOR_FREQ_TIME, null=True, blank=True
    )
    billing_timing = models.CharField(
        max_length=10, choices=BILLING_TIMING, null=True, blank=True
    )
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    last_ordered_date = models.DateField(null=True, blank=True)
    last_billed_date = models.DateField(null=True, blank=True)
    next_order_date = models.DateField(null=True, blank=True)
    next_bill_date = models.DateField(null=True, blank=True)

    orders = models.ManyToManyField(
        "ShopTurboOrders", blank=True, related_name="subscriptions"
    )
    invoices = models.ManyToManyField(
        "Invoice", blank=True, related_name="subscriptions"
    )

    shipping_cost = models.ForeignKey(
        "ShopTurboShippingCost",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    shipping_cost_tax_status = models.CharField(max_length=200, null=True, blank=True)

    discounts = models.ForeignKey(
        "ShopTurboItemsDiscount",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    tax = models.FloatField(null=True, blank=True)
    tax_applied_to = models.CharField(max_length=200, null=True, blank=True)

    quick_entry_mode = models.BooleanField(default=False)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if not self.subscriptions_id:
            # Generate a new order_id if it doesn't exist
            last_subscriptions = (
                ShopTurboSubscriptions.objects.filter(
                    workspace=self.workspace, subscriptions_id__isnull=False
                )
                .order_by("-subscriptions_id")
                .first()
            )
            if last_subscriptions:
                if last_subscriptions.subscriptions_id:
                    try:
                        last_subscriptions_id = int(last_subscriptions.subscriptions_id)
                    except:
                        last_subscriptions_id = 0

                    next_subscriptions_id = last_subscriptions_id + 1
                else:
                    next_subscriptions_id = 1
            else:
                # If it's the first order, start with 1
                next_subscriptions_id = 1

            # Format the order_id as a three-digit string
            self.subscriptions_id = f"{next_subscriptions_id:03d}"

        super().save(*args, **kwargs)

    def get_display_name(self):
        platforms = self.source_subscription.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.platform_display_name
        return data

    def get_total_price_without_tax(self):
        total_price_without_tax = self.total_price
        shipping_cost = self.shipping_cost
        if shipping_cost:
            total_price_without_tax = total_price_without_tax - shipping_cost.value
        tax = self.tax
        if tax:
            total_price_without_tax = total_price_without_tax / (1 + tax / 100)
        return total_price_without_tax


class ShopTurboItemsSubscriptions(models.Model):
    item = models.ForeignKey(
        "ShopTurboItems", on_delete=models.SET_NULL, null=True, blank=True
    )
    item_price_order = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    item_variant = models.ForeignKey(
        "ShopTurboItemsVariations", on_delete=models.SET_NULL, null=True, blank=True
    )
    item_price = models.ForeignKey(
        "ShopTurboItemsPrice", on_delete=models.SET_NULL, null=True, blank=True
    )
    subscriptions = models.ForeignKey(
        "ShopTurboSubscriptions", on_delete=models.CASCADE, null=True, blank=True
    )
    order_platform = models.ForeignKey(
        "ShopTurboOrdersPlatforms", on_delete=models.CASCADE, null=True, blank=True
    )

    # custom item
    custom_item_name = models.CharField(max_length=500, null=True, blank=True)
    number_item = models.IntegerField(null=True, blank=True)
    item_price_order = models.FloatField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    platform_id = models.CharField(max_length=500, blank=True, null=True)

    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)


class ShopTurboSubscriptionPlatforms(models.Model):
    source_subscription = models.ForeignKey(
        "ShopTurboSubscriptions",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="platforms",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    platform_display_name = models.CharField(max_length=500, null=True, blank=True)
    platform_status = models.CharField(
        max_length=200, null=True, blank=True
    )  # platform subscripotion status
    payment_id = models.CharField(max_length=200, null=True, blank=True)
    payment_link = models.TextField(blank=True, null=True)
    payment_expires_at = models.IntegerField(null=True, blank=True)
    payment_allow_discount = models.BooleanField(default=False)
    payment_timezone = models.CharField(max_length=50, null=True, blank=True)
    payment_status = models.CharField(max_length=200, null=True, blank=True)
    manage_payment_session_id = models.TextField(blank=True, null=True)
    payment_setup_id = models.CharField(max_length=200, null=True, blank=True)
    start_date_field = models.CharField(max_length=255, null=True, blank=True)
    next_billing_id = models.CharField(max_length=200, null=True, blank=True)
    next_billing_price = models.FloatField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class ShopTurboSubscriptionsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class ShopTurboSubscriptionsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ShopTurboSubscriptionsNameCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    subscriptions = models.ForeignKey(
        ShopTurboSubscriptions,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_subscriptions_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="shopturbo-subscriptions-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)

    class Meta:
        unique_together = [["field_name", "subscriptions"]]


class ShopTurboSubscriptionsValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Shopturbo Subscriptions Custom Field file",
        upload_to="shopturbo-subscription-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        ShopTurboSubscriptionsValueCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="value_files",
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)
