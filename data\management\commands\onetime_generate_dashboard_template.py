from django.core.management.base import BaseCommand

from datetime import timedelta
from data.models import *

class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        REPORT_TRANSLATIONS = {
            "Expense Sheet": {
                "ja": "経費シート",
                "description": "A summary of company expenses.",
                "description_ja": "会社の経費概要です。"
            },
            "Purchase Order Dashboard": {
                "ja": "購買注文ダッシュボード",
                "description": "Dashboard tracking purchase orders.",
                "description_ja": "購買注文を追跡するダッシュボードです。"
            },
            "Case Dashboard": {
                "ja": "ケースダッシュボード",
                "description": "Dashboard showing case metrics.",
                "description_ja": "ケースに関する指標を表示するダッシュボードです。"
            },
            "Trend Analytics": {
                "ja": "トレンド分析",
                "description": "Analytical trends over time.",
                "description_ja": "時間の経過による分析トレンドです。"
            },
            "Inventory Dashboard": {
                "ja": "在庫ダッシュボード",
                "description": "Dashboard to monitor inventory levels.",
                "description_ja": "在庫レベルを監視するダッシュボードです。"
            },
            "Invoice Dashboard": {
                "ja": "請求書ダッシュボード",
                "description": "Track and review invoices.",
                "description_ja": "請求書の追跡と確認を行います。"
            },
            "Order Dashboard": {
                "ja": "注文ダッシュボード",
                "description": "Dashboard for order tracking.",
                "description_ja": "注文の追跡用ダッシュボードです。"
            },
        }
        
        pre_generated_reports = Report.objects.filter(name__icontains="<system> -").order_by('-created_at')
        for old_report in pre_generated_reports:
            report_id = old_report.id
            cleaned_name = old_report.name.replace("<system> -", "").strip()
            translation = REPORT_TRANSLATIONS.get(cleaned_name, {})
            japanese_name = translation.get("ja")
            desc = translation.get("description")
            desc_ja = translation.get("description_ja")
            
            old_report.id = None
            old_report.name = cleaned_name
            old_report.name_ja = japanese_name
            old_report.description = desc
            old_report.description_ja = desc_ja
            old_report.workspace = None
            old_report.is_template = True
            old_report.created_at = timezone.now()
            old_report.updated_at = timezone.now()
            old_report.save()
            new_report = old_report
            
            old_panel_links = PanelReportPanel.objects.filter(report_id=report_id)
            for link in old_panel_links:
                old_panel = link.panel
                cloned_metrics = []
                for m in old_panel.metrics.all():
                    m.pk = None
                    m.save()
                    cloned_metrics.append(m)
                channels = old_panel.comparison_channel.all()

                old_panel_id = old_panel.id
                old_panel.id = None
                old_panel.workspace = None
                old_panel.is_template = False
                old_panel.end_time = timezone.now()
                old_panel.start_time = timezone.now() - timedelta(days=30)
                old_panel.created_by = user
                old_panel.created_at = timezone.now()
                old_panel.updated_at = timezone.now()
                old_panel.save()
                new_panel = old_panel

                new_panel.metrics.set(cloned_metrics)
                new_panel.comparison_channel.set(channels)

                PanelReportPanel.objects.create(report=new_report, panel=new_panel, order=link.order)

                tab_map = {}
                old_tabs = ReportPanelSheetTab.objects.filter(report_panel_id=old_panel_id)
                
                for tab in old_tabs:
                    old_tab_id = tab.id
                    tab.id = None  # force insert
                    tab.report_panel = new_panel
                    tab.save()
                    tab_map[old_tab_id] = tab

                sheets = ReportPanelSheet.objects.filter(report_panel_id=old_panel_id)
                sheet_clones = [
                    ReportPanelSheet(
                        report_panel=new_panel,
                        tab=tab_map.get(sheet.tab_id),
                        col=sheet.col,
                        row=sheet.row,
                        value=sheet.value,
                    )
                    for sheet in sheets
                ]
                ReportPanelSheet.objects.bulk_create(sheet_clones)

                rows = ReportPanelSheetRows.objects.filter(report_panel_id=old_panel_id)
                row_clones = [
                    ReportPanelSheetRows(
                        report_panel=new_panel,
                        rows_source=row.rows_source,
                        rows_display=row.rows_display,
                        grouped_date_field=row.grouped_date_field,
                        rows_formula=row.rows_formula,
                        filter=row.filter,
                    )
                    for row in rows
                ]
                ReportPanelSheetRows.objects.bulk_create(row_clones)