import ast
import math
import traceback
from datetime import datetime
from django.conf import settings
from django.http import HttpResponse
from django.shortcuts import redirect
from django.views.generic import View
from django_hosts.resolvers import reverse
from data.constants.associate_constant import *
from data.constants.properties_constant import (TYPE_OBJECT_COMPANY,
                                                TYPE_OBJECT_DELIVERY_NOTE,
                                                TYPE_OBJECT_INVOICE,
                                                TYPE_OBJECT_RECEIPT)
from data.models import (
    Module, Notification, ShopTurboShippingCost, ShopTurboOrders,
    Deals, Estimate, ShopTurboSubscriptions, User, Workspace, Log,
    ShopTurboDecimalPoint
)
from data.accounts.association_labels import save_association_label
from utils.decorator import login_or_hubspot_required
from utils.freee_bg_jobs.freee import *
from utils.items_util import handling_items
from utils.meter import (MODELS_TO_STORAGE_USAGE, has_quota,
                         sync_usage)
from utils.properties.properties import (get_page_object)
from utils.serializer import *
from utils.smtp import *
from utils.utility import (get_workspace, is_valid_uuid,
                           save_custom_property,
                           update_query_params_url,
                           assign_object_owner)

from data.commerce import bulk_csv_commerce
from data.commerce.commerce_functions import commerce_send_mail
from data.commerce.bulk_csv_commerce import bulk_csv_commerce
from data.commerce import bulk_csv_commerce


type_http = settings.SITE_URL.split("//")[0]


@login_or_hubspot_required
def invoice_create_and_update(request, id=None, object_type=TYPE_OBJECT_INVOICE):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    print("STARTING: ")
    print(object_type)
    page_obj = get_page_object(object_type, lang)

    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    module = Module.objects.filter(
        workspace=workspace, object_values__contains=object_type).order_by('order', 'created_at')

    module_slug = request.POST.get('module', '')
    if module_slug:
        module = module.filter(slug=module_slug)

    if module:
        module = module.first()
        module_slug = module.slug

    id_field = page_obj['id_field']
    page_title = page_obj['page_title']
    default_columns = page_obj['default_columns']
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    custom_value_model = page_obj['custom_value_model']
    custom_value_file_model = page_obj['custom_value_file_model']
    custom_value_file_relation = page_obj['custom_value_file_relation']
    custom_item_model = page_obj['custom_item_model']
    custom_item_value_model = page_obj['custom_item_value_model']
    item_model = page_obj['item_model']
    field_item_name = page_obj['field_item_name']
    additional_filter_fields = page_obj['additional_filter_fields']

    if request.method != 'POST':
        return HttpResponse(405)

    if 'csv_upload' in request.POST:
        try:
            result = bulk_csv_commerce(request, object_type)
            if result != "Done":
                Notification.objects.create(workspace=workspace, user=request.user,
                                            message="Wrong format uploaded, Please use this format",
                                            message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                                            cta_text="Template",
                                            cta_text_ja="テンプレート",
                                            cta_target=TEMPLATE_FILE[object_type][lang],
                                            type="error")

        except:
            traceback.print_exc()
            Notification.objects.create(workspace=workspace, user=request.user,
                                        message="Wrong format uploaded, Please use this format",
                                        message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                                        cta_text="Template",
                                        cta_text_ja="テンプレート",
                                        cta_target=TEMPLATE_FILE[object_type][lang],
                                        type="error")

        if module_slug:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
        return redirect(reverse('main', host='app'))

    # Input Steps from form ==========================
    amount = []
    amounts_input = request.POST.getlist('amount')
    for am in amounts_input:
        am = am.replace(',', '')
        amount.append(am)

    section_position = request.POST.getlist('section_position')
    section_type = request.POST.getlist('section_type')
    currency = request.POST.get('currency')
    section = request.POST.getlist('section')
    amounts_input = request.POST.getlist('amount')
    amount_item = request.POST.getlist('item_amount')
    contact_and_company = request.POST.getlist("contact_and_company", None)
    start_date = request.POST.get('start-date', '')
    due_date = request.POST.get('freq-due', '')
    tax_rate = request.POST.get('tax_rate', 0)
    status = request.POST.get('status', '')
    item_request = request.POST.getlist('item', '')
    notes = request.POST.get('notes', '')
    send_from = request.POST.get('send_from', '')
    tax_item_rate = request.POST.getlist('tax_item_rate', None)
    tax_option = request.POST.get('tax_option', None)
    tax_inclusive = request.POST.get('tax_inclusive', None)
    discount_toggle = request.POST.get('discount_toggle', None)

    owner = request.POST.get('owner', None)
    shippibg_checkbox = request.POST.get('shipping_checkbox', None)
    shipping = request.POST.get('shipping', 0)

    if tax_rate == '':
        tax_rate = 0

    item = []

    for it in item_request:
        item.append(it.split('|')[0])

    if discount_toggle:
        discount_option = request.POST.get('discount_option')
        discount_tax_option = request.POST.get('discount_tax_option')
        discount_value = request.POST.get('discount')
    else:
        discount_tax_option = ""
        discount_option = ""
        discount_value = ""

    if not tax_inclusive:
        tax_inclusive = False
    else:
        if tax_option != "item_based_tax":
            tax_inclusive = "False"

    if shippibg_checkbox and shipping:
        shipping = ShopTurboShippingCost.objects.filter(id=shipping).first()
    else:
        shipping = None

    if id:
        try:
            invoice = base_model.objects.get(id=id)
        except base_model.DoesNotExist:
            return HttpResponse(status=404)
    else:
        sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])
        if not has_quota(workspace, object_type):
            msg = f"{page_title}, could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing {page_title} to free up space."
            if lang == 'ja':
                msg = f"{page_title},制限を超えたため作成できませんでした。サブスクリプション プランをアップグレードしてクォータを増やすか、既存の {page_title} の一部をアーカイブしてスペースを解放します。"
            Notification.objects.create(
                workspace=workspace, user=request.user, message=msg, type="error")
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
        invoice = base_model.objects.create(
            workspace=workspace, updated_at=timezone.now)

    if type(item) != list:
        item = ast.literal_eval(item)
    if type(amount_item) != list:
        amount_item = ast.literal_eval(amount_item)
    if type(amount) != list:
        amount = ast.literal_eval(amount)
    if type(tax_item_rate) != list:
        tax_item_rate = ast.literal_eval(tax_item_rate)
    if type(section) != list:
        section = ast.literal_eval(section)
    if type(section_type) != list:
        section_type = ast.literal_eval(section_type)
    if type(section_position) != list:
        section_position = ast.literal_eval(section_position)

    # Redirect after updating the associated object
    if 'update_orders' in request.POST:
        source = request.POST.get('source')
        if source == object_type:
            order_ids = request.POST.getlist('orders', [])
            if invoice and order_ids != ['']:
                invoice.orders.clear()
                order_objs = ShopTurboOrders.objects.filter(id__in=order_ids)
                invoice.orders.add(*order_objs)
            else:
                invoice.orders.clear()

    if 'update_cases' in request.POST:
        source = request.POST.get('source')
        if source == object_type:
            case_ids = request.POST.getlist('cases', [])
            if invoice and case_ids != ['']:
                invoice.deals.clear()
                case_objs = Deals.objects.filter(id__in=case_ids)
                invoice.deals.add(*case_objs)
            else:
                invoice.deals.clear()

    if 'update_estimates' in request.POST:
        source = request.POST.get('source')
        if source == object_type:
            estimate_ids = request.POST.getlist('estimates', [])
            if invoice and estimate_ids != ['']:
                invoice.estimate_set.clear()
                estimate_objs = Estimate.objects.filter(id__in=estimate_ids)
                invoice.estimate_set.add(*estimate_objs)
            else:
                invoice.estimate_set.clear()

    if 'update_subscriptions' in request.POST:
        source = request.POST.get('source')
        if source == object_type:
            subscription_ids = request.POST.getlist('subscriptions', [])
            if invoice and subscription_ids != ['']:
                invoice.subscriptions.clear()
                subscription_objs = ShopTurboSubscriptions.objects.filter(
                    id__in=subscription_ids)
                invoice.subscriptions.add(*subscription_objs)
            else:
                invoice.subscriptions.clear()

    if 'update_orders' in request.POST or 'update_cases' in request.POST or 'update_estimates' in request.POST or 'update_subscriptions' in request.POST or 'update_invoices' in request.POST:
        if request.POST.get('source_url'):
            source_url = request.POST.get('source_url', None)
            if source_url:
                source_url = update_query_params_url(source_url, {
                    'target': object_type,
                    'id': [str(invoice.id)]
                })
                return redirect(source_url)
        if request.POST.get("view_id") and request.POST.get("view_id") != 'None':
            view = View.objects.filter(id=request.POST.get("view_id"))
            if view:
                object_type = view[0].target
                page_obj = get_page_object(object_type, lang)

                if invoice:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}&id={invoice.id}')
                else:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}')

    try:
        print('logit!', request.POST)
        invoice_item = item_model.objects.filter(
            **{field_item_name: invoice}).order_by('created_at')
        # Clear all billing item data
        if invoice_item:
            for invoice__item in invoice_item:
                invoice__item.delete(
                    log_data={'user': request.user, 'workspace': workspace})
        # Get decimal point settings for consistent calculations
        general_decimal_point = ShopTurboDecimalPoint.objects.filter(
            workspace=workspace, app_name="general"
        ).first()
        
        total_price = 0
        total_price_without_tax = 0
        
        for index, (item, amount_item, amount_price, tax_item_rate) in enumerate(zip(item, amount_item, amount, tax_item_rate)):
            if not tax_item_rate:
                tax_item_rate = '0'
            if amount_item == '':
                amount_item = 0.0
            if amount_price == '':
                amount_price = 0.0

            invoice_item = item_model.objects.create(
                **{field_item_name: invoice})

            if is_valid_uuid(item):
                item = ShopTurboItems.objects.filter(id=item)
                if item:
                    invoice_item.item_link = item.last()
            else:
                invoice_item.item_name = item
            invoice_item.save(log_data={'user': request.user,
                                        'status': 'create', 'workspace': workspace})

            invoice_item.amount_price = amount_price
            invoice_item.amount_item = amount_item

            # Calculate line item totals using consistent logic
            unit_price = float(amount_price)
            quantity = float(amount_item)
            tax_item_rate = float(tax_item_rate) if tax_item_rate != '0' else 0.0

            if invoice.tax_inclusive:
                # Tax-inclusive: derive tax-exclusive from inclusive price
                line_item_total_with_tax = unit_price * quantity
                line_item_total_without_tax = line_item_total_with_tax / (1 + tax_item_rate / 100.0)
                
                # Apply JPY integer rounding for tax-exclusive amounts
                if hasattr(invoice, 'currency') and invoice.currency and invoice.currency.lower() == "jpy":
                    line_item_total_without_tax = int(line_item_total_without_tax)
            else:
                # Tax-exclusive: calculate inclusive from exclusive price
                line_item_total_without_tax = unit_price * quantity
                line_item_total_with_tax = line_item_total_without_tax * (1 + tax_item_rate / 100.0)

            # Apply line-item specific decimal point formatting if configured
            if general_decimal_point and general_decimal_point.type in ['line_item_cut_off', 'line_item_cut_over']:
                math_func = math.floor if general_decimal_point.type == 'line_item_cut_off' else math.ceil
                line_item_total_without_tax = math_func(line_item_total_without_tax)
                line_item_total_with_tax = math_func(line_item_total_with_tax)

            invoice_item.total_price_without_tax = line_item_total_without_tax
            invoice_item.total_price = line_item_total_with_tax
            invoice_item.tax_item_rate = tax_item_rate
            invoice_item.save(
                log_data={'user': request.user, 'workspace': workspace})

            line_item_properties = {
                key: request.POST.getlist(key)
                for key in request.POST
                if key.startswith('line_item_property')
            }

            for key, value in line_item_properties.items():
                custom_field_keys = key.split('|')
                custom_field_id = key.split('|')[-1]

                custom_field = custom_item_model.objects.get(
                    id=custom_field_id)
                custom_field_value, _ = custom_item_value_model.objects.get_or_create(
                    field_name=custom_field, item_order=invoice_item)

                if len(custom_field_keys) == 3:  # discount
                    custom_field_value.value_number_format = value[index]
                else:
                    custom_field_value.value = value[index]
                custom_field_value.save()
                
            total_price += invoice_item.total_price
            total_price_without_tax += invoice_item.total_price_without_tax
            
    except Exception as e:
        print('[Error saving items]: ', e)

    try:
        SectionItemInvoice.objects.filter(
            workspace=workspace, invoice=invoice).delete()
        for section, section_type, section_position in zip(section, section_type, section_position):
            section_object, _ = SectionItemInvoice.objects.get_or_create(
                workspace=workspace,
                section_type=section_type,
                invoice=invoice,
                position=int(section_position),
                value=section
            )
    except Exception as e:
        print('[Error saving sections]: ', e)

    # IF NEED DELETE OBJECT
    if 'delete' in request.POST:
        invoice = base_model.objects.get(id=id)
    if general_decimal_point and general_decimal_point.type in ['cut_off', 'cut_over']:
        math_func = math.floor if general_decimal_point.type == 'cut_off' else math.ceil
        if hasattr(invoice, 'currency') and invoice.currency and invoice.currency.lower() == "usd":
            # For USD, apply decimal formatting to 2 decimal places
            total_price = math_func(total_price * 100) / 100
            total_price_without_tax = math_func(total_price_without_tax * 100) / 100
        else:
            # For other currencies, apply integer formatting
            total_price = math_func(total_price)
            total_price_without_tax = math_func(total_price_without_tax)
        math_func = math.floor if general_decimal_point.type == 'cut_off' else math.ceil
        total_price = math_func(total_price)
        total_price_without_tax = math_func(total_price_without_tax)

    invoice.workspace = workspace
    invoice.email = email
    invoice.notes = notes
    invoice.status = status
    invoice.tax_rate = float(tax_rate)
    invoice.send_from = send_from
    invoice.tax_list = tax_item_rate
    invoice.tax_inclusive = tax_inclusive
    invoice.discount_option = discount_option
    invoice.discount_tax_option = discount_tax_option
    invoice.discount = discount_value
    invoice.currency = currency
    invoice.updated_at = timezone.now
    invoice.tax_option = tax_option
    invoice.shipping_cost = shipping


    assign_object_owner(invoice,owner,request,object_type)

    if start_date:
        if isinstance(start_date, tuple):
            start_date = start_date[0]
        try:
            if lang == 'ja':
                start_date = datetime.strptime(start_date, '%Y年%m月%d日').date()
            else:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            invoice.start_date = start_date
        except:  # Format date is invalid , skip to save it
            pass
    if due_date:
        try:
            if lang == 'ja':
                due_date = datetime.strptime(due_date, '%Y年%m月%d日').date()
            else:
                due_date = datetime.strptime(due_date, '%Y-%m-%d').date()
            invoice.due_date = due_date
        except:  # Format date is invalid , skip to save it
            pass
    
    
    association_label = AssociationLabel.objects.filter(workspace=workspace,object_source=object_type, label__iexact='customer').first()
    AssociationLabelObject.reset_associations_for_object(
        invoice, 
        workspace, 
        association_label
    )
    for contact_and_company_id in contact_and_company:
        if Contact.objects.filter(id=contact_and_company_id):
            invoice.contact = Contact.objects.get(id=contact_and_company_id)
            invoice.company = None
            if object_type == TYPE_OBJECT_INVOICE:
                AssociationLabelObject.create_association(invoice, invoice.contact, workspace, association_label)

        elif Company.objects.filter(id=contact_and_company_id):
            invoice.company = Company.objects.get(id=contact_and_company_id)
            invoice.contact = None
            if object_type == TYPE_OBJECT_INVOICE:
                AssociationLabelObject.create_association(invoice, invoice.company, workspace, association_label)

    invoice.total_price = total_price
    invoice.total_price_without_tax = total_price_without_tax
    invoice.save(log_data={'user': request.user, 'workspace': workspace})
    invoice = handling_items(invoice)
    print("Obj Handled 1: ")
    print(invoice)

    # == Object Properties handler
    save_custom_property(request, invoice, page_obj)
    
    # == Association Labels handler
    save_association_label(request, invoice, object_type)

    if 'update_receipts' in request.POST:
        source = request.POST.get('source')
        if source == TYPE_OBJECT_INVOICE:
            receipt_ids = request.POST.getlist('receipts', [])
            if invoice and receipt_ids != ['']:
                invoice.receipts.clear()
                receipt_objs = Receipt.objects.filter(
                    id__in=receipt_ids)
                invoice.receipts.add(*receipt_objs)
            else:
                invoice.receipts.clear()

    if 'update_orders' in request.POST:
        source = request.POST.get('source')
        order_ids = request.POST.getlist('orders', [])
        if invoice and order_ids != ['']:
            invoice.orders.clear()
            order_objs = ShopTurboOrders.objects.filter(id__in=order_ids)
            invoice.orders.add(*order_objs)
        else:
            invoice.orders.clear()

    if 'update_cases' in request.POST:
        source = request.POST.get('source')
        case_ids = request.POST.getlist('cases', [])
        if invoice and case_ids != ['']:
            invoice.deals.clear()
            case_objs = Deals.objects.filter(id__in=case_ids)
            invoice.deals.add(*case_objs)
        else:
            invoice.deals.clear()

    if 'update_estimates' in request.POST:
        source = request.POST.get('source')
        estimate_ids = request.POST.getlist('estimates', [])
        if invoice and estimate_ids != ['']:
            invoice.estimate_set.clear()
            estimate_objs = Estimate.objects.filter(id__in=estimate_ids)
            invoice.estimate_set.add(*estimate_objs)
        else:
            invoice.estimate_set.clear()

    if 'update_subscriptions' in request.POST:
        source = request.POST.get('source')
        subscription_ids = request.POST.getlist('subscriptions', [])
        if invoice and subscription_ids != ['']:
            invoice.subscriptions.clear()
            subscription_objs = ShopTurboSubscriptions.objects.filter(
                id__in=subscription_ids)
            invoice.subscriptions.add(*subscription_objs)
        else:
            invoice.subscriptions.clear()

    if request.POST.get('submit_action') == 'send_email':
        commerce_send_mail(request, invoice.id, object_type=object_type)

    if request.POST.get('source_url'):
        source_url = request.POST.get('source_url', None)
        if source_url:
            source_url = update_query_params_url(source_url, {
                'target': object_type,
                'id': [str(invoice.id)]
            })
            return redirect(source_url)
    if request.POST.get("view_id") and request.POST.get("view_id") != 'None':
        view = View.objects.filter(id=request.POST.get("view_id"))
        if view:
            object_type = view[0].target
            page_obj = get_page_object(object_type, lang)

            if invoice:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}&id={invoice.id}')
            else:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}')

    # association related
    if request.POST.get('type', '') == 'create-association':
        if 'object_type' in request.POST:
            object_type = request.POST.get('object_type')
        source = request.POST.get('source')
        module_object_slug = OBJECT_TYPE_TO_SLUG[source]
        module = Module.objects.filter(workspace=workspace, object_values__contains=source).order_by(
            'order', 'created_at').first()
        module_slug = module.slug
        if source == TYPE_OBJECT_CASE:
            object_id = request.POST.get('source_object_id')
            case = Deals.objects.filter(id=object_id).first()
            if object_type == object_type:
                case.invoices.add(invoice)
            case.save()

        elif source == TYPE_OBJECT_RECEIPT:
            object_id = request.POST.get('source_object_id')
            receipt = Receipt.objects.filter(id=object_id).first()
            if object_type == object_type:
                if receipt:
                    receipt.invoices.add(invoice)
                    receipt.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={object_id}')

        elif source == TYPE_OBJECT_DELIVERY_NOTE:
            object_id = request.POST.get('source_object_id')
            delivery_slip = DeliverySlip.objects.filter(id=object_id).first()
            if delivery_slip and object_type == object_type:
                delivery_slip.invoices.add(invoice)
                delivery_slip.save()

            if 'object_id' in request.POST:
                return HttpResponse(status=200)
            else:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={object_id}')
        elif source == TYPE_OBJECT_ORDER:
            object_id = request.POST.get('source_object_id')
            order = ShopTurboOrders.objects.filter(id=object_id).first()
            if object_type == object_type:
                order.invoice.add(invoice)
            order.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={order.id}')

        elif source == TYPE_OBJECT_SUBSCRIPTION:
            object_id = request.POST.get('source_object_id')
            subs = ShopTurboSubscriptions.objects.filter(id=object_id).first()
            if object_type == object_type:
                subs.invoices.add(invoice)
                if not subs.last_billed_date:
                    subs.last_billed_date = invoice.start_date
                if not subs.next_bill_date:
                    subs.next_bill_date = invoice.due_date
            subs.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={subs.id}')

        elif source == TYPE_OBJECT_COMPANY:
            object_id = request.POST.get('source_object_id')
            company = Company.objects.filter(id=object_id).first()
            invoice.company = company
            invoice.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={company.id}')

        elif source == TYPE_OBJECT_JOURNAL:
            object_id = request.POST.get('source_object_id')
            journal = JournalEntry.objects.filter(id=object_id).first()
            if object_type == object_type:
                journal.invoice = invoice
                journal.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={journal.id}')

    else:
        if invoice:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={invoice.id}')
    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
