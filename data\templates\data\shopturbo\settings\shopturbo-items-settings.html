{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

<div class="mt-5" style="width: 100%;">
    
    <div class="mt-5 container-fluid px-0 scroll-y scoll-x">
        {% if setting_type == 'commerce_items' %}
            <div class="mb-5 form-check form-switch form-check-custom tw-flex tw-items-center">
                <label class="d-flex align-items-center fs-3 fw-bolder me-3">
                    <span class="">
                        {{object_name}}
                        {% if LANGUAGE_CODE == 'ja' %}
                        オブジェクト
                        {% else %}
                        Object
                        {% endif %}
                    </span>
                </label>
                {% comment %} <input id="{{page_group_type}}" name="{{page_group_type}}" class="form-check-input" type="checkbox" {% if object_manager.is_show %} checked {% endif %}
                    hx-post="{% host_url 'toggle_object_visibility' host 'app' %}" hx-trigger="click" hx-vals='{"page_group_type":"{{page_group_type}}"}' hx-swap="none"
                    onclick="toggleCommerceInventoryDisplay(this.checked)">
                <script>
                    function toggleCommerceInventoryDisplay(enabled) {
                        const submit = document.getElementById('{{page_group_type}}-display-submit');
                        submit.disabled = !enabled;
                    }
                </script> {% endcomment %}
            </div>
            <div id="change-stamp-section" class="mb-10">
                <div class="mb-10"
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_items"}'
                    hx-trigger="load"
                    hx-target="#properties-table"
                >
                    <div id="properties-table"></div>
                </div>

                <form method="POST" class="mb-10 pb-5" action="{% host_url 'shopturbo_settings' host 'app' %}" onkeydown="return event.key != 'Enter';">
                    <input type="hidden" class="" name="app_settings" value="manage_app"/>
                    <input type="hidden" class="" name="page_group_type" value="{{setting_type}}"/>
                    {% csrf_token %}

                    {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

                    <div class="mb-10 d-none">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="">
                                {% if LANGUAGE_CODE == 'ja' %}
                                検索設定
                                {% else %}
                                Search Settings
                                {% endif %}
                            </span>
                        </label>

                        <input
                        {% if LANGUAGE_CODE == 'ja' %}
                        placeholder="検索設定"
                        {% else %}
                        placeholder="Search Settings"
                        {% endif %}
                        id="search_setting_item"
                        name="search_setting_item" class="form-control" 
                        
                        value=""></input>
                    </div>


                    <div class="mt-5">
                        <div class="fv-rowd-flex flex-column mb-3">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    デフォルトのバーコードの種類
                                    {% else %}
                                    Default Barcode Type
                                    {% endif %}
                                </span>
                            </label>
            
                            <select class="bg-white border {% include 'data/utility/select-form.html' %} select2-this"
                                data-control="select2" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="バーコードの個々の設定"
                                {% else %}
                                data-placeholder="Select Barcode Settings"
                                {% endif %}
                                name="barcode-type"
                                >
                                {% for barcode_type in barcode_types %}
                                    <option value="{{barcode_type.0}}"
                                        {% if barcode_property %}
                                            {% if barcode_property.type == barcode_type.0 %}
                                                selected
                                            {% endif %}
                                        {% endif %}
                                    >
                                        {{barcode_type.1}}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
        
                    <div class="mt-5 mb-10">
                        <div class="fv-rowd-flex flex-column mb-3">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    デフォルトのバーコード読み込みプロパティ
                                    {% else %}
                                    Default Barcode Query
                                    {% endif %}
                                </span>
                            </label>
        
                            <select class="bg-white border {% include 'data/utility/select-form.html' %} select2-this" 
                                name="barcode-properties" 
                                data-control="select2" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="バーコードのプロパティを選択" 
                                {% else %}
                                data-placeholder="Select Barcode Properties" 
                                {% endif %}
                                >
                                {% for barcode_column in barcode_column_values %}
                                    <option value="{{barcode_column}}"
                                        {% if barcode_property %}
                                            {% if barcode_property.column_field == barcode_column %}
                                                selected
                                            {% endif %}
                                        {% endif %}
                                    >
                                        {% if barcode_column|search_custom_field_object_items:request %}
                                            {% with channel_column=barcode_column|search_custom_field_object_items:request %}
                                                {{channel_column.name}}
                                            {% endwith %} 
                                        {% else %}
                                            {% with column_display=barcode_column|display_column_items:request %}
                                                {{column_display}}
                                            {% endwith %}
                                        {% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    {% include 'data/partials/pdf-template-list.html' with default_pdf_color=app_setting.item_pdf_color default_pdf_font_type=app_setting.item_pdf_font_type default_pdf_template=app_setting.item_pdf_template preselected_col=app_setting.item_pdf_line_item_table preselected_header_block=app_setting.item_pdf_header_block preselected_payment_block=app_setting.item_pdf_payment_block preselected_send_from_block=app_setting.item_pdf_send_from_block preselected_send_to_block=app_setting.item_pdf_send_to_block preselected_ship_to_block=app_setting.item_pdf_ship_to_block preselected_notes_block=app_setting.item_pdf_notes_block list_template=list_template preselected_footer_template=app_setting.item_pdf_footer_template pdf_title=app_setting.item_pdf_title|default_if_none:"" preselected_col_display=app_setting.item_pdf_line_item_table_display preselected_header_block_display=app_setting.item_pdf_header_block_display preselected_payment_block_display=app_setting.item_pdf_payment_block_display preselected_send_from_block_display=app_setting.item_pdf_send_from_block_display preselected_send_to_block_display=app_setting.item_pdf_send_to_block_display preselected_notes_block_display=app_setting.item_pdf_notes_block_display default_line_item_bg_color=app_setting.item_pdf_line_item_table_bg_color default_line_item_font_color=app_setting.item_pdf_line_item_table_font_color default_line_item_font_size=app_setting.item_pdf_line_item_table_font_size preselected_user_block=app_setting.item_pdf_user_block preselected_user_block_display=app_setting.item_pdf_user_block_display %}

                    <div class="mt-5">
                        <button id="submit-items-settings-btn" type="submit" class="btn btn-dark" name="submit-items-settings-btn">
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>

        {% elif setting_type == 'commerce_inventory' %}
            <div class="mb-5 form-check form-switch form-check-custom tw-flex tw-items-center">
                <label class="d-flex align-items-center fs-3 fw-bolder me-3">
                    <span class="">
                        {{object_name}}
                        {% if LANGUAGE_CODE == 'ja' %}
                        オブジェクト
                        {% else %}
                        Object
                        {% endif %}
                    </span>
                </label>
                {% comment %} <input id="{{page_group_type}}" name="{{page_group_type}}" class="form-check-input" type="checkbox" {% if object_manager.is_show %} checked {% endif %}
                    hx-post="{% host_url 'toggle_object_visibility' host 'app' %}" hx-trigger="click" hx-vals='{"page_group_type":"{{page_group_type}}"}' hx-swap="none"
                    onclick="toggleCommerceInventoryDisplay(this.checked)">
                <script>
                    function toggleCommerceInventoryDisplay(enabled) {
                        const submit = document.getElementById('{{page_group_type}}-display-submit');
                        submit.disabled = !enabled;
                    }
                </script> {% endcomment %}
            </div>
            
            <div id="change-stamp-section" class="mb-10">
                <div class="mb-10"
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_inventory"}'
                    hx-trigger="load"
                    hx-target="#properties-table-inventory"
                >
                    <div id="properties-table-inventory"></div>
                </div>

                {% comment %} association label {% endcomment %}
                <div
                    hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_inventory"}'
                    hx-trigger="load"
                ></div>

                <form method="POST" class="pb-5 mb-10" action="{% host_url 'shopturbo_settings' host 'app' %}" onkeydown="return event.key != 'Enter';">
                    {% csrf_token %}
                    <input type="hidden" class="" name="app_settings" value="manage_app"/>
                    <input type="hidden" class="" name="page_group_type" value="{{setting_type}}"/>

                    {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

                    {% comment %} Search Settings {% endcomment %}
                    {% include "data/common/search_setting/search-setting.html" with id="search_setting_inventory" name="search_setting_inventory" page_group_type="commerce_inventory" search_setting_values=app_setting.search_setting_inventory %}

                    <div class="mt-5">
                        <div class="fv-rowd-flex flex-column mb-3">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    デフォルトのバーコードの種類
                                    {% else %}
                                    Default Barcode Type 
                                    {% endif %}
                                </span>
                            </label>
            
                            <select class="bg-white border {% include 'data/utility/select-form.html' %} select2-this"
                                data-control="select2" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="バーコードの個々の設定"
                                {% else %}
                                data-placeholder="Select Barcode Settings"
                                {% endif %}
                                name="barcode-type"
                                >
                                {% for barcode_type in barcode_types %}
                                    <option value="{{barcode_type.0}}"
                                        {% if barcode_property %}
                                            {% if barcode_property.type == barcode_type.0 %}
                                                selected
                                            {% endif %}
                                        {% endif %}
                                    >
                                        {{barcode_type.1}}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
        
                    <div class="mt-5 mb-10">
                        <div class="fv-rowd-flex flex-column mb-3">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    デフォルトのバーコード読み込みプロパティ
                                    {% else %}
                                    Default Barcode Query Properties
                                    {% endif %}
                                </span>
                            </label>
        
                            <select class="bg-white border {% include 'data/utility/select-form.html' %} select2-this" 
                                name="barcode-properties" 
                                data-control="select2" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="バーコードのプロパティを選択" 
                                {% else %}
                                data-placeholder="Select Barcode Properties" 
                                {% endif %}
                                >
                                {% for barcode_column in barcode_column_values %}
                                    <option value="{{barcode_column}}"
                                        {% if barcode_property %}
                                            {% if barcode_property.column_field == barcode_column %}
                                                selected
                                            {% endif %}
                                        {% endif %}
                                    >
                                        {% if barcode_column|search_custom_field_object_items:request %}
                                            {% with channel_column=barcode_column|search_custom_field_object_items:request %}
                                                {{channel_column.name}}
                                            {% endwith %} 
                                        {% else %}
                                            {% with column_display=barcode_column|display_column_items:request %}
                                                {{column_display}}
                                            {% endwith %}
                                        {% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="mt-5">
                        <button id="submit-inventory-settings-btn" type="submit" class="btn btn-dark" name="submit-inventory-settings-btn">
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>

        {% elif setting_type == 'commerce_inventory_transaction' %}
            
            <div id="change-stamp-section-1" class="mb-10">
                <div class="mb-5 form-check form-switch form-check-custom tw-flex tw-items-center">
                    <label class="d-flex align-items-center fs-3 fw-bolder me-3">
                        <span class="">
                            {{object_name}}
                            {% if LANGUAGE_CODE == 'ja' %}
                            オブジェクト
                            {% else %}
                            Object
                            {% endif %}
                        </span>
                    </label>
                </div>
                <div class="mb-10"
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_inventory_transaction"}'
                    hx-trigger="load"
                    hx-target="#properties-table-transaction"
                >
                    <div id="properties-table-transaction"></div>
                </div>

                {% comment %} association label {% endcomment %}
                <div
                    hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_inventory_transaction"}'
                    hx-trigger="load"
                ></div>

                <form method="POST" class="pb-5 mb-10" action="{% host_url 'shopturbo_settings' host 'app' %}" onkeydown="return event.key != 'Enter';">
                    {% csrf_token %}
                    <input type="hidden" class="" name="app_settings" value="manage_app"/>
                    <input type="hidden" class="" name="page_group_type" value="{{setting_type}}"/>
    
                    {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

                    {% comment %} Search Settings {% endcomment %}
                    {% include "data/common/search_setting/search-setting.html" with id="search_setting_inventory_transaction" name="search_setting_inventory_transaction" page_group_type="commerce_inventory_transaction" search_setting_values=app_setting.search_setting_inventory_transaction %}

                    <div class="mt-5">
                        <div class="fv-rowd-flex flex-column mb-3">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    デフォルトのバーコードの種類
                                    {% else %}
                                    Default Barcode Type
                                    {% endif %}
                                </span>
                            </label>
            
                            <select class="bg-white border {% include 'data/utility/select-form.html' %} select2-this"
                                data-control="select2" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="バーコードの個々の設定"
                                {% else %}
                                data-placeholder="Select Barcode Settings"
                                {% endif %}
                                name="barcode-type"
                                >
                                {% for barcode_type in barcode_types %}
                                    <option value="{{barcode_type.0}}"
                                        {% if barcode_property %}
                                            {% if barcode_property.type == barcode_type.0 %}
                                                selected
                                            {% endif %}
                                        {% endif %}
                                    >
                                        {{barcode_type.1}}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
        
                    <div class="mt-5 mb-10">
                        <div class="fv-rowd-flex flex-column mb-3">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    デフォルトのバーコード読み込みプロパティ
                                    {% else %}
                                    Default Barcode Query Properties
                                    {% endif %}
                                </span>
                            </label>
        
                            <select class="bg-white border {% include 'data/utility/select-form.html' %} select2-this" 
                                name="barcode-properties" 
                                data-control="select2" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="バーコードのプロパティを選択" 
                                {% else %}
                                data-placeholder="Select Barcode Properties" 
                                {% endif %}
                                >
                                {% for barcode_column in barcode_column_values %}
                                    <option value="{{barcode_column}}"
                                        {% if barcode_property %}
                                            {% if barcode_property.column_field == barcode_column %}
                                                selected
                                            {% endif %}
                                        {% endif %}
                                    >
                                        {% if barcode_column|search_custom_field_object_items:request %}
                                            {% with channel_column=barcode_column|search_custom_field_object_items:request %}
                                                {{channel_column.name}}
                                            {% endwith %} 
                                        {% else %}
                                            {% with column_display=barcode_column|display_column_items:request %}
                                                {{column_display}}
                                            {% endwith %}
                                        {% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
    
                    <div class="mt-5">
                        <button id="submit-inventory-transaction-settings-btn" type="submit" class="btn btn-dark" name="submit-inventory-transaction-settings-btn">
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>

        {% elif setting_type == 'commerce_inventory_warehouse' %}
            

            <div class="mb-10">
                <label class="d-flex align-items-center fs-3 fw-bolder me-3">
                    <span class="">
                        
                        {% if LANGUAGE_CODE == 'ja' %}
                        ロケーションオブジェクト
                        {% else %}
                        Location Object
                        {% endif %}
                    </span>
                </label>
    
            </div>
            <div id="change-stamp-section-2" class="mb-10">
                <form method="POST" class="pb-5 mb-10" action="{% host_url 'shopturbo_settings' host 'app' %}" onkeydown="return event.key != 'Enter';">
                    {% csrf_token %}
                    <input type="hidden" class="" name="app_settings" value="manage_app"/>
                    <input type="hidden" class="" name="page_group_type" value="{{setting_type}}"/>

                    
                    <div class="mb-10"
                        hx-get="{% host_url 'properties' host 'app' %}"
                        hx-vals='{"page_group_type": "commerce_inventory_warehouse"}'
                        hx-trigger="load"
                        hx-target="#properties-table-warehouse"
                    >
                        <div id="properties-table-warehouse"></div>
                    </div>

                    {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

                    {% comment %} Search Settings {% endcomment %}
                    {% include "data/common/search_setting/search-setting.html" with id="search_setting_warehouse" name="search_setting_warehouse" page_group_type="commerce_inventory_warehouse" search_setting_values=app_setting.search_setting_warehouse %}

                    <div class="mt-5">
                        <button id="submit-inventory-warehouse-settings-btn" type="submit" class="btn btn-dark" name="submit-inventory-warehouse-settings-btn">
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block js %}
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>

<script>
    {% if setting_type == 'commerce_items' %}
        var search_setting_item = document.querySelector("#search_setting_item");
        var search_setting_item_tagify = new Tagify(search_setting_item, {
            whitelist: [
            {% for column in column_values %}
                {% if column|search_custom_field_object_items:request %}
                    {% with channel_column=column|search_custom_field_object_items:request %}
                        { "value": "{{channel_column.name}}", "id": "{{channel_column.id}}" },
                    {% endwith %} 
                {% elif "item_platform|" in column %}
                    {% with column_display=column|display_column_items:request %}
                        { "value": "{{column_display|title}}", "id": "{{column}}" },
                    {% endwith %}
                {% elif column|search_channel_objects:request %}
                    {% with channel_column=column|search_channel_objects:request %}
                        { "value": "{{channel_column}}", "id": "{{channel_column}}" },
                    {% endwith %} 
                {% else %}
                    {% with column_display=column|display_column_items:request %}
                        { "value": "{{column_display|title}}", "id": "{{column}}" },
                    {% endwith %}
                {% endif %}
            {% endfor %}
            ],
            dropdown: {
                maxItems: 20,           // <- mixumum allowed rendered suggestions
                classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                enabled: 0,             // <- show suggestions on focus
                closeOnSelect: true    // <- do not hide the suggestions dropdown once an item has been selected
            },
            enforceWhitelist: true,
            searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
            originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
            });
        
        {% if app_setting.search_setting_item %}
            {% with app_setting.search_setting_item|split:"," as search_setting_value %}
                {% for value in search_setting_value %} 
                    {% if value|search_custom_field_object_items:request %}
                        {% with channel_column=value|search_custom_field_object_items:request %}
                            search_setting_item_tagify.addTags([{ value: "{{channel_column.name}}", id: "{{channel_column.id}}" }]);
                        {% endwith %} 
                    {% elif "item_platform|" in value %}
                        {% with column_display=value|display_column_items:request %}
                            search_setting_item_tagify.addTags([{ value: "{{column_display|title}}", id: "{{value}}" }]);
                        {% endwith %}
                    {% elif value|search_channel_objects:request %}
                        {% with channel_column=value|search_channel_objects:request %}
                            search_setting_item_tagify.addTags([{ value: "{{channel_column|title}}", id: "{{channel_column}}" }]);
                        {% endwith %}  
                    {% else %}
                        {% if value != 'unset' %}
                            search_setting_item_tagify.addTags([{ value: "{{value|display_column_items:request}}", id: "{{value}}" }]);
                        {% endif %}
                    {% endif %}
                {% endfor %}
            {% endwith %}
        {% endif %}
    {% endif %}

    
</script>
{% endblock js %}
