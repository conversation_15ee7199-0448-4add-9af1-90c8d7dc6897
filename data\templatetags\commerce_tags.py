"""
Commerce and e-commerce template tags for handling shop, orders, items, and platform operations.
"""

from django import template
from data.constants.constant import CURRENCY_MODEL
from data.models import *
from utils.properties.properties import get_page_object
from utils.utility import get_workspace
import ast

register = template.Library()


@register.filter(name="subscription_platform_name")
def subscription_platform_name(sub_id, platform_name):
    platform_name, _ = platform_name.split("-subscription id")
    subscription = ShopTurboSubscriptions.objects.get(id=sub_id)
    try:
        text = subscription.get_display_name()[platform_name]
        if text:
            return subscription.get_display_name()[platform_name]
        else:
            return ""
    except Exception:
        return ""


@register.filter(name="item_platform_name")
def item_platform_name(item_id, platform_name):
    platform_name = platform_name.lower()
    if "-item id" in platform_name and platform_name.startswith("item_platform|"):
        id_, _ = platform_name.replace("item_platform|", "").split("-item id")
        try:
            item = ShopTurboItems.objects.get(id=item_id)
        except:
            return ""
        try:
            data = item.get_display_name()[id_]
            return data if data else ""
        except Exception:
            return ""
    elif "-sku" in platform_name and platform_name.startswith("item_platform|"):
        id_, _ = platform_name.replace("item_platform|", "").split("-sku")
        try:
            item = ShopTurboItems.objects.get(id=item_id)
        except:
            return ""
        try:
            data = item.get_platform_sku()[id_]
            return data if data else ""
        except Exception:
            return ""
    elif "-variant id" in platform_name and platform_name.startswith("item_platform|"):
        id_, _ = platform_name.replace("item_platform|", "").split("-variant id")
        try:
            item = ShopTurboItems.objects.get(id=item_id)
        except:
            return ""
        try:
            data = item.get_platform_variant()[id_]
            return data if data else ""
        except Exception:
            return ""
    else:
        return ""


@register.filter(name="get_item_platforms")
def get_item_platforms(item: ShopTurboItems):
    platforms_ids = []
    platforms = item.item.filter(platform_type="default")
    for platform in platforms:
        platforms_ids.append(platform.platform_id)

    return platforms_ids


@register.filter(name="inventory_platform_name")
def inventory_platform_name(inventory_id, platform_name):
    if "-inventory id" in platform_name:
        platform_name = platform_name.split("-inventory id")[0]
        inventory = ShopTurboInventory.objects.filter(id=inventory_id).first()
        if inventory:
            return inventory.get_display_name().get(platform_name, "")
        else:
            return ""
    elif "-sku" in platform_name:
        platform_name = platform_name.split("-sku")[0]
        inventory = ShopTurboInventory.objects.filter(id=inventory_id).first()
        if inventory:
            data = inventory.get_platform_sku().get(platform_name, "")
            return data if data else ""
    elif "-variant id" in platform_name:
        platform_name = platform_name.split("-variant id")[0]
        inventory = ShopTurboInventory.objects.filter(id=inventory_id).first()
        if inventory:
            data = inventory.get_platform_variant().get(platform_name, "")
            return data if data else ""
    else:
        return ""


@register.filter(name="get_inventory_platforms")
def get_inventory_platforms(inventory: ShopTurboInventory):
    platforms_ids = []
    platforms = inventory.inventory.all()
    for platform in platforms:
        platforms_ids.append(platform.platform_id)

    return platforms_ids


@register.filter(name="order_platform_name")
def order_platform_name(order_id, platform_name):
    try:
        if "-order id" in platform_name:
            platform_name, _ = platform_name.split("-order id")
        else:
            platform_name, _ = platform_name.split("-deal id")
        order = ShopTurboOrders.objects.filter(id=order_id)
        if order:
            order = order.first()
            return order.get_display_name().get(platform_name)
    except Exception as e:
        print("Error at order_platform_name: ", e)
    return ""


@register.filter(name="order_platform_order_id")
def order_platform_order_id(order_id, platform_name):
    try:
        if "-order id" in platform_name:
            platform_name, _ = platform_name.split("-order id")
        else:
            platform_name, _ = platform_name.split("-deal id")
        order = ShopTurboOrders.objects.filter(id=order_id)
        if order:
            order = order.first()
            return order.get_platform_order_id().get(platform_name)
    except Exception as e:
        print("Error at platform_order_id: ", e)
    return ""


@register.filter(name="order_platform_payment_status")
def order_platform_payment_status(order_id, platform_name):
    platform_name, _ = platform_name.split("-payment status")
    order = ShopTurboOrders.objects.get(id=order_id)
    try:
        return order.get_platform_payment_status()[platform_name]
    except Exception:
        return ""


@register.filter(name="order_platform_paid_at")
def order_platform_paid_at(order, platform_name):
    platform_name, _ = platform_name.split("-paid_at")
    try:
        return order.get_platform_paid_at()[platform_name]
    except Exception:
        return ""


@register.filter(name="order_platform_fulfillment_status")
def order_platform_fulfillment_status(order_id, platform_name):
    platform_name, _ = platform_name.split("-fulfillment status")
    order = ShopTurboOrders.objects.get(id=order_id)
    try:
        return order.get_platform_fulfillment_status()[platform_name]
    except Exception:
        return ""


@register.filter(name="order_platform_order_status")
def order_platform_order_status(order_id, platform_name):
    platform_name, _ = platform_name.split("-order status")
    order = ShopTurboOrders.objects.get(id=order_id)
    try:
        status = order.get_platform_order_status()[platform_name]
        if status:
            return "Activate"
        else:
            return "Deactivate"
    except Exception:
        return ""


@register.filter()
def get_currencies(type):
    return CURRENCY_MODEL


@register.filter()
def get_currencies_text_symbol(currency):
    currency = currency.upper()
    currency_dict = {code: text_symbol for code, text_symbol, _ in CURRENCY_MODEL}

    currency_symbol = currency_dict.get(currency)
    return currency_symbol


@register.simple_tag()
def get_predefined_currency(data):
    currency = None

    if data:
        if data.value:
            try:
                data.value = ast.literal_eval(data.value)
                value_0 = data.value[0]
                if value_0["currency"]:
                    currency = value_0["currency"][0]
            except:
                currency = None

    return currency


@register.simple_tag()
def get_predefined_currency_line_items(data):
    currency = None

    if data:
        if data.value:
            try:
                data.value = ast.literal_eval(data.value)
                value_0 = data.value[0]
                if value_0["currency"]:
                    currency = value_0["currency"]
            except:
                currency = None

    return currency


@register.filter()
def get_order_items(order):
    return ShopTurboItems.objects.filter(
        id__in=order.shopturboitemsorders_set.exclude(item=None).values_list(
            "item__id", flat=True
        )
    )


@register.filter()
def get_purchase_order_items(obj):
    return ShopTurboItems.objects.filter(
        id__in=obj.purchase_order_object.exclude(item=None).values_list(
            "item__id", flat=True
        )
    )


@register.filter()
def get_order(order_id):
    try:
        return ShopTurboOrders.objects.get(id=order_id)
    except:
        return None


@register.filter()
def get_purchase_order(po_id):
    try:
        return PurchaseOrders.objects.get(id=po_id)
    except:
        return None


@register.simple_tag(name="get_customer_item_price")
def get_customer_item_price(
    item: ShopTurboItems = None,
    obj_id=None,
    obj_type=None,
    price_precentage=0,
    tax_precentage=0,
):
    customer_item_price = {"price": 0, "tax": 0, "discount_rate": 0}
    if price_precentage:
        shopturbo_price = ShopTurboItemsPrice.objects.filter(
            item=item, default=True
        ).first()
        if shopturbo_price:
            if not shopturbo_price.price:
                shopturbo_price.price = 0
            if type(price_precentage) == str:
                price_precentage = float(price_precentage)
            customer_item_price = {
                "price": shopturbo_price.price * price_precentage / 100,
                "tax": tax_precentage,
                "discount_rate": price_precentage,
            }
    else:
        if item and obj_id and obj_type:
            if obj_type == TYPE_OBJECT_CONTACT:
                contact = Contact.objects.get(id=obj_id)
                customer_item_price = ShopTurboCustomerItemsPrice.objects.filter(
                    item=item, contact=contact
                ).first()

            elif obj_type == TYPE_OBJECT_COMPANY:
                company = Company.objects.get(id=obj_id)
                customer_item_price = ShopTurboCustomerItemsPrice.objects.filter(
                    item=item, company=company
                ).first()

    return customer_item_price


@register.filter
def get_billings_items(obj, object_type):
    try:
        page_obj = get_page_object(object_type)

        if not page_obj:
            print("Error: get_page_object returned None or empty.")
            return None

        item_model = page_obj.get("item_model")  # Use .get for safety
        field_item_name = page_obj.get("field_item_name")  # Use .get for safety

        if not item_model or not field_item_name:
            return None

        filter_kwargs = {field_item_name: obj}

        items = item_model.objects.filter(**filter_kwargs).order_by("created_at")

        if items:
            items_list = []
            for item in items:
                item_display = ""  # Initialize display string
                # Check attribute existence first
                if hasattr(item, "item_link") and item.item_link:
                    item_display = f"#{getattr(item.item_link, 'item_id', 'N/A')}| {getattr(item.item_link, 'name', 'N/A')}"
                elif hasattr(item, "item_name"):  # Check attribute existence
                    item_display = getattr(item, "item_name", "N/A")
                else:
                    item_display = (
                        f"Item object (ID: {item.id}) has no 'item_link' or 'item_name'"
                    )
                items_list.append(item_display)

            return items_list

        return None
    except Exception as e:
        import traceback

        print("!!! Exception occurred in get_billings_items !!!")
        print(f"Error Type: {type(e).__name__}")
        print(f"Error Message: {e}")
        traceback.print_exc()  # Print the full traceback
        return None


@register.filter
def get_billings_items_count(obj, object_type):
    try:
        page_obj = get_page_object(object_type)

        if not page_obj:
            print("Error: get_page_object returned None or empty.")
            return None

        item_model = page_obj.get("item_model")  # Use .get for safety
        field_item_name = page_obj.get("field_item_name")  # Use .get for safety

        if not item_model or not field_item_name:
            return None

        filter_kwargs = {field_item_name: obj}

        items = item_model.objects.filter(**filter_kwargs).order_by("created_at")

        if items:
            items_list = []
            for item in items:
                item_display = ""  # Initialize display string
                if hasattr(item, "amount_item") and item.amount_item:
                    item_display = item.amount_item
                # Convert to int if applicable
                try:
                    item_display = int(item_display)
                except:
                    pass
                items_list.append(item_display)

            return items_list

        return None
    except Exception as e:
        import traceback

        print("!!! Exception occurred in get_billings_items !!!")
        print(f"Error Type: {type(e).__name__}")
        print(f"Error Message: {e}")
        traceback.print_exc()  # Print the full traceback
        return None


@register.filter
def get_billings_items_id(obj, object_type):
    try:
        page_obj = get_page_object(object_type)

        if not page_obj:
            print("Error: get_page_object returned None or empty.")
            return None

        item_model = page_obj.get("item_model")  # Use .get for safety
        field_item_name = page_obj.get("field_item_name")  # Use .get for safety

        if not item_model or not field_item_name:
            return None

        filter_kwargs = {field_item_name: obj}

        items = item_model.objects.filter(**filter_kwargs).order_by("created_at")

        if items:
            items_list = []
            for item in items:
                item_display = ""  # Initialize display string
                if hasattr(item, "item_link") and item.item_link:
                    item_display = item.item_link.id

                items_list.append(item_display)

            return items_list

        return None
    except Exception as e:
        import traceback

        print("!!! Exception occurred in get_billings_items !!!")
        print(f"Error Type: {type(e).__name__}")
        print(f"Error Message: {e}")
        traceback.print_exc()  # Print the full traceback
        return None


@register.filter(name="contact_platform_name")
def contact_platform_name(contact_id, row_type):
    """
    Get the platform contact ID for a contact based on the row_type
    This filter was missing after custom tags decomposition
    """
    try:
        if not contact_id or not row_type:
            return ""

        # Extract platform name from row_type (format: "{platform} - contact id")
        if " - contact id" not in row_type.lower():
            return ""

        platform_name = row_type.split(" - contact id")[0].strip()

        # Get the contact object
        contact = Contact.objects.filter(id=contact_id).first()
        if not contact:
            return ""

        # Get the contact platform that matches the channel name
        contact_platform = ContactsPlatforms.objects.filter(
            contact=contact, channel__name__iexact=platform_name
        ).first()

        if contact_platform and contact_platform.platform_id:
            return contact_platform.platform_id

        return ""
    except Exception as e:
        print(f"Error in contact_platform_name filter: {e}")
        return ""


@register.filter(name="company_platform_name")
def company_platform_name(company_id, row_type):
    """
    Get the platform company ID for a company based on the row_type
    This filter was missing after custom tags decomposition
    """
    try:
        if not company_id or not row_type:
            return ""

        # Extract platform name from row_type (format: "{platform} - company id")
        if " - company id" not in row_type.lower():
            return ""

        platform_name = row_type.split(" - company id")[0].strip()

        # Get the company object
        company = Company.objects.filter(id=company_id).first()
        if not company:
            return ""

        # Get the company platform that matches the channel name
        company_platform = CompanyPlatforms.objects.filter(
            company=company, channel__name__iexact=platform_name
        ).first()

        if company_platform and company_platform.platform_id:
            return company_platform.platform_id

        return ""
    except Exception as e:
        print(f"Error in company_platform_name filter: {e}")
        return ""
