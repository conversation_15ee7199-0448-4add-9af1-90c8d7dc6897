from datetime import timedelta
from typing import Optional
from pydantic import BaseModel, Field

from django.db.models import Q
from asgiref.sync import sync_to_async

from data.models import ShopTurboOrders, Invoice, Estimate, Workspace, User
from data.orders.utils.order_total_amount_fix import orders_total_fix_bulk_async
from data.invoice.utils.invoice_total_amount_fix import invoices_total_fix_bulk_async
from data.estimate.utils.estimate_total_amount_fix import estimates_total_fix_bulk_async
from utils.logger import logger
from utils.bgjobs.hatchet_client import hatchet
from hatchet_sdk import Context

from utils.bgjobs.handler import set_bg_job_running, set_bg_job_completed, set_bg_job_failed


class FixTotalPricePayload(BaseModel):
    user_id: str
    workspace_id: str
    background_job_id: Optional[str] = Field(default="")
    
    # Backward compatibility (Deprecated soon)
    function: Optional[str] = Field(default="")
    workspace: Optional[str] = Field(default="")
    job_id: Optional[str] = Field(default="")
    payload: Optional[dict] = Field(default_factory=dict)


@hatchet.task(name="FixTotalPrice", input_validator=FixTotalPricePayload, execution_timeout=timedelta(hours=12), schedule_timeout=timedelta(hours=1))
async def fix_total_price(input: FixTotalPricePayload, ctx: Context):
    """
    Background job to fix order, invoice, and estimate total prices based on decimal point configuration.
    This runs when workspace decimal format settings are updated.
    First fixes orders, then fixes invoices, then fixes estimates.
    
    Args:
        input: Payload containing workspace_id, user_id and background_job_id
        ctx: Hatchet context (required by framework but not used)
    """
    workspace_id = input.workspace_id if input.workspace_id != "None" else None or input.workspace
    
    if input.background_job_id or input.job_id:
        await sync_to_async(set_bg_job_running)(input.background_job_id or input.job_id)

    try:
        workspace = await Workspace.objects.aget(id=workspace_id)
    except Workspace.DoesNotExist:
        logger.warning("[DEBUG] - fix_total_price - Workspace does not exist")
        if input.background_job_id or input.job_id:
            await sync_to_async(set_bg_job_failed)(input.background_job_id or input.job_id)
        return

    try:
        user = await User.objects.aget(id=int(input.user_id))
        logger.info(f"[DEBUG] - fix_total_price - Processing request from user {user.email}")
    except User.DoesNotExist:
        logger.warning("[DEBUG] - fix_total_price - User does not exist")
        if input.background_job_id or input.job_id:
            await sync_to_async(set_bg_job_failed)(input.background_job_id or input.job_id)
        return

    try:
        # Step 1: Fix orders total prices
        logger.info(f"[DEBUG] - fix_total_price - Starting orders total price fix for workspace {workspace_id}")
        
        # Get all active orders for the workspace (async)
        orders_queryset = ShopTurboOrders.objects.filter(
            Q(workspace=workspace) & (Q(status='active') | Q(status__isnull=True))
        ).order_by('-order_id')
        
        # Get count asynchronously
        orders_count = await orders_queryset.acount()
        logger.info(f"[DEBUG] - fix_total_price - Processing {orders_count} orders for workspace {workspace_id}")
        
        # Run the async bulk fix operation for orders
        updated_orders = await orders_total_fix_bulk_async(orders_queryset, workspace)
        
        logger.info(f"[DEBUG] - fix_total_price - Successfully updated {len(updated_orders)} orders")
        
        # Step 2: Fix invoices total prices
        logger.info(f"[DEBUG] - fix_total_price - Starting invoices total price fix for workspace {workspace_id}")
        
        # Get all active invoices for the workspace (async)
        invoices_queryset = Invoice.objects.filter(
            Q(workspace=workspace) & (Q(usage_status='active') | Q(usage_status__isnull=True))
        ).order_by('-id_inv')
        
        # Get count asynchronously
        invoices_count = await invoices_queryset.acount()
        logger.info(f"[DEBUG] - fix_total_price - Processing {invoices_count} invoices for workspace {workspace_id}")
        
        # Run the async bulk fix operation for invoices
        updated_invoices = await invoices_total_fix_bulk_async(invoices_queryset, workspace)
        
        logger.info(f"[DEBUG] - fix_total_price - Successfully updated {len(updated_invoices)} invoices")
        
        # Step 3: Fix estimates total prices
        logger.info(f"[DEBUG] - fix_total_price - Starting estimates total price fix for workspace {workspace_id}")
        
        # Get all active estimates for the workspace (async)
        estimates_queryset = Estimate.objects.filter(
            Q(workspace=workspace) & (Q(usage_status='active') | Q(usage_status__isnull=True))
        ).order_by('-id_est')
        
        # Get count asynchronously
        estimates_count = await estimates_queryset.acount()
        logger.info(f"[DEBUG] - fix_total_price - Processing {estimates_count} estimates for workspace {workspace_id}")
        
        # Run the async bulk fix operation for estimates
        updated_estimates = await estimates_total_fix_bulk_async(estimates_queryset, workspace)
        
        logger.info(f"[DEBUG] - fix_total_price - Successfully updated {len(updated_estimates)} estimates")
        
        # Summary log
        logger.info(f"[DEBUG] - fix_total_price - Completed total price fix for workspace {workspace_id}: {len(updated_orders)} orders, {len(updated_invoices)} invoices, and {len(updated_estimates)} estimates updated")
        
        if input.background_job_id or input.job_id:
            await sync_to_async(set_bg_job_completed)(input.background_job_id or input.job_id)
            
    except Exception as e:
        logger.error(f"[DEBUG] - fix_total_price - Error processing total price fix: {e}")
        if input.background_job_id or input.job_id:
            await sync_to_async(set_bg_job_failed)(input.background_job_id or input.job_id)
        raise e