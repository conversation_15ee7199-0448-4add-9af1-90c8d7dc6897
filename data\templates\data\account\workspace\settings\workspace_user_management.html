{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}


{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}


<div class="mt-5" style="width: 100%;">
    <div class="mt-5 container-fluid px-0 scroll-y scoll-x">
        <div class="d-flex align-items-center mb-10">
            <label class="d-flex align-items-center fs-3 fw-bolder me-2">
                <span class="">
                    {{setting_type|object_group_type:request}}
                    {% if LANGUAGE_CODE == 'ja' %}
                    オブジェクト
                    {% else %}
                    Object
                    {% endif %}
                </span>
            </label>
        </div>

        <div class="mb-10"
            hx-get="{% host_url 'properties' host 'app' %}"
            hx-vals='{"page_group_type": "{{setting_type}}"}'
            hx-trigger="load"
            hx-target="#properties-table"
        >
            <div id="properties-table"></div>
        </div>
    </div>
</div>

{% endblock %}