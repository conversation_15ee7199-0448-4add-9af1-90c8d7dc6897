import asyncio
import json
import re
import uuid
from io import String<PERSON>
from typing import Dict, Optional, Union

import chardet
import pandas as pd
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.db.models import OuterRef, Prefetch, Q, Subquery
from django.db import transaction

# Signal
from django.http import Http404, HttpRequest, HttpResponse, HttpResponseRedirect
from django.shortcuts import redirect, render
from django.template.loader import render_to_string
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse

from data.constants.constant import (
    APP_TARGET_SLUG,
    ITEMS_COLUMNS_DISPLAY,
    ITEM_USAGE_CATEGORY,
    INVENTORY_USAGE_CATEGORY,
    INVENTORY_TRANSACTION_USAGE_CATEGORY,
)
from data.constants.properties_constant import OBJECT_TYPE_TO_SLUG, TYPE_OBJECT_ITEM
from data.constants.shopturbo_constant import TYPE_SHOPIFY_SYNC_KEY_PLATFORM
from data.custom_pdf import generate_pdf_bytes
from data.models import (
    Channel,
    InventoryTransaction,
    ItemPurchasePrice,
    Module,
    Notification,
    PropertySet,
    ShopTurboCustomerItemsPrice,
    ShopTurboInventory,
    ShopTurboItemComponents,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsPlatforms,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
    ShopTurboItemsVariations,
    TransferHistory,
    View,
    ViewFilter,
)
from utils.amazon import *
from utils.bcart import *
from utils.date import parse_date
from utils.decorator import login_or_hubspot_required
from utils.eccube import *
from utils.ecforce_bg_jobs.ecforce import *
from utils.filter import (
    build_view_filter,
    check_columns_type,
    create_choice_field_filter_condition,
)
from utils.filter_utils.general_filter import validate_filter_conditions
from utils.freee_bg_jobs.freee import *
from utils.hubspot import *
from utils.inventory import *
from utils.inventory import create_inventory_transaction_helper
from utils.bgjobs.runner import trigger_bg_job
from utils.logger import logger
from utils.makeshop import *
from utils.meter import has_quota
from utils.nextengine import *
from utils.project import get_ordered_views
from utils.properties.properties import get_default_property_set, get_page_object
from utils.rakuten_bg_jobs.rakuten_orders import pull_rakuten_items, push_rakuten_items
from utils.salesforce import *
from utils.seal_subscription import *
from utils.serializer import *
from utils.shopify_bg_job.shopify_orders import *
from utils.square import *
from utils.stripe.stripe import *
from utils.temu import push_temu_items
from utils.utility import (
    apply_item_search_setting,
    build_redirect_url,
    get_redirect_workflow,
    get_workspace,
    is_valid_uuid,
    save_custom_property,
    update_query_params_url,
    assign_object_owner,
    get_permission_filter,
)
from utils.woocommerce import *
from utils.workspace import get_permission
from utils.yahoo import *

from utils.bgjobs.handler import create_bg_job, add_hatchet_run_id
from data.item.background.export_csv_items import ExportCSVItemsPayload, export_csv_items
from data.item.background.import_salesforce_items import ImportSalesforceItemsPayload, import_salesforce_items_task

from django.contrib.auth import get_user_model
from urllib.parse import urlencode

type_http = settings.SITE_URL.split("//")[0]
LANGUAGE_QUERY_PARAMETER = "language"
POSTS_PER_PAGE = 30
POSTS_PER_PAGE_BEGIN = POSTS_PER_PAGE - 1

SHOPTURBO_APP_TARGET = "shopturbo"
SHOPTURBO_APP_SLUG = APP_TARGET_SLUG[SHOPTURBO_APP_TARGET]

# Maximum number of selected items to store in URL to prevent URL length issues
MAX_SELECTED_IDS_IN_URL = 100


def parse_selected_ids(request, workspace):
    """
    Parse and validate selected IDs from URL parameters.
    Returns a set of valid item IDs that belong to the workspace and user has permission to access.
    """
    selected_ids_param = request.GET.get('selected_ids', '')
    if not selected_ids_param:
        return set()

    try:
        # Parse comma-separated IDs, limit to prevent URL abuse
        selected_ids_list = selected_ids_param.split(',')[:MAX_SELECTED_IDS_IN_URL]
        selected_ids = set(id.strip() for id in selected_ids_list if id.strip())

        if not selected_ids:
            return set()

        # Validate IDs belong to workspace and are valid UUIDs
        from utils.utility import is_valid_uuid
        valid_uuid_ids = [id for id in selected_ids if is_valid_uuid(id)]

        if not valid_uuid_ids:
            return set()

        # Get permission filter for the user
        permission = get_permission(object_type=TYPE_OBJECT_ITEM, user=request.user)
        if not permission or permission == "hide":
            return set()

        permission_filter = get_permission_filter(permission, request.user)

        # Validate IDs belong to workspace and user has permission
        valid_items = ShopTurboItems.objects.filter(
            id__in=valid_uuid_ids,
            workspace=workspace
        ).filter(permission_filter).values_list('id', flat=True)

        return set(str(id) for id in valid_items)
    except Exception as e:
        # Log the error but don't break the page
        print(f"Error parsing selected IDs: {e}")
        return set()


def build_pagination_url(base_url, selected_ids=None, **params):
    """
    Build pagination URL with preserved selections and other parameters.
    """
    url_params = {}

    # Add selected IDs if present and within limits
    if selected_ids and len(selected_ids) <= MAX_SELECTED_IDS_IN_URL:
        url_params['selected_ids'] = ','.join(selected_ids)

    # Add other parameters
    url_params.update(params)

    # Remove None values
    url_params = {k: v for k, v in url_params.items() if v is not None}

    if url_params:
        return f"{base_url}?{urlencode(url_params)}"
    return base_url


def get_appropriate_page_after_bulk_operation(request):
    """
    Simple function to get appropriate page number after bulk operations.

    This function handles pagination preservation by:
    1. Preserving the current page number from the request
    2. Falling back to page 1 if no page is specified

    Args:
        request: The HTTP request object

    Returns:
        int: The appropriate page number to redirect to
    """
    try:
        current_page = int(request.GET.get("page", 1))
        print(f"DEBUG: get_appropriate_page_after_bulk_operation - current_page from request: {current_page}")
        return max(1, current_page)  # Ensure page is at least 1
    except (ValueError, TypeError) as e:
        print(f"DEBUG: get_appropriate_page_after_bulk_operation - error parsing page, defaulting to 1: {e}")
        return 1


def get_selected_items_context(selected_ids, workspace, permission):
    """
    Get context information about selected items for template rendering.
    """
    if not selected_ids:
        return {
            'selected_ids': set(),
            'total_selected_count': 0,
            'selected_items_preview': []
        }

    try:
        permission_filter = get_permission_filter(permission, None)  # We already validated permission

        # Get basic info about selected items for preview
        selected_items = ShopTurboItems.objects.filter(
            id__in=selected_ids,
            workspace=workspace
        ).filter(permission_filter).values('id', 'name', 'item_id')[:10]  # Limit preview to 10 items

        return {
            'selected_ids': selected_ids,
            'total_selected_count': len(selected_ids),
            'selected_items_preview': list(selected_items)
        }
    except Exception as e:
        print(f"Error getting selected items context: {e}")
        return {
            'selected_ids': set(),
            'total_selected_count': 0,
            'selected_items_preview': []
        }


def get_bulk_action_item_ids(request, workspace):
    """
    Get item IDs for bulk actions from both traditional checkboxes and cross-page selections.
    Returns a list of item IDs that are valid for the workspace and user permissions.
    """
    item_ids = []

    # First, try to get from traditional checkbox selections
    checkbox_ids = request.POST.getlist("checkbox")
    if checkbox_ids:
        item_ids.extend(checkbox_ids)

    # Then, try to get from selected_ids parameter (cross-page selections)
    selected_ids_param = request.POST.get("selected_ids", "")
    if selected_ids_param and not checkbox_ids:  # Only use selected_ids if no checkboxes
        try:
            selected_ids = selected_ids_param.split(',')
            selected_ids = [id.strip() for id in selected_ids if id.strip()]
            item_ids.extend(selected_ids)
        except Exception as e:
            print(f"Error parsing selected_ids parameter: {e}")

    # Remove duplicates and validate
    item_ids = list(set(item_ids))

    # Validate that all IDs are valid UUIDs and belong to the workspace
    from utils.utility import is_valid_uuid
    valid_ids = []
    for item_id in item_ids:
        if is_valid_uuid(item_id):
            # Check if item exists in workspace
            if ShopTurboItems.objects.filter(id=item_id, workspace=workspace).exists():
                valid_ids.append(item_id)

    return valid_ids


def get_bulk_action_items(request, workspace):
    """
    Get items for bulk actions from either checkbox selections or selected_ids parameter.
    Returns a list of item IDs that are valid for the workspace and user permissions.
    """
    item_ids = []

    # First, try to get from traditional checkbox selections
    checkbox_ids = request.POST.getlist("checkbox")
    if checkbox_ids:
        item_ids.extend(checkbox_ids)

    # Then, try to get from selected_ids parameter (cross-page selections)
    selected_ids_param = request.POST.get("selected_ids", "")
    if selected_ids_param:
        try:
            selected_ids = [id.strip() for id in selected_ids_param.split(",") if id.strip()]
            item_ids.extend(selected_ids)
        except Exception as e:
            print(f"Error parsing selected_ids parameter: {e}")

    # Remove duplicates and validate
    item_ids = list(set(item_ids))

    if not item_ids:
        return []

    # Validate that all IDs are valid UUIDs and belong to the workspace
    from utils.utility import is_valid_uuid
    valid_ids = [id for id in item_ids if is_valid_uuid(id)]

    if not valid_ids:
        return []

    # Get permission filter
    permission = get_permission(object_type=TYPE_OBJECT_ITEM, user=request.user)
    if not permission or permission == "hide":
        return []

    permission_filter = get_permission_filter(permission, request.user)

    # Validate items exist and user has permission
    valid_items = ShopTurboItems.objects.filter(
        id__in=valid_ids,
        workspace=workspace
    ).filter(permission_filter).values_list('id', flat=True)

    return [str(id) for id in valid_items]


@login_or_hubspot_required
def shopturbo_items(request):
    if (
        not (
            request.path.startswith("/modules/")
            or request.path.startswith("/ja/modules/")
        )
        and request.method == "GET"
    ):
        return redirect(reverse("main", host="app"))

    lang = request.LANGUAGE_CODE
    if lang == "ja":
        page_title = "商品"
    else:
        page_title = "Items"

    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse("start", host="app"))

    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=workspace, app_target=SHOPTURBO_APP_TARGET
    )
    # Set Default Search Setting if first time open Orders
    if not app_setting.search_setting_item:
        app_setting.search_setting_item = "name"
        app_setting.save()

    page_obj = get_page_object(TYPE_OBJECT_ITEM, lang)
    base_model = page_obj["base_model"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    custom_value_file_model = page_obj["custom_value_file_model"]
    custom_value_file_relation = page_obj["custom_value_file_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    id_field = page_obj["id_field"]
    if request.method == "GET":
        # Working Hours
        tag_slug = request.GET.get("tag_slug")
        config_view = None
        archive = None
        import_id = request.GET.get("imported")
        view_id = request.GET.get("view_id", None)
        if view_id == "None":  # Explicitly check for the string "None"
            view_id = None  # Convert to Python's None type
        search_q = request.GET.get("q")

        permission = get_permission(object_type=TYPE_OBJECT_ITEM, user=request.user)
        if not permission:
            permission = "hide"

        if permission == "hide":
            context = {
                "permission": permission,
                "page_title": page_title,
            }
            return render(request, "data/shopturbo/items.html", context)

        views = get_ordered_views(workspace, TYPE_OBJECT_ITEM, user=request.user)
        view = None
        if view_id:
            view = View.objects.filter(id=view_id).first()
            if not view or (
                view.is_private and (not request.user or view.user != request.user)
            ):
                view, _ = View.objects.get_or_create(
                    workspace=workspace, title__isnull=True, target=TYPE_OBJECT_ITEM
                )
                view_id = view.id
        else:
            try:
                view, _ = View.objects.get_or_create(
                    workspace=workspace, title__isnull=True, target=TYPE_OBJECT_ITEM
                )
            except:
                view = View.objects.filter(
                    workspace=workspace, title__isnull=True, target=TYPE_OBJECT_ITEM
                ).first()
            view_id = view.id

        view_filter, is_new_view_filter = ViewFilter.objects.get_or_create(view=view)
        if is_new_view_filter:
            view_filter.view_type = "list"
            view_filter.save()

        config_view = view_filter.view_type
        archive = view_filter.archive

        pagination_number = view_filter.pagination
        if not pagination_number:
            pagination_number = 20

        # Filter by Search Keywords
        filter_conditions = Q(workspace=workspace)

        filter_conditions &= get_permission_filter(
            permission, request.user
        )

        # Handle status filter for archived items
        status = request.GET.get("status")
        if status == "archived":
            filter_conditions &= Q(status="archived")
        else:
            # By default, show only active items
            filter_conditions &= Q(status="active")

        import_history = TransferHistory.objects.filter(
            id=import_id, workspace=workspace
        ).first()
        advance_search = None
        advance_search_filter = {}
        if import_history:
            imported_ids = import_history.checkpoint_details["imported"]
            if imported_ids:
                filter_conditions &= Q(item_id__in=imported_ids)
            else:
                filter_conditions &= Q(item_id__in=[])  # to show no items
        else:
            advance_search = AdvanceSearchFilter.objects.filter(
                workspace=workspace, object_type=TYPE_OBJECT_ITEM, type="default"
            ).first()

            if advance_search:
                if advance_search.search_settings:
                    app_setting.search_setting_item = advance_search.search_settings
                    app_setting.save()
            print("search settings:", app_setting.search_setting_item)

            if search_q:
                match_special_char = re.search(r"#(\d+)", search_q)
                if match_special_char:
                    number = match_special_char.group(1)  # Get the number part
                    if number:
                        from utils.utility import get_item_id_search_filter

                        filter_conditions &= get_item_id_search_filter(number)
                else:
                    search_ = search_q.split()
                    for search_key in search_:
                        ##### BE CAREFUL TO INCLUDE WORKSPACE ####
                        from utils.utility import get_item_id_search_filter

                        filter_conditions &= apply_item_search_setting(
                            app_setting, search_key.lower()
                        ) | get_item_id_search_filter(search_key.lower())

            if advance_search:
                if advance_search.is_active:
                    advance_search_filter = advance_search.search_filter
                    advance_search_filter_status = advance_search.search_filter_status
                    if advance_search_filter_status:
                        advance_search_filter = {
                            k: v
                            for k, v in advance_search_filter.items()
                            if v["value"] != ""
                            and advance_search_filter_status.get(k) != "False"
                        }
                    else:
                        advance_search_filter = {
                            k: v
                            for k, v in advance_search_filter.items()
                            if v["value"] != ""
                        }
                    try:
                        if (
                            advance_search_filter.get("usage_status", {}).get("value")
                            == "all"
                        ):
                            del advance_search_filter["usage_status"]
                    except KeyError:
                        pass

                    view_filter.filter_value = advance_search_filter

            filter_conditions = apply_shopturboitems_view_filter(
                filter_conditions, view_filter, request=request)
            print('log filter', filter_conditions)

        shopturbo_items_columns = DEFAULT_COLUMNS_ITEM.copy()
        if view_filter and view_filter.column:
            shopturbo_items_columns = ast.literal_eval(view_filter.column)
            for idx, column in enumerate(["checkbox", "item_id"]):
                shopturbo_items_columns.insert(idx, column)
            shopturbo_items_columns = [data.lower() for data in shopturbo_items_columns]

        if view_filter.view_type == "group":
            shopturbo_items = []
            shopturbo_items_columns.insert(0, "dropdown")
            cf_id = view_filter.group_customfield
            field_name = ShopTurboItemsNameCustomField.objects.filter(
                id=cf_id, workspace=workspace
            ).first()

            if field_name and field_name.type == "components":
                components_qs = (
                    ShopTurboItemComponents.objects.filter(
                        property__field_name=field_name
                    )
                    .select_related("item")
                    .distinct()
                )

                components_items = components_qs.values_list("item")

                # Now get the parent items
                parent_items = (
                    ShopTurboItems.objects.filter(
                        filter_conditions, id__in=components_items
                    )
                    .prefetch_related(
                        Prefetch(
                            "shopturbo_item_custom_field_relations",
                            queryset=ShopTurboItemsValueCustomField.objects.select_related(
                                "field_name"
                            ),
                        )
                    )
                    .order_by("-item_id")
                )

                other_items = (
                    ShopTurboItems.objects.filter(filter_conditions)
                    .exclude(id__in=components_items)
                    .prefetch_related(
                        Prefetch(
                            "shopturbo_item_custom_field_relations",
                            queryset=ShopTurboItemsValueCustomField.objects.select_related(
                                "field_name"
                            ),
                        )
                    )
                    .order_by("-item_id")
                )

                other_items_count = other_items.count()
                other_items = other_items[:pagination_number]

                shopturbo_items = {
                    "parent_items": parent_items,
                    "other_items": {
                        "items": other_items,
                        "is_more": other_items_count > pagination_number,
                    },
                }

            # Initialize all_shopturbo_items for group view
            try:
                all_shopturbo_items = list(ShopTurboItems.objects.filter(filter_conditions).values_list('id', flat=True))
                all_shopturbo_items = [str(item_id) for item_id in all_shopturbo_items]
            except Exception as e:
                print(f"===== Error getting item IDs for group view: {e} =====")
                all_shopturbo_items = []

            paginator_item_begin = None
            paginator_item_end = None
            page_content = None
            paginator = None
            page_number = None
            shopturbo_items = shopturbo_items
        else:
            shopturbo_items_base_qs = ShopTurboItems.objects.filter(filter_conditions)
            try:
                if view_filter.sort_order_by:
                    order_method = view_filter.sort_order_method
                    order_by = view_filter.sort_order_by

                    if is_valid_uuid(order_by):
                        field_name = ShopTurboItemsNameCustomField.objects.filter(
                            id=order_by
                        ).first()
                        if field_name:
                            custom_value_subquery = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    items=OuterRef("pk"), field_name=field_name
                                )
                            )

                            if field_name.type in ["date", "date_time"]:
                                custom_value_subquery = custom_value_subquery.values(
                                    "value_time"
                                )[:1]
                            else:
                                custom_value_subquery = custom_value_subquery.values(
                                    "value"
                                )[:1]

                            try:
                                shopturbo_items_base_qs = shopturbo_items_base_qs.annotate(
                                    custom_value=Subquery(custom_value_subquery)
                                )

                                if order_method == "asc":
                                    shopturbo_items_base_qs = (
                                        shopturbo_items_base_qs.distinct(
                                            "custom_value", "id"
                                        )
                                        .values("id", "custom_value")
                                        .order_by("custom_value")
                                    )
                                else:
                                    shopturbo_items_base_qs = (
                                        shopturbo_items_base_qs.distinct(
                                            "custom_value", "id"
                                        )
                                        .values("id", "custom_value")
                                        .order_by("-custom_value")
                                    )
                                try:
                                    print(shopturbo_items_base_qs.values("custom_value"))
                                except Exception as debug_e:
                                    print(f"Debug print failed: {debug_e}")
                            except Exception as annotation_e:
                                print(f"Custom field annotation failed: {annotation_e}")
                                # Fallback to default ordering without custom field
                                shopturbo_items_base_qs = (
                                    shopturbo_items_base_qs.distinct("item_id", "id")
                                    .values("id")
                                    .order_by("-item_id")
                                )
                    elif order_by.startswith("item_platform|") and is_valid_uuid(
                        order_by.replace("item_platform|", "")[:36]
                    ):
                        id_ = order_by.replace("item_platform|", "")[:36]
                        if "-SKU" in order_by:
                            values = "platform_sku"
                            channel = Channel.objects.filter(
                                id=id_, workspace=workspace, integration__slug="shopify"
                            ).first()
                        elif "-Item ID" in order_by:
                            values = "platform_id"
                            channel = Channel.objects.filter(
                                id=id_, workspace=workspace
                            ).first()
                        else:
                            values = "variant_id"
                            channel = Channel.objects.filter(
                                id=id_, workspace=workspace, integration__slug="shopify"
                            ).first()
                        inv_platform = ShopTurboItemsPlatforms.objects.filter(
                            item=OuterRef("pk"), channel=channel
                        ).values(values)[:1]

                        try:
                            shopturbo_items_base_qs = shopturbo_items_base_qs.annotate(
                                custom_value=Subquery(inv_platform)
                            )
                        except Exception as platform_annotation_e:
                            print(f"Platform annotation failed: {platform_annotation_e}")
                            # Continue without the annotation
                    else:
                        if order_by == "inventory__available_amount":
                            order_by = "available_inventory_amount"
                        elif order_by == "inventory__unavailable_amount":
                            order_by = "unavailable_inventory_amount"
                        elif order_by == "inventory__committed_amount":
                            order_by = "committed_inventory_amount"
                        elif order_by == "inventory__total_inventory":
                            order_by = "total_inventory"
                        if order_method == "asc":
                            shopturbo_items_base_qs = (
                                shopturbo_items_base_qs.distinct(order_by, "id")
                                .values("id")
                                .order_by(order_by)
                            )
                        else:
                            shopturbo_items_base_qs = (
                                shopturbo_items_base_qs.distinct(order_by, "id")
                                .values("id")
                                .order_by("-" + order_by)
                            )
                else:
                    shopturbo_items_base_qs = (
                        shopturbo_items_base_qs.distinct("item_id", "id")
                        .values("id")
                        .order_by("-item_id")
                    )
            except Exception as e:
                print("===== Debug Error at ShopTurbo Items =====", e)
                print(f"Error type: {type(e).__name__}")
                print(f"Error message: {str(e)}")
                if hasattr(e, '__traceback__'):
                    import traceback
                    traceback.print_exc()
                # Create a completely fresh queryset to avoid any annotation conflicts
                shopturbo_items_base_qs = ShopTurboItems.objects.filter(filter_conditions)
                shopturbo_items_base_qs = (
                    shopturbo_items_base_qs.distinct("item_id", "id")
                    .values("id")
                    .order_by("-item_id")
                )

            # Safely get the item IDs, handling any potential queryset issues
            try:
                all_shopturbo_items = list(shopturbo_items_base_qs.values_list('id', flat=True))
                all_shopturbo_items = [str(all_shopturbo_item) for all_shopturbo_item in all_shopturbo_items]
            except Exception as e:
                print(f"===== Error getting item IDs: {e} =====")
                print(f"Error type: {type(e).__name__}")
                if 'custom_value' in str(e):
                    print("Detected custom_value field error - creating completely fresh queryset")
                # Fallback to a simple query without any complex operations
                try:
                    simple_qs = ShopTurboItems.objects.filter(filter_conditions).values_list('id', flat=True)
                    all_shopturbo_items = [str(item_id) for item_id in simple_qs]
                    # Also reset the base queryset for pagination
                    shopturbo_items_base_qs = ShopTurboItems.objects.filter(filter_conditions).values('id').order_by('-item_id')
                except Exception as e2:
                    print(f"===== Fallback query also failed: {e2} =====")
                    # Ultimate fallback - just get IDs without any ordering
                    ultimate_qs = ShopTurboItems.objects.filter(filter_conditions)
                    all_shopturbo_items = [str(item.id) for item in ultimate_qs]
                    shopturbo_items_base_qs = ultimate_qs.values('id')
            
            paginator = Paginator(shopturbo_items_base_qs, pagination_number)
            page_number = request.GET.get("page", 1)
            try:
                page_content = paginator.page(page_number)
            except PageNotAnInteger:
                page_content = paginator.page(1)
            except EmptyPage:
                page_content = paginator.page(paginator.num_pages)

            # Extract IDs from the paginated queryset and fetch actual objects
            paginated_ids = [item['id'] if isinstance(item, dict) else item for item in page_content.object_list]
            shopturbo_items = (
                ShopTurboItems.objects.filter(id__in=paginated_ids)
                .prefetch_related(
                    Prefetch(
                        "shopturbo_item_custom_field_relations",
                        queryset=ShopTurboItemsValueCustomField.objects.select_related(
                            "field_name"
                        ),
                    )
                )
                .order_by("-item_id")
            )

            paginator_item_begin = (pagination_number * (page_content.number - 1)) + 1
            paginator_item_end = pagination_number * page_content.number

        if not view_filter.view_type:
            view_filter.view_type = "list"
            view_filter.save()

        item_id = request.GET.get("item_id", None)

        # Property Set
        default_property_set = get_default_property_set(
            TYPE_OBJECT_ITEM, workspace, lang
        )
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_ITEM
        ).order_by("created_at")

        set_id = request.GET.get("set_id")
        if view_filter.view.form:
            set_id = view_filter.view.form.id
        else:
            if not set_id:
                property_set_default = property_sets.filter(as_default=True).first()
                if property_set_default:
                    set_id = property_set_default.id

        # delete Temporary Shopturbo Item Price
        ShopTurboItemsPrice.objects.filter(
            workspace=workspace, item__isnull=True
        ).delete()
        ItemPurchasePrice.objects.filter(
            workspace=workspace, item__isnull=True
        ).delete()

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list('id', flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ','.join(str(id) for id in members)

        # Handle selected IDs for cross-page selection
        selected_ids = parse_selected_ids(request, workspace)
        print(f"DEBUG items.py - parsed selected_ids: {selected_ids}")
        print(f"DEBUG items.py - selected_ids type: {type(selected_ids)}")
        print(f"DEBUG items.py - selected_ids length: {len(selected_ids) if selected_ids else 'None'}")

        selected_context = get_selected_items_context(selected_ids, workspace, permission)
        print(f"DEBUG items.py - selected_context: {selected_context}")

        # Build pagination URL with selected IDs preserved
        current_url = request.path
        pagination_url = build_pagination_url(
            current_url,
            selected_ids=selected_ids,
            view_id=view_id,
            status=status,
            q=search_q
        )

        context = {
            "page_title": page_title,
            # NOTE: (app-drawer.html) Use Object Type
            "app_slug": SHOPTURBO_APP_SLUG,
            "tag_slug": tag_slug,
            "search_q": search_q,
            "customfields_map_formula_id": {
                str(ctf.id): ctf
                for ctf in ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace, type="formula"
                )
            },
            "shopturbo_items": shopturbo_items,
            "shopturbo_items_columns": shopturbo_items_columns,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "paginator": paginator,
            "page": page_number,
            "config_view": config_view,
            "view_filter": view_filter,
            "view_id": view_id,
            "target": TYPE_OBJECT_ITEM,
            "views": views,
            "item_id": item_id,
            "property_sets": property_sets,
            "grouped_items": None,
            "shopturbo_items_parent": None,
            "permission": permission,
            "open_drawer": request.GET.get("open_drawer", None),
            "side_drawer": request.GET.get("sidedrawer", ""),
            "set_id": set_id,
            "advance_search": advance_search,
            "advance_search_filter": advance_search_filter,
            "group_members": members,
            # Cross-page selection context
            "selected_ids": selected_context['selected_ids'],
            "total_selected_count": selected_context['total_selected_count'],
            "selected_items_preview": selected_context['selected_items_preview'],
            "pagination_url": current_url,  # Base URL for pagination links
            "all_shopturbo_items":all_shopturbo_items
        }
        if view_filter.view_type == 'price_table':
            context["view_type"] = "price_table"
            context['shopturbo_items_columns'] = ['checkbox', 'item_id',
                                                  'name', 'item_table_price', 'item_table_price_currency', 'item_table_price_tax', 'supplier__company', 'supplier__contact']
        return render(request, "data/shopturbo/items.html", context)
    else:
        module = request.POST.get("module")
        if not module:
            module_obj = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_ITEM
                )
                .order_by("order", "created_at")
                .first()
            )
            if module_obj:
                module = module_obj.slug
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ITEM]

        if "download" in request.POST:
            logger.info(f"EXPORT_JOB: CSV export request received from user {request.user.email} in workspace {workspace.id}")
            logger.info(f"EXPORT_JOB: Request method: {request.method}")
            logger.info(f"EXPORT_JOB: Request path: {request.path}")
            logger.info(f"EXPORT_JOB: POST data keys: {list(request.POST.keys())}")
            logger.info(f"EXPORT_JOB: Download parameter: {request.POST.get('download')}")

            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="export_item",
                name="",
            )
            logger.info(f"EXPORT_JOB: Created TransferHistory with ID: {history.id}")

            download = request.POST.get("download")
            shopturbo_items_columns = request.POST.get("column", None)
            view_id = request.POST.get("view_id")
            encoded_format = request.POST.get("encoded_format", None)

            logger.info(f"EXPORT_JOB: Download type: {download}")
            logger.info(f"EXPORT_JOB: View ID: {view_id}")
            logger.info(f"EXPORT_JOB: Encoded format: {encoded_format}")

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }
            filter_dictionary = json.dumps(filter_dictionary)

            record_ids = ""
            if request.POST.get("record_ids"):
                record_ids = ",".join(request.POST.getlist("record_ids"))
                
            payload = ExportCSVItemsPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                view_id=str(view_id),
                history_id=str(history.id),
                shopturbo_items_columns=shopturbo_items_columns,
                download=download,
                filter_dictionary=filter_dictionary,
                language=lang,
                encoded_format=encoded_format,
                record_ids=record_ids,
            )
                
            job_id = create_bg_job(
                workspace=workspace,
                user=request.user,
                function_name="export_csv_items",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id
                

            # Log export job parameters for debugging
            logger.info(f"EXPORT_JOB: Starting items export for user {request.user.email} in workspace {workspace.id}")
            logger.info(f"EXPORT_JOB: Export parameters - function: {payload.function}, job_id: {job_id}")
            logger.info(f"EXPORT_JOB: View ID: {view_id}, History ID: {history.id}, Language: {lang}")
            logger.info(f"EXPORT_JOB: Columns: {shopturbo_items_columns}")
            logger.info(f"EXPORT_JOB: Filter dictionary: {filter_dictionary}")
            logger.info(f"EXPORT_JOB: Record IDs: {record_ids}")
            
            try:
                ref = export_csv_items.run_no_wait(input=payload)
            except Exception as e:
                logger.error(f"EXPORT_JOB: Exception occurred during export_csv_items: {str(e)}", exc_info=True)
                ref = None
                
            is_running = None
            if ref:
                logger.info(f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}")
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                logger.error(f"EXPORT_JOB: Failed to submit background job for user {request.user.email}")
                is_running = False
            
            if is_running:
                logger.info(f"EXPORT_JOB: Successfully submitted export job for user {request.user.email}")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, csv will be sent to your email.",
                        type="success",
                    )
            else:
                logger.error(f"EXPORT_JOB: Failed to submit export job for user {request.user.email} in workspace {workspace.id}")
                logger.error(f"EXPORT_JOB: trigger_bg_job returned falsy value: {is_running}")
                logger.error(f"EXPORT_JOB: Parameters used: {payload.model_dump(mode='json')}")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブの送信中にエラーが発生しました。サポートに連絡してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="There is an error while submitting the export job. Please contact support.",
                        type="error",
                    )
                history.delete()

            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )
            return HttpResponseRedirect(
                request.META.get("HTTP_REFERER", reverse("main", host="app"))
            )

        elif "bulk_delete_items" in request.POST:
            # Get view_id from request for redirect
            view_id = request.POST.get("view_id")
            print(f"DEBUG: bulk_delete_items - view_id from request: {view_id}")

            if "flag_all" in request.POST:
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ITEM, workspace=workspace
                    ).first()
                filter_conditions = Q(workspace=workspace)
                if view:
                    from django.db.models.fields.related_descriptors import (
                        ForwardManyToOneDescriptor,
                    )

                    if hasattr(view, "viewfilter_set") and isinstance(
                        view.viewfilter_set, ForwardManyToOneDescriptor
                    ):
                        view_filter = view.viewfilter_set.first()
                        if view_filter:
                            filter_conditions = build_view_filter(
                                filter_conditions,
                                view_filter,
                                TYPE_OBJECT_ITEM,
                                force_filter_list=additional_filter_fields,
                            )
                objects = base_model.objects.filter(filter_conditions)
            else:
                # Use the new helper function to get IDs from both checkboxes and cross-page selections
                item_ids = get_bulk_action_item_ids(request, workspace)
                objects = base_model.objects.filter(id__in=item_ids)
            items = objects

            items.update(status="archived")
            sync_usage(workspace, ITEM_USAGE_CATEGORY)

            if items:
                inventory = ShopTurboInventory.objects.filter(
                    workspace=workspace, item__in=items
                )
                if inventory:
                    inventory.update(status="archived")
                    sync_usage(workspace, INVENTORY_USAGE_CATEGORY)

                inventory_transactions = InventoryTransaction.objects.filter(
                    workspace=workspace, inventory__in=inventory
                )
                if inventory_transactions:
                    inventory_transactions.update(usage_status="archived")
                    sync_usage(workspace, INVENTORY_TRANSACTION_USAGE_CATEGORY)

            # Calculate appropriate page after bulk archive operation
            appropriate_page = get_appropriate_page_after_bulk_operation(request)
            print(f"DEBUG: bulk_delete_items - redirecting to page {appropriate_page}")

            # Redirect with preserved pagination
            if module:
                redirect_url = build_redirect_url(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={"module_slug": module, "object_slug": module_object_slug},
                    ),
                    view_id=view_id,
                    page=appropriate_page,
                )
                print(f"DEBUG: bulk_delete_items - redirecting to URL: {redirect_url}")
                return redirect(redirect_url)
            return redirect(reverse("main", host="app"))

        elif "bulk_restore_items" in request.POST:
            # Get view_id from request for redirect
            view_id = request.POST.get("view_id")
            print(f"DEBUG: bulk_restore_items - view_id from request: {view_id}")

            if "flag_all" in request.POST:
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ITEM, workspace=workspace
                    ).first()

                filter_conditions = Q(workspace=workspace)
                if view and hasattr(view, "viewfilter_set"):
                    try:
                        view_filter = view.viewfilter_set.first()
                        if view_filter:
                            filter_conditions = build_view_filter(
                                filter_conditions,
                                view_filter,
                                TYPE_OBJECT_ITEM,
                                force_filter_list=additional_filter_fields,
                            )
                    except Exception as e:
                        print(f"Error getting view filter: {e}")

                objects = base_model.objects.filter(filter_conditions)
            else:
                # Use the new helper function to get IDs from both checkboxes and cross-page selections
                item_ids = get_bulk_action_item_ids(request, workspace)
                objects = base_model.objects.filter(id__in=item_ids)

            items = objects
            sync_usage(workspace, ITEM_USAGE_CATEGORY)
            if workspace and has_quota(workspace, ITEM_USAGE_CATEGORY):
                workspace_available_storage = get_workspace_available_storage(
                    workspace, ITEM_USAGE_CATEGORY
                )
                if (
                    workspace_available_storage is None
                    or workspace_available_storage > 0
                ):
                    items.update(status="active")
                    if workspace:  # Double check since we're using it again
                        sync_usage(workspace, ITEM_USAGE_CATEGORY)

            # Calculate appropriate page after bulk restore operation
            appropriate_page = get_appropriate_page_after_bulk_operation(request)
            print(f"DEBUG: bulk_restore_items - redirecting to page {appropriate_page}")

            # Redirect with preserved pagination
            if module:
                redirect_url = build_redirect_url(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={"module_slug": module, "object_slug": module_object_slug},
                    ),
                    view_id=view_id,
                    page=appropriate_page,
                )
                print(f"DEBUG: bulk_restore_items - redirecting to URL: {redirect_url}")
                return redirect(redirect_url)
            return redirect(reverse("main", host="app"))

        elif "bulk_permanent_delete_items" in request.POST:
            logger.info(f"PERMANENT DELETE: Handler triggered by user {request.user.email} in workspace {workspace.id}")
            logger.info(f"PERMANENT DELETE: Request GET params: {dict(request.GET)}")
            logger.info(f"PERMANENT DELETE: Request POST params: {dict(request.POST)}")

            # Get view_id from request for redirect
            view_id = request.POST.get("view_id")
            print(f"DEBUG: bulk_permanent_delete_items - view_id from request: {view_id}")

            # Safety check: Only allow permanent deletion when viewing archived items
            if request.GET.get('status') != 'archived':
                logger.warning(f"PERMANENT DELETE: Blocked - not on archived page. Status: {request.GET.get('status')}")
                # Redirect back with error message if not on archived page
                return HttpResponseRedirect(
                    request.META.get("HTTP_REFERER", reverse("main", host="app"))
                )

            # Get items to permanently delete
            if "flag_all" in request.POST:
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ITEM, workspace=workspace
                    ).first()

                filter_conditions = Q(workspace=workspace, status="archived")
                if view and hasattr(view, "viewfilter_set"):
                    try:
                        view_filter = view.viewfilter_set.first()
                        if view_filter:
                            filter_conditions = build_view_filter(
                                filter_conditions,
                                view_filter,
                                TYPE_OBJECT_ITEM,
                                force_filter_list=additional_filter_fields,
                            )
                    except Exception as e:
                        logger.error(f"Error getting view filter for permanent deletion: {e}")

                objects = base_model.objects.filter(filter_conditions)
            else:
                # Use the new helper function to get IDs from both checkboxes and cross-page selections
                item_ids = get_bulk_action_item_ids(request, workspace)
                logger.info(f"PERMANENT DELETE: Selected item IDs: {item_ids}")

                # Only allow deletion of archived items
                objects = base_model.objects.filter(
                    id__in=item_ids,
                    workspace=workspace,
                    status="archived"
                )
                logger.info(f"PERMANENT DELETE: Found {objects.count()} archived items to delete")

            items = objects

            if items.count() == 0:
                logger.warning(f"PERMANENT DELETE: No items found for deletion")
                return HttpResponseRedirect(
                    request.META.get("HTTP_REFERER", reverse("main", host="app"))
                )

            # Perform permanent deletion with transaction for data consistency
            try:
                with transaction.atomic():
                    # Log the permanent deletion action
                    item_ids = list(items.values_list('item_id', flat=True))
                    item_names = list(items.values_list('name', flat=True))
                    logger.info(f"PERMANENT DELETE: Starting deletion of items: IDs {item_ids}, Names: {item_names}")

                    # Delete related inventory records first
                    inventory = ShopTurboInventory.objects.filter(
                        workspace=workspace, item__in=items
                    )
                    if inventory:
                        inventory_transactions = InventoryTransaction.objects.filter(
                            workspace=workspace, inventory__in=inventory
                        )
                        if inventory_transactions:
                            inventory_transactions.delete()
                        inventory.delete()

                    # Delete related custom fields
                    # Note: ShopTurboItemsValueCustomField doesn't have workspace field
                    # Filter through items relationship instead

                    ShopTurboItemsValueCustomField.objects.filter(
                        items__in=items
                    ).delete()

                    # Delete related price records
                    ShopTurboItemsPrice.objects.filter(
                        workspace=workspace, item__in=items
                    ).delete()

                    # Delete related purchase price records
                    ItemPurchasePrice.objects.filter(
                        workspace=workspace, item__in=items
                    ).delete()

                    # Delete related component records
                    # Note: ShopTurboItemComponents doesn't have workspace field
                    ShopTurboItemComponents.objects.filter(
                        item__in=items
                    ).delete()

                    # Finally, permanently delete the items themselves
                    deleted_count = items.count()
                    items.delete()

                    # Log the permanent deletion for audit purposes
                    logger.info(f"Permanently deleted {deleted_count} items from workspace {workspace.id}: IDs {item_ids}, Names: {item_names}, User: {request.user.email}")

                    # Note: ID sequence is preserved automatically by PostgreSQL/MySQL
                    # The auto-increment counter is not reset when records are deleted

                    # Update usage metrics
                    sync_usage(workspace, ITEM_USAGE_CATEGORY)
                    sync_usage(workspace, INVENTORY_USAGE_CATEGORY)
                    sync_usage(workspace, INVENTORY_TRANSACTION_USAGE_CATEGORY)

                # Calculate appropriate page after bulk permanent delete operation
                appropriate_page = get_appropriate_page_after_bulk_operation(request)
                print(f"DEBUG: bulk_permanent_delete_items - redirecting to page {appropriate_page}")

                # Redirect with preserved pagination
                if module:
                    redirect_url = build_redirect_url(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={"module_slug": module, "object_slug": module_object_slug},
                        ),
                        view_id=view_id,
                        page=appropriate_page,
                    )
                    print(f"DEBUG: bulk_permanent_delete_items - redirecting to URL: {redirect_url}")
                    return redirect(redirect_url)
                return redirect(reverse("main", host="app"))

            except Exception as e:
                logger.error(f"Error during permanent deletion in workspace {workspace.id}: {e}, User: {request.user.email}")
                # Transaction will be rolled back automatically
                return HttpResponseRedirect(
                    request.META.get("HTTP_REFERER", reverse("main", host="app"))
                )

        elif "task_id" in request.POST:
            task = TransferHistory.objects.filter(
                workspace=workspace, id=request.POST.get("task_id")
            ).first()
            if task:
                task.status = "canceled"
                task.save()

        elif "update-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            view_name = request.POST.get("view_name", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column = request.POST.get("column", None)
            status_selector = request.POST.get("status-selector", None)

            group_selector = request.POST.get("group-selector", None)

            order_by = request.POST.get("order-by", None)
            sort_method = request.POST.get("sort-method", None)
            pagination = request.POST.get("pagination", None)

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if view.is_private and (view.user == request.user):
                    view.is_private = bool(request.POST.get("set-private-view", False))
                    view.user = request.user if view.is_private else None
                    view.save()
            # create
            else:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                view = View.objects.create(
                    workspace=workspace,
                    title=view_name,
                    target=TYPE_OBJECT_ITEM,
                    is_private=is_private,
                    user=user,
                )

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            property_set_is_default_input = request.POST.get(
                "property_set_default", None
            )
            if property_set_is_default_input and view:
                try:
                    default_set = PropertySet.objects.get(
                        id=property_set_is_default_input
                    )
                    if hasattr(view, "form"):
                        view.form = default_set
                        default_set.as_default = True
                        default_set.save()
                        # check default
                        PropertySet.objects.filter(
                            workspace=default_set.workspace, target=default_set.target
                        ).exclude(id=default_set.id).update(as_default=False)
                except PropertySet.DoesNotExist:
                    print(
                        f"PropertySet with id {property_set_is_default_input} does not exist"
                    )

            if view and view_name:
                if hasattr(view, "title"):
                    view.title = view_name

            if view_filter and view_table:
                view_filter.view_type = view_table

            if view_filter:
                if order_by:
                    view_filter.sort_order_by = order_by
                else:
                    view_filter.sort_order_by = None

            if pagination:
                view_filter.pagination = pagination
            else:
                view_filter.pagination = 20

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = "asc"
                else:
                    view_filter.sort_order_method = None

            view_filter.archive = archive

            # convert to normal List
            column = column.split(",")
            if view and column[0]:
                view_filter.column = column
            else:
                view_filter.column = None

            if view_table and status_selector:
                if view_table == "kanban":
                    view_filter.choice_customfield = status_selector
                else:
                    view_filter.choice_customfield = None

            if view_table and group_selector:
                if view_table == "group":
                    view_filter.group_customfield = group_selector
                else:
                    view_filter.group_customfield = None
            else:
                view_filter.group_customfield = None

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            # Handle "between" operation (have two filter values)
            filter_values2 = request.POST.getlist("filter_value-2", None)
            idx_filter_values2 = 0
            for idx, filter_type in enumerate(filter_types):
                value = filter_values[idx]
                if filter_options[idx] == "between":
                    try:
                        value = (
                            f"{filter_values[idx]}-{filter_values2[idx_filter_values2]}"
                        )
                    except:
                        value = ""
                    idx_filter_values2 += 1

                if (
                    str(filter_type) not in filter_dictionary
                    and filter_types.count(str(filter_type)) > 1
                ):
                    filter_dictionary[str(filter_type)] = {
                        "key": [filter_options[idx]],
                        "value": [value],
                    }
                elif str(filter_type) in filter_dictionary:
                    filter_dictionary[str(filter_type)]["key"].append(
                        filter_options[idx]
                    )
                    filter_dictionary[str(filter_type)]["value"].append(value)
                else:
                    filter_dictionary[str(filter_type)] = {
                        "key": filter_options[idx],
                        "value": value,
                    }
            if view_filter and view:
                view_filter.filter_value = filter_dictionary
                view_filter.save()
                view.save()

                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ITEM]
                if view.title and module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module,
                                "object_slug": module_object_slug,
                            },
                        )
                        + "?view_id="
                        + str(view.id)
                    )
                elif module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
            return redirect(reverse("main", host="app"))

        elif "update_bulk_item" in request.POST:
            # Get IDs from both traditional form and cross-page selections
            account_ids = request.POST.getlist("shopturboitems_account_id", None)
            if not account_ids:
                # Fallback to cross-page selections
                account_ids = get_bulk_action_item_ids(request, workspace)

            name = request.POST.get("name", None)
            description = request.POST.get("description", None)
            status = request.POST.get("status", None)

            for account_id in account_ids:
                if not is_valid_uuid(account_id):
                    continue

                item = ShopTurboItems.objects.get(id=account_id)
                if "item_name|checkbox" in request.POST:
                    item.name = name

                if "descriptions|checkbox" in request.POST:
                    item.description = description

                if "status|checkbox" in request.POST:
                    item.status = status

                item.save()

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "update-view-button-delete" in request.POST:
            view_id = request.POST.get("view_id", None)
            if view_id:
                View.objects.get(id=view_id).delete()
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "map-upload-photos" in request.POST:
            img_property = request.POST.get("img-property", None)
            text_property = request.POST.get("text-property", None)

            files = request.FILES.getlist("files[]", None)

            if img_property and text_property and files:
                for file in files:
                    if (
                        file.name.endswith(".jpg")
                        or file.name.endswith(".png")
                        or file.name.endswith(".jpeg")
                    ):
                        file_name = (
                            file.name.replace(".jpg", "")
                            .replace(".png", "")
                            .replace(".jpeg", "")
                        )
                        img_fieldname = ShopTurboItemsNameCustomField.objects.filter(
                            id=img_property
                        ).first()
                        text_fieldname = ShopTurboItemsNameCustomField.objects.filter(
                            id=text_property
                        ).first()
                        if img_fieldname and text_fieldname:
                            mapping_values = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    field_name=text_fieldname, value=file_name
                                )
                            )
                            if mapping_values:
                                for mapping_value in mapping_values:
                                    img_value = (
                                        ShopTurboItemsValueCustomField.objects.filter(
                                            field_name=img_fieldname,
                                            items=mapping_value.items,
                                        ).first()
                                    )
                                    if not img_value:
                                        img_value = ShopTurboItemsValueCustomField.objects.create(
                                            field_name=img_fieldname,
                                            items=mapping_value.items,
                                        )

                                    filename = file.name
                                    file_value, _ = (
                                        ShopTurboItemsValueFile.objects.get_or_create(
                                            valuecustomfield=img_value, name=filename
                                        )
                                    )
                                    file_value.file = file
                                    file_value.save()
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                type="error",
                                message=f"画像ファイルのみアップロードできます, ファイル名: {file.name}",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                type="error",
                                message=f"Only image files can be uploaded, file name: {file.name}",
                            )
                        continue
            else:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        type="error",
                        message="画像とテキストプロパティを選択",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        type="error",
                        message="Please select image and text property",
                    )
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))

            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    type="success",
                    message="アップロードが完了しました",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    type="success",
                    message="Upload Successfully",
                )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "save_item_mapping" in request.POST:
            select_integration_ids = request.POST.get("select_integration_ids")
            channel = Channel.objects.filter(id=select_integration_ids).first()

            item_file_columns = request.POST.getlist("item-file-column", [])
            item_file_columns_name = request.POST.getlist("item-file-column-name", [])
            item_sanka_properties = request.POST.getlist("sanka-item-properties", [])
            item_ignores = request.POST.getlist("item-ignore", [])

            item_mapping = {}
            mapping_item_custom_fields = {}
            for idx, file_column in enumerate(item_file_columns):
                # check ignore
                item_mapping[file_column] = item_sanka_properties[idx]
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            mapping, _ = ShopTurboItemsMappingFields.objects.get_or_create(
                workspace=workspace,
                platform=channel.integration.slug,
                mapping_type__isnull=True,
            )
            
            data = {}
            for i, field in enumerate(item_mapping):
                if "create_new" in item_mapping[field] and item_ignores[i] != "True":
                    new_field, _ = ShopTurboItemsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=item_file_columns_name[i], type="text"
                    )
                    field_data = {
                        "value": item_file_columns_name[i], "skip": item_ignores[i]}
                    item_sanka_properties[i] = item_file_columns_name[i]
                else:
                    field_data = {"value": item_mapping[field], "skip": item_ignores[i]}
                data[field] = field_data

            mapping.input_data = data
            mapping.save()

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "save_shopify_location_mapping" in request.POST:
            shopify_location_mapping = {}
            for query in request.POST:
                if "shopify_mapping" in query:
                    map_id = query.split("|")[1]
                    channel_id = query.split("|")[2]
                    value = request.POST.get(query)
                    if map_id not in shopify_location_mapping:
                        shopify_location_mapping[map_id] = {}

                    shopify_location_mapping[map_id][channel_id] = value

            mapping, _ = ShopTurboItemsMappingFields.objects.get_or_create(
                workspace=workspace, platform="shopify", mapping_type="location"
            )
            mapping.input_data = shopify_location_mapping
            mapping.save()

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "sync_items" in request.POST:
            file_columns = request.POST.getlist("order-file-column", [])
            sanka_properties = request.POST.getlist("order-sanka-properties", [])
            ignores = request.POST.getlist("order-ignore", [])

            contact_file_columns = request.POST.getlist("file-column", [])
            contact_sanka_properties = request.POST.getlist("sanka-properties", [])
            contact_ignores = request.POST.getlist("ignore", [])

            item_file_columns = request.POST.getlist("item-file-column", [])
            item_file_columns_name = request.POST.getlist("item-file-column-name", [])
            item_sanka_properties = request.POST.getlist("sanka-item-properties", [])
            item_ignores = request.POST.getlist("item-ignore", [])

            mapping_custom_fields = {}
            mapping_contact_custom_fields = {}
            mapping_item_custom_fields = {}

            order_mapping = {}
            contact_mapping = {}
            item_mapping = {}

            for idx, file_column in enumerate(file_columns):
                # check ignore
                order_mapping[file_column] = sanka_properties[idx]
                if ignores[idx] == "True":
                    continue
                mapping_custom_fields[file_column] = sanka_properties[idx]
            for idx, file_column in enumerate(contact_file_columns):
                # check ignore
                contact_mapping[file_column] = contact_sanka_properties[idx]
                if contact_ignores[idx] == "True":
                    continue
                mapping_contact_custom_fields[file_column] = contact_sanka_properties[
                    idx
                ]
            for idx, file_column in enumerate(item_file_columns):
                # check ignore
                item_mapping[file_column] = item_sanka_properties[idx]
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            # Process create new properties
            data = {}
            for i, field in enumerate(item_mapping):
                if "create_new" in item_mapping[field] and item_ignores[i] != "True":
                    new_field, _ = ShopTurboItemsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=item_file_columns_name[i], type="text"
                    )
                    field_data = {
                        "value": item_file_columns_name[i], "skip": item_ignores[i]}
                    item_sanka_properties[i] = item_file_columns_name[i]
                else:
                    field_data = {"value": item_mapping[field], "skip": item_ignores[i]}
                data[field] = field_data
            
            for idx, file_column in enumerate(item_file_columns):
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            print(
                "=== shopturbo.py -- 320 Sync item for select_integration_ids",
                select_integration_ids,
            )

            filter_id = request.POST.get("filter", None)
            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            print("=== shopturbo.py -- 322 Sync item for filter_items", filter_items)

            channels = Channel.objects.filter(id__in=select_integration_ids)
            key_item_field = request.POST.get("update-options", None)
            for channel in channels:
                # Save Mapping
                mapping = ShopTurboItemsMappingFields.objects.filter(
                    workspace=workspace, platform=channel.integration.slug, mapping_type__isnull=True
                ).first()
                mapping.input_data = data
                mapping.save()


                if lang == "ja":
                    task_name = f"{channel.integration.slug} - {channel.name} の商品をインポート"
                else:
                    task_name = (
                        f"Import {channel.integration.slug} - {channel.name} items"
                    )
                print("=== shopturbo.py -- 377 Pull items for channel", channel.id)
                if channel.integration.slug == "shopify":
                    filter_method = request.POST.get("filter-method", None)
                    filter_type = (
                        request.POST.get("filter-type", None)
                        if filter_method == "choice_filter"
                        else None
                    )
                    filter_id = (
                        request.POST.get("filter", None)
                        if filter_method == "choice_filter"
                        else None
                    )
                    filter_choice = (
                        request.POST.get("filter-choice", None)
                        if filter_method == "choice_filter"
                        else None
                    )
                    filter_sku = (
                        request.POST.get("sku-filter", None)
                        if filter_method == "sku"
                        else None
                    )

                    filter_items = None
                    if filter_method == "choice_filter":
                        if filter_id:
                            cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                                id=filter_id
                            ).first()
                            if cf_filter:
                                value = filter_choice
                                if value:
                                    cf_value_items = (
                                        ShopTurboItemsValueCustomField.objects.filter(
                                            field_name=cf_filter, value=value
                                        )
                                    )
                                    if cf_value_items:
                                        filter_items = cf_value_items.values_list(
                                            "items", flat=True
                                        )
                                        filter_items = filter_items.distinct()

                        if filter_items:
                            filter_items = ",".join(str(item) for item in filter_items)

                    elif filter_method == "sku":
                        if filter_sku:
                            filter_sku = filter_sku
                        else:
                            filter_sku = None
                    else:
                        filter_items = None
                        filter_sku = None

                    # shopify_mapping|map_number|channel_id|postfix
                    shopify_location_mapping = {}
                    for query in request.POST:
                        if "shopify_mapping" in query:
                            map_id = query.split("|")[1]
                            channel_id = query.split("|")[2]
                            value = request.POST.get(query)
                            if map_id not in shopify_location_mapping:
                                shopify_location_mapping[map_id] = {}

                            shopify_location_mapping[map_id][channel_id] = value

                    mapping, _ = ShopTurboItemsMappingFields.objects.get_or_create(
                        workspace=workspace, platform="shopify", mapping_type="location"
                    )
                    mapping.input_data = shopify_location_mapping
                    mapping.save()

                    item_mapping_str = json.dumps(item_mapping)
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_item",
                        name=task_name,
                        channel = channel or None,
                    )

                    param = {
                        "function": "shopify_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--item_mapping={item_mapping_str}",
                            f"--sync_method={request.POST.get('sync-key', TYPE_SHOPIFY_SYNC_KEY_PLATFORM)}",
                            f"--filter_method={filter_method}",
                            f"--filter_items={filter_items}",
                            f"--filter_sku={filter_sku}",
                            f"--history_id={str(history.id)}",
                            f"--location_mapping={shopify_location_mapping}",
                            f"--language={request.user.verification.language if request.user.verification.language else 'ja'}",
                        ],
                    }

                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify 商品のインポート ジョブが正常に送信されました。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items import job submitted successfully.",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify商品のインポートに失敗しました。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items import failed.",
                                type="error",
                            )

                elif channel.integration.slug == "square":
                    pull_square_items(channel.id)
                elif channel.integration.slug == "amazon":
                    pull_amazon_items(channel.id)
                elif channel.integration.slug == "stripe":
                    pull_stripe_items(channel.id)
                elif channel.integration.slug == "ecforce":
                    pull_ecforce_items(channel.id)
                elif channel.integration.slug == "rakuten":
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_item",
                        name=task_name,
                        channel = channel or None,
                    )
                    param = {
                        "function": "rakuten_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--key_item_field={key_item_field}",
                            f"--history_id={str(history.id)}",
                        ],
                    }
                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="楽天商品のインポートが完了しました",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import Rakuten items success",
                                type="success",
                            )
                    else:
                        pull_rakuten_items(channel.id, key_item_field=key_item_field)
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="楽天商品のインポートに失敗しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import Rakuten items failed",
                                type="error",
                            )
                elif channel.integration.slug == "freee":
                    pull_freee_items(channel.id)
                elif channel.integration.slug == "ec-cube":
                    param = {
                        "function": "eccube_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                        ],
                    }
                    is_running = trigger_bg_job(param)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="EC-Cube商品のインポートが完了しました",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import EC-Cube items success",
                                type="success",
                            )
                    else:
                        pull_eccube_items(channel.id)
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="EC-Cube商品のインポートに失敗しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import EC-Cube items failed",
                                type="error",
                            )
                elif channel.integration.slug == "makeshop":
                    item_mapping_str = json.dumps(item_mapping)
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_item",
                        name=task_name,
                        channel = channel or None,
                    )
                    param = {
                        "function": "makeshop_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--key_item_field={key_item_field}",
                            f"--history_id={str(history.id)}",
                            f"--item_mapping={item_mapping_str}",
                        ],
                    }
                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="MakeShop商品のインポートが完了しました",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import MakeShop items success",
                                type="success",
                            )
                    else:
                        pull_makeshop_items(channel.id, key_item_field=key_item_field)
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="MakeShop商品のインポートに失敗しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import MakeShop items failed",
                                type="error",
                            )
                elif channel.integration.slug == "yahoo-shopping":
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_item",
                        name=task_name,
                        channel = channel or None,
                    )
                    param = {
                        "function": "yahoo_shopping_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--key_item_field={key_item_field}",
                            f"--history_id={str(history.id)}",
                            f"--user={request.user.id}",
                        ],
                    }
                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Yahooショッピング商品のインポートが完了しました",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import Yahoo Shopping items success",
                                type="success",
                            )
                    else:
                        pull_yahoo_shopping_items(
                            request.user.id, channel.id, key_item_field=key_item_field
                        )
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Yahooショッピング商品のインポートに失敗しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import Yahoo Shopping items failed",
                                type="error",
                            )
                elif channel.integration.slug == "smaregi":
                    item_mapping_str = json.dumps(item_mapping)
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_item",
                        name=task_name,
                        channel = channel or None,
                    )
                    filter_type = request.POST.getlist("item-import-filter-type", None)
                    filter_option = request.POST.getlist(
                        "item-import-filter-option", None
                    )
                    filter_value = request.POST.getlist(
                        "item-import-filter-value", None
                    )

                    filter_import = {}
                    if filter_type and filter_option and filter_value:
                        try:
                            for idx, filter_type in enumerate(filter_type):
                                if (
                                    filter_type
                                    and filter_option[idx]
                                    and filter_value[idx]
                                ):
                                    filter_import[filter_type] = {
                                        "key": filter_option[idx],
                                        "value": filter_value[idx],
                                    }
                        except:
                            pass
                    param = {
                        "function": "smaregi_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--history_id={str(history.id)}",
                            f"--item_mapping={item_mapping_str}",
                            f"--filter_import={filter_import}",
                        ],
                    }
                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="スマレジ商品のインポートが完了しました",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import Smaregi items success",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="スマレジ商品のインポートに失敗しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Import Smaregi items failed",
                                type="error",
                            )
                elif channel.integration.slug == "salesforce":
                    logger.info(f"Item Mapping: {mapping_item_custom_fields}")
                    
                    # Extract Salesforce filtering parameters
                    enable_salesforce_filter = request.POST.get("enable-salesforce-filter", None)
                    if enable_salesforce_filter == "1":
                        enable_salesforce_filter = True
                    else:
                        enable_salesforce_filter = False
                    
                    salesforce_field = request.POST.get("salesforce-field", None)
                    if salesforce_field == "None":
                        salesforce_field = None
                    salesforce_filter = request.POST.get("salesforce-filter", None)
                    
                    # Convert item_mapping to JSON string
                    item_mapping_str = json.dumps(mapping_item_custom_fields) if mapping_item_custom_fields else None
                    
                    # Create transfer history record
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_item",
                        name=task_name,
                        channel=channel,
                    )
                    
                    # Create payload for background job
                    payload = ImportSalesforceItemsPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        history_id=str(history.id),
                        item_mapping=item_mapping_str,
                        language=lang,
                        enable_salesforce_filter=enable_salesforce_filter,
                        salesforce_field=salesforce_field,
                        salesforce_filter=salesforce_filter,
                        salesforce_checkpoint=0,
                    )
                    
                    # Create background job
                    job_id = create_bg_job(
                        workspace=workspace,
                        user=request.user,
                        function_name="import_salesforce_items",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id
                    
                    # Log import job parameters for debugging
                    logger.info(f"IMPORT_SALESFORCE_ITEMS: Starting items import for user {request.user.email} in workspace {workspace.id}")
                    logger.info(f"IMPORT_SALESFORCE_ITEMS: Import parameters - function: import_salesforce_items, job_id: {job_id}")
                    logger.info(f"IMPORT_SALESFORCE_ITEMS: Channel ID: {channel.id}, History ID: {history.id}, Language: {lang}")
                    logger.info(f"IMPORT_SALESFORCE_ITEMS: Mapping: {item_mapping_str}")
                    logger.info(f"IMPORT_SALESFORCE_ITEMS: Filter - enabled: {enable_salesforce_filter}, field: {salesforce_field}, value: {salesforce_filter}")
                    
                    try:
                        ref = import_salesforce_items_task.run_no_wait(input=payload)
                    except Exception as e:
                        logger.error(f"IMPORT_SALESFORCE_ITEMS: Exception occurred during import_salesforce_items_task: {str(e)}", exc_info=True)
                        ref = None
                    
                    is_running = None
                    if ref:
                        logger.info(f"IMPORT_SALESFORCE_ITEMS: Background job submitted successfully for user {request.user.email}")
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        logger.error(f"IMPORT_SALESFORCE_ITEMS: Failed to submit background job for user {request.user.email}")
                        is_running = False
                    
                    if is_running:
                        logger.info(f"IMPORT_SALESFORCE_ITEMS: Successfully submitted import job for user {request.user.email}")
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Salesforce商品のインポートジョブが送信されました。完了時に通知されます。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Salesforce items import job submitted successfully. You will be notified when complete.",
                                type="success",
                            )
                    else:
                        logger.error(f"IMPORT_SALESFORCE_ITEMS: Failed to submit import job for user {request.user.email} in workspace {workspace.id}")
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Salesforce商品のインポートジョブの送信に失敗しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Failed to submit Salesforce items import job",
                                type="error",
                            )

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "push_items" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            # print("=== shopturbo.py -- 320 Push item for select_integration_ids", select_integration_ids, len(select_integration_ids), type(select_integration_ids))
            item_ids = request.POST.getlist("item_ids", [])
            update_inventory_toggle = request.POST.get("update_inventory", None)
            if len(select_integration_ids) == 0:
                return HttpResponse(status=200)
            channels = Channel.objects.filter(id__in=select_integration_ids)

            filter_id = request.POST.get("filter", None)

            item_file_columns = request.POST.getlist("item-file-column", [])
            item_sanka_properties = request.POST.getlist("sanka-item-properties", [])
            item_ignores = request.POST.getlist("item-ignore", [])

            item_mapping = {}

            mapping_item_custom_fields = {}
            for idx, file_column in enumerate(item_file_columns):
                # check ignore
                item_mapping[file_column] = item_sanka_properties[idx]
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            print("=== shopturbo.py -- 322 Sync item for filter_items", filter_items)

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            # Handle "between" operation (have two filter values)
            filter_values2 = request.POST.getlist("filter_value-2", None)
            idx_filter_values2 = 0
            for idx, filter_type in enumerate(filter_types):
                value = filter_values[idx]
                if filter_options[idx] == "between":
                    try:
                        value = (
                            f"{filter_values[idx]}-{filter_values2[idx_filter_values2]}"
                        )
                    except:
                        value = ""
                    idx_filter_values2 += 1

                if (
                    str(filter_type) not in filter_dictionary
                    and filter_types.count(str(filter_type)) > 1
                ):
                    filter_dictionary[str(filter_type)] = {
                        "key": [filter_options[idx]],
                        "value": [value],
                    }
                elif str(filter_type) in filter_dictionary:
                    filter_dictionary[str(filter_type)]["key"].append(
                        filter_options[idx]
                    )
                    filter_dictionary[str(filter_type)]["value"].append(value)
                else:
                    filter_dictionary[str(filter_type)] = {
                        "key": filter_options[idx],
                        "value": value,
                    }

            filter_conditions = Q(workspace=workspace, status="active")

            if item_ids:
                filter_conditions &= Q(id__in=item_ids)

            view, _ = View.objects.get_or_create(
                workspace=workspace, title__isnull=True, target=TYPE_OBJECT_ITEM
            )

            view_filter, is_new_view_filter = ViewFilter.objects.get_or_create(view=view)
            if is_new_view_filter:
                view_filter.view_type = "list"
                view_filter.filter_value = {}
                view_filter.save()
            else:
                view_filter.filter_value = {}
                view_filter.save()
            
            temp_view_filter = view_filter.filter_value
            view_filter.filter_value = filter_dictionary
            view_filter.save()
            view.save()

            filter_conditions = apply_shopturboitems_view_filter(
                filter_conditions, view_filter, request=request)
            print("=== items.py -- 322 Sync item for filter_dictionary", filter_dictionary)
            view_filter.filter_value = temp_view_filter
            view_filter.save()
            view.save() 
            
            items = ShopTurboItems.objects.filter(filter_conditions)
            print("=== items.py -- 322 Sync item for items", items)
            
            inventories = ShopTurboInventory.objects.filter(
                item__in=items, workspace=workspace
            )
            
            if filter_items:
                if request.POST.get("filter-type") == "exclude":
                    items = items.exclude(id__in=filter_items)
                    inventories = inventories.exclude(item__in=items)
                else:
                    items = items.filter(id__in=filter_items)
                    inventories = inventories.filter(item__in=items)

            items = list(items.values_list('id', flat=True))

            ExportImportConfiguration.objects.update_or_create(
                workspace=workspace,
                object_type=TYPE_OBJECT_ITEM,
                platform=channels[0].integration.slug,
                defaults={
                    "input_data": {
                        "filter_dictionary": filter_dictionary,
                        "update-inventory-toggle": update_inventory_toggle,
                    }
                }
            )

            mapping = ShopTurboItemsMappingFields.objects.filter(
                workspace=workspace,
                platform=channels[0].integration.slug,
                mapping_type__isnull=True,
            ).first()
            for channel in channels:
                # print("=== shopturbo.py -- 377 Push items for channel", channel.id)
                if channel.integration.slug == "shopify":
                    filter_method = request.POST.get("filter-method", None)
                    filter_sku = (
                        request.POST.get("sku-filter", None)
                        if filter_method == "sku"
                        else None
                    )

                    items = []
                    for item_id in item_ids:
                        items.append(item_id)

                    items = ",".join(items)

                    if lang == "ja":
                        task_name = "Shopify 商品のエクスポート"
                    else:
                        task_name = "Shopify Items Export"
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="export_item",
                        name=task_name,
                        channel = channel or None,
                    )

                    filter_items = (
                        ",".join(str(item) for item in filter_items)
                        if filter_method == "choice_filter" and filter_items
                        else None
                    )
                    filter_sku = (
                        filter_sku if filter_method == "sku" and filter_sku else None
                    )

                    filter_type = None
                    if filter_items:
                        filter_type = request.POST.get("filter-type")

                    param = {
                        "function": "shopify_export_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--item_ids={items}",
                            f"--workspace={str(workspace.id)}",
                            f"--filter_method={filter_method}",
                            f"--filter_items={filter_items}",
                            f"--filter_sku={filter_sku}",
                            f"--history_id={str(history.id)}",
                            f"--filter_type={filter_type}",
                        ],
                    }

                    is_running = trigger_bg_job(param, transfer_history=history)

                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="エクスポートジョブが正常に送信されました。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Export job submitted successfully.",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="エクスポートに失敗しました。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Export failed.",
                                type="error",
                            )

                elif channel.integration.slug == "ecforce":
                    push_ecforce_items(items, channel.id)
                elif channel.integration.slug == "yahoo-shopping":
                    push_yahoo_shopping_items(items, channel.id)
                elif channel.integration.slug == "temu":
                    push_temu_items(items, channel.id)
                elif channel.integration.slug == "rakuten":
                    push_rakuten_items(request, mapping.input_data, items, channel.id, update_inventory_toggle)
                elif channel.integration.slug == "amazon":
                    push_amazon_items(items, channel.id, mapping.input_data, update_inventory_toggle)
                elif channel.integration.slug == "makeshop":
                    push_makeshop_items(items, channel.id, mapping.input_data, update_inventory_toggle)
                elif channel.integration.slug == "hubspot":
                    if lang == "ja":
                        task_name = "Hubspot 商品のエクスポート"
                    else:
                        task_name = "Hubspot Items Export"
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="export_item",
                        name=task_name,
                        channel = channel or None,
                    )

                    param = {
                        "function": "hubspot_export_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--item_ids={item_ids}",
                            f"--workspace={str(workspace.id)}",
                            f"--mapping_item_custom_fields={mapping_item_custom_fields}",
                            f"--history_id={str(history.id)}",
                        ],
                    }

                    is_running = trigger_bg_job(param, transfer_history=history)

                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Hubspot 商品のエクスポート ジョブが正常に送信されました。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Hubspot items export job submitted successfully.",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Hubspot 商品のエクスポートに失敗しました。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Hubspot items export failed.",
                                type="error",
                            )
                # elif channel.integration.slug == 'freee':
                #     push_freee_items(items, channel.id)
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "bulk_duplicate" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ITEM, workspace=workspace
                    ).first()
                filter_conditions = Q(workspace=workspace)
                if view:
                    from django.db.models.fields.related_descriptors import (
                        ForwardManyToOneDescriptor,
                    )

                    if hasattr(view, "viewfilter_set") and isinstance(
                        view.viewfilter_set, ForwardManyToOneDescriptor
                    ):
                        view_filter = view.viewfilter_set.first()
                        if view_filter:
                            filter_conditions = build_view_filter(
                                filter_conditions,
                                view_filter,
                                TYPE_OBJECT_ITEM,
                                force_filter_list=additional_filter_fields,
                            )
                objects = base_model.objects.filter(filter_conditions)
            else:
                # Use the new helper function to get IDs from both checkboxes and cross-page selections
                item_ids = get_bulk_action_item_ids(request, workspace)
                objects = base_model.objects.filter(id__in=item_ids)

            for obj in objects:
                customFields = custom_value_model.objects.filter(
                    **{custom_value_relation: obj}
                )
                obj_prc = ShopTurboItemsPrice.objects.filter(**{"item": obj}).order_by(
                    "created_at"
                )
                obj_purchase_price = ItemPurchasePrice.objects.filter(
                    **{"item": obj}
                ).order_by("created_at")
                obj_itm_var = ShopTurboItemsVariations.objects.filter(
                    **{"items": obj}
                ).order_by("created_at")
                obj_itm_cust_price = ShopTurboCustomerItemsPrice.objects.filter(
                    **{"item": obj}
                ).order_by("created_at")
                print("=== DEBUG obj_purchase_price", obj_purchase_price)
                print("=== DEBUG obj_prc", obj_prc)

                obj.id = None
                setattr(obj, id_field, None)
                obj.created_at = timezone.now()
                obj.save()

                for field in customFields:
                    old_field_id = field.id
                    field.id = None
                    setattr(field, custom_value_relation, obj)
                    field.save()

                    # duplicate file or image
                    customFieldsValueFile = custom_value_file_model.objects.filter(
                        **{f"{custom_value_file_relation}__id": old_field_id}
                    )
                    if customFieldsValueFile:
                        for value_file in customFieldsValueFile:
                            value_file.id = None
                            value_file.created_at = timezone.now()
                            setattr(value_file, custom_value_file_relation, field)
                            value_file.save()

                if obj_prc:
                    for item_ in obj_prc:
                        item_.id = None
                        item_.created_at = timezone.now()
                        setattr(item_, "item", obj)
                        item_.save()

                if obj_purchase_price:
                    for item_ in obj_purchase_price:
                        item_.id = None
                        item_.created_at = timezone.now()
                        setattr(item_, "item", obj)
                        item_.save()
                if obj_itm_var:
                    for item_ in obj_itm_var:
                        item_.id = None
                        item_.created_at = timezone.now()
                        setattr(item_, "items", obj)
                        item_.save()

                if obj_itm_cust_price:
                    for item_ in obj_itm_cust_price:
                        item_.id = None
                        item_.created_at = timezone.now()
                        setattr(item_, "item", obj)
                        item_.save()

        view_id = request.POST.get("view_id")
        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={"module_slug": module, "object_slug": module_object_slug},
                )
                + f"?view_id={view_id if view_id else None}"
            )
        return redirect(reverse("main", host="app"))


def validate_and_convert_filter_value(value, expected_type="int", field_name="unknown"):
    """
    Validate and convert filter values to expected types.

    Args:
        value: The value to validate and convert
        expected_type: Expected type ('int', 'float', 'str')
        field_name: Name of the field for debugging

    Returns:
        Converted value or None if invalid
    """
    if not value or (isinstance(value, str) and not value.strip()):
        print(f"[DEBUG] Empty or None value for field '{field_name}' - skipping")
        return None

    try:
        if expected_type == "int":
            return int(str(value).strip())
        elif expected_type == "float":
            return float(str(value).strip())
        elif expected_type == "str":
            return str(value).strip()
        else:
            return value
    except (ValueError, TypeError) as e:
        print(f"[DEBUG] Invalid {expected_type} value for field '{field_name}': '{value}' - {e}")
        return None


def get_custom_field_matching_items(custom_field, filter_data, workspace=None):
    """Get item IDs that match a custom field filter.

    This function handles custom field filtering separately to avoid complex joins
    that can cause issues with pagination and distinct() operations.

    Args:
        custom_field: ShopTurboItemsNameCustomField instance
        filter_data: Dictionary with 'key' and 'value' for the filter
        workspace: Optional workspace filter

    Returns:
        List of item IDs that match the filter criteria
    """
    filter_key = filter_data.get('key', '')
    filter_value = filter_data.get('value', '')

    base_query = ShopTurboItemsValueCustomField.objects.filter(
        field_name=custom_field
    )

    # Add workspace filter if provided
    if workspace:
        base_query = base_query.filter(items__workspace=workspace)

    # Apply the specific filter based on the key
    if filter_key == 'contains':
        matching_values = base_query.filter(value__icontains=filter_value)
    elif filter_key == 'is':
        matching_values = base_query.filter(value=filter_value)
    elif filter_key == 'is_not':
        matching_values = base_query.exclude(value=filter_value)
    elif filter_key == 'starts_with':
        matching_values = base_query.filter(value__startswith=filter_value)
    elif filter_key == 'ends_with':
        matching_values = base_query.filter(value__endswith=filter_value)
    elif filter_key == 'does_not_contain':
        matching_values = base_query.exclude(value__icontains=filter_value)
    elif filter_key == 'is_empty':
        matching_values = base_query.filter(
            Q(value__isnull=True) | Q(value='')
        )
    elif filter_key == 'is_not_empty':
        matching_values = base_query.filter(
            value__isnull=False
        ).exclude(value='')
    elif filter_key == 'includes':
        if isinstance(filter_value, str):
            value_list = filter_value.split(',')
        elif isinstance(filter_value, (list, tuple)):
            value_list = list(filter_value)
        else:
            # Handle non-iterable types (int, bool, None, etc.)
            value_list = [str(filter_value)] if filter_value is not None else []
        matching_values = base_query.filter(value__in=value_list)
    elif filter_key == 'includes_all':
        # For includes_all, we need records that contain ALL specified values
        # This uses AND logic with icontains for each value
        if isinstance(filter_value, str):
            value_list = [val.strip() for val in filter_value.split(',')]
        elif isinstance(filter_value, (list, tuple)):
            value_list = [str(val).strip() for val in filter_value]
        else:
            # Handle non-iterable types (int, bool, None, etc.)
            value_list = [str(filter_value).strip()] if filter_value is not None else []

        # Start with base query and apply AND logic for each value
        matching_values = base_query
        for val in value_list:
            if val:  # Skip empty values
                matching_values = matching_values.filter(value__icontains=val)
    elif filter_key == 'excludes':
        if isinstance(filter_value, str):
            value_list = filter_value.split(',')
        elif isinstance(filter_value, (list, tuple)):
            value_list = list(filter_value)
        else:
            # Handle non-iterable types (int, bool, None, etc.)
            value_list = [str(filter_value)] if filter_value is not None else []
        matching_values = base_query.exclude(value__in=value_list)
    else:
        print(f"[DEBUG] Unsupported custom field filter key: {filter_key}")
        return []

    # Return distinct item IDs
    return list(matching_values.values_list('items_id', flat=True).distinct())


def apply_shopturboitems_view_filter(
    filter_conditions: Q, view_filter: Optional[ViewFilter], request=None
) -> Q:
    """Apply filter conditions for ShopTurbo items view.

    Args:
        filter_conditions: Base query filter conditions
        view_filter: Optional ItemViewFilter instance

    Returns:
        Updated filter conditions
    """
    original_filter_conditions = filter_conditions
    if view_filter:
        default_columns_dict = {
            field.name: field.get_internal_type()
            for field in ShopTurboItems._meta.fields
        }
        filter_dictionary = view_filter.filter_value

        # Validate filter_dictionary structure
        if filter_dictionary and not isinstance(filter_dictionary, dict):
            print(f"[DEBUG] Invalid filter_dictionary type: {type(filter_dictionary)} - expected dict")
            return original_filter_conditions

        if filter_dictionary:
            # Pre-process custom field filters to avoid complex joins
            custom_field_item_ids = None
            has_custom_field_filters = False
            workspace = view_filter.view.workspace if view_filter.view else None

            # First pass: handle custom field filters separately
            for filter_key, filter_data in filter_dictionary.items():
                # Check if this is a custom field filter (UUID format)
                if is_valid_uuid(filter_key):
                    try:
                        custom_field = ShopTurboItemsNameCustomField.objects.get(id=filter_key)
                        has_custom_field_filters = True

                        # Handle multi-value filters (where filter_data has lists for key and value)
                        if (isinstance(filter_data, dict) and
                            isinstance(filter_data.get("key"), list) and
                            isinstance(filter_data.get("value"), list) and
                            len(filter_data["key"]) == len(filter_data["value"])):

                            # Process each key-value pair in the multi-value filter
                            for key, value in zip(filter_data["key"], filter_data["value"]):
                                single_filter_data = {"key": key, "value": value}
                                matching_items = get_custom_field_matching_items(
                                    custom_field, single_filter_data, workspace
                                )

                                if custom_field_item_ids is None:
                                    custom_field_item_ids = set(matching_items)
                                else:
                                    # Intersect with previous results (AND logic)
                                    custom_field_item_ids &= set(matching_items)
                        else:
                            # Handle single-value filter
                            matching_items = get_custom_field_matching_items(
                                custom_field, filter_data, workspace
                            )

                            if custom_field_item_ids is None:
                                custom_field_item_ids = set(matching_items)
                            else:
                                # Intersect with previous results (AND logic)
                                custom_field_item_ids &= set(matching_items)

                        print(f"[DEBUG] Custom field {custom_field.name} filter processed")

                    except ShopTurboItemsNameCustomField.DoesNotExist:
                        print(f"[DEBUG] Custom field not found for ID: {filter_key}")
                        continue

            # Apply custom field filters as a simple ID filter
            if has_custom_field_filters:
                if custom_field_item_ids:
                    filter_conditions &= Q(id__in=list(custom_field_item_ids))
                    print(f"[DEBUG] Applied custom field filter: {len(custom_field_item_ids)} items")
                else:
                    # No items match the custom field filters
                    filter_conditions &= Q(id__in=[])
                    print(f"[DEBUG] No items match custom field filters")

            # Second pass: handle regular field filters
            for filter in filter_dictionary.keys():
                filter_lower = filter.lower()
                print(filter)

                # Skip custom field filters as they're already handled
                if is_valid_uuid(filter):
                    continue

                data = filter_dictionary[filter]
                # Multiple filters by same field key
                if (
                    isinstance(data, dict)
                    and isinstance(data.get("key"), list)
                    and isinstance(data.get("value"), list)
                    and len(data["key"]) == len(data["value"])
                ):
                    for i, (k, v) in enumerate(zip(data["key"], data["value"])):
                        column_type = check_columns_type(filter, default_columns_dict)
                        filter_key = filter

                        # If filter_key is a UUID and not a default column, it's a custom field
                        if (
                            is_valid_uuid(filter_key)
                            and filter_key not in default_columns_dict
                        ):
                            cf_query_kwargs = {
                                "shopturbo_item_custom_field_relations__field_name_id": filter_key
                            }
                            negate = False
                            # Determine the correct lookup for the custom field's value (assuming string-like)
                            if k == "is":
                                cf_query_kwargs[
                                    "shopturbo_item_custom_field_relations__value"
                                ] = str(v)
                            elif k == "is_not":
                                cf_query_kwargs[
                                    "shopturbo_item_custom_field_relations__value"
                                ] = str(v)
                                negate = True
                            elif k == "contains":
                                cf_query_kwargs[
                                    "shopturbo_item_custom_field_relations__value__icontains"
                                ] = str(v)
                            elif k == "does_not_contain":
                                cf_query_kwargs[
                                    "shopturbo_item_custom_field_relations__value__icontains"
                                ] = str(v)
                                negate = True
                            elif k == "starts_with":
                                cf_query_kwargs[
                                    "shopturbo_item_custom_field_relations__value__istartswith"
                                ] = str(v)
                            elif k == "ends_with":
                                cf_query_kwargs[
                                    "shopturbo_item_custom_field_relations__value__iendswith"
                                ] = str(v)
                            elif k == "is_empty":
                                # For 'is_empty' on custom fields, we check for related value being null or empty string.
                                # This includes items that have the custom field set but its value is empty/null.
                                # It does not explicitly find items that *don't have the custom field set at all* without further complex queries (e.g. subquery with NOT EXISTS).
                                # This is a common simplification.
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name_id=filter_key,
                                    shopturbo_item_custom_field_relations__value__isnull=True,
                                ) | Q(
                                    shopturbo_item_custom_field_relations__field_name_id=filter_key,
                                    shopturbo_item_custom_field_relations__value__exact="",
                                )
                                continue  # Move to next filter condition
                            elif k == "is_not_empty":
                                # Converse of 'is_empty'.
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name_id=filter_key,
                                    shopturbo_item_custom_field_relations__value__isnull=False,
                                ) & ~Q(
                                    shopturbo_item_custom_field_relations__field_name_id=filter_key,
                                    shopturbo_item_custom_field_relations__value__exact="",
                                )
                                continue  # Move to next filter condition
                            else:
                                # Unsupported operator for this custom field type, skip.
                                continue

                            current_q = Q(**cf_query_kwargs)
                            if negate:
                                filter_conditions &= ~current_q
                            else:
                                filter_conditions &= current_q
                            # Skip further processing for this iteration if it was handled as a custom field
                            continue
                        # Existing logic for standard fields or other special handlers:
                        elif filter == "platform_ids":
                            # Handle platform_ids filtering through the related ShopTurboItemsPlatforms model
                            if filter_dictionary[filter]["key"][i] == "is_empty":
                                filter_conditions &= Q(item__isnull=True)
                            elif filter_dictionary[filter]["key"][i] == "is_not_empty":
                                filter_conditions &= Q(item__isnull=False)
                            elif filter_dictionary[filter]["key"][i] == "contains":
                                filter_conditions &= Q(
                                    item__platform_id__icontains=filter_dictionary[
                                        filter
                                    ]["value"][i]
                                )
                            elif (
                                filter_dictionary[filter]["key"][i]
                                == "does_not_contain"
                            ):
                                filter_conditions &= ~Q(
                                    item__platform_id__icontains=filter_dictionary[
                                        filter
                                    ]["value"][i]
                                )
                            elif filter_dictionary[filter]["key"][i] == "is":
                                filter_conditions &= Q(
                                    item__platform_id=filter_dictionary[filter][
                                        "value"
                                    ][i]
                                )
                            elif filter_dictionary[filter]["key"][i] == "is_not":
                                filter_conditions &= ~Q(
                                    item__platform_id=filter_dictionary[filter][
                                        "value"
                                    ][i]
                                )
                            elif filter_dictionary[filter]["key"][i] == "starts_with":
                                filter_conditions &= Q(
                                    item__platform_id__startswith=filter_dictionary[
                                        filter
                                    ]["value"][i]
                                )
                            elif filter_dictionary[filter]["key"][i] == "ends_with":
                                filter_conditions &= Q(
                                    item__platform_id__endswith=filter_dictionary[
                                        filter
                                    ]["value"][i]
                                )
                            continue  # Skip further processing for this iteration

                        elif ("-item id" in filter_lower) or (
                            "-sku" in filter_lower
                        ):  # Note elif
                            if filter_dictionary[filter]["key"][i] == "is_not_empty":
                                if "-SKU" in filter and filter.startswith(
                                    "item_platform|"
                                ):
                                    id_ = filter.replace("-SKU", "").replace(
                                        "item_platform|", ""
                                    )
                                    filter_conditions &= Q(item__channel__id=id_)
                                elif "-Item ID" in filter and filter.startswith(
                                    "item_platform|"
                                ):
                                    id_ = filter.replace("-Item ID", "").replace(
                                        "item_platform|", ""
                                    )
                                    filter_conditions &= Q(item__channel__id=id_)
                                elif "-Variant ID" in filter and filter.startswith(
                                    "item_platform|"
                                ):
                                    id_ = filter.replace("-Variant ID", "").replace(
                                        "item_platform|", ""
                                    )
                                    filter_conditions &= Q(item__channel__id=id_)

                            if "-item id" in filter_lower:
                                filter_key = "item__platform_id"
                            elif "-sku" in filter_lower:
                                filter_key = "item__platform_sku"

                            column_type = "string"
                        if "supplier__" in filter_lower:
                            if filter_lower == "supplier__contact__first_name":
                                filter_key = "contact__name"
                            else:
                                filter_key = filter_lower.replace("supplier__", "")

                        print(column_type, filter)
                        if column_type == "string":
                            if (
                                filter == "status"
                                and filter_dictionary[filter]["value"][i] == "all"
                            ):
                                continue
                            if filter_dictionary[filter]["key"][i] == "is":
                                filter_conditions &= Q(
                                    **{
                                        filter_key: filter_dictionary[filter]["value"][
                                            i
                                        ]
                                    }
                                )
                            elif filter_dictionary[filter]["key"][i] == "is_not":
                                filter_conditions &= ~Q(
                                    **{
                                        filter_key: filter_dictionary[filter]["value"][
                                            i
                                        ]
                                    }
                                )
                            elif filter_dictionary[filter]["key"][i] == "contains":
                                filter_conditions &= Q(
                                    **{
                                        filter_key + "__icontains": filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    }
                                )
                            elif (
                                filter_dictionary[filter]["key"][i]
                                == "does_not_contain"
                            ):
                                filter_conditions &= ~Q(
                                    **{
                                        filter_key + "__icontains": filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    }
                                )
                            elif filter_dictionary[filter]["key"][i] == "starts_with":
                                filter_conditions &= Q(
                                    **{
                                        filter_key + "__startswith": filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    }
                                )
                            elif filter_dictionary[filter]["key"][i] == "ends_with":
                                filter_conditions &= Q(
                                    **{
                                        filter_key + "__endswith": filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    }
                                )
                            elif filter_dictionary[filter]["key"][i] == "is_empty":
                                filter_conditions &= Q(
                                    **{filter_key + "__isnull": True}
                                )
                            elif filter_dictionary[filter]["key"][i] == "is_not_empty":
                                filter_conditions &= Q(
                                    **{filter_key + "__isnull": False}
                                )
                            elif filter_dictionary[filter]["key"][i] == "includes":
                                filter_conditions &= Q(
                                    **{
                                        filter_key + "__icontains": filter_dictionary[
                                            filter
                                        ]["value"][i].strip()
                                    }
                                )
                            elif filter_dictionary[filter]["key"][i] == "excludes":
                                filter_conditions &= ~Q(
                                    **{
                                        filter_key + "__icontains": filter_dictionary[
                                            filter
                                        ]["value"][i].strip()
                                    }
                                )

                        elif column_type == "number":
                            if filter_key == "tax":
                                filter_conditions &= Q(
                                    **{"shopturbo_item_price__default": True}
                                )
                                if filter_dictionary[filter]["key"][i] == "less_than":
                                    filter_conditions &= Q(
                                        **{
                                            "shopturbo_item_price__"
                                            + filter_key
                                            + "__lt": filter_dictionary[filter][
                                                "value"
                                            ][i]
                                        }
                                    )
                                elif (
                                    filter_dictionary[filter]["key"][i]
                                    == "less_than_equal"
                                ):
                                    filter_conditions &= Q(
                                        **{
                                            "shopturbo_item_price__"
                                            + filter_key
                                            + "__lte": filter_dictionary[filter][
                                                "value"
                                            ][i]
                                        }
                                    )
                                elif filter_dictionary[filter]["key"][i] == "equal":
                                    filter_conditions &= Q(
                                        **{
                                            "shopturbo_item_price__"
                                            + filter_key: filter_dictionary[filter][
                                                "value"
                                            ][i]
                                        }
                                    )
                                elif (
                                    filter_dictionary[filter]["key"][i]
                                    == "greater_than"
                                ):
                                    filter_conditions &= Q(
                                        **{
                                            "shopturbo_item_price__"
                                            + filter_key
                                            + "__gt": filter_dictionary[filter][
                                                "value"
                                            ][i]
                                        }
                                    )
                                elif (
                                    filter_dictionary[filter]["key"][i]
                                    == "greater_than_equal"
                                ):
                                    filter_conditions &= Q(
                                        **{
                                            "shopturbo_item_price__"
                                            + filter_key
                                            + "__gte": filter_dictionary[filter][
                                                "value"
                                            ][i]
                                        }
                                    )
                            else:
                                if filter_dictionary[filter]["key"][i] == "less_than":
                                    filter_conditions &= Q(
                                        **{
                                            filter_key + "__lt": filter_dictionary[
                                                filter
                                            ]["value"][i]
                                        }
                                    )
                                elif (
                                    filter_dictionary[filter]["key"][i]
                                    == "less_than_equal"
                                ):
                                    filter_conditions &= Q(
                                        **{
                                            filter_key + "__lte": filter_dictionary[
                                                filter
                                            ]["value"][i]
                                        }
                                    )
                                elif filter_dictionary[filter]["key"][i] == "equal":
                                    filter_conditions &= Q(
                                        **{
                                            filter_key: filter_dictionary[filter][
                                                "value"
                                            ][i]
                                        }
                                    )
                                elif (
                                    filter_dictionary[filter]["key"][i]
                                    == "greater_than"
                                ):
                                    filter_conditions &= Q(
                                        **{
                                            filter_key + "__gt": filter_dictionary[
                                                filter
                                            ]["value"][i]
                                        }
                                    )
                                elif (
                                    filter_dictionary[filter]["key"][i]
                                    == "greater_than_equal"
                                ):
                                    filter_conditions &= Q(
                                        **{
                                            filter_key + "__gte": filter_dictionary[
                                                filter
                                            ]["value"][i]
                                        }
                                    )

                        elif filter == "item_id":
                            filter_value_ = filter_dictionary[filter]["value"][i].split(
                                ","
                            )

                            item_id_int_filter = []
                            for val_ in filter_value_:
                                # Use the validation function
                                validated_val = validate_and_convert_filter_value(val_, "int", "item_id")
                                if validated_val is not None:
                                    item_id_int_filter.append(validated_val)

                            # Only apply filter if we have valid integer values
                            if item_id_int_filter:
                                filter_conditions &= Q(
                                    **{filter + "__in": item_id_int_filter}
                                )

                        elif filter == "created_at":
                            created_at_filter = filter_dictionary.get("created_at", {})
                            filter_value = created_at_filter.get("value")
                            if filter_value:
                                if isinstance(filter_value, str):
                                    if " - " in filter_value:
                                        filter_value = filter_value.split(" - ")
                                        try:
                                            # Try to cast to datetime type
                                            filter_value = [
                                                datetime.strptime(
                                                    item.strip(), "%Y-%m-%d"
                                                )
                                                for item in filter_value
                                            ]
                                        except:
                                            try:
                                                filter_value = [
                                                    parse_date(item.strip())
                                                    for item in filter_value
                                                ]
                                                if any(
                                                    not isinstance(fv, datetime)
                                                    for fv in filter_value
                                                ):
                                                    raise Exception(
                                                        f"Invalid date filter value: {filter_value}"
                                                    )
                                            except Exception as e:
                                                print(
                                                    f"... ERROR === filter.py -- 250: {e}"
                                                )
                                                filter_value = None
                                    else:
                                        try:
                                            # Try to cast to datetime type
                                            filter_value = datetime.strptime(
                                                filter_value, "%Y-%m-%d"
                                            )
                                        except:
                                            try:
                                                filter_value = parse_date(filter_value)
                                                if not isinstance(
                                                    filter_value, datetime
                                                ):
                                                    raise Exception(
                                                        f"Invalid date filter value: {filter_value}"
                                                    )
                                            except Exception as e:
                                                print(
                                                    f"... ERROR === filter.py -- 259: {e}"
                                                )
                                                filter_value = None

                                if filter_value is not None:
                                    filter_key = created_at_filter.get("key")
                                    if filter_key == "start_date":
                                        filter_conditions &= Q(
                                            created_at__gte=filter_value
                                        )
                                    elif filter_key == "end_date":
                                        filter_conditions &= Q(
                                            created_at__lte=filter_value
                                        )
                                    elif (
                                        filter_key == "date_range"
                                        and isinstance(filter_value, (list, tuple))
                                        and len(filter_value) >= 2
                                    ):
                                        filter_conditions &= Q(
                                            created_at__range=(
                                                filter_value[0],
                                                filter_value[1],
                                            )
                                        )

                        elif filter == "supplier":
                            if filter_dictionary[filter]["key"][i] == "is_empty":
                                filter_conditions &= Q(
                                    company__isnull=True, contact__isnull=True
                                )
                            elif filter_dictionary[filter]["key"][i] == "is_not_empty":
                                filter_conditions &= Q(company__isnull=False) | Q(
                                    contact__isnull=False
                                )
                            elif filter_dictionary[filter]["key"][i] == "is":
                                filter_conditions &= (
                                    Q(
                                        company__name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                    | Q(
                                        contact__name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                    | Q(
                                        contact__last_name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                )
                            elif filter_dictionary[filter]["key"][i] == "is_not":
                                filter_conditions &= ~(
                                    Q(
                                        company__name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                    | Q(
                                        contact__name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                    | Q(
                                        contact__last_name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                )
                            elif filter_dictionary[filter]["key"][i] == "contains":
                                filter_conditions &= (
                                    Q(
                                        company__name__icontains=filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    )
                                    | Q(
                                        contact__name__icontains=filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    )
                                    | Q(
                                        contact__last_name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                )
                            elif (
                                filter_dictionary[filter]["key"][i]
                                == "does_not_contain"
                            ):
                                filter_conditions &= ~(
                                    Q(
                                        company__name__icontains=filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    )
                                    | Q(
                                        contact__name__icontains=filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    )
                                    | Q(
                                        contact__last_name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                )
                            elif filter_dictionary[filter]["key"][i] == "starts_with":
                                filter_conditions &= (
                                    Q(
                                        company__name__startswith=filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    )
                                    | Q(
                                        contact__name__startswith=filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    )
                                    | Q(
                                        contact__last_name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                )
                            elif filter_dictionary[filter]["key"][i] == "ends_with":
                                filter_conditions &= (
                                    Q(
                                        company__name__endswith=filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    )
                                    | Q(
                                        contact__name__endswith=filter_dictionary[
                                            filter
                                        ]["value"][i]
                                    )
                                    | Q(
                                        contact__last_name=filter_dictionary[filter][
                                            "value"
                                        ][i]
                                    )
                                )
                            elif filter_dictionary[filter]["key"][i] == "includes":
                                filter_conditions &= (
                                    Q(
                                        company__name__icontains=filter_dictionary[
                                            filter
                                        ]["value"][i].strip()
                                    )
                                    | Q(
                                        contact__name__icontains=filter_dictionary[
                                            filter
                                        ]["value"][i].strip()
                                    )
                                    | Q(
                                        contact__last_name=filter_dictionary[filter][
                                            "value"
                                        ][i].strip()
                                    )
                                )
                            elif filter_dictionary[filter]["key"][i] == "excludes":
                                filter_conditions &= ~(
                                    Q(
                                        company__name__icontains=filter_dictionary[
                                            filter
                                        ]["value"][i].strip()
                                    )
                                    | Q(
                                        contact__name__icontains=filter_dictionary[
                                            filter
                                        ]["value"][i].strip()
                                    )
                                    | Q(
                                        contact__last_name=filter_dictionary[filter][
                                            "value"
                                        ][i].strip()
                                    )
                                )
                        else:
                            filter_name = filter
                            try:
                                itemsCustomFieldName = (
                                    ShopTurboItemsNameCustomField.objects.filter(
                                        name__iexact=filter,
                                        workspace=view_filter.view.workspace,
                                    ).first()
                                )
                                if (not itemsCustomFieldName) and is_valid_uuid(filter):
                                    itemsCustomFieldName = (
                                        ShopTurboItemsNameCustomField.objects.filter(
                                            id=filter
                                        ).first()
                                    )
                                    if itemsCustomFieldName:
                                        filter_name = itemsCustomFieldName.name
                            except Exception as e:
                                traceback.print_exc()
                                print(f"ERROR === shopturbo.py -- 2143: {e}")
                                continue

                            if itemsCustomFieldName:
                                if itemsCustomFieldName.type == "text":
                                    key = filter_dictionary[filter]["key"][i]
                                    value = filter_dictionary[filter]["value"][i]
                                    field_conditions = Q()
                                    if key != "is_empty":
                                        field_conditions = Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="text",
                                        )

                                    if key == "is":
                                        field_conditions &= Q(
                                            shopturbo_item_custom_field_relations__value=value
                                        )
                                    elif key == "is_not":
                                        field_conditions &= ~Q(
                                            shopturbo_item_custom_field_relations__value=value
                                        )
                                    elif key == "contains":
                                        field_conditions &= Q(
                                            shopturbo_item_custom_field_relations__value__icontains=value
                                        )
                                    elif key == "does_not_contain":
                                        field_conditions &= ~Q(
                                            shopturbo_item_custom_field_relations__value__icontains=value
                                        )
                                    elif key == "starts_with":
                                        field_conditions &= Q(
                                            shopturbo_item_custom_field_relations__value__startswith=value
                                        )
                                    elif key == "ends_with":
                                        field_conditions &= Q(
                                            shopturbo_item_custom_field_relations__value__endswith=value
                                        )
                                    elif key == "is_empty":
                                        field_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="text",
                                        ) & (
                                            Q(
                                                shopturbo_item_custom_field_relations__value__isnull=True
                                            )
                                            | Q(
                                                shopturbo_item_custom_field_relations__value=""
                                            )
                                        )
                                        field_conditions |= Q(
                                            shopturbo_item_custom_field_relations__isnull=True
                                        )
                                    elif key == "is_not_empty":
                                        field_conditions &= Q(
                                            shopturbo_item_custom_field_relations__value__isnull=False
                                        )
                                    elif key == "includes":
                                        field_conditions &= Q(
                                            shopturbo_item_custom_field_relations__value__in=value.split(
                                                ","
                                            )
                                        )
                                    elif key == "excludes":
                                        field_conditions &= ~Q(
                                            shopturbo_item_custom_field_relations__value__in=value.split(
                                                ","
                                            )
                                        )

                                    filter_conditions &= field_conditions

                                elif itemsCustomFieldName.type == "choice":
                                    q = create_choice_field_filter_condition(
                                        "shopturbo_item_custom_field_relations",
                                        filter_dictionary[filter]["key"][i],
                                        filter_dictionary[filter]["value"][i],
                                    )
                                    if q.children:  # not empty filter
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="choice",
                                        )
                                        filter_conditions &= q

                                elif itemsCustomFieldName.type == "number":
                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "less_than"
                                    ):
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="number",
                                            shopturbo_item_custom_field_relations__value_number__lt=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )
                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "less_than_equal"
                                    ):
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="number",
                                            shopturbo_item_custom_field_relations__value_number__lte=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )
                                    if filter_dictionary[filter]["key"][i] == "equal":
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="number",
                                            shopturbo_item_custom_field_relations__value_number=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )
                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "greater_than"
                                    ):
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="number",
                                            shopturbo_item_custom_field_relations__value_number__gt=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )
                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "greater_than_equal"
                                    ):
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="number",
                                            shopturbo_item_custom_field_relations__value_number__gte=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )

                                elif itemsCustomFieldName.type == "formula":
                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "less_than"
                                    ):
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="formula",
                                            shopturbo_item_custom_field_relations__value_number__lt=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )
                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "less_than_equal"
                                    ):
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="formula",
                                            shopturbo_item_custom_field_relations__value_number__lte=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )
                                    if filter_dictionary[filter]["key"][i] == "equal":
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="formula",
                                            shopturbo_item_custom_field_relations__value_number=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )
                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "greater_than"
                                    ):
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="formula",
                                            shopturbo_item_custom_field_relations__value_number__gt=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )
                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "greater_than_equal"
                                    ):
                                        filter_conditions &= Q(
                                            shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                            shopturbo_item_custom_field_relations__field_name__type="formula",
                                            shopturbo_item_custom_field_relations__value_number__gte=filter_dictionary[
                                                filter
                                            ]["value"][i],
                                        )

                                elif (
                                    itemsCustomFieldName.type == "date"
                                    or itemsCustomFieldName.type == "date_time"
                                ):
                                    filter_conditions_date = Q(
                                        shopturbo_item_custom_field_relations__field_name__id=filter,
                                        shopturbo_item_custom_field_relations__field_name__type=itemsCustomFieldName.type,
                                        shopturbo_item_custom_field_relations__value__isnull=False,
                                        shopturbo_item_custom_field_relations__value_time__isnull=False,
                                    ) & ~Q(
                                        shopturbo_item_custom_field_relations__value__exact=""
                                    )

                                    if (
                                        filter_dictionary[filter]["key"][i]
                                        == "start_date"
                                    ):
                                        # Parse the date string to handle Japanese date formats
                                        parsed_date = parse_date(filter_dictionary[filter]["value"][i])
                                        if parsed_date:
                                            filter_conditions_date &= Q(
                                                shopturbo_item_custom_field_relations__value_time__gte=parsed_date
                                            )

                                    elif (
                                        filter_dictionary[filter]["key"][i]
                                        == "end_date"
                                    ):
                                        # Parse the date string to handle Japanese date formats
                                        parsed_date = parse_date(filter_dictionary[filter]["value"][i])
                                        if parsed_date:
                                            filter_conditions_date &= Q(
                                                shopturbo_item_custom_field_relations__value_time__lte=parsed_date
                                            )

                                    elif (
                                        filter_dictionary[filter]["key"][i]
                                        == "date_range"
                                    ):
                                        date_list = filter_dictionary[filter]["value"][
                                            i
                                        ].split(" - ")
                                        start_date_str = date_list[0]
                                        end_date_str = date_list[-1]

                                        # Parse the date strings to handle Japanese date formats
                                        start_date = parse_date(start_date_str)
                                        end_date = parse_date(end_date_str)

                                        if start_date and end_date:
                                            filter_conditions_date &= Q(
                                                shopturbo_item_custom_field_relations__value_time__range=(
                                                    start_date,
                                                    end_date,
                                                )
                                            )
                                    filter_conditions &= filter_conditions_date

                                elif itemsCustomFieldName.type == "user":
                                    value_list = filter_dictionary[filter]["value"][
                                        i
                                    ].split(",")
                                    filter_conditions &= Q(
                                        shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                        shopturbo_item_custom_field_relations__field_name__type="user",
                                    )
                                    filter_conditions &= Q(
                                        shopturbo_item_custom_field_relations__value__in=value_list
                                    )

                                elif itemsCustomFieldName.type == "tag":
                                    filter_text = filter_dictionary[filter]["value"][i]

                                    item_ids = []
                                    items_val = (
                                        ShopTurboItemsValueCustomField.objects.filter(
                                            field_name=itemsCustomFieldName
                                        )
                                    )
                                    for item_val in items_val:
                                        if item_val.value:
                                            if (
                                                filter_dictionary[filter]["key"][i]
                                                == "is_not_empty"
                                            ):
                                                item_ids.append(str(item_val.items.id))
                                                continue
                                            item_val_json = json.loads(item_val.value)
                                            for tag in item_val_json:
                                                if (
                                                    filter_dictionary[filter]["key"][i]
                                                    == "is"
                                                ):
                                                    if tag["value"] == filter_text:
                                                        item_ids.append(
                                                            str(item_val.items.id)
                                                        )
                                                        break
                                                elif (
                                                    filter_dictionary[filter]["key"][i]
                                                    == "is_not"
                                                ):
                                                    if tag["value"] != filter_text:
                                                        item_ids.append(
                                                            str(item_val.items.id)
                                                        )
                                                        break
                                                elif (
                                                    filter_dictionary[filter]["key"][i]
                                                    == "contains"
                                                ):
                                                    if filter_text in tag["value"]:
                                                        item_ids.append(
                                                            str(item_val.items.id)
                                                        )
                                                        break
                                                elif (
                                                    filter_dictionary[filter]["key"][i]
                                                    == "does_not_contain"
                                                ):
                                                    if filter_text not in tag["value"]:
                                                        item_ids.append(
                                                            str(item_val.items.id)
                                                        )
                                                        break
                                                elif (
                                                    filter_dictionary[filter]["key"][i]
                                                    == "starts_with"
                                                ):
                                                    if str(tag["value"]).startswith(
                                                        filter_text
                                                    ):
                                                        item_ids.append(
                                                            str(item_val.items.id)
                                                        )
                                                        break
                                                elif (
                                                    filter_dictionary[filter]["key"][i]
                                                    == "ends_with"
                                                ):
                                                    if str(tag["value"]).endswith(
                                                        filter_text
                                                    ):
                                                        item_ids.append(
                                                            str(item_val.items.id)
                                                        )
                                                        break
                                        else:
                                            if (
                                                filter_dictionary[filter]["key"][i]
                                                == "is_empty"
                                            ):
                                                item_ids.append(str(item_val.items.id))

                                    filter_conditions &= Q(
                                        shopturbo_item_custom_field_relations__field_name__id=filter,
                                        shopturbo_item_custom_field_relations__field_name__type=itemsCustomFieldName.type,
                                        id__in=item_ids,
                                    )

                elif filter == "platform_ids":
                    # Handle platform_ids filtering through the related ShopTurboItemsPlatforms model
                    if filter_dictionary[filter]["key"] == "is_empty":
                        filter_conditions &= Q(item__isnull=True)
                    elif filter_dictionary[filter]["key"] == "is_not_empty":
                        filter_conditions &= Q(item__isnull=False)
                    elif filter_dictionary[filter]["key"] == "contains":
                        filter_conditions &= Q(
                            item__platform_id__icontains=filter_dictionary[filter][
                                "value"
                            ]
                        )
                    elif filter_dictionary[filter]["key"] == "does_not_contain":
                        filter_conditions &= ~Q(
                            item__platform_id__icontains=filter_dictionary[filter][
                                "value"
                            ]
                        )
                    elif filter_dictionary[filter]["key"] == "is":
                        filter_conditions &= Q(
                            item__platform_id=filter_dictionary[filter]["value"]
                        )
                    elif filter_dictionary[filter]["key"] == "is_not":
                        filter_conditions &= ~Q(
                            item__platform_id=filter_dictionary[filter]["value"]
                        )
                    elif filter_dictionary[filter]["key"] == "starts_with":
                        filter_conditions &= Q(
                            item__platform_id__startswith=filter_dictionary[filter][
                                "value"
                            ]
                        )
                    elif filter_dictionary[filter]["key"] == "ends_with":
                        filter_conditions &= Q(
                            item__platform_id__endswith=filter_dictionary[filter][
                                "value"
                            ]
                        )

                elif (
                    filter in ["name", "description", "price", "tax", "status"]
                    or any(item in filter_lower for item in ["-item id", "-sku"])
                ) or "supplier__" in filter_lower:
                    column_type = check_columns_type(filter, default_columns_dict)
                    filter_key = filter

                    if ("-item id" in filter_lower) or ("-sku" in filter_lower):
                        if filter_dictionary[filter]["key"] == "is_not_empty":
                            if "-SKU" in filter and filter.startswith("item_platform|"):
                                id_ = filter.replace("-SKU", "").replace(
                                    "item_platform|", ""
                                )
                                filter_conditions &= Q(item__channel__id=id_)
                            elif "-Item ID" in filter and filter.startswith(
                                "item_platform|"
                            ):
                                id_ = filter.replace("-Item ID", "").replace(
                                    "item_platform|", ""
                                )
                                filter_conditions &= Q(item__channel__id=id_)
                            elif "-Variant ID" in filter and filter.startswith(
                                "item_platform|"
                            ):
                                id_ = filter.replace("-Variant ID", "").replace(
                                    "item_platform|", ""
                                )
                                filter_conditions &= Q(item__channel__id=id_)

                        if "-item id" in filter_lower:
                            filter_key = "item__platform_id"
                        elif "-sku" in filter_lower:
                            filter_key = "item__platform_sku"

                        column_type = "string"

                    if "supplier__" in filter_lower:
                        if filter_lower == "supplier__contact__first_name":
                            filter_key = "contact__name"
                        else:
                            filter_key = filter_lower.replace("supplier__", "")

                    if column_type == "string":
                        if (
                            filter == "status"
                            and filter_dictionary[filter]["value"] == "all"
                        ):
                            continue
                        if filter_dictionary[filter]["key"] == "is":
                            filter_conditions &= Q(
                                **{filter_key: filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "is_not":
                            filter_conditions &= ~Q(
                                **{filter_key: filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "contains":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__icontains": filter_dictionary[
                                        filter
                                    ]["value"]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "does_not_contain":
                            filter_conditions &= ~Q(
                                **{
                                    filter_key + "__icontains": filter_dictionary[
                                        filter
                                    ]["value"]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "starts_with":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__startswith": filter_dictionary[
                                        filter
                                    ]["value"]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "ends_with":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__endswith": filter_dictionary[
                                        filter
                                    ]["value"]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "is_empty":
                            filter_conditions &= Q(**{filter_key + "__isnull": True})
                        elif filter_dictionary[filter]["key"] == "is_not_empty":
                            filter_conditions &= Q(**{filter_key + "__isnull": False})
                        elif filter_dictionary[filter]["key"] == "includes":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__icontains": filter_dictionary[
                                        filter
                                    ]["value"].strip()
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "excludes":
                            filter_conditions &= ~Q(
                                **{
                                    filter_key + "__icontains": filter_dictionary[
                                        filter
                                    ]["value"].strip()
                                }
                            )

                    elif column_type == "number":
                        if filter_key == "tax":
                            filter_conditions &= Q(
                                **{"shopturbo_item_price__default": True}
                            )
                            if filter_dictionary[filter]["key"] == "less_than":
                                filter_conditions &= Q(
                                    **{
                                        "shopturbo_item_price__"
                                        + filter_key
                                        + "__lt": filter_dictionary[filter]["value"]
                                    }
                                )
                            elif filter_dictionary[filter]["key"] == "less_than_equal":
                                filter_conditions &= Q(
                                    **{
                                        "shopturbo_item_price__"
                                        + filter_key
                                        + "__lte": filter_dictionary[filter]["value"]
                                    }
                                )
                            elif filter_dictionary[filter]["key"] == "equal":
                                filter_conditions &= Q(
                                    **{
                                        "shopturbo_item_price__"
                                        + filter_key: filter_dictionary[filter]["value"]
                                    }
                                )
                            elif filter_dictionary[filter]["key"] == "greater_than":
                                filter_conditions &= Q(
                                    **{
                                        "shopturbo_item_price__"
                                        + filter_key
                                        + "__gt": filter_dictionary[filter]["value"]
                                    }
                                )
                            elif (
                                filter_dictionary[filter]["key"] == "greater_than_equal"
                            ):
                                filter_conditions &= Q(
                                    **{
                                        "shopturbo_item_price__"
                                        + filter_key
                                        + "__gte": filter_dictionary[filter]["value"]
                                    }
                                )
                        else:
                            if filter_dictionary[filter]["key"] == "less_than":
                                filter_conditions &= Q(
                                    **{
                                        filter_key + "__lt": filter_dictionary[filter][
                                            "value"
                                        ]
                                    }
                                )
                            elif filter_dictionary[filter]["key"] == "less_than_equal":
                                filter_conditions &= Q(
                                    **{
                                        filter_key + "__lte": filter_dictionary[filter][
                                            "value"
                                        ]
                                    }
                                )
                            elif filter_dictionary[filter]["key"] == "equal":
                                filter_conditions &= Q(
                                    **{filter_key: filter_dictionary[filter]["value"]}
                                )
                            elif filter_dictionary[filter]["key"] == "greater_than":
                                filter_conditions &= Q(
                                    **{
                                        filter_key + "__gt": filter_dictionary[filter][
                                            "value"
                                        ]
                                    }
                                )
                            elif (
                                filter_dictionary[filter]["key"] == "greater_than_equal"
                            ):
                                filter_conditions &= Q(
                                    **{
                                        filter_key + "__gte": filter_dictionary[filter][
                                            "value"
                                        ]
                                    }
                                )

                elif filter == "item_id":
                    filter_key = filter

                    filter_value_ = filter_dictionary[filter]["value"].split(",")

                    if filter_dictionary[filter]["key"] == "csv_contains":
                        item_id_int_filter = []
                        for val_ in filter_value_:
                            # Use the validation function
                            validated_val = validate_and_convert_filter_value(val_, "int", "item_id")
                            if validated_val is not None:
                                item_id_int_filter.append(validated_val)

                        if item_id_int_filter:  # Only apply filter if we have valid values
                            filter_conditions &= Q(**{filter + "__in": item_id_int_filter})
                    else:
                        # Validate and convert the first filter value to integer using our validation function
                        if filter_value_:
                            filter_value_int = validate_and_convert_filter_value(filter_value_[0], "int", "item_id")
                            if filter_value_int is not None:
                                # Apply the appropriate filter with validated integer value
                                if filter_dictionary[filter]["key"] == "less_than":
                                    filter_conditions &= Q(
                                        **{filter_key + "__lt": filter_value_int}
                                    )
                                elif filter_dictionary[filter]["key"] == "less_than_equal":
                                    filter_conditions &= Q(
                                        **{filter_key + "__lte": filter_value_int}
                                    )
                                elif filter_dictionary[filter]["key"] == "equal":
                                    filter_conditions &= Q(**{filter_key: filter_value_int})
                                elif filter_dictionary[filter]["key"] == "greater_than":
                                    filter_conditions &= Q(
                                        **{filter_key + "__gt": filter_value_int}
                                    )
                                elif filter_dictionary[filter]["key"] == "greater_than_equal":
                                    filter_conditions &= Q(
                                        **{filter_key + "__gte": filter_value_int}
                                    )

                elif filter == "supplier":
                    if filter_dictionary[filter]["key"] == "is_empty":
                        filter_conditions &= Q(
                            company__isnull=True, contact__isnull=True
                        )
                    elif filter_dictionary[filter]["key"] == "is_not_empty":
                        filter_conditions &= Q(company__isnull=False) | Q(
                            contact__isnull=False
                        )
                    elif filter_dictionary[filter]["key"] == "is":
                        filter_conditions &= (
                            Q(company__name=filter_dictionary[filter]["value"])
                            | Q(contact__name=filter_dictionary[filter]["value"])
                            | Q(contact__last_name=filter_dictionary[filter]["value"])
                        )
                    elif filter_dictionary[filter]["key"] == "is_not":
                        filter_conditions &= ~(
                            Q(company__name=filter_dictionary[filter]["value"])
                            | Q(contact__name=filter_dictionary[filter]["value"])
                            | Q(contact__last_name=filter_dictionary[filter]["value"])
                        )
                    elif filter_dictionary[filter]["key"] == "contains":
                        filter_conditions &= (
                            Q(
                                company__name__icontains=filter_dictionary[filter][
                                    "value"
                                ]
                            )
                            | Q(
                                contact__name__icontains=filter_dictionary[filter][
                                    "value"
                                ]
                            )
                            | Q(contact__last_name=filter_dictionary[filter]["value"])
                        )
                    elif filter_dictionary[filter]["key"] == "does_not_contain":
                        filter_conditions &= ~(
                            Q(
                                company__name__icontains=filter_dictionary[filter][
                                    "value"
                                ]
                            )
                            | Q(
                                contact__name__icontains=filter_dictionary[filter][
                                    "value"
                                ]
                            )
                            | Q(contact__last_name=filter_dictionary[filter]["value"])
                        )
                    elif filter_dictionary[filter]["key"] == "starts_with":
                        filter_conditions &= (
                            Q(
                                company__name__startswith=filter_dictionary[filter][
                                    "value"
                                ]
                            )
                            | Q(
                                contact__name__startswith=filter_dictionary[filter][
                                    "value"
                                ]
                            )
                            | Q(contact__last_name=filter_dictionary[filter]["value"])
                        )
                    elif filter_dictionary[filter]["key"] == "ends_with":
                        filter_conditions &= (
                            Q(
                                company__name__endswith=filter_dictionary[filter][
                                    "value"
                                ]
                            )
                            | Q(
                                contact__name__endswith=filter_dictionary[filter][
                                    "value"
                                ]
                            )
                            | Q(contact__last_name=filter_dictionary[filter]["value"])
                        )
                    elif filter_dictionary[filter]["key"] == "includes":
                        filter_conditions &= (
                            Q(
                                company__name__icontains=filter_dictionary[filter][
                                    "value"
                                ].strip()
                            )
                            | Q(
                                contact__name__icontains=filter_dictionary[filter][
                                    "value"
                                ].strip()
                            )
                            | Q(
                                contact__last_name=filter_dictionary[filter][
                                    "value"
                                ].strip()
                            )
                        )
                    elif filter_dictionary[filter]["key"] == "excludes":
                        filter_conditions &= ~(
                            Q(
                                company__name__icontains=filter_dictionary[filter][
                                    "value"
                                ].strip()
                            )
                            | Q(
                                contact__name__icontains=filter_dictionary[filter][
                                    "value"
                                ].strip()
                            )
                            | Q(
                                contact__last_name=filter_dictionary[filter][
                                    "value"
                                ].strip()
                            )
                        )

                elif filter == "created_at":
                    created_at_filter = filter_dictionary.get("created_at", {})
                    filter_value = created_at_filter.get("value")
                    if filter_value:
                        if isinstance(filter_value, str):
                            if " - " in filter_value:
                                filter_value = filter_value.split(" - ")
                                try:
                                    # Try to cast to datetime type
                                    filter_value = [
                                        datetime.strptime(item.strip(), "%Y-%m-%d")
                                        for item in filter_value
                                    ]
                                except:
                                    try:
                                        filter_value = [
                                            parse_date(item.strip())
                                            for item in filter_value
                                        ]
                                        if any(
                                            not isinstance(fv, datetime)
                                            for fv in filter_value
                                        ):
                                            raise Exception(
                                                f"Invalid date filter value: {filter_value}"
                                            )
                                    except Exception as e:
                                        print(f"... ERROR === filter.py -- 250: {e}")
                                        filter_value = None
                            else:
                                try:
                                    # Try to cast to datetime type
                                    filter_value = datetime.strptime(
                                        filter_value, "%Y-%m-%d"
                                    )
                                except:
                                    try:
                                        filter_value = parse_date(filter_value)
                                        if not isinstance(filter_value, datetime):
                                            raise Exception(
                                                f"Invalid date filter value: {filter_value}"
                                            )
                                    except Exception as e:
                                        print(f"... ERROR === filter.py -- 259: {e}")
                                        filter_value = None

                        if filter_value is not None:
                            filter_key = created_at_filter.get("key")
                            if filter_key == "start_date":
                                filter_conditions &= Q(created_at__gte=filter_value)
                            elif filter_key == "end_date":
                                filter_conditions &= Q(created_at__lte=filter_value)
                            elif (
                                filter_key == "date_range"
                                and isinstance(filter_value, (list, tuple))
                                and len(filter_value) >= 2
                            ):
                                filter_conditions &= Q(
                                    created_at__range=(filter_value[0], filter_value[1])
                                )

                elif filter == 'owner':
                    if filter_dictionary['owner']["value"]:
                        filter_value_ = filter_dictionary['owner']["value"]
                        if filter_value_ == 'myself':
                            filter_value_ = request.user if request and request.user else None
                        else:
                            User = get_user_model()
                            # User Instance
                            filter_value_ = User.objects.filter(id=filter_value_).first()
                        filter_value_ = UserManagement.objects.filter(
                            workspace=get_workspace(request.user),
                            user=filter_value_
                        ).first()
                        filter_conditions &= (Q(**{f"{filter}": filter_value_}))
                else:
                    filter_name = filter
                    try:
                        itemsCustomFieldName = (
                            ShopTurboItemsNameCustomField.objects.filter(
                                name__iexact=filter,
                                workspace=view_filter.view.workspace,
                            ).first()
                        )
                        if (not itemsCustomFieldName) and is_valid_uuid(filter):
                            itemsCustomFieldName = (
                                ShopTurboItemsNameCustomField.objects.filter(
                                    id=filter
                                ).first()
                            )
                            if itemsCustomFieldName:
                                filter_name = itemsCustomFieldName.name
                    except Exception as e:
                        traceback.print_exc()
                        print(f"ERROR === shopturbo.py -- 2143: {e}")
                        continue

                    if itemsCustomFieldName:
                        if itemsCustomFieldName.type == "text":
                            key = filter_dictionary[filter]["key"]
                            value = filter_dictionary[filter]["value"]
                            field_conditions = Q()
                            if key != "is_empty":
                                field_conditions = Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="text",
                                )

                            if key == "is":
                                field_conditions &= Q(
                                    shopturbo_item_custom_field_relations__value=value
                                )
                            elif key == "is_not":
                                field_conditions &= ~Q(
                                    shopturbo_item_custom_field_relations__value=value
                                )
                            elif key == "contains":
                                field_conditions &= Q(
                                    shopturbo_item_custom_field_relations__value__icontains=value
                                )
                            elif key == "does_not_contain":
                                field_conditions &= ~Q(
                                    shopturbo_item_custom_field_relations__value__icontains=value
                                )
                            elif key == "starts_with":
                                field_conditions &= Q(
                                    shopturbo_item_custom_field_relations__value__startswith=value
                                )
                            elif key == "ends_with":
                                field_conditions &= Q(
                                    shopturbo_item_custom_field_relations__value__endswith=value
                                )
                            elif key == "is_empty":
                                field_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="text",
                                ) & (
                                    Q(
                                        shopturbo_item_custom_field_relations__value__isnull=True
                                    )
                                    | Q(shopturbo_item_custom_field_relations__value="")
                                )
                                field_conditions |= Q(
                                    shopturbo_item_custom_field_relations__isnull=True
                                )
                            elif key == "is_not_empty":
                                field_conditions &= Q(
                                    shopturbo_item_custom_field_relations__value__isnull=False
                                )
                            elif key == "includes":
                                field_conditions &= Q(
                                    shopturbo_item_custom_field_relations__value__in=value.split(
                                        ","
                                    )
                                )
                            elif key == "excludes":
                                field_conditions &= ~Q(
                                    shopturbo_item_custom_field_relations__value__in=value.split(
                                        ","
                                    )
                                )

                            filter_conditions &= field_conditions

                        elif itemsCustomFieldName.type == "choice":
                            q = create_choice_field_filter_condition(
                                "shopturbo_item_custom_field_relations",
                                filter_dictionary[filter]["key"],
                                filter_dictionary[filter]["value"],
                            )
                            if q.children:  # not empty filter
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="choice",
                                )
                                filter_conditions &= q

                        elif itemsCustomFieldName.type == "number":
                            if filter_dictionary[filter]["key"] == "less_than":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="number",
                                    shopturbo_item_custom_field_relations__value_number__lt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "less_than_equal":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="number",
                                    shopturbo_item_custom_field_relations__value_number__lte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "equal":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="number",
                                    shopturbo_item_custom_field_relations__value_number=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="number",
                                    shopturbo_item_custom_field_relations__value_number__gt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than_equal":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="number",
                                    shopturbo_item_custom_field_relations__value_number__gte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )

                        elif itemsCustomFieldName.type == "formula":
                            if filter_dictionary[filter]["key"] == "less_than":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="formula",
                                    shopturbo_item_custom_field_relations__value_number__lt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "less_than_equal":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="formula",
                                    shopturbo_item_custom_field_relations__value_number__lte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "equal":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="formula",
                                    shopturbo_item_custom_field_relations__value_number=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="formula",
                                    shopturbo_item_custom_field_relations__value_number__gt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than_equal":
                                filter_conditions &= Q(
                                    shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                    shopturbo_item_custom_field_relations__field_name__type="formula",
                                    shopturbo_item_custom_field_relations__value_number__gte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )

                        elif (
                            itemsCustomFieldName.type == "date"
                            or itemsCustomFieldName.type == "date_time"
                        ):
                            filter_conditions_date = Q(
                                shopturbo_item_custom_field_relations__field_name__id=filter,
                                shopturbo_item_custom_field_relations__field_name__type=itemsCustomFieldName.type,
                                shopturbo_item_custom_field_relations__value__isnull=False,
                                shopturbo_item_custom_field_relations__value_time__isnull=False,
                            ) & ~Q(
                                shopturbo_item_custom_field_relations__value__exact=""
                            )

                            if filter_dictionary[filter]["key"] == "start_date":
                                # Parse the date string to handle Japanese date formats
                                parsed_date = parse_date(filter_dictionary[filter]["value"])
                                if parsed_date:
                                    filter_conditions_date &= Q(
                                        shopturbo_item_custom_field_relations__value_time__gte=parsed_date
                                    )

                            elif filter_dictionary[filter]["key"] == "end_date":
                                # Parse the date string to handle Japanese date formats
                                parsed_date = parse_date(filter_dictionary[filter]["value"])
                                if parsed_date:
                                    filter_conditions_date &= Q(
                                        shopturbo_item_custom_field_relations__value_time__lte=parsed_date
                                    )

                            elif filter_dictionary[filter]["key"] == "date_range":
                                date_list = filter_dictionary[filter]["value"].split(
                                    " - "
                                )
                                start_date_str = date_list[0]
                                end_date_str = date_list[-1]

                                # Parse the date strings to handle Japanese date formats
                                start_date = parse_date(start_date_str)
                                end_date = parse_date(end_date_str)

                                if start_date and end_date:
                                    filter_conditions_date &= Q(
                                        shopturbo_item_custom_field_relations__value_time__range=(
                                            start_date,
                                            end_date,
                                        )
                                    )
                            filter_conditions &= filter_conditions_date

                        elif itemsCustomFieldName.type == "user":
                            value_list = filter_dictionary[filter]["value"].split(",")
                            filter_conditions &= Q(
                                shopturbo_item_custom_field_relations__field_name__name=filter_name,
                                shopturbo_item_custom_field_relations__field_name__type="user",
                            )
                            filter_conditions &= Q(
                                shopturbo_item_custom_field_relations__value__in=value_list
                            )

                        elif itemsCustomFieldName.type == "tag":
                            filter_text = filter_dictionary[filter]["value"]

                            item_ids = []
                            items_val = ShopTurboItemsValueCustomField.objects.filter(
                                field_name=itemsCustomFieldName
                            )
                            for item_val in items_val:
                                if item_val.value:
                                    if (
                                        filter_dictionary[filter]["key"]
                                        == "is_not_empty"
                                    ):
                                        item_ids.append(str(item_val.items.id))
                                        continue
                                    item_val_json = json.loads(item_val.value)
                                    for tag in item_val_json:
                                        if filter_dictionary[filter]["key"] == "is":
                                            if tag["value"] == filter_text:
                                                item_ids.append(str(item_val.items.id))
                                                break
                                        elif (
                                            filter_dictionary[filter]["key"] == "is_not"
                                        ):
                                            if tag["value"] != filter_text:
                                                item_ids.append(str(item_val.items.id))
                                                break
                                        elif (
                                            filter_dictionary[filter]["key"]
                                            == "contains"
                                        ):
                                            if filter_text in tag["value"]:
                                                item_ids.append(str(item_val.items.id))
                                                break
                                        elif (
                                            filter_dictionary[filter]["key"]
                                            == "does_not_contain"
                                        ):
                                            if filter_text not in tag["value"]:
                                                item_ids.append(str(item_val.items.id))
                                                break
                                        elif (
                                            filter_dictionary[filter]["key"]
                                            == "starts_with"
                                        ):
                                            if str(tag["value"]).startswith(
                                                filter_text
                                            ):
                                                item_ids.append(str(item_val.items.id))
                                                break
                                        elif (
                                            filter_dictionary[filter]["key"]
                                            == "ends_with"
                                        ):
                                            if str(tag["value"]).endswith(filter_text):
                                                item_ids.append(str(item_val.items.id))
                                                break
                                else:
                                    if filter_dictionary[filter]["key"] == "is_empty":
                                        item_ids.append(str(item_val.items.id))

                            filter_conditions &= Q(
                                shopturbo_item_custom_field_relations__field_name__id=filter,
                                shopturbo_item_custom_field_relations__field_name__type=itemsCustomFieldName.type,
                                id__in=item_ids,
                            )

    if not validate_filter_conditions(ShopTurboItems, filter_conditions):
        return original_filter_conditions
    return filter_conditions


def check_columns_type(data_filter: str, default_columns_dict: Dict[str, str]) -> str:
    """Check column type based on field name.

    Args:
        data_filter: Field name to check
        default_columns_dict: Dictionary mapping field names to their types

    Returns:
        Column type as string ('string', 'number', or 'date')
    """
    data_type = "string"
    if data_filter in default_columns_dict:
        field_type = default_columns_dict[data_filter].lower()
        if any(check in field_type for check in ["float", "integer"]):
            data_type = "number"
        elif "date" in field_type:
            data_type = "date"
    return data_type


@require_POST
@login_or_hubspot_required
def create_items(request):
    target = TYPE_OBJECT_ITEM
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    view_id = None
    shopturboitem = None
    source_url = request.POST.get("source_url")
    module_slug = request.POST.get("module", None)
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ITEM]
    module = Module.objects.filter(
        workspace=workspace, object_values__contains=TYPE_OBJECT_ITEM
    ).order_by("order", "created_at")
    if module_slug:
        module = module.filter(slug=module_slug).first()
    else:
        module = module.first()
        module_slug = module.slug

    view_id = request.POST.get("view_id")
    page = request.POST.get("page")
    item = None

    default_currency = "USD"
    if request.LANGUAGE_CODE == "ja":
        default_currency = "JPY"
    if workspace.currencies:
        try:
            default_currency = ast.literal_eval(workspace.currencies)
            default_currency = default_currency[0]
        except:
            pass

    if "single-entry" in request.POST:
        name = request.POST.get("name", None)
        description = request.POST.get("description", None)
        supplier = request.POST.get("supplier", None)
        inventory_checkbox = request.POST.get("inventory_checkbox", None)
        owner = request.POST.get('owner', None)
        
        item_price_ids = request.POST.getlist("item_price_id", None)
        item_purchase_price_ids = request.POST.getlist("item_purchase_price_id", None)

        default_item_price_id = request.POST.get("default", None)
        if default_item_price_id == "False":
            default_item_price_id = None

        default_item_purchase_price_id = request.POST.get(
            "default-purchase-price", None
        )
        if default_item_purchase_price_id == "False":
            default_item_purchase_price_id = None

        # CHECK LIMIT
        if not has_quota(workspace, ITEM_USAGE_CATEGORY):
            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="一部のアイテムは上限を超えたため、作成できませんでした。サブスクリプションプランをアップグレードして容量を増やすか、既存のアイテムをアーカイブして容量を空けてください。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Some items could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                    type="error",
                )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?view_id={view_id}"
                )
            return redirect(reverse("main", host="app"))

        shopturboitem = ShopTurboItems.objects.create(
            workspace=workspace,
            name=name,
            status="active",
        )

        assign_object_owner(shopturboitem,owner,request,TYPE_OBJECT_ITEM)

        if supplier:
            contact = Contact.objects.filter(workspace=workspace, id=supplier).first()
            if contact:
                shopturboitem.contact = contact
                shopturboitem.company = None

            else:
                company = Company.objects.filter(
                    workspace=workspace, id=supplier
                ).first()
                if company:
                    shopturboitem.company = company
                    shopturboitem.contact = None

        shopturboitem.save(
            log_data={"user": request.user, "status": "create", "workspace": workspace}
        )

        if description:
            shopturboitem.description = description

        currency = None
        # attach item price to item
        for item_price_id in item_price_ids:
            try:
                shopturboitem_price = ShopTurboItemsPrice.objects.get(id=item_price_id)
                if shopturboitem_price:
                    shopturboitem_price.item = shopturboitem
                    shopturboitem_price.save()
            except ShopTurboItemsPrice.DoesNotExist:
                print(f"Price with id {item_price_id} does not exist")
        else:
            shopturboitem_price = ShopTurboItemsPrice.objects.create(
                workspace=workspace,
                item=shopturboitem,
                price=0,
                currency=default_currency,
                default=True,
            )

        # SET DEFAULT for Shopturbo Item Price
        price_conditions = Q(item=shopturboitem, default=True)
        shopturboitem_price = (
            ShopTurboItemsPrice.objects.filter(price_conditions)
            .order_by("created_at")
            .update(default=False)
        )
        if default_item_price_id:
            price_conditions = Q(id=default_item_price_id)
            shopturboitem_price = (
                ShopTurboItemsPrice.objects.filter(price_conditions)
                .order_by("created_at")
                .first()
            )
        else:
            shopturboitem_price = (
                ShopTurboItemsPrice.objects.filter(price_conditions)
                .order_by("created_at")
                .first()
            )
            if not shopturboitem_price:
                shopturboitem_price = (
                    ShopTurboItemsPrice.objects.filter(item=shopturboitem)
                    .order_by("created_at")
                    .first()
                )

        if shopturboitem_price:
            currency = shopturboitem_price.currency
            shopturboitem_price.default = True
            shopturboitem_price.save(
                log_data={"user": request.user, "workspace": workspace}
            )
            shopturboitem.price = shopturboitem_price.price

        # attach item purchase price to item
        for item_purchase_price_id in item_purchase_price_ids:
            try:
                shopturboitem_purchase_price = ItemPurchasePrice.objects.get(
                    id=item_purchase_price_id
                )
                if shopturboitem_purchase_price:
                    shopturboitem_purchase_price.item = shopturboitem
                    shopturboitem_purchase_price.save()
            except ItemPurchasePrice.DoesNotExist:
                print(f"Purchase price with id {item_purchase_price_id} does not exist")
        else:
            shopturboitem_purchase_price = ItemPurchasePrice.objects.create(
                workspace=workspace,
                item=shopturboitem,
                price=0,
                currency=default_currency,
                default=True,
            )

        # SET DEFAULT for Purchase Price
        price_conditions = Q(item=shopturboitem, default=True)
        shopturboitem_purchase_price = (
            ItemPurchasePrice.objects.filter(price_conditions)
            .order_by("created_at")
            .update(default=False)
        )
        if default_item_purchase_price_id:
            price_conditions = Q(id=default_item_purchase_price_id)
            shopturboitem_purchase_price = (
                ItemPurchasePrice.objects.filter(price_conditions)
                .order_by("created_at")
                .first()
            )
        else:
            shopturboitem_purchase_price = (
                ItemPurchasePrice.objects.filter(price_conditions)
                .order_by("created_at")
                .first()
            )
            if not shopturboitem_purchase_price:
                shopturboitem_purchase_price = (
                    ItemPurchasePrice.objects.filter(item=shopturboitem)
                    .order_by("created_at")
                    .first()
                )

        if shopturboitem_purchase_price:
            shopturboitem_purchase_price.default = True
            shopturboitem_purchase_price.save(
                log_data={"user": request.user, "workspace": workspace}
            )
            shopturboitem.purchase_price = shopturboitem_purchase_price.price

        if currency:
            shopturboitem.currency = currency

        save_custom_property(request, shopturboitem)

        # Create Inventory
        if inventory_checkbox:
            inventory_status = request.POST.get("inventory_status", None)
            initial_value = request.POST.get("initial_value", None)

            inventory = None
            if "inventory" in request.POST:
                if initial_value is not None and inventory_status in [
                    "available",
                    "committed",
                    "unavailable",
                ]:
                    inventory_id = request.POST.get("inventory_id", None)
                    if inventory_id:
                        try:
                            inventory = ShopTurboInventory.objects.get(id=inventory_id)
                        except:
                            if module:
                                return redirect(
                                    reverse(
                                        "load_object_page",
                                        host="app",
                                        kwargs={
                                            "module_slug": module_slug,
                                            "object_slug": module_object_slug,
                                        },
                                    )
                                    + f"?view_id={view_id}&target={target}&item_id={shopturboitem.id}&page={page}"
                                )
                            return redirect(reverse("main", host="app"))
                        inventory.inventory_status = inventory_status
                    else:
                        if not has_quota(workspace, INVENTORY_USAGE_CATEGORY):
                            msg = "Inventory could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing inventories to free up space."
                            if lang == "ja":
                                msg = "制限を超えたため、在庫を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存の在庫の一部をアーカイブしてスペースを解放してください。"
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=msg,
                                type="error",
                            )
                        else:
                            inventory = ShopTurboInventory.objects.create(
                                workspace=workspace,
                                status="active",
                                inventory_status=inventory_status,
                            )
                            inventory.save(
                                log_data={
                                    "user": request.user,
                                    "status": "create",
                                    "workspace": workspace,
                                }
                            )

                            create_inventory_transaction_helper(
                                inventory, request.user, lang=lang
                            )
            elif "inventory-sync" in request.POST:
                sync_inventory_id = request.POST.get("sync_inventories", None)
                try:
                    inventory = ShopTurboInventory.objects.get(id=sync_inventory_id)
                except:
                    if module:
                        return redirect(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module_slug,
                                    "object_slug": module_object_slug,
                                },
                            )
                            + f"?view_id={view_id}&target={target}&item_id={shopturboitem.id}&page={page}"
                        )
                    return redirect(reverse("main", host="app"))

            if inventory:
                inventory.item.add(shopturboitem)
                inventory.initial_value = initial_value
                inventory.save(log_data={"user": request.user, "workspace": workspace})

        # shopturboitem.property_set_id = request.POST.get('set_id')
        shopturboitem.save(log_data={"user": request.user, "workspace": workspace})

        if request.POST.get("type_association", "") == "create-association":
            source = request.POST.get("source")
            if "object_type" in request.POST:
                object_type = request.POST.get("object_type")
            module_object_slug = OBJECT_TYPE_TO_SLUG[source]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=source
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
            if source == TYPE_OBJECT_INVENTORY:
                object_id = request.POST.get("source_object_id")
                inventory_obj = ShopTurboInventory.objects.filter(id=object_id).first()
                if inventory_obj and shopturboitem:
                    inventory_obj.item.clear()
                    inventory_obj.item.add(shopturboitem)
                    inventory_obj.save()
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={object_id}"
                )
            elif source == TYPE_OBJECT_SUBSCRIPTION:
                object_id = request.POST.get("source_object_id")
                sub_obj = ShopTurboSubscriptions.objects.filter(id=object_id).first()
                if sub_obj and shopturboitem:
                    sub_obj.item = shopturboitem
                    sub_obj.save()
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={object_id}"
                )
            elif source == TYPE_OBJECT_ORDER:
                object_id = request.POST.get("source_object_id")
                order = ShopTurboOrders.objects.filter(id=object_id).first()
                if order and shopturboitem:
                    order.item.add(shopturboitem)
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={object_id}"
                )

        # Clean up any remaining temporary prices after successful item creation
        ShopTurboItemsPrice.objects.filter(
            workspace=workspace, item__isnull=True
        ).delete()
        ItemPurchasePrice.objects.filter(
            workspace=workspace, item__isnull=True
        ).delete()

    elif "submit_csv_upload" in request.POST:
        csv = request.FILES.get("csv_upload", False)
        if not csv:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Failed to retreive the CSV file.",
                type="error",
            )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        file_columns = request.POST.getlist("item-file-column", [])
        sanka_properties = request.POST.getlist("item-sanka-properties")
        ignores = request.POST.getlist("item-ignore")

        import_method_items = request.POST.get("import-method-item")
        items_key_field = request.POST.getlist("item-key-field", [])

        # Checking Key
        if not any(item in sanka_properties for item in items_key_field):
            import_method_items = "create"

        try:
            # save mapping
            mapping_storage, _ = ImportMappingFields.objects.get_or_create(
                workspace=workspace, object_type=TYPE_OBJECT_ITEM
            )
            if mapping_storage:
                mapping = {
                    sanka_prop: file_col
                    for sanka_prop, file_col in zip(sanka_properties, file_columns)
                }
                setattr(mapping_storage, "input_pair", mapping)
                mapping_storage.save()

            with csv.open() as f:
                # checking encoding
                read_data = f.read()
                result = chardet.detect(read_data)
                encoding = result["encoding"]
                # #Reset Pointer
                f.seek(0)
                if encoding.lower() == "shift_jis":
                    content = f.read()
                    decoded_content = content.decode("shift_jis")
                    string_data = StringIO(decoded_content)
                    df = pd.read_csv(
                        string_data, sep=",", encoding="shift_jis", dtype=str
                    )
                else:
                    df = pd.read_csv(f, dtype=str)

                df = df.dropna(axis=1, how="all")
                df = df.dropna(axis=0, how="all")
                df = df.reset_index(drop=True)

            df = df.astype(str)
            for _, row in df.iterrows():
                create_json_items = {}
                create_json_items_custom_field = {}
                create_json_items_component_qty = {}
                for idx, file_column in enumerate(file_columns):
                    # check ignore
                    if ignores[idx] == "True":
                        continue

                    if row[file_column] == "nan" or row[file_column] == None:
                        continue

                    if is_valid_uuid(sanka_properties[idx]):
                        custom_field = ShopTurboItemsNameCustomField.objects.filter(
                            id=sanka_properties[idx], workspace=workspace
                        ).first()
                        create_json_items_custom_field[custom_field.name] = row[
                            file_column
                        ]
                    elif "component_quantity|" in sanka_properties[idx]:
                        quantity_uuid = sanka_properties[idx].split("|")[1]
                        if is_valid_uuid(quantity_uuid):
                            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                id=quantity_uuid, workspace=workspace
                            ).first()
                            if custom_field:
                                create_json_items_component_qty[
                                    str(custom_field.id)
                                ] = row[file_column]
                    else:
                        obj = row[file_column]
                        if sanka_properties[idx] == "currency":
                            obj = row[file_column].upper()
                        if sanka_properties[idx] == "price_of_items":
                            if "," in row[file_column]:
                                obj = row[file_column].replace(",", "")
                        if sanka_properties[idx] == "tax_price_of_items":
                            if "%" in row[file_column]:
                                obj = row[file_column].replace("%", "")
                        if sanka_properties[idx] == "purchase_price_of_items":
                            if "," in row[file_column]:
                                obj = row[file_column].replace(",", "")
                        if sanka_properties[idx] == "tax_purchase_price_of_items":
                            if "%" in row[file_column]:
                                obj = row[file_column].replace("%", "")
                        row[file_column] = obj

                        create_json_items[sanka_properties[idx]] = row[file_column]

                insert_price_items = ""
                if "price_of_items" in create_json_items:
                    insert_price_items = create_json_items.pop("price_of_items")

                insert_tax_price_items = ""
                if "tax_price_of_items" in create_json_items:
                    insert_tax_price_items = create_json_items.pop("tax_price_of_items")

                insert_purchase_price_items = ""
                if "purchase_price_of_items" in create_json_items:
                    insert_purchase_price_items = create_json_items.pop(
                        "purchase_price_of_items"
                    )

                insert_tax_purchase_price_items = ""
                if "tax_purchase_price_of_items" in create_json_items:
                    insert_tax_purchase_price_items = create_json_items.pop(
                        "tax_purchase_price_of_items"
                    )

                if import_method_items == "create_and_update":
                    if items_key_field:
                        item_key_field_json = {}
                        for item_key in items_key_field:
                            if item_key in create_json_items:
                                if create_json_items[item_key]:
                                    item_key_field_json[item_key] = create_json_items[
                                        item_key
                                    ]

                        try:
                            item = ShopTurboItems.objects.get(
                                **item_key_field_json, workspace=workspace
                            )

                        except ShopTurboItems.MultipleObjectsReturned:
                            item = ShopTurboItems.objects.filter(
                                Q(**item_key_field_json, workspace=workspace)
                            ).first()
                            for item_key_field_sub_json in item_key_field_json:
                                setattr(
                                    item,
                                    item_key_field_sub_json,
                                    item_key_field_json[item_key_field_sub_json],
                                )

                        except ShopTurboItems.DoesNotExist:
                            if not has_quota(workspace, ITEM_USAGE_CATEGORY):
                                if lang == "ja":
                                    Notification.objects.create(
                                        workspace=get_workspace(request.user),
                                        user=request.user,
                                        message="一部のアイテムは上限を超えたため、作成できませんでした。サブスクリプションプランをアップグレードして容量を増やすか、既存のアイテムをアーカイブして容量を空けてください。",
                                        type="error",
                                    )
                                else:
                                    Notification.objects.create(
                                        workspace=get_workspace(request.user),
                                        user=request.user,
                                        message="Some items could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                                        type="error",
                                    )
                                if module:
                                    return redirect(
                                        reverse(
                                            "load_object_page",
                                            host="app",
                                            kwargs={
                                                "module_slug": module_slug,
                                                "object_slug": module_object_slug,
                                            },
                                        )
                                    )
                                return redirect(reverse("main", host="app"))

                            if "item_id" in create_json_items:
                                create_json_items.pop("item_id")

                            item = ShopTurboItems.objects.create(
                                **create_json_items,
                                status="active",
                                workspace=workspace,
                            )
                            item.save(
                                log_data={
                                    "user": request.user,
                                    "status": "create",
                                    "workspace": workspace,
                                }
                            )  # Log
                            add_usage(workspace, ITEM_USAGE_CATEGORY)

                        if not item:
                            return redirect("shopturbo_items")

                        if insert_price_items:
                            insert_price_items = insert_price_items.split(
                                CSV_DELIMITER_LIST_FIELD
                            )
                            if insert_tax_price_items:
                                insert_tax_price_items = insert_tax_price_items.split(
                                    CSV_DELIMITER_LIST_FIELD
                                )

                            for idx, insert_price in enumerate(insert_price_items):
                                item_price = ShopTurboItemsPrice.objects.filter(
                                    item=item,
                                    price=insert_price,
                                    currency=item.currency,
                                ).first()
                                if not item_price:
                                    item_price, _ = (
                                        ShopTurboItemsPrice.objects.get_or_create(
                                            item=item,
                                            price=insert_price,
                                            currency=item.currency,
                                        )
                                    )

                                if insert_tax_price_items:
                                    if insert_tax_price_items[idx]:
                                        item_price.tax = insert_tax_price_items[idx]
                                        item_price.save()

                            if insert_price_items:
                                # Reset Default
                                ShopTurboItemsPrice.objects.filter(item=item).order_by(
                                    "created_at"
                                ).update(default=False)

                                item_price.default = True
                                item_price.save()

                                item.price = item_price.price
                                item.save()

                        if insert_purchase_price_items:
                            insert_purchase_price_items = (
                                insert_purchase_price_items.split(
                                    CSV_DELIMITER_LIST_FIELD
                                )
                            )
                            if insert_tax_purchase_price_items:
                                insert_tax_purchase_price_items = (
                                    insert_tax_purchase_price_items.split(
                                        CSV_DELIMITER_LIST_FIELD
                                    )
                                )

                            for idx, insert_price in enumerate(
                                insert_purchase_price_items
                            ):
                                item_price = ItemPurchasePrice.objects.filter(
                                    item=item,
                                    price=insert_price,
                                    currency=item.currency,
                                ).first()
                                if not item_price:
                                    item_price, _ = (
                                        ItemPurchasePrice.objects.get_or_create(
                                            item=item,
                                            price=insert_price,
                                            currency=item.currency,
                                        )
                                    )

                                if insert_tax_purchase_price_items:
                                    if insert_tax_purchase_price_items[idx]:
                                        item_price.tax = (
                                            insert_tax_purchase_price_items[idx]
                                        )
                                        item_price.save()

                            if insert_purchase_price_items:
                                # Reset Default
                                ItemPurchasePrice.objects.filter(item=item).order_by(
                                    "created_at"
                                ).update(default=False)

                                item_price.default = True
                                item_price.save()

                                item.purchase_price = item_price.price
                                item.save()

                elif import_method_items == "create":
                    if "item_id" in create_json_items:
                        create_json_items.pop("item_id")

                    # CHECK LIMIT
                    if not has_quota(workspace, ITEM_USAGE_CATEGORY):
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message="一部のアイテムは上限を超えたため、作成できませんでした。サブスクリプションプランをアップグレードして容量を増やすか、既存のアイテムをアーカイブして容量を空けてください。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message="Some items could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                                type="error",
                            )
                        if module:
                            return redirect(
                                reverse(
                                    "load_object_page",
                                    host="app",
                                    kwargs={
                                        "module_slug": module_slug,
                                        "object_slug": module_object_slug,
                                    },
                                )
                            )
                        return redirect(reverse("main", host="app"))

                    item = ShopTurboItems.objects.create(
                        **create_json_items, status="active", workspace=workspace
                    )
                    add_usage(workspace, ITEM_USAGE_CATEGORY)

                    if insert_price_items:
                        insert_price_items = insert_price_items.split(
                            CSV_DELIMITER_LIST_FIELD
                        )
                        if insert_tax_price_items:
                            insert_tax_price_items = insert_tax_price_items.split(
                                CSV_DELIMITER_LIST_FIELD
                            )
                        for idx, insert_price in enumerate(insert_price_items):
                            item_price = ShopTurboItemsPrice.objects.filter(
                                item=item, price=insert_price, currency=item.currency
                            ).first()
                            if not item_price:
                                item_price, _ = (
                                    ShopTurboItemsPrice.objects.get_or_create(
                                        item=item,
                                        price=insert_price,
                                        currency=item.currency,
                                    )
                                )

                            if insert_tax_price_items:
                                if insert_tax_price_items[idx]:
                                    item_price.tax = insert_tax_price_items[idx]
                                    item_price.save()

                        if insert_price_items:
                            # Reset Default
                            ShopTurboItemsPrice.objects.filter(item=item).order_by(
                                "created_at"
                            ).update(default=False)

                            item_price.default = True
                            item_price.save()

                            item.price = item_price.price

                    if insert_purchase_price_items:
                        insert_purchase_price_items = insert_purchase_price_items.split(
                            CSV_DELIMITER_LIST_FIELD
                        )
                        if insert_tax_purchase_price_items:
                            insert_tax_purchase_price_items = (
                                insert_tax_purchase_price_items.split(
                                    CSV_DELIMITER_LIST_FIELD
                                )
                            )

                        for idx, insert_price in enumerate(insert_purchase_price_items):
                            item_price = ItemPurchasePrice.objects.filter(
                                item=item, price=insert_price, currency=item.currency
                            ).first()
                            if not item_price:
                                item_price, _ = ItemPurchasePrice.objects.get_or_create(
                                    item=item,
                                    price=insert_price,
                                    currency=item.currency,
                                )

                            if insert_tax_purchase_price_items:
                                if insert_tax_purchase_price_items[idx]:
                                    item_price.tax = insert_tax_purchase_price_items[
                                        idx
                                    ]
                                    item_price.save()

                        if insert_purchase_price_items:
                            # Reset Default
                            ItemPurchasePrice.objects.filter(item=item).order_by(
                                "created_at"
                            ).update(default=False)

                            item_price.default = True
                            item_price.save()

                            item.purchase_price = item_price.price
                            item.save()

                    item.save(
                        log_data={
                            "user": request.user,
                            "status": "create",
                            "workspace": workspace,
                        }
                    )  # Log

                    # Clean up any remaining temporary prices after successful item creation
                    ShopTurboItemsPrice.objects.filter(
                        workspace=workspace, item__isnull=True
                    ).delete()
                    ItemPurchasePrice.objects.filter(
                        workspace=workspace, item__isnull=True
                    ).delete()

                elif import_method_items == "update":
                    if items_key_field:
                        item_key_field_json = {}
                        for item_key in items_key_field:
                            if item_key in create_json_items:
                                if create_json_items[item_key]:
                                    item_key_field_json[item_key] = create_json_items[
                                        item_key
                                    ]

                        # if Data is not exist, Skip
                        item = ShopTurboItems.objects.filter(
                            Q(**item_key_field_json, workspace=workspace)
                        ).first()
                        if item:
                            try:
                                item = ShopTurboItems.objects.get(
                                    **item_key_field_json, workspace=workspace
                                )
                            except ShopTurboItems.MultipleObjectsReturned:
                                item = ShopTurboItems.objects.filter(
                                    Q(**item_key_field_json, workspace=workspace)
                                ).first()
                                for item_key_field_sub_json in item_key_field_json:
                                    setattr(
                                        item,
                                        item_key_field_sub_json,
                                        item_key_field_json[item_key_field_sub_json],
                                    )

                            if not item:
                                print("Item not found")
                                return redirect("shopturbo_items")

                            if insert_price_items:
                                insert_price_items = insert_price_items.split(
                                    CSV_DELIMITER_LIST_FIELD
                                )
                                if insert_tax_price_items:
                                    insert_tax_price_items = (
                                        insert_tax_price_items.split(
                                            CSV_DELIMITER_LIST_FIELD
                                        )
                                    )
                                for idx, insert_price in enumerate(insert_price_items):
                                    item_price = ShopTurboItemsPrice.objects.filter(
                                        item=item,
                                        price=insert_price,
                                        currency=item.currency,
                                    ).first()
                                    if not item_price:
                                        item_price, _ = (
                                            ShopTurboItemsPrice.objects.get_or_create(
                                                item=item,
                                                price=insert_price,
                                                currency=item.currency,
                                            )
                                        )

                                    if insert_tax_price_items:
                                        if insert_tax_price_items[idx]:
                                            item_price.tax = insert_tax_price_items[idx]
                                            item_price.save()

                                if insert_price_items:
                                    # Reset Default
                                    ShopTurboItemsPrice.objects.filter(
                                        item=item
                                    ).order_by("created_at").update(default=False)

                                    item_price.default = True
                                    item_price.save()

                                    item.price = item_price.price
                                    item.save()

                            if insert_purchase_price_items:
                                insert_purchase_price_items = (
                                    insert_purchase_price_items.split(
                                        CSV_DELIMITER_LIST_FIELD
                                    )
                                )
                                if insert_tax_purchase_price_items:
                                    insert_tax_purchase_price_items = (
                                        insert_tax_purchase_price_items.split(
                                            CSV_DELIMITER_LIST_FIELD
                                        )
                                    )

                                for idx, insert_price in enumerate(
                                    insert_purchase_price_items
                                ):
                                    item_price = ItemPurchasePrice.objects.filter(
                                        item=item,
                                        price=insert_price,
                                        currency=item.currency,
                                    ).first()
                                    if not item_price:
                                        item_price, _ = (
                                            ItemPurchasePrice.objects.get_or_create(
                                                item=item,
                                                price=insert_price,
                                                currency=item.currency,
                                            )
                                        )

                                    if insert_tax_purchase_price_items:
                                        if insert_tax_purchase_price_items[idx]:
                                            item_price.tax = (
                                                insert_tax_purchase_price_items[idx]
                                            )
                                            item_price.save()

                                if insert_purchase_price_items:
                                    # Reset Default
                                    ItemPurchasePrice.objects.filter(
                                        item=item
                                    ).order_by("created_at").update(default=False)

                                    item_price.default = True
                                    item_price.save()

                                    item.purchase_price = item_price.price
                                    item.save()

                if item:
                    # Write Custom Field
                    for create_json_cs_field in create_json_items_custom_field:
                        item_property = ShopTurboItemsNameCustomField.objects.get(
                            name=create_json_cs_field, workspace=workspace
                        )
                        CustomFieldValue, _ = (
                            ShopTurboItemsValueCustomField.objects.get_or_create(
                                field_name=item_property, items=item
                            )
                        )
                        if (
                            create_json_items_custom_field[create_json_cs_field]
                            != "nan"
                        ):
                            if item_property.type == "components":
                                components_item_ids = [
                                    item.strip()
                                    for item in create_json_items_custom_field[
                                        create_json_cs_field
                                    ].split(CSV_DELIMITER_LIST_FIELD)
                                ]
                                components_quantities = create_json_items_component_qty[
                                    str(item_property.id)
                                ]
                                components_quantities = [
                                    quantity.strip()
                                    for quantity in components_quantities.split(
                                        CSV_DELIMITER_LIST_FIELD
                                    )
                                ]
                                components_item = ShopTurboItems.objects.filter(
                                    workspace=workspace, item_id__in=components_item_ids
                                )

                                if (
                                    components_item_ids
                                    and components_item
                                    and len(components_item_ids)
                                    == len(components_quantities)
                                ):
                                    active_item_components = []
                                    for item_component in components_item:
                                        index = components_item_ids.index(
                                            str(item_component.item_id)
                                        )

                                        item_components, _ = (
                                            ShopTurboItemComponents.objects.get_or_create(
                                                property=CustomFieldValue,
                                                item=item,
                                                item_component=item_component,
                                            )
                                        )
                                        item_components.quantity = int(
                                            components_quantities[index]
                                        )
                                        item_components.save()
                                        active_item_components.append(
                                            str(item_components.id)
                                        )

                                    if active_item_components:
                                        ShopTurboItemComponents.objects.filter(
                                            property=CustomFieldValue, item=item
                                        ).exclude(
                                            id__in=active_item_components
                                        ).delete()
                                else:
                                    ShopTurboItemComponents.objects.filter(
                                        property=CustomFieldValue, item=item
                                    ).delete()
                                    CustomFieldValue.delete()
                            elif item_property.type == "purchase_item":
                                id_item = create_json_items_custom_field[
                                    create_json_cs_field
                                ]
                                try:
                                    pi_id = PurchaseItems.objects.get(
                                        workspace=workspace, id_item=id_item
                                    ).id
                                except PurchaseItems.DoesNotExist:
                                    print("Purchase Item does not exist.")
                                    continue
                                CustomFieldValue.value = pi_id
                                CustomFieldValue.save()
                            else:
                                CustomFieldValue.value = create_json_items_custom_field[
                                    create_json_cs_field
                                ]
                                CustomFieldValue.save()

                    # Populate rest of the key
                    for create_json_items_key in create_json_items:
                        setattr(
                            item,
                            create_json_items_key,
                            create_json_items[create_json_items_key],
                        )
                    setattr(item, "status", "active")
                    item.save(
                        log_data={"user": request.user, "workspace": workspace}
                    )  # Log

        except Exception as e:
            traceback.print_exc()
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Wrong format uploaded, Please use this format|{str(e)}",
                message_ja=f"アップロードされた形式が間違っています。この形式を使用してください|{str(e)}",
                cta_text="Template",
                cta_text_ja="テンプレート",
                cta_target=TEMPLATE_FILE[TYPE_OBJECT_ITEM][lang],
                type="error",
            )

    # Final cleanup of any remaining temporary prices
    ShopTurboItemsPrice.objects.filter(workspace=workspace, item__isnull=True).delete()
    ItemPurchasePrice.objects.filter(workspace=workspace, item__isnull=True).delete()

    if lang == "ja":
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="商品が正常に追加されました。",
            type="success",
        )
    else:
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="Item Sucessfully added.",
            type="success",
        )

    if shopturboitem:
        if source_url:
            source_url = update_query_params_url(
                source_url,
                {
                    "target": target,
                    "id": [str(shopturboitem.id)],
                    "page": page,
                },
            )
            return redirect(source_url)

    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + f"?view_id={view_id}&target={target}&item_id={shopturboitem.id}&page={page}"
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def shopturbo_item_detail(request, id):
    workspace = get_workspace(request.user)
    item = (
        ShopTurboItems.objects.filter(id=id)
        .prefetch_related(
            Prefetch(
                "shopturbo_item_custom_field_relations",
                queryset=ShopTurboItemsValueCustomField.objects.select_related(
                    "field_name"
                ),
            )
        )
        .first()
    )

    if not item:
        raise Http404(
            "No %s matches the given query." % ShopTurboItems._meta.object_name
        )

    view_id = request.GET.get("view_id", None)
    menu_key = request.GET.get("menu_key", None)
    target = TYPE_OBJECT_ITEM
    if view_id:
        view = View.objects.filter(id=view_id).first()
    else:
        # Create Main View
        view, _ = View.objects.get_or_create(
            workspace=workspace, title__isnull=True, target=target
        )
        view_id = view.id

    view_filter, _ = ViewFilter.objects.get_or_create(view=view)

    shopturbo_items_columns = DEFAULT_COLUMNS_ITEM.copy()
    if view_filter and view_filter.column:
        shopturbo_items_columns = ast.literal_eval(view_filter.column)
        for idx, column in enumerate(["checkbox", "item_id"]):
            if column not in shopturbo_items_columns:
                shopturbo_items_columns.insert(idx, column)
        shopturbo_items_columns = [data.lower() for data in shopturbo_items_columns]

    property_sets = PropertySet.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_ITEM
    )

    context = {
        "item": item,
        "shopturbo_items_columns": shopturbo_items_columns,
        "view_id": view_id,
        "page": request.GET.get("page", 1),
        "property_sets": property_sets,
        "menu_key": menu_key,
        "customfields_map_id": {
            str(ctf.id): ctf
            for ctf in ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace
            )
        }
    }
    if view_filter.view_type == 'price_table':
        context["view_type"] = "price_table"
        context["index"] = request.GET.get("index")
        context['shopturbo_items_columns'] = ['checkbox', 'item_id',
                                              'name', 'item_table_price', 'item_table_price_currency', 'item_table_price_tax', 'supplier__company', 'supplier__contact']
        if request.GET.get("price_id") and request.GET.get("price_type"):
            price_id = request.GET.get("price_id")
            price_type = request.GET.get("price_type")
            if price_type == 'sales_price':
                item_price = ShopTurboItemsPrice.objects.filter(id=price_id).first()
            elif price_type == 'purchase_price':
                item_price = ItemPurchasePrice.objects.filter(id=price_id).first()
            elif price_type == 'customer_price':
                item_price = ShopTurboCustomerItemsPrice.objects.filter(
                    id=price_id).first()
            if item_price:
                context['item_price'] = item_price
    return render(request, "data/shopturbo/items-row-partial.html", context)


@login_or_hubspot_required
def shopturbo_column_detail(request):
    workspace = get_workspace(request.user)
    column_type = request.GET.get("column_type")
    column_value = request.GET.get("column_value")

    context = {
        "column_type": column_type,
        "column_value": column_value,
        "workspace": workspace,
    }

    if not column_value:
        return render(
            request,
            "data/shopturbo/items/shopturbo-item-row-column-detail.html",
            context,
        )
    if column_type == "user":
        context["user"] = User.objects.filter(id=column_value).first()
    elif column_type == "contact":
        context["contact"] = Contact.objects.filter(id=column_value).first()
    elif column_type == "company":
        context["company"] = Company.objects.filter(id=column_value).first()
    elif column_type == "invoice_objects":
        context["invoice"] = Invoice.objects.filter(id=column_value).first()

    return render(
        request, "data/shopturbo/items/shopturbo-item-row-column-detail.html", context
    )


@login_or_hubspot_required
def shopturbo_item_group_row(request):
    target = TYPE_OBJECT_ITEM
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    main_parent_group = request.GET.get("main_parent_group", None)

    child_level_type = request.GET.get("child_level_type", "1")

    current_length = int(request.GET.get("current_length", 0)) - 1
    max_length = POSTS_PER_PAGE + current_length

    parent_group_id = request.GET.get("parent_group_id", None)
    if parent_group_id == "others":
        parent_group_id = None

    view_id = request.GET.get("view_id")
    view = View.objects.filter(workspace=workspace, id=view_id).first()
    view_filter = ViewFilter.objects.filter(view=view).first()

    # Filter by Search Keywords
    filter_conditions = Q(workspace=workspace)
    filter_conditions = apply_shopturboitems_view_filter(filter_conditions, view_filter)

    shopturbo_items = (
        ShopTurboItems.objects.filter(filter_conditions)
        .prefetch_related(
            Prefetch(
                "shopturbo_item_custom_field_relations",
                queryset=ShopTurboItemsValueCustomField.objects.select_related(
                    "field_name"
                ),
            )
        )
        .order_by("-item_id")
    )

    cf_id = view_filter.group_customfield
    field_name = ShopTurboItemsNameCustomField.objects.filter(
        id=cf_id, workspace=workspace
    ).first()
    items = []

    if field_name:
        if field_name.type == "components":
            custom_field_values = ShopTurboItemsValueCustomField.objects.filter(
                field_name=field_name,
                items=parent_group_id,
                components__item=parent_group_id,
            )
            for value in custom_field_values:
                for component in value.components.all():
                    if len(items) >= max_length:
                        if not isinstance(items[-1], str):
                            items.append("load_more")
                        continue

                    item = shopturbo_items.filter(
                        id=str(component.item_component.id)
                    ).first()
                    if item:
                        if item not in items:
                            items.append(item)

    if parent_group_id == None and len(items) < max_length:
        available_items = []
        all_parents = []

        if field_name:
            if field_name.type == "components":
                custom_field_values = ShopTurboItemsValueCustomField.objects.filter(
                    field_name=field_name
                )
                for value in custom_field_values:
                    all_parents.append(value.items)
                    for component in value.components.all():
                        available_items.append(component.item_component)

        all_items = available_items + items
        # Add other items that are not in Custom Field
        other_items = shopturbo_items.exclude(
            id__in=[str(item.id) for item in all_items]
        )

        for item in other_items:
            if len(items) >= max_length:
                if not isinstance(items[-1], str):
                    items.append("load_more")
                break
            if item not in items:
                items.append(item)

        removed_items = []
        removed_items.extend(
            item for item in items if any(item == p_ for p_ in all_parents)
        )
        if removed_items:
            items = [item for item in items if item not in removed_items]

    if current_length != -1:
        if len(items) - 1 >= max_length:
            items = items[-(POSTS_PER_PAGE + 1) :]
        else:
            items = items[max_length - POSTS_PER_PAGE :]

    default_property_set = get_default_property_set(target, workspace, lang)

    property_sets = PropertySet.objects.filter(workspace=workspace, target=target)
    default_set = property_sets.filter(name__isnull=True).first()
    property_sets = property_sets.exclude(id=default_set.id).order_by("created_at")

    shopturbo_items_columns = [
        "checkbox",
        "item_id",
        "name",
        "description",
        "price",
        "tax",
        "status",
        "created_at",
    ]
    if view_filter:
        if view_filter.column:
            shopturbo_items_columns = ast.literal_eval(view_filter.column)
            for idx, column in enumerate(["checkbox", "item_id"]):
                shopturbo_items_columns.insert(idx, column)
            shopturbo_items_columns = [data.lower() for data in shopturbo_items_columns]

    if view_filter.view_type == "group":
        shopturbo_items_columns.insert(0, "dropdown")

    context = {
        "items": items,
        "view_id": view_id,
        "page": 1,
        "property_sets": property_sets,
        "default_set": default_set,
        "default_property_set": default_property_set,
        "shopturbo_items_columns": shopturbo_items_columns,
        "parent_group_id": parent_group_id,
        "child_level_type": child_level_type,
        "main_parent_group": main_parent_group,
    }

    return render(request, "data/shopturbo/items/item-group-row.html", context)


@login_or_hubspot_required
def shopturbo_cascade_dropdown_parent(request, id):
    workspace = get_workspace(request.user)
    parent_group = request.GET.get("parent_group")

    view_id = request.GET.get("view_id")

    try:
        view = View.objects.filter(id=view_id).first()
        view_filter = ViewFilter.objects.filter(view=view).first()
    except:
        view = None
        view_filter = None

    exists = False

    try:
        cf_id = view_filter.group_customfield

        field_name = ShopTurboItemsNameCustomField.objects.filter(
            id=cf_id, workspace=workspace
        ).first()
        parent = ShopTurboItems.objects.filter(workspace=workspace, id=id).first()
        if field_name and parent:
            if field_name.type == "components":
                custom_field_values = ShopTurboItemsValueCustomField.objects.filter(
                    field_name=field_name, components__item=parent
                )
                exists = custom_field_values.exists()
    except:
        pass

    context = {
        "view_filter": view_filter,
    }
    if exists:
        context["item"] = parent
        context["parent_group"] = parent_group
    else:
        context["item"] = None
        context["parent_group"] = None

    return render(
        request, "data/shopturbo/items/shopturbo-cascade-dropdown.html", context
    )


@login_or_hubspot_required
def manage_items(request, id):
    target = TYPE_OBJECT_ITEM
    page = 1
    view_id = request.POST.get("view_id", None)
    source_url = request.POST.get("source_url")
    workspace = get_workspace(request.user)
    # Initialize module variable to prevent UnboundLocalError
    module = None

    if source_url:
        source_url = update_query_params_url(source_url, {"target": target, "id": id})

    if request.method == "POST":
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ITEM]
        module = request.POST.get("module")
        lang = request.LANGUAGE_CODE
        workspace = get_workspace(request.user)
        shopturboitem = ShopTurboItems.objects.get(id=id)
        page = request.POST.get("page", 1)
        set_id = request.POST.get("set_id")
        if set_id == "None":
            set_id = None
        if "update-items" in request.POST:
            name = request.POST.get("name", None)
            description = request.POST.get("description", None)
            supplier = request.POST.get("supplier", None)
            owner = request.POST.get('owner', None)

            assign_object_owner(shopturboitem,owner,request,TYPE_OBJECT_ITEM)

            default_item_price_id = request.POST.get("default", None)
            if default_item_price_id == "False":
                default_item_price_id = None

            default_item_purchase_price_id = request.POST.get(
                "default-purchase-price", None
            )
            if default_item_purchase_price_id == "False":
                default_item_purchase_price_id = None

            inventory_checkbox = request.POST.get("inventory_checkbox", None)

            if name:
                shopturboitem.name = name

            if description:
                shopturboitem.description = description
            else:
                shopturboitem.description = None

            if supplier:
                contact = Contact.objects.filter(
                    workspace=workspace, id=supplier
                ).first()
                if contact:
                    shopturboitem.contact = contact
                    shopturboitem.company = None

                else:
                    company = Company.objects.filter(
                        workspace=workspace, id=supplier
                    ).first()
                    if company:
                        shopturboitem.company = company
                        shopturboitem.contact = None

            if set_id:
                shopturboitem.property_set_id = set_id

            currency = None

            # choose default
            price_conditions = Q(item=shopturboitem, default=True)
            shopturboitem_price = (
                ShopTurboItemsPrice.objects.filter(price_conditions)
                .order_by("created_at")
                .update(default=False)
            )
            if default_item_price_id:
                price_conditions = Q(id=default_item_price_id)
                shopturboitem_price = (
                    ShopTurboItemsPrice.objects.filter(price_conditions)
                    .order_by("created_at")
                    .first()
                )
            else:
                shopturboitem_price = (
                    ShopTurboItemsPrice.objects.filter(price_conditions)
                    .order_by("created_at")
                    .first()
                )
                if not shopturboitem_price:
                    shopturboitem_price = (
                        ShopTurboItemsPrice.objects.filter(item=shopturboitem)
                        .order_by("created_at")
                        .first()
                    )

            if shopturboitem_price:
                currency = shopturboitem_price.currency
                shopturboitem_price.default = True
                shopturboitem_price.save(
                    log_data={"user": request.user, "workspace": workspace}
                )
                shopturboitem.price = shopturboitem_price.price

            # Purchase PRICE
            price_conditions = Q(item=shopturboitem, default=True)
            shopturboitem_purchase_price = (
                ItemPurchasePrice.objects.filter(price_conditions)
                .order_by("created_at")
                .update(default=False)
            )
            if default_item_purchase_price_id:
                price_conditions = Q(id=default_item_purchase_price_id)
                shopturboitem_purchase_price = (
                    ItemPurchasePrice.objects.filter(price_conditions)
                    .order_by("created_at")
                    .first()
                )
            else:
                shopturboitem_purchase_price = (
                    ItemPurchasePrice.objects.filter(price_conditions)
                    .order_by("created_at")
                    .first()
                )
                if not shopturboitem_purchase_price:
                    shopturboitem_purchase_price = (
                        ItemPurchasePrice.objects.filter(item=shopturboitem)
                        .order_by("created_at")
                        .first()
                    )

            if shopturboitem_purchase_price:
                shopturboitem_purchase_price.default = True
                shopturboitem_purchase_price.save(
                    log_data={"user": request.user, "workspace": workspace}
                )
                shopturboitem.purchase_price = shopturboitem_purchase_price.price

            print("== currency: ", currency)

            if currency:
                shopturboitem.currency = currency

            save_custom_property(request, shopturboitem)

            # Create Inventory
            if inventory_checkbox:
                inventory_status = request.POST.get("inventory_status", None)
                initial_value = request.POST.get("initial_value", None)

                inventory = None
                if "inventory" in request.POST:
                    if (
                        inventory_status in ["available", "unavailable", "committed"]
                        and initial_value is not None
                    ):
                        inventory_id = request.POST.get("inventory_id", None)
                        if inventory_id:
                            try:
                                inventory = ShopTurboInventory.objects.get(
                                    id=inventory_id
                                )
                            except:
                                if module:
                                    return redirect(
                                        build_redirect_url(
                                            reverse(
                                                "load_object_page",
                                                host="app",
                                                kwargs={
                                                    "module_slug": module,
                                                    "object_slug": module_object_slug,
                                                },
                                            ),
                                            view_id=view_id,
                                            page=page,
                                            item_id=id,
                                            target=target,
                                        )
                                    )
                                return redirect(reverse("main", host="app"))
                            inventory.inventory_status = inventory_status
                        else:
                            if not has_quota(workspace, INVENTORY_USAGE_CATEGORY):
                                msg = "Inventory could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing inventories to free up space."
                                if lang == "ja":
                                    msg = "制限を超えたため、在庫を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存の在庫の一部をアーカイブしてスペースを解放してください。"
                                Notification.objects.create(
                                    workspace=get_workspace(request.user),
                                    user=request.user,
                                    message=msg,
                                    type="error",
                                )
                            else:
                                inventory = ShopTurboInventory.objects.create(
                                    workspace=workspace,
                                    status="active",
                                    inventory_status=inventory_status,
                                )

                                create_inventory_transaction_helper(
                                    inventory, request.user, lang=lang
                                )

                elif "inventory-sync" in request.POST:
                    sync_inventory_id = request.POST.get("sync_inventories", None)
                    try:
                        inventory = ShopTurboInventory.objects.get(id=sync_inventory_id)
                    except:
                        if module:
                            return redirect(
                                build_redirect_url(
                                    reverse(
                                        "load_object_page",
                                        host="app",
                                        kwargs={
                                            "module_slug": module,
                                            "object_slug": module_object_slug,
                                        },
                                    ),
                                    view_id=view_id,
                                    page=page,
                                    item_id=id,
                                    target=target,
                                )
                            )
                        return redirect(reverse("main", host="app"))

                if inventory and initial_value is not None:
                    inventory.item.add(shopturboitem)
                    inventory.initial_value = initial_value

                    inventory.save(
                        log_data={
                            "user": request.user,
                            "target": "commerce_inventory",
                            "workspace": workspace,
                        }
                    )

            shopturboitem.save(log_data={"user": request.user, "workspace": workspace})

            if "source-inventory" in request.POST:
                if source_url:
                    return redirect(source_url)
                if module:
                    return redirect(
                        build_redirect_url(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module,
                                    "object_slug": module_object_slug,
                                },
                            ),
                            view_id=view_id,
                            page=page,
                            item_id=id,
                            target=target,
                        )
                    )
                return redirect(reverse("main", host="app"))

        elif "delete-items" in request.POST:
            shopturboitem.status = "archived"
            shopturboitem.save(log_data={"user": request.user, "workspace": workspace})
            inventory = ShopTurboInventory.objects.filter(
                workspace=workspace, item=shopturboitem
            )
            if inventory:
                inventory.update(status="archived")
                sync_usage(workspace, INVENTORY_USAGE_CATEGORY)

            inventory_transactions = InventoryTransaction.objects.filter(
                workspace=workspace, inventory__in=inventory
            )
            if inventory_transactions:
                inventory_transactions.update(usage_status="archived")
                sync_usage(workspace, INVENTORY_TRANSACTION_USAGE_CATEGORY)

        elif "restore-items" in request.POST:
            if not has_quota(workspace, ITEM_USAGE_CATEGORY):
                if lang == "ja":
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="一部のアイテムは上限を超えたため、作成できませんでした。サブスクリプションプランをアップグレードして容量を増やすか、既存のアイテムをアーカイブして容量を空けてください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="Some items could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                        type="error",
                    )
                if module:
                    return redirect(
                        build_redirect_url(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module,
                                    "object_slug": module_object_slug,
                                },
                            ),
                            view_id=view_id,
                            page=page,
                            item_id=id,
                            target=target,
                        )
                    )
                return redirect(reverse("main", host="app"))

            shopturboitem.status = "active"
            shopturboitem.save(log_data={"user": request.user, "workspace": workspace})

        if "update_inventories" in request.POST:
            source = request.POST.get("source")
            object_id = request.POST.get("object_id")
            if source == TYPE_OBJECT_ITEM:
                item_obj = ShopTurboItems.objects.filter(id=object_id).first()
                inventory_ids = request.POST.getlist("inventories", [])
                associated_inventory = item_obj.shopturboinventory_set.all()
                for inventory in associated_inventory:
                    inventory.item.remove(item_obj)

                if item_obj and inventory_ids != [""]:
                    inventory_to_add = ShopTurboInventory.objects.filter(
                        id__in=inventory_ids
                    )
                    for inventory in inventory_to_add:
                        inventory.item.add(item_obj)

        if "update_orders" in request.POST:
            source = request.POST.get("source")
            object_id = request.POST.get("object_id")
            if source == TYPE_OBJECT_ITEM:
                item_obj = ShopTurboItems.objects.filter(id=object_id).first()
                order_ids = request.POST.getlist("orders", [])
                associated_orders = item_obj.shopturboorders_set.all()
                for order in associated_orders:
                    order.item.remove(item_obj)

                if item_obj and order_ids != [""]:
                    orders_to_add = ShopTurboOrders.objects.filter(id__in=order_ids)
                    for order in orders_to_add:
                        order.item.add(item_obj)
    try:
        if not module or module == "None":
            module_obj = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_ITEM
                )
                .order_by("order", "created_at")
                .first()
            )
            if module_obj:
                module = module_obj.slug
    except:
        pass

    if source_url:
        return redirect(source_url)
    if module:
        return redirect(
            build_redirect_url(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={"module_slug": module, "object_slug": module_object_slug},
                ),
                view_id=view_id,
                page=page,
                item_id=id,
                target=target,
            )
        )

    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def export_items(request: HttpRequest) -> Union[HttpResponse, HttpResponseRedirect]:
    """Export ShopTurbo items to various platforms.

    Args:
        request: The HTTP request

    Returns:
        HTTP response with rendered template or redirect

    Raises:
        Http404: If required objects don't exist
    """
    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse("main", host="app"))
    lang = request.LANGUAGE_CODE or "ja"

    if request.method == "GET":
        # Required
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        input_data = {}
        if node:
            input_data = node.input_data

        platforms = [
            "amazon",
            "square",
            "stripe",
            "ecforce",
            "rakuten",
            "freee",
            "ec-cube",
            "makeshop",
            "yahoo-shopping",
        ]
        channels = Channel.objects.filter(
            workspace=get_workspace(request.user), integration__slug__in=platforms
        ).exclude(status="draft")

        cf_filters = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace, type="choice"
        )

        channels = channels.exclude(name__icontains="Hubspot Power Inventory")

        for name in EXCLUDE_SYNC_CHANNEL_NAME:
            channels = channels.exclude(name__icontains=name)

        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ITEM
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        context = {
            "channels": channels,
            "drawer_type": "shopturbo-view-sync-items",
            "item_ids": "",
            "object_type": TYPE_OBJECT_ITEM,
            "filters": cf_filters,
            "menu_key": module_slug,
            "import_export_type": "export",
            "action_index": action_index,
            "action_slug": action_slug,
            "input_data": input_data,
        }
        return render(request, "data/shopturbo/export-items-action.html", context)

    if request.method == "POST":
        print("[DEBUG] POST", request.POST)

        submit_option = request.POST.get("submit-option")
        action_index = request.POST.get("action_index")
        action_node_id = request.POST.get("action_node_id")
        action_slug = request.POST.get("action_slug")
        at_id = request.POST.get("action_tracker_id")
        wat_id = request.POST.get("workflow_action_tracker_id")

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                print("ActionNode does not exist")
                return HttpResponse(status=404)
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                print("ActionTracker does not exist")
                return HttpResponse(status=404)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                print("WorkflowActionTracker does not exist")
                return HttpResponse(status=404)

        # Text Field
        postfix = ""
        if action_index:
            postfix = "-" + action_index

        try:
            integration_ids = request.POST.getlist(
                "select_integration_ids" + postfix, []
            )

            item_file_columns = request.POST.getlist("item-file-column" + postfix, [])
            item_file_columns_name = request.POST.getlist(
                "item-file-column-name" + postfix, []
            )
            item_sanka_properties = request.POST.getlist(
                "sanka-item-properties" + postfix, []
            )
            item_ignores = request.POST.getlist("item-ignore" + postfix, [])
        except:
            integration_ids = []

            item_file_columns = []
            item_file_columns_name = []
            item_sanka_properties = []
            item_ignores = []

        filter_type = request.POST.get("filter-type" + postfix, None)
        filter_id = request.POST.get("filter" + postfix, None)
        filter_choice = request.POST.get("filter-choice" + postfix, None)

        sync_key = request.POST.get("sync-key" + postfix, None)

        if "save_item_mapping" + postfix in request.POST:
            item_mapping = {}
            mapping_item_custom_fields = {}
            for idx, file_column in enumerate(item_file_columns):
                # check ignore
                item_mapping[file_column] = item_sanka_properties[idx]
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            mapping, _ = ShopTurboItemsMappingFields.objects.get_or_create(
                workspace=workspace, platform="shopify", mapping_type__isnull=True
            )
            data = {}
            for i, field in enumerate(item_mapping):
                if "create_new" in item_mapping[field] and item_ignores[i] != "True":
                    new_field, _ = ShopTurboItemsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=item_file_columns_name[i], type="text"
                    )
                    field_data = {
                        "value": item_file_columns_name[i], "skip": item_ignores[i]}
                    item_sanka_properties[i] = item_file_columns_name[i]
                else:
                    field_data = {"value": item_mapping[field], "skip": item_ignores[i]}
                data[field] = field_data
            ShopTurboItemsMappingFields.objects.filter(id=mapping.id).update(
                input_data=data
            )
            return HttpResponse(status=200)
        if submit_option == "save":
            required_fields = [integration_ids, sync_key]
            valid_to_run = True if all(required_fields) else False

            input_data = {
                "integration_ids": integration_ids,
                "item_file_columns": item_file_columns,
                "item_sanka_properties": item_sanka_properties,
                "item_ignores": item_ignores,
                "filter_type": filter_type,
                "filter_id": filter_id,
                "filter_choice": filter_choice,
                "sync_key": sync_key,
            }

            ActionNode.objects.filter(id=node.id).update(
                valid_to_run=valid_to_run,
                input_data=input_data,
                predefined_input=input_data,
            )

            return HttpResponse(status=200)
        else:
            if submit_option == "run":
                integration_ids = node.input_data.get("integration_ids", [])

                item_file_columns = node.input_data.get("item_file_columns", [])
                item_sanka_properties = node.input_data.get("item_sanka_properties", [])
                item_ignores = node.input_data.get("item_ignores", [])

                filter_type = node.input_data.get("filter_type", None)
                filter_id = node.input_data.get("filter_id", None)
                filter_choice = node.input_data.get("filter_choice", None)

                sync_key = node.input_data.get("sync_key", None)
            else:
                required_fields = [integration_ids, sync_key]
                if not all(required_fields):
                    print("Required fields are missing")
                    return HttpResponse(status=400)

            # Get Channels
            channels = Channel.objects.filter(id__in=integration_ids)

            # Get Items and inventory
            items = ShopTurboItems.objects.filter(workspace=workspace, status="active")
            inventories = ShopTurboInventory.objects.filter(
                item__in=items, workspace=workspace
            )

            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = filter_choice
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()

            if filter_items:
                if filter_type == "exclude":
                    items = items.exclude(id__in=filter_items)
                    inventories = inventories.exclude(item__in=items)
                else:
                    items = items.filter(id__in=filter_items)
                    inventories = inventories.filter(item__in=items)

            for channel in channels:
                # print("=== shopturbo.py -- 377 Push items for channel", channel.id)
                if channel.integration.slug == "shopify":
                    if filter_items:
                        filter_items = ",".join(str(item) for item in filter_items)

                    if lang == "ja":
                        task_name = f"({channel.name}) Shopify 商品のエクスポート "
                    else:
                        task_name = f"Shopify Items Export ({channel.name})"

                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="export_item",
                        name=task_name,
                        channel = channel or None,
                    )

                    param = {
                        "function": "shopify_export_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--workspace={str(workspace.id)}",
                            f"--filter_items={filter_items}",
                            f"--history_id={str(history.id)}",
                            f"--filter_type={filter_type}",
                        ],
                    }

                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            message = "エクスポートジョブが正常に送信されました。"
                        else:
                            message = "Export job submitted successfully."
                        ActionTracker.objects.filter(id=at.id).update(
                            output_data={"status": "run", "message": message}
                        )
                        return_fn = get_redirect_workflow(
                            submit_option, node, workspace, at, wat, status="success"
                        )
                        return redirect(return_fn)
                    else:
                        if lang == "ja":
                            message = "エクスポートに失敗しました。"
                        else:
                            message = "Export failed."
                        ActionTracker.objects.filter(id=at.id).update(
                            output_data={"status": "error", "message": message}
                        )
                        return_fn = get_redirect_workflow(
                            submit_option, node, workspace, at, wat, status="failed"
                        )
                        return redirect(return_fn)
                elif channel.integration.slug == "ecforce":
                    push_ecforce_items(items, channel.id)
                elif channel.integration.slug == "yahoo-shopping":
                    push_yahoo_shopping_items(items, channel.id)

            if lang == "ja":
                message = "エクスポートが正常に送信されました。"
            else:
                message = "Export job submitted successfully."

            at.output_data = {"status": "run", "message": message}
            return_fn = get_redirect_workflow(
                submit_option, node, workspace, at, wat, status="success"
            )
            return redirect(return_fn)

    return HttpResponse(status=200)


@login_or_hubspot_required
def item_pdf_download(request):
    workspace = get_workspace(request.user)
    is_preview = request.GET.get("is_preview", False)
    is_layout = request.GET.get("is_layout", False)
    template_path = request.GET.get("template_path", "")

    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=workspace, app_target=SHOPTURBO_APP_TARGET
    )
    item_pdf_line_item_table = (
        app_setting.item_pdf_line_item_table.split(",")
        if app_setting.item_pdf_line_item_table
        else []
    )
    item_pdf_header_block = (
        app_setting.item_pdf_header_block.split(",")
        if app_setting.item_pdf_header_block
        else []
    )
    item_pdf_payment_block = app_setting.item_pdf_payment_block
    item_pdf_send_from_block = app_setting.item_pdf_send_from_block
    item_pdf_send_to_block = app_setting.item_pdf_send_to_block
    item_pdf_notes_block = app_setting.item_pdf_notes_block
    item_pdf_title = app_setting.item_pdf_title
    item_pdf_font_type = app_setting.item_pdf_font_type
    item_pdf_bg_color = app_setting.item_pdf_color
    item_pdf_line_item_table_display = app_setting.item_pdf_line_item_table_display
    item_pdf_header_block_display = app_setting.item_pdf_header_block_display
    item_pdf_payment_block_display = app_setting.item_pdf_payment_block_display
    item_pdf_send_from_block_display = app_setting.item_pdf_send_from_block_display
    item_pdf_send_to_block_display = app_setting.item_pdf_send_to_block_display
    item_pdf_notes_block_display = app_setting.item_pdf_notes_block_display
    item_pdf_line_item_table_bg_color = app_setting.item_pdf_line_item_table_bg_color
    item_pdf_line_item_table_font_color = (
        app_setting.item_pdf_line_item_table_font_color
    )
    item_pdf_line_item_table_font_size = app_setting.item_pdf_line_item_table_font_size

    if item_pdf_line_item_table_display:
        base_val = item_pdf_line_item_table
        custom_trans = item_pdf_line_item_table_display.split(",")
        item_pdf_line_item_table_display = {}

        for i in range(len(base_val)):
            if len(custom_trans) <= i:
                continue

            if not custom_trans[i]:
                continue

            key = base_val[i]
            if not key:
                continue

            item_pdf_line_item_table_display[key] = custom_trans[i]

    if item_pdf_header_block_display:
        base_val = item_pdf_header_block
        custom_trans = item_pdf_header_block_display.split(",")
        item_pdf_header_block_display = {}

        for i in range(len(base_val)):
            if len(custom_trans) <= i:
                continue

            if not custom_trans[i]:
                continue

            key = base_val[i]
            if not key:
                continue

            item_pdf_header_block_display[key] = custom_trans[i]

    if item_pdf_payment_block_display:
        custom_trans = item_pdf_payment_block_display
        item_pdf_payment_block_display = {}
        item_pdf_payment_block_display[item_pdf_payment_block] = custom_trans

    if item_pdf_send_from_block_display:
        custom_trans = item_pdf_send_from_block_display
        item_pdf_send_from_block_display = {}
        item_pdf_send_from_block_display[item_pdf_send_from_block] = custom_trans

    if item_pdf_send_to_block_display:
        custom_trans = item_pdf_send_to_block_display
        item_pdf_send_to_block_display = {}
        item_pdf_send_to_block_display[item_pdf_send_to_block] = custom_trans

    if item_pdf_notes_block_display:
        custom_trans = item_pdf_notes_block_display
        item_pdf_notes_block_display = {}
        item_pdf_notes_block_display[item_pdf_notes_block] = custom_trans

    context = {
        "workspace": workspace,
        "is_preview": is_preview,
        "preview_layout": is_layout,
        "lang": request.LANGUAGE_CODE,
        "pdf_line_item_table": item_pdf_line_item_table,
        "pdf_header_block": item_pdf_header_block,
        "pdf_payment_block": item_pdf_payment_block,
        "pdf_send_from_block": item_pdf_send_from_block,
        "pdf_send_to_block": item_pdf_send_to_block,
        "pdf_notes_block": item_pdf_notes_block,
        "obj_trans": ITEMS_COLUMNS_DISPLAY,
        "custom_model": "ShopTurboItemsNameCustomField",
        "main_model": "ShopTurboItems",
        "logo_url": app_setting.item_logo_file.url
        if app_setting.item_logo_file
        else "",
        "stamp_url": app_setting.item_stamp_file.url
        if app_setting.item_stamp_file
        else "",
        "pdf_title": item_pdf_title,
        "pdf_font_type": item_pdf_font_type,
        "pdf_bg_color": item_pdf_bg_color,
        "pdf_line_item_table_display": item_pdf_line_item_table_display,
        "pdf_header_block_display": item_pdf_header_block_display,
        "pdf_payment_block_display": item_pdf_payment_block_display,
        "pdf_send_from_block_display": item_pdf_send_from_block_display,
        "pdf_send_to_block_display": item_pdf_send_to_block_display,
        "pdf_notes_block_display": item_pdf_notes_block_display,
        "pdf_line_item_table_bg_color": item_pdf_line_item_table_bg_color,
        "pdf_line_item_table_font_color": item_pdf_line_item_table_font_color,
        "pdf_line_item_table_font_size": item_pdf_line_item_table_font_size,
    }

    # Generate the PDF
    if is_preview:
        setting_type = APP_TARGET_DISPLAY.get(TYPE_OBJECT_ITEM)
        setting_type_display = ""
        if setting_type:
            if request.LANGUAGE_CODE == "ja":
                setting_type_display = setting_type.get("ja", "") or ""
                setting_type_display = f"{setting_type_display} - テンプレート"
            else:
                setting_type_display = setting_type.get("en", "") or ""
                setting_type_display = f"{setting_type_display} - Template"
        match = re.search(r"(\d+)\.html$", template_path)
        pdf_pattern = ""
        if match:
            pdf_pattern = int(match.group(1))  # Convert to integer
        else:
            pass
        context["title"] = f"{setting_type_display} - {pdf_pattern}"
        html_content = render_to_string(template_path, context)
        landscape = False

        if "orderPDF-pattern-5" in template_path:
            landscape = True
        pdf_bytes = asyncio.run(
            generate_pdf_bytes(
                html_content, "", app_setting.item_pdf_footer_template, landscape
            )
        )

        # return HttpResponse(html_content) #TODO: debug only, remove when not needed
        response = HttpResponse(pdf_bytes, content_type="application/pdf")
    else:
        pass

    # Set the response headers
    response["Content-Disposition"] = 'inline; filename="order.pdf"'
    # return HttpResponse(html_content)
    return response

def get_count_of_items_by_filter(request):
    workspace = get_workspace(request.user)
    filter_dictionary = {}
    filter_types = request.POST.getlist("filter_type", None)
    filter_options = request.POST.getlist("filter_options", None)
    filter_values = request.POST.getlist("filter_value", None)
    # Handle "between" operation (have two filter values)
    filter_values2 = request.POST.getlist("filter_value-2", None)
    idx_filter_values2 = 0
    for idx, filter_type in enumerate(filter_types):
        value = filter_values[idx]
        if filter_options[idx] == "between":
            try:
                value = (
                    f"{filter_values[idx]}-{filter_values2[idx_filter_values2]}"
                )
            except:
                value = ""
            idx_filter_values2 += 1

        if (
            str(filter_type) not in filter_dictionary
            and filter_types.count(str(filter_type)) > 1
        ):
            filter_dictionary[str(filter_type)] = {
                "key": [filter_options[idx]],
                "value": [value],
            }
        elif str(filter_type) in filter_dictionary:
            filter_dictionary[str(filter_type)]["key"].append(
                filter_options[idx]
            )
            filter_dictionary[str(filter_type)]["value"].append(value)
        else:
            filter_dictionary[str(filter_type)] = {
                "key": filter_options[idx],
                "value": value,
            }
    filter_conditions = Q(workspace=workspace, status="active")

    view, _ = View.objects.get_or_create(
        workspace=workspace, title__isnull=True, target=TYPE_OBJECT_ITEM
    )

    view_filter, is_new_view_filter = ViewFilter.objects.get_or_create(view=view)
    if is_new_view_filter:
        view_filter.view_type = "list"
        view_filter.save()
    else:
        view_filter.save()
        
    temp_view_filter = view_filter.filter_value
    view_filter.filter_value = filter_dictionary
    view_filter.save()
    view.save()

    filter_conditions = apply_shopturboitems_view_filter(
        filter_conditions, view_filter, request=request)
    print("=== items.py get_count_of_items_by_filter -- 322 Sync item for filter_dictionary", filter_dictionary)

    view_filter.filter_value = temp_view_filter
    view_filter.save()
    view.save()

    items = ShopTurboItems.objects.filter(filter_conditions)
    return HttpResponse(f"({items.count()})")
