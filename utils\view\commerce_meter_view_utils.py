import ast
import re

from django.core.paginator import <PERSON><PERSON><PERSON>, PageNotAnInteger, Paginator
from django.db.models import OuterRef, Q, Subquery
from django_hosts.resolvers import reverse

from data.constants.commerce_meter_constant import (
    COMMERCE_METER_COLUMN_DISPLAY,
    DEFAULT_COLUMNS_COMMERCE_METER,
)
from data.constants.properties_constant import (
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_COMMERCE_METER,
)
from data.models import (
    AdvanceSearchFilter,
    AppSetting,
    CommerceMeter,
    CommerceMeterNameCustomField,
    CommerceMeterValueCustomField,
    Module,
)
from utils.filter import build_view_filter
from utils.utility import is_valid_uuid


def process_view_columns(view_filter):
    """Process and validate columns from view filter."""
    columns = DEFAULT_COLUMNS_COMMERCE_METER[2:].copy()
    if view_filter.column:
        columns = (
            ast.literal_eval(view_filter.column)
            if isinstance(view_filter.column, str)
            else view_filter.column
        )

    for val in ["checkbox", "meter_id"]:
        if val in columns:
            columns.remove(val)
    for idx, column in enumerate(["checkbox", "meter_id"]):
        columns.insert(idx, column)

    return [data.lower() for data in columns]


def build_base_queryset(filter_conditions):
    """Build and return the base queryset with common prefetch/select related."""
    try:
        return CommerceMeter.objects.prefetch_related(
            "contact",
            "company",
            "item",
            "subscription",
        ).filter(filter_conditions)
    except Exception as e:
        print(f"Error building base queryset: {e}")
        return CommerceMeter.objects.none()


def apply_custom_field_ordering(queryset, field_id, order_method):
    """Apply ordering for custom fields."""
    field_name = CommerceMeterNameCustomField.objects.filter(id=field_id).first()
    if not field_name:
        return queryset

    custom_value_subquery = CommerceMeterValueCustomField.objects.filter(
        meter=OuterRef("pk"), field_name=field_name
    ).values("value_time" if field_name.type in ["date", "date_time"] else "value")[:1]

    return queryset.annotate(custom_value=Subquery(custom_value_subquery)).order_by(
        "custom_value" if order_method == "asc" else "-custom_value"
    )


def apply_ordering(queryset, view_filter):
    """Apply ordering to the queryset based on view filter."""
    if not view_filter.sort_order_by:
        return queryset.distinct("id", "meter_id", "created_at").order_by(
            "-meter_id", "-created_at"
        )

    order_method = view_filter.sort_order_method
    order_by = view_filter.sort_order_by

    if order_by == "item":
        order_by = "item__item_id"
    if order_by == "contact":
        order_by = "contact__contact_id"
    if order_by == "company":
        order_by = "company__company_id"
    if order_by == "subscription":
        order_by = "subscription__subscription_id"

    try:
        if is_valid_uuid(order_by):
            return apply_custom_field_ordering(queryset, order_by, order_method)
        else:
            order_prefix = "" if order_method == "asc" else "-"
            return queryset.distinct("id", order_by).order_by(
                f"{order_prefix}{order_by}"
            )
    except:
        return queryset.distinct("id", "meter_id", "created_at").order_by(
            "meter_id", "created_at"
        )


def get_paginated_data(queryset, page_number, items_per_page):
    """Return paginated data and pagination info."""
    try:
        page_number = int(page_number) if page_number else 1
        page_number = max(1, page_number)
    except (TypeError, ValueError):
        page_number = 1

    paginator = Paginator(queryset, items_per_page)
    try:
        page = paginator.page(page_number)
    except (PageNotAnInteger, EmptyPage):
        page = paginator.page(1)

    start = (page.number - 1) * items_per_page
    end = start + len(page.object_list)

    return {
        "objects": page.object_list,
        "paginator": paginator,
        "page_number": page.number,
        "start": start + 1,  # 1-based index for display
        "end": end,
        "total": paginator.count,
        "has_other_pages": page.has_other_pages(),
    }


def apply_advance_search_filters(advance_search, view_filter):
    """Apply advance search filters to the view filter."""
    if not advance_search or not advance_search.search_filter:
        return

    search_filter = advance_search.search_filter
    search_filter_status = advance_search.search_filter_status or {}

    # Filter out empty values and inactive filters
    filtered_search = {
        k: v
        for k, v in search_filter.items()
        if v.get("value") != "" and search_filter_status.get(k) != "False"
    }

    # Handle special case for usage_status
    if filtered_search.get("usage_status", {}).get("value") == "all":
        filtered_search.pop("usage_status", None)

    view_filter.filter_value = filtered_search
    view_filter.save()


def handle_search_and_filtering(
    request, workspace, view_filter, filter_conditions, search_q
):
    """Handle search and advanced filtering logic."""
    # Get advance search if exists
    advance_search = AdvanceSearchFilter.objects.filter(
        workspace=workspace, object_type=TYPE_OBJECT_COMMERCE_METER, type="default"
    ).first()

    if advance_search and advance_search.search_settings:
        # Update app settings with search settings if they exist
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target="shopturbo"
        ).first()
        if app_setting:
            app_setting.search_setting_inventory = advance_search.search_settings
            app_setting.save()

    # Handle search query
    if search_q:
        match_special_char = re.search(r"#?(\d+)", search_q)
        if match_special_char:
            number = match_special_char.group(1)
            if number:
                filter_conditions &= Q(meter_id=number)
        else:
            search_q = search_q.lower()
            filter_conditions &= (
                Q(item__name__icontains=search_q)
                | Q(contact__name__icontains=search_q)
                | Q(company__name__icontains=search_q)
            )

    # Apply advance search filters if active
    if advance_search and advance_search.is_active:
        apply_advance_search_filters(advance_search, view_filter)

    # Apply view filters
    return build_view_filter(
        filter_conditions, view_filter, TYPE_OBJECT_COMMERCE_METER, request=request
    )


def get_column_labels(columns, workspace=None):
    """Return a dictionary of column labels for the given columns."""
    if not columns:
        return {}
    column_labels = {}
    for column in columns:
        if is_valid_uuid(column):
            custom_field = CommerceMeterNameCustomField.objects.filter(
                id=column, workspace=workspace
            ).first()
            column_labels[column] = custom_field.name or column
        else:
            column_labels[column] = COMMERCE_METER_COLUMN_DISPLAY.get(column, column)
    return column_labels


def get_redirect_link(workspace, module_slug):
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_COMMERCE_METER]
    redirect_url = reverse("main", host="app")
    if not module_slug:
        module = (
            Module.objects.filter(
                workspace=workspace,
                object_values__contains=TYPE_OBJECT_COMMERCE_METER,
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = module.slug
        redirect_url = reverse(
            "load_object_page",
            host="app",
            kwargs={
                "module_slug": module_slug,
                "object_slug": module_object_slug,
            },
        )
    return redirect_url
