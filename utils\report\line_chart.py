import random
import re
import traceback
import uuid
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from typing import List
from types import SimpleNamespace
from zoneinfo import ZoneInfo

from dateutil.relativedelta import relativedelta
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.paginator import Paginator
from django.db import connection
from django.db.models import (Avg, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Count, Date<PERSON>ield,
                              DateTimeField, ExpressionWrapper, F, FloatField,
                              Func, IntegerField, Max, Min, OuterRef, Q,
                              Subquery, Sum, Value, When)
from django.db.models.functions import (Cast, Coalesce, Concat, TruncDate,
                                        TruncHour, TruncMonth, TruncWeek)
from django.utils import timezone

from data.constants.constant import (CHART_CHANNEL_STAT, CHART_POST_STAT,
                                     PANEL_METRIC_TITLE)
from data.constants.date_constant import *
from data.constants.properties_constant import *
from data.models import *
from utils.currency_symbols._constants import *
from utils.currency_symbols.currency_symbols import CurrencySymbols
from utils.filter import build_filters, build_raw_filters, q_has_field
from utils.forecast import Forecast
from utils.formula import calculate_math
from utils.properties.properties import (ALIAS_TABLE, get_list_view_columns,
                                         get_object_display_based_columns,
                                         get_page_object)
from utils.utility import is_valid_uuid, to_snake_case
from utils.reports import get_line_chart_params, get_time_range, new_result, new_result_invoice_growth, new_result_detailed_growth

def fetch_line_chart_data(panel: ReportPanel, start_time=None, stop_time=None, lang=None, group_by=None, is_realtime=None):
    workspace = panel.workspace
    label = ''
    chart_config = {
        'fill': True,
        'radius': 3
    }
    net_result = {}

    if is_realtime is not None:
        panel.start_time = start_time
        panel.end_time = stop_time
        group_by = group_by if group_by else 'day'
        panel.group_by = group_by
        is_realtime = is_realtime if is_realtime is not None else True
        panel.is_realtime = is_realtime
        panel.save()
    else:
        start_time = panel.start_time if panel.start_time else start_time
        stop_time = panel.end_time if panel.end_time else stop_time
        group_by = panel.group_by if panel.group_by else 'day'
        is_realtime = panel.is_realtime if panel.is_realtime is not None else True

    trunc_func, time_format, time_step, start_time = get_line_chart_params(
        group_by, start_time)
    data_points = get_time_range(start_time, stop_time, time_format, time_step, is_start_monday=True)
    if is_realtime:  # Update stop_time by current date
        stop_time = datetime.fromtimestamp(
            (datetime.today() - timezone.timedelta(days=0)).timestamp(), tz=timezone.utc)
        start_time = stop_time - time_step*(len(data_points)-1)
        panel.end_time = stop_time
        panel.start_time = start_time
        panel.group_by = group_by
        panel.is_realtime = is_realtime
        panel.save()
        trunc_func, time_format, time_step, start_time = get_line_chart_params(
            group_by, start_time)
        data_points = get_time_range(
            start_time, stop_time, time_format, time_step, is_start_monday=True)

    start_time_str = start_time.strftime('%Y-%m-%dT%H:%M:%S')
    stop_time_str = stop_time.strftime('%Y-%m-%dT%H:%M:%S')

    results = []
    x_axis = []
    for point in data_points:
        x_axis.append(point['display'])

    rule_filter = Q()
    if panel.filter and panel.filter['filters']:
        rule_filter = build_filters(panel.filter['filters'])
        print(f'=== report.py - 770: Filter for {panel.name}: {rule_filter}')

    metrics = panel.list_metric_names_and_metric_types()
    print('........... metrics', metrics)
    for metric in metrics:
        metric_name = metric['name']
        metric_type = metric['type']
        metric_filter = PanelMetric.objects.filter(
            metric__contains=metric_name, reportpanel=panel).values_list('filter', flat=True).first()
        print(metric_filter)
        print(metric_name)

        try:
            if metric_name == 'number_orders':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                condition = Q(
                    workspace=workspace, order_at__gte=start_time_str, order_at__lte=stop_time_str)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 770: Filter for {metric_name}: {condition}')

                breakdown_conditions = [
                    'contact', 'company'] if panel.breakdown == 'customers' else [None]
                for breakdown in breakdown_conditions:
                    breakdown_label = f"{label} - {breakdown.capitalize()}" if breakdown else label

                    if breakdown == 'contact':
                        spesific_condition = condition & Q(
                            contact__isnull=False)
                    elif breakdown == 'company':
                        spesific_condition = condition & Q(
                            company__isnull=False)
                    else:
                        spesific_condition = condition

                    queryset = (
                        ShopTurboOrders.objects
                        .filter(spesific_condition)
                        .annotate(date=trunc_func('order_at'))
                        .values('date')
                        .annotate(count=Coalesce(Count('id'), 0))
                        .order_by('date')
                    )
                    print(
                        f'=== report.py - 848: Query result for {metric_name} - {queryset}')

                    data = {item['display']: 0 for item in data_points}
                    for item in queryset:
                        data[item['date'].strftime(
                            time_format)] = item['count']

                    # Metric type[1][0] is for shows the cumulative, using it to avoid using hard coded
                    if metric_type == METRIC_TYPE[1][0]:
                        all_filter = rule_filter
                        if metric_filter:
                            all_filter &= filter_condition

                        cumulative_count = ShopTurboOrders.objects.filter(
                            Q(order_at__lt=start_time_str) & all_filter & Q(workspace=workspace)).count()
                        for idx, _ in data.items():
                            data[idx] += cumulative_count
                            cumulative_count = data[idx]

                    results.append(new_result(breakdown_label, [
                                   value for value in data.values()],  len(results)))

            elif metric_name == 'number_subscriptions':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1064: Filter for {metric_name}: {condition}')

                if time_step == timedelta(days=1):
                    time_step_unit = 'day'
                elif time_step == timedelta(weeks=1):
                    time_step_unit = 'week'
                elif time_step == timedelta(days=30) or time_step == timedelta(days=31):
                    time_step_unit = 'month'
                    time_step = '1 month'
                elif time_step == timedelta(hours=1):
                    time_step_unit = 'hour'
                    a
                # Metric type[1][0] is for shows the cumulative, using it to avoid using hard coded
                if metric_type == METRIC_TYPE[1][0]:
                    where_clause, where_params = build_raw_filters(
                        ShopTurboSubscriptions, condition)

                    raw_query = f"""
                        SELECT DISTINCT
                            dates.date,
                            COUNT(data_shopturbosubscriptions.id) AS count
                        FROM (
                            SELECT generate_series(
                                date_trunc('{time_step_unit}', '{start_time_str}'::timestamp), 
                                date_trunc('{time_step_unit}', '{stop_time_str}'::timestamp), 
                                '{time_step}'::interval
                            ) AS date
                        ) AS dates
                        LEFT JOIN data_shopturbosubscriptions ON (
                            data_shopturbosubscriptions.status = 'active'
                            AND data_shopturbosubscriptions.workspace_id = '{workspace.id}'
                            AND (data_shopturbosubscriptions.start_date <= dates.date)
                            AND (data_shopturbosubscriptions.end_date >= dates.date OR data_shopturbosubscriptions.end_date IS NULL)
                            {'AND ' + where_clause if where_clause is not None else ''}
                        )
                        GROUP BY dates.date
                        ORDER BY dates.date ASC;
                    """

                    data = {item['display']: 0 for item in data_points}
                    with connection.cursor() as cursor:
                        cursor.execute(raw_query, list(where_params))
                        raw_results = cursor.fetchall()
                        print(
                            f'=== report.py - 883: Query result for {metric_name} - {raw_results}')

                        for raw_item in raw_results:
                            data[raw_item[0].strftime(
                                time_format)] = int(raw_item[1])
                else:
                    queryset = (
                        ShopTurboSubscriptions.objects
                        .filter(condition)
                        .annotate(date=trunc_func('start_date'))
                        .values('date')
                        .annotate(count=Coalesce(Count('id'), 0))
                        .order_by('date')
                    )
                    print(
                        f'=== report.py - 848: Query result for {metric_name} - {queryset}')

                    data = {item['display']: 0 for item in data_points}
                    for item in queryset:
                        if item['date'] is None:
                            continue
                        
                        data[item['date'].strftime(
                            time_format)] = item['count']

                results.append(new_result(
                    label, [value for value in data.values()],  len(results)))

            elif metric_name == 'recurring_revenue':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = ShopTurboSubscriptions.objects.filter(
                    workspace=workspace, status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    ShopTurboSubscriptions, condition)
                breakdown_values = ['no_breakdown']
                if panel.breakdown:
                    breakdown_field = panel.breakdown

                    if 'contact_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            ContactsValueCustomField.objects.filter(
                                field_name_id=id,
                                contact__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    elif 'company_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name_id=id,
                                company__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    else:
                        breakdown_values = (
                            ShopTurboSubscriptions
                            .objects
                            .filter(workspace=workspace, status='active')
                            .values_list(breakdown_field, flat=True)
                            .distinct()
                        )

                if time_step == timedelta(days=1):
                    time_step_unit = 'day'
                elif time_step == timedelta(weeks=1):
                    time_step_unit = 'week'
                elif time_step == timedelta(days=30) or time_step == timedelta(days=31):
                    time_step_unit = 'month'
                    time_step = '1 month'
                elif time_step == timedelta(hours=1):
                    time_step_unit = 'hour'
                    
                for breakdown in breakdown_values:
                    where_parameters = where_params
                    breakdown_field = None
                    join_sql = None
                    parent_table = None
                    field_name_id = ""
                    if breakdown != 'no_breakdown':
                        breakdown_fields = panel.breakdown.split("__")
                        parent = "subscription"
                        if len(breakdown_fields) > 1:
                            if 'custom_field_relations' in breakdown_fields[0]:
                                parent = breakdown_fields[0]
                                field_name_id = breakdown_fields[2].replace(
                                    "|", "")
                                breakdown_field = "value"
                            else:
                                parent = breakdown_fields[0]
                                breakdown_field = breakdown_fields[1]
                        else:
                            breakdown_field = panel.breakdown
                        join_clauses = {
                            'subscription': (
                                "",
                                (breakdown,),
                                's'
                            ),
                            'contact': (
                                f"""
                                LEFT JOIN data_contact AS c
                                    ON c.id   = s.contact_id
                                """,
                                (breakdown,),
                                'c'
                            ),
                            'company': (
                                f"""
                                LEFT JOIN data_company AS co
                                    ON co.id   = s.company_id
                                """,
                                (breakdown,),
                                'co'
                            ),
                            'contact_custom_field_relations': (
                                f"""
                                LEFT JOIN data_contactsvaluecustomfield AS cf
                                    ON cf.contact_id     = s.contact_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'company_custom_field_relations': (
                                f"""
                                LEFT JOIN data_companyvaluecustomfield AS cf
                                    ON cf.company_id     = s.company_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            # add more entries here as you introduce new breakdown types…
                        }

                        join_sql, join_params, parent_table = join_clauses[parent]
                        where_parameters += join_params

                    raw_query = f"""
                        SELECT DISTINCT
                            dates.date,
                            COALESCE(SUM(fs.total_price), 0) as total_price
                        FROM (
                            SELECT generate_series(
                                date_trunc('{time_step_unit}', '{start_time_str}'::timestamp), 
                                date_trunc('{time_step_unit}', '{stop_time_str}'::timestamp), 
                                '{time_step}'::interval
                            ) AS date
                        ) AS dates
                        LEFT JOIN (
                            SELECT s.*
                            FROM data_shopturbosubscriptions AS s
                            {join_sql if join_sql else ""}  
                            WHERE 
                            s.workspace_id = '{workspace.id}'
                            {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                        ) AS fs
                            ON (
                                fs.status = 'active'
                                AND fs.workspace_id = '{workspace.id}'
                                AND (fs.start_date <= dates.date)
                                AND (fs.end_date >= dates.date OR fs.end_date IS NULL)
                                {'AND ' + where_clause if where_clause is not None else ''}
                            )
                        GROUP BY dates.date
                        ORDER BY dates.date ASC;
                    """

                    data = {item['display']: 0 for item in data_points}
                    with connection.cursor() as cursor:
                        cursor.execute(raw_query, list(where_parameters))
                        raw_results = cursor.fetchall()
                        print(
                            f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                        for raw_item in raw_results:
                            data[raw_item[0].strftime(
                                time_format)] = int(raw_item[1])

                    results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else label, [
                                   value for value in data.values()],  len(results)))

            elif metric_name == 'recurring_revenue_annually':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = ShopTurboSubscriptions.objects.filter(
                    workspace=workspace, status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    ShopTurboSubscriptions, condition)
                breakdown_values = ['no_breakdown']
                if panel.breakdown:
                    breakdown_field = panel.breakdown

                    if 'contact_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            ContactsValueCustomField.objects.filter(
                                field_name_id=id,
                                contact__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    elif 'company_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name_id=id,
                                company__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    else:
                        breakdown_values = (
                            ShopTurboSubscriptions
                            .objects
                            .filter(workspace=workspace, status='active')
                            .values_list(breakdown_field, flat=True)
                            .distinct()
                        )

                for breakdown in breakdown_values:
                    where_parameters = where_params
                    breakdown_field = None
                    join_sql = None
                    parent_table = None
                    if breakdown != 'no_breakdown':
                        breakdown_fields = panel.breakdown.split("__")
                        parent = "subscription"
                        if len(breakdown_fields) > 1:
                            if 'custom_field_relations' in breakdown_fields[0]:
                                parent = breakdown_fields[0]
                                field_name_id = breakdown_fields[2].replace(
                                    "|", "")
                                breakdown_field = "value"
                            else:
                                parent = breakdown_fields[0]
                                breakdown_field = breakdown_fields[1]
                        else:
                            breakdown_field = panel.breakdown
                        join_clauses = {
                            'subscription': (
                                "",
                                (breakdown,),
                                's'
                            ),
                            'contact': (
                                f"""
                                LEFT JOIN data_contact AS c
                                    ON c.id   = s.contact_id
                                """,
                                (breakdown,),
                                'c'
                            ),
                            'company': (
                                f"""
                                LEFT JOIN data_company AS co
                                    ON co.id   = s.company_id
                                """,
                                (breakdown,),
                                'co'
                            ),
                            'contact_custom_field_relations': (
                                f"""
                                LEFT JOIN data_contactsvaluecustomfield AS cf
                                    ON cf.contact_id     = s.contact_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'company_custom_field_relations': (
                                f"""
                                LEFT JOIN data_companyvaluecustomfield AS cf
                                    ON cf.company_id     = s.company_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            # add more entries here as you introduce new breakdown types…
                        }

                        join_sql, join_params, parent_table = join_clauses[parent]
                        where_parameters += join_params

                    raw_query = f"""
                        SELECT DISTINCT
                            dates.date,
                            COALESCE(SUM(fs.total_price * 12), 0) as total_price
                        FROM (
                            SELECT generate_series(
                                '{start_time_str}'::timestamp, '{stop_time_str}'::timestamp, '{time_step}'::interval
                            ) AS date
                        ) AS dates
                        LEFT JOIN (
                            SELECT s.*
                            FROM data_shopturbosubscriptions AS s
                            {join_sql if join_sql else ""}  
                            WHERE 
                            s.workspace_id = '{workspace.id}'
                            {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                        ) AS fs
                            ON (
                                fs.status = 'active'
                                AND fs.workspace_id = '{workspace.id}'
                                AND (fs.start_date <= dates.date)
                                AND (fs.end_date >= dates.date OR fs.end_date IS NULL)
                                {'AND ' + where_clause if where_clause is not None else ''}
                            )
                        GROUP BY dates.date
                        ORDER BY dates.date ASC;
                    """

                    data = {item['display']: 0 for item in data_points}
                    with connection.cursor() as cursor:
                        cursor.execute(raw_query, list(where_parameters))
                        raw_results = cursor.fetchall()
                        print(
                            f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                        for raw_item in raw_results:
                            data[raw_item[0].strftime(
                                time_format)] = int(raw_item[1])

                    results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else panel, [
                                   value for value in data.values()],  len(results)))

            elif metric_name == 'recurring_revenue_invoice' and panel.breakdown in ['subscription_growth', 'subscription_revenue']:
                start_time_str = start_time.strftime('%Y-%m-%d')
                stop_time_str = stop_time.strftime('%Y-%m-%d')

                condition = Q(
                    workspace=workspace, start_date__gte=start_time_str, start_date__lte=stop_time_str)
                condition &= ~Q(usage_status='archived')

                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(
                        metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 770: Filter for {metric_name}: {condition}')

                queryset = (
                    Invoice.objects.filter(condition)
                    .annotate(**{
                        'contact_': F('contact'),
                        'company_': F('company'),
                        'date_period': trunc_func('start_date'),
                    })
                    .values(*['contact_', 'company_', 'date_period'])
                    .annotate(
                        count=Sum(
                            Case(
                                When(tax_rate__isnull=True,
                                     then=F('total_price')),
                                default=F('total_price') *
                                (1 - F('tax_rate') / 100),
                                output_field=FloatField()
                            )
                        )
                    )
                )

                # 1) Bucket your queryset rows by formatted date
                map_invoice_by_date_and_customer = defaultdict(list)
                for row in queryset:
                    # pick company if set, else contact, else "other"
                    customer = row.get('company_') or row.get(
                        'contact_') or "other"
                    # format the date period
                    customer = str(customer)
                    x_key = row['date_period'].strftime(time_format) if row['date_period'] else None
                    if x_key is None:
                        continue  # Skip rows with no date
                    # count is already your summed invoice total for that row
                    total_invoice = row.get('count') or 0

                    # append to a list (so you don't overwrite previous entries)
                    map_invoice_by_date_and_customer[x_key].append({
                        'customer': customer,
                        'invoice_total_price': total_invoice,
                    })

                # Walk through calculation --- Subscription Growth ---
                if panel.breakdown == 'subscription_growth':
                    # 2) Prepare your output containers
                    matrix = {
                        'new':        defaultdict(int),
                        'expansion':  defaultdict(int),
                        'contraction': defaultdict(int),
                        'churned':    defaultdict(int),
                    }
                    net_matrix = defaultdict(int)
                    # will hold last-period's total per customer
                    group_customer = {}

                    # 3) Walk through each period in your dynamic x_axis
                    for xx in x_axis:
                        # make sure every category has a zero baseline for this date
                        for cat in matrix:
                            matrix[cat].setdefault(xx, 0)

                        # the list of customer-totals for this date (maybe empty)
                        todays = map_invoice_by_date_and_customer.get(
                            xx, [])
                        # turn into a quick lookup: customer -> this period's total
                        curr_totals = {}
                        for d in todays:
                            if d['customer'] not in curr_totals:
                                curr_totals[d['customer']] = 0

                            curr_totals[d['customer']
                                        ] += d['invoice_total_price']

                        # consider everyone we've ever seen plus any brand-new ones today
                        all_customers = set(
                            group_customer) | set(curr_totals)

                        for customer in all_customers:
                            prev = group_customer.get(customer, 0)
                            curr = curr_totals.get(customer, 0)

                            if prev == 0 and curr > 0:
                                # no invoice last period -> New
                                matrix['new'][xx] += curr

                            elif prev > 0 and curr == 0:
                                # had invoice, now none -> Churned
                                prev *= -1
                                matrix['churned'][xx] += prev

                            elif curr > prev:
                                # invoice increased -> Expansion
                                matrix['expansion'][xx] += (curr - prev)

                            elif curr < prev:
                                # invoice decreased -> Contraction
                                matrix['contraction'][xx] += (
                                    (prev - curr) * -1)

                            # update for next iteration
                            group_customer[customer] = curr
                        net_matrix[xx] = matrix['new'][xx] + matrix['churned'][xx] + \
                            matrix['expansion'][xx] + \
                            matrix['contraction'][xx]
                    LABEL_MAP = {
                        'new': {
                            'en': 'New',
                            'ja': '新規',
                        },
                        'expansion': {
                            'en': 'Expansion',
                            'ja': '拡大',
                        },
                        'contraction': {
                            'en': 'Contraction',
                            'ja': '縮小',
                        },
                        'churned': {
                            'en': 'Churned',
                            'ja': '解約',
                        },
                    }

                    for bx in ['new', 'expansion', 'contraction', 'churned']:
                        result_dict = matrix[bx]
                        result_list = [result_dict[xx] for xx in x_axis]
                        trans = LABEL_MAP.get(bx, {})
                        title = trans.get(
                            'ja', bx) if lang == 'ja' else trans.get('en', bx)
                        idx_color = len(results)
                        if bx == 'new':
                            idx_color = 3
                        elif bx == 'expansion':
                            idx_color = 1
                        elif bx == 'contraction':
                            idx_color = 5
                        elif bx == 'churned':
                            idx_color = 0

                        results.append(new_result_detailed_growth(
                            title, result_list, idx_color))

                    net_matrix_list = [net_matrix[xx] for xx in x_axis]
                    net_result = new_result(
                        "ネット" if lang == 'ja' else "Net", net_matrix_list, 0)
                # Walk through calculation --- Subscription Revenue ---
                elif panel.breakdown == 'subscription_revenue':
                    # 2) Prepare your output containers
                    matrix = {
                        'new':      defaultdict(int),
                        'retain':   defaultdict(int),
                    }
                    net_matrix = defaultdict(int)

                    group_customer = {}

                    # 3) Walk through each period in your dynamic x_axis
                    for xx in x_axis:
                        # make sure every category has a zero baseline for this date
                        for cat in matrix:
                            matrix[cat].setdefault(xx, 0)

                        # the list of customer-totals for this date (maybe empty)
                        todays = map_invoice_by_date_and_customer.get(
                            xx, [])
                        # turn into a quick lookup: customer -> this period's total
                        curr_totals = {}
                        for d in todays:
                            if d['customer'] not in curr_totals:
                                curr_totals[d['customer']] = 0

                            curr_totals[d['customer']
                                        ] += d['invoice_total_price']

                        # consider everyone we've ever seen plus any brand-new ones today
                        all_customers = set(
                            group_customer) | set(curr_totals)

                        for customer in all_customers:
                            prev = group_customer.get(customer, 0)
                            curr = curr_totals.get(customer, 0)

                            if prev == 0 and curr > 0:
                                # no invoice last period -> New
                                matrix['new'][xx] += curr

                            else:
                                # invoice increased/decrease/similar -> Expansion/Churned/NoChanges
                                matrix['retain'][xx] += curr

                            # update for next iteration
                            group_customer[customer] = curr

                        net_matrix[xx] = matrix['new'][xx] + \
                            matrix['retain'][xx]
                    LABEL_MAP = {
                        'new': {
                            'en': 'New',
                            'ja': '新規',
                        },
                        'retain': {
                            'en': 'Retain',
                            'ja': '継続',
                        },
                    }

                    for bx in ['retain', 'new']:
                        result_dict = matrix[bx]
                        result_list = [result_dict[xx] for xx in x_axis]
                        trans = LABEL_MAP.get(bx, {})
                        title = trans.get(
                            'ja', bx) if lang == 'ja' else trans.get('en', bx)
                        idx_color = len(results)
                        if bx == 'new':
                            idx_color = 3
                        elif bx == 'retain':
                            idx_color = 1

                        results.append(new_result_invoice_growth(
                            title, result_list, idx_color))

                    net_matrix_list = [net_matrix[xx] for xx in x_axis]
                    net_result = new_result(
                        "トータル" if lang == 'ja' else "Total", net_matrix_list, 0)

            elif metric_name == 'recurring_revenue_invoice':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = Invoice.objects.filter(
                    workspace=workspace, usage_status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    Invoice, condition)

                breakdown_values = ['no_breakdown']
                value_map = {}
                if panel.breakdown:
                    panel.breakdown = panel.breakdown.replace("invoice__", "")
                    breakdown_field = panel.breakdown

                    if 'contact_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            ContactsValueCustomField.objects.filter(
                                field_name_id=id,
                                contact__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                        custom_field = ContactsNameCustomField.objects.get(
                            id=id)
                        if custom_field.type == 'choice':
                            labels = ast.literal_eval(
                                custom_field.choice_value)
                            for l_ in labels:
                                value_map[l_['value']] = l_['label']
                    elif 'company_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name_id=id,
                                company__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                        custom_field = CompanyNameCustomField.objects.get(
                            id=id)
                        if custom_field.type == 'choice':
                            labels = ast.literal_eval(
                                custom_field.choice_value)
                            for l_ in labels:
                                value_map[l_['value']] = l_['label']
                    elif breakdown_field == 'line_item':
                        breakdown_values = (
                            InvoiceItem
                            .objects
                            .filter(invoice__workspace=workspace, invoice__usage_status='active')
                            .values_list('item_link__name', flat=True)
                            .distinct()
                        )
                    else:
                        breakdown_values = (
                            Invoice
                            .objects
                            .filter(workspace=workspace, usage_status='active')
                            .values_list(breakdown_field, flat=True)
                            .distinct()
                        )

                if time_step == timedelta(days=1):
                    time_step_unit = 'day'
                elif time_step == timedelta(weeks=1):
                    time_step_unit = 'week'
                elif time_step == timedelta(days=30) or time_step == timedelta(days=31):
                    time_step_unit = 'month'
                    time_step = '1 month'
                elif time_step == timedelta(hours=1):
                    time_step_unit = 'hour'

                for breakdown in breakdown_values:
                    where_parameters = where_params
                    breakdown_field = None
                    join_sql = None
                    parent_table = None
                    field_name_id = None
                    if breakdown != 'no_breakdown':
                        breakdown_fields = panel.breakdown.split("__")
                        parent = "invoice"
                        if len(breakdown_fields) > 1:
                            if 'custom_field_relations' in breakdown_fields[0]:
                                parent = breakdown_fields[0]
                                field_name_id = breakdown_fields[2].replace(
                                    "|", "")
                                breakdown_field = "value"
                            else:
                                parent = breakdown_fields[0]
                                breakdown_field = breakdown_fields[1]
                        else:
                            breakdown_field = panel.breakdown
                        join_clauses = {
                            'invoice': (
                                "",
                                (breakdown,),
                                'i'
                            ),
                            'contact': (
                                f"""
                                LEFT JOIN data_contact AS c
                                    ON c.id   = i.contact_id
                                """,
                                (breakdown,),
                                'c'
                            ),
                            'company': (
                                f"""
                                LEFT JOIN data_company AS co
                                    ON co.id   = i.company_id
                                """,
                                (breakdown,),
                                'co'
                            ),
                            'contact_custom_field_relations': (
                                f"""
                                LEFT JOIN data_contactsvaluecustomfield AS cf
                                    ON cf.contact_id     = i.contact_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'company_custom_field_relations': (
                                f"""
                                LEFT JOIN data_companyvaluecustomfield AS cf
                                    ON cf.company_id     = i.company_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'line_item': (
                                "",
                                (),
                                ''
                            )
                            # add more entries here as you introduce new breakdown types…
                        }

                        join_sql, join_params, parent_table = join_clauses[parent]
                        where_parameters += join_params

                    if panel.breakdown == 'line_item':
                        raw_query = f"""
                            SELECT DISTINCT
                                dates.date,
                                COALESCE(SUM(invoiceitem.total_price), 0) as total_price
                            FROM (
                                SELECT generate_series(
                                    '{start_time_str}'::timestamp, 
                                    '{stop_time_str}'::timestamp, 
                                    '{time_step}'::interval
                                ) AS date
                            ) AS dates
                            LEFT JOIN (
                                SELECT 
                                    ii.total_price,
                                    date_trunc('{time_step_unit}', i.start_date) AS invoice_date
                                FROM data_invoiceitem ii
                                LEFT JOIN data_invoice i ON i.id = ii.invoice_id
                                LEFT JOIN data_shopturboitems si ON si.id = ii.item_link_id
                                {join_sql if join_sql else ""}
                                WHERE i.usage_status = 'active'
                                AND i.workspace_id = '{workspace.id}'
                                {f"AND si.name = '{breakdown}'" if breakdown is not None else "AND si IS NULL"}
                                {f"AND {where_clause}" if where_clause is not None else ""}
                            ) AS invoiceitem ON (
                                invoiceitem.invoice_date = date_trunc('{time_step_unit}', dates.date)
                            )
                            GROUP BY dates.date
                            ORDER BY dates.date ASC;
                        """

                        if breakdown is None:
                            breakdown = "他の" if lang == 'ja' else 'Other'
                    else:
                        raw_query = f"""
                            SELECT DISTINCT
                                dates.date,
                                COALESCE(SUM(invoice.total_price), 0) as total_price
                            FROM (
                                SELECT generate_series(
                                    date_trunc('{time_step_unit}', '{start_time_str}'::timestamp), 
                                    date_trunc('{time_step_unit}', '{stop_time_str}'::timestamp), 
                                    '{time_step}'::interval
                                ) AS date
                            ) AS dates
                            LEFT JOIN (
                                SELECT 
                                    i.total_price,
                                    date_trunc('{time_step_unit}', i.start_date) AS invoice_date
                                FROM data_invoice i 
                                {join_sql if join_sql else ""}
                                WHERE i.usage_status = 'active'
                                {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                                AND i.workspace_id = '{workspace.id}'
                                {f"AND {where_clause}" if where_clause is not None else ""}
                            ) AS invoice ON (
                                invoice.invoice_date = date_trunc('{time_step_unit}', dates.date)
                            )
                            GROUP BY dates.date
                            ORDER BY dates.date ASC;
                        """

                    data = {item['display']: 0 for item in data_points}
                    with connection.cursor() as cursor:
                        cursor.execute(raw_query, list(where_parameters))
                        raw_results = cursor.fetchall()
                        print(
                            f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                        for raw_item in raw_results:
                            data[raw_item[0].strftime(
                                time_format)] = int(raw_item[1])

                    if value_map:
                        breakdown = value_map.get(breakdown, breakdown)
                    results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else label, [
                                   value for value in data.values()],  len(results)))

            elif metric_name == 'recurring_revenue_annually_invoice':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = Invoice.objects.filter(
                    workspace=workspace, usage_status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    Invoice, condition)

                breakdown_values = ['no_breakdown']
                if panel.breakdown:
                    panel.breakdown = panel.breakdown.replace("invoice__", "")
                    breakdown_field = panel.breakdown

                    if 'contact_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            ContactsValueCustomField.objects.filter(
                                field_name_id=id,
                                contact__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    elif 'company_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name_id=id,
                                company__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    elif breakdown_field == 'line_item':
                        breakdown_values = (
                            InvoiceItem
                            .objects
                            .filter(invoice__workspace=workspace, invoice__usage_status='active')
                            .values_list('item_link__name', flat=True)
                            .distinct()
                        )
                    else:
                        breakdown_values = (
                            Invoice
                            .objects
                            .filter(workspace=workspace, usage_status='active')
                            .values_list(breakdown_field, flat=True)
                            .distinct()
                        )

                if time_step == timedelta(days=1):
                    time_step_unit = 'day'
                elif time_step == timedelta(weeks=1):
                    time_step_unit = 'week'
                elif time_step == timedelta(days=30) or time_step == timedelta(days=31):
                    time_step_unit = 'month'
                    time_step = '1 month'
                elif time_step == timedelta(hours=1):
                    time_step_unit = 'hour'

                for breakdown in breakdown_values:
                    where_parameters = where_params
                    breakdown_field = None
                    join_sql = None
                    parent_table = None
                    field_name_id = None
                    if breakdown != 'no_breakdown':
                        breakdown_fields = panel.breakdown.split("__")
                        parent = "invoice"
                        if len(breakdown_fields) > 1:
                            if 'custom_field_relations' in breakdown_fields[0]:
                                parent = breakdown_fields[0]
                                field_name_id = breakdown_fields[2].replace(
                                    "|", "")
                                breakdown_field = "value"
                            else:
                                parent = breakdown_fields[0]
                                breakdown_field = breakdown_fields[1]
                        else:
                            breakdown_field = panel.breakdown
                        join_clauses = {
                            'invoice': (
                                "",
                                (breakdown,),
                                'i'
                            ),
                            'contact': (
                                f"""
                                LEFT JOIN data_contact AS c
                                    ON c.id   = i.contact_id
                                """,
                                (breakdown,),
                                'c'
                            ),
                            'company': (
                                f"""
                                LEFT JOIN data_company AS co
                                    ON co.id   = i.company_id
                                """,
                                (breakdown,),
                                'co'
                            ),
                            'contact_custom_field_relations': (
                                f"""
                                LEFT JOIN data_contactsvaluecustomfield AS cf
                                    ON cf.contact_id     = i.contact_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'company_custom_field_relations': (
                                f"""
                                LEFT JOIN data_companyvaluecustomfield AS cf
                                    ON cf.company_id     = i.company_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'line_item': (
                                "",
                                (),
                                ''
                            )
                            # add more entries here as you introduce new breakdown types…
                        }

                        join_sql, join_params, parent_table = join_clauses[parent]
                        where_parameters += join_params

                    if panel.breakdown == 'line_item':
                        raw_query = f"""
                            SELECT DISTINCT
                                dates.date,
                                COALESCE(SUM(invoiceitem.total_price * 12), 0) as total_price
                            FROM (
                                SELECT generate_series(
                                    '{start_time_str}'::timestamp, 
                                    '{stop_time_str}'::timestamp, 
                                    '{time_step}'::interval
                                ) AS date
                            ) AS dates
                            LEFT JOIN (
                                SELECT 
                                    ii.total_price,
                                    date_trunc('{time_step_unit}', i.start_date) AS invoice_date
                                FROM data_invoiceitem ii
                                LEFT JOIN data_invoice i ON i.id = ii.invoice_id
                                LEFT JOIN data_shopturboitems si ON si.id = ii.item_link_id
                                {join_sql if join_sql else ""}
                                WHERE i.usage_status = 'active'
                                AND i.workspace_id = '{workspace.id}'
                                {f"AND si.name = '{breakdown}'" if breakdown is not None else "AND si IS NULL"}
                                {f"AND {where_clause}" if where_clause is not None else ""}
                            ) AS invoiceitem ON (
                                invoiceitem.invoice_date = dates.date
                            )
                            GROUP BY dates.date
                            ORDER BY dates.date ASC;
                        """

                        if breakdown is None:
                            breakdown = "他の" if lang == 'ja' else 'Other'
                    else:
                        raw_query = f"""
                            SELECT DISTINCT
                                dates.date,
                                COALESCE(SUM(invoice.total_price * 12), 0) as total_price
                            FROM (
                                SELECT generate_series(
                                    '{start_time_str}'::timestamp, 
                                    '{stop_time_str}'::timestamp, 
                                    '{time_step}'::interval
                                ) AS date
                            ) AS dates
                            LEFT JOIN (
                                SELECT 
                                    i.total_price,
                                    date_trunc('{time_step_unit}', i.start_date) AS invoice_date
                                FROM data_invoice i 
                                {join_sql if join_sql else ""}
                                WHERE i.usage_status = 'active'
                                {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                                AND i.workspace_id = '{workspace.id}'
                                {f"AND {where_clause}" if where_clause is not None else ""}
                            ) AS invoice ON (
                                invoice.invoice_date = dates.date
                            )
                            GROUP BY dates.date
                            ORDER BY dates.date ASC;
                        """

                    data = {item['display']: 0 for item in data_points}
                    with connection.cursor() as cursor:
                        cursor.execute(raw_query, list(where_parameters))
                        raw_results = cursor.fetchall()
                        print(
                            f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                        for raw_item in raw_results:
                            data[raw_item[0].strftime(
                                time_format)] = int(raw_item[1])

                    results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else label, [
                                   value for value in data.values()],  len(results)))

            elif metric_name == 'average_recurring_revenue':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = ShopTurboSubscriptions.objects.filter(
                    workspace=workspace, status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    ShopTurboSubscriptions, condition)

                breakdown_values = ['no_breakdown']
                if panel.breakdown:
                    breakdown_field = panel.breakdown

                    if 'contact_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            ContactsValueCustomField.objects.filter(
                                field_name_id=id,
                                contact__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    elif 'company_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name_id=id,
                                company__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    else:
                        breakdown_values = (
                            ShopTurboSubscriptions
                            .objects
                            .filter(workspace=workspace, status='active')
                            .values_list(breakdown_field, flat=True)
                            .distinct()
                        )

                if time_step == timedelta(days=1):
                    time_step_unit = 'day'
                elif time_step == timedelta(weeks=1):
                    time_step_unit = 'week'
                elif time_step == timedelta(days=30) or time_step == timedelta(days=31):
                    time_step_unit = 'month'
                    time_step = '1 month'
                elif time_step == timedelta(hours=1):
                    time_step_unit = 'hour'

                for breakdown in breakdown_values:
                    where_parameters = where_params
                    breakdown_field = None
                    join_sql = None
                    parent_table = None
                    if breakdown != 'no_breakdown':
                        breakdown_fields = panel.breakdown.split("__")
                        parent = "subscription"
                        if len(breakdown_fields) > 1:
                            if 'custom_field_relations' in breakdown_fields[0]:
                                parent = breakdown_fields[0]
                                field_name_id = breakdown_fields[2].replace(
                                    "|", "")
                                breakdown_field = "value"
                            else:
                                parent = breakdown_fields[0]
                                breakdown_field = breakdown_fields[1]
                        else:
                            breakdown_field = panel.breakdown
                        join_clauses = {
                            'subscription': (
                                "",
                                (breakdown,),
                                's'
                            ),
                            'contact': (
                                f"""
                                LEFT JOIN data_contact AS c
                                    ON c.id   = s.contact_id
                                """,
                                (breakdown,),
                                'c'
                            ),
                            'company': (
                                f"""
                                LEFT JOIN data_company AS co
                                    ON co.id   = s.company_id
                                """,
                                (breakdown,),
                                'co'
                            ),
                            'contact_custom_field_relations': (
                                f"""
                                LEFT JOIN data_contactsvaluecustomfield AS cf
                                    ON cf.contact_id     = s.contact_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'company_custom_field_relations': (
                                f"""
                                LEFT JOIN data_companyvaluecustomfield AS cf
                                    ON cf.company_id     = s.company_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            # add more entries here as you introduce new breakdown types…
                        }

                        join_sql, join_params, parent_table = join_clauses[parent]
                        where_parameters += join_params
                        where_parameters += join_params

                    raw_query = f"""
                        SELECT DISTINCT
                            dates.date,
                            COALESCE(SUM(invoice.total_price), 0) / NULLIF(COUNT(fs.id), 0) as total_price
                        FROM (
                            SELECT generate_series(
                                '{start_time_str}'::timestamp, 
                                '{stop_time_str}'::timestamp, 
                                '{time_step}'::interval
                            ) AS date
                        ) AS dates
                        LEFT JOIN (
                            SELECT 
                                i.total_price,
                                date_trunc('{time_step_unit}', i.start_date) AS invoice_date,
                                s.workspace_id
                            FROM data_invoice i
                            JOIN data_shopturbosubscriptions_invoices AS si ON si.invoice_id = i.id
                            JOIN data_shopturbosubscriptions AS s ON s.id = si.shopturbosubscriptions_id
                            {join_sql if join_sql else ""}
                            WHERE s.status = 'active'
                            {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                            AND s.workspace_id = '{workspace.id}'
                            {f"AND {where_clause}" if where_clause is not None else ""}
                        ) AS invoice ON (
                            invoice.invoice_date = dates.date
                        )
                        LEFT JOIN (
                            SELECT s.*
                            FROM data_shopturbosubscriptions AS s
                            {join_sql if join_sql else ""}  
                            WHERE 
                            s.workspace_id = '{workspace.id}'
                            {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                        ) AS fs
                            ON (
                                fs.status = 'active'
                                AND fs.workspace_id = '{workspace.id}'
                                AND (fs.start_date <= dates.date)
                                AND (fs.end_date >= dates.date OR fs.end_date IS NULL)
                                {'AND ' + where_clause if where_clause is not None else ''}
                            )
                        GROUP BY dates.date
                        ORDER BY dates.date ASC;
                    """

                    data = {item['display']: 0 for item in data_points}
                    with connection.cursor() as cursor:
                        cursor.execute(raw_query, list(where_parameters))
                        raw_results = cursor.fetchall()
                        print(
                            f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                        for raw_item in raw_results:
                            data[raw_item[0].strftime(time_format)] = int(
                                raw_item[1] if raw_item[1] is not None else 0)

                    results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else panel, [
                                   value for value in data.values()],  len(results)))

            elif metric_name == 'average_recurring_revenue_annually':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = ShopTurboSubscriptions.objects.filter(
                    workspace=workspace, status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    ShopTurboSubscriptions, condition)

                breakdown_values = ['no_breakdown']
                if panel.breakdown:
                    breakdown_field = panel.breakdown

                    if 'contact_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            ContactsValueCustomField.objects.filter(
                                field_name_id=id,
                                contact__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    elif 'company_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name_id=id,
                                company__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    else:
                        breakdown_values = (
                            ShopTurboSubscriptions
                            .objects
                            .filter(workspace=workspace, status='active')
                            .values_list(breakdown_field, flat=True)
                            .distinct()
                        )

                if time_step == timedelta(days=1):
                    time_step_unit = 'day'
                elif time_step == timedelta(weeks=1):
                    time_step_unit = 'week'
                elif time_step == timedelta(days=30) or time_step == timedelta(days=31):
                    time_step_unit = 'month'
                    time_step = '1 month'
                elif time_step == timedelta(hours=1):
                    time_step_unit = 'hour'

                for breakdown in breakdown_values:
                    where_parameters = where_params
                    breakdown_field = None
                    join_sql = None
                    parent_table = None
                    if breakdown != 'no_breakdown':
                        breakdown_fields = panel.breakdown.split("__")
                        parent = "subscription"
                        if len(breakdown_fields) > 1:
                            if 'custom_field_relations' in breakdown_fields[0]:
                                parent = breakdown_fields[0]
                                field_name_id = breakdown_fields[2].replace(
                                    "|", "")
                                breakdown_field = "value"
                            else:
                                parent = breakdown_fields[0]
                                breakdown_field = breakdown_fields[1]
                        else:
                            breakdown_field = panel.breakdown
                        join_clauses = {
                            'subscription': (
                                "",
                                (breakdown,),
                                's'
                            ),
                            'contact': (
                                f"""
                                LEFT JOIN data_contact AS c
                                    ON c.id   = s.contact_id
                                """,
                                (breakdown,),
                                'c'
                            ),
                            'company': (
                                f"""
                                LEFT JOIN data_company AS co
                                    ON co.id   = s.company_id
                                """,
                                (breakdown,),
                                'co'
                            ),
                            'contact_custom_field_relations': (
                                f"""
                                LEFT JOIN data_contactsvaluecustomfield AS cf
                                    ON cf.contact_id     = s.contact_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'company_custom_field_relations': (
                                f"""
                                LEFT JOIN data_companyvaluecustomfield AS cf
                                    ON cf.company_id     = s.company_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            # add more entries here as you introduce new breakdown types…
                        }

                        join_sql, join_params, parent_table = join_clauses[parent]
                        where_parameters += join_params
                        where_parameters += join_params

                    raw_query = f"""
                        SELECT DISTINCT
                            dates.date,
                            COALESCE(SUM(invoice.total_price * 12), 0) / NULLIF(COUNT(fs.id), 0) as total_price
                        FROM (
                            SELECT generate_series(
                                '{start_time_str}'::timestamp, 
                                '{stop_time_str}'::timestamp, 
                                '{time_step}'::interval
                            ) AS date
                        ) AS dates
                        LEFT JOIN (
                            SELECT 
                                i.total_price,
                                date_trunc('{time_step_unit}', i.start_date) AS invoice_date,
                                s.workspace_id
                            FROM data_invoice i
                            JOIN data_shopturbosubscriptions_invoices AS si ON si.invoice_id = i.id
                            JOIN data_shopturbosubscriptions AS s ON s.id = si.shopturbosubscriptions_id
                            {join_sql if join_sql else ""}
                            WHERE s.status = 'active'
                            {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                            AND s.workspace_id = '{workspace.id}'
                            {f"AND {where_clause}" if where_clause is not None else ""}
                        ) AS invoice ON (
                            invoice.invoice_date = dates.date
                        )
                        LEFT JOIN (
                            SELECT s.*
                            FROM data_shopturbosubscriptions AS s
                            {join_sql if join_sql else ""}  
                            WHERE 
                            s.workspace_id = '{workspace.id}'
                            {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                        ) AS fs
                            ON (
                                fs.status = 'active'
                                AND fs.workspace_id = '{workspace.id}'
                                AND (fs.start_date <= dates.date)
                                AND (fs.end_date >= dates.date OR fs.end_date IS NULL)
                                {'AND ' + where_clause if where_clause is not None else ''}
                            )
                        GROUP BY dates.date
                        ORDER BY dates.date ASC;
                    """

                    data = {item['display']: 0 for item in data_points}
                    with connection.cursor() as cursor:
                        cursor.execute(raw_query, list(where_parameters))
                        raw_results = cursor.fetchall()
                        print(
                            f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                        for raw_item in raw_results:
                            data[raw_item[0].strftime(time_format)] = int(
                                raw_item[1] if raw_item[1] is not None else 0)

                    results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else panel, [
                                   value for value in data.values()],  len(results)))

            elif metric_name == 'expansion_recurring_revenue':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = ShopTurboSubscriptions.objects.filter(
                    workspace=workspace, status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    ShopTurboSubscriptions, condition)
                breakdown_values = ['no_breakdown']
                if panel.breakdown:
                    breakdown_field = panel.breakdown

                    if 'contact_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            ContactsValueCustomField.objects.filter(
                                field_name_id=id,
                                contact__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    elif 'company_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name_id=id,
                                company__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    else:
                        breakdown_values = (
                            ShopTurboSubscriptions
                            .objects
                            .filter(workspace=workspace, status='active')
                            .values_list(breakdown_field, flat=True)
                            .distinct()
                        )

                for breakdown in breakdown_values:
                    where_parameters = where_params
                    breakdown_field = None
                    join_sql = None
                    parent_table = None
                    if breakdown != 'no_breakdown':
                        breakdown_fields = panel.breakdown.split("__")
                        parent = "subscription"
                        if len(breakdown_fields) > 1:
                            if 'custom_field_relations' in breakdown_fields[0]:
                                parent = breakdown_fields[0]
                                field_name_id = breakdown_fields[2].replace(
                                    "|", "")
                                breakdown_field = "value"
                            else:
                                parent = breakdown_fields[0]
                                breakdown_field = breakdown_fields[1]
                        else:
                            breakdown_field = panel.breakdown
                        join_clauses = {
                            'subscription': (
                                "",
                                (breakdown,),
                                's'
                            ),
                            'contact': (
                                f"""
                                LEFT JOIN data_contact AS c
                                    ON c.id   = s.contact_id
                                """,
                                (breakdown,),
                                'c'
                            ),
                            'company': (
                                f"""
                                LEFT JOIN data_company AS co
                                    ON co.id   = s.company_id
                                """,
                                (breakdown,),
                                'co'
                            ),
                            'contact_custom_field_relations': (
                                f"""
                                LEFT JOIN data_contactsvaluecustomfield AS cf
                                    ON cf.contact_id     = s.contact_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'company_custom_field_relations': (
                                f"""
                                LEFT JOIN data_companyvaluecustomfield AS cf
                                    ON cf.company_id     = s.company_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            # add more entries here as you introduce new breakdown types…
                        }

                        join_sql, join_params, parent_table = join_clauses[parent]
                        where_parameters += join_params

                    raw_query = f"""
                        SELECT DISTINCT
                            dates.date,
                            COALESCE(SUM(fs.total_price), 0) as total_price
                        FROM (
                            SELECT generate_series(
                                '{start_time_str}'::timestamp, '{stop_time_str}'::timestamp, '{time_step}'::interval
                            ) AS date
                        ) AS dates
                        LEFT JOIN (
                            SELECT s.*
                            FROM data_shopturbosubscriptions AS s
                            {join_sql if join_sql else ""}  
                            WHERE 
                            s.workspace_id = '{workspace.id}'
                            {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                        ) AS fs
                            ON (
                                fs.status = 'active'
                                AND fs.workspace_id = '{workspace.id}'
                                AND (fs.start_date <= dates.date)
                                AND (fs.end_date >= dates.date OR fs.end_date IS NULL)
                                {'AND ' + where_clause if where_clause is not None else ''}
                            )
                        GROUP BY dates.date
                        ORDER BY dates.date ASC;
                    """

                    data = {item['display']: 0 for item in data_points}
                    with connection.cursor() as cursor:
                        cursor.execute(raw_query, list(where_parameters))
                        raw_results = cursor.fetchall()
                        print(
                            f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                        prev_data = None
                        for raw_item in raw_results:
                            if prev_data is None:
                                prev_data = int(raw_item[1])
                                data[raw_item[0].strftime(time_format)] = 0
                                continue
                            this_month = int(raw_item[1])
                            data[raw_item[0].strftime(time_format)] = max(
                                0, this_month - prev_data)
                            prev_data = this_month

                    results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else panel, [
                                   value for value in data.values()],  len(results)))

            elif metric_name == 'downgrade_recurring_revenue':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = ShopTurboSubscriptions.objects.filter(
                    workspace=workspace, status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1115: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    ShopTurboSubscriptions, condition)
                breakdown_values = ['no_breakdown']
                if panel.breakdown:
                    breakdown_field = panel.breakdown

                    if 'contact_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            ContactsValueCustomField.objects.filter(
                                field_name_id=id,
                                contact__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    elif 'company_custom_field_relations__value__' in breakdown_field:
                        id = breakdown_field.split("|")[1]
                        breakdown_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name_id=id,
                                company__workspace=workspace
                            )
                            .exclude(value__isnull=True)
                            .values_list('value', flat=True)
                            .distinct()
                        )
                    else:
                        breakdown_values = (
                            ShopTurboSubscriptions
                            .objects
                            .filter(workspace=workspace, status='active')
                            .values_list(breakdown_field, flat=True)
                            .distinct()
                        )

                for breakdown in breakdown_values:
                    where_parameters = where_params
                    breakdown_field = None
                    join_sql = None
                    parent_table = None
                    if breakdown != 'no_breakdown':
                        breakdown_fields = panel.breakdown.split("__")
                        parent = "subscription"
                        if len(breakdown_fields) > 1:
                            if 'custom_field_relations' in breakdown_fields[0]:
                                parent = breakdown_fields[0]
                                field_name_id = breakdown_fields[2].replace(
                                    "|", "")
                                breakdown_field = "value"
                            else:
                                parent = breakdown_fields[0]
                                breakdown_field = breakdown_fields[1]
                        else:
                            breakdown_field = panel.breakdown
                        join_clauses = {
                            'subscription': (
                                "",
                                (breakdown,),
                                's'
                            ),
                            'contact': (
                                f"""
                                LEFT JOIN data_contact AS c
                                    ON c.id   = s.contact_id
                                """,
                                (breakdown,),
                                'c'
                            ),
                            'company': (
                                f"""
                                LEFT JOIN data_company AS co
                                    ON co.id   = s.company_id
                                """,
                                (breakdown,),
                                'co'
                            ),
                            'contact_custom_field_relations': (
                                f"""
                                LEFT JOIN data_contactsvaluecustomfield AS cf
                                    ON cf.contact_id     = s.contact_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            'company_custom_field_relations': (
                                f"""
                                LEFT JOIN data_companyvaluecustomfield AS cf
                                    ON cf.company_id     = s.company_id
                                    AND cf.field_name_id = '{field_name_id}'
                                """,
                                (breakdown,),
                                'cf'
                            ),
                            # add more entries here as you introduce new breakdown types…
                        }

                        join_sql, join_params, parent_table = join_clauses[parent]
                        where_parameters += join_params

                    raw_query = f"""
                        SELECT DISTINCT
                            dates.date,
                            COALESCE(SUM(fs.total_price), 0) as total_price
                        FROM (
                            SELECT generate_series(
                                '{start_time_str}'::timestamp, '{stop_time_str}'::timestamp, '{time_step}'::interval
                            ) AS date
                        ) AS dates
                        LEFT JOIN (
                            SELECT s.*
                            FROM data_shopturbosubscriptions AS s
                            {join_sql if join_sql else ""}  
                            WHERE 
                            s.workspace_id = '{workspace.id}'
                            {'AND '+ parent_table +'.' + breakdown_field + ' = %s' if breakdown_field else ''}
                        ) AS fs
                            ON (
                                fs.status = 'active'
                                AND fs.workspace_id = '{workspace.id}'
                                AND (fs.start_date <= dates.date)
                                AND (fs.end_date >= dates.date OR fs.end_date IS NULL)
                                {'AND ' + where_clause if where_clause is not None else ''}
                            )
                        GROUP BY dates.date
                        ORDER BY dates.date ASC;
                    """

                data = {item['display']: 0 for item in data_points}
                with connection.cursor() as cursor:
                    cursor.execute(raw_query, list(where_parameters))
                    raw_results = cursor.fetchall()
                    print(
                        f'=== report.py - 916: Query result for {metric_name} - {raw_results}')

                    prev_data = None
                    for raw_item in raw_results:
                        if prev_data is None:
                            prev_data = int(raw_item[1])
                            data[raw_item[0].strftime(time_format)] = 0
                            continue
                        this_month = int(raw_item[1])
                        data[raw_item[0].strftime(time_format)] = min(
                            0, this_month - prev_data)
                        prev_data = this_month

                results.append(new_result(f"{label} - {breakdown}" if breakdown != 'no_breakdown' else panel, [
                               value for value in data.values()],  len(results)))

            elif metric_name == 'revenue':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                currency = ShopTurboOrders.objects.filter(
                    workspace=workspace, status='active').values_list('currency', flat=True).first()
                label = f'{label} ({next((symbol for code, symbol in CURRENCY if code.lower() == currency.lower()), "")})' if currency else label

                condition = Q(workspace=workspace, order_at__gte=start_time_str,
                              order_at__lte=stop_time_str, status='active', total_price__isnull=False)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1114: Filter for {metric_name}: {condition}')
                queryset = (
                    ShopTurboOrders.objects
                    .filter(condition)
                    .annotate(date=trunc_func('order_at'))
                    .values('date')
                    .annotate(
                        total_price=Sum(
                            Case(
                                When(tax__isnull=True, then=F('total_price')),
                                default=F('total_price') *
                                (1 - F('tax') / 100),
                                output_field=FloatField()
                            )
                        )
                    )
                    .order_by('date')
                )
                print(
                    f'=== report.py - 947: Query result for {metric_name} - {queryset}')

                data = {item['display']: 0 for item in data_points}
                for item in queryset:
                    data[item['date'].strftime(
                        time_format)] = item['total_price']

                results.append(new_result(
                    label, [value for value in data.values()],  len(results)))

            elif metric_name == 'number_companies':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                condition = Q(
                    workspace=workspace, created_at__gte=start_time_str, created_at__lte=stop_time_str)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1142: Filter for {metric_name}: {condition}')
                queryset = (
                    Company.objects
                    .filter(condition)
                    .annotate(date=trunc_func('created_at'))
                    .values('date')
                    .annotate(count=Count('id'))
                    .order_by('date')
                )
                print(
                    f'=== report.py - 967: Query result for {metric_name} - {queryset}')

                data = {item['display']: 0 for item in data_points}
                for item in queryset:
                    data[item['date'].strftime(time_format)] = item['count']

                results.append(new_result(
                    label, [value for value in data.values()],  len(results)))

            elif metric_name == 'number_contacts':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                condition = Q(
                    workspace=workspace, created_at__gte=start_time_str, created_at__lte=stop_time_str)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1168: Filter for {metric_name}: {condition}')
                queryset = (
                    Contact.objects
                    .filter(condition)
                    .annotate(date=trunc_func('created_at'))
                    .values('date')
                    .annotate(count=Count('id'))
                    .order_by('date')
                )
                print(
                    f'=== report.py - 985: Query result for {metric_name} - {queryset}')

                data = {item['display']: 0 for item in data_points}
                for item in queryset:
                    data[item['date'].strftime(time_format)] = item['count']

                # Metric type[1][0] is for shows the cumulative, using it to avoid using hard coded
                if metric_type == METRIC_TYPE[1][0]:
                    all_filter = rule_filter
                    if metric_filter:
                        all_filter &= filter_condition

                    cumulative_count = Contact.objects.filter(
                        Q(created_at__lt=start_time_str) & all_filter).count()
                    for idx, _ in data.items():
                        data[idx] += cumulative_count
                        cumulative_count = data[idx]

                results.append(new_result(
                    label, [value for value in data.values()], len(results)))

            elif 'unique_session' in metric_name or 'session' in metric_name:
                unique_session = False
                if 'unique_session' in metric_name:
                    unique_session = True
                    label = 'ユニークセッション' if lang == 'ja' else 'Unique Sessions'
                else:
                    label = 'セッション' if lang == 'ja' else 'Sessions'
                label = label.upper()

                # rule_filter = Q(workspace=workspace, created_at__gte=start_time_str, created_at__lte=stop_time_str)
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    rule_filter &= filter_condition
                    print(f'=== Filter for {metric_name}: {rule_filter}')

                try:
                    rule_id = metric_name.split('-', 1)[1]
                    rule = EventCapturingRule.objects.get(id=rule_id)
                    label = f'{rule.name} - ' + label
                    rule_filter &= Q(rule_id=rule_id)
                except:
                    label = f"{'ページビュー' if lang == 'ja' else 'Page View'} - " + label
                    rule_filter &= Q(rule_id__isnull=True)

                list_data = []
                b_list_data = []
                if panel.breakdown:
                    data = {item['display']: 0 for item in data_points}
                    print(
                        f'=== report.py - 1008: panel.breakdown - {panel.breakdown.name}')
                    if unique_session:
                        distinct_field = 'session'
                    else:
                        distinct_field = 'id'

                    # print(f'=== report.py distinct_values - 1019: {distinct_values}')
                    breakdown_property = panel.breakdown.property_name
                    if panel.breakdown.event_data_type == 'parameter':
                        print("================================PARAMETER")
                        # Get the list of available parameter variation key & value. Example: ["utm_campaign=ig01", "utm_campaign=fb03"]
                        queryset = (PixelEvent.objects
                                    .filter(
                                        rule_filter,
                                        # Q(**{"data__parameters__"+breakdown_property+"__isnull": False}),
                                        workspace=workspace,
                                        created_at__gte=start_time,
                                        created_at__lte=stop_time,
                                    )
                                    .annotate(
                                        property_value=F(
                                            "data__parameters__"+breakdown_property),
                                        date=trunc_func('created_at'))
                                    .values('rule__name', 'date', 'property_value')
                                    .annotate(sessions=Count(distinct_field, distinct=True))
                                    ).order_by('-date', '-sessions', 'property_value', )

                    else:
                        print('.......................',
                              rule_filter, start_time, stop_time)
                        queryset = PixelEvent.objects.filter(
                            rule_filter,
                            workspace=workspace,
                            created_at__gte=start_time,
                            created_at__lte=stop_time,
                            # **{distinct_field + '__in': Subquery(distinct_values)}
                        ).annotate(
                            date=trunc_func('created_at'),
                            property_value=F(
                                'data__' + breakdown_property),
                        ).values(
                            'rule__name',
                            'date',
                            'property_value'
                        ).annotate(
                            sessions=Count('property_value')
                        ).order_by('property_value', '-date', '-sessions')

                    print(
                        f'=== report.py - 1023: Query result for {breakdown_property} - {queryset}', queryset.query)

                    list_label = queryset.values_list(
                        'property_value', flat=True).exclude(property_value=None)
                    list_label = list(dict.fromkeys(list_label))
                    list_label = list_label[:5] + ["Other"]

                    # Prepare the base list data for each label.
                    for label in list_label:
                        b_list_data.append({
                            'label': f'{panel.breakdown.name}: {label}' if label != "Other" else label,
                            'data': {data_point['display']: 0 for data_point in data_points}
                        })
                    print(
                        f'=== report.py - 1026: list_label====== - {list_label}')

                    for item in queryset:
                        if item['property_value'] not in list_label:
                            item['property_value'] = "Other"

                        print('item property', item['property_value'])

                        index = list_label.index(item['property_value'])
                        # b_data means base data (?)
                        b_data = b_list_data[index]['data']
                        b_data[item['date'].strftime(
                            time_format)] = item['sessions']
                        b_list_data[index]['data'] = b_data

                    list_data.extend(b_list_data)

                else:
                    try:
                        queryset = PixelEvent.objects.filter(
                            rule_filter & Q(
                                workspace=workspace, created_at__date__gte=start_time, created_at__date__lte=stop_time)
                        ).annotate(date=trunc_func('created_at')).values('rule__name', 'date').annotate(
                            sessions=Count(
                                'id' if not unique_session else 'session', distinct=True)
                        ).order_by('date')

                        # print("sql----",str(queryset.query))

                    except Exception as e:
                        print(
                            f'=== report.py - 1023: Error in Query result for {metric_name} - {e}')
                        queryset = ()
                    print(
                        f'=== report.py - 1008: Query result for {metric_name} - {queryset}')
                    """
                    The expected results will be like this:
                    {
                        "<rule name>": {
                            {
                                "data": {
                                    <index>: [...],
                                }
                                "bg_color": "rgba(79*<index>, 201*<index>, 218*<index>, 0.2)",
                                "bg_color_border": "rgba(79*<index>, 201*<index>, 218*<index>, 1)",
                            }
                        }
                    }
                    """

                    if len(queryset) < 1:
                        data = {item['display']: 0 for item in data_points}
                        list_data.append({
                            'label': label,
                            'data': data
                        })
                    else:
                        for item in queryset:
                            list_label = [item['label'] for item in list_data]
                            try:
                                index = list_label.index(label)
                                data = list_data[index]['data']
                            except:
                                data = {item['display']
                                    : 0 for item in data_points}
                                list_data.append({
                                    'label': label,
                                    'data': data
                                })

                            data[item['date'].strftime(
                                time_format)] = item['sessions']

                # print(f'=== report.py - 1040: list_data - {list_data}')

                for item in list_data:
                    result = new_result(
                        item['label'], [value for value in item['data'].values()], len(results))
                    results.append(result)

                print('results', results)

            elif metric_name == 'cvr':
                label = 'コンバージョン率 （％）' if lang == 'ja' else 'Conversion Rate (%)'
                result = new_result(label, data_points, len(results))

                condition = rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1395: Filter for {metric_name}: {condition}')
                where_clause, where_params = build_raw_filters(
                    ShopTurboOrders, condition)

                raw_sql_query = f"""
                    SELECT
                        COALESCE("orders"."date", "sessions"."date") AS "date",
                        CASE
                            WHEN "count_orders" > 0 AND "count_sessions" > 0
                                THEN ("count_orders" * 100.0) / "count_sessions"
                            ELSE 0
                        END::float AS "result",
                        "count_orders",
                        "count_sessions"
                    FROM
                        (
                            SELECT
                                date_trunc('{group_by}', "data_shopturboorders"."order_at") AS "date",
                                COUNT("data_shopturboorders"."id")::float AS "count_orders"
                            FROM
                                "data_shopturboorders"
                            WHERE
                                (
                                    date_trunc('{group_by}', "data_shopturboorders"."order_at") >= '{start_time_str}'
                                    AND date_trunc('{group_by}', "data_shopturboorders"."order_at") <= '{stop_time_str}'
                                    AND "data_shopturboorders"."status" = 'active'
                                    AND "data_shopturboorders"."workspace_id" = '{workspace.id}'
                                    {'AND ' + where_clause if where_clause is not None else ''}
                                )
                            GROUP BY 1
                        ) AS "orders"
                    FULL OUTER JOIN
                        (
                            SELECT
                                date_trunc('{group_by}', "data_pixelevent"."created_at") AS "date",
                                COUNT(DISTINCT "data_pixelevent"."session")::float AS "count_sessions"
                            FROM
                                "data_pixelevent"
                            WHERE
                                (
                                    date_trunc('{group_by}', "data_pixelevent"."created_at") >= '{start_time_str}'
                                    AND date_trunc('{group_by}', "data_pixelevent"."created_at") <= '{stop_time_str}'
                                    AND "data_pixelevent"."workspace_id" = '{workspace.id}'
                                    AND "data_pixelevent"."rule_id" IS NULL
                                )
                            GROUP BY 1
                        ) AS "sessions"
                    ON
                        "orders"."date" = "sessions"."date"
                    ORDER BY 1 ASC;
                """

                data = {item['display']: 0 for item in data_points}
                with connection.cursor() as cursor:
                    cursor.execute(raw_sql_query, list(where_params))
                    raw_results = cursor.fetchall()
                    print(
                        f'=== report.py - 883: Query result for {metric_name} - {raw_results}')

                    for raw_item in raw_results:
                        print(f'=== report.py - 1074: {raw_item}')
                        data[raw_item[0].strftime(
                            time_format)] = float(raw_item[1])

                results.append(new_result(
                    label, [value for value in data.values()], len(results)))

            elif metric_name == 'ltv':
                chart_config['fill'] = False
                chart_config['radius'] = 0
                chart_config['is_ltv'] = True
                if lang == 'ja':
                    chart_config['x_title'] = '最初の購入からの日数'
                    chart_config['y_title'] = 'ユーザーごとの収益'
                    if group_by == 'week':
                        chart_config['x_title'] = '最初の購入からの週数'
                    elif group_by == 'month':
                        chart_config['x_title'] = '最初の購入からの月数'
                else:
                    chart_config['x_title'] = f'{group_by.capitalize()}s since first purchase'
                    chart_config['y_title'] = 'Revenue per user'

                num_days, units_per_day, data_points, time_step = get_ltv_chart_params(
                    group_by)
                x_axis = [i for i in range(data_points)]

                # Filter list of first orders for each separate user (contact)
                min_order_dates = ShopTurboOrders.objects.filter(
                    contact__isnull=False,
                    status='active'
                ).values('contact').annotate(min_order_date=Min('order_at'))

                first_orders = ShopTurboOrders.objects.filter(
                    contact__isnull=False,
                    order_at__in=min_order_dates.values('min_order_date'),
                    status='active'
                ).order_by('contact', 'order_at', 'id').distinct('contact')

                temp_results = {}
                """ 
                - temp_result has key is one of Month in a year, which contains first purchase of specific user
                    Jan, 2024... Feb, 2025...
                - LTV formula:
                    ltv = total_revenue/number_of_users (contacts)
                """
                for fo in first_orders:
                    print(
                        f' === report.py - 1109 ... First order of contact {fo.contact.id} : {fo.id} ({fo.total_price} {fo.currency}) at {fo.order_at}')
                    month = fo.order_at.strftime("%b, %Y")

                    start_date = fo.order_at.replace(
                        day=1, hour=0, minute=0, second=0, microsecond=0)
                    time_points = data_points
                    if start_date + timedelta(days=num_days - 1) > timezone.now():
                        time_points = round(
                            (timezone.now() - start_date).days*units_per_day) + 1

                    print(
                        f' === report.py - 1110 ... {month} days_diff: {time_points}')
                    date_range = [fo.order_at + i *
                                  time_step for i in range(time_points)]

                    if month not in temp_results:
                        temp_results[month] = {
                            'customer_list': [],
                            'revenue': [0] * time_points
                        }
                    if fo.contact.id not in temp_results[month]['customer_list']:
                        temp_results[month]['customer_list'].append(
                            fo.contact.id)

                    condition = Q(contact=fo.contact,
                                  company=fo.company, status='active')
                    condition &= rule_filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 1168: Filter for {metric_name}: {condition}')

                    cumulative = 0
                    for i, date in enumerate(date_range):
                        queryset = (ShopTurboOrders.objects
                                    .filter(build_date_filter(date, group_by) & condition)
                                    .annotate(date=trunc_func('order_at'))
                                    .values('id', 'order_at')
                                    .aggregate(
                                        total_revenue=Sum(
                                            Case(
                                                When(tax__isnull=True,
                                                     then=F('total_price')),
                                                default=F('total_price') *
                                                (1 - F('tax') / 100),
                                                output_field=FloatField()
                                            )
                                        )
                                    )
                                    )
                        cumulative += queryset['total_revenue'] if queryset['total_revenue'] else 0
                        temp_results[month]['revenue'][i] += cumulative

                for month, tmp_result in temp_results.items():
                    print(
                        f' === report.py - 1141 ... Dataset for {month}: {tmp_result}')
                    data = [0] * len(tmp_result['revenue'])
                    customer_count = len(tmp_result['customer_list'])
                    for i, revenue in enumerate(tmp_result['revenue']):
                        data[i] = revenue/customer_count

                    results.append(new_result(month, data, len(results)))

            elif metric_name == 'formula':
                metric = PanelMetric.objects.filter(
                    metric__contains=metric_name, reportpanel=panel).first()
                meta_data = metric.meta_data
                label = metric.name

                variable_results = {}
                for key, var in meta_data['variables'].items():
                    print(key, var)
                    time_field_name = 'created_at'
                    if var['source'] == 'orders':
                        time_field_name = 'order_at'
                        model_objects = ShopTurboOrders.objects
                    elif var['source'] == 'contacts':
                        time_field_name = 'created_at'
                        model_objects = Contact.objects
                    elif var['source'] == 'events':
                        time_field_name = 'created_at'
                        model_objects = PixelEvent.objects

                    if var['math'] == 'total_count':
                        expression = Coalesce(
                            Count(var['property']), 0, output_field=FloatField())
                    elif var['math'] == 'unique_count':
                        expression = Coalesce(
                            Count(var['property'], distinct=True), 0, output_field=FloatField())
                    elif var['math'] == 'sum':
                        expression = Coalesce(
                            Sum(var['property']), 0, output_field=FloatField())
                    elif var['math'] == 'average':
                        expression = Coalesce(
                            Avg(var['property']), 0, output_field=FloatField())
                    elif var['math'] == 'max':
                        expression = Coalesce(
                            Max(var['property']), 0, output_field=FloatField())
                    elif var['math'] == 'min':
                        expression = Coalesce(
                            Min(var['property']), 0, output_field=FloatField())

                    condition = Q(**{"workspace": workspace, time_field_name +
                                  "__gte": start_time_str, time_field_name + "__lte": stop_time_str})
                    condition &= rule_filter

                    # Metric filter conditions for Custom Chart
                    if 'filters' in var:
                        for filter_ in var['filters']:
                            filter_["filter_source"] = var['source']
                        condition &= build_filters(var['filters'])
                        print(
                            f'=== report.py - 1727: Filter for {metric_name}: {condition}')

                    # General metric filter
                    if metric_filter:
                        filter_condition = build_filters(
                            metric_filter['filters'])
                        condition &= filter_condition
                        print(
                            f'=== report.py - 1727: Filter for {metric_name}: {condition}')

                    queryset = (
                        model_objects
                        .filter(condition)
                        .annotate(date=trunc_func(time_field_name))
                        .values('date')
                        .annotate(stat=expression)
                        .order_by('date')
                    )
                    print(
                        f'=== report.py - 848: Query result for {metric_name} - {queryset}')

                    data = {item['display']: 0 for item in data_points}
                    for item in queryset:
                        data[item['date'].strftime(time_format)] = item['stat']

                    variable_results[key] = [value for value in data.values()]

                data = {item['display']: 0 for item in data_points}
                formula = meta_data['math']
                for i, date in enumerate(data_points):
                    variables = {key: values[i]
                                 for key, values in variable_results.items()}
                    try:
                        result = eval(formula, variables)
                    except:
                        result = 0
                    data[date['display']] = result

                results.append(new_result(
                    label, [value for value in data.values()],  len(results)))

            elif metric_name == 'number_cases':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                condition = Q(
                    workspace=workspace, created_at__gte=start_time_str, created_at__lte=stop_time_str)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 1168: Filter for {metric_name}: {condition}')
                queryset = (
                    Deals.objects
                    .filter(condition)
                    .annotate(date=trunc_func('created_at'))
                    .values('date')
                    .annotate(count=Count('id'))
                    .order_by('date')
                )
                print(
                    f'=== report.py - 985: Query result for {metric_name} - {queryset}')

                data = {item['display']: 0 for item in data_points}
                for item in queryset:
                    data[item['date'].strftime(time_format)] = item['count']

                # Metric type[1][0] is for shows the cumulative, using it to avoid using hard coded
                if metric_type == METRIC_TYPE[1][0]:
                    all_filter = rule_filter
                    if metric_filter:
                        all_filter &= filter_condition

                    cumulative_count = Deals.objects.filter(
                        Q(created_at__lt=start_time_str) & all_filter).count()
                    for idx, _ in data.items():
                        data[idx] += cumulative_count
                        cumulative_count = data[idx]

                results.append(new_result(
                    label, [value for value in data.values()], len(results)))

            elif metric_name == 'inventory':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                has_breakdown = True if panel.breakdown else False
                inventory_data = {}
                col_display = "inventory"
                if has_breakdown:
                    if panel.breakdown == 'inventory_id':
                        obj, created = ObjectManager.objects.get_or_create(
                            workspace=workspace, page_group_type=TYPE_OBJECT_INVENTORY)
                        if created:
                            try:
                                col_display = ','.join(
                                    DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_INVENTORY])
                            except:
                                col_display = "inventory_id"
                            obj.column_display = col_display
                            obj.save()
                        else:
                            col_display = obj.column_display

                    elif panel.breakdown == 'item_id':
                        obj, created = ObjectManager.objects.get_or_create(
                            workspace=workspace, page_group_type=TYPE_OBJECT_ITEM)
                        if created:
                            try:
                                col_display = ','.join(
                                    DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_ITEM])
                            except:
                                col_display = 'item_id'
                            obj.column_display = col_display
                            obj.save()
                        else:
                            col_display = obj.column_display

                    elif panel.breakdown == 'warehouse_id':
                        col_display = ''

                    col_display = col_display.split(',')

                condition = Q(workspace=workspace, transaction_date__gte=start_time_str,
                              transaction_date__lte=stop_time_str)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'==== report.py - 1168: Filter for {metric_name}: {condition}')

                queryset = (
                    InventoryTransaction.objects
                    .filter(condition)
                    .annotate(date=trunc_func('transaction_date'))
                    .values('date', 'inventory__id', 'inventory__item__id', 'inventory__warehouse__id', 'transaction_type')
                    .annotate(count=Sum('amount'))
                    .order_by('date')
                )

                print(
                    f'=== report.py - 985: Query result for {metric_name} - {queryset}')

                init_data = {}
                for item in queryset:
                    key = 'all'
                    if has_breakdown:
                        if panel.breakdown == 'inventory_id':
                            key = item['inventory__id']

                        elif panel.breakdown == 'item_id':
                            key = item['inventory__item__id']

                        elif panel.breakdown == 'warehouse_id':
                            key = item['inventory__warehouse__id']

                    if key not in inventory_data:
                        inventory_data[key] = {}

                    if item['date'].strftime(time_format) not in inventory_data[key]:
                        inventory_data[key][item['date'].strftime(time_format)] = {
                            'in': 0,
                            'out': 0,
                            'adjust': 0,
                        }

                    inventory_data[key][item['date'].strftime(
                        time_format)][item['transaction_type']] += item['count']

                all_filter = rule_filter
                if metric_filter:
                    all_filter &= filter_condition

                initial_inventory = (
                    InventoryTransaction.objects
                    .filter(Q(transaction_date__lt=start_time_str, workspace=workspace) & all_filter)
                    .annotate(date=trunc_func('transaction_date'))
                    .values('inventory__id', 'inventory__item__id', 'inventory__warehouse__id', 'transaction_type')
                    .annotate(count=Sum('amount'))
                    .order_by('date')
                )

                for item in initial_inventory:
                    key = 'all'
                    if has_breakdown:
                        if panel.breakdown == 'inventory_id':
                            key = item['inventory__id']

                        elif panel.breakdown == 'item_id':
                            key = item['inventory__item__id']

                        elif panel.breakdown == 'warehouse_id':
                            key = item['inventory__warehouse__id']

                    if key not in init_data:
                        init_data[key] = 0

                    if item['transaction_type'] in ['in', 'adjust']:
                        init_data[key] += item['count']
                    elif item['transaction_type'] == 'out':
                        init_data[key] -= item['count']

                # Remove if there is no inventory_left
                init_data = {k: v for k, v in init_data.items() if v != 0}

                # Iterate through all keys in init_data and inventory_data
                for key in set(init_data) | set(inventory_data):
                    data = {item['display']: 0 for item in data_points}
                    if key in inventory_data:
                        for item in inventory_data[key]:
                            data[item] = inventory_data[key][item]['in'] + \
                                inventory_data[key][item]['adjust'] - \
                                inventory_data[key][item]['out']

                    # Use cumulative count from init_data if available
                    cumulative_count = init_data.get(key, 0)

                    # Update data with cumulative count
                    for idx, _ in data.items():
                        data[idx] += cumulative_count
                        cumulative_count = data[idx]

                    label = '在庫' if lang == 'ja' else 'Inventory'
                    if panel.breakdown == 'inventory_id':
                        obj = ShopTurboInventory.objects.get(id=key)
                        label = get_object_display_based_columns(
                            TYPE_OBJECT_INVENTORY, obj, col_display, workspace.timezone, lang)
                    elif panel.breakdown == 'item_id':
                        obj = ShopTurboItems.objects.get(id=key)
                        label = get_object_display_based_columns(
                            TYPE_OBJECT_ITEM, obj, col_display, workspace.timezone, lang)
                    elif panel.breakdown == 'warehouse_id':
                        if key:
                            obj = InventoryWarehouse.objects.get(id=key)
                            label = obj.location
                        else:
                            label = "関連付けられていないロケーション" if lang == 'ja' else "No Associated Location"

                    results.append(new_result(
                        label, [value for value in data.values()], len(results)))

            elif metric_name == 'number_estimates':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                est_start_time_str = start_time.strftime('%Y-%m-%d')
                est_stop_time_str = stop_time.strftime('%Y-%m-%d')
                condition = Q(
                    workspace=workspace, start_date__gte=est_start_time_str, start_date__lte=est_stop_time_str)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 770: Filter for {metric_name}: {condition}')

                breakdown_conditions = [
                    'contact', 'company'] if panel.breakdown == 'customers' else [None]
                for breakdown in breakdown_conditions:
                    breakdown_label = f"{label} - {breakdown.capitalize()}" if breakdown else label

                    if breakdown == 'contact':
                        spesific_condition = condition & Q(
                            contact__isnull=False)
                    elif breakdown == 'company':
                        spesific_condition = condition & Q(
                            company__isnull=False)
                    else:
                        spesific_condition = condition

                    queryset = (
                        Estimate.objects
                        .filter(spesific_condition)
                        .annotate(date=trunc_func('start_date'))
                        .values('date')
                        .annotate(count=Coalesce(Count('id'), 0))
                        .order_by('date')
                    )
                    print(
                        f'=== report.py - 848: Query result for {metric_name} - {queryset}')

                    data = {item['display']: 0 for item in data_points}
                    for item in queryset:
                        data[item['date'].strftime(
                            time_format)] = item['count']

                    # Metric type[1][0] is for shows the cumulative, using it to avoid using hard coded
                    if metric_type == METRIC_TYPE[1][0]:
                        all_filter = rule_filter
                        if metric_filter:
                            all_filter &= filter_condition

                        cumulative_count = Estimate.objects.filter(
                            Q(order_at__lt=est_start_time_str) & all_filter).count()
                        for idx, _ in data.items():
                            data[idx] += cumulative_count
                            cumulative_count = data[idx]

                    results.append(new_result(breakdown_label, [
                                   value for value in data.values()],  len(results)))
                    
            elif metric_name == 'revenue_estimates':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                est_start_time_str = start_time.strftime('%Y-%m-%d')
                est_stop_time_str = stop_time.strftime('%Y-%m-%d')
                condition = Q(
                    workspace=workspace, start_date__gte=est_start_time_str, start_date__lte=est_stop_time_str)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 770: Filter for {metric_name}: {condition}')

                breakdown_conditions = [
                    'contact', 'company'] if panel.breakdown == 'customers' else [None]
                for breakdown in breakdown_conditions:
                    breakdown_label = f"{label} - {breakdown.capitalize()}" if breakdown else label

                    if breakdown == 'contact':
                        spesific_condition = condition & Q(
                            contact__isnull=False)
                    elif breakdown == 'company':
                        spesific_condition = condition & Q(
                            company__isnull=False)
                    else:
                        spesific_condition = condition

                    queryset = (
                        Estimate.objects
                        .filter(spesific_condition)
                        .annotate(date=trunc_func('start_date'))
                        .values('date')
                        .annotate(revenue=Sum('total_price'),)
                        .order_by('date')
                    )
                    print(
                        f'=== report.py - 848: Query result for {metric_name} - {queryset}')

                    data = {item['display']: 0 for item in data_points}
                    for item in queryset:
                        data[item['date'].strftime(
                            time_format)] = item['revenue'] if item['revenue'] else 0

                    results.append(new_result(breakdown_label, [
                                   value for value in data.values()],  len(results)))
        
            elif metric_name == 'number_invoices':
                label = PANEL_METRIC_TITLE[metric_name]['ja'] if lang == 'ja' else PANEL_METRIC_TITLE[metric_name]['en']

                est_start_time_str = start_time.strftime('%Y-%m-%d')
                est_stop_time_str = stop_time.strftime('%Y-%m-%d')
                condition = Q(
                    workspace=workspace, start_date__gte=est_start_time_str, start_date__lte=est_stop_time_str)
                condition &= rule_filter
                if metric_filter:
                    filter_condition = build_filters(metric_filter['filters'])
                    condition &= filter_condition
                    print(
                        f'=== report.py - 770: Filter for {metric_name}: {condition}')

                breakdown_conditions = [
                    'contact', 'company'] if panel.breakdown == 'customers' else [None]
                for breakdown in breakdown_conditions:
                    breakdown_label = f"{label} - {breakdown.capitalize()}" if breakdown else label

                    if breakdown == 'contact':
                        spesific_condition = condition & Q(
                            contact__isnull=False)
                    elif breakdown == 'company':
                        spesific_condition = condition & Q(
                            company__isnull=False)
                    else:
                        spesific_condition = condition

                    queryset = (
                        Invoice.objects
                        .filter(spesific_condition)
                        .annotate(date=trunc_func('start_date'))
                        .values('date')
                        .annotate(count=Coalesce(Count('id'), 0))
                        .order_by('date')
                    )
                    print(
                        f'=== report.py - 848: Query result for {metric_name} - {queryset}')

                    data = {item['display']: 0 for item in data_points}
                    for item in queryset:
                        data[item['date'].strftime(
                            time_format)] = item['count']

                    # Metric type[1][0] is for shows the cumulative, using it to avoid using hard coded
                    if metric_type == METRIC_TYPE[1][0]:
                        all_filter = rule_filter
                        if metric_filter:
                            all_filter &= filter_condition

                        cumulative_count = Invoice.objects.filter(
                            Q(start_date__lt=est_start_time_str) & all_filter).count()
                        for idx, _ in data.items():
                            data[idx] += cumulative_count
                            cumulative_count = data[idx]

                    results.append(new_result(breakdown_label, [
                                   value for value in data.values()],  len(results)))
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(
                f'ERROR === report.py - 1491: Error in Query result for {metric_name} - {e}')

        # print(result)

    # If FORECAST
    if panel.panel_type == 'forecast' or panel.is_forecast:
        forecast = Forecast()
        forecast_results = []
        for existing_result in results:
            forecast_data_points = get_time_range(stop_time + timezone.timedelta(
                days=1), stop_time + timezone.timedelta(days=len(data_points)), time_format, time_step)
            print('Forecast for: ', existing_result['label'])
            print(existing_result['data'])
            if len(existing_result['data']) < 4:
                continue
            forecast.load_and_train(existing_result['data'])
            predictions = forecast.forecast(len(forecast_data_points))
            predictions = predictions.astype(int).tolist()
            data = [0] * len(data_points)
            print(data, predictions)

            for point in forecast_data_points:
                x_axis.append(point['display'])
                existing_result['data'].append(0)

            forecast_results.append(new_result(
                ("予測: " if lang == 'ja' else "Forecast: ") +
                existing_result['label'],
                list(data + predictions),
                len(results),
            ))
        results = results + forecast_results

    print(f'=== report.py - 681: Data Source: {panel.data_source}')
    print(f'x-axis: {x_axis}')
    print(f'y-axis: {results}')
    chart = {
        'id': panel.id,
        'name': panel.name,
        'label': label,
        'x': x_axis,
        'y': results,
        'config': chart_config,
        'group_by': group_by,
        'net_result': net_result,
    }
    return chart