import uuid

from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone

from data.constants.constant import *
from data.models import ObjectManager
from data.models.base import save_tags_values, validate_value_custom_field
from data.models.constant import *
from data.models.order import LINE_ITEMS_CF_TYPE
from data.models.user import UserManagement
from sanka.storage_backends import PrivateMediaStorage
from utils.encryption import Encryption
from utils.bgjobs.runner import trigger_bg_job

EXPENSE_STATUS = [
    ("draft", "Draft"),
    ("submitted", "Submitted"),
    ("approved", "Approved"),
    ("reimbursed", "Reimbursed"),
]

PURCHASE_ORDER_STATUS = [
    ("draft", "Draft"),
    ("sent", "Sent"),
    ("internal_approved", "Internal Approved"),
    ("supplier_approved", "Approved by Supplier"),
    ("in_stock", "In-Stock"),
    ("paid", "Paid"),
]

CURRENCY = [
    ("jpy", "¥"),
    ("usd", "$"),
]

# Invoices


class EncryptedField(models.CharField):
    description = "Encrypted string value"

    def __init__(self, *args, **kwargs):
        kwargs["max_length"] = 255
        super().__init__(*args, **kwargs)

        self.encryption = Encryption()

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        del kwargs["max_length"]
        return name, path, args, kwargs

    def get_prep_value(self, value):
        if value is None:
            return value
        return self.encryption.encrypt(value)

    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return self.encryption.decrypt(value)

    def to_python(self, value):
        if value is None:
            return value
        return self.encryption.decrypt(value)


class Expense(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_pm = models.IntegerField(
        null=True, blank=True
    )  # id_pm means id of payment, should be expense (id_ex) but i think its not a big deal
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.SET_NULL, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, null=True, blank=True
    )
    submitter = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="submitted_by",
    )
    description = models.TextField(null=True, blank=True)
    reimburse_date = models.DateTimeField(null=True, blank=True)
    due_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=20, choices=EXPENSE_STATUS, null=True, blank=True
    )
    currency = models.CharField(max_length=20, choices=CURRENCY, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    # (Value) Amount of expense in base currency
    base_currency = models.FloatField(null=True, blank=True)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    owner = models.ForeignKey(
        UserManagement, on_delete=models.SET_NULL, null=True, blank=True
    )
    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        id_field = "id_pm"
        if not getattr(self, id_field):
            # Generate a new order_id if it doesn't exist
            last_item = (
                self.__class__.objects.filter(
                    workspace=self.workspace, id_pm__isnull=False
                )
                .order_by(f"-{id_field}")
                .first()
            )
            if last_item:
                if getattr(last_item, id_field):
                    try:
                        last_id = int(getattr(last_item, id_field))
                    except:
                        last_id = 0

                    next_id = last_id + 1
                else:
                    next_id = 1
            else:
                # If it's the first order, start with 1
                next_id = 1

            # Format the order_id as a three-digit string
            setattr(self, id_field, f"{next_id:04d}")

        super().save(*args, **kwargs)


class ExpenseFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=256, null=True, blank=True)
    expense = models.ForeignKey(
        Expense, on_delete=models.CASCADE, null=True, blank=True
    )
    file = models.FileField(
        verbose_name="Expense File",
        upload_to="expense-files",
        storage=PrivateMediaStorage(),
    )
    created_at = models.DateTimeField(default=timezone.now)


class ExpenseNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)


class ExpenseValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ExpenseNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    expense = models.ForeignKey(
        Expense,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="expense_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="billings-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


COMMERCE_STATUS = [
    ("draft", "Draft"),
    ("approved", "Approved"),
    ("received", "Received"),
    ("paid", "Paid"),
]

BILL_TYPE = [
    ("manual_bill", "Manual Bill"),
    ("item_bill", "Item Bill"),
]


BILLING_TYPE = [
    ("manual", "Manual"),
    ("item", "Item"),
]


class Bill(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_bill = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.SET_NULL, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, null=True, blank=True
    )
    description = models.TextField(null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    currency = models.CharField(
        max_length=20, choices=GENERAL_CURRENCY, null=True, blank=True
    )
    number_item = models.IntegerField(null=True, blank=True)
    item_price_bill = models.FloatField(null=True, blank=True)
    status = models.CharField(
        max_length=20, choices=COMMERCE_STATUS, null=True, blank=True
    )
    due_date = models.DateField(null=True, blank=True)
    issued_date = models.DateField(null=True, blank=True)
    file = models.FileField(
        verbose_name="Billing File",
        upload_to="billing-files",
        storage=PrivateMediaStorage(),
    )
    updated_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    notified = models.BooleanField(default=False, blank=True)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    bill_type = models.CharField(
        choices=BILL_TYPE, max_length=30, null=True, blank=True, default="item_bill"
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    journal_entry = models.ManyToManyField("JournalEntry", blank=True)
    owner = models.ForeignKey(
        UserManagement,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="owned_bills",
        # db_index=True,  # not required: ForeignKey fields are indexed by default
    )

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        id_field = "id_bill"
        if not getattr(self, id_field):
            # Generate a new order_id if it doesn't exist
            last_item = (
                self.__class__.objects.filter(
                    workspace=self.workspace, id_bill__isnull=False
                )
                .order_by(f"-{id_field}")
                .first()
            )
            if last_item:
                if getattr(last_item, id_field):
                    try:
                        last_id = int(getattr(last_item, id_field))
                    except:
                        last_id = 0

                    next_id = last_id + 1
                else:
                    next_id = 1
            else:
                # If it's the first order, start with 1
                next_id = 1

            # Format the order_id as a three-digit string
            setattr(self, id_field, f"{next_id:04d}")

        super().save(*args, **kwargs)


class ShopTurboItemsBills(models.Model):
    # main foreng key to Bill
    bill = models.ForeignKey(Bill, on_delete=models.CASCADE, null=True, blank=True)

    # item foreign key
    item = models.ForeignKey(
        "ShopTurboItems",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_item_bills",
    )
    item_variant = models.ForeignKey(
        "ShopTurboItemsVariations", on_delete=models.CASCADE, null=True, blank=True
    )
    item_price = models.ForeignKey(
        "ShopTurboItemsPrice", on_delete=models.CASCADE, null=True, blank=True
    )
    customer_item_price = models.ForeignKey(
        "ShopTurboCustomerItemsPrice", on_delete=models.CASCADE, null=True, blank=True
    )

    # custom item
    custom_item_name = models.CharField(max_length=500, null=True, blank=True)

    number_item = models.FloatField(null=True, blank=True)
    item_price_bill = models.FloatField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    order_view = models.IntegerField(null=True, blank=True)

    item_status = models.CharField(max_length=500, null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)

    def __str__(self):
        if self.item:
            if self.item.name:
                return f"{self.item.name}"
            else:
                return f"{self.item.item_id}"
        else:
            return f"{self.id}"


class BillNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class BillValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        BillNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    bill = models.ForeignKey(
        Bill,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="bill_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="billings-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class PurchaseItems(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    id_item = models.IntegerField(null=True, blank=True)
    name = models.CharField(max_length=256, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    currency = models.CharField(
        max_length=20, choices=GENERAL_CURRENCY, null=True, blank=True
    )
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    file = models.FileField(
        verbose_name="Purchase Item File",
        upload_to="purchase-files",
        storage=PrivateMediaStorage(),
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.SET_NULL, null=True, blank=True
    )
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        id_field = "id_item"
        if not getattr(self, id_field):
            # Generate a new order_id if it doesn't exist
            last_item = (
                self.__class__.objects.filter(
                    workspace=self.workspace, id_item__isnull=False
                )
                .order_by(f"-{id_field}")
                .first()
            )
            if last_item:
                if getattr(last_item, id_field):
                    try:
                        last_id = int(getattr(last_item, id_field))
                    except:
                        last_id = 0

                    next_id = last_id + 1
                else:
                    next_id = 1
            else:
                # If it's the first order, start with 1
                next_id = 1

            # Format the order_id as a three-digit string
            setattr(self, id_field, f"{next_id:04d}")

        super().save(*args, **kwargs)


PO_TAX_OPTION = [
    ("unified_tax", "Unified Tax"),
    ("item_based_tax", "Item Based Tax"),
    ("non_tax", "No Tax"),
]


class PurchaseOrders(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    id_po = models.IntegerField(null=True, blank=True)
    currency = models.CharField(
        max_length=20, choices=GENERAL_CURRENCY, null=True, blank=True
    )
    date = models.DateField(null=True, blank=True)
    status = models.CharField(
        max_length=20, choices=PURCHASE_ORDER_STATUS, null=True, blank=True
    )
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    file = models.FileField(
        verbose_name="Purchase Order File",
        upload_to="purchase-files",
        storage=PrivateMediaStorage(),
    )
    created_at = models.DateTimeField(default=timezone.now)
    contact = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.SET_NULL, null=True, blank=True
    )
    items = models.ForeignKey(
        "PurchaseItems", on_delete=models.SET_NULL, null=True, blank=True
    )
    tax_option = models.CharField(
        max_length=20,
        choices=PO_TAX_OPTION,
        null=True,
        blank=True,
        default="unified_tax",
    )
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    send_from = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    # Association
    inventory_transactions = models.ManyToManyField("InventoryTransaction", blank=True)

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        id_field = "id_po"
        if not getattr(self, id_field):
            # Generate a new order_id if it doesn't exist
            last_item = (
                self.__class__.objects.filter(
                    workspace=self.workspace, id_po__isnull=False
                )
                .order_by(f"-{id_field}")
                .first()
            )
            if last_item:
                if getattr(last_item, id_field):
                    try:
                        last_id = int(getattr(last_item, id_field))
                    except:
                        last_id = 0

                    next_id = last_id + 1
                else:
                    next_id = 1
            else:
                # If it's the first order, start with 1
                next_id = 1

            # Format the order_id as a three-digit string
            setattr(self, id_field, f"{next_id:04d}")

        super().save(*args, **kwargs)


class PurchaseOrdersItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    purchase_order = models.ForeignKey(
        PurchaseOrders,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="purchase_order_object",
    )
    item = models.ForeignKey(
        "ShopTurboItems",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="item_objects",
    )
    item_name = models.CharField(max_length=1024, null=True, blank=True)
    item_status = models.CharField(max_length=1024, null=True, blank=True)

    item_price = models.ForeignKey(
        "ItemPurchasePrice", on_delete=models.SET_NULL, null=True, blank=True
    )
    amount_price = models.FloatField(null=True, blank=True)
    amount_item = models.FloatField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if isinstance(self.amount_price, str):
            # Remove currency symbols and commas before attempting to convert to float
            cleaned_price = (
                self.amount_price.replace("¥", "").replace("$", "").replace(",", "")
            )  # Add other currency symbols if needed
            try:
                self.amount_price = float(cleaned_price)
            except ValueError:
                # Optionally, add more robust error handling here, like logging or raising a specific exception
                # For now, we'll let the original ValueError propagate if cleaning doesn't fully resolve it
                # or if the string is not a valid number after cleaning (e.g. "abc")
                pass

        # Original save logic for logging (if any) and calling super().save()
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        super().save(*args, **kwargs)


class PurchaseOrdersNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class PurchaseOrdersValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        PurchaseOrdersNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    purchaseorders = models.ForeignKey(
        PurchaseOrders,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="purchase_order_field_relations",
    )
    value = models.TextField(null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="billings-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class PurchaseOrdersItemNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=LINE_ITEMS_CF_TYPE, max_length=20, null=True, blank=True
    )
    choice_value = models.TextField(null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class PurchaseOrdersItemValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    field_name = models.ForeignKey(
        PurchaseOrdersItemNameCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    purchaseordersitem = models.ForeignKey(
        PurchaseOrdersItem, on_delete=models.CASCADE, null=True, blank=True
    )
    value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class PurchaseOrdersValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Purchase Order Custom Field file",
        upload_to="purchase-order-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        PurchaseOrdersValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class RecordedEvent(models.Model):
    event_type = models.CharField(max_length=255)
    event_data = models.JSONField()
    timestamp = models.DateTimeField(auto_now_add=True)
    created_at = models.DateTimeField(default=timezone.now)


FREQ_CHOICE = {("one_time", "One Time"), ("recurring", "Recurring")}


class Invoice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_inv = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.CASCADE, null=True, blank=True
    )
    subscription = models.ForeignKey(
        "ShopTurboSubscriptions", on_delete=models.CASCADE, null=True, blank=True
    )
    start_date = models.DateField(null=True, blank=True)
    due_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=20, choices=COMMERCE_STATUS, null=True, blank=True
    )
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    currency = models.CharField(
        max_length=20, choices=GENERAL_CURRENCY, null=True, blank=True
    )
    notes = models.TextField(null=True, blank=True)
    send_from = models.TextField(blank=True, null=True)
    tax_rate = models.FloatField(null=True, blank=True)
    tax_list = models.CharField(max_length=512, null=True, blank=True)
    tax_inclusive = models.BooleanField(default=False)
    is_stamp = models.BooleanField(default=False)
    discount = models.CharField(max_length=512, null=False, blank=True)
    discount_option = models.CharField(max_length=512, null=False, blank=True)
    discount_tax_option = models.CharField(max_length=512, null=False, blank=True)
    tax_option = models.CharField(
        max_length=20,
        choices=BILLS_TAX_OPTION,
        null=True,
        blank=True,
        default="unified_tax",
    )
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)

    orders = models.ManyToManyField(
        "ShopTurboOrders", blank=True, related_name="order_invoice"
    )
    receipts = models.ManyToManyField(
        "Receipt", blank=True, related_name="invoice_receipts"
    )

    shipping_cost = models.ForeignKey(
        "ShopTurboShippingCost", on_delete=models.SET_NULL, null=True, blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if not self.id_inv:
            # Generate a new order_id if it doesn't exist
            last_ = (
                Invoice.objects.filter(workspace=self.workspace, id_inv__isnull=False)
                .order_by("id_inv")
                .last()
            )
            if last_:
                if last_.id_inv:
                    try:
                        last_ = int(last_.id_inv)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.id_inv = f"{next_id:04d}"

        super().save(*args, **kwargs)
        try:
            if self.id:
                param = {
                    "function": "trigger_workflow_when_record_updated",
                    "job_id": str(uuid.uuid4()),
                    "workspace_id": str(self.workspace.id),
                    "user_id": None,
                    "args": [
                        f"--obj_id={self.id}",
                        f"--obj_type={self.__class__.__name__}",
                    ],
                }
                trigger_bg_job(param)
        except:
            pass


class InvoicePlatform(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="invoice_platforms",
    )
    integration = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    platform_invoice_id = models.CharField(
        max_length=1024, null=True, blank=True
    )  # record ID in platform
    platform_invoice_url = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class InvoiceItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="invoice_object",
    )
    item_name = models.CharField(max_length=1024, null=True, blank=True)
    item_link = models.ForeignKey(
        "ShopTurboItems", on_delete=models.CASCADE, null=True, blank=True
    )
    amount_price = models.FloatField(null=True, blank=True)
    amount_item = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)


# Create models instead adding new field to InvoiceItem, so we can track when the data is updated/synced
class InvoiceItemPlatform(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    invoice_item = models.ForeignKey(
        InvoiceItem,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="invoice_item_platforms",
    )
    integration = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    platform_invoice_item_id = models.CharField(
        max_length=1024, null=True, blank=True
    )  # record ID in platform
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class InvoiceNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    order = models.IntegerField(null=True, default=0, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class InvoiceValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        InvoiceNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="invoice_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="billings-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class InvoiceItemsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=LINE_ITEMS_CF_TYPE, max_length=20, null=True, blank=True
    )
    choice_value = models.TextField(null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class InvoiceItemsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    field_name = models.ForeignKey(
        InvoiceItemsNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    item_order = models.ForeignKey(
        InvoiceItem, on_delete=models.CASCADE, null=True, blank=True
    )
    value = models.TextField(null=True, blank=True)
    value_number_format = models.CharField(max_length=20, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class InvoiceFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    invoice = models.ForeignKey(
        Invoice, on_delete=models.CASCADE, null=True, blank=True
    )
    file = models.FileField(verbose_name="PDF Invoice File", upload_to="Invoice-files")
    created_at = models.DateTimeField(default=timezone.now)


class InvoicePlatforms(models.Model):
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="contact_platform",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform = models.CharField(choices=PLATFORM, max_length=100, null=True, blank=True)
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class Estimate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_est = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    start_date = models.DateField(null=True, blank=True)
    due_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=20, choices=ESTIMATE_STATUS, null=True, blank=True
    )
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    currency = models.CharField(
        max_length=20, choices=GENERAL_CURRENCY, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.CASCADE, null=True, blank=True
    )
    notes = models.TextField(null=True, blank=True)
    send_from = models.TextField(blank=True, null=True)
    tax_rate = models.FloatField(null=True, blank=True)
    tax_list = models.CharField(max_length=256, null=True, blank=True)
    is_stamp = models.BooleanField(default=False)
    tax_inclusive = models.BooleanField(default=False)
    custom_price = models.FloatField(null=True, blank=True)
    discount = models.CharField(max_length=512, null=False, blank=True)
    discount_option = models.CharField(max_length=512, null=False, blank=True)
    cost_option = models.CharField(max_length=512, null=True, blank=True)
    discount_tax_option = models.CharField(max_length=512, null=False, blank=True)
    tax_option = models.CharField(
        max_length=20,
        choices=BILLS_TAX_OPTION,
        null=True,
        blank=True,
        default="unified_tax",
    )
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)

    # Association
    invoice = models.ManyToManyField(Invoice, blank=True)

    shipping_cost = models.ForeignKey(
        "ShopTurboShippingCost",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    # Versioning fields
    version = models.PositiveIntegerField(default=1)
    previous_version = models.ForeignKey(
        "self", on_delete=models.SET_NULL, null=True, blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if not self.id_est:
            # Generate a new order_id if it doesn't exist
            last_ = (
                Estimate.objects.filter(workspace=self.workspace, id_est__isnull=False)
                .order_by("id_est")
                .last()
            )
            if last_:
                if last_.id_est:
                    try:
                        last_ = int(last_.id_est)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.id_est = f"{next_id:04d}"

        super().save(*args, **kwargs)

    def get_complete_version_chain(self):
        """Get ALL versions (past and future) starting from any version"""
        visited = set()
        root = self.get_root_version()
        all_versions = []
        current = root

        while current and current.id not in visited:
            visited.add(current.id)
            all_versions.append(current)
            current = self.__class__.objects.filter(previous_version=current).first()
        all_versions = list(reversed(all_versions))
        return all_versions

    def get_latest_version(self):
        """Get the current/latest version of this product"""
        visited = set()
        root = self.get_root_version()
        current = root

        while current and current.id not in visited:
            visited.add(current.id)
            next_version = self.__class__.objects.filter(
                previous_version=current
            ).first()
            if not next_version:
                break
            current = next_version

        return current

    def get_root_version(self):
        """Get the first version of this product"""
        current = self
        while current.previous_version:
            current = current.previous_version
        return current

    def is_latest(self):
        """Check if this is the latest version"""
        # Check if there's any product that points to this as previous_version
        return not Estimate.objects.filter(previous_version=self).exists()


class EstimateFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    estimate = models.ForeignKey(
        Estimate, on_delete=models.CASCADE, null=True, blank=True
    )
    file = models.FileField(
        verbose_name="PDF Estimate File", upload_to="Estimate-files"
    )
    created_at = models.DateTimeField(default=timezone.now)


class EstimateItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    estimate = models.ForeignKey(
        Estimate,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="estimate_object",
    )
    item_name = models.CharField(max_length=1024, null=True, blank=True)
    item_link = models.ForeignKey(
        "ShopTurboItems", on_delete=models.CASCADE, null=True, blank=True
    )
    amount_price = models.FloatField(null=True, blank=True)
    amount_item = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)


class EstimatePurchaseItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    estimate = models.ForeignKey(
        Estimate, on_delete=models.CASCADE, null=True, blank=True
    )
    item_name = models.CharField(max_length=1024, null=True, blank=True)
    item_link = models.ForeignKey(
        "ShopTurboItems", on_delete=models.CASCADE, null=True, blank=True
    )
    amount_price = models.FloatField(null=True, blank=True)
    amount_item = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)


class EstimateNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class EstimateValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        EstimateNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    estimate = models.ForeignKey(
        Estimate,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="estimate_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="billings-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class EstimateItemsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=LINE_ITEMS_CF_TYPE, max_length=20, null=True, blank=True
    )
    choice_value = models.TextField(null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class EstimateItemsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    field_name = models.ForeignKey(
        EstimateItemsNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    item_order = models.ForeignKey(
        EstimateItem, on_delete=models.CASCADE, null=True, blank=True
    )
    value = models.TextField(null=True, blank=True)
    value_number_format = models.CharField(max_length=20, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class EstimatePlatforms(models.Model):
    estimate = models.ForeignKey(
        Estimate,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="estimate_platform",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform = models.CharField(choices=PLATFORM, max_length=100, null=True, blank=True)
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


# New Slips Model inside Commerce
DEFAULT_SLIPS_TYPE = [
    ("sales_slips", "Sales Slips"),
    ("withdraw_slips", "Withdraw Slips"),
    ("deposit_slip", "Deposit Slip"),
    ("transfer_slip", "Transfer Slip"),
    ("delivery_slips", "Delivery Slips"),
]


class Slip(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_slip = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    start_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=20, choices=COMMERCE_STATUS, null=True, blank=True
    )
    slip_type = models.CharField(
        choices=DEFAULT_SLIPS_TYPE, max_length=30, null=True, blank=True
    )
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    currency = models.CharField(
        max_length=20, choices=GENERAL_CURRENCY, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.CASCADE, null=True, blank=True
    )
    notes = models.TextField(null=True, blank=True)
    send_from = models.TextField(blank=True, null=True)
    tax_rate = models.FloatField(null=True, blank=True)
    tax_list = models.CharField(max_length=256, null=True, blank=True)
    is_stamp = models.BooleanField(default=False)
    tax_option = models.CharField(
        max_length=20, choices=BILLS_TAX_OPTION, null=True, blank=True
    )
    tax_inclusive = models.BooleanField(default=False)
    custom_price = models.FloatField(null=True, blank=True)
    discount = models.CharField(max_length=512, null=False, blank=True)
    discount_option = models.CharField(max_length=512, null=False, blank=True)
    discount_tax_option = models.CharField(max_length=512, null=False, blank=True)
    tax_option = models.CharField(
        max_length=20,
        choices=BILLS_TAX_OPTION,
        null=True,
        blank=True,
        default="unified_tax",
    )
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    journal_entry = models.ManyToManyField("JournalEntry", blank=True)

    shipping_cost = models.ForeignKey(
        "ShopTurboShippingCost",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if not self.id_slip:
            # Generate a new order_id if it doesn't exist
            last_ = (
                Slip.objects.filter(workspace=self.workspace, id_slip__isnull=False)
                .order_by("id_slip")
                .last()
            )
            if last_:
                if last_.id_slip:
                    try:
                        last_ = int(last_.id_slip)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.id_slip = f"{next_id:04d}"

        super().save(*args, **kwargs)


class SlipItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slip = models.ForeignKey(
        Slip,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="slip_object",
    )
    item_name = models.CharField(max_length=1024, null=True, blank=True)
    item_link = models.ForeignKey(
        "ShopTurboItems", on_delete=models.CASCADE, null=True, blank=True
    )
    amount_price = models.FloatField(null=True, blank=True)
    amount_item = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)


class SlipNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class SlipValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        SlipNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    slip = models.ForeignKey(
        Slip,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="slip_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="billings-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = [["field_name", "slip"]]

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class DeliverySlip(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_ds = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.CASCADE, null=True, blank=True
    )
    start_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=20, choices=COMMERCE_STATUS, null=True, blank=True
    )
    currency = models.CharField(
        max_length=20, choices=GENERAL_CURRENCY, null=True, blank=True
    )
    lang = models.CharField(max_length=50, null=False, blank=True)
    notes = models.TextField(null=True, blank=True)
    send_from = models.TextField(blank=True, null=True)
    tax_rate = models.FloatField(null=True, blank=True)
    is_stamp = models.BooleanField(default=False)
    tax_inclusive = models.BooleanField(default=False)
    custom_price = models.FloatField(null=True, blank=True)
    discount = models.CharField(max_length=512, null=False, blank=True)
    discount_option = models.CharField(max_length=512, null=False, blank=True)
    discount_tax_option = models.CharField(max_length=512, null=False, blank=True)
    tax_option = models.CharField(
        max_length=20,
        choices=BILLS_TAX_OPTION,
        null=True,
        blank=True,
        default="unified_tax",
    )
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )

    invoices = models.ManyToManyField(
        "Invoice", blank=True, related_name="delivery_note_invoices"
    )

    shipping_cost = models.ForeignKey(
        "ShopTurboShippingCost",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        id_field = "id_ds"
        if not getattr(self, id_field):
            # Generate a new order_id if it doesn't exist
            last_item = (
                self.__class__.objects.filter(
                    # ngu vl
                    workspace=self.workspace,
                    id_ds__isnull=False,
                )
                .order_by(f"-{id_field}")
                .first()
            )
            if last_item:
                if getattr(last_item, id_field):
                    try:
                        last_id = int(getattr(last_item, id_field))
                    except:
                        last_id = 0

                    next_id = last_id + 1
                else:
                    next_id = 1
            else:
                # If it's the first order, start with 1
                next_id = 1

            # Format the order_id as a three-digit string
            setattr(self, id_field, f"{next_id:04d}")

        super().save(*args, **kwargs)


class DeliverySlipFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    deliveryslip = models.ForeignKey(
        DeliverySlip, on_delete=models.CASCADE, null=True, blank=True
    )
    file = models.FileField(
        verbose_name="PDF Delivery Slip File", upload_to="Delivery-Slip-files"
    )
    created_at = models.DateTimeField(default=timezone.now)


class DeliverySlipNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class DeliverySlipValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        DeliverySlipNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    deliveryslip = models.ForeignKey(
        DeliverySlip,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="delivery_slip_custom_field_relations",
    )
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="billings-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    value = models.CharField(max_length=500, null=True, blank=True)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class DeliverySlipItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    deliveryslip = models.ForeignKey(
        DeliverySlip,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="deliveryslip_object",
    )
    item_name = models.CharField(max_length=1024, null=True, blank=True)
    item_link = models.ForeignKey(
        "ShopTurboItems", on_delete=models.CASCADE, null=True, blank=True
    )
    amount_price = models.FloatField(null=True, blank=True)
    amount_item = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)


class DeliverySlipItemsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=LINE_ITEMS_CF_TYPE, max_length=20, null=True, blank=True
    )
    choice_value = models.TextField(null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class DeliverySlipItemsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    field_name = models.ForeignKey(
        DeliverySlipItemsNameCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    item_order = models.ForeignKey(
        DeliverySlipItem, on_delete=models.CASCADE, null=True, blank=True
    )
    value = models.TextField(null=True, blank=True)
    value_number_format = models.CharField(max_length=20, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class DeliverySlipPlatforms(models.Model):
    deliveryslip = models.ForeignKey(
        DeliverySlip,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="contact_platform",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform = models.CharField(choices=PLATFORM, max_length=100, null=True, blank=True)
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class Receipt(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_rcp = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, null=True, blank=True
    )
    contact = models.ForeignKey(
        "Contact", on_delete=models.CASCADE, null=True, blank=True
    )
    start_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=20, choices=COMMERCE_STATUS, null=True, blank=True
    )
    currency = models.CharField(
        max_length=20, choices=GENERAL_CURRENCY, null=True, blank=True
    )
    notes = models.TextField(null=True, blank=True)
    send_from = models.TextField(blank=True, null=True)
    tax_rate = models.FloatField(null=True, blank=True)
    is_stamp = models.BooleanField(default=False)
    tax_inclusive = models.BooleanField(default=False)
    custom_price = models.FloatField(null=True, blank=True)
    discount = models.CharField(max_length=512, null=False, blank=True)
    discount_option = models.CharField(max_length=512, null=False, blank=True)
    discount_tax_option = models.CharField(max_length=512, null=False, blank=True)
    tax_option = models.CharField(
        max_length=20,
        choices=BILLS_TAX_OPTION,
        null=True,
        blank=True,
        default="unified_tax",
    )
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )

    entry_type = models.CharField(
        choices=BILLING_TYPE, max_length=30, null=True, blank=True, default="item"
    )
    manual_price = models.FloatField(null=True, blank=True)

    shipping_cost = models.ForeignKey(
        "ShopTurboShippingCost",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    invoices = models.ManyToManyField(
        "Invoice", blank=True, related_name="receipt_invoices"
    )

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        id_field = "id_rcp"
        if not getattr(self, id_field):
            # Generate a new order_id if it doesn't exist
            last_item = (
                self.__class__.objects.filter(
                    workspace=self.workspace, id_rcp__isnull=False
                )
                .order_by(f"-{id_field}")
                .first()
            )
            if last_item:
                if getattr(last_item, id_field):
                    try:
                        last_id = int(getattr(last_item, id_field))
                    except:
                        last_id = 0

                    next_id = last_id + 1
                else:
                    next_id = 1
            else:
                # If it's the first order, start with 1
                next_id = 1

            # Format the order_id as a three-digit string
            setattr(self, id_field, f"{next_id:04d}")

        super().save(*args, **kwargs)


class ReceiptFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    receipt = models.ForeignKey(
        Receipt, on_delete=models.CASCADE, null=True, blank=True
    )
    file = models.FileField(
        verbose_name="Receipt File",
        upload_to="Receipt-files",
        storage=PrivateMediaStorage(),
    )
    created_at = models.DateTimeField(default=timezone.now)


class ReceiptNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class ReceiptValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ReceiptNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    receipt = models.ForeignKey(
        Receipt,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="receipt_custom_field_relations",
    )
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="billings-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    value = models.CharField(max_length=500, null=True, blank=True)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class ReceiptItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    receipt = models.ForeignKey(
        Receipt,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="receipt_object",
    )
    item_name = models.CharField(max_length=1024, null=True, blank=True)
    item_link = models.ForeignKey(
        "ShopTurboItems", on_delete=models.CASCADE, null=True, blank=True
    )
    amount_price = models.FloatField(null=True, blank=True)
    amount_item = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    total_price_without_tax = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)


class ReceiptPlatforms(models.Model):
    receipt = models.ForeignKey(
        Receipt,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="contact_platform",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform = models.CharField(choices=PLATFORM, max_length=100, null=True, blank=True)
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class ReceiptItemsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=LINE_ITEMS_CF_TYPE, max_length=20, null=True, blank=True
    )
    choice_value = models.TextField(null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class ReceiptItemsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    field_name = models.ForeignKey(
        ReceiptItemsNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    item_order = models.ForeignKey(
        ReceiptItem, on_delete=models.CASCADE, null=True, blank=True
    )
    value = models.TextField(null=True, blank=True)
    value_number_format = models.CharField(max_length=20, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class InvoiceValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Billing Custom Field file", upload_to="billing-custom-field-files"
    )
    valuecustomfield = models.ForeignKey(
        InvoiceValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class EstimateValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Billing Custom Field file", upload_to="billing-custom-field-files"
    )
    valuecustomfield = models.ForeignKey(
        EstimateValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class ReceiptValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Billing Custom Field file", upload_to="billing-custom-field-files"
    )
    valuecustomfield = models.ForeignKey(
        ReceiptValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class DeliverySlipValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Billing Custom Field file", upload_to="billing-custom-field-files"
    )
    valuecustomfield = models.ForeignKey(
        DeliverySlipValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class SlipValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Billing Custom Field file", upload_to="billing-custom-field-files"
    )
    valuecustomfield = models.ForeignKey(
        SlipValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class ExpenseValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Spendpocket Custom Field file",
        upload_to="spendpocket-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        ExpenseValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class BillValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Spendpocket Custom Field file",
        upload_to="spendpocket-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        BillValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)
