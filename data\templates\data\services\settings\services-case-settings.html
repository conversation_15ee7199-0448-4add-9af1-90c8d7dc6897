
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

{% if setting_type == 'customer_case' %}
<div class="mb-10">
    <div id="change-stamp-section" class="mb-10">
        <form method="POST" action="{% host_url 'customerlink_settings' host 'app' %}" enctype="multipart/form-data" onkeydown="return event.key != 'Enter';">
            {% csrf_token %}
            <input type="hidden" class="" name="app_settings" value="manage_app"/>
            
            <div class="mb-10"
                hx-get="{% host_url 'properties' host 'app' %}"
                hx-vals='{"page_group_type": "customer_case"}'
                hx-trigger="load"
                hx-target="#properties-table"
            >
                <div id="properties-table"></div>
            </div>

            {% comment %} ASSOCIATION LABEL SETTINGS {% endcomment %}
            <div
                hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                hx-vals='{"page_group_type":"{{constant.TYPE_OBJECT_CASE}}" }'
                hx-trigger="load"
            ></div>

            {% include 'data/account/workspace/settings/workspace-object-manager.html' %}
        
            <button id="submit-customfield-deals-button" name="submit-customfield-deals-button" type="submit" class="btn btn-dark my-5">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </form> 

    </div>
</div>

<style>
    .stretch-select-custom span {
        height:100% !important;
    }
    .stretch-select-custom span span span span{
        {% comment %} display: flex !important; {% endcomment %}
        align-content: center;
        flex-wrap: wrap;
        margin-bottom: auto;
        margin-top: auto;
    }
</style>
<style>
    .loading-spinner{
        display:none;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>

<script>
    $(document).ready(function() {
        $('.select2-this').select2();
    });

    function delete_deals_custom_field_name(elm){
        elm.parentElement.parentElement.parentElement.parentElement.remove()
    }
</script>

{% endif %}