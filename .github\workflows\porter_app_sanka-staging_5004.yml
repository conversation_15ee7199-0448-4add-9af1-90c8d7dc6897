on:
  workflow_dispatch:
    
name: Deploy to <PERSON> sanka-staging
jobs:
    porter-deploy:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Set Github tag
              id: vars
              run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

            - name: Set up node 20
              uses: actions/setup-node@v4
              with:
                node-version: 20

            - name: Build Tailwind CSS
              run: npm install && npm run build

            - name: Remove Node Modules
              run: rm -rf node_modules

            - name: Setup Environment for San AI
              env:
                VITE_STG_ENV: |
                  ${{ secrets.VITE_STG_ENV }}
              run: |
                echo "$VITE_STG_ENV" > san-ai/.env.production && echo "$VITE_STG_ENV" > san-ai/.env

            - name: Build San AI
              run: |
                cd san-ai && npm install && npm run build

            - name: Remove Node Modules
              run: |
                cd san-ai && rm -rf node_modules

            - name: Setup porter
              uses: porter-dev/setup-porter@v0.1.0

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3

            - name: Set up Environment for Sanka
              env:
                STG_ENV: |
                  ${{ secrets.STG_ENV }}
              run: |
                echo "$STG_ENV" > .env

            - name: Log in to Azure Container Registry
              uses: docker/login-action@v3
              with:
                registry: 'p16475locjapaneastsubeff9663d33.azurecr.io'
                username: ${{ secrets.ACR_PORTER_USERNAME }}
                password: ${{ secrets.ACR_PORTER_PASSWORD }}

            - name: Build and push container image to registry
              uses: docker/build-push-action@v6
              with:
                context: .
                push: true
                platforms: linux/amd64
                tags: p16475locjapaneastsubeff9663d33.azurecr.io/sanka-staging:${{ github.sha }}
                file: ./docker/Dockerfile.porter.sanka
                cache-to: |
                  type=registry,ref=p16475locjapaneastsubeff9663d33.azurecr.io/sanka-staging
                cache-from: |
                  type=registry,ref=p16475locjapaneastsubeff9663d33.azurecr.io/sanka-staging
                build-args: |
                  RELEASE=${{ github.sha }}
                  CACHEBUST=$(date +"%s")
                outputs: |
                  type=image,name=p16475locjapaneastsubeff9663d33.azurecr.io/sanka-staging,oci-mediatypes=true,push=true

            - name: Authenticate Porter
              run: porter auth --token ${{ secrets.PORTER_APP_16475_4969 }}

            - name: Update Porter tag for sanka-staging
              run: |
                porter app update-tag sanka-staging --tag ${{ github.sha }}
              env:
                PORTER_APP_NAME: sanka-staging
                PORTER_CLUSTER: "5004"
                PORTER_HOST: https://dashboard.porter.run
                PORTER_PR_NUMBER: ${{ github.event.number }}
                PORTER_PROJECT: "16475"
                PORTER_REPO_NAME: ${{ github.event.repository.name }}
                PORTER_TAG: ${{ github.sha }}
                PORTER_TOKEN: ${{ secrets.PORTER_APP_16475_4969 }}
