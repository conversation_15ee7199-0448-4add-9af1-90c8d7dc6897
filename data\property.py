from io import StringIO
import json
import traceback
import uuid

import chardet
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import F, Q
from django.http.response import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import resolve
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse
import pandas as pd

from data.association import (
    get_custom_properties_associate,
    set_custom_properties_association,
)
from data.constants.constant import (
    CSV_DELIMITER_LIST_FIELD,
    CURRENCY_MODEL,
    DEFAULT_FORM_FIELD_COMMERCE,
    DEFAULT_JOURNAL_CATEGORY,
    DEFAULT_SLIPS_TYPE_DISPLAY,
    PRODUCTION_LINE_DEFAULT_DISPLAY,
    THREAD_STATUS_CHOICES,
    THREAD_STATUS_DISPLAY,
)
from data.constants.properties_constant import (
    DEFAULT_OBJECT_DISPLAY,
    LIST_COMMERCE_OBJECT_TYPES,
    OBJECT_SLUG_TO_OBJECT_TYPE,
    OBJECT_TYPE_TO_SLUG,
    OBJECT_TYPE_TO_URL_NAME,
    TYPE_OBJECTS,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_CONVERSATION,
    TYPE_OBJECT_CUSTOM_OBJECT,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_JOBS,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SESSION_EVENT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_TASK,
    TYPE_OBJECT_TIMEGENIE,
    TYPE_OBJECT_WORKER,
    TYPE_OBJECT_WORKER_ABSENCE,
    TYPE_OBJECT_WORKER_REVIEW,
)
from data.models import (
    AdvanceSearchFilter,
    AssociationLabel,
    BARCODE_TYPES,
    CASE_STATUS,
    CASE_STATUS_DISPLAY,
    COMMERCE_STATUS,
    COMMERCE_STATUS_DISPLAY,
    CompanyValueCustomField,
    ContactList,
    CustomObject,
    CustomProperty,
    DealsItemsNameCustomField,
    DealsNameCustomField,
    DefaultBarcodeProperty,
    DeliverySlipItemsNameCustomField,
    ESTIMATE_STATUS,
    ESTIMATE_STATUS_DISPLAY,
    EstimateItemsNameCustomField,
    EventNameCustomProperties,
    InvoiceItemsNameCustomField,
    JOB_STATUS,
    JOB_STATUS_DISPLAY,
    JOB_TYPE,
    JOB_TYPE_DISPLAY,
    JOURNAL_ACCOUNT,
    JOURNAL_ACCOUNT_DISPLAY,
    JOURNAL_TAX_CATEGORY,
    JOURNAL_TAX_CATEGORY_DISPLAY,
    Module,
    ORDERS_STATUS_DISPLAY,
    ObjectManager,
    PURCHASE_ORDER_STATUS,
    PURCHASE_ORDER_STATUS_DISPLAY,
    Projects,
    PropertySet,
    PurchaseOrdersItemNameCustomField,
    ReceiptItemsNameCustomField,
    SHOPTURBO_ORDER_DELIVERY_STATUS,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsOrdersNameCustomField,
    ShopTurboItemsValueCustomField,
    Slip,
    Task,
    TaskCustomFieldName,
    TaskCustomFieldValue,
    User,
    View,
    ViewFilter,
    WorkspacePropertySet,
    ast,
)
from data.models.order import LINE_ITEMS_CF_TYPE
from utils.decorator import login_or_hubspot_required
from utils.formula import calculate_math
from utils.logger import logger
from utils.properties.form_properties import (
    get_form_properties,
    get_form_properties_exclude_list,
)
from utils.properties.properties import (
    get_context_formula_properties,
    get_default_property_set,
    get_page_object,
    get_properties_with_details,
    property_display,
)
from utils.utility import get_attr, get_currency_symbol, get_workspace, is_valid_uuid


@login_or_hubspot_required
def properties(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    if not page_group_type:
        return HttpResponse(404)

    page_obj = get_page_object(page_group_type, lang)
    custom_model = page_obj["custom_model"]
    if page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
        setting_url = page_obj["setting_url"]
        custom_object_id = request.POST.get(
            "custom_object_id", request.GET.get("custom_object_id", "")
        )
        custom_object = CustomObject.objects.filter(id=custom_object_id).first()
        setting_type = custom_object.slug
    else:
        setting_url = page_obj["setting_url"]
        setting_type = page_obj["setting_type"]
        if "parent_object" in page_obj:
            if page_obj["parent_object"]:
                parent_page_obj = get_page_object(page_obj["parent_object"], lang)
                setting_url = parent_page_obj["setting_url"]
                setting_type = parent_page_obj["setting_type"]

    if request.method == "GET":
        p_id = request.GET.get("p_id", None)
        custom_object_id = request.GET.get("custom_object_id", None)
        search_q = request.GET.get("q", "").strip()  # Extract search query parameter

        # Pagination parameters
        try:
            page = int(request.GET.get("page", 1))
            per_page = int(
                request.GET.get("per_page", 50)
            )  # Default 50 properties per page
        except:
            page = 1
            per_page = 50

        # DEBUG: Log workspace and search information
        print(f"DEBUG PROPERTIES SEARCH: Workspace ID: {workspace.id}")
        print(f"DEBUG PROPERTIES SEARCH: Workspace name: {workspace.name}")
        print(f"DEBUG PROPERTIES SEARCH: Page group type: {page_group_type}")
        print(f"DEBUG PROPERTIES SEARCH: Search query: '{search_q}'")
        print(f"DEBUG PROPERTIES SEARCH: Project ID: {p_id}")
        print(f"DEBUG PROPERTIES SEARCH: Custom object ID: {custom_object_id}")
        print(f"DEBUG PROPERTIES SEARCH: Page: {page}, Per page: {per_page}")

        default_properties = []
        custom_properties = []
        custom_property_ids = []
        custom_object = None

        if not custom_object_id:
            properties_result = get_properties_with_details(
                page_group_type, workspace, lang, p_id
            )

            properties = properties_result
            print(
                f"DEBUG PROPERTIES SEARCH: Retrieved {len(properties)} properties from get_properties_with_details"
            )
        else:
            custom_object = CustomObject.objects.filter(
                id=custom_object_id, workspace=workspace
            ).first()
            if not custom_object:
                print(
                    f"DEBUG PROPERTIES SEARCH: Custom object not found: {custom_object_id}"
                )
                return HttpResponse(status=404)

            custom_fields = custom_model.objects.filter(
                custom_object=custom_object, workspace=workspace, name__isnull=False
            ).order_by("-created_at")

            print(
                f"DEBUG PROPERTIES SEARCH: Retrieved {custom_fields.count()} custom fields for custom object"
            )

            properties = []
            for ctf in custom_fields:
                if "edit_by_id" in ctf.__dict__:
                    ctf.__dict__["edit_by"] = User.objects.filter(
                        id=ctf.__dict__["edit_by_id"]
                    ).first()

                properties.append(ctf.__dict__)

        for prop in properties:
            if is_valid_uuid(prop["id"]):
                if (
                    str(prop["id"]) not in custom_property_ids
                    and "immutable" not in prop
                ):
                    if page_group_type in [
                        TYPE_OBJECT_ORDER,
                        TYPE_OBJECT_CASE,
                        TYPE_OBJECT_CONTACT,
                        TYPE_OBJECT_COMPANY,
                        TYPE_OBJECT_INVENTORY_TRANSACTION,
                        TYPE_OBJECT_INVENTORY,
                        TYPE_OBJECT_SUBSCRIPTION,
                        TYPE_OBJECT_ESTIMATE,
                        TYPE_OBJECT_INVOICE,
                        TYPE_OBJECT_RECEIPT,
                    ]:
                        if prop["type"] in page_obj["exclude_custom_types"]:
                            continue

                    custom_properties.append(prop)
                    custom_property_ids.append(prop["id"])
            else:
                default_properties.append(prop)

        print(
            f"DEBUG PROPERTIES SEARCH: Categorized properties - Default: {len(default_properties)}, Custom: {len(custom_properties)}"
        )
        print(
            f"DEBUG PROPERTIES SEARCH: Default properties: {[p.get('id', 'no-id') for p in default_properties]}"
        )
        print(
            f"DEBUG PROPERTIES SEARCH: Custom properties: {[p.get('id', 'no-id') for p in custom_properties]}"
        )
        print("SHOWING PROP: ", custom_properties)
        # Associate
        if page_group_type in [
            TYPE_OBJECT_CASE,
            TYPE_OBJECT_ORDER,
            TYPE_OBJECT_PURCHASE_ORDER,
            TYPE_OBJECT_ESTIMATE,
            TYPE_OBJECT_TASK,
        ]:
            associate = get_custom_properties_associate(
                workspace=workspace, associate_type=page_group_type, p_id=p_id
            )

        elif page_group_type == TYPE_OBJECT_SESSION_EVENT:
            associate = []
            default_properties = []

            event_properties = EventNameCustomProperties.objects.filter(
                workspace=workspace
            ).order_by("created_at")
            for prop in event_properties:
                default_properties.append(
                    {
                        "base_id": prop.id,
                        "id": prop.name,
                        "type": prop.type.lower(),
                        "property_value": prop.property_name,
                        "created_at": prop.created_at,
                        "updated_at": prop.created_at,
                        "immutable": prop.is_default,
                    }
                )
        else:
            associate = []

        # Shopify Sync Components
        shopify_sync_properties = []
        if page_group_type in [TYPE_OBJECT_ITEM, TYPE_OBJECT_INVENTORY]:
            shopify_sync_properties = get_properties_with_details(
                page_group_type, workspace, lang, p_id, shopify_sync_only=True
            )

        auto_association_properties = []
        if page_group_type in [TYPE_OBJECT_ORDER]:
            auto_association_properties = get_properties_with_details(
                page_group_type, workspace, lang, p_id, auto_association_only=True
            )

        # label related
        if page_group_type in [
            TYPE_OBJECT_ORDER,
            TYPE_OBJECT_CASE,
            TYPE_OBJECT_CONTACT,
            TYPE_OBJECT_COMPANY,
            TYPE_OBJECT_INVENTORY_TRANSACTION,
            TYPE_OBJECT_INVENTORY,
            TYPE_OBJECT_SUBSCRIPTION,
            TYPE_OBJECT_ESTIMATE,
            TYPE_OBJECT_INVOICE,
            TYPE_OBJECT_RECEIPT,
        ]:
            default_properties = [
                item for item in default_properties if item["type"] != "association"
            ]

        if page_group_type in [
            TYPE_OBJECT_INVENTORY
        ]:  # Should b use to all function, but for now just handle the inventory
            default_properties = [
                item for item in default_properties if item["type"] != "items"
            ]

        combined = (
            default_properties
            + custom_properties
            + shopify_sync_properties
            + auto_association_properties
        )

        sort_properties = WorkspacePropertySet.objects.filter(
            object_type=page_group_type
        )
        order_map = {sp.property_id: sp.order for sp in sort_properties}

        for prop_id, target_idx in sorted(order_map.items(), key=lambda x: x[1]):
            # find the current position of that item
            cur_index = next(
                (i for i, item in enumerate(combined) if str(item["id"]) == prop_id),
                None,
            )

            # skip if item not found in combined list
            if cur_index is None:
                continue

            # pull it out
            item = combined.pop(cur_index)

            # clamp: if target_idx > len, this will just append
            insert_at = min(target_idx, len(combined))
            combined.insert(insert_at, item)

        paginator = Paginator(combined, per_page)
        combined = paginator.page(page)
        paginator_item_begin = (per_page * int(page)) - (per_page - 1)
        paginator_item_end = per_page * int(page)

        custom_properties = []

        print("======= ====== default_properties: ", default_properties)

        context = {
            "default_properties": combined,
            "custom_properties": custom_properties,
            "shopify_sync_properties": shopify_sync_properties,
            "page_group_type": page_group_type,
            "associate": associate,
            "auto_association_properties": auto_association_properties,
            # Project
            "p_id": p_id,
            # Custom Object
            "custom_object": custom_object,
            # Search
            "search_q": search_q,
            # Pagination
            "pagination": paginator,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "current_page": page,
            "per_page": per_page,
        }
        return render(request, "data/property/properties-table.html", context)

    elif request.method == "POST":
        property_ids = request.POST.getlist("property_ids")
        for i, property_id in enumerate(property_ids):
            custom_model.objects.filter(id=property_id).update(order=i)

        if page_group_type in [
            TYPE_OBJECT_INVENTORY,
            TYPE_OBJECT_INVENTORY_TRANSACTION,
            TYPE_OBJECT_ITEM,
            TYPE_OBJECT_CASE,
        ]:
            target = page_group_type
        else:
            target = None

        # NOTE: @khanhdq - Abstraction please, it's better than comment
        if target:
            # NOTE: @hira29 saving value of default barcode property
            barcode_type = request.POST.get("barcode-type", None)
            barcode_properties = request.POST.get("barcode-properties", None)
            default_barcode_property = DefaultBarcodeProperty.objects.filter(
                workspace=workspace, target=target
            ).first()
            if default_barcode_property:
                if barcode_type:
                    default_barcode_property.type = barcode_type

                if barcode_properties:
                    default_barcode_property.column_field = barcode_properties

                default_barcode_property.edit_by = request.user
                default_barcode_property.save()
            else:
                # NOTE: @hira29 this is preventing when try to set the value but the the DefaultBarcodeProperty is not created yet
                pre_default_barcode_column = []
                barcode_column_values = [
                    "item_id",
                    "name",
                    "description",
                    "price",
                    "tax",
                    "status",
                    "created_at",
                ]
                pre_default_barcode_column.extend(barcode_column_values)
                item_namecustomfields = ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace
                ).values_list("id", flat=True)
                item_namecustomfields = [str(uuid) for uuid in item_namecustomfields]
                barcode_column_values.extend(item_namecustomfields)

                default_barcode_property = DefaultBarcodeProperty.objects.create(
                    workspace=workspace, target=target
                )
                if barcode_type:
                    default_barcode_property.type = barcode_type
                else:
                    default_barcode_property.type = BARCODE_TYPES[0][0]

                if barcode_properties:
                    default_barcode_property.column_field = barcode_properties
                else:
                    default_barcode_property.column_field = barcode_column_values[0]

                default_barcode_property.edit_by = request.user
                default_barcode_property.save()

        if page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
            return redirect(
                reverse(f"{setting_url}", host="app") + f"?custom_object={setting_type}"
            )
        else:
            return redirect(
                reverse(f"{setting_url}", host="app") + f"?setting_type={setting_type}"
            )


@login_or_hubspot_required
def manage_property(request, id=None):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    if not page_group_type:
        return HttpResponse(404)

    page_obj = get_page_object(page_group_type, lang)
    base_model = page_obj["base_model"]
    custom_model = page_obj["custom_model"]
    setting_type = page_obj["setting_type"]
    setting_url = page_obj["setting_url"]
    exclude_custom_types = page_obj["exclude_custom_types"]

    if "parent_object" in page_obj:
        if page_obj["parent_object"]:
            parent_page_obj = get_page_object(page_obj["parent_object"], lang)
            setting_url = parent_page_obj["setting_url"]
            setting_type = parent_page_obj["setting_type"]

    if request.method == "GET":
        custom_model_types = custom_model._meta.get_field("type").choices
        custom_model_types = [
            _type
            for _type in custom_model_types
            if _type[0] not in exclude_custom_types
        ]

        context = {"page_group_type": page_group_type, "types": custom_model_types}

        p_id = request.GET.get("p_id", None)
        if p_id:
            context["p_id"] = p_id

        # For Procurement Item Status
        view_custom_property = request.GET.get("view_custom_property", None)
        manage_type = request.GET.get("manage-type", None)
        view_id = request.GET.get("view_id", None)
        set_id = request.GET.get("set_id", None)
        if view_id:
            context["view_id"] = view_id
        if set_id:
            context["set_id"] = set_id
        if view_custom_property:
            context["page_group_type"] = page_group_type
            context["manage_type"] = manage_type
            context["property"] = None
            return render(
                request, "data/property/procurement-property-drawer.html", context
            )

        line_item_property = request.GET.get("line_item_property", None)
        line_item_custom_property = request.GET.get("line_item_custom_property", None)

        if line_item_property:
            context["page_group_type"] = page_group_type
            context["manage_type"] = manage_type
            context["property"] = None
            context["LINE_ITEMS_CF_TYPE"] = LINE_ITEMS_CF_TYPE

            if line_item_custom_property:
                if page_group_type == TYPE_OBJECT_CASE:
                    context["line_item_custom_property"] = (
                        DealsNameCustomField.objects.filter(
                            id=line_item_custom_property
                        ).first()
                    )
                existing_line_item_properties = (
                    DealsNameCustomField.objects.filter(id=line_item_custom_property)
                    .first()
                    .line_item_properties
                )
                if existing_line_item_properties:
                    existing_line_item_properties = ast.literal_eval(
                        existing_line_item_properties
                    )
                    context["existing_line_item_properties"] = [
                        prop["name"] for prop in existing_line_item_properties
                    ]
                else:
                    context["existing_line_item_properties"] = []

                return render(
                    request,
                    "data/property/line-items-custom-property-drawer.html",
                    context,
                )
            else:
                return render(
                    request, "data/property/line-items-property-drawer.html", context
                )

        if id:
            if is_valid_uuid(id):
                custom_field = None
                if manage_type:
                    if manage_type == "manage-status":
                        try:
                            custom_field = get_object_or_404(CustomProperty, id=id)
                        except:
                            pass
                    elif manage_type == "manage-line-item-property":
                        try:
                            if page_group_type == TYPE_OBJECT_ORDER:
                                custom_field = get_object_or_404(
                                    ShopTurboItemsOrdersNameCustomField, id=id
                                )
                            elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                                custom_field = get_object_or_404(
                                    PurchaseOrdersItemNameCustomField, id=id
                                )
                            elif page_group_type == TYPE_OBJECT_CASE:
                                custom_field = get_object_or_404(
                                    DealsItemsNameCustomField, id=id
                                )
                            elif page_group_type == TYPE_OBJECT_ESTIMATE:
                                custom_field = get_object_or_404(
                                    EstimateItemsNameCustomField, id=id
                                )
                        except:
                            pass

                    context["page_group_type"] = page_group_type
                    context["manage_type"] = manage_type
                    context["property"] = custom_field
                    if manage_type == "manage-status":
                        return render(
                            request,
                            "data/property/procurement-property-drawer.html",
                            context,
                        )
                    elif manage_type == "manage-line-item-property":
                        context["LINE_ITEMS_CF_TYPE"] = LINE_ITEMS_CF_TYPE
                        return render(
                            request,
                            "data/property/line-items-property-drawer.html",
                            context,
                        )
                    elif manage_type == "manage-line-item-custom-property":
                        context["LINE_ITEMS_CF_TYPE"] = LINE_ITEMS_CF_TYPE
                        context["line_item_custom_property"] = line_item_custom_property
                        context["line_item_custom_property_type"] = request.GET.get(
                            "line_item_custom_property_type", None
                        )
                        if page_group_type == TYPE_OBJECT_CASE:
                            context["property"] = DealsNameCustomField.objects.filter(
                                id=id
                            ).first()

                        existing_line_item_properties = (
                            DealsNameCustomField.objects.filter(id=id)
                            .first()
                            .line_item_properties
                        )
                        if existing_line_item_properties:
                            existing_line_item_properties = ast.literal_eval(
                                existing_line_item_properties
                            )
                            context["existing_line_item_properties"] = [
                                prop["name"] for prop in existing_line_item_properties
                            ]
                        else:
                            context["existing_line_item_properties"] = []

                        return render(
                            request,
                            "data/property/line-items-custom-property-drawer.html",
                            context,
                        )
                else:
                    try:
                        custom_field = custom_model.objects.get(id=id)
                    except Exception as e:
                        print(e)
                context["property"] = custom_field

            elif id in [field.name for field in base_model._meta.fields]:
                context["property"] = {
                    "id": id,
                    "name": request.GET.get("name"),
                    "type": request.GET.get("type"),
                    "immutable": True,
                }
        properties = get_properties_with_details(page_group_type, workspace, lang, p_id)
        default_properties = [
            property_display(f"{p['id']}|{page_group_type}", lang, workspace)["name"]
            for p in properties
            if not is_valid_uuid(p["id"])
        ]

        names = custom_model.objects.filter(workspace=workspace).values_list(
            "name", flat=True
        )
        context["existing_custom_property"] = default_properties + list(names)
        context["from_table_mapping"] = request.GET.get("from-table-mapping", None)

        if page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
            context["custom_object_id"] = request.GET.get("custom_object_id", None)

        return render(request, "data/property/property-drawer.html", context)

    elif request.method == "POST":
        name = request.POST.get("name")
        type = request.POST.get("type")
        value_display = request.POST.get("value_display")
        if value_display not in ["text", "number", "percent"] + [
            currency[0].lower() for currency in CURRENCY_MODEL
        ]:
            value_display = "text"
        number_format = request.POST.get("number_format")
        p_id = request.POST.get("p_id", None)
        text_unique = request.POST.get("text-unique-check", None)
        required_field = request.POST.get("required-field", None)
        multiple_select = request.POST.get("multiple-select-choice", None)

        project = request.POST.get("project", None)

        view_id = request.POST.get("view_id", "")
        set_id = request.POST.get("set_id", "")
        object_type = request.POST.get("object_type", "")
        line_item_property = request.POST.get("line_item_property", None)
        show_badge = request.POST.get("show_badge", None)

        custom_object_id = request.POST.get("custom_object_id", None)
        custom_object = None
        line_item_custom_property = request.POST.get("line_item_custom_property", None)

        if id:
            if is_valid_uuid(id):
                custom_field = None
                try:
                    custom_field = custom_model.objects.get(id=id)
                    if (
                        line_item_custom_property
                        and "delete-property" not in request.POST
                    ):
                        try:
                            if not custom_field.line_item_properties:
                                line_item_properties = []
                            else:
                                line_item_properties = ast.literal_eval(
                                    custom_field.line_item_properties
                                )

                            if not custom_field.hidden_properties:
                                hidden_properties = []
                            else:
                                hidden_properties = ast.literal_eval(
                                    custom_field.hidden_properties
                                )

                            if hidden_properties is False:
                                hidden_properties = []

                            choice = ""
                            if type == "choice":
                                choice_labels = request.POST.getlist("choice_label")
                                choice_values = request.POST.getlist("choice_value")
                                choice_colors = request.POST.getlist('choice_color')
                                choice = []
                                for i, value in enumerate(choice_values):
                                    label = choice_labels[i]
                                    if label:
                                        label = label.strip()
                                    if value:
                                        value = value.replace(",", "").strip()

                                    color = choice_colors[i] if i < len(choice_colors) else '#000000'

                                    choice.append({"label": label, "value": value, "color": color})

                            for line_item_property in line_item_properties:
                                if (
                                    line_item_property["name"]
                                    == line_item_custom_property
                                ):
                                    if line_item_property["name"] in hidden_properties:
                                        hidden_properties.remove(
                                            line_item_property["name"]
                                        )
                                        hidden_properties.append(name)
                                    line_item_property["name"] = name
                                    line_item_property["type"] = type
                                    line_item_property["choice_value"] = choice
                                    line_item_property["number_format"] = number_format
                                    custom_field.line_item_properties = (
                                        line_item_properties
                                    )
                                    custom_field.hidden_properties = hidden_properties
                                    custom_field.save()
                                    break
                        except:
                            traceback.print_exc()
                        return redirect(
                            reverse("manage_property", host="app", kwargs={"id": id})
                            + f"?page_group_type={page_group_type}&name={custom_field.name}"
                        )

                except:
                    if line_item_property:
                        if page_group_type == TYPE_OBJECT_ORDER:
                            custom_field = (
                                ShopTurboItemsOrdersNameCustomField.objects.get(id=id)
                            )
                        elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                            custom_field = (
                                PurchaseOrdersItemNameCustomField.objects.get(id=id)
                            )
                        elif page_group_type == TYPE_OBJECT_CASE:
                            custom_field = DealsItemsNameCustomField.objects.get(id=id)
                        elif page_group_type == TYPE_OBJECT_ESTIMATE:
                            custom_field = EstimateItemsNameCustomField.objects.get(
                                id=id
                            )
                    else:
                        try:
                            custom_field = get_object_or_404(CustomProperty, id=id)
                        except Exception as e:
                            print(e)
        else:  # New
            if view_id or set_id or line_item_property or line_item_custom_property:
                if line_item_property:
                    if page_group_type == TYPE_OBJECT_ORDER:
                        custom_field = (
                            ShopTurboItemsOrdersNameCustomField.objects.create(
                                workspace=workspace, name=name, type=type
                            )
                        )
                    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                        custom_field = PurchaseOrdersItemNameCustomField.objects.create(
                            workspace=workspace, name=name, type=type
                        )
                    elif page_group_type == TYPE_OBJECT_CASE:
                        custom_field = DealsItemsNameCustomField.objects.create(
                            workspace=workspace, name=name, type=type
                        )
                    elif page_group_type == TYPE_OBJECT_ESTIMATE:
                        custom_field = EstimateItemsNameCustomField.objects.create(
                            workspace=workspace, name=name, type=type
                        )

                        print("====================== custom_field: ", custom_field)

                elif line_item_custom_property:
                    if page_group_type == TYPE_OBJECT_CASE:
                        custom_field = DealsNameCustomField.objects.get(
                            id=line_item_custom_property
                        )
                        existing_line_item_properties = (
                            custom_field.line_item_properties
                        )

                        if not existing_line_item_properties:
                            updated_line_item_properties = []
                        else:
                            updated_line_item_properties = ast.literal_eval(
                                existing_line_item_properties
                            )

                        choice = ""
                        if type == "choice":
                            choice_labels = request.POST.getlist("choice_label")
                            choice_values = request.POST.getlist("choice_value")
                            choice_colors = request.POST.getlist('choice_color')
                            choice = []
                            for i, value in enumerate(choice_values):
                                label = choice_labels[i]
                                if label:
                                    label = label.strip()
                                if value:
                                    value = value.replace(",", "").strip()
                                
                                color = choice_colors[i] if i < len(choice_colors) else '#000000'

                                choice.append({"label": label, "value": value, "color": color})

                        updated_line_item_properties.append(
                            {
                                "name": name,
                                "type": type,
                                "choice_value": choice,
                                "number_format": number_format,
                            }
                        )
                        custom_field.line_item_properties = updated_line_item_properties
                        custom_field.save()

                        return redirect(
                            reverse(
                                "manage_property",
                                host="app",
                                kwargs={"id": line_item_custom_property},
                            )
                            + f"?page_group_type={page_group_type}&name={custom_field.name}"
                        )
                else:
                    try:
                        custom_field = CustomProperty.objects.create(
                            workspace=workspace
                        )
                        custom_field.model = base_model._meta.db_table
                        custom_field.save()
                        id = custom_field.id
                    except:
                        pass
            else:
                if page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
                    custom_object = CustomObject.objects.filter(
                        id=custom_object_id, workspace=workspace
                    ).first()
                    if not custom_object:
                        return HttpResponse(status=404)

                    custom_field = custom_model(
                        workspace=workspace, custom_object=custom_object
                    )
                else:
                    custom_field = custom_model(workspace=workspace)
                if "order" in [field.name for field in custom_model._meta.fields]:
                    custom_field.order = custom_model.objects.filter(
                        workspace=workspace, name__isnull=False
                    ).count()

        if "delete-property" in request.POST:
            if type == "contact_list":
                contact_list = ContactList.objects.filter(
                    workspace=workspace, contact_name_customfield=custom_field
                ).delete()
                custom_field.delete()
                query_string = f"?setting_type={setting_type}"
                return redirect(reverse(f"{setting_url}", host="app") + query_string)

            if custom_field._meta.model_name == "customproperty":
                custom_field.delete()
                if page_group_type == TYPE_OBJECT_ORDER:
                    return redirect(
                        reverse("shopturbo_load_drawer", host="app")
                        + f"?drawer_type=shopturbo-view-settings&page=orders&view_id={view_id}"
                    )
                elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                    return redirect(
                        reverse("commerce_view_setting", host="app")
                        + f"?object_type={page_group_type}&view_id={view_id}"
                    )
            elif line_item_property:
                if (
                    page_group_type == TYPE_OBJECT_ORDER
                    or page_group_type == TYPE_OBJECT_PURCHASE_ORDER
                    or page_group_type == TYPE_OBJECT_CASE
                    or page_group_type == TYPE_OBJECT_ESTIMATE
                ):
                    print("========= page_group_type: ", page_group_type)

                    custom_field.delete()
                    if lang == "ja":
                        line_item_param_name = "商品項目"
                    else:
                        line_item_param_name = "Line Item"
                    return redirect(
                        reverse(
                            "manage_base_property",
                            host="app",
                            kwargs={"id": "line_item"},
                        )
                        + f"?page_group_type={page_group_type}&name={line_item_param_name}"
                    )
            elif line_item_custom_property:
                try:
                    if not custom_field.line_item_properties:
                        line_item_properties = []
                    else:
                        line_item_properties = ast.literal_eval(
                            custom_field.line_item_properties
                        )
                    if not custom_field.hidden_properties:
                        hidden_properties = []
                    else:
                        hidden_properties = ast.literal_eval(
                            custom_field.hidden_properties
                        )

                    if hidden_properties is False:
                        hidden_properties = []

                    for line_item_property in line_item_properties:
                        if line_item_property["name"] == line_item_custom_property:
                            if line_item_property["name"] in hidden_properties:
                                hidden_properties.remove(line_item_property["name"])
                            line_item_properties.remove(line_item_property)
                            custom_field.line_item_properties = line_item_properties
                            custom_field.hidden_properties = hidden_properties
                            custom_field.save()
                            break
                except:
                    traceback.print_exc()
                return redirect(
                    reverse("manage_property", host="app", kwargs={"id": id})
                    + f"?page_group_type={page_group_type}&name={custom_field.name}"
                )
            elif page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
                custom_field.delete()
                custom_object = CustomObject.objects.filter(
                    id=custom_object_id, workspace=workspace
                ).first()
                if not custom_object:
                    return HttpResponse(status=404)

                return redirect(
                    reverse(f"{setting_url}", host="app")
                    + f"?custom_object={custom_object.slug}"
                )

            else:
                if page_group_type == "company":
                    page_group_type = "companies"  # additional mapping
                view_filters = ViewFilter.objects.filter(
                    view__workspace=workspace, view__target=page_group_type
                )
                print("view_filters ", view_filters)
                for view_filter in view_filters:
                    if not view_filter or not view_filter.column:
                        continue

                    columns = ast.literal_eval(view_filter.column)
                    if is_valid_uuid(id) and id in columns:
                        columns.remove(id)

                    view_filter.column = columns
                    view_filter.save()

                custom_field.delete()

                query_string = f"?setting_type={setting_type}"
                query_string += f"&p_id={p_id}" if p_id else ""

                return redirect(reverse(f"{setting_url}", host="app") + query_string)

        if name:
            name = name.strip()
            custom_field.name = name
        if type:
            custom_field.type = type
        if type == "formula":
            custom_field.value_display = value_display

        if required_field:
            custom_field.required_field = True
        else:
            custom_field.required_field = False

        if show_badge:
            custom_field.show_badge = True
        else:
            custom_field.show_badge = False

        if number_format:
            custom_field.number_format = number_format
        if p_id and p_id != "None":
            project_target = Projects.objects.get(id=p_id)
            custom_field.project_target = project_target

        if type in ["hierarchy"]:
            choice_labels = request.POST.getlist("choice_label")
            choice_values = request.POST.getlist("choice_value")

            choice = []
            for i, value in enumerate(choice_values):
                # if needs_normalization(value):value=normalize_japanese_text(value)

                label = choice_labels[i]
                if label:
                    label = label.strip()
                if value:
                    value = value.strip()

                choice.append({"label": label, "value": value})

            if (
                custom_field._meta.model_name == "companynamecustomfield"
            ):  # hiearcy related
                if custom_field.type == "hierarchy":
                    # choice = [{'label': 'AAA', 'value': '99460b99-6d2d-4e0b-93c6-3117dbfc3366'}, {'label': 'CCC', 'value': 'a1c6259c-e7fa-427e-a9e3-5c7a272557cb'}]
                    custom_field.choice_value = choice
                    for option in choice:
                        company_cf_values = (
                            CompanyValueCustomField.objects.filter(
                                field_name__workspace=workspace,
                                field_name=custom_field,
                                value__icontains=option["value"],
                            )
                            .exclude(value__isnull=True)
                            .order_by("-created_at")
                        )
                        for company_cf_value in company_cf_values:
                            hierarchy_choice_id = company_cf_value.value.split("#")[-1]
                            company_cf_value.value = (
                                option["label"] + "#" + hierarchy_choice_id
                            )
                            company_cf_value.save()

                else:
                    custom_field.choice_value = choice

        elif type in ["choice", "process_master", "production_line"]:
            choice_labels = request.POST.getlist("choice_label")
            choice_values = request.POST.getlist("choice_value")
            choice_colors = request.POST.getlist('choice_color')

            choice = []

            for i, value in enumerate(choice_values):
                # if needs_normalization(value):value=normalize_japanese_text(value)

                label = choice_labels[i]
                if label:
                    label = label.strip()
                if value:
                    value = value.strip()

                color = choice_colors[i] if i < len(choice_colors) else '#000000'

                choice.append({"label": label, "value": value, "color": color})

            if "message" in custom_field._meta.model_name:
                if not custom_field.choice_value:
                    custom_field.choice_value = [
                        {"label": THREAD_STATUS_DISPLAY[value][lang], "value": value}
                        for value, label in THREAD_STATUS_CHOICES
                    ]

            elif custom_field._meta.model_name == "customproperty":
                custom_field.value = choice
            else:
                custom_field.choice_value = choice

            # handle Multiple Select
            if multiple_select:
                custom_field.multiple_select = True
            else:
                custom_field.multiple_select = False

        elif type == "hierarchy_choice":
            choice = []
            print(request.POST)
            uid_list = request.POST.getlist("category_uid")
            for uid in uid_list:
                if uid:
                    category = request.POST.get("main_category_" + uid)
                    choice_labels = request.POST.getlist("choice_label_" + uid)
                    choice_values = request.POST.getlist("choice_value_" + uid)

                    for i, value in enumerate(choice_values):
                        choice.append(
                            {
                                "category": category,
                                "choices": [
                                    {
                                        "label": choice_labels[i],
                                        "value": choice_values[i]
                                    }
                                ],
                            }
                        )

            # Combine choices by category
            categorized_data = {}
            for item in choice:
                category = item["category"]
                if category not in categorized_data:
                    # Initialize list if category not in dictionary
                    categorized_data[category] = []
                categorized_data[category].extend(
                    item["choices"]
                )  # Add choices to the category

            # Convert the categorized dictionary back to the desired format
            choice = [
                {"category": category, "choices": choices}
                for category, choices in categorized_data.items()
            ]

            if "message" in custom_field._meta.model_name:
                if not custom_field.choice_value:
                    custom_field.choice_value = [
                        {"label": THREAD_STATUS_DISPLAY[value][lang], "value": value}
                        for value, label in THREAD_STATUS_CHOICES
                    ]
            elif custom_field._meta.model_name == "customproperty":
                print("Saving Choice: ", choice)
                custom_field.value = choice
            else:
                custom_field.choice_value = choice

        elif type == "contact_list":
            contact_list_name = request.POST.getlist("contact_list_name")
            contact_list_id = request.POST.getlist("contact_list_id")

            contact_list_data = []
            active_contact_lists = []
            for i, name in enumerate(contact_list_name):
                cl_id = contact_list_id[i]
                if name:
                    name = name.strip()
                if cl_id:
                    cl_id = cl_id.strip()

                if cl_id:
                    contact_list = ContactList.objects.get(id=cl_id)
                    contact_list.name = name
                    contact_list.save()
                else:
                    custom_field.save()  # save custom_field first. before creating new

                    contact_list = ContactList.objects.create(
                        workspace=workspace, contact_name_customfield=custom_field
                    )
                    contact_list.name = name
                    contact_list.save()

                contact_list_data.append({"name": name, "id": str(contact_list.id)})
                active_contact_lists.append(contact_list.id)

            ContactList.objects.filter(
                workspace=workspace, contact_name_customfield=custom_field
            ).exclude(id__in=active_contact_lists).delete()

            custom_field.choice_value = contact_list_data

        elif type == "shipping_info":
            custom_field.sub_property = request.POST.getlist("field_value")

        elif type == "property_sync":
            source_property = request.POST.get("source_property")
            pair_property = request.POST.get("pair_property")
            field_conditions = request.POST.getlist("field_condition")
            field_sources = request.POST.getlist("field_source")
            field_outputs = request.POST.getlist("field_output")
            sync_conditions = []

            print(
                source_property,
                pair_property,
                field_conditions,
                field_sources,
                field_outputs,
            )
            for i, condition in enumerate(field_conditions):
                if condition:
                    sync_conditions.append(
                        [field_conditions[i], field_sources[i], field_outputs[i]]
                    )
            if source_property == "default":
                custom_field.source_property = None
            else:
                custom_field.source_property = source_property

            if pair_property == "default":
                custom_field.pair_property = None
            else:
                custom_field.pair_property = pair_property
            custom_field.sync_conditions = sync_conditions

        elif type == "language":
            source_property = request.POST.get("source_property")
            pair_property = request.POST.get("pair_property")
            source_language = request.POST.get("source_language")
            pair_language = request.POST.get("pair_language")
            sync_conditions = []

            print(source_property, pair_property, source_language, pair_language)
            if source_property == "default":
                custom_field.source_property = None
            else:
                custom_field.source_property = source_property

            if pair_property == "default":
                custom_field.pair_property = None
            else:
                custom_field.pair_property = pair_property
            custom_field.sync_conditions = [source_language, pair_language]

        elif type == "currency":
            source_property = request.POST.get("source_property")
            pair_property = request.POST.get("pair_property")
            selected_currency = request.POST.get("selected_currency")

            if source_property == "default":
                custom_field.source_property = None
            else:
                custom_field.source_property = source_property

            if pair_property == "default":
                custom_field.pair_property = None
            else:
                custom_field.pair_property = pair_property
            custom_field.sync_conditions = selected_currency

        elif type == "formula":
            choice_values = request.POST.getlist("choice_value", None)
            choice_colors = request.POST.getlist('choice_color')
            print("====== choice_values: ", choice_values)
            custom_field.choice_value = choice_values[0]
            print("===== custom_field.choice_value: ",custom_field)
        elif type == "text":
            if text_unique:
                custom_field.unique = True
            else:
                custom_field.unique = False
        elif type == "price-information":
            property_check = request.POST.getlist("property-check", None)
            custom_field.hidden_properties = property_check

        # Input for Multiple Select
        elif type in [
            "attendance",
            "choice",
            "purchase_order",
            "order_objects",
            "case",
            "production_objects",
        ]:
            if multiple_select:
                custom_field.multiple_select = True
            else:
                custom_field.multiple_select = False

        elif type in ["task"]:
            if project:
                try:
                    project = Projects.objects.get(id=project)
                except Projects.DoesNotExist:
                    return redirect(
                        reverse(f"{setting_url}", host="app") + query_string
                    )

                custom_field.project_target = project
            else:
                custom_field.project_target = None

        if type == "production_line":
            production_line_property = {}
            line_ids = request.POST.getlist("line_id", [])
            line_names = request.POST.getlist("line_name", [])
            throughputs = request.POST.getlist("throughput", [])
            available_hours = request.POST.getlist("available_hours", [])

            if line_ids and line_names and throughputs and available_hours:
                # Ensure all lists have the same length to prevent IndexError
                if not (
                    len(line_ids)
                    == len(line_names)
                    == len(throughputs)
                    == len(available_hours)
                ):
                    logger.warning(
                        "Mismatched lengths for production line properties. Skipping update."
                    )
                    production_line_property = {}  # Clear to prevent partial updates
                else:
                    for index, idx in enumerate(line_ids):
                        production_line_property[idx] = {
                            "line_name": line_names[index],
                            "throughput": throughputs[index],
                            "available_hours": available_hours[index],
                        }
            if production_line_property:
                custom_field.choice_value = production_line_property

        custom_field.edit_by = request.user
        custom_field.save()

        set_custom_properties_association(
            type_object=type,
            page_group_type=page_group_type,
            workspace=workspace,
            custom_field=custom_field,
        )

        query_string = f"?setting_type={setting_type}"
        query_string += f"&p_id={p_id}" if p_id else ""

        if request.POST.get("from-table-mapping", None):
            return HttpResponse(200)

        if page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
            custom_object = CustomObject.objects.filter(
                id=custom_object_id, workspace=workspace
            ).first()
            if not custom_object:
                return HttpResponse(status=404)

            return redirect(
                reverse(f"{setting_url}", host="app")
                + f"?custom_object={custom_object.slug}"
            )

        if custom_field._meta.model_name == "customproperty" and set_id:
            return redirect(
                reverse("form_sets_manage", host="app", kwargs={"id": set_id})
                + f"?page_group_type={object_type}&view_id={view_id}"
            )
        elif custom_field._meta.model_name == "customproperty" or line_item_property:
            if (
                page_group_type == TYPE_OBJECT_ORDER
                or page_group_type == TYPE_OBJECT_PURCHASE_ORDER
                or page_group_type == TYPE_OBJECT_CASE
                or page_group_type == TYPE_OBJECT_ESTIMATE
            ):
                if lang == "ja":
                    line_item_param_name = "商品項目"
                else:
                    line_item_param_name = "Line Item"
                return redirect(
                    reverse(
                        "manage_base_property", host="app", kwargs={"id": "line_item"}
                    )
                    + f"?page_group_type={page_group_type}&name={line_item_param_name}"
                )

        return redirect(reverse(f"{setting_url}", host="app") + query_string)


@login_or_hubspot_required
def manage_base_property(request, id):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    key = None
    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    if not page_group_type:
        return HttpResponse(404)

    page_obj = get_page_object(page_group_type, lang)
    base_model = page_obj["base_model"]
    custom_model = page_obj["custom_model"]
    setting_type = page_obj["setting_type"]
    setting_url = page_obj["setting_url"]
    additional_column_fields = page_obj["additional_column_fields"]

    # Handle case where base_model is None (object type not fully implemented)
    if base_model is None:
        return HttpResponse(404)

    if "parent_object" in page_obj:
        if page_obj["parent_object"]:
            parent_page_obj = get_page_object(page_obj["parent_object"], lang)
            setting_type = parent_page_obj["setting_type"]
            setting_url = parent_page_obj["setting_url"]

    if (
        id not in [field.name for field in base_model._meta.get_fields()]
        and additional_column_fields
        and id not in additional_column_fields
    ):
        # Drawer become broken if use redirect , so just modify the response
        return HttpResponse(404)

    if request.method == "GET":
        types = custom_model._meta.get_field("type").choices
        key = request.GET.get("key", "")

        context = {"page_group_type": page_group_type, "key": key, "types": types}

        context["property"] = {
            "id": id,
            "name": request.GET.get("name"),
            "type": request.GET.get("type"),
            "immutable": True,
            "immutable_property": request.GET.get("immutable_property"),
        }
        if key:
            return render(
                request, "data/property/property-drawer-specific-key.html", context
            )
        elif id == "line_item":
            if page_group_type == TYPE_OBJECT_ORDER:
                context["properties"] = (
                    ShopTurboItemsOrdersNameCustomField.objects.filter(
                        workspace=workspace
                    )
                )
            elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                context["properties"] = (
                    PurchaseOrdersItemNameCustomField.objects.filter(
                        workspace=workspace
                    )
                )
            elif page_group_type == TYPE_OBJECT_CASE:
                context["properties"] = DealsItemsNameCustomField.objects.filter(
                    workspace=workspace
                )

            elif page_group_type == TYPE_OBJECT_ESTIMATE:
                context["properties"] = EstimateItemsNameCustomField.objects.filter(
                    workspace=workspace
                )
            elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                context["properties"] = (
                    PurchaseOrdersItemNameCustomField.objects.filter(
                        workspace=workspace
                    )
                )

            return render(
                request, "data/property/property-drawer-line-item.html", context
            )
        else:
            return render(request, "data/property/property-drawer.html", context)

    elif request.method == "POST":
        type = request.POST.get("type")
        key = request.POST.get("key", "")
        # use_default = request.POST.get("use_default", "")
        field_name = id.lower()

        custom_property, _ = CustomProperty.objects.get_or_create(
            workspace=workspace, model=base_model._meta.db_table, name=field_name
        )

        if type in ["choice", "hierarchy"]:
            choice_labels = request.POST.getlist("choice_label")
            choice_values = request.POST.getlist("choice_value")
            choice_colors = request.POST.getlist('choice_color')
            choice = {}
            for i, value in enumerate(choice_values):
                color = choice_colors[i] if i < len(choice_colors) else '#000000'
                choice[value] = {'label': choice_labels[i], 'color': color}
            custom_property.value = choice

        elif type == "hierarchy_choice":
            choice = []
            choice_counter_category = {}
            uid_list = request.POST.getlist("category_uid")
            for uid in uid_list:
                if uid:
                    category = request.POST.get("main_category_" + uid)
                    choice_labels = request.POST.getlist("choice_label_" + uid)
                    choice_values = request.POST.getlist("choice_value_" + uid)

                    if page_group_type == TYPE_OBJECT_JOURNAL:
                        choice_income_expense = request.POST.getlist(
                            "choice_income_expense_" + uid, None
                        )

                    for i, value in enumerate(choice_values):
                        choice.append(
                            {
                                "category": category,
                                "choices": [
                                    {
                                        "label": choice_labels[i],
                                        "value": choice_values[i],
                                    }
                                ],
                            }
                        )
                        # Set the default counter category for accounting
                        if (
                            choice_income_expense
                            and page_group_type == TYPE_OBJECT_JOURNAL
                        ):
                            try:
                                choice_counter_category[choice_values[i]] = (
                                    choice_income_expense[i]
                                )
                            except Exception as e:
                                print("Error in choice_income_expense: ", e)
                                choice_counter_category[choice_values[i]] = ";"

            # Combine choices by category
            categorized_data = {}
            for item in choice:
                category = item["category"]
                if category not in categorized_data:
                    # Initialize list if category not in dictionary
                    categorized_data[category] = []
                categorized_data[category].extend(
                    item["choices"]
                )  # Add choices to the category

            # Convert the categorized dictionary back to the desired format
            result = [
                {"category": category, "choices": choices}
                for category, choices in categorized_data.items()
            ]

            custom_property.value = result

            if page_group_type == TYPE_OBJECT_JOURNAL and choice_counter_category:
                related_custom_property = CustomProperty.objects.filter(
                    workspace=workspace,
                    model=base_model._meta.db_table,
                    name="counter_category",
                ).first()

                if related_custom_property:
                    related_custom_property.value = choice_counter_category
                    related_custom_property.save()
                else:
                    CustomProperty.objects.create(
                        workspace=workspace,
                        model=base_model._meta.db_table,
                        value=choice_counter_category,
                        name="counter_category",
                    )

        elif type == "string" or type == "text":
            if "items" in base_model.__name__.lower():
                max_text_length = request.POST.get("max_text_length")
                custom_property.value = {"max_text_length": max_text_length}
            else:
                try:
                    customproperty_value = (
                        ast.literal_eval(custom_property.value)
                        if custom_property.value
                        else {}
                    )
                except (ValueError, SyntaxError):
                    customproperty_value = {}

                if "required-field" in request.POST:
                    customproperty_value["required_field"] = True
                else:
                    customproperty_value.pop("required_field", None)

                custom_property.value = customproperty_value

        elif type == "items":
            if "shopturboinventory" in base_model.__name__.lower():
                max_text_length = request.POST.get("max_text_length")
                custom_property.value = {"max_text_length": max_text_length}

        custom_property.edit_by = request.user
        custom_property.save()
    if key:
        return HttpResponse(200)

    return redirect(
        reverse(f"{setting_url}", host="app") + f"?setting_type={setting_type}"
    )


@login_or_hubspot_required
def property_child(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    page_obj = get_page_object(page_group_type, lang)
    base_model = page_obj["base_model"]
    custom_model = page_obj["custom_model"]
    editable_columns = page_obj["editable_columns"]
    additional_column_fields = page_obj["additional_column_fields"]

    if request.method == "GET":
        view_custom_property = request.GET.get("view_custom_property", None)
        line_item_property = request.GET.get("line_item_property", None)
        property_type = request.GET.get("type")
        property_id = request.GET.get("property_id")
        key = request.GET.get("key", "")

        context = {
            "type": request.GET.get("type"),
            "key": key,
            "page_group_type": page_group_type,
        }
        if view_custom_property:
            context["view_custom_property"] = view_custom_property
        if line_item_property:
            context["line_item_property"] = line_item_property

        context["custom_properties"] = custom_model.objects.filter(
            workspace=workspace
        ).exclude(type__in=["formula", "property_sync"])

        context["default_properties"] = page_obj["base_columns"]

        context["from_table_mapping"] = request.GET.get("from_table_mapping", None)

        property = None
        related_custom_property = None
        related_custom_property_choices = []
        line_item_custom_property = request.GET.get("line_item_custom_property", None)
        if property_id:
            if is_valid_uuid(property_id):
                if view_custom_property:
                    property = CustomProperty.objects.filter(id=property_id).first()
                    custom_property = property
                elif line_item_property:
                    if page_group_type == TYPE_OBJECT_ORDER:
                        property = ShopTurboItemsOrdersNameCustomField.objects.filter(
                            id=property_id
                        ).first()
                    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                        property = PurchaseOrdersItemNameCustomField.objects.filter(
                            id=property_id
                        ).first()
                    elif page_group_type == TYPE_OBJECT_CASE:
                        property = DealsItemsNameCustomField.objects.filter(
                            id=property_id
                        ).first()
                    elif page_group_type == TYPE_OBJECT_ESTIMATE:
                        property = EstimateItemsNameCustomField.objects.filter(
                            id=property_id
                        ).first()
                elif line_item_custom_property:
                    if page_group_type == TYPE_OBJECT_CASE:
                        line_item_property = DealsNameCustomField.objects.filter(
                            id=property_id
                        ).first()
                        line_item_properties = ast.literal_eval(
                            line_item_property.line_item_properties
                        )

                        choice_property = None
                        for line_item_property in line_item_properties:
                            if line_item_property["name"] == line_item_custom_property:
                                choice_property = line_item_property
                                break

                        choice_value = [{}]
                        if choice_property:
                            try:
                                choice_value = [
                                    {"value": item["value"], "label": item["label"]}
                                    for item in choice_property["choice_value"]
                                ]
                            except:
                                choice_value = [{}]

                        context["choices"] = choice_value
                        if property_type == "choice":
                            return render(
                                request,
                                "data/property/line-item-property-choice-option-loop.html",
                                context,
                            )
                        elif property_type == "number":
                            # Safely get number_format with default fallback
                            context["selected_number_format"] = choice_property.get(
                                "number_format", "number"
                            )
                            context["number_formats"] = custom_model._meta.get_field(
                                "number_format"
                            ).choices
                            return render(
                                request,
                                "data/property/line-item-number-format-select.html",
                                context,
                            )
                else:
                    property = custom_model.objects.filter(id=property_id).first()

            if (
                not property
                and property_id
                in [field.name for field in base_model._meta.get_fields()]
                or property_id in additional_column_fields
                and not line_item_custom_property
            ):
                if not view_custom_property:
                    custom_property = CustomProperty.objects.filter(
                        workspace=workspace,
                        model=base_model._meta.db_table,
                        name=property_id,
                    ).first()

                if property_type == "choice":
                    choice_value = [{}]
                    if custom_property:
                        if view_custom_property:
                            try:
                                choice_value = [
                                    {"value": item["value"], "label": item["label"]}
                                    for item in ast.literal_eval(custom_property.value)
                                    if item["value"].strip() and item["label"].strip()
                                ]
                            except:
                                choice_value = [{}]
                        elif (
                            property_id == "counter_category"
                            and page_group_type == TYPE_OBJECT_JOURNAL
                        ):
                            default_category_list = {}
                            default_choice_list = {}
                            for (
                                key,
                                category,
                            ) in DEFAULT_JOURNAL_CATEGORY.copy().items():
                                default_category_list[key] = [
                                    category["en"],
                                    category["ja"],
                                ]
                                for choice in category["choices"]:
                                    default_choice_list[choice["value"]] = [
                                        choice["en"],
                                        choice["ja"],
                                    ]

                            related_custom_property = CustomProperty.objects.filter(
                                workspace=workspace,
                                model=base_model._meta.db_table,
                                name="category",
                            ).first()

                            if related_custom_property:
                                custom_property_value = ast.literal_eval(
                                    related_custom_property.value
                                )
                                result = []

                                # Translating from default
                                for category in custom_property_value:
                                    for key, cat_ in default_category_list.items():
                                        if cat_[0] == category["category"]:
                                            if lang == "ja":
                                                category_value = cat_[1]
                                            else:
                                                category_value = cat_[0]
                                            break
                                        elif cat_[1] == category["category"]:
                                            if lang == "ja":
                                                category_value = cat_[1]
                                            else:
                                                category_value = cat_[0]
                                            break
                                        else:
                                            category_value = category["category"]

                                    category_data = {"category": category_value}
                                    choices_list = []
                                    for choice in category["choices"]:
                                        choice_list_dict = {
                                            "label": choice["label"],
                                            "value": choice["value"],
                                        }
                                        choices_list.append(choice_list_dict)
                                        if choice["value"] in default_choice_list:
                                            default_label_list = default_choice_list[
                                                choice["value"]
                                            ]
                                            if choice["label"] in default_label_list:
                                                if lang == "ja":
                                                    choice_list_dict["label"] = (
                                                        default_choice_list[
                                                            choice["value"]
                                                        ][1]
                                                    )
                                                else:
                                                    choice_list_dict["label"] = (
                                                        default_choice_list[
                                                            choice["value"]
                                                        ][0]
                                                    )
                                        category_data["choices"] = choices_list
                                    result.append(category_data)
                                related_custom_property.value = str(result)

                                # Merging choices
                                if related_custom_property.value:
                                    choice_value = []
                                    custom_property_value = ast.literal_eval(
                                        custom_property.value
                                    )
                                    for category in ast.literal_eval(
                                        related_custom_property.value
                                    ):
                                        for internal_value in category["choices"]:
                                            related_custom_property_choices.append(
                                                {
                                                    "value": internal_value["value"],
                                                    "label": internal_value["label"],
                                                }
                                            )
                                            if (
                                                internal_value["value"]
                                                in custom_property_value
                                            ):
                                                label = custom_property_value[
                                                    internal_value["value"]
                                                ]
                                                try:
                                                    choice_value.append(
                                                        {
                                                            "label_income": label.split(
                                                                CSV_DELIMITER_LIST_FIELD
                                                            )[0],
                                                            "label_expense": label.split(
                                                                CSV_DELIMITER_LIST_FIELD
                                                            )[1],
                                                            "value": internal_value[
                                                                "label"
                                                            ],
                                                            "refered_choice": internal_value[
                                                                "value"
                                                            ],
                                                        }
                                                    )
                                                except:
                                                    choice_value.append(
                                                        {
                                                            "label_income": label,
                                                            "label_expense": label,
                                                            "value": internal_value[
                                                                "label"
                                                            ],
                                                            "refered_choice": internal_value[
                                                                "value"
                                                            ],
                                                        }
                                                    )

                                            else:
                                                choice_value.append(
                                                    {
                                                        "label_income": "",
                                                        "label_expense": "",
                                                        "value": internal_value[
                                                            "label"
                                                        ],
                                                        "refered_choice": internal_value[
                                                            "value"
                                                        ],
                                                    }
                                                )
                        else:
                            choice_value = [
                                {"value": value, "label": label}
                                for value, label in ast.literal_eval(
                                    custom_property.value
                                ).items()
                            ]

                    elif property_id in editable_columns:
                        if property_id == "counter_category":  # JournalEntry Object
                            related_custom_property = CustomProperty.objects.filter(
                                workspace=workspace,
                                model=base_model._meta.db_table,
                                name="category",
                            ).first()
                            if related_custom_property:
                                if related_custom_property.value:
                                    choice_value = []
                                    for category in ast.literal_eval(
                                        related_custom_property.value
                                    ):
                                        for internal_value in category["choices"]:
                                            # For the list of options <Select2>
                                            related_custom_property_choices.append(
                                                {
                                                    "value": internal_value["value"],
                                                    "label": internal_value["label"],
                                                }
                                            )

                                            choice_value.append(
                                                {
                                                    "label_income": "",
                                                    "label_expense": "",
                                                    "value": internal_value["label"],
                                                    "refered_choice": internal_value[
                                                        "value"
                                                    ],
                                                }
                                            )
                            else:
                                choice_value = []
                                for (
                                    key,
                                    category,
                                ) in DEFAULT_JOURNAL_CATEGORY.copy().items():
                                    for choice in category["choices"]:
                                        # For the list of options <Select2>
                                        related_custom_property_choices.append(
                                            {
                                                "value": choice["value"],
                                                "label": choice["en"]
                                                if lang == "en"
                                                else choice["ja"],
                                            }
                                        )

                                        choice_value.append(
                                            {
                                                "label_income": f"{choice['counter_category_income']}",
                                                "label_expense": f"{choice['counter_category_expense']}",
                                                "value": choice["en"]
                                                if lang == "en"
                                                else choice["ja"],
                                                "refered_choice": choice["value"],
                                            }
                                        )
                        else:
                            choice_value = [
                                {
                                    "value": value,
                                    "label": label["ja"]
                                    if lang == "ja"
                                    else label["en"],
                                }
                                for value, label in editable_columns[
                                    property_id
                                ].items()
                            ]
                    else:
                        choice_value = [
                            {"value": item[0], "label": item[1]}
                            for item in base_model._meta.get_field(property_id).choices
                        ]

                    property = {
                        "id": property_id,
                        "choice_value": choice_value,
                    }
                    if not view_custom_property:
                        property["immutable"] = True

                elif property_type == "hierarchy_choice":
                    try:
                        if custom_property:
                            if (
                                property_id == "category"
                                and page_group_type == TYPE_OBJECT_JOURNAL
                            ):
                                default_category_list = {}
                                default_choice_list = {}
                                for (
                                    key,
                                    category,
                                ) in DEFAULT_JOURNAL_CATEGORY.copy().items():
                                    default_category_list[key] = [
                                        category["en"],
                                        category["ja"],
                                    ]
                                    for choice in category["choices"]:
                                        default_choice_list[choice["value"]] = [
                                            choice["en"],
                                            choice["ja"],
                                        ]

                                check_default_label = custom_property.value
                                # Translating from default
                                if check_default_label:
                                    custom_property_value = ast.literal_eval(
                                        check_default_label
                                    )
                                    result = []
                                    for category in custom_property_value:
                                        for key, cat_ in default_category_list.items():
                                            if cat_[0] == category["category"]:
                                                if lang == "ja":
                                                    category_value = cat_[1]
                                                else:
                                                    category_value = cat_[0]
                                                break
                                            elif cat_[1] == category["category"]:
                                                if lang == "ja":
                                                    category_value = cat_[1]
                                                else:
                                                    category_value = cat_[0]
                                                break
                                            else:
                                                category_value = category["category"]

                                        category_data = {"category": category_value}

                                        choices_list = []
                                        for choice in category["choices"]:
                                            choice_list_dict = {
                                                "label": choice["label"],
                                                "value": choice["value"],
                                            }
                                            choices_list.append(choice_list_dict)
                                            if choice["value"] in default_choice_list:
                                                default_label_list = (
                                                    default_choice_list[choice["value"]]
                                                )
                                                if (
                                                    choice["label"]
                                                    in default_label_list
                                                ):
                                                    if lang == "ja":
                                                        choice_list_dict["label"] = (
                                                            default_choice_list[
                                                                choice["value"]
                                                            ][1]
                                                        )
                                                    else:
                                                        choice_list_dict["label"] = (
                                                            default_choice_list[
                                                                choice["value"]
                                                            ][0]
                                                        )
                                            category_data["choices"] = choices_list
                                        result.append(category_data)
                                    custom_property.value = result
                                    # custom_property.save()

                            choice_value = custom_property.value

                        elif property_id in editable_columns:
                            result = []
                            if (
                                page_group_type == TYPE_OBJECT_JOURNAL
                                and property_id == "category"
                            ):
                                for (
                                    key,
                                    category,
                                ) in DEFAULT_JOURNAL_CATEGORY.copy().items():
                                    category_data = {
                                        # Use English name as the category label
                                        "category": category[lang],
                                        "choices": [
                                            {
                                                # Use English name as the label
                                                "label": choice[lang],
                                                # Keep the value as-is
                                                "value": choice["value"],
                                                # Keep the value as-is
                                                "income_expense": f"{choice['counter_category_income']}{CSV_DELIMITER_LIST_FIELD}{choice['counter_category_expense']}",
                                            }
                                            for choice in category["choices"]
                                        ],
                                    }
                                    result.append(category_data)

                                context["use_default"] = True
                            choice_value = result
                        else:
                            choice_value = [{}]

                        property = {
                            "id": property_id,
                            "choice_value": choice_value,
                        }
                        if not view_custom_property:
                            property["immutable"] = True
                    except Exception as e:
                        traceback.print_exc()
                        print("Errors: ", e)

                elif property_type == "text":
                    if (
                        "items" in base_model.__name__.lower()
                        or "shopturboinventory" in base_model.__name__.lower()
                    ):
                        if custom_property:
                            if custom_property.value:
                                custom_property.value = ast.literal_eval(
                                    custom_property.value
                                )
                            context["max_text_length"] = custom_property.value
                        else:
                            context["max_text_length"] = {"max_text_length": 25}

                    elif property_id in editable_columns:
                        if custom_property:
                            if custom_property.value:
                                try:
                                    custom_property.value = ast.literal_eval(
                                        custom_property.value
                                    )
                                except (ValueError, SyntaxError):
                                    custom_property.value = {}

                                context["custom_property_value"] = custom_property.value

        context["property"] = property
        if related_custom_property_choices:
            context["related_custom_property_choices"] = related_custom_property_choices
        if request.GET.get("uid"):
            context["uid"] = request.GET.get("uid")
        else:
            context["uid"] = uuid.uuid4()

        if property_type in ["choice", "process_master"]:
            if "add" in request.GET:
                return render(
                    request, "data/property/property-choice-option.html", context
                )

        elif property_type == "hierarchy_choice":
            if "add" in request.GET:
                if request.GET.get("choice") == "main_category":
                    return render(
                        request,
                        "data/property/property-hierarchy-main-category.html",
                        context,
                    )
                else:
                    return render(
                        request,
                        "data/property/property-hierarchy-choice-option.html",
                        context,
                    )

        elif property_type == "contact_list":
            if "add" in request.GET:
                return render(
                    request, "data/property/property-contact-list.html", context
                )

        elif property_type == "hierarchy":
            if "add" in request.GET:
                return render(
                    request, "data/property/property-hierarchy-option.html", context
                )

        elif property_type == "shipping_info":
            if "add" in request.GET:
                return render(
                    request, "data/property/property-field-option.html", context
                )

        elif property_type == "property_sync":
            if "add" in request.GET:
                return render(
                    request, "data/property/property-sync-option.html", context
                )

        elif property_type == "number":
            context["number_formats"] = custom_model._meta.get_field(
                "number_format"
            ).choices

        elif property_type == "formula":
            context = get_context_formula_properties(
                context, page_group_type, workspace, lang
            )
            context["display_formats"] = [
                (currency[0].lower(), currency[0].upper())
                for currency in CURRENCY_MODEL
            ]

        elif property_type == "text":
            # Set text_unique_check context variable for all text properties
            if property:
                # Try to get the unique attribute, default to False if not found
                try:
                    unique_value = get_attr(property, "unique")
                    context["text_unique_check"] = (
                        bool(unique_value) if unique_value is not None else False
                    )
                except (AttributeError, TypeError):
                    context["text_unique_check"] = False
            else:
                # Even if property is None, set the context variable to False
                context["text_unique_check"] = False

        elif property_type == "add_line_item":
            if "add" in request.GET:
                context["is_line_item"] = True
                if page_group_type == TYPE_OBJECT_ORDER:
                    context["choices"] = (
                        ShopTurboItemsOrdersNameCustomField.objects.filter(
                            workspace=workspace
                        ).order_by("created_at")
                    )
                elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                    context["choices"] = (
                        PurchaseOrdersItemNameCustomField.objects.filter(
                            workspace=workspace
                        ).order_by("created_at")
                    )
                elif page_group_type == TYPE_OBJECT_ESTIMATE:
                    context["choices"] = EstimateItemsNameCustomField.objects.filter(
                        workspace=workspace
                    ).order_by("created_at")
                elif page_group_type == TYPE_OBJECT_CASE:
                    context["choices"] = DealsItemsNameCustomField.objects.filter(
                        workspace=workspace
                    ).order_by("created_at")
                    context["show_default_fields"] = True
                return render(
                    request, "data/property/property-field-option.html", context
                )

        # Add Multiple Select
        if property_type in [
            "attendance",
            "choice",
            "purchase_order",
            "order_objects",
            "case",
            "production_objects",
            "hierarchy",
        ]:
            if property:
                try:
                    if get_attr(property, "multiple_select"):
                        context["multiple_select_choice"] = property.multiple_select
                    else:
                        context["multiple_select_choice"] = False
                except:
                    context["multiple_select_choice"] = False

            if isinstance(property, CustomProperty):
                context["is_custom_property"] = True
            else:
                context["is_custom_property"] = False

        context["languages"] = [
            ("en", "English", "英語"),
            ("ja", "Japanese", "日本語"),
            ("fr", "French", "フランス語"),
            ("de", "German", "ドイツ語"),
            ("es", "Spanish", "スペイン語"),
            ("zh", "Chinese", "中国語"),
            ("ko", "Korean", "韓国語"),
            ("ru", "Russian", "ロシア語"),
        ]

        context["currencies"] = CURRENCY_MODEL

        return render(request, "data/property/property-child-attr.html", context)
    else:
        property_id = request.POST.get("property_id")
        property_type = request.POST.get("type")
        page_group_type = request.POST.get("page_group_type")
        if property_type == "choice":
            if "add" in request.POST:
                add = request.POST.get("add", None)
                if add == "default":
                    DEFAULT_VALUE = []
                    if page_group_type == TYPE_OBJECT_ORDER:
                        DEFAULT_VALUE = SHOPTURBO_ORDER_DELIVERY_STATUS
                        VALUE_DISPLAY = ORDERS_STATUS_DISPLAY

                    elif page_group_type == TYPE_OBJECT_CONVERSATION:
                        DEFAULT_VALUE = THREAD_STATUS_CHOICES
                        VALUE_DISPLAY = THREAD_STATUS_DISPLAY

                    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                        DEFAULT_VALUE = PURCHASE_ORDER_STATUS
                        VALUE_DISPLAY = PURCHASE_ORDER_STATUS_DISPLAY

                    elif (
                        page_group_type == TYPE_OBJECT_JOURNAL
                        and property_id == "settle_choice"
                    ):
                        DEFAULT_VALUE = JOURNAL_ACCOUNT
                        VALUE_DISPLAY = JOURNAL_ACCOUNT_DISPLAY

                    elif (
                        page_group_type == TYPE_OBJECT_JOURNAL
                        and property_id == "tax_rate"
                    ):
                        DEFAULT_VALUE = JOURNAL_TAX_CATEGORY
                        VALUE_DISPLAY = JOURNAL_TAX_CATEGORY_DISPLAY

                    elif page_group_type in LIST_COMMERCE_OBJECT_TYPES:
                        DEFAULT_VALUE = COMMERCE_STATUS
                        VALUE_DISPLAY = COMMERCE_STATUS_DISPLAY
                        if page_group_type == TYPE_OBJECT_ESTIMATE:
                            DEFAULT_VALUE = ESTIMATE_STATUS
                            VALUE_DISPLAY = ESTIMATE_STATUS_DISPLAY

                    elif page_group_type == TYPE_OBJECT_CASE:
                        DEFAULT_VALUE = CASE_STATUS
                        VALUE_DISPLAY = CASE_STATUS_DISPLAY

                    elif (
                        page_group_type == TYPE_OBJECT_JOBS and property_id == "status"
                    ):
                        DEFAULT_VALUE = JOB_STATUS
                        VALUE_DISPLAY = JOB_STATUS_DISPLAY

                    elif (
                        page_group_type == TYPE_OBJECT_JOBS
                        and property_id == "job_type"
                    ):
                        DEFAULT_VALUE = JOB_TYPE
                        VALUE_DISPLAY = JOB_TYPE_DISPLAY

                    data = []
                    for status in DEFAULT_VALUE:
                        label = status[1]
                        if status[0] in VALUE_DISPLAY:
                            label = VALUE_DISPLAY[status[0]][request.LANGUAGE_CODE]
                        data.append((label, status[0]))

                elif add == "csv_import":
                    csv = request.FILES.get("csv_file", False)
                    data = []
                    if csv:
                        with csv.open() as f:
                            read_data = f.read()
                            result = chardet.detect(read_data)
                            encoding = result["encoding"]
                            # #Reset Pointer
                            f.seek(0)
                            if (
                                encoding
                                and isinstance(encoding, str)
                                and encoding.lower() == "shift_jis"
                            ):
                                content = f.read()
                                decoded_content = content.decode("shift_jis")
                                string_data = StringIO(decoded_content)
                                df = pd.read_csv(
                                    string_data,
                                    sep=",",
                                    encoding="shift_jis",
                                    dtype=str,
                                    header=None,
                                )
                            else:
                                try:
                                    df = pd.read_csv(f, dtype=str, header=None)
                                except UnicodeDecodeError:
                                    # Fallback to Shift-JIS if UTF-8 decoding fails
                                    # Reset pointer again
                                    f.seek(0)
                                    try:
                                        # Try to decode with Shift-JIS
                                        decoded_content = read_data.decode(
                                            "shift_jis", errors="ignore"
                                        )
                                        string_data = StringIO(decoded_content)
                                        df = pd.read_csv(
                                            string_data,
                                            sep=",",
                                            encoding="shift_jis",
                                            dtype=str,
                                            header=None,
                                        )
                                    except Exception:
                                        # If all else fails, try with errors='replace'
                                        decoded_content = read_data.decode(
                                            "shift_jis", errors="replace"
                                        )
                                        string_data = StringIO(decoded_content)
                                        df = pd.read_csv(
                                            string_data,
                                            sep=",",
                                            encoding="shift_jis",
                                            dtype=str,
                                            header=None,
                                        )
                            df = df.dropna(axis=1, how="all")
                            df = df.dropna(axis=0, how="all")
                            df = df.drop_duplicates()
                            df = df.dropna(how="any")
                            df = df.reset_index(drop=True)

                        data = [df.iloc[i].tolist() for i in range(len(df))]

                choices = []
                if data:
                    try:
                        data = ast.literal_eval(data)
                    except:
                        pass

                    for (
                        value_item
                    ) in data:  # Renamed 'value' to 'value_item' for clarity
                        if (
                            isinstance(value_item, (list, tuple))
                            and len(value_item) >= 2
                        ):
                            choices.append(
                                {"label": value_item[0], "value": value_item[1]}
                            )
                        else:
                            # Item does not have at least two elements (e.g., from a CSV row with <2 columns).
                            # Silently skipping to prevent IndexError.
                            # Consider logging this for diagnostics if needed.
                            pass

                context = {
                    "type": property_type,
                    "page_group_type": page_group_type,
                    "choices": choices,
                }
                return render(
                    request, "data/property/property-choice-option-loop.html", context
                )
            elif "export" in request.POST:
                import csv

                property_id = request.POST.get("property_id")
                choices_label = request.POST.getlist("choice_label", [])
                choices_value = request.POST.getlist("choice_value", [])

                response = HttpResponse(
                    content_type="text/csv",
                    headers={
                        "Content-Disposition": 'attachment; filename="property_choices.csv"'
                    },
                )

                writer = csv.writer(response)

                for i in range(len(choices_label)):
                    print("i: ", i)
                    if i < len(choices_label) and i < len(choices_value):
                        writer.writerow([choices_label[i], choices_value[i]])

                return response

        elif property_type == "hierarchy_choice":
            print("=============================")
            result = []
            if page_group_type == TYPE_OBJECT_JOURNAL and property_id == "category":
                for key, category in DEFAULT_JOURNAL_CATEGORY.copy().items():
                    category_data = {
                        # Use English name as the category label
                        "category": category[lang],
                        "choices": [
                            {
                                # Use English name as the label
                                "label": choice[lang],
                                # Keep the value as-is
                                "value": choice["value"],
                                # Keep the value as-is
                                "income_expense": f"{choice['counter_category_income']}{CSV_DELIMITER_LIST_FIELD}{choice['counter_category_expense']}",
                            }
                            for choice in category["choices"]
                        ],
                    }
                    result.append(category_data)

            # add = request.POST.get('add', None)
            if "add" in request.POST:
                add = request.POST.get("add", None)
                if add == "csv_import":
                    csv = request.FILES.get("csv_file", False)
                    if csv:
                        with csv.open() as f:
                            read_data = f.read()
                            result_encoding = chardet.detect(read_data)
                            encoding = result_encoding["encoding"]
                            # #Reset Pointer
                            f.seek(0)
                            if (
                                encoding
                                and isinstance(encoding, str)
                                and encoding.lower() == "shift_jis"
                            ):
                                content = f.read()
                                decoded_content = content.decode("shift_jis")
                                string_data = StringIO(decoded_content)
                                df = pd.read_csv(
                                    string_data,
                                    sep=",",
                                    encoding="shift_jis",
                                    dtype=str,
                                    header=None,
                                )
                            else:
                                try:
                                    df = pd.read_csv(f, dtype=str, header=None)
                                except UnicodeDecodeError:
                                    # Fallback to Shift-JIS if UTF-8 decoding fails
                                    # Reset pointer again
                                    f.seek(0)
                                    try:
                                        # Try to decode with Shift-JIS
                                        decoded_content = read_data.decode(
                                            "shift_jis", errors="ignore"
                                        )
                                        string_data = StringIO(decoded_content)
                                        df = pd.read_csv(
                                            string_data,
                                            sep=",",
                                            encoding="shift_jis",
                                            dtype=str,
                                            header=None,
                                        )
                                    except Exception:
                                        # If all else fails, try with errors='replace'
                                        decoded_content = read_data.decode(
                                            "shift_jis", errors="replace"
                                        )
                                        string_data = StringIO(decoded_content)
                                        df = pd.read_csv(
                                            string_data,
                                            sep=",",
                                            encoding="shift_jis",
                                            dtype=str,
                                            header=None,
                                        )
                            df = df.dropna(axis=1, how="all")
                            df = df.dropna(axis=0, how="all")
                            df = df.reset_index(drop=True)

                        for i in range(len(df)):
                            row = df.iloc[i].tolist()
                            if len(row) >= 3:  # Ensure row has category, label, value
                                category = row[0]
                                label = row[1]
                                value = row[2]
                                result.append(
                                    {
                                        "category": category,
                                        "choices": [
                                            {
                                                "label": label,
                                                "value": value,
                                            }
                                        ],
                                    }
                                )

                        def combine_choices_by_category(data):
                            # Create a dictionary to store categories and their combined choices
                            category_dict = {}

                            # Process each item in the data
                            for item in data:
                                category = item["category"]
                                choices = item["choices"]

                                # If we've seen this category before
                                if category in category_dict:
                                    # Add these choices to the existing choices for this category
                                    category_dict[category]["choices"].extend(choices)
                                else:
                                    # Create a new entry for this category
                                    category_dict[category] = {
                                        "category": category,
                                        "choices": choices.copy(),
                                    }

                            # Convert the dictionary back to a list for the final result
                            result = list(category_dict.values())
                            return result

                        result = combine_choices_by_category(result)

            elif "export" in request.POST:
                import csv

                choice = []
                uid_list = request.POST.getlist("category_uid")
                for uid in uid_list:
                    if uid:
                        category = request.POST.get("main_category_" + uid)
                        choice_labels = request.POST.getlist("choice_label_" + uid)
                        choice_values = request.POST.getlist("choice_value_" + uid)

                        for i, _ in enumerate(choice_values):
                            choice.append(
                                {
                                    "category": category,
                                    "choices": [
                                        {
                                            "label": choice_labels[i],
                                            "value": choice_values[i],
                                        }
                                    ],
                                }
                            )

                # Create CSV response
                response = HttpResponse(
                    content_type="text/csv",
                    headers={
                        "Content-Disposition": 'attachment; filename="hierarchy_choices.csv"'
                    },
                )

                writer = csv.writer(response)
                for item in choice:
                    category = item["category"]
                    for choice_item in item["choices"]:
                        writer.writerow(
                            [category, choice_item["label"], choice_item["value"]]
                        )

                return response

            choices = result
            context = {
                "use_default": True,
                "type": property_type,
                "page_group_type": page_group_type,
                "choices": choices,
            }
            return render(
                request, "data/property/property-choice-option-loop.html", context
            )
    return HttpResponse(200)


# ======================================================= PROPERTY_SET ======================================================= #


@login_or_hubspot_required
def form_sets(request):
    workspace = get_workspace(request.user)
    view = None

    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    if not page_group_type:
        return HttpResponse(404)

    print(f"DEBUG FORM_SETS: Called with page_group_type: {page_group_type}")

    view = None
    view_id = request.GET.get("view_id", None)
    print(f"DEBUG FORM_SETS: view_id: {view_id}")

    sets = PropertySet.objects.filter(
        workspace=workspace, target=page_group_type
    ).order_by(
        "created_at"
    )  # NOTE:FARIS: I change it so it can be expandable to others cc @Rendardi
    print(f"DEBUG FORM_SETS: Found {sets.count()} PropertySets")

    for property_set in sets:
        print(
            f"DEBUG FORM_SETS: PropertySet '{property_set.name}' children: {property_set.children}"
        )

        # Update PropertySet to include inventory custom properties if missing
        if page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
            from utils.properties.properties import ShopTurboInventoryNameCustomField

            inventory_custom_fields = ShopTurboInventoryNameCustomField.objects.filter(
                workspace=workspace
            )
            updated = False
            for cf in inventory_custom_fields:
                inventory_custom_property = f"commerce_inventory|{cf.id}"
                if inventory_custom_property not in property_set.children:
                    property_set.children.append(inventory_custom_property)
                    updated = True
                    print(
                        f"DEBUG FORM_SETS: Added missing inventory custom property to PropertySet: {inventory_custom_property} (name: {cf.name})"
                    )
            if updated:
                property_set.save()
                print(
                    f"DEBUG FORM_SETS: Updated PropertySet '{property_set.name}' children: {property_set.children}"
                )

        if property_set.children:
            for child in property_set.children:
                if "commerce_inventory|" in str(child):
                    print(
                        f"DEBUG FORM_SETS: ✓ Found inventory custom property in PropertySet children: {child}"
                    )
                else:
                    print(f"DEBUG FORM_SETS: PropertySet child: {child}")

    if view_id:
        view = View.objects.get(id=view_id)
        if not view.form:
            default_set = sets.filter(Q(as_default=True) | Q(name__isnull=True)).first()
            view.form = default_set
            view.save()

    context = {
        "sets": sets,
        "page_group_type": page_group_type,
        "view": view,
    }
    return render(request, "data/property/set/form-table.html", context)


@login_or_hubspot_required
def form_sets_manage(request, id=None):
    print(f"DEBUG FORM_SETS_MANAGE: Called with id: {id}, method: {request.method}")
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    if not page_group_type:
        return HttpResponse(404)

    page_obj = get_page_object(page_group_type, lang)
    required_properties = page_obj["required_properties"]
    default_form_fields = page_obj["default_form_fields"]

    module_object_slug = OBJECT_TYPE_TO_SLUG[page_group_type]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=page_group_type
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug

    if request.method == "GET":
        view_id = request.GET.get("view_id", None)
        view_form_trigger_from_drawer = request.GET.get(
            "view_form_trigger_from_drawer", None
        )

        property_set = None
        if id and is_valid_uuid(id):
            property_set = get_object_or_404(PropertySet, id=id)

        excludes = get_form_properties_exclude_list(page_group_type)
        properties = get_form_properties(
            page_group_type, workspace, lang, excludes=excludes
        )

        print(
            f"DEBUG FORM_SETS_MANAGE: Got {len(properties)} properties from get_form_properties"
        )
        for prop in properties:
            if "commerce_inventory|" in str(prop.get("id", "")):
                print(
                    f"DEBUG FORM_SETS_MANAGE: Found inventory custom property: {prop}"
                )

        if property_set and not property_set.children:
            if page_group_type in [
                TYPE_OBJECT_INVOICE,
                TYPE_OBJECT_ESTIMATE,
                TYPE_OBJECT_RECEIPT,
            ]:
                property_set.children = DEFAULT_FORM_FIELD_COMMERCE
                property_set.children = [
                    prop for prop in property_set.children if prop not in excludes
                ]
            elif default_form_fields:
                property_set.children = [
                    prop
                    for prop in properties
                    if prop["id"] in default_form_fields
                    or is_valid_uuid(str(prop["id"]))
                ]

        # filter properties
        print(
            f"DEBUG FORM_SETS_MANAGE: Before filtering - properties count: {len(properties)}"
        )
        print(f"DEBUG FORM_SETS_MANAGE: default_form_fields: {default_form_fields}")
        print(f"DEBUG FORM_SETS_MANAGE: page_group_type: {page_group_type}")

        if default_form_fields and (
            page_group_type
            in [
                TYPE_OBJECT_CASE,
                TYPE_OBJECT_ITEM,
                TYPE_OBJECT_ORDER,
                TYPE_OBJECT_CONTACT,
                TYPE_OBJECT_COMPANY,
                TYPE_OBJECT_INVENTORY,
                TYPE_OBJECT_PURCHASE_ORDER,
                TYPE_OBJECT_ESTIMATE,
                TYPE_OBJECT_INVOICE,
                TYPE_OBJECT_RECEIPT,
                TYPE_OBJECT_DELIVERY_NOTE,
                TYPE_OBJECT_INVENTORY_TRANSACTION,
                TYPE_OBJECT_INVENTORY_WAREHOUSE,
                TYPE_OBJECT_EXPENSE,
                TYPE_OBJECT_BILL,
                TYPE_OBJECT_TASK,
            ]
        ):
            print(
                f"DEBUG FORM_SETS_MANAGE: Entering filtering block for {page_group_type}"
            )

            # Allow default form fields, UUIDs, and inventory custom properties (pipe syntax)
            def should_include_property(prop_id):
                if prop_id in default_form_fields:
                    return True
                if is_valid_uuid(str(prop_id)):
                    return True
                # Allow inventory custom properties for inventory transactions
                if page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION and str(
                    prop_id
                ).startswith("commerce_inventory|"):
                    return True
                return False

            properties = [
                prop for prop in properties if should_include_property(prop["id"])
            ]
            print(
                f"DEBUG PROPERTY_FILTERING: Filtered properties count: {len(properties)}"
            )

            for prop in properties:
                print(f"DEBUG PROPERTY_FILTERING: Included property: {prop['id']}")
                if "commerce_inventory|" in str(prop.get("id", "")):
                    print(
                        f"DEBUG PROPERTY_FILTERING: ✓ Inventory custom property survived filtering: {prop}"
                    )
            # additional append
            if page_group_type == TYPE_OBJECT_INVENTORY:
                properties.append(
                    {
                        "id": "initial_value",
                        "type": "number",
                        "created_at": None,
                        "updated_at": None,
                        "edit_by": None,
                        "immutable": False,
                    }
                )
            elif page_group_type == TYPE_OBJECT_INVOICE:
                properties.append(
                    {
                        "id": "customers",
                        "type": "association",
                        "created_at": None,
                        "updated_at": None,
                        "edit_by": None,
                        "immutable": True,
                    }
                )
            elif page_group_type == TYPE_OBJECT_EXPENSE:
                properties.append(
                    {
                        "id": "file",
                        "type": "text",
                        "created_at": None,
                        "updated_at": None,
                        "edit_by": None,
                        "immutable": True,
                    }
                )

        if property_set and not property_set.children and default_form_fields:
            property_set.children = [
                prop
                for prop in properties
                if prop["id"] in default_form_fields or is_valid_uuid(str(prop["id"]))
            ]

        print(
            f"DEBUG FORM_SETS_MANAGE: Final properties count for template: {len(properties)}"
        )
        for prop in properties:
            if "commerce_inventory|" in str(prop.get("id", "")):
                print(
                    f"DEBUG FORM_SETS_MANAGE: ✓ Final inventory custom property for template: {prop}"
                )

        # association label
        for association_label in AssociationLabel.objects.filter(
            workspace=workspace, object_source=page_group_type, created_by_sanka=True
        ).order_by("created_at"):
            properties.append(
                {
                    "id": association_label.label,
                    "type": "association",
                    "created_at": None,
                    "updated_at": None,
                    "edit_by": None,
                    "immutable": True,
                }
            )
        for association_label in AssociationLabel.objects.filter(
            workspace=workspace, object_source=page_group_type, created_by_sanka=False
        ).order_by("created_at"):
            properties.append(
                {
                    "id": str(association_label.id),
                    "type": "association",
                    "created_at": None,
                    "updated_at": None,
                    "edit_by": None,
                    "immutable": True,
                }
            )

        print(f"DEBUG FORM_SETS_MANAGE: Checking duplicated columns {properties}")
        check_columns_properties = set()
        filtered_properties = []

        for index, prop in enumerate(properties):
            prop_id = str(prop["id"])
            print(f"DEBUG FORM_SETS_MANAGE: Checking property {index}: {prop_id}")
            if prop_id in check_columns_properties:
                print(f"DEBUG FORM_SETS_MANAGE: DUPLICATED FOR ID: {prop_id}")
                continue
            check_columns_properties.add(prop_id)
            filtered_properties.append(prop)

        print(
            f"DEBUG FORM_SETS_MANAGE: Checking duplicated columns completed: {list(check_columns_properties)}"
        )
        properties = filtered_properties

        context = {
            "properties": properties,
            "page_group_type": page_group_type,
            "required_properties": required_properties,
            "view_id": view_id,
            "set": property_set,
            "view_form_trigger_from_drawer": view_form_trigger_from_drawer,
        }

        if page_group_type == TYPE_OBJECT_TASK:
            view = View.objects.filter(id=view_id).first() if view_id else None
            context["project_id"] = (
                str(view.project_target.id)
                if view and hasattr(view, "project_target") and view.project_target
                else None
            )

        if page_group_type == TYPE_OBJECT_SLIP:
            context["obj"] = {
                "model": page_group_type[:-1].replace("_", "-"),
                "workspace": workspace,
            }
            context["SLIP_TYPE"] = Slip._meta.get_field("slip_type").choices
        return render(request, "data/property/set/form-manage.html", context)

    elif request.method == "POST":
        view = View.objects.filter(id=request.POST.get("view_id")).first()

        if "delete-set" in request.POST:
            PropertySet.objects.get(id=id).delete()
        else:
            name = request.POST.get("name", None)

            if id:
                property_set = PropertySet.objects.get(id=id)
            elif name:
                property_set, _ = PropertySet.objects.get_or_create(
                    workspace=workspace, target=page_group_type, name=name
                )
            else:
                return HttpResponse(400)

            if page_group_type in [
                TYPE_OBJECT_PURCHASE_ORDER,
                TYPE_OBJECT_ORDER,
                TYPE_OBJECT_CASE,
                TYPE_OBJECT_ESTIMATE,
            ]:
                enable_status = request.POST.get("enable_status", False)
                # property_status_default = request.POST.get(
                #     "property_status_default", None
                # )
                field_value = request.POST.getlist("field_value", None)
                if enable_status:
                    if page_group_type == TYPE_OBJECT_ORDER:
                        property_set.order_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                        property_set.purchase_order_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_ESTIMATE:
                        property_set.estimate_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_RECEIPT:
                        property_set.receipt_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_DELIVERY_NOTE:
                        property_set.delivery_slip_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_INVOICE:
                        property_set.invoice_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_CASE:
                        property_set.deal_line_items.clear()
                        property_set.default_properties = None

                    default_properties = []
                    for field in field_value:
                        if page_group_type == TYPE_OBJECT_ORDER:
                            custom_field = (
                                ShopTurboItemsOrdersNameCustomField.objects.filter(
                                    id=field
                                ).first()
                            )
                            property_set.order_line_items.add(custom_field)
                        elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                            custom_field = (
                                PurchaseOrdersItemNameCustomField.objects.filter(
                                    id=field
                                ).first()
                            )
                            property_set.purchase_order_line_items.add(custom_field)
                        elif page_group_type == TYPE_OBJECT_ESTIMATE:
                            custom_field = EstimateItemsNameCustomField.objects.filter(
                                id=field
                            ).first()
                            property_set.estimate_line_items.add(custom_field)

                        elif page_group_type == TYPE_OBJECT_RECEIPT:
                            custom_field = ReceiptItemsNameCustomField.objects.filter(
                                id=field
                            ).first()
                            property_set.receipt_line_items.add(custom_field)
                        elif page_group_type == TYPE_OBJECT_DELIVERY_NOTE:
                            custom_field = (
                                DeliverySlipItemsNameCustomField.objects.filter(
                                    id=field
                                ).first()
                            )
                            property_set.delivery_slip_line_items.add(custom_field)
                        elif page_group_type == TYPE_OBJECT_INVOICE:
                            custom_field = InvoiceItemsNameCustomField.objects.filter(
                                id=field
                            ).first()
                            property_set.invoice_line_items.add(custom_field)

                        elif page_group_type == TYPE_OBJECT_CASE:
                            if is_valid_uuid(field):
                                custom_field = DealsItemsNameCustomField.objects.filter(
                                    id=field
                                ).first()
                                property_set.deal_line_items.add(custom_field)
                            else:
                                default_properties.append(field)

                    if page_group_type == TYPE_OBJECT_CASE:
                        property_set.default_properties = default_properties
                    # custom_property = CustomProperty.objects.filter(id=property_status_default).first()
                    # if custom_property:
                    #     property_set.custom_property = custom_property
                else:
                    if page_group_type == TYPE_OBJECT_ORDER:
                        property_set.order_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
                        property_set.purchase_order_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_CASE:
                        property_set.deal_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_ESTIMATE:
                        property_set.estimate_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_RECEIPT:
                        property_set.receipt_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_DELIVERY_NOTE:
                        property_set.delivery_slip_line_items.clear()
                    elif page_group_type == TYPE_OBJECT_INVOICE:
                        property_set.invoice_line_items.clear()
                    property_set.default_properties = []
            property_set.save()

            # Safety if they not put name on the form
            # print("======== name: ",name)
            # if not name:
            #     return HttpResponse(200)

            children = request.POST.get("children", None)
            sub_children = request.POST.get("sub_children", None)

            print("=== Form submission data:")
            print("=== children:", children)
            print("=== sub_children:", sub_children)
            print("=== name:", name)
            print("=== page_group_type:", page_group_type)

            if name:
                property_set.name = name

            if sub_children:
                property_set.sub_children = sub_children

            if children:
                children = children.split(",")
                print("=== Children before saving:", children)  # Debug log
                property_set.children = children
                print(
                    "=== PropertySet children after saving:", property_set.children
                )  # Debug log

            for idx, require_prop in enumerate(required_properties):
                if not property_set.children:
                    property_set.children = []

                if require_prop not in property_set.children:
                    property_set.children.insert(idx, require_prop)

            property_set.edit_by = request.user
            property_set.save()
            print(
                "=== Final PropertySet children after save:", property_set.children
            )  # Debug log

        return HttpResponse(200)
    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def property_sets(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    if not page_group_type:
        return HttpResponse(404)

    page_obj = get_page_object(page_group_type, lang)
    setting_type = page_obj["setting_type"]
    setting_url = page_obj["setting_url"]

    if request.method == "GET":
        # Updated in form of Slips
        # --> NOTE from Faris: This one is about to not create default for Slips (page_group_type)
        default_set = None
        if page_group_type == TYPE_OBJECT_SLIP:
            for i, prop in enumerate(DEFAULT_SLIPS_TYPE_DISPLAY):
                prop_set_name = DEFAULT_SLIPS_TYPE_DISPLAY[prop][lang]
                prop_list = (
                    DEFAULT_SLIPS_TYPE_DISPLAY[prop]["en"],
                    DEFAULT_SLIPS_TYPE_DISPLAY[prop]["ja"],
                )
                prop_set = PropertySet.objects.filter(
                    workspace=workspace,
                    name__in=prop_list,
                    target=TYPE_OBJECT_SLIP,
                    edit_by__isnull=True,
                ).first()
                if prop_set:
                    prop_set.name = prop_set_name
                    prop_set.save()
        else:
            default_set = get_default_property_set(page_group_type, workspace, lang)

        sets = PropertySet.objects.filter(
            workspace=workspace,
            name__isnull=False,
            target=page_group_type,
        ).order_by("created_at")

        # --> NOTE:FARIS: Catch to create if no property set available there, Because in SLIP we remove all property set (include default)
        try:
            if page_group_type == TYPE_OBJECT_SLIP:
                if not sets:
                    default_set = get_default_property_set(
                        page_group_type,
                        workspace,
                        lang,
                        name=page_obj["default_slip_name"],
                    )
                else:
                    if default_set:
                        default_set.name = page_obj["default_slip_name"]
                        default_set.save()

            elif page_group_type == TYPE_OBJECT_ORDER:
                if not sets:
                    default_set = get_default_property_set(
                        page_group_type,
                        workspace,
                        lang,
                        name=page_obj["default_order_name"],
                    )
                else:
                    if default_set:
                        default_set.name = page_obj["default_order_name"]
                        default_set.save()

        except Exception as e:
            print("Slips props error: ", e)

        sets = list(sets)

        if default_set:
            sets.insert(0, default_set)

        context = {"sets": sets, "page_group_type": page_group_type}
        return render(request, "data/property/set/property-sets-table.html", context)

    elif request.method == "POST":
        set_ids = request.POST.getlist("set_ids")
        for i, set_id in enumerate(set_ids):
            PropertySet.objects.filter(id=set_id).update(order=i)

        return redirect(
            reverse(f"{setting_url}", host="app") + f"?setting_type={setting_type}"
        )


@login_or_hubspot_required
def manage_property_set(request, id=None):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    if not page_group_type:
        return HttpResponse(404)

    page_obj = get_page_object(page_group_type, lang)
    setting_type = page_obj["setting_type"]
    setting_url = page_obj["setting_url"]
    required_properties = page_obj["required_properties"]

    if request.method == "GET":
        properties = get_properties_with_details(page_group_type, workspace, lang)
        context = {
            "properties": properties,
            "page_group_type": page_group_type,
            "required_properties": required_properties,
        }

        if id and is_valid_uuid(id):
            property_set = get_object_or_404(PropertySet, id=id)
            context["set"] = property_set

        return render(request, "data/property/set/property-set-drawer.html", context)

    elif request.method == "POST":
        logger.info("Saving property set with ID: %s", id)
        logger.info(request.POST)

        name = request.POST.get("name")
        is_default = request.POST.get("is_default")
        as_default = request.POST.get("as_default")
        children_str = request.POST.get("children")

        if id:
            property_set = get_object_or_404(PropertySet, id=id)
        else:  # New
            property_set = PropertySet(workspace=workspace, target=page_group_type)

        if "delete-set" in request.POST:
            property_set.delete()
            return redirect(
                reverse(f"{setting_url}", host="app") + f"?setting_type={setting_type}"
            )

        if name and not is_default:
            property_set.name = name
        if children_str:
            children = children_str.split(",")
            property_set.children = children
        if as_default:
            property_set.as_default = True

            sets = PropertySet.objects.filter(
                workspace=workspace,
                target=page_group_type,
            ).exclude(id=property_set.id)

            for _set in sets:
                _set.as_default = False
                _set.save()

        for idx, require_prop in enumerate(required_properties):
            if not property_set.children:
                property_set.children = []

            if require_prop not in property_set.children:
                property_set.children.insert(idx, require_prop)

        property_set.edit_by = request.user

        property_set.save()

        return redirect(
            reverse(f"{setting_url}", host="app") + f"?setting_type={setting_type}"
        )


@login_or_hubspot_required
@require_POST
def toggle_object_visibility(request):
    page_group_type = request.POST.get("page_group_type")
    if page_group_type not in TYPE_OBJECTS:
        print("Page group type is invalid.")
        return HttpResponse(status=400)

    workspace = get_workspace(request.user)
    om, _ = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=page_group_type
    )
    om.is_show = not om.is_show
    om.save()
    return HttpResponse(status=200)


@login_or_hubspot_required
def property_text_unique_check(
    request, object_type, property_id, object_id=None, form=None
):
    if request.method == "POST":
        if object_type in [
            TYPE_OBJECT_PURCHASE_ORDER,
            TYPE_OBJECT_BILL,
            TYPE_OBJECT_EXPENSE,
            TYPE_OBJECT_ESTIMATE,
            TYPE_OBJECT_INVOICE,
            TYPE_OBJECT_DELIVERY_NOTE,
            TYPE_OBJECT_RECEIPT,
            TYPE_OBJECT_SLIP,
            TYPE_OBJECT_CONTACT,
            TYPE_OBJECT_COMPANY,
            TYPE_OBJECT_CASE,
            TYPE_OBJECT_ITEM,
        ]:
            if object_id:
                input_value = request.POST.get(
                    "text|value|" + str(object_id) + "|" + str(property_id)
                )
            else:
                input_value = request.POST.get("text|value||" + str(property_id))
        elif object_type == TYPE_OBJECT_ORDER:
            if object_id:
                input_value = request.POST.get("customfield|" + str(property_id))
                if not input_value:
                    input_value = request.POST.get(
                        "text|value|" + str(object_id) + "|" + str(property_id)
                    )
            else:
                input_value = request.POST.get("text|value|" + str(property_id))
        elif object_type == TYPE_OBJECT_TASK:
            if object_id:
                input_value = request.POST.get("task_custom_field-" + str(property_id))
            else:
                input_value = request.POST.get("task_custom_field-" + str(property_id))
        else:
            if object_id:
                input_value = request.POST.get(
                    "text|value|" + str(object_id) + "|" + str(property_id)
                )
            else:
                input_value = request.POST.get("text|value|" + str(property_id))

        context = {
            "object_type": object_type,
            "object_id": object_id,
            "property_id": property_id,
            "value": input_value,
            "form": form,
            "is_unique": True,
        }

        if input_value:
            object_types_mapping = {
                # ITEMS AND INVENTORY
                TYPE_OBJECT_ITEM: "items",
                TYPE_OBJECT_INVENTORY: "inventory",
                TYPE_OBJECT_INVENTORY_TRANSACTION: "transaction",
                TYPE_OBJECT_INVENTORY_WAREHOUSE: "warehouse",
                # ORDERS AND BILLING
                TYPE_OBJECT_ORDER: "orders",
                TYPE_OBJECT_SUBSCRIPTION: "subscriptions",
                TYPE_OBJECT_ESTIMATE: "estimate",
                TYPE_OBJECT_DELIVERY_NOTE: "deliveryslip",
                TYPE_OBJECT_INVOICE: "invoice",
                TYPE_OBJECT_RECEIPT: "receipt",
                TYPE_OBJECT_SLIP: "slip",
                # PROCURE & SPEND
                TYPE_OBJECT_PURCHASE_ORDER: "purchaseorders",
                TYPE_OBJECT_BILL: "bill",
                TYPE_OBJECT_EXPENSE: "expense",
                # CONTACTS & CASE
                TYPE_OBJECT_CONTACT: "contact",
                TYPE_OBJECT_COMPANY: "company",
                TYPE_OBJECT_CASE: "deals",
                # PROJECT
                TYPE_OBJECT_TASK: "task",
                # WORKERS
                TYPE_OBJECT_WORKER: "worker",
            }

            if object_type in object_types_mapping:
                page_obj = get_page_object(object_type, request.LANGUAGE_CODE)
                custom_model = page_obj["custom_model"]
                custom_value_model = page_obj["custom_value_model"]

                filter_kwargs = (
                    {
                        "field_name__id": property_id,
                        f"{object_types_mapping[object_type]}__id": object_id,
                    }
                    if object_id
                    else None
                )

                custom_field_value = (
                    custom_value_model.objects.filter(**filter_kwargs).first()
                    if filter_kwargs
                    else None
                )
                custom_field = custom_model.objects.filter(id=property_id).first()

                if custom_field and custom_field.unique:
                    is_exists_query = custom_value_model.objects.filter(
                        field_name=custom_field, value=input_value
                    )
                    if custom_field_value:
                        is_exists_query = is_exists_query.exclude(
                            id=custom_field_value.id
                        )
                    print(is_exists_query.exists())
                    context["unique_exists"] = is_exists_query.exists()
                else:
                    context["unique_exists"] = False
            else:
                context["unique_exists"] = False

        return render(request, "data/property/property-unique-text.html", context)
    return HttpResponse(status=404)


@login_or_hubspot_required
def load_object_page(request, module_slug, object_slug):
    workspace = get_workspace(request.user)

    redirect_to_home = redirect(reverse("main", host="app"))
    try:
        module = Module.objects.get(slug=module_slug, workspace=workspace)
    except Module.DoesNotExist:
        print("Module doesnt exist")
        return redirect_to_home

    if not module.object_values:
        print("Object values is missing")
        return redirect_to_home

    custom_object = None
    if object_slug not in OBJECT_SLUG_TO_OBJECT_TYPE:
        custom_object = CustomObject.objects.filter(
            workspace=workspace, slug=object_slug
        ).first()
        if not custom_object:
            print("Custom Object is missing")
            return redirect_to_home

    if custom_object:
        object_type = str(custom_object.id)
    else:
        object_type = OBJECT_SLUG_TO_OBJECT_TYPE[object_slug]

    module_objects = module.object_values.split(",")

    if object_type not in module_objects:
        print(f'Object type "{object_type}" is not in module objects: {module_objects}')
        return redirect_to_home

    if not custom_object:
        # Create Object Manager
        if object_type in [
            TYPE_OBJECT_TIMEGENIE,
            TYPE_OBJECT_WORKER,
            TYPE_OBJECT_WORKER_REVIEW,
            TYPE_OBJECT_WORKER_ABSENCE,
            TYPE_OBJECT_JOBS,
            TYPE_OBJECT_EXPENSE,
        ]:
            om = ObjectManager.objects.filter(
                workspace=workspace, page_group_type=object_type
            ).first()
            if not om:
                om = ObjectManager.objects.create(
                    workspace=workspace, page_group_type=object_type
                )
            try:
                om.column_display = ",".join(DEFAULT_OBJECT_DISPLAY[object_type])
                om.save()
            except Exception:
                pass

        if not ObjectManager.objects.filter(
            workspace=workspace, page_group_type=object_type
        ).exists():
            print("Object Manager is missing")
            return redirect_to_home

        route_to = reverse(OBJECT_TYPE_TO_URL_NAME[object_type], host="app")[
            2:
        ].replace("app." + settings.PARENT_HOST, "")
        print(f"Route to {OBJECT_TYPE_TO_URL_NAME[object_type]}: {route_to}")
    else:
        route_to = reverse("custom_object", host="app", kwargs={"id": object_type})[
            2:
        ].replace("app." + settings.PARENT_HOST, "")
        print(f"Route to custom_object: {route_to}")

    resolved = resolve(route_to, urlconf="data.urls")
    view_func = resolved.func
    view_kwargs = resolved.kwargs

    # Note: The original code here was trying to find default arguments from URL patterns,
    # but this was causing AttributeError and doesn't seem to be necessary.
    # The resolve() call above already provides the correct view_func and view_kwargs.
    # If specific default arguments are needed for certain object types, they should be
    # added explicitly here rather than trying to extract them from URL patterns.

    use_advance_search = request.GET.get("advance_search", False)
    advance_search = AdvanceSearchFilter.objects.filter(
        workspace=workspace, object_type=object_type, type="default"
    ).first()
    if advance_search:
        if use_advance_search:
            advance_search.is_active = True
        else:
            advance_search.is_active = False
        advance_search.save()

    return view_func(request, **view_kwargs)


@login_or_hubspot_required
def get_formula_result(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    obj_id = request.POST.get("obj_id", None)
    custom_prop_id = request.POST.get("custom_field_id", None)
    object_type = request.POST.get("object_type")
    if not (custom_prop_id and object_type):
        print("require custom_prop_id and object_type")
        return HttpResponse(status=400)

    if object_type not in TYPE_OBJECTS:
        print("invalid object_type")
        return HttpResponse(status=400)

    page_obj = get_page_object(object_type, lang)
    base_model = page_obj["base_model"]
    custom_prop_model = page_obj["custom_model"]

    try:
        obj = base_model.objects.get(workspace=workspace, id=obj_id)
    except base_model.DoesNotExist:
        print(f"{base_model} object does not exist")
        return HttpResponse(status=404)
    except Exception as e:
        print(
            f"[ERROR] there is value error cannot be handled by basemodel({base_model}):",
            e,
        )
        return HttpResponse(status=400)

    try:
        custom_prop = custom_prop_model.objects.get(
            id=custom_prop_id, workspace=workspace
        )
    except custom_prop_model.DoesNotExist:
        print(f"{custom_prop_model} object does not exist")
        return HttpResponse(status=404)
    except Exception as e:
        print(
            f"[ERROR] there is value error cannot be handled by custom_prop_model({custom_prop_model}):",
            e,
        )
        return HttpResponse(status=400)

    result = calculate_math(page_obj, obj, custom_prop)
    if result is None:
        return render(
            request,
            "data/property/property-formula-res-display.html",
            {"result": result},
        )
    if custom_prop.value_display:
        if custom_prop.value_display == "text":
            result = str(result)
        elif custom_prop.value_display == "number":
            pass
        elif custom_prop.value_display == "percent":
            result = f"{result * 100:.2f}%"
        else:
            currency = custom_prop.value_display
            symbol = get_currency_symbol(currency.upper())
            print(symbol)
            if currency == "usd":
                result = f"{symbol} {result:,.2f}"
            elif currency == "jpy":
                result = f"{symbol} {result:,.0f}"
            else:
                result = f"{symbol} {result:,}"

    return render(
        request, "data/property/property-formula-res-display.html", {"result": result}
    )


@login_or_hubspot_required
def custom_property_list(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    page_group_type = (
        request.GET.get("page_group_type")
        if request.method == "GET"
        else request.POST.get("page_group_type")
    )
    if not page_group_type:
        return HttpResponse(404)

    form = None
    form_id = request.GET.get("form_id", None)
    view_id = request.GET.get("view_id", None)
    page_obj = get_page_object(page_group_type, lang)
    base_model = page_obj["base_model"]
    properties = CustomProperty.objects.filter(
        workspace=workspace,
        model=base_model._meta.db_table,
    ).order_by(
        "created_at"
    )  # NOTE:FARIS: I change it so it can be expandable to others cc @Rendardi
    context = {}  # Initialize context
    if form_id:
        form = PropertySet.objects.get(id=form_id)
        if not form:
            if properties:
                default_property_status = properties.first()
                form.custom_property = default_property_status
                form.save()

    if form_id and is_valid_uuid(form_id):
        # Corrected 'id' to 'form_id' and ensured 'context' is initialized if needed
        property_set = get_object_or_404(PropertySet, id=form_id)
        # Assuming context is initialized elsewhere or this is part of a larger context dictionary
        # If context is not guaranteed to exist, it should be initialized: context = {}
        context["set"] = property_set

    if view_id and view_id.lower() != "none" and is_valid_uuid(view_id):
        view = View.objects.get(id=view_id)
    else:
        view = None

    line_item_properties = None
    show_default_fields = False
    if page_group_type == TYPE_OBJECT_ORDER:
        line_item_properties = form.order_line_items.all()
    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
        line_item_properties = form.purchase_order_line_items.all()
    elif page_group_type == TYPE_OBJECT_CASE:
        line_item_properties = form.deal_line_items.all()
        show_default_fields = True
    elif page_group_type == TYPE_OBJECT_ESTIMATE:
        line_item_properties = form.estimate_line_items.all()
    elif page_group_type == TYPE_OBJECT_RECEIPT:
        line_item_properties = form.receipt_line_items.all()
    elif page_group_type == TYPE_OBJECT_DELIVERY_NOTE:
        line_item_properties = form.delivery_slip_line_items.all()
    elif page_group_type == TYPE_OBJECT_INVOICE:
        line_item_properties = form.invoice_line_items.all()

    context = {
        "properties": properties,
        "line_item_properties": line_item_properties,
        "default_properties": form.default_properties,
        "show_default_fields": show_default_fields,
        "page_group_type": page_group_type,
        "form": form,
        "view": view,
    }
    return render(request, "data/property/custom/custom-property-table.html", context)


@login_or_hubspot_required
def get_related_custom_object(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    object_type = request.GET.get("object_type")
    if not object_type or object_type not in TYPE_OBJECTS:
        return HttpResponse(status=400)
    object_id = request.GET.get("object_id")

    page_object = get_page_object(object_type, lang)
    custom_model = page_object["custom_model"]

    custom_object_types = [
        "bill_objects",
        "invoice_objects",
        "contact",
        "company",
        "purchase_order",
        "components",
        "subscription",
        "warehouse_objects",
    ]
    custom_properties = custom_model.objects.filter(
        workspace=workspace, type__in=custom_object_types
    )
    if not custom_properties:
        return HttpResponse()

    context = {
        "custom_properties": custom_properties,
        "object_id": object_id,
        "object_type": object_type,
    }
    return render(
        request,
        "data/partials/manage-drawer/associated-custom-object-component.html",
        context,
    )


@login_or_hubspot_required
def get_related_custom_object_values(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    object_type = request.GET.get("object_type")
    if not object_type or object_type not in TYPE_OBJECTS:
        return HttpResponse(status=400)
    object_id = request.GET.get("object_id")

    page_object = get_page_object(object_type, lang)
    base_model = page_object["base_model"]
    custom_prop_value_model = page_object["custom_value_model"]
    custom_value_relation = page_object["custom_value_relation"]

    try:
        obj = base_model.objects.get(id=object_id)
    except base_model.DoesNotExist:
        print("Object does not exist")
        return HttpResponse(status=404)

    custom_object_types = [
        "bill_objects",
        "invoice_objects",
        "contact",
        "company",
        "purchase_order",
        "components",
        "subscription",
        "warehouse_objects",
    ]
    custom_prop_values = custom_prop_value_model.objects.filter(
        Q(**{custom_value_relation: obj}),
        field_name__workspace=workspace,
        field_name__type__in=custom_object_types,
    )
    if not custom_prop_values:
        return HttpResponse()

    context = {
        "source": object_type,
        "custom_prop_values": custom_prop_values,
        "obj": obj,
    }
    return render(
        request,
        "data/partials/manage-drawer/associated-custom-object-values-component.html",
        context,
    )


@login_or_hubspot_required
def get_object(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    object_type = request.GET.get("object_type")
    if not object_type or object_type not in TYPE_OBJECTS:
        return HttpResponse(status=400)
    object_id = request.GET.get("object_id")

    page_object = get_page_object(object_type, lang)
    base_model = page_object["base_model"]

    try:
        obj = base_model.objects.get(workspace=workspace, id=object_id)
    except base_model.DoesNotExist:
        print("Object does not exist")
        return HttpResponse(status=404)

    context = {"object": obj, "object_type": object_type}
    return render(request, "data/property/object-display.html", context)


@login_or_hubspot_required
def create_custom_value(request):
    target_id = request.GET.get("target_id")
    header_value = request.GET.get("header_value")
    context = {
        "target_id": target_id,
        "header_value": header_value,
    }
    return render(request, "data/property/custom/custom-value-drawer.html", context)


@login_or_hubspot_required
def get_process_master(request):
    print(request.GET)

    workspace = get_workspace(request.user)
    property_id = request.GET.get("property_id")
    object_type = request.GET.get("object_type")
    object_id = request.GET.get("object_id", None)
    last_idx = request.GET.get("last_idx", None)

    if not property_id and object_type:
        return HttpResponse(status=400)

    add_item = request.GET.get("add_item", False)

    print(property_id, object_type, object_id, last_idx)

    process_list = []
    process_master = None
    custom_prop = None

    if object_type == TYPE_OBJECT_ITEM:
        if property_id:
            custom_prop = ShopTurboItemsNameCustomField.objects.filter(
                id=property_id, workspace=workspace
            ).first()
            process_list = ast.literal_eval(custom_prop.choice_value)
        else:
            return HttpResponse(status=400)

        if object_id and custom_prop:
            item = ShopTurboItems.objects.filter(
                id=object_id, workspace=workspace
            ).first()
            if item:
                custom_prop_value = ShopTurboItemsValueCustomField.objects.filter(
                    field_name=custom_prop, items=item
                ).first()
                if custom_prop_value:
                    try:
                        process_master = custom_prop_value.value
                        process_master = json.loads(process_master)
                    except:
                        process_master = None
    else:
        return HttpResponse(status=400)

    index = 0
    if last_idx:
        try:
            index = int(last_idx) + 1
        except:
            index = 0

    context = {
        "process_list": process_list,
        "process_master": process_master,
        "add_item": add_item,
        "property_id": property_id,
        "obj_id": object_id,
        "CustomFieldName": custom_prop,
        "index": index,
    }
    return render(request, "data/property/process-master-selector.html", context)


def choice_conditional_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        if "load_drawer" in request.GET:
            property_id = request.GET.get("property_id")
            object_type = request.GET.get("object_type")
            if not property_id or not object_type:
                return HttpResponse(status=400)

            # Special handling for predefined property IDs that are not UUIDs
            special_properties = [
                "tax_rate",
                "slip_type",
                "delivery_status",
                "settle_choice",
                "status",
                "job_type",
                "case_status",
            ]
            if property_id in special_properties:
                # Return a response indicating this is a special property that doesn't support conditional options
                if lang == "ja":
                    return HttpResponse(
                        "このプロパティは条件付き選択をサポートしていません。"
                    )
                else:
                    return HttpResponse(
                        "This property does not support conditional options."
                    )

            # Check if property_id is a valid UUID before querying
            if not is_valid_uuid(property_id):
                if lang == "ja":
                    return HttpResponse(f'"{property_id}" は有効なUUIDではありません。')
                else:
                    return HttpResponse(f'"{property_id}" is not a valid UUID.')

            object_page = get_page_object(object_type, lang)
            custom_model = object_page["custom_model"]

            try:
                obj = custom_model.objects.get(id=property_id)
            except custom_model.DoesNotExist:
                return HttpResponse(status=404)

            mapping = {}
            if obj.conditional_choice_mapping:
                mapping = obj.conditional_choice_mapping

            if mapping:
                controlling_property_id = mapping["controlling_property_id"]
                try:
                    _ = custom_model.objects.get(id=controlling_property_id)
                except custom_model.DoesNotExist:
                    mapping = {}
                    obj.conditional_choice_mapping = mapping
                    obj.save()

            properties = custom_model.objects.filter(
                workspace=workspace, type="choice"
            ).exclude(id=property_id)

            context = {
                "property": obj,
                "properties": properties,
                "object_type": object_type,
                "page_group_type": object_type,
                "mapping": mapping,
            }

            return render(
                request,
                "data/property/property-conditional-options-drawer.html",
                context,
            )

        elif "conditional_configure" in request.GET:
            controlling_property_id = request.GET.get("controlling_property", None)
            property_id = request.GET.get("property_id", None)

            object_type = request.GET.get("object_type")
            if not object_type or not controlling_property_id or not property_id:
                return HttpResponse()

            # Special handling for predefined property IDs that are not UUIDs
            special_properties = [
                "tax_rate",
                "slip_type",
                "delivery_status",
                "settle_choice",
                "status",
                "job_type",
                "case_status",
            ]
            if (
                property_id in special_properties
                or controlling_property_id in special_properties
            ):
                # Return a response indicating this is a special property that doesn't support conditional options
                if lang == "ja":
                    return HttpResponse(
                        "このプロパティは条件付き選択をサポートしていません。"
                    )
                else:
                    return HttpResponse(
                        "This property does not support conditional options."
                    )

            # Check if property_id and controlling_property_id are valid UUIDs before querying
            if not is_valid_uuid(property_id):
                if lang == "ja":
                    return HttpResponse(f'"{property_id}" は有効なUUIDではありません。')
                else:
                    return HttpResponse(f'"{property_id}" is not a valid UUID.')

            if not is_valid_uuid(controlling_property_id):
                if lang == "ja":
                    return HttpResponse(
                        f'"{controlling_property_id}" は有効なUUIDではありません。'
                    )
                else:
                    return HttpResponse(
                        f'"{controlling_property_id}" is not a valid UUID.'
                    )

            page_obj = get_page_object(object_type, request.LANGUAGE_CODE)
            custom_model = page_obj["custom_model"]

            try:
                property = custom_model.objects.get(id=property_id)
            except custom_model.DoesNotExist:
                return HttpResponse()

            child_choices = []
            if property.choice_value:
                try:
                    child_choices = ast.literal_eval(property.choice_value)
                except:
                    if lang == "ja":
                        return HttpResponse(
                            "制御プロパティにはオプションがありません。"
                        )
                    else:
                        return HttpResponse("The property does not have any choices.")

            mapping = {}
            if property.conditional_choice_mapping:
                mapping = property.conditional_choice_mapping

            try:
                obj = custom_model.objects.get(id=controlling_property_id)
            except (custom_model.DoesNotExist, ValidationError):
                if lang == "ja":
                    return HttpResponse(
                        f'"{controlling_property_id}" は有効なUUIDではありません。'
                    )
                else:
                    return HttpResponse(
                        f'"{controlling_property_id}" is not a valid UUID.'
                    )

            choice_value = []
            if obj.choice_value:
                try:
                    choice_value = ast.literal_eval(obj.choice_value)
                except:
                    if lang == "ja":
                        return HttpResponse(
                            "制御プロパティにはオプションがありません。"
                        )
                    else:
                        return HttpResponse(
                            "The controlling property does not have any choices."
                        )

            context = {
                "controlling_property": obj,
                "choice_value": choice_value,
                "page_group_type": object_type,
                "child_choices": child_choices,
                "mapping": mapping,
            }

            return render(
                request,
                "data/property/property-conditional-options-configure.html",
                context,
            )

    if request.method == "POST":
        controlling_property_id = request.POST.get("controlling-property", None)
        object_type = request.POST.get("object_type", None)
        property_id = request.POST.get("property_id", None)

        page_obj = get_page_object(object_type, request.LANGUAGE_CODE)
        custom_model = page_obj["custom_model"]

        if not controlling_property_id and property_id:
            try:
                property = custom_model.objects.get(id=property_id)
            except custom_model.DoesNotExist:
                return HttpResponse(status=400)

            property.conditional_choice_mapping = None
            property.save()
            return HttpResponse(200)

        if (
            not any(key.startswith("choice-options|") for key in request.POST)
            or not object_type
            or not property_id
        ):
            return HttpResponse(status=400)

        # Special handling for predefined property IDs that are not UUIDs
        special_properties = [
            "tax_rate",
            "slip_type",
            "delivery_status",
            "settle_choice",
            "status",
            "job_type",
            "case_status",
        ]
        if (
            property_id in special_properties
            or controlling_property_id in special_properties
        ):
            # Return a response indicating this is a special property that doesn't support conditional options
            if lang == "ja":
                return HttpResponse(
                    "このプロパティは条件付き選択をサポートしていません。"
                )
            else:
                return HttpResponse(
                    "This property does not support conditional options."
                )

        # Check if property_id and controlling_property_id are valid UUIDs before querying
        if not is_valid_uuid(property_id):
            if lang == "ja":
                return HttpResponse(f'"{property_id}" は有効なUUIDではありません。')
            else:
                return HttpResponse(f'"{property_id}" is not a valid UUID.')

        if not is_valid_uuid(controlling_property_id):
            if lang == "ja":
                return HttpResponse(
                    f'"{controlling_property_id}" は有効なUUIDではありません。'
                )
            else:
                return HttpResponse(f'"{controlling_property_id}" is not a valid UUID.')

        try:
            property = custom_model.objects.get(id=property_id)
        except custom_model.DoesNotExist:
            return HttpResponse(status=400)

        child_choices = []
        if property.choice_value:
            try:
                child_choices = ast.literal_eval(property.choice_value)
            except:
                HttpResponse(status=400)

        try:
            obj = custom_model.objects.get(id=controlling_property_id)
        except (custom_model.DoesNotExist, ValidationError):
            if lang == "ja":
                return HttpResponse(
                    f'"{controlling_property_id}" は有効なUUIDではありません。'
                )
            else:
                return HttpResponse(f'"{controlling_property_id}" is not a valid UUID.')

        choice_value = []
        if obj.choice_value:
            try:
                choice_value = ast.literal_eval(obj.choice_value)
            except:
                HttpResponse(status=400)

        # Do Mapping between controlling property
        context = {
            "controlling_property_id": str(obj.id),
            "relation": {},
        }

        for choice in choice_value:
            choice_vals = choice["value"]
            context["relation"][choice_vals] = []
            if request.POST.get(f"choice-options|{choice_vals}", None) == "all":
                context["relation"][choice_vals] = ["choice-options:all"] + [
                    choice_child["value"] for choice_child in child_choices
                ]
            elif request.POST.get(f"choice-options|{choice_vals}", None) == "specific":
                if any(key.startswith("condition|") for key in request.POST):
                    for choice_child in child_choices:
                        if request.POST.get(
                            f"condition|{choice_vals}|{choice_child['value']}", None
                        ):
                            context["relation"][choice_vals].append(
                                choice_child["value"]
                            )
                else:
                    context["relation"][choice_vals] = []
            else:
                context["relation"][choice_vals] = []

        property.conditional_choice_mapping = context
        property.save()

        return HttpResponse(200)

    return HttpResponse(status=404)


@login_or_hubspot_required
def get_production_line(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    property_id = request.GET.get("property_id")
    object_type = request.GET.get("object_type")
    object_id = request.GET.get("object_id", None)
    last_idx = request.GET.get("last_idx", None)

    if not property_id and object_type:
        return HttpResponse(status=400)

    add_item = request.GET.get("add_item", False)
    logger.info(
        f"get_production_line called with property_id: {property_id}, object_type: {object_type}, object_id: {object_id}, last_idx: {last_idx}"
    )

    production_line_list = [
        PRODUCTION_LINE_DEFAULT_DISPLAY[label][lang]
        for label in PRODUCTION_LINE_DEFAULT_DISPLAY
    ]
    production_line = [
        {
            "label_id": label,
            "label": PRODUCTION_LINE_DEFAULT_DISPLAY[label][lang],
            "value": "",
        }
        for label in PRODUCTION_LINE_DEFAULT_DISPLAY
    ]
    custom_prop = None

    if object_type == TYPE_OBJECT_TASK:
        if property_id:
            custom_prop = TaskCustomFieldName.objects.filter(
                id=property_id, workspace=workspace
            ).first()
        else:
            return HttpResponse(status=400)

        if object_id and custom_prop:
            task = Task.objects.filter(id=object_id, workspace=workspace).first()
            if task:
                custom_prop_value = TaskCustomFieldValue.objects.filter(
                    field_name=custom_prop, task=task
                ).first()
                if custom_prop_value:
                    try:
                        value_list = ast.literal_eval(custom_prop_value.value)
                        production_line = [
                            {
                                "label_id": label,
                                "label": PRODUCTION_LINE_DEFAULT_DISPLAY[label][lang],
                                "value": value_list[index]["value"],
                            }
                            for index, label in enumerate(
                                PRODUCTION_LINE_DEFAULT_DISPLAY
                            )
                        ]
                    except:
                        production_line = None
    else:
        return HttpResponse(status=400)

    index = 0
    if last_idx:
        try:
            index = int(last_idx) + 1
        except:
            index = 0

    context = {
        "production_line_list": production_line_list,
        "production_line": production_line,
        "add_item": add_item,
        "property_id": property_id,
        "obj_id": object_id,
        "CustomFieldName": custom_prop,
        "index": index,
    }
    return render(request, "data/property/production-line-selector.html", context)


@login_or_hubspot_required
def add_sub_property(request):
    page_group_type = request.GET.get("page_group_type", None)
    add = request.GET.get("add", None)
    if page_group_type == TYPE_OBJECT_TASK:
        line_id = request.GET.get("line_id", str(uuid.uuid4()))
        if not is_valid_uuid(line_id):
            line_id = str(uuid.uuid4())
        context = {"line_name": "", "throughput": "0", "line_id": line_id}

        if add != "new":
            try:
                add = ast.literal_eval(add)
            except:
                add = ""

            if add:  # add for existing production line data value
                context["line_name"] = add.get("line_name", "")
                context["throughput"] = add.get("throughput", "")
                context["available_hours"] = add.get("available_hours", "")

        return render(
            request,
            "data/taskflow/component/production-line-property-partial.html",
            context,
        )
    else:
        return HttpResponse()


@login_or_hubspot_required
@require_POST
def manage_reorder_property(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    object_type = request.POST.get("object_type", None)
    property_id = request.POST.get("property_id", None)
    old_index = request.POST.get("old_index", None)
    new_index = request.POST.get("new_index", None)

    if (
        not workspace
        or not object_type
        or not property_id
        or not old_index
        or not new_index
    ):
        message = (
            "必要なパラメーターが不足しています"
            if lang == "ja"
            else "Missing required parameters"
        )
        return JsonResponse({"message": message}, status=500)

    property_obj, _ = WorkspacePropertySet.objects.get_or_create(
        workspace=workspace,
        object_type=object_type,
        property_id=property_id,
        defaults={
            "order": 9999
        },  # Put really large number in create mode so it wont be problem later on
    )

    with transaction.atomic():
        if old_index > new_index:
            WorkspacePropertySet.objects.filter(
                workspace=workspace,
                object_type=object_type,
                order__gte=new_index,
                order__lt=old_index,
            ).update(order=F("order") + 1)
        elif old_index < new_index:
            WorkspacePropertySet.objects.filter(
                workspace=workspace,
                object_type=object_type,
                order__gt=old_index,
                order__lte=new_index,
            ).update(order=F("order") - 1)

        property_obj.order = new_index
        property_obj.save()

    return JsonResponse({"message": "success"})
