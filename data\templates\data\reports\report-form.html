{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="card border-0 shadow-none rounded-0 w-100 bg-white">
    <div class="card-header align-items-center" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if report %}
                {% if LANGUAGE_CODE == 'ja' %}
                ダッシュボードを管理
                {% else %}
                Manage Dashboard
                {% endif %}
            {% else %}
                {% if LANGUAGE_CODE == 'ja' %}
                ダッシュボードの作成
                {% else %}
                Create Dashboard
                {% endif %}
            {% endif %}
        </h5>

        {% if report %}
        <div class="card-toolbar">
            <button class="d-flex btn bg-white opacity-75-hover text-start py-3 px-1" type="button" data-kt-menu-trigger="click" data-kt-menu-placement="bottom">
                <span class="svg-icon svg-icon-2x">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <rect x="0" y="0" width="24" height="24"/>
                            <circle fill="#000000" cx="12" cy="5" r="2"/>
                            <circle fill="#000000" cx="12" cy="12" r="2"/>
                            <circle fill="#000000" cx="12" cy="19" r="2"/>
                        </g>
                    </svg>
                </span>
            </button>

            <script>
                KTMenu.createInstances()
            </script>

            <div class="fw-bolder docs-aside-select-menu menu menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-active-bg fw-semibold py-3 w-175px" data-kt-menu="true">
                <div class="menu-item cursor-pointer px-3" data-bs-toggle="tooltip" data-bs-placement="right" data-kt-initialized="1">
                    <button type="submit" 
                            form="report_form" 
                            name="delete-report" 
                            value="true" 
                            class="menu-link px-3 text-danger border-0 bg-transparent w-100 text-start">
                        {% if LANGUAGE_CODE == 'ja' %}削除{% else %}Delete{% endif %}
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    <div class="card-body overflow-scroll">
        
        {% comment %} {% if channel_by_user %} {% endcomment %}
        <div class="mb-10">
            <form id="report_form" method="post" action="{% host_url 'report_form' host 'app' %}">
                {% csrf_token %}
                <input type="hidden" name="workflow" value="{{workflow_id}}">

                {% if type == 'edit' %}
                    <input type="hidden" name="report_id" value="{{report.id}}">
                {% endif %}
                <div class="mb-10">
                    {% if not is_template %}
                        <div class="fv-rowd-flex flex-column mb-8">
                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span class="required">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    名前
                                    {% else %}
                                    Name
                                    {% endif %}
                                </span>
                            </label>
                            <input type="text" class="form-control" {% if report %}value="{{report.name}}"{% endif %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            placeholder="名前" 
                            {% else %}
                            placeholder="Name" 
                            {% endif %}
                            name="name" required />
                        </div>
                    {% endif %}

                    {% if user.verification.admin %}
                        <div class="fv-rowd-flex flex-column mb-8">
                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span class="">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    名前
                                    {% else %}
                                    Name
                                    {% endif %}
                                    - JA
                                </span>
                            </label>
                            <input type="text" class="form-control" {% if report %}value="{{report.name_ja}}"{% endif %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            placeholder="名前 - JA" 
                            {% else %}
                            placeholder="Name - JA" 
                            {% endif %}
                            name="name_ja"  />
                        </div>

                        <div class="fv-rowd-flex flex-column mb-8">
                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span class="">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    説明
                                    {% else %}
                                    Description
                                    {% endif %}
                                </span>
                            </label>
                            <input type="text" class="form-control" {% if report %}value="{{report.description}}"{% endif %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            placeholder="説明 - JA" 
                            {% else %}
                            placeholder="Description - JA" 
                            {% endif %}
                            name="description"  />
                        </div>

                        <div class="fv-rowd-flex flex-column mb-8">
                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span class="">
                                    {% if LANGUAGE_CODE == 'ja' %}
                                    説明
                                    {% else %}
                                    Description
                                    {% endif %}
                                    - JA
                                </span>
                            </label>
                            <input type="text" class="form-control" {% if report %}value="{{report.description_ja}}"{% endif %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            placeholder="説明 - JA" 
                            {% else %}
                            placeholder="Description - JA" 
                            {% endif %}
                            name="description_ja"  />
                        </div>

                        <label for="is_template" class="text-sm font-medium text-gray-700">
                            {% if LANGUAGE_CODE == 'ja' %}
                            テンプレート
                            {% else %}
                            Template
                            {% endif %}
                        </label>
                        <div class="flex items-center space-x-2">
                            <input id="is_template" name="is_template" type="checkbox"
                                class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                {% if report.is_template %}checked{% endif %} />
                            <span class="text-sm text-gray-600">
                                {% if LANGUAGE_CODE == 'ja' %}
                                テンプレートとして保存
                                {% else %}
                                Mark as template
                                {% endif %}
                            </span>
                        </div>
                    {% endif %}

                    <div id="report-type-container" class="fv-rowd-flex flex-column mb-8">
                        {% include 'data/reports/type/panels.html' %}
                    </div>

                    <div class="owner-form-{{object_type}} mb-5">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="min-w-100px">
                                {% if LANGUAGE_CODE == 'ja'%}
                                所有者
                                {% else %}
                                Owner
                                {% endif %}
                            </span>
                        </label>
                        <select data-allow-clear='true' id="owner-input-{{object_type}}" class="h-40px form-select form-select-solid border bg-white select2-this" data-control="select2" name="owner" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}所有者{% else %}Owner{% endif %}">
                            <option></option>
                            {% if report.id and report.owner and report.owner.user %}
                                <option value="{{report.owner.user.username}}" selected>
                                    {{report.owner.user.first_name}} - {{report.owner.user.email}}
                                </option>
                            {% endif %}
                            {% for member in permission|get_scope_from_permission:'edit'|get_users_by_scope:user %}
                                {% if member != report.owner.user %}
                                <option value="{{member.username}}">
                                    {{member.first_name}} - {{member.email}}
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>

                        <script>
                            $('.select2-this').select2()
                        </script>
                    </div>
                </div>

                {% for CustomFieldName in NameCustomField %} 
                    {% if CustomFieldName %}
                        <div class="fv-rowd-flex flex-column mb-8">
                            <div class="mb-4">
                                
                                <span class="{% include 'data/utility/form-label.html' %}">
                                    {{CustomFieldName.name }}
                                </span>
                                
                                {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=CustomFieldName obj=report object_type='dashboards' %}

                            </div>
                        </div>
                    {% endif %}
                {% endfor %}

                {% if permission|check_permission:'edit' %}
                <button type="submit" class="btn btn-dark w-100 " onclick="document.getElementById('delete-report-input').remove();">
                    {% if type == 'edit' %}
                        {% if LANGUAGE_CODE == 'ja' %}
                        更新
                        {% else %}
                        Update
                        {% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ja' %}
                        作成
                        {% else %}
                        Create
                        {% endif %}
                    {% endif %}
                </button>
                {% endif %}
            </form>
        </div>
    </div>
</div>

