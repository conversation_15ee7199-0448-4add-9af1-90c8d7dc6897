{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<div class="d-flex flex-grow-1 w-100">
    <input type="hidden" id="" value="{{condition.value_type}}" name="value-type-{{action_index}}{{condition_index}}">
    <div class="flex-fill">
        <select class="h-40px bg-white border rounded-end-0 form-select form-select-solid condition-field-{{action_index}}{{condition_index}}"
            name="condition-field-{{action_index}}{{condition_index}}"
            onchange="onChangePanelFilterPropertySelect(this.parentElement)"
            >
            {% for k, v in condition_options.items %}
                <option value="{{k}}" data-type="{{v.type}}" data-val-option="{{v.values}}" {% if condition.field_name == k %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}{{v.ja}}{% else %}{{v.en}}{% endif %}</option>
            {% endfor %}
        </select>
    </div>
    <div class="flex-fill">
        <select class="h-40px bg-white border rounded-0 form-select form-select-solid condition-operator-{{action_index}}{{condition_index}}"
            name="condition-operator-{{action_index}}{{condition_index}}">
            {% for k, v in operators.number.items %}
                <option value="{{k}}" {% if condition.operator == k %}selected{% endif %}>{% if LANGUAGE_CODE == 'ja' %}{{v.ja}}{% else %}{{v.en}}{% endif %}</option>
            {% endfor %}
        </select>
    </div>

    <div id="condition-value-number-{{action_index}}{{condition_index}}" class="flex-fill">
        <input type="number" class="rounded-start-0 form-control h-40px " name="condition-value-{{action_index}}{{condition_index}}"
            {% if condition.value_number %}value="{{condition.value_number}}"{% endif %}>
    </div>
</div>

<script>
    $('.condition-field-{{action_index}}{{condition_index}}').select2()
    $('.condition-operator-{{action_index}}{{condition_index}}').select2()

    function onChangePanelFilterPropertySelect(element) {
        const parentElement = element.parentElement;
        const operators = {{operators|safe}}
        const selector = parentElement.querySelector('.condition-field-{{action_index}}{{condition_index}}')
        let inputField = parentElement.querySelector('[name="condition-value-{{action_index}}{{condition_index}}"]')
        let inputValueType = parentElement.querySelector('[name="value-type-{{action_index}}{{condition_index}}"]')
        let propType = selector.selectedOptions[0].getAttribute('data-type')
        let valOpt = selector.selectedOptions[0].getAttribute('data-val-option')

        const operatorSelector = parentElement.querySelector('.condition-operator-{{action_index}}{{condition_index}}')
        operatorSelector.innerHTML = ''

        operators_by_type = operators[propType]
        inputValueType.value = propType
        console.log(propType)
        console.log(operators_by_type)
        Object.entries(operators_by_type).forEach(([key, val]) => {
            const option = document.createElement('option');
            option.value = key;
            option.textContent = LANGUAGE_CODE === 'ja' ? val.ja : val.en;
            operatorSelector.appendChild(option);
        });

        if (inputField.tagify) {
            inputField.tagify.destroy()
            inputField.tagify = undefined
        }
        
        inputField.setAttribute('type', 'text');
        if (propType == 'numeric') {
            inputField.setAttribute('type', 'number');
        } else if (propType == 'datetime') {
            inputField.setAttribute('type', 'date');
        } else if (valOpt) {
            valOpt = JSON.parse(valOpt);
            let tagifyWhitelist = valOpt.map(([value, label]) => ({
                id: value,
                value: label,
            }));

            let tagify = new Tagify(inputField, {
                whitelist: tagifyWhitelist,
                enforceWhitelist: true,
                maxTags: tagifyWhitelist.length, // Limit max selections based on available options
                searchKeys: ['value'], // Allow searching by label
                dropdown: {
                    maxItems: tagifyWhitelist.length,
                    classname: "tagify__inline__suggestions",
                    enabled: 0,
                    closeOnSelect: false
                },
                originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(',') // Submit IDs to backend
            });

            const preselected_value = inputField.getAttribute('value')
            if (preselected_value) {
                let selectedValues = preselected_value.split(",");
                let selectedTags = selectedValues.map(value => ({
                    id: value,
                    value: tagifyWhitelist.find(tag => tag.id === value)?.value || value
                }));
                
                tagify.addTags(selectedTags);
            }

            tagify.on('add', function (e) {
                console.log("Tag added:", tagify.value.map(tag => tag.value));
            });
        
            // Handle tag removal
            tagify.on('remove', function (e) {
                console.log("Tag removed:", tagify.value.map(tag => tag.value));
            });
            inputField.tagify = tagify
        } else if (propType == 'object') {
            inputField.classList.add('d-none')
        } else {
            inputField.setAttribute('type', 'text');
        }

    }
</script>