{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<div class="fv-rowd-flex flex-column mb-8">
    <label class="{% include 'data/utility/form-label.html' %}">
        <span class="fs-5 fw-bolder text-active-primary ms-0 mb-0 py-0 border-0">
        {% if LANGUAGE_CODE == 'ja'%}
        案件ID
        {% else %}
        Case ID
        {% endif %}
        </span>
    </label>
    <div class="mb-2">
        <span class="fw-bolder fs-4" >{% if deal.deal_id %}{{deal.deal_id|stringformat:"04d"}} {% else %}"Unknown"{% endif %}</span>        
    </div>
</div>

{% if deal.status == 'archived' %}
    <div class="card-body mt-0 mb-4">
        <div class="fv-rowd-flex flex-column">
            <h3>
                {% if LANGUAGE_CODE == 'ja'%}
                このレコードはアーカイブされています。このレコードを表示および使用するには有効化してください。
                {% else %}
                This record was archived. Please activate to see and use this record.
                {% endif %}
            </h3>
            {% if permission|check_permission:'archive' %}
            <form method="POST" action="{% host_url 'service_manage_deals' id=deal.id host 'app' %}">
                {% csrf_token %}   
                <input class="source-url-input" type="hidden" name="source_url" value="">

                <div class="border-0">
                    <button type="submit"name="restore-deals"  class="btn btn-success">
                        {% if LANGUAGE_CODE == 'ja'%}
                        有効化
                        {% else %}
                        Activate
                        {% endif %}
                    </button>
                </div>
            </form>
            {% endif %}
        </div>
    </div>
    <div class="pe-none user-select-none" hx-get="{% url 'load_drawer_orders' %}" hx-vals='{"drawer_type":"orders", "section":"blurry" }' hx-trigger="load" hx-swap="innerHTML">
    </div>

{% else %}
        
    <form id="deals-form" method="POST" action="{% host_url 'service_manage_deals' deal.id host 'app' %}" class="new-deals" onsubmit="return validateForm(event)" enctype="multipart/form-data">
        {% csrf_token %}
        <input type="hidden" name="view_id" {% if view_id %}value="{{view_id}}"{% endif %} />
        <input class="source-url-input" type="hidden" name="source_url" value="{{source_url}}" />
        <input hidden name="module" value="{{module_slug}}" />

        {% for property in properties.list_all %}
            {% if property == 'name' %}
                <div class="min-w-200px mb-8">
                    <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0 required">
                        {% if LANGUAGE_CODE == 'ja'%}
                        案件名
                        {% else %}
                        Case Name
                        {% endif %}
                    </span>
                    <input required type="text" class="form-control bg-white" placeholder="Profile Name" name="deals_name" value="{{deal.name}}" />
                </div>

            {% elif property == 'customer' %}
                <div class="fv-rowd-flex flex-column mb-8">
                    <div class="d-flex align-items-center mb-2 justify-content-between">
                        <div class="d-flex align-items-center fs-4 fw-bolder">
                            <span class="min-w-100px">
                                {% if LANGUAGE_CODE == 'ja'%}
                                顧客情報
                                {% else %}
                                Customer
                                {% endif %}
                            </span>
                        </div>
                        {% if not hide_associated_data %}
                        <div>
                            <button type="button" class="btn btn-sm btn-light-primary me-2 customer-create-wizard"
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-vals='{"drawer_type":"contacts", "section":"create"}'
                                hx-target="#customer-drawer">
                                <i class="ki-duotone ki-plus fs-2"></i>
                                {% if LANGUAGE_CODE == 'ja'%}連絡先レコードの作成{% else %}Create Contact Record{% endif %}
                            </button>
    
                            <button type="button" class="btn btn-sm btn-light-primary customer-create-wizard"
                                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                                hx-vals='{"drawer_type":"companies", "section":"create"}'
                                hx-target="#customer-drawer">
                                <i class="ki-duotone ki-plus fs-2"></i>
                                {% if LANGUAGE_CODE == 'ja'%}企業レコードの作成{% else %}Create Company Record{% endif %}
                            </button>
    
                        </div>
                        {% endif %}
                    </div>

                    
                    <div class="mb-2">
                        <select id="select-contact_and_company-{{customer_selector_id}}" name="contact_and_company" class="bg-white form-select form-select-solid border h-40px" data-placeholder="{% if LANGUAGE_CODE == 'ja'%}顧客を選択{% else %}Select Customer{% endif %}" data-allow-clear="true">
                            <option value=""></option>

                            {% comment %} Only show currently selected customers, dynamic loading will handle the rest {% endcomment %}
                            {% for contact in selected_contacts %}
                                <option value="{{contact.id}}" selected>#{{contact.contact_id|stringformat:"04d"}} | {{contact|display_contact_name:LANGUAGE_CODE}} ({% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %})</option>
                            {% endfor %}

                            {% for company in selected_companies %}
                                <option value="{{company.id}}" selected>#{{company.company_id|stringformat:"04d"}} | {{company.name}} ({% if LANGUAGE_CODE == 'ja'%}企業{% else %}Company{% endif %})</option>
                            {% endfor %}

                        </select>
                    </div>
                </div>

            {% comment %} association label {% endcomment %}
             {% elif property|lower in association_label_list and property != 'customer'%}  {% comment %} customer already handled by above code {% endcomment %}

                {% comment %} htmx {% endcomment %}
                <div hx-get="{% url 'load_association_label_form' %}" 
                    hx-vals='{"obj_id":"{{deal.id}}", "property":"{{property}}", "page_group_type":"customer_case" }' 
                    hx-trigger="load" hx-swap="outerHTML">
                </div>

            {% elif property == 'case_status' %}
                <div class="fv-rowd-flex flex-column mb-8">
                    <label class="d-flex align-items-center fs-4 fw-bolder mb-2">
                        <span class="min-w-100px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            案件ステータス
                            {% else %}
                            Case Status
                            {% endif %}
                        </span>
                    </label>
                    <div class="mb-2">
                        <select name="case_status" class="bg-white form-select form-select-solid border h-40px select2-this" data-placeholder="{% if LANGUAGE_CODE == 'ja'%}案件ステータス{% else %}Case Status{% endif %}" data-allow-clear="true"> 
                        {% if 'case_status'|get_custom_property_object:deal %}
                            {% with value_map_label='case_status'|get_custom_property_object:deal|get_attr:'value'|string_list_to_list %}
                                {% for value, label in value_map_label.items %}
                                    <option value="{{value}}" {% if deal.case_status == value %}selected{% endif %}>{{label}}</option>
                                {% endfor %}
                            {% endwith %}
                        {% else %}
                            {% for case_status in CASE_STATUS %}
                                <option value="{{case_status.0}}" {% if deal.case_status == case_status.0 %}selected{% endif %}>{% with deals_column=case_status.0|display_column_deals:request %}{{deals_column}}{% endwith %}</option>
                            {% endfor %}
                        {% endif %}
                        </select>
                    </div>
                </div>
            {% elif property == 'owner' %}
                <div class="owner-form-{{object_type}} mb-5">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="min-w-100px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            所有者
                            {% else %}
                            Owner 
                            {% endif %}
                        </span>
                    </label>
                    <select data-allow-clear="true" id="owner-input-{{object_type}}" class="h-40px form-select form-select-solid border bg-white select2-this" data-control="select2" name="owner" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}所有者{% else %}Owner{% endif %}">
                        <option></option>
                        {% if deal.id and deal.owner and deal.owner.user %}
                            <option value="{{deal.owner.user.username}}" selected>
                                {{deal.owner.user.first_name}} - {{deal.owner.user.email}}
                            </option>
                        {% endif %}
                        {% for member in permission|get_scope_from_permission:'edit'|get_users_by_scope:user %}
                            {% if not deal.owner or member != deal.owner.user %}
                            <option value="{{member.username}}">
                                {{member.first_name}} - {{member.email}}
                            </option>
                            {% endif %}
                        {% endfor %}
                    </select>

                    <script>
                        $('#owner-input-{{object_type}}').select2()
                    </script>
                </div>
            {% elif property == 'estimates' %}
                <div class="{% include "data/utility/form-div.html" %}">          
                    <label class="mb-2 d-flex align-items-center fs-4 fw-bold">
                        <span>{% if LANGUAGE_CODE == 'ja'%}見積書{% else %}Estimates{% endif %}</span>
                    </label>
                    
                    <select multiple placeholder="Choose Estimate" class="border min-h-40px form-select select2-estimates-lazy" name="estimates" data-placeholder="{% if LANGUAGE_CODE == 'ja'%}見積書{% else %}Estimate{% endif %}" data-allow-clear="true">
                        {% for estimate in deal.estimates.all %}
                            <option value="{{estimate.id}}" selected>
                                {% if estimate.contact %}
                                    #{{ estimate.id_est|stringformat:"04d" }} | {{estimate.contact.name}}
                                {% elif estimate.company %}
                                    #{{ estimate.id_est|stringformat:"04d" }} | {{estimate.company.name}}
                                {% else %}
                                    #{{ estimate.id_est|stringformat:"04d" }} |
                                {% endif %}
                            </option>
                        {% endfor %}
                    </select>     
                
                    <script>
                        $('.select2-estimates-lazy').select2({
                            ajax: {
                                delay: 250, // wait 250 milliseconds before triggering the request
                                dataType: 'json',
                                url: '{% host_url "get_estimate_object_options" host "app" %}',
                                data: function (params) {
                                        var query = {
                                            q: params.term,
                                            page: params.page || 1,
                                            purpose: "case",
                                            json_response: true,
                                        }
                                        return query;
                                    },
                                minimumInputLength: 2,
                            },
                            language: {
                                "noResults": function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}案件レコードが見つかりません{% else %}No Case Record Found{% endif %}";
                                },
                                searching: function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                                },
                                "loadingMore": function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                                },
                            },
                        })
                    </script>
                
                </div>
            {% elif property == 'invoices' %}

                <div class="{% include "data/utility/form-div.html" %}">          
                    <label class="mb-2 d-flex align-items-center fs-4 fw-bold">
                        <span>{% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoices{% endif %}</span>
                    </label>
                    
                    <select multiple placeholder="Choose Invoice" class="border min-h-40px form-select select2-invoices-lazy" name="invoices" data-placeholder="{% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoice{% endif %}" data-allow-clear="true">
                        {% for invoice in deal.invoices.all %}
                            <option value="{{invoice.id}}" selected>
                                {% if invoice.contact %}
                                    #{{ invoice.id_inv|stringformat:"04d" }} | {{invoice.contact.name}}
                                {% elif invoice.company %}
                                    #{{ invoice.id_inv|stringformat:"04d" }} | {{invoice.company.name}}
                                {% else %}
                                    #{{ invoice.id_inv|stringformat:"04d" }} |
                                {% endif %}
                            </option>
                        {% endfor %}
                    </select>     
                
                    <script>
                        $('.select2-invoices-lazy').select2({
                            ajax: {
                                delay: 250, // wait 250 milliseconds before triggering the request
                                dataType: 'json',
                                url: '{% host_url "get_invoice_object_options" host "app" %}',
                                data: function (params) {
                                        var query = {
                                            q: params.term,
                                            page: params.page || 1,
                                            purpose: "case",
                                            json_response: true,
                                        }
                                        return query;
                                    },
                                minimumInputLength: 2,
                            },
                            language: {
                                "noResults": function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}レコードが見つかりません{% else %}No record found{% endif %}";
                                },
                                searching: function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                                },
                                "loadingMore": function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                                },
                            },
                        })
                    </script>
                
                </div>
            {% elif property == 'tasks' %}

                <div class="{% include "data/utility/form-div.html" %}">          
                    <label class="mb-2 d-flex align-items-center fs-4 fw-bold">
                        <span>{% if LANGUAGE_CODE == 'ja'%}タスク{% else %}Tasks{% endif %}</span>
                    </label>
                    
                    <select id="case-tasks-select" placeholder="Choose Tasks" class="border min-h-40px form-select select2-tasks-lazy" name="tasks" data-placeholder="{% if LANGUAGE_CODE == 'ja'%}タスク{% else %}Tasks{% endif %}" data-allow-clear="true" multiple="multiple">
                        <option value=""></option>
                        {% for task in deal.tasks.all %}
                            <option value="{{task.id}}" selected>
                                {{task.project_target.title}} | #{{ task.task_id|stringformat:"04d" }} - {{task.title}}
                            </option>
                        {% endfor %}
                    </select>     
                
                    <script>
                        $('.select2-tasks-lazy').select2({
                            ajax: {
                                delay: 250, // wait 250 milliseconds before triggering the request
                                dataType: 'json',
                                url: '{% host_url "tasks_options" host "app" %}',
                                data: function (params) {
                                        var query = {
                                            q: params.term,
                                            page: params.page || 1,
                                            json_response: true,
                                        }
                                        return query;
                                    },
                                minimumInputLength: 2,
                            },
                            language: {
                                "noResults": function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}タスクが見つかりません{% else %}No Tasks found{% endif %}";
                                },
                                searching: function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}検索中...{% else %}Searching...{% endif %}";
                                },
                                "loadingMore": function(){
                                    return "{% if LANGUAGE_CODE == 'ja'%}読み込み中...{% else %}Loading more...{% endif %}";
                                },
                            },
                        })
                    </script>
                </div>
                
            {% elif property == 'line_item' %}
                {% if show_line_items %}
                {% with deal.currency as predefined_currency %}
                <span class="{% include "data/utility/form-label.html" %}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    商品項目
                    {% else %}
                    Line Item
                    {% endif %}
                </span>
                <div class="fv-rowd-flex flex-column mb-8">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="min-w-100px">
                            {% if LANGUAGE_CODE == 'ja'%}
                            通貨
                            {% else %}
                            Currency
                            {% endif %}
                        </span>
                    </label>
                    
                    <div class="mb-2">
                        <select id="currency-select" name="currency" class="bg-white form-select form-select-solid border h-40px select2-this" 
                        {% if LANGUAGE_CODE == 'ja'%}
                        data-placeholder="通貨の選択"
                        {% else %}
                        data-placeholder="Select Currency"
                        {% endif %}
                        >  


                        {% if workspace.currencies %}
                            <optgroup
                                {% if LANGUAGE_CODE == 'ja'%}
                                label="デフォルト通貨"
                                {% else %}
                                label="Default Currency"
                                {% endif %}
                            >
                                {% for currency in workspace.currencies|string_list_to_list %}

                                    {% if forloop.counter == 1 %}
                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>                         
                                    {% elif currency == predefined_currency %}
                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>                         
                                    {% else %}
                                        <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>                         
                                    {% endif %}
                                {% endfor %}

                            </optgroup>
                        {% endif %}

                            <optgroup
                                {% if LANGUAGE_CODE == 'ja'%}
                                label="すべての通貨"
                                {% else %}
                                label="All Currency"
                                {% endif %}
                            >
                            {% for currency in currency_model|get_currencies %}
                                {% if not currency.0 in workspace.currencies|string_list_to_list %}
                                    {% if currency.0 == predefined_currency %}
                                    <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                    {% elif not predefined_currency and not workspace.currencies and LANGUAGE_CODE == 'en' and currency.0 == 'USD' %}
                                    <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                    {% elif not predefined_currency and not workspace.currencies and LANGUAGE_CODE == 'ja' and currency.0 == 'JPY' %}
                                    <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                    {% else %}
                                    <option value="{{currency.0}}">{{currency.1}}</option>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                            </optgroup>

                        </select>
                    </div>
                </div>

                <div class="mb-2"
                        id="price"
                        hx-get="{% host_url 'load_case_price_info' host 'app' %}"
                        hx-trigger="load, case-price-item-trigger"
                        hx-target="#case-price-info"
                        hx-swap="innerHTML"
                        hx-vals='js:{"obj_id":"{{deal.id}}", "set_id":"{{set_id}}", "currency":document.getElementById("currency-select").value}'
                    >
                        <div id="case-price-info"></div>
                    
                </div>

                <script>
                    $('#currency-select').on('change', function (e) {
                        document.getElementById('price').dispatchEvent(new Event('case-price-item-trigger'));
                    })
                    function delete_item(elm){
                        elm.parentElement.parentElement.remove()
                    }
                </script>
                {% endwith %}
                {% endif %}

            {% else %}
                {% with CustomFieldName=CustomFieldMap|get_attr:property %}
                    {% if CustomFieldName %}
                    <div class="fv-rowd-flex flex-column mb-8">
                            
                            <span class="{% include "data/utility/form-label.html" %} {% if CustomFieldName.required_field %} required {% endif%}">
                                {{CustomFieldName.name }}
                            </span>

                            {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=CustomFieldName obj=deal object_type=object_type %}

                        </div>
                    {% endif %}
                {% endwith %}
            {% endif %}

        {% endfor %}
            
            
            
        <input hidden name="update" value='{{deal.id}}'>

        {% if permission|check_permission:'edit' %}
        <div class="submit-button-container-{{deal.id}}">
            <button name="post" type="submit" class="btn btn-dark">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </div>
        {% endif %}
        
    </form>
    
{% endif %}

<style>
.submit-button-container-{{deal.id}} {
    position: fixed;
    bottom: 0;
    right: 0;
    padding: 1rem;
    background: white;
    box-shadow: 0 -5px 15px rgba(0,0,0,0.05);
    z-index: 100;

    {% if hide_associated_data %} 
    width:70% !important;
    text-align: left !important;
    {% else %}
    left: 0;
    {% endif %}
}


/* Add padding to the form to prevent the submit button from overlapping content */
#deals-form {
    padding-bottom: 20px;
    position: relative;
}
</style>

<script>
// This script positions the button container to match its parent column width
document.addEventListener('DOMContentLoaded', function() {
    const formElement = document.getElementById('deals-form');
    const buttonContainer = document.querySelector('.submit-button-container');
    
    if (formElement && buttonContainer) {
        function adjustButtonContainer() {
            // Get the width and left position of the form/column
            const formRect = formElement.getBoundingClientRect();
            
            // Set the button container width and left position to match the form
            buttonContainer.style.width = formRect.width + 'px';
            buttonContainer.style.left = formRect.left + 'px';
        }
        
        // Run initially
        adjustButtonContainer();
        
        // Run on window resize
        window.addEventListener('resize', adjustButtonContainer);
    }
});

// Initialize Select2 with AJAX for dynamic customer loading
$(document).ready(function () {
    $('#select-contact_and_company-{{customer_selector_id}}').select2({
        ajax: {
            delay: 250, // wait 250 milliseconds before triggering the request
            dataType: 'json',
            url: '{% host_url "get_customer_options" host "app" %}',
            data: function (params) {
                var query = {
                    q: params.term,
                    page: params.page || 1,
                    json_response: true,
                }
                return query;
            },
            minimumInputLength: 2,
        },
        language: {
            "noResults": function(){
                return "{% if LANGUAGE_CODE == 'ja'%}顧客が見つかりません{% else %}No customers found{% endif %}";
            },
            searching: function(){
                return "{% if LANGUAGE_CODE == 'ja'%}検索中...{% else %}Searching...{% endif %}";
            },
            "loadingMore": function(){
                return "{% if LANGUAGE_CODE == 'ja'%}読み込み中...{% else %}Loading more...{% endif %}";
            },
        },
    });
});
</script>
