{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="card-header" id="kt_help_header">
    <h5 class="{% include "data/utility/card-header.html" %}">
        {% if dataset_obj %}
            {% if LANGUAGE_CODE == 'ja' %}
                データセットを更新
            {% else %}
                Update Dataset
            {% endif %}
        {% else %}
            {% if LANGUAGE_CODE == 'ja' %}
                データセットを作成
            {% else %}
                Create Dataset
            {% endif %}
        {% endif %}
    </h5>
    <div class="card-toolbar">
        <button type="button" class="btn btn-sm btn-icon me-n5" data-kt-drawer-dismiss="true">
            <span class="svg-icon svg-icon-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                    <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                </svg>
            </span>
        </button>
    </div>
</div>

<form method="POST" action="{% host_url 'dataset_form' host 'app' %}">
    <div class="card-body">
        {% csrf_token %}
        <input type="hidden" name="dataset_obj_id" value="{{ dataset_obj.id }}" />
        <div class="mb-5">
            <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0 required">
                {% if LANGUAGE_CODE == 'ja'%}
                名前
                {% else %}
                Name
                {% endif %}
            </span>
            
            <input required class="form-control" type="text" name="name"
                placeholder="{% if LANGUAGE_CODE == 'ja' %}名前{% else %}Name{% endif %}"
                {% if dataset_obj %}
                    value="{{ dataset_obj.name }}"
                {% endif %}
            /> 
        </div>

        <div class="mb-5">
            <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0 required">
                {% if LANGUAGE_CODE == 'ja'%}
                データセット
                {% else %}
                Objects
                {% endif %}
            </span>

            <input required id="object_source_picker" type="text" class="form-control object_source_picker" placeholder="{% if LANGUAGE_CODE == 'ja'%}オブジェクト{% else %}Objects{% endif %}" name="object_source"/ >
        </div>

        <div class="mb-5">
            <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
                {% if LANGUAGE_CODE == 'ja' %}
                カラム
                {% else %}
                Columns
                {% endif %}

                <button type="button" class="ms-4 pt-1 pb-2 px-4 rounded-1 btn btn-light-primary" onclick="addColumn()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4"/>
                    </svg>
                </button>
            </span>

            <div class="column-picker sortable mt-5" id="column-list">
            </div>
        </div>
    </div>

    <div class="card-footer">
        <button type="submit" class="btn btn-dark">
            {% if dataset_obj %}
                {% if LANGUAGE_CODE == 'ja' %}
                    更新
                {% else %}
                    Update
                {% endif %}
            {% else %}
                {% if LANGUAGE_CODE == 'ja' %}
                    作成
                {% else %}
                    Create
                {% endif %}
            {% endif %}
        </button>
    </div>
</form>

<script>
    initObjectSourceData()
    function initObjectSourceData() {
        const options = {{ objects|safe }};

        let tagData = Object.keys(options).map(key => ({
            id: key,
            value: LANGUAGE_CODE == 'ja' ? (options[key]?.ja ?? key) : (options[key]?.en ?? key)
        }));

        var objectSourcePicker = document.getElementById('object_source_picker');
        let tagify = new Tagify(objectSourcePicker, {
            whitelist: tagData,
            enforceWhitelist: true,
            maxTags: tagData.length, // Limit max selections based on available options
            searchKeys: ['value'], // Allow searching by label
            dropdown: {
                maxItems: tagData.length,
                classname: "tagify__inline__suggestions",
                enabled: 0,
                closeOnSelect: false
            },
            originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(',') // Submit IDs to backend
        });

        {% if dataset_obj and dataset_obj.data_source %}
            let selectedTags = [
                {% for obj in dataset_obj.data_source|split:',' %}
                    { id: "{{ obj|escapejs }}", value: tagData.find(tag => tag.id === "{{ obj|escapejs }}")?.value ?? "{{ obj|escapejs }}" },
                {% endfor %}
            ];
            
            tagify.addTags(selectedTags);
        {% endif %}

        tagify.on('add', function (e) {
            updatePrimaryTag(tagify);
            console.log("Tag added:", tagify.value.map(tag => tag.id));
        });
    
        // Handle tag removal
        tagify.on('remove', function (e) {
            updatePrimaryTag(tagify);
            console.log("Tag removed:", tagify.value.map(tag => tag.id));
        });

        var dragsort = new DragSort(tagify.DOM.scope, {
            selector:'.'+tagify.settings.classNames.tag,
            callbacks: {
                dragEnd: function() {
                    tagify.updateValueByDOMTags();
                    updatePrimaryTag(tagify);
                }
            }
        });

        tagify.on('change', function() {
            if (objectSourcePicker.value.trim()) {
                objectSourcePicker.setCustomValidity("");
            } else {
                objectSourcePicker.setCustomValidity("Please select at least one tag.");
            }
        });

        var dragsort = new DragSort(tagify.DOM.scope, {
            selector:'.'+tagify.settings.classNames.tag,
            callbacks: {
                dragEnd: onDragEnd
            }
        })
    
        function onDragEnd(elm){
            tagify.updateValueByDOMTags()
        }
    }

    $('#object_source_picker').on('change', function () {
        updateColumn();
    });

    function updatePrimaryTag(tagify) {
        let primaryText = {% if LANGUAGE_CODE == 'ja' %}"（プライマリ）"{% else %}" (Primary)"{% endif %}
        tagify.DOM.scope.querySelectorAll('.tagify__tag-text').forEach(tag => {
            tag.innerText = tag.innerText.replace(` ${primaryText}`, "");
        });

        let firstTag = tagify.DOM.scope.querySelector('.tagify__tag-text');
        if (firstTag) {
            firstTag.innerText += ` ${primaryText}`;
        }
    }

    function addColumn(preselectedValue=null) {
        const nextIndex = ($('.column-container').length + 1)
        var metricSelectStr = `
            <select class="form-select h-40px select2-this column-picker" id="column-picker-${nextIndex}" name="column-picker-${nextIndex}" data-control="select2" >
        `
        var selectInput = document.getElementById('object_source_picker')
        const selectedValues = selectInput.value
        const selectOptions = getColumnOptions(selectedValues);
        console.log(selectOptions)
        selectOptions.forEach(function (optionVal) {
            metricSelectStr += `
                <option value="${optionVal.value}" ${preselectedValue == optionVal.value ? 'selected': ''}>${LANGUAGE_CODE === 'ja' ? optionVal.display_ja : optionVal.display}</option>
            `
        })
        metricSelectStr += `</select>`

        var metricContainerStr = `
            <div class="d-flex mb-5 column-container" id="column-container-${nextIndex}">
                ${metricSelectStr}
                <button type="button" class="ms-4 px-3 py-1 rounded-1 btn btn-danger" onclick="removeMetric(this)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x" viewBox="0 0 16 16">
                        <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"/>
                    </svg>
                </button>
            </div>
        `;

        console.log(document.querySelector('#column-list'))
        document.querySelector('#column-list').insertAdjacentHTML('beforeend', metricContainerStr);
        $(`#column-picker-${nextIndex}`).select2();
    }

    function getColumnOptions(selectedValues) {
        selectedValues = selectedValues.split(',');
        const columns = {{columns|safe}}
        let combinedOptions = new Set();
        selectedValues.forEach(value => {
            if (value in columns) {
                columns[value].forEach(option => combinedOptions.add(option));
            }
        });

        return Array.from(combinedOptions);
    }

    function updateColumn() {
        var selectInput = document.getElementById('object_source_picker')
        const selectedValues = selectInput.value
        const select_options = getColumnOptions(selectedValues);

        var selectColumn = document.querySelectorAll('[id^="column-picker-"]');

        selectColumn.forEach(function (elem) {
            const currentValue = elem.value;
            while (elem.options.length > 0) {
                elem.remove(0);
            }

            select_options.forEach(function (option_val) {
                const optionElement = document.createElement('option');
                optionElement.value = `${option_val.value}`;
                optionElement.textContent = (LANGUAGE_CODE === 'ja') ? option_val.display_ja : option_val.display;
                if (currentValue == option_val.value) {
                    optionElement.selected  = true
                }

                elem.appendChild(optionElement);
                elem.setAttribute('required','');
            }); 
        })
    }

    setTimeout(() => {
        {% if dataset_obj %}
            {% for metric in dataset_obj.metrics.all %}
                addColumn("{{metric.metric}}")  
            {% endfor %}
        {% else %}
            addColumn()
        {% endif %}
    }, 500)

    function removeMetric(element) {
        childLen = element.parentElement.parentElement.childElementCount
        if (childLen > 1){
            element.parentElement.remove()
        }
    }
</script>