{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="mb-5">
    <div class="w-100 d-flex justify-content-center align-items-center cursor-grab z-index-1 position-relative task-grip">
        {% if page_group_type == 'conversation' or not property.immutable or property.id == 'slip_type' or property.id == 'delivery_status' or property.id == 'status' or property.id == 'job_type' or property.id == 'case_status' %}
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-grip-vertical" viewBox="0 0 16 16">
            <path d="M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z">
            </path>
        </svg>
        {% endif%}
    
        {% if is_line_item %}
        <select class="{% include 'data/utility/select-form.html' %} select2-this" name="field_value"
            {% if LANGUAGE_CODE == 'ja'%}
            placeholder="ラベル" 
            {% else %}
            placeholder="Field" 
            {% endif %}
        >
        {% if show_default_fields %}
            <option value="item" {% if field == 'item' %}selected{% endif %}>
            {% if LANGUAGE_CODE == 'ja' %}商品{% else %}Item{% endif %}
            </option>
            <option value="item_price" {% if field == 'item_price' %}selected{% endif %}>
            {% if LANGUAGE_CODE == 'ja' %}商品価格{% else %}Item Price{% endif %}
            </option>
            <option value="number_of_items" {% if field == 'number_of_items' %}selected{% endif %}>
            {% if LANGUAGE_CODE == 'ja' %}数量{% else %}Number of Items{% endif %}
            </option>
        {% endif %}
        {% for choice in choices %}
            <option value="{{choice.id}}" {% if choice == field %}selected{% endif %}>{{choice.name}}</option>
        {% endfor %}
        </select>

        {% else %}

        <input required class="form-control d-flex w-100 h-100" name="field_value"
            {% if LANGUAGE_CODE == 'ja'%}
            placeholder="ラベル" 
            {% else %}
            placeholder="Field" 
            {% endif %}
            value = '{{field}}'
            oninput="onChangeChoiceOption(this);"
        />
        {% endif %}
    
        {% comment %} {% if not property.immutable or property.id == 'slip_type' %} {% endcomment %}
        <button class="btn btn-danger btn-sm ms-1" onclick='$(this).parent().remove()' type="button">X</button>
        {% comment %} {% endif %} {% endcomment %}
    </div>
    
    <label class="warning-label" style="display: none; color: red;"></label>

    <script>
        $('.select2-this').select2();
    </script>
</div>
