import json
import logging
import re
import traceback
import uuid
from datetime import datetime, timedelta

import pytz
from dateutil import parser
from django.core.paginator import Paginator
from django.db.models import OuterRef, Q, Subquery

logger = logging.getLogger(__name__)

# Signal
from django.http import HttpResponseRedirect
from django.http.response import HttpResponse
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse
from hubspot import HubSpot

from action.action import trigger_next_action
from data.association import set_custom_properties_association
from data.constants.constant import APP_TARGET_SLUG, ORDER_USAGE_CATEGORY, MONTHLY
from data.constants.date_constant import DATE_FORMAT_PARSER
from data.constants.properties_constant import (
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_TASK,
    OBJECT_TYPE_TO_SLUG,
)
from data.constants.shopturbo_constant import TYPE_SHOPIFY_SYNC_KEY_PLATFORM
from data.home import hubspot_order_callback, hubspot_subscription_callback
from data.models import (
    AppSetting,
    AdvanceSearchFilter,
    ShopTurboOrders,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    ShopTurboDecimalPoint,
    PropertySet,
    CustomProperty,
    Module,
    BasePricing,
    ShopTurboOrdersMappingFields,
    Channel,
    TransferHistory,
    Notification,
    View,
    ViewFilter,
    Contact,
    Company,
    ShopTurboItems,
    ShopTurboItemsOrders,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsValueCustomField,
    ShopTurboSubscriptions,
    PurchaseOrders,
    DeliverySlip,
    ShopTurboShippingCost,
    ShopTurboOrderLevelTax,
    CURRENCY_MODEL,
    Task,
    TaskItem,
    Action,
    ActionNode,
    WorkflowActionTracker,
    ContactsNameCustomField,
    CompanyNameCustomField,
    ShopTurboItemsOrdersNameCustomField,
    DealsNameCustomField,
    ShopTurboOrdersPlatforms,
    ExportImportConfiguration,
    Projects
)
from utils.actions import transfer_output_to_target_input
from utils.amazon import *
from utils.bcart import *
from utils.decorator import login_or_hubspot_required
from utils.eccube import *
from utils.ecforce_bg_jobs.ecforce import *
from utils.filter import build_view_filter
from utils.freee_bg_jobs.freee import *
from utils.inventory import *
from utils.bgjobs.runner import trigger_bg_job
from utils.logger import logger
from utils.makeshop import *
from utils.meter import has_quota
from utils.nextengine import *
from utils.project import get_ordered_views
from utils.production import create_bulk_production
from utils.date import parse_date as parse_date_utils
from utils.properties.properties import (
    get_default_prompt_set,
    get_default_property_set,
    get_page_object,
)
from utils.rakuten_bg_jobs.rakuten_orders import (
    import_rakuten_orders,
    pull_rakuten_items,
)
from utils.salesforce.orders import import_salesforce_opportunities
from utils.seal_subscription import *
from utils.serializer import *
from utils.shopify_bg_job.shopify_orders import *
from utils.square import *
from utils.stripe.stripe import *
from utils.utility import (
    build_redirect_url,
    get_item_by_code_col_query,
    get_workspace,
    is_valid_uuid,
    modular_view_filter,
    get_permission_filter,
    assign_object_owner
)
from utils.date import parse_date
from utils.woocommerce import *
from utils.workspace import get_permission
from utils.yahoo import *

from data.orders.background.export_csv_orders import ExportCSVOrdersPayload, export_csv_orders
from data.orders.background.import_orders import (
    ImportOrdersPayload,
    import_orders as hatchet_import_orders,
)
from utils.bgjobs.handler import create_bg_job, add_hatchet_run_id
from data.orders.background.export_orders import export_orders as hatchet_export_orders
from data.orders.background.export_orders.models import ExportOrdersPayload
from data.item.background.import_items import import_items as hatchet_import_items
from data.item.background.import_items.models import ImportItemsPayload
from utils.shopify_bg_job.rest.push_data.push_inventories import push_shopify_inventories
from django.core import serializers

from utils.shopify_bg_job.rest.import_orders.import_shopify_orders import import_shopify_orders
# from utils.hubspot import import_hubspot_orders

type_http = settings.SITE_URL.split("//")[0]
LANGUAGE_QUERY_PARAMETER = "language"
POSTS_PER_PAGE = 30
POSTS_PER_PAGE_BEGIN = POSTS_PER_PAGE - 1

SHOPTURBO_APP_TARGET = "shopturbo"
SHOPTURBO_APP_SLUG = APP_TARGET_SLUG[SHOPTURBO_APP_TARGET]


def trigger_hatchet_import_items(request, workspace, channel, platform, item_mapping=None, key_item_field=None, task_name=None, lang='ja', filter_items=None, sync_method=None):
    """
    Helper function to trigger the new Hatchet-based import items system
    """
    from utils.logger import logger
    
    # Create transfer history
    history = TransferHistory.objects.create(
        workspace=workspace,
        user=request.user,
        status="running",
        type="import_item",
        name=task_name or f"Import {platform} items",
        channel=channel,
    )
    
    # Create payload for the new Hatchet system
    payload = ImportItemsPayload(
        user=str(request.user.id),
        platform=platform,
        channel_id=str(channel.id),
        history_id=str(history.id),
        key_item_field=key_item_field or "",
        item_mapping=json.dumps(item_mapping) if item_mapping else "{}"
    )
    
    # Create background job record
    job_id = create_bg_job(
        workspace=workspace,
        user=request.user,
        job_type="import_item",
        transfer_history=history,
        payload=payload.model_dump(mode="json"),
    )
    payload.background_job_id = job_id
    
    # Log import job parameters for debugging
    logger.info(f"IMPORT_ITEMS_JOB: Starting items import for user {request.user.email} in workspace {workspace.id}")
    logger.info(f"IMPORT_ITEMS_JOB: Channel ID: {channel.id}, Platform: {platform}")
    
    # Execute background task
    ref = None
    try:
        ref = hatchet_import_items.run_no_wait(input=payload)
    except Exception as e:
        logger.error(f"IMPORT_ITEMS_JOB: Exception occurred during import items: {str(e)}", exc_info=True)
        ref = None
    
    is_running = None
    if ref:
        logger.info(f"IMPORT_ITEMS_JOB: Background job submitted successfully for user {request.user.email}")
        add_hatchet_run_id(job_id, ref)
        is_running = True
    else:
        logger.error(f"IMPORT_ITEMS_JOB: Failed to submit background job for user {request.user.email}")
        is_running = False

    if is_running:
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"{platform} 商品のインポートを開始しております。少々お待ちください...",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Importing {platform} items. Please give it a few moment...",
                type="success",
            )
    else:
        logger.error("Import failed. Please contact Sanka support for assistance.")
        history.status = "failed"
        history.error_message = "Import failed. Please contact Sanka support for assistance."
        history.save()
        
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="インポートに失敗しました。サポートにお問い合わせください。",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Import failed. Please contact Sanka support for assistance.",
                type="error",
            )


@login_or_hubspot_required
def shopturbo_orders(request, id=None):
    lang = request.LANGUAGE_CODE
    isopen = None
    if lang == "ja":
        page_title = "受注"
    else:
        page_title = "Order"

    page_obj = get_page_object(TYPE_OBJECT_ORDER, lang)
    base_model = page_obj["base_model"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    custom_value_file_model = page_obj["custom_value_file_model"]
    custom_value_file_relation = page_obj["custom_value_file_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    id_field = page_obj["id_field"]
    item_model = page_obj["item_model"]
    workspace = get_workspace(request.user)

    app_setting, _ = AppSetting.objects.get_or_create(
        workspace=workspace, app_target=SHOPTURBO_APP_TARGET
    )
    # Set Default Search Setting if first time open Orders
    if not app_setting.search_setting_order:
        app_setting.search_setting_order = "customer"
        app_setting.save()
    else:
        # for existing users who have "total_price" in search setting
        if "total_price" in app_setting.search_setting_order:
            app_setting.search_setting_order = app_setting.search_setting_order.replace(
                ",total_price", ""
            )
            app_setting.save()

    if not workspace:
        return redirect(reverse("start", host="app"))

    if request.method == "GET":
        config_view = None
        archive = None
        view_id = request.GET.get("view_id", None)
        tab = request.GET.get("tab", None)

        ORDER_POSTS_PER_PAGE = POSTS_PER_PAGE
        ORDER_PER_PAGE_BEGIN = ORDER_POSTS_PER_PAGE - 1

        order_by = ["-order_id"]

        permission = get_permission(object_type=TYPE_OBJECT_ORDER, user=request.user)
        if not permission:
            permission = "hide"

        if permission == "hide":
            context = {
                "permission": permission,
                "target": TYPE_OBJECT_ORDER,
                "page_title": page_title,
            }
            return render(request, "data/shopturbo/orders.html", context)

        views = get_ordered_views(workspace, TYPE_OBJECT_ORDER, user=request.user)
        view_filter = modular_view_filter(
            workspace,
            TYPE_OBJECT_ORDER,
            view_id=view_id,
            column_view=DEFAULT_COLUMNS_ORDER.copy(),
            user=request.user,
        )

        config_view = view_filter.view_type
        archive = view_filter.archive
        shopturbo_orders_columns = view_filter.column

        pagination_number = view_filter.pagination
        if not pagination_number:
            pagination_number = 20
        ORDER_POSTS_PER_PAGE = pagination_number
        ORDER_PER_PAGE_BEGIN = ORDER_POSTS_PER_PAGE - 1

        # Filter by Search Keywords
        filter_conditions = Q(workspace=workspace)

        filter_conditions &= get_permission_filter(permission, request.user)
        # Handle status filter for archived items
        status = request.GET.get("status")
        if status == "archived":
            filter_conditions &= Q(status="archived")
        else:
            # By default, show only active items
            filter_conditions &= Q(status="active")

        # kanban stuff
        boardStatus = request.GET.get("boardStatus", None)
        if boardStatus and config_view == "kanban":
            ORDER_POSTS_PER_PAGE = 10
            ORDER_PER_PAGE_BEGIN = ORDER_POSTS_PER_PAGE - 1
            default_field_names = [
                field.name for field in ShopTurboOrders._meta.get_fields()
            ]
            if view_filter.choice_customfield in default_field_names:
                if boardStatus == "unlisted":
                    filter_conditions &= Q(
                        **{f"{view_filter.choice_customfield}__isnull": True}
                    ) | Q(**{f"{view_filter.choice_customfield}": ""})
                else:
                    filter_conditions &= Q(
                        **{f"{view_filter.choice_customfield}": boardStatus}
                    )
            else:
                if boardStatus == "unlisted":
                    filter_conditions &= (
                        ~Q(
                            shopturbo_custom_field_relations__field_name__id=view_filter.choice_customfield
                        )
                        | Q(shopturbo_custom_field_relations__isnull=True)
                    ) | (
                        Q(
                            shopturbo_custom_field_relations__field_name__id=view_filter.choice_customfield
                        )
                        & (
                            Q(shopturbo_custom_field_relations__value__isnull=True)
                            | Q(shopturbo_custom_field_relations__value="")
                        )
                    )
                else:
                    filter_conditions &= Q(
                        shopturbo_custom_field_relations__field_name__id=view_filter.choice_customfield,
                        shopturbo_custom_field_relations__value=boardStatus,
                    )

            if view_filter.kanban_order == "manual":
                order_by = ["kanban_order", "-order_id"]
            if view_filter.kanban_order == "created_at":
                order_by = ["-created_at"]

        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_ORDER, type="default"
        ).first()

        if not advance_search:
            advance_search = None
        if advance_search:
            if advance_search.search_settings:
                app_setting.search_setting_order = advance_search.search_settings
                app_setting.save()
        print("search settings:", app_setting.search_setting_order)

        search_q = request.GET.get("q")
        if search_q:
            match_special_char = re.search(r"#(\d+)", search_q)
            if match_special_char:
                number = match_special_char.group(1)  # Get the number part
                if number:
                    filter_conditions &= Q(order_id=number)
            else:
                search_ = search_q.split()
                for search_key in search_:
                    ##### BE CAREFUL TO INCLUDE WORKSPACE ####
                    filter_conditions &= apply_order_search_setting(
                        app_setting, search_key.lower()
                    ) | Q(order_id__icontains=search_key.lower())

        advance_search_filter = None
        if advance_search:
            if advance_search.is_active:
                advance_search_filter = advance_search.search_filter
                advance_search_filter_status = advance_search.search_filter_status
                if advance_search_filter_status:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                        and advance_search_filter_status.get(k) != "False"
                    }
                else:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }
                try:
                    if (
                        advance_search_filter.get("usage_status", {}).get("value")
                        == "all"
                    ):
                        del advance_search_filter["usage_status"]
                except KeyError:
                    pass

                view_filter.filter_value = advance_search_filter

        # Planning to use build_view_filter but seems its still need to check for some conditions
        filter_conditions = apply_shopturboorders_view_filter(
            filter_conditions, view_filter, workspace.timezone
        )

        # filter_conditions = build_view_filter(
        #     filter_conditions, view_filter, TYPE_OBJECT_ORDER, force_filter_list=additional_filter_fields, request=request)
        print("order filter:", filter_conditions)
        try:
            shopturbo_orders = ShopTurboOrders.objects.filter(filter_conditions)
        except:
            shopturbo_orders = ShopTurboOrders.objects.filter(workspace=workspace)

        try:
            if view_filter.sort_order_by:
                sort_order_method = view_filter.sort_order_method
                sort_order_by = view_filter.sort_order_by

                if is_valid_uuid(sort_order_by):
                    field_name = ShopTurboOrdersNameCustomField.objects.filter(
                        id=sort_order_by
                    ).first()
                    if field_name:
                        custom_value_subquery = (
                            ShopTurboOrdersValueCustomField.objects.filter(
                                orders=OuterRef("pk"), field_name=field_name
                            )
                        )

                        if field_name.type in ["date", "date_time"]:
                            custom_value_subquery = custom_value_subquery.values(
                                "value_time"
                            )[:1]
                        else:
                            custom_value_subquery = custom_value_subquery.values(
                                "value"
                            )[:1]

                        shopturbo_orders = shopturbo_orders.annotate(
                            custom_value=Subquery(custom_value_subquery)
                        )

                        if sort_order_method == "asc":
                            shopturbo_orders = shopturbo_orders.distinct(
                                "id", "custom_value"
                            ).order_by("custom_value")
                        else:
                            shopturbo_orders = shopturbo_orders.distinct(
                                "id", "custom_value"
                            ).order_by("-custom_value")
                else:
                    if sort_order_method == "asc":
                        shopturbo_orders = shopturbo_orders.distinct(
                            "id", sort_order_by
                        ).order_by(sort_order_by)
                    else:
                        shopturbo_orders = shopturbo_orders.distinct(
                            "id", sort_order_by
                        ).order_by("-" + sort_order_by)
            else:
                if (
                    not boardStatus and not config_view == "kanban"
                ):  # Execute below code if not in kanban
                    shopturbo_orders = shopturbo_orders.distinct(
                        "id", "order_id"
                    ).order_by(*order_by)
        except Exception as e:
            print("===== Debug Error at ShopTurbo Items =====", e)
            # Create a fresh queryset to avoid any annotation conflicts
            shopturbo_orders = base_model.objects.filter(filter_conditions)
            if (
                not boardStatus and not config_view == "kanban"
            ):  # Execute below code if not in kanban
                shopturbo_orders = shopturbo_orders.distinct("id", "order_id").order_by(
                    *order_by
                )

        decimal_point = ShopTurboDecimalPoint.objects.filter(
            workspace=workspace, app_name="orders"
        ).first()
        try:
            paginator = Paginator(shopturbo_orders, ORDER_POSTS_PER_PAGE)
            page = request.GET.get("page", 1)
            if page:
                page_content = paginator.page(page)
                shopturbo_orders = page_content.object_list
            else:
                page_content = paginator.page(1)
                shopturbo_orders = page_content.object_list
        except:
            page = 1
            shopturbo_orders = None
            page_content = None

        if page == "":
            page = 1
        paginator_item_begin = (ORDER_POSTS_PER_PAGE * int(page)) - ORDER_PER_PAGE_BEGIN
        paginator_item_end = ORDER_POSTS_PER_PAGE * int(page)

        if id:
            isopen = id
            selected_shopturbo_order = ShopTurboOrders.objects.get(id=id)
            if selected_shopturbo_order not in shopturbo_orders:
                len_obj = len(shopturbo_orders) - 1
                shopturbo_orders = shopturbo_orders[:len_obj]
                shopturbo_orders = [selected_shopturbo_order] + list(shopturbo_orders)

        # Property Set
        default_property_set = get_default_property_set(
            TYPE_OBJECT_ORDER, workspace, lang
        )
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_ORDER
        ).order_by("created_at")

        set_id = request.GET.get("set_id")
        if view_filter.view.form:
            set_id = view_filter.view.form.id
        else:
            if not set_id:
                property_set_default = property_sets.filter(as_default=True).first()
                if property_set_default:
                    set_id = property_set_default.id

        selected_order_id = request.GET.get("order_id")
        if not selected_order_id:
            selected_order_id = request.GET.get("id")

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list("id", flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ",".join(str(id) for id in members)

        context = {
            "app_setting": app_setting,
            "page_title": page_title,
            "search_q": search_q,
            "app_slug": SHOPTURBO_APP_SLUG,
            "shopturbo_orders": shopturbo_orders,
            "shopturbo_orders_columns": shopturbo_orders_columns,
            "CURRENCY_MODEL": CURRENCY_MODEL,
            "base_pricing": BasePricing.objects.filter(
                tier=workspace.subscription,
                payment_frequency=MONTHLY,
                is_base_plan=False,
                category=ORDER_USAGE_CATEGORY,
            ).order_by('created_at').first()
            if workspace.subscription != "partner"
            else None,
            "page": page,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "paginator": paginator,
            "decimal_point": decimal_point,
            "config_view": config_view,
            "view_filter": view_filter,
            "view_id": view_id,
            "target": TYPE_OBJECT_ORDER,
            "views": views,
            "isopen": isopen,
            "selected_order_id": selected_order_id,
            "property_sets": property_sets,
            # 'default_property_set': default_property_set,
            "order_id": request.GET.get("id"),  # kanban
            "workspace": workspace,  # Add workspace to context
            "permission": permission,
            "tab": tab,
            "open_drawer": request.GET.get("open_drawer"),
            "status": request.GET.get("status"),
            "set_id": set_id,
            "side_drawer": request.GET.get("sidedrawer", None),
            "advance_search": advance_search,
            "advance_search_filter": advance_search_filter,
            "group_members": members,
        }

        if config_view == "kanban" and boardStatus:
            custom_property = CustomProperty.objects.filter(
                workspace=workspace,
                model=ShopTurboOrders._meta.db_table,
                name="delivery_status",
            ).first()
            if custom_property:
                custom_property.value = ast.literal_eval(custom_property.value)
                property_value_list = list(custom_property.value.keys())
                excl_conditions = (
                    Q(delivery_status__in=property_value_list)
                    | Q(delivery_status__isnull=True)
                    | Q(delivery_status="")
                )
                ShopTurboOrders.objects.filter(workspace=workspace).exclude(
                    excl_conditions
                ).update(delivery_status=None)

            # ast.literal_eval(custom_property.value)
            if "checkbox" in view_filter.column:
                view_filter.column.remove("checkbox")
            context["customfields_map_id"] = (
                {
                    str(ctf.id): ctf
                    for ctf in ShopTurboOrdersNameCustomField.objects.filter(
                        workspace=workspace
                    )
                },
            )
            context["boardStatus"] = boardStatus
            context["view_filter"] = view_filter

            return render(
                request, "data/common/kanban/kanban_order_template.html", context
            )

        return render(request, "data/shopturbo/orders.html", context)

    else:  # Post Request
        print("save mapping")
        print(request.POST)
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        mapping_custom_fields = {}
        mapping_custom_fields_association = {}
        mapping_custom_fields_status = {}
        mapping_custom_fields_conditional = {}
        mapping_contact_custom_fields = {}
        mapping_item_custom_fields = {}

        order_mapping = {}
        order_mapping_association = {}
        order_mapping_status = {}
        order_mapping_conditional = {}
        contact_mapping = {}
        item_mapping = {}

        # Mapping Order
        file_columns = request.POST.getlist("order-file-column", [])
        file_columns_name = request.POST.getlist("order-file-column-name", [])
        sanka_properties = request.POST.getlist("order-sanka-properties", [])
        ignores = request.POST.getlist("order-ignore", [])

        # Mapping Association
        file_columns_association = request.POST.getlist("order-association-file-column", [])
        file_columns_association_name = request.POST.getlist("order-association-file-column-name", [])
        sanka_properties_association = request.POST.getlist("order-association-sanka-properties", [])
        ignores_association = request.POST.getlist("order-association-ignore", [])

        print('file_columns_association:', file_columns_association)
        print('file_columns_association_name:', file_columns_association_name)
        print('sanka_properties_association:', sanka_properties_association)
        print('ignores_association:', ignores_association)

        # Mapping Conditional
        file_columns_conditional = request.POST.getlist(
            "order-file-column-conditional", []
        )
        file_columns_name_conditional = request.POST.getlist(
            "order-file-column-name-conditional", []
        )
        sanka_properties_conditional = request.POST.getlist(
            "order-sanka-properties-conditional", []
        )
        ignores_conditional = request.POST.getlist("order-ignore", [])

        # Mapping Status
        file_columns_status = request.POST.getlist("order-status-file-column", [])
        file_columns_name_status = request.POST.getlist(
            "order-status-file-column-name", []
        )
        sanka_properties_status = request.POST.getlist(
            "order-status-sanka-properties", []
        )

        # Mapping Contact
        contact_file_columns = request.POST.getlist("file-column", [])
        contact_file_columns_name = request.POST.getlist("file-column-name", [])
        contact_sanka_properties = request.POST.getlist("sanka-properties", [])

        # Checking Ignorance
        contact_ignores = request.POST.getlist("ignore", [])

        # Mapping Item
        item_file_columns = request.POST.getlist("item-file-column", [])
        item_sanka_properties = request.POST.getlist("sanka-item-properties", [])
        item_ignores = request.POST.getlist("item-ignore", [])

        # To be shown in drawer mapping
        for idx, file_column in enumerate(file_columns):
            # check ignore
            order_mapping[file_column] = sanka_properties[idx]
            if ignores[idx] == "True":
                continue
            mapping_custom_fields[file_column] = sanka_properties[idx]

        try:
            for idx, file_column in enumerate(file_columns_association):
                # check ignore
                order_mapping_association[file_column] = sanka_properties_association[idx]
                if ignores_association[idx] == "True":
                    continue
                mapping_custom_fields_association[file_column] = sanka_properties_association[idx]
        except:
            pass

        for idx, file_column in enumerate(file_columns_conditional):
            # check ignore
            order_mapping_conditional[file_column] = sanka_properties_conditional[idx]
            if ignores_conditional[idx] == "True":
                continue
            mapping_custom_fields_conditional[file_column] = (
                sanka_properties_conditional[idx]
            )

        for idx, file_column in enumerate(file_columns_status):
            order_mapping_status[file_column] = sanka_properties_status[idx]
            mapping_custom_fields_status[file_column] = sanka_properties_status[idx]

        for idx, file_column in enumerate(contact_file_columns):
            # check ignore
            contact_mapping[file_column] = contact_sanka_properties[idx]

            if contact_ignores[idx] == "True":
                continue
            mapping_contact_custom_fields[file_column] = contact_sanka_properties[idx]

        for idx, file_column in enumerate(item_file_columns):
            # check ignore
            item_mapping[file_column] = item_sanka_properties[idx]
            if item_ignores[idx] == "True":
                continue
            mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

        # save import configuration
        selected_platform = request.POST.get("channel-select-platform", '')
        default_customer_id = request.POST.get("default-customer-id", None)
        default_customer_text = request.POST.get("default-customer-text", None)
        how_to_import = request.POST.get("how-to-import", None)
        how_to_import_hubspot = request.POST.get("how-to-import-hubspot", None)
        how_to_import_customer = request.POST.get("how-to-import-customer", None)
        key_item_field = request.POST.get("update-options", None)
        key_customer_field = request.POST.get("update-options-customer", None)
        import_order_date = request.POST.get("import-order-date", None)
        sync_method = request.POST.get("sync-method", None)

        if import_order_date:
            import_order_date = parse_date_utils(
                import_order_date).strftime('%Y-%m-%d')

        ExportImportConfiguration.objects.update_or_create(
            workspace=workspace,
            object_type=TYPE_OBJECT_ORDER,
            platform=selected_platform,
            defaults={
                "input_data": {
                    "default_customer_id": default_customer_id,
                    "default_customer_text": default_customer_text,
                    "how_to_import": how_to_import,
                    "how_to_import_hubspot": how_to_import_hubspot,
                    "how_to_import_customer": how_to_import_customer,
                    "key_item_field": key_item_field,
                    "key_customer_field": key_customer_field,
                    "import_order_date": import_order_date,
                    "sync_method": sync_method,
                }
            }
        )

        # apply for CSV Download
        if "csv_download" in request.POST:
            if lang == "ja":
                history_name = "受注のエクスポート"
            else:
                history_name = "Export Orders"

            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="export_order",
                name=history_name,
            )

            view_id = request.POST.get("view_id", None)
            shopturbo_orders_columns = request.POST.get("column", None)
            download_level = request.POST.get("download-level", "order-level")
            encoded_format = request.POST.get("encoded_format", "utf-8")

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)

            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }

            if request.POST.get("order_ids"):
                filter_dictionary["id"] = {
                    "key": "include",
                    "value": ",".join(request.POST.getlist("order_ids")),
                }
            filter_dictionary = json.dumps(filter_dictionary)
            
            payload = ExportCSVOrdersPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                view_id=str(view_id),
                history_id=str(history.id),
                shopturbo_orders_columns=shopturbo_orders_columns,
                download_level=download_level,
                filter_dictionary=filter_dictionary,
                encoded_format=encoded_format,
                language=lang,
            )
            
            job_id = create_bg_job(
                workspace,
                request.user,
                "export_csv_orders",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id
            
            # Log export job parameters for debugging
            logger.info(f"EXPORT_JOB: Starting orders export for user {request.user.email} in workspace {workspace.id}")
            logger.info(f"EXPORT_JOB: Export parameters - function: {payload.function}, job_id: {job_id}")
            logger.info(f"EXPORT_JOB: View ID: {view_id}, History ID: {history.id}, Language: {lang}")
            logger.info(f"EXPORT_JOB: Columns: {shopturbo_orders_columns}")
            logger.info(f"EXPORT_JOB: Download level: {download_level}")
            logger.info(f"EXPORT_JOB: Filter dictionary: {filter_dictionary}")
            
            ref = None
            try:
                ref = export_csv_orders.run_no_wait(input=payload)
            except Exception as e:
                logger.error(f"EXPORT_JOB: Exception occurred during export_csv_orders: {str(e)}", exc_info=True)
                ref = None
            
            is_running = None  
            if ref:
                logger.info(f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}")
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                logger.error(f"EXPORT_JOB: Failed to submit background job for user {request.user.email}")
                is_running = False
            
            if is_running:
                logger.info(f"EXPORT_JOB: Successfully submitted orders export job for user {request.user.email}")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, csv will be sent to your email.",
                        type="success",
                    )
            else:
                logger.error(f"EXPORT_JOB: Failed to submit orders export job for user {request.user.email} in workspace {workspace.id}")
                logger.error(f"EXPORT_JOB: trigger_bg_job returned falsy value: {is_running}")
                logger.error(f"EXPORT_JOB: Parameters used: {payload}")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブの送信中にエラーが発生しました。サポートに連絡してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="There is an error while submitting the export job. Please contact support.",
                        type="error",
                    )
                history.delete()

        if "refresh_header" in request.POST:
            channel_id = request.POST.get("channel_id")
            channel = Channel.objects.filter(id=channel_id).first()

            if channel.integration.slug == "hubspot":
                function_type = request.POST.get("function_type", None)
                header_list = []

                default_mapping_field = None
                ordersnamecustomfield = (
                    ShopTurboOrdersNameCustomField.objects.filter(workspace=workspace)
                    .exclude(type__in=["image", "user", "formula"])
                    .values_list("name", flat=True)
                )
                if ordersnamecustomfield:
                    default_mapping_field = ordersnamecustomfield[0]

                header_list = [
                    {
                        "value": "deal_name",
                        "name": "Deal - Deal Name",
                        "name_ja": "取引 - 取引名",
                        "skip": False,
                        "default": "name",
                    },
                    {
                        "value": "deal_stage",
                        "name": "Deal - Deal Stage",
                        "name_ja": "取引 - 取引ステージ",
                        "skip": False,
                        "default": default_mapping_field,
                    },
                ]

                if function_type:
                    access_token = channel.access_token
                    api_client = HubSpot(access_token=access_token)

                    try:
                        token_url = "https://api.hubapi.com/oauth/v1/token"
                        data = {
                            "grant_type": "refresh_token",
                            "client_id": settings.HUBSPOT_CLIENT_ID,
                            "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                            "refresh_token": channel.refresh_token,
                        }
                        response = requests.post(token_url, data=data)
                        token_info = response.json()
                        channel.access_token = token_info.get("access_token")
                        channel.save()

                        access_token = channel.access_token
                        headers = {
                            "Authorization": f"Bearer {access_token}",
                            "Content-Type": "application/json",
                        }

                        deal_properties_url = (
                            "https://api.hubapi.com/properties/v1/deals/properties"
                        )
                        deal_properties_response = requests.get(
                            deal_properties_url, headers=headers
                        )
                        deal_properties_response.raise_for_status()

                        # Process the deal properties
                        deal_properties = deal_properties_response.json()
                        if function_type == "import":
                            deal_inclusions = [
                                "sanka_id",
                                "platform",
                                "platform_id",
                                "hs_lastmodifieddate",
                                "hs_latest_approval_status",
                                "notes",
                                "pipeline",
                                "hs_acv",
                                "hs_arr",
                                "dealtype",
                                "description",
                                "hs_deal_score",
                                "hs_deal_stage_probability",
                                "hs_priority",
                            ]
                        else:
                            deal_inclusions = [
                                "hubspot_owner_id",
                                "sanka_id",
                                "platform",
                                "platform_id",
                                "hs_lastmodifieddate",
                                "notes",
                                "pipeline",
                                "dealtype",
                                "description",
                                "hs_deal_stage_probability",
                                "hs_priority",
                            ]
                        custom_properties = [
                            prop
                            for prop in deal_properties
                            if not prop.get("hubspotDefined", True)
                            or prop.get("name") in deal_inclusions
                        ]
                        for property in custom_properties:
                            if function_type == "import":
                                _exc = ["sanka_id"]
                            else:
                                _exc = ["sanka_id", "platform", "platform_id"]
                            if property.get("name", "") not in _exc:
                                header_list.append(
                                    {
                                        "value": f"{property.get('name', '')}|deal",
                                        "name": f"Deal - {property.get('label', property.get('name', ''))}",
                                        "name_ja": f"取引 - {property.get('label', property.get('name', ''))}",
                                        "skip": False,
                                        "default": default_mapping_field,
                                        "hubspot_default": property.get(
                                            "hubspotDefined"
                                        ),
                                    }
                                )

                        # Line Item
                        header_list.append(
                            {
                                "value": "name|line_item",
                                "name": "Line Item - Name",
                                "name_ja": "商品項目 - 名前",
                                "skip": False,
                                "default": "name",
                            }
                        )
                        header_list.append(
                            {
                                "value": "discount|line_item",
                                "name": "Line Item - Discount",
                                "name_ja": "商品項目 - 割引",
                                "skip": False,
                                "default": "discount",
                            }
                        )
                        product_properties_url = (
                            "https://api.hubapi.com/properties/v1/products/properties"
                        )
                        product_properties_response = requests.get(
                            product_properties_url, headers=headers
                        )
                        product_properties_response.raise_for_status()

                        product_properties = product_properties_response.json()
                        custom_product_properties = [
                            prop
                            for prop in product_properties
                            if not prop.get("hubspotDefined", True)
                        ]
                        for property in custom_product_properties:
                            header_list.append(
                                {
                                    "value": f"{property.get('name', '')}|line_item",
                                    "name": f"Line Item - {property.get('label', property.get('name', ''))}",
                                    "name_ja": f"商品項目 - {property.get('label', property.get('name', ''))}",
                                    "skip": False,
                                    "default": default_mapping_field,
                                    "hubspot_default": property.get(
                                        "hubspotDefined"
                                    ),
                                }
                            )

                        hubspot_defined_inclusions = [
                            "name",
                            "firstname",
                            "email",
                            "phone",
                            "address",
                            "address2",
                            "contact_address",
                            "company_address",
                            "city",
                            "company",
                            "country",
                            "fax",
                            "industry",
                            "jobtitle",
                            "lastmodifieddate",
                            "mobilephone",
                            "state",
                            "website",
                            "zip",
                            "total_revenue",
                            "work_email",
                            "annualrevenue",
                        ]

                        contact_properties_url = (
                            "https://api.hubapi.com/properties/v1/contacts/properties"
                        )
                        contact_properties_response = requests.get(
                            contact_properties_url, headers=headers
                        )
                        contact_properties_response.raise_for_status()

                        contact_properties = contact_properties_response.json()
                        custom_contact_properties = [
                            prop
                            for prop in contact_properties
                            if not prop.get("hubspotDefined", True)
                            or prop.get("name") in hubspot_defined_inclusions
                        ]
                        for property in custom_contact_properties:
                            if property.get("name", "") not in [
                                "sanka_id",
                                "platform",
                                "platform_id",
                            ]:
                                header_list.append(
                                    {
                                        "value": f"{property.get('name', '')}|contact",
                                        "name": f"Contact - {property.get('label', property.get('name', ''))}",
                                        "name_ja": f"連絡先 - {property.get('label', property.get('name', ''))}",
                                        "skip": False,
                                        "default": default_mapping_field,
                                        "hubspot_default": property.get(
                                            "hubspotDefined"
                                        ),
                                    }
                                )

                        company_properties_url = (
                            "https://api.hubapi.com/properties/v1/companies/properties"
                        )
                        company_properties_response = requests.get(
                            company_properties_url, headers=headers
                        )
                        company_properties_response.raise_for_status()

                        company_properties = company_properties_response.json()
                        custom_company_properties = [
                            prop
                            for prop in company_properties
                            if not prop.get("hubspotDefined", True)
                            or prop.get("name") in hubspot_defined_inclusions
                        ]
                        for property in custom_company_properties:
                            if property.get("name", "") not in [
                                "sanka_id",
                                "platform",
                                "platform_id",
                                "email",
                            ]:
                                header_list.append(
                                    {
                                        "value": f"{property.get('name', '')}|company",
                                        "name": f"Company - {property.get('label', property.get('name', ''))}",
                                        "name_ja": f"会社 - {property.get('label', property.get('name', ''))}",
                                        "skip": False,
                                        "default": default_mapping_field,
                                        "hubspot_default": property.get(
                                            "hubspotDefined"
                                        ),
                                    }
                                )
                    except:
                        print()

            hs_header_list, _ = HSHeaderList.objects.get_or_create(
                channel=channel, function_type=function_type
            )
            hs_header_list.header_list = header_list
            hs_header_list.save()
            return HttpResponse(status=200)

        # Import Export Save Mapping
        if "save_mapping" in request.POST:
            print("On save mapping: ", selected_platform)
            platform = request.POST.getlist("platform")
            diff_mapping_order = ["shopify"]
            if platform:
                if len(platform) > 1 and not selected_platform:
                    if platform[1] in diff_mapping_order:
                        platform = platform[1]
                    else:
                        platform = platform[0]
                else:
                    platform = platform[0]

            mapping, _ = ShopTurboOrdersMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )
            mapping_association, _ = ShopTurboOrdersAssociationMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )
            mapping_conditional, _ = (
                ShopTurboOrdersMappingFieldsConditional.objects.get_or_create(
                    workspace=workspace, platform=platform
                )
            )
            mapping_status, _ = (
                ShopTurboOrdersStatusMappingFields.objects.get_or_create(
                    workspace=workspace, platform=platform
                )
            )

            object_type = request.POST.get("object_type", "contact")
            contact_workspace_mapping = None
            if object_type == "contact":
                contact_workspace_mapping, _ = (
                    ContactsMappingFields.objects.get_or_create(
                        workspace=workspace, platform=platform, object_type=object_type
                    )
                )
            else:
                contact_workspace_mapping, _ = (
                    ContactsMappingFields.objects.get_or_create(
                        workspace=workspace, platform=platform, object_type=object_type
                    )
                )

            data = {}
            for i, field in enumerate(order_mapping):
                if "create_new|order" == order_mapping[field] and ignores[i] != "True" or order_mapping[field] == "create_new" and ignores[i] != "True":
                    new_field, _ = ShopTurboOrdersNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    if selected_platform == 'shopify':
                        field_data = {
                            "value": file_columns_name[i] + '|order', "skip": ignores[i]}
                    else:
                        field_data = {
                            "value": file_columns_name[i], "skip": ignores[i]}
                elif (
                    order_mapping[field]
                    in ["create_new|contact", "create_new_choice|contact"]
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|contact"

                    field_type = "text"
                    if order_mapping[field] == "create_new_choice|contact":
                        field_type = "choice"

                    _, _ = ContactsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type=field_type
                    )
                    field_data = {
                        "value": new_column_value, "skip": ignores[i]}
                elif (
                    order_mapping[field]
                    in ["create_new|company", "create_new_choice|company"]
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|company"

                    field_type = "text"
                    if order_mapping[field] == "create_new_choice|company":
                        field_type = "choice"

                    _, _ = CompanyNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type=field_type
                    )
                    field_data = {
                        "value": new_column_value, "skip": ignores[i]}
                elif order_mapping[field] == "create_new|item" and ignores[i] != "True":
                    new_column_value = f"{file_columns_name[i]}|item"
                    new_field, _ = ShopTurboItemsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {
                        "value": new_column_value, "skip": ignores[i]}
                elif (
                    order_mapping[field] == "create_new|line_item"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|line_item"
                    new_field, _ = (
                        ShopTurboItemsOrdersNameCustomField.objects.get_or_create(
                            workspace=workspace, name=file_columns_name[i], type="text"
                        )
                    )
                    field_data = {
                        "value": new_column_value, "skip": ignores[i]}
                else:
                    field_data = {
                        "value": order_mapping[field], "skip": ignores[i]}
                data[field] = field_data

            if "advanced_filter" in request.POST:
                # Advanced Filter Handler (Save Only)
                data = mapping_advanced_filter_handler(
                    request, mapping_type="save_import", input_data=data
                )

            logger.info("SAVING DATA: " + str(data))
            mapping.input_data = data
            
            if mapping_contact_custom_fields and not data: # If data is empty, use mapping_contact_custom_fields
                mapping.input_data = mapping_contact_custom_fields
            elif mapping_custom_fields and not data: # If data is empty, use mapping_custom_fields
                mapping.input_data = mapping_custom_fields

            mapping.save()

            mapping_conditional_data = {}
            for i, field in enumerate(order_mapping_conditional):
                field_data = {
                    "value": order_mapping_conditional[field],
                    "skip": ignores_conditional[i],
                }
                mapping_conditional_data[field] = field_data
            mapping_conditional.input_data = mapping_conditional_data
            mapping_conditional.save()

            mapping_association_data = {}
            for i, field in enumerate(order_mapping_association):
                field_data = {
                    "value": order_mapping_association[field],
                    "skip": ignores_association[i],
                }
                mapping_association_data[field] = field_data
            mapping_association.input_data = mapping_association_data
            mapping_association.save()

            mapping_status_data = {}
            for i, field in enumerate(order_mapping_status):
                field_data = {"value": order_mapping_status[field], "skip": False}
                mapping_status_data[field] = field_data
            mapping_status.input_data = mapping_status_data
            mapping_status.save()

            contact_data = {}
            for i, field in enumerate(contact_mapping):
                if (
                    contact_mapping[field] in ["create_new", "create_new_choice"]
                    and contact_ignores[i] != "True"
                ):
                    field_type = "text"
                    if contact_mapping[field] == "create_new_choice":
                        field_type = "choice"
                    if object_type == "company":
                        _, _ = CompanyNameCustomField.objects.get_or_create(
                            workspace=workspace,
                            name=contact_file_columns_name[i],
                            type=field_type,
                        )
                    else:
                        _, _ = ContactsNameCustomField.objects.get_or_create(
                            workspace=workspace,
                            name=contact_file_columns_name[i],
                            type=field_type,
                        )
                    field_data = {
                        "value": contact_file_columns_name[i],
                        "skip": contact_ignores[i],
                    }
                elif (
                    contact_mapping[field] == "create_new|order"
                    and contact_ignores[i] != "True"
                ):
                    new_column_value = f"{contact_file_columns_name[i]}"
                    new_field, _ = ShopTurboOrdersNameCustomField.objects.get_or_create(
                        workspace=workspace,
                        name=contact_file_columns_name[i],
                        type="text",
                    )
                    field_data = {"value": new_column_value, "skip": contact_ignores[i]}
                else:
                    field_data = {
                        "value": contact_mapping[field],
                        "skip": contact_ignores[i],
                    }
                contact_data[field] = field_data
            print(contact_data)
            contact_workspace_mapping.input_data = contact_data
            contact_workspace_mapping.save()
            return HttpResponse(status=200)

        if "save_cases_mapping" in request.POST:
            platform = request.POST.getlist("platform")
            diff_mapping_order = ["shopify"]
            if platform:
                if len(platform) > 1:
                    if platform[1] in diff_mapping_order:
                        platform = platform[1]
                    else:
                        platform = platform[0]
                else:
                    platform = platform[0]
            print(platform)

            mapping, _ = DealsMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )

            data = {}
            for i, field in enumerate(order_mapping):
                if order_mapping[field] == "create_new" and ignores[i] != "True":
                    new_field, _ = DealsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": file_columns_name[i], "skip": ignores[i]}
                elif (
                    order_mapping[field] == "create_new|contact"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|contact"
                    new_field, _ = ContactsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                elif (
                    order_mapping[field] == "create_new|company"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|company"
                    new_field, _ = CompanyNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                elif order_mapping[field] == "create_new|item" and ignores[i] != "True":
                    new_column_value = f"{file_columns_name[i]}|item"
                    new_field, _ = ShopTurboItemsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                else:
                    field_data = {"value": order_mapping[field], "skip": ignores[i]}
                data[field] = field_data

            mapping.input_data = data
            mapping.save()

            return HttpResponse(status=200)

        if "save_item_mapping" in request.POST:
            mapping, _ = ShopTurboItemsMappingFields.objects.get_or_create(
                workspace=workspace, platform="shopify", mapping_type__isnull=True
            )
            data = {}
            for i, field in enumerate(item_mapping):
                field_data = {"value": item_mapping[field], "skip": item_ignores[i]}
                data[field] = field_data

            mapping.input_data = data
            mapping.save()

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "bulk_delete_orders" in request.POST:  # This is archive , not delete
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = apply_shopturboorders_view_filter(
                    filter_conditions, view_filter, workspace.timezone
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)
            sync_usage(workspace, ORDER_USAGE_CATEGORY)
            objects.update(status="archived")
            sync_usage(workspace, ORDER_USAGE_CATEGORY)

        elif "bulk_restore_orders" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                    request=request,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            sync_usage(workspace, ORDER_USAGE_CATEGORY)
            if has_quota(workspace, ORDER_USAGE_CATEGORY):
                workspace_available_storage = get_workspace_available_storage(
                    workspace, ORDER_USAGE_CATEGORY
                )
                if (
                    workspace_available_storage is None
                    or workspace_available_storage > 0
                ):
                    objects.update(status="active")
                    sync_usage(workspace, ORDER_USAGE_CATEGORY)
                else:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="オーダーの作成に失敗しました。サブスクリプション プランをアップグレードしてクォータを増やすか、既存のオーダーの一部をアーカイブしてスペースを解放してください。",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Order could not be activated due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing orders to free up space.",
                            type="error",
                        )
            else:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="オーダーの作成に失敗しました。サブスクリプション プランをアップグレードしてクォータを増やすか、既存のオーダーの一部をアーカイブしてスペースを解放してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Order could not be activated due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing orders to free up space.",
                        type="error",
                    )

        elif "update-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            view_name = request.POST.get("view_name", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column = request.POST.get("column", None)
            kanban_order_by = request.POST.get("kanban-order-by", None)
            kanban_unlisted = (
                True
                if request.POST.get("kanban_unlisted", False) == "on"
                else request.POST.get("kanban_unlisted", False)
            )
            status_selector = request.POST.get("status-selector", None)
            view_page = request.POST.get("view_page", None)

            order_by = request.POST.get("order-by", None)
            sort_method = request.POST.get("sort-method", None)

            property_set_is_default_input = request.POST.get(
                "property_set_default", None
            )

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if view.is_private and (view.user == request.user):
                    view.is_private = bool(request.POST.get("set-private-view", False))
                    view.user = request.user if view.is_private else None
                    view.save()
            # create
            else:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                view = View.objects.create(
                    workspace=workspace,
                    title=view_name,
                    target=TYPE_OBJECT_ORDER,
                    is_private=is_private,
                    user=user,
                )

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            if property_set_is_default_input:
                default_set = PropertySet.objects.get(id=property_set_is_default_input)
                view.form = default_set
                default_set.as_default = True
                default_set.save()
                # check default
                PropertySet.objects.filter(
                    workspace=default_set.workspace, target=default_set.target
                ).exclude(id=default_set.id).update(as_default=False)

            if view_name:
                view.title = view_name

            if view_table:
                view_filter.view_type = view_table

            if order_by:
                view_filter.sort_order_by = order_by
            else:
                view_filter.sort_order_by = None

            pagination = request.POST.get("pagination", None)
            if pagination:
                view_filter.pagination = pagination
            else:
                view_filter.pagination = 25

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = "asc"
                else:
                    view_filter.sort_order_method = None

            view_filter.kanban_unlisted = kanban_unlisted
            view_filter.archive = archive
            view_filter.kanban_order = kanban_order_by

            # convert to normal List
            column = column.split(",")
            if view and column[0]:
                view_filter.column = column
            else:
                view_filter.column = None

            if view_table and status_selector:
                if view_table == "kanban":
                    view_filter.choice_customfield = status_selector
                else:
                    view_filter.choice_customfield = None

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            # Handle "between" operation (have two filter values)
            filter_values2 = request.POST.getlist("filter_value-2", None)

            customer_filter_values = request.POST.getlist("customer_filter_value", None)

            idx_filter_values2 = 0
            for idx, filter_type in enumerate(filter_types):
                value = filter_values[idx]
                if filter_options[idx] == "between":
                    try:
                        value = (
                            f"{filter_values[idx]}-{filter_values2[idx_filter_values2]}"
                        )
                    except:
                        value = ""
                    idx_filter_values2 += 1

                if filter_type == "customer":
                    value = customer_filter_values

                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": value,
                }
            view_filter.filter_value = filter_dictionary
            view_filter.save()

            if view_page in ["orders"]:
                enable_status = request.POST.get("enable_status", False)
                property_status_default = request.POST.get(
                    "property_status_default", None
                )
                if enable_status and property_status_default:
                    custom_property = CustomProperty.objects.filter(
                        id=property_status_default
                    ).first()
                    if custom_property:
                        view.custom_property = custom_property
                else:
                    view.custom_property = None
            view.save()

            if module:
                if view.title:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + "?view_id="
                        + str(view_filter.view.id)
                    )
                else:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
            return redirect(reverse("main", host="app"))

        elif "update-view-button-delete" in request.POST:
            view_id = request.POST.get("view_id", None)
            if view_id:
                View.objects.get(id=view_id).delete()
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "update_bulk_order" in request.POST:
            account_ids = request.POST.getlist("shopturbo_account_id", None)
            contact_and_company = request.POST.getlist("contact_and_company", None)
            status = request.POST.get("status", None)
            delivery_status = request.POST.get("delivery_status", None)

            for account_id in account_ids:
                if not is_valid_uuid(account_id):
                    continue
                Order = ShopTurboOrders.objects.get(id=account_id)
                if "customer|checkbox" in request.POST:
                    Order.contact = None
                    Order.company = None
                    for contact_and_company_id in contact_and_company:
                        if Contact.objects.filter(id=contact_and_company_id):
                            Order.contact = Contact.objects.get(
                                id=contact_and_company_id
                            )
                            Order.save()

                        elif Company.objects.filter(id=contact_and_company_id):
                            Order.company = Company.objects.get(
                                id=contact_and_company_id
                            )
                            Order.save()

                if "status|checkbox" in request.POST:
                    Order.status = status

                if "delivery_status|checkbox" in request.POST:
                    Order.delivery_status = delivery_status

                Order.save()

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "task_id" in request.POST:
            task = TransferHistory.objects.filter(
                workspace=workspace, id=request.POST.get("task_id")
            ).first()
            if "refresh_task" in request.POST:
                if task.progress == 0 and task.created_at < timezone.now() - timedelta(
                    hours=1
                ):
                    task.status = "failed"
            else:
                task.status = "canceled"

            task.save()

        elif "import_orders" in request.POST:
            # Save Mapping
            platform = request.POST.getlist("platform")
            diff_mapping_order = ["shopify"]
            if platform:
                if len(platform) > 1:
                    if platform[1] in diff_mapping_order:
                        platform = platform[1]
                    else:
                        platform = platform[0]
                else:
                    platform = platform[0]

            mapping, _ = ShopTurboOrdersMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )
            mapping_association, _ = ShopTurboOrdersAssociationMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )
            mapping_status, _ = (
                ShopTurboOrdersStatusMappingFields.objects.get_or_create(
                    workspace=workspace, platform=platform
                )
            )
            contact_workspace_mapping, _ = ContactsMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform, object_type="contact"
            )

            data = {}

            mapping_custom_fields = {}
            mapping_association_custom_fields = {}
            mapping_custom_fields_status = {}
            mapping_contact_custom_fields = {}

            for i, field in enumerate(order_mapping):
                if "create_new" in order_mapping[field] and ignores[i] != "True":
                    new_field, _ = ShopTurboOrdersNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    if platform == 'shopify':
                        field_data = {
                            "value": file_columns_name[i] + '|order', "skip": ignores[i]}
                    else:
                        field_data = {
                            "value": file_columns_name[i], "skip": ignores[i]}
                    sanka_properties[i] = file_columns_name[i]
                elif (
                    order_mapping[field] == "create_new|contact"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|contact"
                    new_field, _ = ContactsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                    sanka_properties[i] = new_column_value
                elif (
                    order_mapping[field] == "create_new|company"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|company"
                    new_field, _ = CompanyNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                    sanka_properties[i] = new_column_value
                elif order_mapping[field] == "create_new|item" and ignores[i] != "True":
                    new_column_value = f"{file_columns_name[i]}|item"
                    new_field, _ = ShopTurboItemsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                    sanka_properties[i] = new_column_value
                elif (
                    order_mapping[field] == "create_new|line_item"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|line_item"
                    new_field, _ = (
                        ShopTurboItemsOrdersNameCustomField.objects.get_or_create(
                            workspace=workspace, name=file_columns_name[i], type="text"
                        )
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                    sanka_properties[i] = new_column_value
                else:
                    field_data = {"value": order_mapping[field], "skip": ignores[i]}

                data[field] = field_data

            if "advanced_filter" in request.POST:
                # Advanced Filter Handler (Save and Run Workflow)
                data = mapping_advanced_filter_handler(
                    request, mapping_type="save_import", input_data=data
                )
            print("Input update data: ", data)
            mapping.input_data = data
            mapping.save()

            association_data = {}
            for i, field in enumerate(order_mapping_association):
                field_data = {"value": order_mapping_association[field], "skip": ignores_association[i]}
                association_data[field] = field_data
            mapping_association.input_data = association_data
            mapping_association.save()

            status_data = {}
            for i, field in enumerate(order_mapping_status):
                field_data = {"value": order_mapping_status[field], "skip": False}
                status_data[field] = field_data
            mapping_status.input_data = status_data
            mapping_status.save()

            contact_data = {}
            for i, field in enumerate(contact_mapping):
                field_data = {
                    "value": contact_mapping[field],
                    "skip": contact_ignores[i],
                }
                contact_data[field] = field_data
            contact_workspace_mapping.input_data = contact_data
            contact_workspace_mapping.save()

            select_integration_ids = request.POST.getlist("select_integration_ids", [])

            file_columns = request.POST.getlist("order-file-column", [])
            ignores = request.POST.getlist("order-ignore", [])

            file_columns_status = request.POST.getlist("order-status-file-column", [])

            contact_file_columns = request.POST.getlist("file-column", [])
            contact_ignores = request.POST.getlist("ignore", [])

            for idx, file_column in enumerate(file_columns):
                # check ignore
                if ignores[idx] == "True":
                    continue
                mapping_custom_fields[file_column] = sanka_properties[idx]

            try:
                for idx, file_column in enumerate(file_columns_association):
                    # check ignore
                    if ignores_association[idx] == "True":
                        continue
                    mapping_association_custom_fields[file_column] = sanka_properties_association[idx]
            except:
                pass

            for idx, file_column in enumerate(file_columns_status):
                mapping_custom_fields_status[file_column] = sanka_properties_status[idx]

            for idx, file_column in enumerate(contact_file_columns):
                # check ignore
                contact_mapping[file_column] = contact_sanka_properties[idx]
                if contact_ignores[idx] == "True":
                    continue
                mapping_contact_custom_fields[file_column] = contact_sanka_properties[
                    idx
                ]

            # Export Filter orders Partial
            filter_id = request.POST.get("filter", None)
            filter_orders = None

            if filter_id:
                order_filter = ShopTurboOrdersNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if order_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        order_value_items = (
                            ShopTurboOrdersValueCustomField.objects.filter(
                                field_name=order_filter, value=value
                            )
                        )
                        if order_value_items:
                            filter_orders = order_value_items.values_list(
                                "orders", flat=True
                            )
                            filter_orders = filter_orders.distinct()

            if filter_orders:
                orders = orders.filter(id__in=filter_orders)
                order_ids = list(map(str, orders.values_list("id", flat=True)))

            print(
                "=== shopturbo.py -- 484 Sync order for select_integration_ids",
                select_integration_ids,
                mapping_custom_fields,
            )
            channels = Channel.objects.filter(
                workspace=workspace, id__in=select_integration_ids
            )
            how_to_import = request.POST.get("how-to-import", None)
            how_to_import_hubspot = request.POST.get("how-to-import-hubspot", None)
            how_to_import_customer = request.POST.get("how-to-import-customer", None)
            key_item_field = request.POST.get("update-options", None)
            key_customer_field = request.POST.get("update-options-customer", None)
            import_order_date = request.POST.get("import-order-date", None)

            hubspot_filter_type = request.POST.getlist("hubspot-filter-type", None)
            hubspot_filter_option = request.POST.getlist("hubspot-filter-option", None)
            hubspot_filter_value = request.POST.getlist("hubspot-filter-value", None)

            import_filter_type = request.POST.getlist("order-import-filter-type", None)
            import_filter_option = request.POST.getlist("order-import-filter-option", None)
            import_filter_value = request.POST.getlist("order-import-filter-value", None)

            if import_order_date:
                import_order_date = parse_date_utils(
                    import_order_date).strftime('%Y-%m-%d')
                
            hubspot_filter_option_status = request.POST.get(
                "hubspot-filter-option-status", None
            )
            hubspot_filter_value_status = request.POST.getlist(
                "hubspot-filter-value-status", None
            )
            hubspot_filter_import = {}
            default_customer = request.POST.get("default-customer", None)
            sync_method = request.POST.get('sync-method', 'platform')

            if hubspot_filter_option_status and hubspot_filter_value_status:
                hubspot_filter_import["order_status"] = {
                    "key": hubspot_filter_option_status,
                    "value": hubspot_filter_value_status,
                }

            if hubspot_filter_type and hubspot_filter_option and hubspot_filter_value:
                try:
                    for idx, filter_type in enumerate(hubspot_filter_type):
                        if (
                            filter_type
                            and hubspot_filter_option[idx]
                            and hubspot_filter_value[idx]
                        ):
                            hubspot_filter_import[filter_type] = {
                                "key": hubspot_filter_option[idx],
                                "value": hubspot_filter_value[idx],
                            }
                except:
                    pass
            
            import_filter = {}
            if import_filter_type and import_filter_option and import_filter_value:
                try:
                    for idx, import_filter_type in enumerate(import_filter_type):
                        if (
                            import_filter_type
                            and import_filter_option[idx]
                            and import_filter_value[idx]
                        ):
                            import_filter[import_filter_type] = {
                                "key": import_filter_option[idx],
                                "value": import_filter_value[idx],
                            }
                except:
                    pass

            default_contact = None
            default_company = None
            try:
                default_contact = Contact.objects.filter(id=default_customer).first()
                default_company = Company.objects.filter(id=default_customer).first()
            except:
                pass

            if default_contact:
                default_customer_text = display_contact_name(default_contact)
                default_customer_id = str(default_contact.id)
            elif default_company:
                default_customer_text = default_company.name
                default_customer_id = str(default_company.id)

            ExportImportConfiguration.objects.update_or_create(
                workspace=workspace,
                object_type=TYPE_OBJECT_ORDER,
                platform=channels[0].integration.slug if channels else '',
                defaults={
                    "input_data": {
                        "default_customer_id": default_customer_id,
                        "default_customer_text": default_customer_text,
                        "how_to_import": how_to_import,
                        "how_to_import_hubspot": how_to_import_hubspot,
                        "how_to_import_customer": how_to_import_customer,
                        "key_item_field": key_item_field,
                        "key_customer_field": key_customer_field,
                        "import_order_date": import_order_date,
                        "sync_method": sync_method,
                    }
                }
            )

            for channel in channels:
                if request.POST.get("data_transfer"):
                    DataTransfer.objects.create(
                        workspace=workspace,
                        user=request.user,
                        channel=channel,
                        import_export_type="import",
                    )

                valid_channel = [
                    "shopify",
                    "square",
                    "amazon",
                    "ecforce",
                    "nextengine",
                    "rakuten",
                    "hubspot",
                    "ec-cube",
                    "makeshop",
                    "yahoo-shopping",
                    "b-cart",
                    "wordpress",
                    "salesforce",
                ]
                if channel.integration.slug in valid_channel:
                    if lang == "ja":
                        task_name = (
                            f"{channel.integration.title_ja} の受注レコードをインポート"
                        )
                    else:
                        task_name = f"Import {channel.integration.title} orders"
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_order",
                        name=task_name,
                        channel = channel or None,
                    )

                    if channel.integration.slug == "nextengine":
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Start Importing Order Records from NextEngine",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="NextEngineからの受注レコードのインポートを開始する",
                                type="success",
                            )
                        res = import_nextengine_orders(
                            channel.id, request, mapping_data=mapping.input_data
                        )

                        history.progress = 100
                        history.status = "completed"
                        history.save()
                    else:
                        if channel.integration.slug in [
                            "hubspot",
                            "makeshop",
                            "amazon",
                            "rakuten",
                            "shopify",
                        ]:
                            if "set-contact-as-company" in request.POST:
                                channel.ms_refresh_token = 1
                                channel.save()
                            else:
                                channel.ms_refresh_token = None
                                channel.save()
                        elif channel.integration.slug == "b-cart":
                            if "set-contact-as-company" in request.POST:
                                channel.following = 1
                                channel.save()
                            else:
                                channel.following = None
                                channel.save()

                        payload = ImportOrdersPayload(
                            user=str(request.user.id),
                            platform=channel.integration.slug,
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields),
                            mapping_association_custom_fields=json.dumps(mapping_association_custom_fields),
                            mapping_contact_custom_fields=json.dumps(mapping_contact_custom_fields),
                            mapping_status_custom_fields=json.dumps(mapping_custom_fields_status),
                            history_id=str(history.id),
                            how_to_import=how_to_import,
                            how_to_import_hubspot=how_to_import_hubspot,
                            how_to_import_customer=how_to_import_customer,
                            key_item_field=key_item_field,
                            key_customer_field=key_customer_field,
                            import_order_date=import_order_date,
                            as_deal=False,
                            lang=lang,
                            last_index="",
                            hubspot_filter_import=json.dumps(hubspot_filter_import),
                            import_filter=json.dumps(import_filter),
                            default_customer=default_customer,
                            background_job_id="",
                            mapping_id=str(mapping.id),
                            sync_method=sync_method,
                        )
                        
                        job_id = create_bg_job(
                            workspace,
                            request.user,
                            "import_orders",
                            transfer_history=history,
                            payload=payload.model_dump(mode="json"),
                        )
                        payload.background_job_id = job_id
                        
                        # Log export job parameters for debugging
                        logger.info(f"EXPORT_JOB: Starting orders import for user {request.user.email} in workspace {workspace.id}")
                        logger.info(f"EXPORT_JOB: Channel ID: {channel.id}, Platform: {channel.integration.slug}")
                        
                        ref = None
                        try:
                            ref = hatchet_import_orders.run_no_wait(input=payload)
                        except Exception as e:
                            logger.error(f"EXPORT_JOB: Exception occurred during import orders: {str(e)}", exc_info=True)
                            ref = None
                        
                        is_running = None  
                        if ref:
                            logger.info(f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}")
                            add_hatchet_run_id(job_id, ref)
                            is_running = True
                        else:
                            logger.error(f"EXPORT_JOB: Failed to submit background job for user {request.user.email}")
                            is_running = False

                        if is_running:
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="受注レコードをインポートしております。少々お待ちください...",
                                    type="success",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="Orders being imported. Please give it a few moment...",
                                    type="success",
                                )
                        else:
                            logger.error(
                                "Import failed. Please contact Sanka support for assistance."
                            )
                            history.status = "failed"
                            history.error_message = (
                                "Import failed. Please contact Sanka support for assistance."
                            )
                            history.save()
                            
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="インポートに失敗しました。サポートにお問い合わせください。",
                                    type="error",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=request.user,
                                    message="Import failed. Please contact Sanka support for assistance.",
                                    type="error",
                                )
                            
                            continue

                        # if settings.LOCAL: # for quick test
                        #     if channel.integration.slug == "shopify":
                        #         import_shopify_orders(
                        #             channel.id, None, mapping_contact_custom_fields
                        #         )
                        #     elif channel.integration.slug == "square":
                        #         import_square_orders(channel.id)
                        #     elif channel.integration.slug == "amazon":
                        #         import_amazon_orders(
                        #             channel.id,
                        #             mapping_custom_fields=mapping_custom_fields,
                        #             key_item_field=key_item_field,
                        #             how_to_import=how_to_import,
                        #             import_filter=import_filter,
                        #         )
                        #     elif channel.integration.slug == "ecforce":
                        #         import_ecforce_orders(
                        #             channel.id,
                        #             mapping_custom_fields=mapping_custom_fields,
                        #             import_order_date=import_order_date,
                        #         )
                        #     elif channel.integration.slug == "nextengine":
                        #         mapping_data = ShopTurboOrdersMappingFields.objects.get(
                        #             id=mapping.id
                        #         ).input_data
                        #         import_nextengine_orders(
                        #             channel.id, request, mapping_data=mapping_data
                        #         )
                        #     elif channel.integration.slug == "rakuten":
                        #         import_rakuten_orders(
                        #             channel.id,
                        #             mapping_custom_fields,
                        #             key_item_field=key_item_field,
                        #             how_to_import=how_to_import,
                        #             import_filter=import_filter,
                        #         )
                        #     elif channel.integration.slug == "hubspot":
                        #         import_hubspot_orders(
                        #             channel.id,
                        #             mapping_custom_fields=mapping_custom_fields,
                        #             how_to_import=how_to_import,
                        #             lang=lang,
                        #         )
                        #     elif channel.integration.slug == "ec-cube":
                        #         import_eccube_orders(channel.id)
                        #     elif channel.integration.slug == "makeshop":
                        #         import_makeshop_orders(
                        #             channel.id,
                        #             mapping_custom_fields,
                        #             key_item_field=key_item_field,
                        #             how_to_import=how_to_import,
                        #             import_order_date=import_order_date,
                        #             import_filter=import_filter,
                        #         )
                        #     elif channel.integration.slug == "yahoo-shopping":
                        #         import_yahoo_shopping_orders(
                        #             request.user,
                        #             channel.id,
                        #             mapping_custom_fields,
                        #             key_item_field=key_item_field,
                        #             how_to_import=how_to_import,
                        #             import_filter=import_filter,
                        #         )
                        #     elif channel.integration.slug == "b-cart":
                        #         import_bcart_orders(channel.id, import_filter=import_filter)
                        #     elif platform == "wordpress":
                        #         import_woocommerce_orders(
                        #             channel.id, mapping_custom_fields
                        #         )
                        #     elif platform == "salesforce":
                        #         import_salesforce_opportunities(
                        #             channel.id,
                        #             field_mapping=mapping_custom_fields,
                        #             user_id=request.user.id,
                        #             mapping_custom_fields_association=mapping_association_custom_fields
                        #         )

            # return HttpResponse(status=200)
            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )
            return HttpResponseRedirect(
                request.META.get("HTTP_REFERER", reverse("main", host="app"))
            )

        elif "export_orders" in request.POST:
            # Save Mapping
            platform = request.POST.getlist("platform")
            diff_mapping_order = ["shopify"]
            if platform:
                if len(platform) > 1:
                    if platform[1] in diff_mapping_order:
                        platform = platform[1]
                    else:
                        platform = platform[0]
                else:
                    platform = platform[0]

            mapping, _ = ShopTurboOrdersMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )
            mapping_association, _ = ShopTurboOrdersAssociationMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )
            mapping_conditional, _ = (
                ShopTurboOrdersMappingFieldsConditional.objects.get_or_create(
                    workspace=workspace, platform=platform
                )
            )
            try:
                contact_workspace_mapping, _ = (
                    ContactsMappingFields.objects.get_or_create(
                        workspace=workspace, platform=platform
                    )
                )
            except:
                contact_workspace_mapping = ContactsMappingFields.objects.filter(
                    workspace=workspace, platform=platform
                ).first()

            data = {}
            for i, field in enumerate(order_mapping):
                field_data = {"value": order_mapping[field], "skip": ignores[i]}
                data[field] = field_data

            if "advanced_filter" in request.POST:  # Export Orders
                # Advanced Filter Handler (Save Only)
                data = mapping_advanced_filter_handler(
                    request, mapping_type="save_export", input_data=data
                )
            mapping.input_data = data
            mapping.save()

            association_data = {}
            for i, field in enumerate(order_mapping_association):
                field_data = {"value": order_mapping_association[field], "skip": ignores_association[i]}
                association_data[field] = field_data
            mapping_association.input_data = association_data
            mapping_association.save()

            mapping_conditional_data = {}
            for i, field in enumerate(order_mapping_conditional):
                field_data = {
                    "value": order_mapping_conditional[field],
                    "skip": ignores_conditional[i],
                }
                mapping_conditional_data[field] = field_data
            mapping_conditional.input_data = mapping_conditional_data
            mapping_conditional.save()

            contact_data = {}
            for i, field in enumerate(contact_mapping):
                field_data = {
                    "value": contact_mapping[field],
                    "skip": contact_ignores[i],
                }
                contact_data[field] = field_data
            contact_workspace_mapping.input_data = contact_data
            contact_workspace_mapping.save()

            print("=== POST ", request.POST)
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            order_ids = request.POST.getlist("order_ids", [])

            file_columns = request.POST.getlist("order-file-column", [])
            sanka_properties = request.POST.getlist("order-sanka-properties", [])
            ignores = request.POST.getlist("order-ignore", [])

            file_columns_conditional = request.POST.getlist(
                "order-file-column-conditional", []
            )
            sanka_properties_conditional = request.POST.getlist(
                "order-sanka-properties-conditional", []
            )
            ignores_conditional = request.POST.getlist("order-ignore", [])

            contact_file_columns = request.POST.getlist("file-column", [])
            contact_sanka_properties = request.POST.getlist("sanka-properties", [])
            contact_ignores = request.POST.getlist("ignore", [])

            if order_ids:
                orders = ShopTurboOrders.objects.filter(
                    workspace=workspace, id__in=order_ids
                )
            else:
                # Use the same filter conditions as the main view
                status = request.GET.get("status")
                if status == "archived":
                    orders = ShopTurboOrders.objects.filter(
                        workspace=workspace, status="archived"
                    ).order_by("-order_id")
                else:
                    orders = ShopTurboOrders.objects.filter(
                        workspace=workspace, status="active"
                    ).order_by("-order_id")

            filter_id = request.POST.get("filter", None)
            nextengine_hubspot_filter = request.POST.get(
                "nextengine-hubspot-filter", None
            )
            filter_orders = None

            if filter_id:
                order_filter = ShopTurboOrdersNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if order_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        order_value_items = (
                            ShopTurboOrdersValueCustomField.objects.filter(
                                field_name=order_filter, value=value
                            )
                        )
                        if order_value_items:
                            filter_orders = order_value_items.values_list(
                                "orders", flat=True
                            )
                            filter_orders = filter_orders.distinct()

            ##################
            if "advanced_filter" in request.POST:
                filter_mapping_list = mapping_advanced_filter_handler(
                    request, mapping_type="load_export", input_data=mapping.input_data
                )
                if filter_mapping_list:
                    for filter_ in filter_mapping_list:
                        try:
                            if is_valid_uuid(filter_["filter_type"]):
                                order_filter = (
                                    ShopTurboOrdersNameCustomField.objects.filter(
                                        id=filter_["filter_type"]
                                    ).first()
                                )
                                if order_filter:
                                    value = filter_["filter_value"]
                                    order_value_items = (
                                        ShopTurboOrdersValueCustomField.objects.filter(
                                            field_name=order_filter, value=value
                                        ).values_list("orders_id", flat=True)
                                    )
                                    if order_value_items:
                                        if not filter_orders:
                                            filter_orders = orders.filter(
                                                id__in=order_value_items
                                            )
                                        else:
                                            filter_orders = filter_orders.filter(
                                                id__in=order_value_items
                                            )

                                    if value == "":
                                        order_value_items = ShopTurboOrdersValueCustomField.objects.filter(
                                            field_name=order_filter, value="None"
                                        )
                            else:
                                if (
                                    filter_["filter_type"] == "order_at"
                                    or filter_["filter_type"] == "updated_at"
                                    or filter_["filter_type"] == "created_at"
                                ):
                                    date_filter = datetime.strptime(
                                        filter_["filter_value"], "%Y-%m-%d"
                                    ).date()
                                    if not filter_orders:
                                        filter_orders = orders.filter(
                                            **{
                                                filter_["filter_type"]
                                                + "__gte": date_filter
                                            }
                                        )
                                    else:
                                        filter_orders = filter_orders.filter(
                                            **{
                                                filter_["filter_type"]
                                                + "__gte": date_filter
                                            }
                                        )
                                elif "|platform_id" in filter_["filter_type"]:
                                    platform_id_ = filter_["filter_type"].split("|")[0]
                                    channel_ = Channel.objects.filter(
                                        id=platform_id_
                                    ).first()
                                    if filter_["filter_logic"] == "is_not_empty":
                                        related_platforms = (
                                            ShopTurboOrdersPlatforms.objects.filter(
                                                order__in=orders,
                                                channel=channel_,
                                                platform_order_id__isnull=False,
                                            ).values_list("order_id", flat=True)
                                        )
                                    else:
                                        related_platforms = (
                                            ShopTurboOrdersPlatforms.objects.filter(
                                                order__in=orders,
                                                channel=channel_,
                                                platform_order_id=filter_[
                                                    "filter_value"
                                                ],
                                            ).values_list("order_id", flat=True)
                                        )

                                    if not filter_orders:
                                        filter_orders = orders.filter(
                                            id__in=related_platforms
                                        )
                                    else:
                                        filter_orders = filter_orders.filter(
                                            id__in=related_platforms
                                        )
                                else:
                                    if not filter_orders:
                                        filter_orders = orders.filter(
                                            **{
                                                filter_["filter_type"]: filter_[
                                                    "filter_value"
                                                ]
                                            }
                                        ).values_list("id", flat=True)
                                    else:
                                        filter_orders = filter_orders.filter(
                                            **{
                                                filter_["filter_type"]: filter_[
                                                    "filter_value"
                                                ]
                                            }
                                        ).values_list("id", flat=True)
                                    if filter_["filter_value"] == "":
                                        if not filter_orders:
                                            filter_orders = orders.filter(
                                                **{filter_["filter_type"]: "None"}
                                            ).values_list("id", flat=True)
                                        else:
                                            filter_orders = filter_orders.filter(
                                                **{filter_["filter_type"]: "None"}
                                            ).values_list("id", flat=True)

                        except:
                            pass

                if filter_orders:
                    orders = filter_orders
                    order_ids = list(map(str, orders.values_list("id", flat=True)))
                    print(list(map(str, orders.values_list("order_id", flat=True))))

            ##################

            min_id = request.POST.get("min_id", None)
            max_id = request.POST.get("max_id", None)
            try:
                if min_id and max_id:
                    orders = orders.filter(order_id__gte=min_id, order_id__lte=max_id)
                else:
                    if min_id:
                        orders = orders.filter(order_id__gte=min_id)
                    if max_id:
                        orders = orders.filter(order_id__lte=max_id)
                order_ids = list(map(str, orders.values_list("id", flat=True)))
            except:
                pass

            if filter_orders:
                filter_orders = filter_orders.distinct()
                orders = orders.filter(id__in=filter_orders)
                order_ids = list(map(str, orders.values_list("id", flat=True)))

            hubspot_export_platform_match = request.POST.getlist(
                "hubspot-export-platform-match", None
            )
            if hubspot_export_platform_match:
                shopturbo_order_platforms = ShopTurboOrdersPlatforms.objects.filter(
                    order__in=orders,
                    channel__integration__slug__in=hubspot_export_platform_match,
                )
                order_ids = list(
                    map(
                        str,
                        shopturbo_order_platforms.values_list("order_id", flat=True),
                    )
                )

            mapping_custom_fields = {}
            mapping_association_custom_fields = {}
            mapping_custom_fields_conditional = {}
            mapping_contact_custom_fields = {}

            for idx, file_column in enumerate(file_columns):
                # check ignore
                if ignores[idx] == "True":
                    continue
                mapping_custom_fields[file_column] = sanka_properties[idx]

            try:
                for idx, file_column in enumerate(file_columns_association):
                    # check ignore
                    if ignores_association[idx] == "True":
                        continue
                    mapping_association_custom_fields[file_column] = sanka_properties_association[idx]
            except:
                pass

            for idx, file_column in enumerate(file_columns_conditional):
                # check ignore
                if ignores_conditional[idx] == "True":
                    continue
                mapping_custom_fields_conditional[file_column] = (
                    sanka_properties_conditional[idx]
                )

            for idx, file_column in enumerate(contact_file_columns):
                # check ignore
                contact_mapping[file_column] = contact_sanka_properties[idx]
                if contact_ignores[idx] == "True":
                    continue
                mapping_contact_custom_fields[file_column] = contact_sanka_properties[
                    idx
                ]

            print(
                "=== shopturbo.py -- 889 Export order for select_integration_ids",
                select_integration_ids,
                order_ids,
                mapping_custom_fields,
            )
            print("conditional:", mapping_custom_fields_conditional)
            channels = Channel.objects.filter(
                workspace=workspace, id__in=select_integration_ids
            )
            for channel in channels:
                # Create task name based on language
                if lang == "ja":
                    task_name = f"{channel.integration.slug} の受注レコードをエクスポート"
                else:
                    task_name = f"Export {channel.integration.slug} orders"
                
                # Create transfer history
                history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="export_order",
                    name=task_name,
                    channel=channel or None,
                )
                
                # Handle platform-specific fields
                additional_action = ""
                filter_output = ""
                update_hubspot_list = []
                set_contact_as_company = False
                
                if channel.integration.slug == "nextengine":
                    if nextengine_hubspot_filter == "true":
                        additional_action = request.POST.get("additional-action-channel-choice", "")
                        filter_output = request.POST.get("filter-output", "")
                        update_hubspot_list = [additional_action, filter_output]
                elif channel.integration.slug == "hubspot":
                    if "set-contact-as-company" in request.POST:
                        channel.ms_refresh_token = 1
                        channel.save()
                        set_contact_as_company = True
                    else:
                        channel.ms_refresh_token = None
                        channel.save()
                
                # Initialize variables that may not be defined in this context
                mapping_association_custom_fields = locals().get('mapping_association_custom_fields', None)
                mapping_status_custom_fields = locals().get('mapping_status_custom_fields', None)
                
                # Create payload for Hatchet export orders job
                payload = ExportOrdersPayload(
                    user=str(request.user.id),
                    platform=channel.integration.slug,
                    channel_id=str(channel.id),
                    order_ids=order_ids,
                    mapping_association_custom_fields=json.dumps(mapping_association_custom_fields) if mapping_association_custom_fields else None,
                    mapping_custom_fields=json.dumps(mapping_custom_fields) if mapping_custom_fields else None,
                    mapping_contact_custom_fields=json.dumps(mapping_contact_custom_fields) if mapping_contact_custom_fields else None,
                    mapping_status_custom_fields=json.dumps(mapping_status_custom_fields) if mapping_status_custom_fields else None,
                    history_id=str(history.id),
                    lang=lang,
                    background_job_id="",
                    mapping_id=str(mapping.id) if mapping else "",
                    # Platform-specific fields
                    additional_action=additional_action,
                    filter_output=filter_output,
                    update_hubspot=update_hubspot_list,
                    set_contact_as_company=set_contact_as_company
                )
                
                # Create background job
                job_id = create_bg_job(
                    workspace,
                    request.user,
                    "export_orders",
                    transfer_history=history,
                    payload=payload.model_dump(mode="json"),
                )
                payload.background_job_id = job_id
                
                # Log export job parameters for debugging
                logger.info(f"EXPORT_JOB: Starting orders export for user {request.user.email} in workspace {workspace.id}")
                logger.info(f"EXPORT_JOB: Channel ID: {channel.id}, Platform: {channel.integration.slug}")
                
                ref = None
                try:
                    ref = hatchet_export_orders.run_no_wait(input=payload)
                except Exception as e:
                    logger.error(f"EXPORT_JOB: Exception occurred during export orders: {str(e)}", exc_info=True)
                    ref = None
                
                is_running = None  
                if ref:
                    logger.info(f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}")
                    add_hatchet_run_id(job_id, ref)
                    is_running = True
                else:
                    logger.error(f"EXPORT_JOB: Failed to submit background job for user {request.user.email}")
                    is_running = False

                if is_running:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message=f"{channel.integration.slug}の受注レコードをエクスポートしております。少々お待ちください...",
                            type="success",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message=f"Exporting orders to {channel.integration.slug}. Please give it a few moment...",
                            type="success",
                        )
                else:
                    logger.error("Export failed. Please contact Sanka support for assistance.")
                    history.status = "failed"
                    history.error_message = "Export failed. Please contact Sanka support for assistance."
                    history.save()
                    
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="エクスポートに失敗しました。サポートにお問い合わせください。",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Export failed. Please contact Sanka support for assistance.",
                            type="error",
                        )
                        
            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )
            return HttpResponseRedirect(
                request.META.get("HTTP_REFERER", reverse("main", host="app"))
            )

        elif "import_subscriptions" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])

            channels = Channel.objects.filter(
                workspace=workspace, id__in=select_integration_ids
            )
            print(
                "=== shopturbo.py -- 484 Sync subscription for select_integration_ids",
                channels,
            )
            for channel in channels:
                if channel.integration.slug == "seal-subscription":
                    try:
                        import_seal_subscriptions(channel.id)
                    except Exception as e:
                        if str(e) == "client-error":
                            if lang == "ja":
                                message = f"チャネル {channel.name} :Seal Subscription APIが有効になっていません"
                            else:
                                message = f"Channel {channel.name} : The Seal Subscription API is not enable"
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=message,
                                type="error",
                            )

            # return HttpResponse(status=200)
            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "export_subscriptions" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            subscription_ids = request.POST.getlist("subscription_ids", [])
            channels = Channel.objects.filter(
                workspace=workspace, id__in=select_integration_ids
            )
            for channel in channels:
                if channel.integration.slug == "seal-subscription":
                    export_seal_subscriptions(
                        request.user, channel.id, subscription_ids
                    )
                elif channel.integration.slug == "hubspot":
                    param = {
                        "function": "hubspot_push_subscriptions",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--subscription_ids={subscription_ids}",
                            f"--lang={lang}",
                            f"--user={request.user.id}",
                        ],
                    }
                    is_running = trigger_bg_job(param)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Hubspotのサブスクリプションをエクスポートしました",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Export Hubspot subscriptions success",
                                type="success",
                            )
                    else:
                        hubspot_subscription_callback(
                            request, sub_ids=subscription_ids, channel_id=channel.id
                        )

            # return HttpResponse(status=200)
            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "sync_items" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            print(
                "=== shopturbo.py -- 320 Sync item for select_integration_ids",
                select_integration_ids,
            )

            filter_id = request.POST.get("filter", None)
            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            print("=== shopturbo.py -- 322 Sync item for filter_items", filter_items)

            channels = Channel.objects.filter(id__in=select_integration_ids)
            for channel in channels:
                print("=== shopturbo.py -- 377 Pull items for channel", channel.id)
                if lang == "ja":
                    task_name = f"({channel.integration.slug} - {channel.name}) の商品をインポート"
                else:
                    task_name = f"Import ({channel.integration.slug} - {channel.name}) items"

                # Check if another import job is already running for this channel
                running_history = TransferHistory.objects.filter(
                    workspace=workspace, status="running", type="import_item", channel=channel
                ).first()
                if running_history:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="他の商品インポートジョブが実行中です。キャンセルするか、完了するまで待ちます",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Another item import job is running. Cancel or Wait until its completed",
                            type="error",
                        )
                elif channel.integration.slug == "shopify":
                    # Use new Hatchet-based import system
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="shopify",
                        item_mapping=item_mapping,
                        task_name=task_name,
                        lang=lang,
                        filter_items=filter_items,
                        sync_method=request.POST.get('sync-key', TYPE_SHOPIFY_SYNC_KEY_PLATFORM)
                    )
                elif channel.integration.slug == "square":
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="square",
                        task_name=task_name,
                        lang=lang
                    )
                elif channel.integration.slug == "amazon":
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="amazon",
                        task_name=task_name,
                        lang=lang
                    )
                elif channel.integration.slug == "stripe":
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="stripe",
                        task_name=task_name,
                        lang=lang
                    )
                elif channel.integration.slug == "ecforce":
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="ecforce",
                        task_name=task_name,
                        lang=lang
                    )
                elif channel.integration.slug == "rakuten":
                    # Use new Hatchet-based import system
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="rakuten",
                        item_mapping=item_mapping,
                        task_name=task_name,
                        lang=lang
                    )
                elif channel.integration.slug == "freee":
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="freee",
                        task_name=task_name,
                        lang=lang
                    )
                elif channel.integration.slug == "ec-cube":
                    # Use new Hatchet-based import system
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="ec-cube",
                        task_name=task_name,
                        lang=lang
                    )
                elif channel.integration.slug == "makeshop":
                    # Use new Hatchet-based import system
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="makeshop",
                        item_mapping=item_mapping,
                        task_name=task_name,
                        lang=lang
                    )
                elif channel.integration.slug == "yahoo-shopping":
                    # Use new Hatchet-based import system
                    trigger_hatchet_import_items(
                        request=request,
                        workspace=workspace, 
                        channel=channel,
                        platform="yahoo-shopping",
                        task_name=task_name,
                        lang=lang
                    )

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "sync_inventories" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            print(
                "=== shopturbo.py -- Sync Inventories for select_integration_ids",
                select_integration_ids,
            )
            channels = Channel.objects.filter(id__in=select_integration_ids)

            filter_id = request.POST.get("filter", None)
            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            print("=== shopturbo.py -- 322 Sync item for filter_items", filter_items)

            for channel in channels:
                print("=== shopturbo.py -- Pull Inventories for channel", channel.id)
                if channel.integration.slug == "shopify":
                    item_mapping_str = json.dumps(item_mapping)
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_inventory",
                        channel = channel or None,
                    )

                    param = {
                        "function": "shopify_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--item_mapping={item_mapping_str}",
                            f"--sync_method={request.POST.get('sync-key', TYPE_SHOPIFY_SYNC_KEY_PLATFORM)}",
                            f"--filter_items={filter_items}",
                            f"--history_id={str(history.id)}",
                            f"--language={request.user.verification.language if request.user.verification.language else 'ja'}",
                        ],
                    }

                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify 商品のインポート ジョブが正常に送信されました。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items import job submitted successfully.",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify商品のインポートに失敗しました。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items import failed.",
                                type="error",
                            )

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "sync_transactions" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            print(
                "=== shopturbo.py -- Sync Transactions for select_integration_ids",
                select_integration_ids,
            )
            channels = Channel.objects.filter(id__in=select_integration_ids)

            filter_id = request.POST.get("filter", None)
            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            print("=== shopturbo.py -- 322 Sync item for filter_items", filter_items)

            for channel in channels:
                print("=== shopturbo.py -- Pull Transactions for channel", channel.id)
                if channel.integration.slug == "shopify":
                    item_mapping_str = json.dumps(item_mapping)
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_inventory_transaction",
                        channel = channel or None,
                    )

                    param = {
                        "function": "shopify_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--item_mapping={item_mapping_str}",
                            f"--sync_method={request.POST.get('sync-key', TYPE_SHOPIFY_SYNC_KEY_PLATFORM)}",
                            f"--filter_items={filter_items}",
                            f"--history_id={str(history.id)}",
                            f"--language={request.user.verification.language if request.user.verification.language else 'ja'}",
                        ],
                    }

                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify 商品のインポート ジョブが正常に送信されました。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items import job submitted successfully.",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify商品のインポートに失敗しました。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items import failed.",
                                type="error",
                            )

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "sync_locations" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            print(
                "=== shopturbo.py -- Sync Locations for select_integration_ids",
                select_integration_ids,
            )
            channels = Channel.objects.filter(id__in=select_integration_ids)

            filter_id = request.POST.get("filter", None)
            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            print("=== shopturbo.py -- 322 Sync item for filter_items", filter_items)

            for channel in channels:
                print("=== shopturbo.py -- Pull Locations for channel", channel.id)
                if channel.integration.slug == "shopify":
                    item_mapping_str = json.dumps(item_mapping)
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_inventory_warehouse",
                        channel = channel or None,
                    )

                    param = {
                        "function": "shopify_import_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--item_mapping={item_mapping_str}",
                            f"--sync_method={request.POST.get('sync-key', TYPE_SHOPIFY_SYNC_KEY_PLATFORM)}",
                            f"--filter_items={filter_items}",
                            f"--history_id={str(history.id)}",
                            f"--language={request.user.verification.language if request.user.verification.language else 'ja'}",
                        ],
                    }

                    is_running = trigger_bg_job(param, transfer_history=history)
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify 商品のインポート ジョブが正常に送信されました。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items import job submitted successfully.",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify商品のインポートに失敗しました。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items import failed.",
                                type="error",
                            )

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "push_items" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            # print("=== shopturbo.py -- 320 Push item for select_integration_ids", select_integration_ids, len(select_integration_ids), type(select_integration_ids))
            item_ids = request.POST.getlist("item_ids", [])
            if len(select_integration_ids) == 0:
                return HttpResponse(status=200)
            channels = Channel.objects.filter(id__in=select_integration_ids)

            filter_id = request.POST.get("filter", None)
            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            print("=== shopturbo.py -- 322 Sync item for filter_items", filter_items)

            if item_ids:
                items = ShopTurboItems.objects.filter(
                    workspace=workspace, id__in=item_ids
                )
                inventories = ShopTurboInventory.objects.filter(item__in=items)
            else:
                items = ShopTurboItems.objects.filter(
                    workspace=workspace, status="active"
                )
                inventories = ShopTurboInventory.objects.filter(
                    item__in=items, workspace=workspace
                )

            if filter_items:
                items = items.filter(id__in=filter_items)
                inventories = inventories.filter(item__in=items)

            for channel in channels:
                # print("=== shopturbo.py -- 377 Push items for channel", channel.id)
                if channel.integration.slug == "shopify":
                    items = []
                    for item_id in item_ids:
                        items.append(item_id)

                    items = ",".join(items)
                    filter_items = ",".join(filter_items)

                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="export_item",
                        channel = channel or None,
                    )

                    param = {
                        "function": "shopify_export_items",
                        "job_id": str(uuid.uuid4()),
                        "workspace_id": str(workspace.id),
                        "user_id": str(request.user.id),
                        "args": [
                            f"--channel_id={channel.id}",
                            f"--workspace_id={str(workspace.id)}",
                            f"--item_ids={items}",
                            f"--filter_items={filter_items}",
                            f"--history_id={str(history.id)}",
                        ],
                    }

                    is_running = trigger_bg_job(param, transfer_history=history)

                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify 商品のエクスポートジョブが正常に送信されました。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items export job submitted successfully.",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify商品のエクスポートに失敗しました。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Shopify items export failed.",
                                type="error",
                            )

                elif channel.integration.slug == "ecforce":
                    push_ecforce_items(items, channel.id)
                elif channel.integration.slug == "yahoo-shopping":
                    push_yahoo_shopping_items(items, channel.id)
                # elif channel.integration.slug == 'freee':
                #     push_freee_items(items, channel.id)
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "push_inventories" in request.POST:
            select_integration_ids = request.POST.getlist("select_integration_ids", [])
            # print("=== shopturbo.py -- 320 Push item for select_integration_ids", select_integration_ids, len(select_integration_ids), type(select_integration_ids))
            inventory_ids = request.POST.getlist("inventory_ids", [])
            if len(select_integration_ids) == 0:
                return HttpResponse(status=200)
            channels = Channel.objects.filter(id__in=select_integration_ids)

            filter_id = request.POST.get("filter", None)
            filter_items = None
            if filter_id:
                cf_filter = ShopTurboItemsNameCustomField.objects.filter(
                    id=filter_id
                ).first()
                if cf_filter:
                    value = request.POST.get("filter-choice", None)
                    if value:
                        cf_value_items = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=cf_filter, value=value
                        )
                        if cf_value_items:
                            filter_items = cf_value_items.values_list(
                                "items", flat=True
                            )
                            filter_items = filter_items.distinct()
            print("=== shopturbo.py -- 322 Sync item for filter_items", filter_items)

            if inventory_ids:
                inventories = ShopTurboInventory.objects.filter(id__in=inventory_ids)
            else:
                inventories = ShopTurboInventory.objects.filter(
                    workspace=workspace, status="active"
                )

            if filter_items:
                inventories = inventories.filter(item__in=filter_items)

            for channel in channels:
                if channel.integration.slug == "shopify":
                    for inventory in inventories:
                        push_shopify_inventories(channel.id, inventory)
                elif channel.integration.slug == "yahoo-shopping":
                    push_yahoo_shopping_inventories(channel.id, inventories)

            if module:
                return reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            return redirect(reverse("main", host="app"))

        elif "bulk_duplicate" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                # Check if view_id is a valid UUID before using it in the filter
                if view_id and is_valid_uuid(view_id):
                    view = View.objects.filter(id=view_id).first()
                else:
                    view = None

                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                    request=request,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            for obj in objects:
                obj_item = item_model.objects.filter(**{"order": obj}).order_by(
                    "created_at"
                )
                customFields = custom_value_model.objects.filter(
                    **{custom_value_relation: obj}
                )

                obj.id = None
                setattr(obj, id_field, None)
                obj.created_at = timezone.now()
                obj.save()

                for field in customFields:
                    old_field_id = field.id
                    field.id = None
                    setattr(field, custom_value_relation, obj)
                    field.save()

                    # duplicate file or image
                    customFieldsValueFile = custom_value_file_model.objects.filter(
                        **{f"{custom_value_file_relation}__id": old_field_id}
                    )
                    if customFieldsValueFile:
                        for value_file in customFieldsValueFile:
                            value_file.id = None
                            value_file.created_at = timezone.now()
                            setattr(value_file, custom_value_file_relation, field)
                            value_file.save()

                if obj_item:
                    for item_ in obj_item:
                        item_.id = None
                        item_.created_at = timezone.now()
                        setattr(item_, "order", obj)
                        item_.save()

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?view_id={str(request.POST.get('view_id'))}&id={id}&target=commerce_orders"
            )
        return redirect(reverse("main", host="app"))


def update_order_price(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        order_id = request.GET.get("order_ids")
        module = request.GET.get("module")
        if not order_id:
            return HttpResponse(status=400)

        try:
            order = ShopTurboOrders.objects.get(id=order_id)
        except ShopTurboOrders.DoesNotExist:
            print("Order does not exist")
            return HttpResponse(status=404)

        company_custom_properties = CompanyNameCustomField.objects.filter(
            workspace=workspace, type="number", number_format__in=["number", "%"]
        )
        context = {
            "order_id": order_id,
            "company_custom_properties": company_custom_properties,
            "module": module,
        }
        return render(request, "data/shopturbo/orders/update-order-price.html", context)

    # POST
    print(request.POST)
    order_id = request.POST.get("order_id")
    company_custom_prop_id = request.POST.get("company_custom_prop")
    module_slug = request.POST.get("module")
    if not (order_id and company_custom_prop_id):
        print("Require order_id and company_custom_prop_id")
        return HttpResponse(status=400)

    try:
        order = ShopTurboOrders.objects.get(id=order_id)
    except ShopTurboOrders.DoesNotExist:
        print("Order does not exist")
        return HttpResponse(status=404)

    object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
    if not module_slug:
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug

    if not order.company:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": object_slug},
            )
            + f"?id={order_id}"
        )

    company_prop_value = CompanyValueCustomField.objects.filter(
        company=order.company,
        field_name__id=company_custom_prop_id,
        field_name__type="number",
        field_name__number_format__in=["number", "%"],
    ).first()
    if not company_prop_value or (
        company_prop_value and company_prop_value.value_number is None
    ):
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": object_slug},
            )
            + f"?id={order_id}"
        )

    if company_prop_value.field_name.number_format == "number":
        discount = company_prop_value.value_number
        if company_prop_value.value_number > 0:
            discount = 1
    else:
        # For percentage format, we need to apply the discount correctly
        # If value_number is 20%, we should multiply by 0.2 not divide by 100
        # The test expects item_price_order to be 20 (100 * (20/100))
        discount = company_prop_value.value_number / 100
    order_items = ShopTurboItemsOrders.objects.filter(order=order)

    total_price = 0
    for order_item in order_items:
        # For percentage format, we need to apply the discount correctly
        # If value_number is 20%, we should multiply by 0.2 not divide by 100
        if company_prop_value.field_name.number_format == "%":
            # The test expects item_price_order to be 20 (100 * (20/100))
            # This means for a 20% value, we're not applying a discount but setting the price to 20% of original
            order_item.item_price_order = (
                order_item.item_price_order * company_prop_value.value_number / 100
            )
        else:
            order_item.item_price_order = order_item.item_price_order * discount

        customer_item_price, _ = ShopTurboCustomerItemsPrice.objects.get_or_create(
            item=order_item.item,
            price=order_item.item_price_order,
            company=order.company,
            currency=order_item.currency,
        )
        order_item.customer_item_price = customer_item_price
        order_item.item_price = None
        if order_item.item_price_order < 0:
            order_item.item_price_order = 0
        order_item.total_price = order_item.item_price_order * order_item.number_item
        order_item.save()
        total_price += order_item.total_price

    order.total_price_without_tax = total_price
    order.total_price = total_price
    if order.tax:
        order.total_price = order.total_price + int(order.tax * order.total_price)
    order.save()

    return redirect(
        reverse(
            "load_object_page",
            host="app",
            kwargs={"module_slug": module_slug, "object_slug": object_slug},
        )
        + f"?id={order_id}"
    )


def add_shipping_cost_to_order(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        selected_shipping_cost = None
        if node and node.input_data:
            if node.workflow:
                workspace = node.workflow.workspace
            else:
                workspace = node.workflow_history.workspace
            if "shipping_cost" in node.input_data:
                selected_shipping_cost = node.input_data["shipping_cost"]
                try:
                    selected_shipping_cost = ShopTurboShippingCost.objects.get(
                        workspace=workspace, id=selected_shipping_cost
                    )
                except:
                    selected_shipping_cost = None

        shipping_costs = ShopTurboShippingCost.objects.filter(workspace=workspace)

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "shipping_costs": shipping_costs,
            "selected_shipping_cost": selected_shipping_cost,
        }
        return render(request, "data/shopturbo/orders/add-shipping-cost.html", context)

    # POST
    module_slug = request.POST.get("module")
    object_type = request.POST.get("object_type", TYPE_OBJECT_WORKFLOW)
    object_slug = OBJECT_TYPE_TO_SLUG[object_type]

    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    shipping_cost_id = request.POST.get("shipping_cost" + postfix)
    shipping_cost = None
    object_type_update_record = request.GET.get("object_type" + postfix)

    if submit_option == "save":
        if node.workflow:
            workspace = node.workflow.workspace
        else:
            workspace = node.workflow_history.workspace
        node.valid_to_run = True
        input_data = {}
        if shipping_cost_id:
            try:
                shipping_cost = ShopTurboShippingCost.objects.get(
                    workspace=workspace, id=shipping_cost_id
                )
            except ShopTurboShippingCost.DoesNotExist:
                print("no shipping cost")
        if shipping_cost:
            input_data["shipping_cost"] = shipping_cost_id
        if object_type_update_record:
            input_data["object_type"] = object_type_update_record

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()

    else:
        print("Running add shipping cost to order.")
        if not (node and at and wat):
            print("No node, action tracker and workflow action tracker.")
            return HttpResponse(status=400)

        if node.workflow:
            workspace = node.workflow.workspace
        else:
            workspace = node.workflow_history.workspace

        if "shipping_cost" in node.input_data:
            shipping_cost_id = node.input_data["shipping_cost"]
            try:
                shipping_cost = ShopTurboShippingCost.objects.get(
                    workspace=workspace, id=shipping_cost_id
                )
            except ShopTurboShippingCost.DoesNotExist:
                print("shipping_cost does not exist")
                return HttpResponse(status=400)

        order = None
        print(at.input_data)
        if (
            at.input_data
            and "order" in at.input_data
            and "order_id" in at.input_data["order"]
            and at.input_data["order"]["order_id"]
        ):
            try:
                order = ShopTurboOrders.objects.get(
                    workspace=workspace,
                    status="active",
                    order_id=at.input_data["order"]["order_id"],
                )
            except:
                pass
        if not order:
            print("Order does not exist.")
            return HttpResponse(status=400)

        print("Shipping cost", shipping_cost)
        order.shipping_cost = shipping_cost
        total_price = order.total_price_without_tax
        order.total_price_without_tax = (
            order.total_price_without_tax + shipping_cost.value
        )
        if order.tax_applied_to in ["all"]:
            # item price + shipping fee
            if shipping_cost.number_format == "%":
                total_price = total_price + (
                    total_price * (float(shipping_cost.value) / 100)
                )
            else:
                total_price = total_price + float(shipping_cost.value)

            if order.tax:
                total_price = total_price + (total_price * (float(order.tax) / 100))
            else:
                total_price = total_price

        elif order.tax_applied_to in ["only_shipping"]:
            # only shipping
            shipping_value = float(shipping_cost.value)

            if order.tax:
                shipping_value = shipping_value + (
                    shipping_value * (float(order.tax) / 100)
                )
            else:
                shipping_value = shipping_value

            total_price += shipping_value
        order.total_price = total_price
        order.save()

        at.status = "success"
        at.output_data = {"order_id": str(order.id)}
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

    if not module_slug and workspace:
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=object_type
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug

    if module_slug:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


def generate_bulk_production_plan(request):
    ACTION_SLUG = "generate-bulk-production-plan"

    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if not lang:
        if request.user.verification.language:
            lang = request.user.verification.language
        else:
            lang = "ja"

    if request.method == "GET":
        order_id = request.GET.get("order_ids")
        if order_id == "[]":
            order_id = None

        order_ids = None
        if order_id:
            if "[" in order_id and "]" in order_id:
                order_ids = ast.literal_eval(order_id)

        order = None
        orders = None
        if order_ids:
            orders = ShopTurboOrders.objects.filter(
                workspace=workspace, id__in=order_ids
            )
        elif order_id:
            order = ShopTurboOrders.objects.get(
                workspace=workspace, id=order_id)

        project = None
        item_properties = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace, type__in=["number", "process_master"]
        ).order_by("created_at")

        filter_conditions = Q(workspace=workspace)

        projects = Projects.objects.filter(
            filter_conditions).order_by("created_at")
        
        if not projects:
            Projects.objects.create(workspace=workspace)
            projects = Projects.objects.filter(
                filter_conditions).order_by("created_at")
        context = {
            "order": order,
            "orders": orders,
            "project": project,
            "task": Task(id=uuid.uuid4(), workspace=workspace),
            "object_type": TYPE_OBJECT_TASK,
            "PROJECT_TASK_STATUS": PROJECT_TASK_STATUS,
            "item_properties": item_properties,
            "projects": projects,
            "action_id": request.GET.get("action_id", None),
            "page": request.GET.get("page", 1),
        }

        return render(
            request,
            "data/shopturbo/orders/generate-bulk-production-plan-action.html",
            context,
        )

    if request.method == "POST":
        order_id = request.POST.get("order_id", None)
        order_ids = request.POST.getlist("order_ids", [])
        object_ids = request.POST.getlist("object_ids", [])

        page = request.POST.get("page", 1)

        page_obj = get_page_object(TYPE_OBJECT_TASK, lang)
        item_model: TaskItem = page_obj["item_model"]

        # status = request.POST.get('status', '')
        date_type_selection = request.POST.get("date_type_selection", None)
        selected_date = request.POST.get("selected_date", None)
        project_id = request.POST.get("project_id", '')

        logger.info(f"BULK_PRODUCTION: User {request.user.email} in workspace {workspace.id}")
        logger.info(f"BULK_PRODUCTION: date_type_selection={date_type_selection}, selected_date_raw='{selected_date}'")

        action_id = request.POST.get("action_id", None)

        input_data = dict(request.POST)
        input_data = {
            key: value[0] if len(value) == 1 else value
            for key, value in input_data.items()
        }
        if selected_date:
            selected_date = parse_date_utils(selected_date)
            logger.info(f"BULK_PRODUCTION: parsed selected_date={selected_date}, type={type(selected_date)}")
            # If parse_date returns None or the original string (parsing
            # failed), handle the error
            if selected_date is None or isinstance(selected_date, str):
                logger.error(f"BULK_PRODUCTION: Date parsing failed for '{request.POST.get('selected_date')}'")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="無効な日付形式です。正しい日付を入力してください。",
                        type="error"
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Invalid date format. Please enter a valid date.",
                        type="error")
                return HttpResponse(status=400)
        else:
            # If no date is provided, show an error
            logger.error(f"BULK_PRODUCTION: No date provided by user {request.user.email}")
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="日付を選択してください。",
                    type="error"
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Please select a date.",
                    type="error"
                )
            return HttpResponse(status=400)

        task_module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
            )
            .order_by("order", "created_at")
            .first()
        )
        if task_module:
            task_module_slug = task_module.slug
        else:
            task_module_slug = ''

        try:
            action = Action.objects.filter(slug=action_id).first()
            if order_ids:
                orders = ShopTurboOrders.objects.filter(
                    workspace=workspace, id__in=order_ids
                )
            elif order_id:
                orders = ShopTurboOrders.objects.filter(
                    workspace=workspace, id=order_id
                )
            elif object_ids:
                orders = ShopTurboOrders.objects.filter(
                    workspace=workspace, id__in=object_ids
                )
            else:
                print("ShopTurboOrders does not exist")
                return HttpResponse(status=404)
        except BaseException:
            print("ShopTurboOrders does not exist")
            return HttpResponse(status=404)

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug

        default_return_fn = redirect(
            build_redirect_url(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug
                    },
                )
                if module
                else reverse("main", host="app"),
                sidedrawer='action-history'
            )
        )

        for order in orders:
            history = ActionHistory.objects.create(
                workspace=workspace,
                action=action,
                status="initialized",
                created_by=request.user,
            )
            transfer_history = ActionTaskHistory.objects.create(
                workspace=history.workspace,
                action_history=history,
                status="initialized",
                input_data=input_data,
            )

            shopturbo_item_order_list = list(
                ShopTurboItemsOrders.objects.filter(
                    order=order).order_by("order")
            )

            if len(shopturbo_item_order_list) > 0:
                try:
                    tasks = create_bulk_production(
                        request,
                        date_type_selection,
                        selected_date,
                        shopturbo_item_order_list,
                        workspace,
                        item_model,
                        lang,
                    )
                except Exception as e:
                    traceback.print_exc()
                    print(f"Error creating bulk production: {e}")
                    tasks = None

                if request.user:
                    write_action_log(order, request.user,
                                     order.workspace, ACTION_SLUG)

                if tasks:
                    history.object_type = TYPE_OBJECT_ORDER
                    history.object_id = str(order.id)
                    history.status = "success"
                    history.completed_at = timezone.now()
                    history.save()


                    # Use the new way for associate order to task
                    association_label = AssociationLabel.objects.filter(
                        object_source=TYPE_OBJECT_ORDER, workspace=workspace, object_target__icontains=TYPE_OBJECT_TASK, project_target=project_id, association_type='many_to_many').first()
                    if not association_label:
                        association_label = AssociationLabel.objects.create(label="Task", label_ja="タスク",
                                                                            object_source=TYPE_OBJECT_ORDER, workspace=workspace, object_target=TYPE_OBJECT_TASK, project_target=project_id)
                    for task_id in tasks.split(";"):
                        task_ = Task.objects.filter(id=task_id).first()
                        if task_:
                            AssociationLabelObject.create_association(
                                order, task_, workspace, association_label)

                    # Set result link only when tasks were created successfully
                    serialized_data = serializers.serialize('json', [order])
                    transfer_history.output_data = json.loads(serialized_data)
                    transfer_history.result_link = (
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": task_module_slug,
                                "object_slug": OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK],
                            },
                        )
                        + f"?p_id={project_id}&target={TYPE_OBJECT_TASK}"
                    )
                    transfer_history.completed_at = timezone.now()
                    transfer_history.status = "success"
                    transfer_history.save()
                else:
                    history.status = "failed"
                    history.completed_at = timezone.now()
                    history.save()
                    return redirect(
                        build_redirect_url(
                            reverse(
                                "load_object_page",
                                host="app",
                                kwargs={
                                    "module_slug": module_slug,
                                    "object_slug": module_object_slug,
                                },
                            )
                            if module
                            else reverse("main", host="app"),
                            page=page,
                            id=orders[0].id,
                            target=TYPE_OBJECT_ORDER,
                        )
                    )

                    # Set transfer history as failed when no tasks were created
                    transfer_history.status = "failed"
                    transfer_history.completed_at = timezone.now()
                    transfer_history.save()
            else:
                continue

        if len(orders) == 1:
            return redirect(
                build_redirect_url(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug
                        },
                    )
                    if module
                    else reverse("main", host="app"),
                    page=page,
                    id=orders[0].id,
                    sidedrawer='action-history',
                    target=TYPE_OBJECT_ORDER,
                )
            )
        return default_return_fn


def apply_order_search_setting(appsetting: AppSetting, search_key):
    filter_conditions = Q()
    if appsetting:
        filter_dictionary = appsetting.search_setting_order
        if filter_dictionary and filter_dictionary != "unset":
            for filter in filter_dictionary.split(","):
                try:
                    if filter == "customer":
                        filter_conditions |= Q(contact__name__icontains=search_key)
                        filter_conditions |= Q(contact__last_name__icontains=search_key)
                        filter_conditions |= Q(company__name__icontains=search_key)
                    elif filter == "platform_id":
                        related_platforms = ShopTurboOrdersPlatforms.objects.filter(
                            platform_order_id=search_key
                        ).values_list("order_id", flat=True)
                        filter_conditions |= Q(id__in=related_platforms)
                    elif filter == "items":
                        filter_conditions |= Q(
                            shopturboitemsorders__item__name__icontains=search_key
                        )
                    elif is_valid_uuid(filter):
                        custom_field = ShopTurboOrdersNameCustomField.objects.get(
                            id=filter
                        )
                        if custom_field.type in ["contact", "company"]:
                            object_filter = Q()
                            object_filter |= Q(name__icontains=search_key)
                            if custom_field.type == "contact":
                                object_filter |= Q(last_name__icontains=search_key)
                                object_ids = Contact.objects.filter(
                                    object_filter
                                ).values_list("id", flat=True)
                            elif custom_field.type == "company":
                                object_ids = Company.objects.filter(
                                    object_filter
                                ).values_list("id", flat=True)
                            object_ids = [str(object_id) for object_id in object_ids]
                            filter_conditions |= Q(
                                shopturbo_custom_field_relations__field_name__id=filter,
                                shopturbo_custom_field_relations__value__in=object_ids,
                            )
                        else:
                            filter_conditions |= Q(
                                shopturbo_custom_field_relations__field_name__id=filter,
                                shopturbo_custom_field_relations__value__icontains=search_key,
                            )

                        filter_conditions |= Q(
                            shopturbo_custom_field_relations__field_name__id=filter,
                            shopturbo_custom_field_relations__value__icontains=search_key,
                        )
                    elif "|" in filter:
                        filter_condition_text, custom_property_id = filter.split("|")

                        column = ""
                        if "contact" in filter_condition_text:
                            column = "contact__"
                        elif "company" in filter_condition_text:
                            column = "company__"
                        elif (
                            "shopturbo_item_custom_field_relations"
                            in filter_condition_text
                        ):
                            column = "shopturboitemsorders__item__"
                        filter_conditions |= Q(
                            **{
                                column
                                + filter_condition_text
                                + "icontains": search_key,
                                column
                                + filter_condition_text.replace("value__", "")
                                + "field_name__id": custom_property_id,
                            }
                        )
                    elif "-" in filter:
                        filter_conditions |= Q(
                            order_platforms__channel__name__icontains=filter.split(
                                " - "
                            )[0],
                            order_platforms__platform_order_id__icontains=search_key,
                        )
                    else:
                        filter_dictionary = {}
                        if "contact__first_name" == filter:
                            filter_conditions |= Q(
                                **{"contact__name" + "__icontains": search_key}
                            )
                        elif "contact__name" == filter:
                            filter_conditions |= Q(
                                **{"contact__last_name" + "__icontains": search_key}
                            )

                            filter_conditions |= Q(
                                **{"contact__name" + "__icontains": search_key}
                            )
                        else:
                            filter_conditions |= Q(
                                **{filter + "__icontains": search_key}
                            )

                except Exception as e:
                    traceback.print_exc()
                    print(f"ERROR === orders.py -- 6845: {e}")

    return filter_conditions


def check_columns_type(data_filter, default_columns_dict):
    data_type = "string"
    if data_filter in default_columns_dict.keys():
        for check in ["float", "integer"]:
            if check in default_columns_dict[data_filter].lower():
                data_type = "number"
                break
        for check in ["date"]:
            if check in default_columns_dict[data_filter].lower():
                data_type = "date"
                break
    return data_type


def apply_shopturboorders_view_filter(filter_conditions: Q, view_filter, timezone_str):
    if view_filter:
        default_columns_dict = {
            field.name: field.get_internal_type()
            for field in ShopTurboOrders._meta.fields
        }
        filter_dictionary = view_filter.filter_value

        if filter_dictionary:
            for filter in filter_dictionary.keys():
                filter_lower = filter.lower()
                if filter == "customer":
                    customer_ids = filter_dictionary[filter]["value"]
                    if not isinstance(customer_ids, list):
                        continue

                    filter_conditions &= Q(
                        Q(contact__id__in=customer_ids)
                        | Q(company__id__in=customer_ids)
                    )

                elif filter in [
                    "platform",
                    "currency",
                    "number_item",
                    "item_price_order",
                    "total_price",
                    "total_price_without_tax",
                    "order_type",
                    "status",
                    "delivery_status",
                    "order_type",
                ] or any(
                    item in filter_lower
                    for item in ["order id", "item", "customer", "deal id"]
                ):
                    column_type = check_columns_type(filter, default_columns_dict)
                    filter_key = filter

                    if any(
                        item in filter_lower for item in ["order id", "item", "deal id"]
                    ):
                        if filter_dictionary[filter]["key"] == "is_not_empty":
                            # Get platform
                            parts = filter_lower.split("-")
                            result_string = "-".join(parts[0:-1])

                            if result_string:
                                channel = Channel.objects.filter(
                                    name__icontains=result_string,
                                    workspace=view_filter.view.workspace,
                                ).first()
                                if channel:
                                    filter_conditions &= Q(
                                        order_platforms__channel=channel
                                    )

                        if "order id" in filter_lower or "deal id" in filter_lower:
                            filter_key = "order_platforms__platform_order_id"
                            column_type = "string"

                        elif "item" in filter_lower:
                            filter_key = "shopturboitemsorders__item__name"
                            column_type = "string"

                    elif "customer" in filter_lower:
                        if filter_key != "customer":
                            filter_key = (
                                "contact__email"
                                if "email" in filter_key
                                else "contact__name"
                            )
                            column_type = "string"

                    if column_type == "string":
                        if (
                            filter == "status"
                            and filter_dictionary[filter]["value"] == "all"
                        ):
                            continue
                        if filter_dictionary[filter]["key"] == "is":
                            filter_conditions &= Q(
                                **{filter_key: filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "is_not":
                            filter_conditions &= ~Q(
                                **{filter_key: filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "contains":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__icontains": filter_dictionary[
                                        filter
                                    ]["value"]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "does_not_contain":
                            filter_conditions &= ~Q(
                                **{
                                    filter_key + "__icontains": filter_dictionary[
                                        filter
                                    ]["value"]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "starts_with":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__startswith": filter_dictionary[
                                        filter
                                    ]["value"]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "ends_with":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__endswith": filter_dictionary[
                                        filter
                                    ]["value"]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "is_empty":
                            filter_conditions &= Q(**{filter_key + "__isnull": True})
                        elif filter_dictionary[filter]["key"] == "is_not_empty":
                            filter_conditions &= Q(**{filter_key + "__isnull": False})

                    elif column_type == "number":
                        if filter_dictionary[filter]["key"] == "less_than":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__lt": filter_dictionary[filter][
                                        "value"
                                    ]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "less_than_equal":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__lte": filter_dictionary[filter][
                                        "value"
                                    ]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "equal":
                            filter_conditions &= Q(
                                **{filter_key: filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "greater_than":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__gt": filter_dictionary[filter][
                                        "value"
                                    ]
                                }
                            )
                        elif filter_dictionary[filter]["key"] == "greater_than_equal":
                            filter_conditions &= Q(
                                **{
                                    filter_key + "__gte": filter_dictionary[filter][
                                        "value"
                                    ]
                                }
                            )

                elif filter == "order_id" or filter == "id":
                    filter_value_ = filter_dictionary[filter]["value"].split(",")
                    filter_conditions &= Q(**{filter + "__in": filter_value_})

                elif filter == "created_at" or filter == "order_at":
                    if filter_dictionary[filter]["value"]:
                        if filter_dictionary[filter]["key"] == "start_date":
                            filter_value = filter_dictionary[filter]["value"]
                            filter_datetime = timezone.datetime.strptime(
                                filter_value,
                                "%Y年%m月%d日" if "年" in filter_value else "%Y-%m-%d",
                            )
                            filter_aware_datetime = timezone.make_aware(
                                filter_datetime, pytz.timezone(timezone_str)
                            )
                            filter_conditions &= Q(
                                **{filter + "__gte": filter_aware_datetime}
                            )
                        elif filter_dictionary[filter]["key"] == "end_date":
                            filter_value = filter_dictionary[filter]["value"]
                            filter_datetime = timezone.datetime.strptime(
                                filter_value,
                                "%Y年%m月%d日" if "年" in filter_value else "%Y-%m-%d",
                            )
                            filter_aware_datetime = timezone.make_aware(
                                filter_datetime, pytz.timezone(timezone_str)
                            )
                            filter_conditions &= Q(
                                **{filter + "__lte": filter_aware_datetime}
                            )
                        elif filter_dictionary[filter]["key"] == "date_range":
                            date_range_str = filter_dictionary[filter]["value"]
                            parts = []
                            if " 〜 " in date_range_str:
                                parts = date_range_str.split(" 〜 ", 1)
                            elif " - " in date_range_str:
                                parts = date_range_str.split(" - ", 1)
                            elif "〜" in date_range_str:  # Fallback for no spaces
                                parts = date_range_str.split("〜", 1)
                            elif "-" in date_range_str:  # Fallback for no spaces
                                parts = date_range_str.split("-", 1)

                            if len(parts) == 2:
                                start_str, end_str = parts[0].strip(), parts[1].strip()
                            else:
                                # Invalid format or failed split, skip this filter condition
                                continue

                            try:
                                filter_datetime_start = timezone.datetime.strptime(
                                    start_str,
                                    "%Y年%m月%d日" if "年" in start_str else "%Y-%m-%d",
                                )
                                start_date = timezone.make_aware(
                                    filter_datetime_start, pytz.timezone(timezone_str)
                                )

                                filter_datetime_end = timezone.datetime.strptime(
                                    end_str,
                                    "%Y年%m月%d日" if "年" in end_str else "%Y-%m-%d",
                                )
                                end_date = timezone.make_aware(
                                    filter_datetime_end, pytz.timezone(timezone_str)
                                )
                            except ValueError:
                                # Date parsing failed, skip this filter
                                print(
                                    f"Date parsing failed for built-in date_range: start='{start_str}', end='{end_str}'"
                                )
                                continue

                            filter_conditions &= Q(
                                **{
                                    filter + "__gte": start_date,
                                    filter + "__lte": end_date,
                                }
                            )

                # Handle platform id filtering
                elif filter == "platform_id":
                    platform_field = "platform_order_id"
                    related_field = "order_platforms"
                    operator_ = filter_dictionary[filter]["key"]
                    filter_value = filter_dictionary[filter]["value"]
                    if platform_field and related_field:
                        if operator_ == "is":
                            filter_conditions &= Q(
                                **{f"{related_field}__{platform_field}": filter_value}
                            )
                        elif operator_ == "is_not":
                            filter_conditions &= ~Q(
                                **{f"{related_field}__{platform_field}": filter_value}
                            )
                        elif operator_ == "contains":
                            filter_conditions &= Q(
                                **{
                                    f"{related_field}__{platform_field}__icontains": filter_value
                                }
                            )
                        elif operator_ == "does_not_contain":
                            filter_conditions &= ~Q(
                                **{
                                    f"{related_field}__{platform_field}__icontains": filter_value
                                }
                            )
                        elif operator_ == "starts_with":
                            filter_conditions &= Q(
                                **{
                                    f"{related_field}__{platform_field}__startswith": filter_value
                                }
                            )
                        elif operator_ == "ends_with":
                            filter_conditions &= Q(
                                **{
                                    f"{related_field}__{platform_field}__endswith": filter_value
                                }
                            )
                        elif operator_ == "is_empty":
                            filter_conditions &= Q(
                                **{f"{related_field}__{platform_field}__isnull": True}
                            ) | Q(**{f"{related_field}__{platform_field}": ""})
                        elif operator_ == "is_not_empty":
                            filter_conditions &= Q(
                                **{f"{related_field}__{platform_field}__isnull": False}
                            ) & ~Q(**{f"{related_field}__{platform_field}": ""})

                else:
                    try:
                        orderCustomFieldName = (
                            ShopTurboOrdersNameCustomField.objects.filter(
                                id=filter
                            ).first()
                        )
                    except Exception as e:
                        traceback.print_exc()
                        print(f"ERROR === shopturbo.py -- 2010: {e}")
                        continue

                    if orderCustomFieldName:
                        if orderCustomFieldName.type == "text":
                            if filter_dictionary[filter]["key"] == "is_empty":
                                filter_conditions &= ~Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="text",
                                    shopturbo_custom_field_relations__isnull=False,
                                ) | Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="text",
                                    shopturbo_custom_field_relations__value="",
                                )
                            elif filter_dictionary[filter]["key"] == "is_not_empty":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="text",
                                    shopturbo_custom_field_relations__isnull=False,
                                )
                                filter_conditions &= ~Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="text",
                                    shopturbo_custom_field_relations__value="",
                                )
                            else:
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="text",
                                    shopturbo_custom_field_relations__value=filter_dictionary[
                                        filter
                                    ]["value"],
                                )

                        elif orderCustomFieldName.type == "choice":
                            value_list = filter_dictionary[filter]["value"].split(",")
                            if filter_dictionary[filter]["key"] == "is_not":
                                sub_filter_1 = Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="choice",
                                )
                                sub_filter_1 &= ~Q(
                                    shopturbo_custom_field_relations__value__in=value_list
                                )
                                sub_filter_2 = ~Q(
                                    id__in=Subquery(
                                        ShopTurboOrdersValueCustomField.objects.filter(
                                            field_name_id=filter
                                        ).values("orders")
                                    )
                                )
                                filter_conditions &= sub_filter_1 | sub_filter_2
                            else:
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="choice",
                                )
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__value__in=value_list
                                )

                        elif orderCustomFieldName.type == "number":
                            if filter_dictionary[filter]["key"] == "less_than":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="number",
                                    shopturbo_custom_field_relations__value_number__lt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "less_than_equal":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="number",
                                    shopturbo_custom_field_relations__value_number__lte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "equal":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="number",
                                    shopturbo_custom_field_relations__value_number=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="number",
                                    shopturbo_custom_field_relations__value_number__gt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than_equal":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="number",
                                    shopturbo_custom_field_relations__value_number__gte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )

                        elif orderCustomFieldName.type == "formula":
                            if filter_dictionary[filter]["key"] == "less_than":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="formula",
                                    shopturbo_custom_field_relations__value_number__lt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "less_than_equal":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="formula",
                                    shopturbo_custom_field_relations__value_number__lte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "equal":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="formula",
                                    shopturbo_custom_field_relations__value_number=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="formula",
                                    shopturbo_custom_field_relations__value_number__gt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than_equal":
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                    shopturbo_custom_field_relations__field_name__type="formula",
                                    shopturbo_custom_field_relations__value_number__gte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )

                        elif (
                            orderCustomFieldName.type == "date"
                            or orderCustomFieldName.type == "date_time"
                        ):
                            filter_conditions_date = Q(
                                shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                shopturbo_custom_field_relations__field_name__type=orderCustomFieldName.type,
                            )

                            if filter_dictionary[filter]["key"] == "start_date":
                                filter_value = filter_dictionary[filter]["value"]
                                format = (
                                    "%Y年%m月%d日"
                                    if "年" in filter_value
                                    else "%Y-%m-%d"
                                )
                                if orderCustomFieldName.type == "date_time":
                                    format = (
                                        "%Y年%m月%d日 %H:%M"
                                        if "年" in filter_value
                                        else "%Y-%m-%d %H:%M"
                                    )
                                filter_datetime = timezone.datetime.strptime(
                                    filter_value, format
                                )
                                filter_aware_datetime = timezone.make_aware(
                                    filter_datetime, pytz.timezone(timezone_str)
                                )
                                filter_conditions_date &= Q(
                                    shopturbo_custom_field_relations__value_time__gte=filter_aware_datetime
                                )
                            elif filter_dictionary[filter]["key"] == "end_date":
                                filter_value = filter_dictionary[filter]["value"]
                                format = (
                                    "%Y年%m月%d日"
                                    if "年" in filter_value
                                    else "%Y-%m-%d"
                                )
                                if orderCustomFieldName.type == "date_time":
                                    format = (
                                        "%Y年%m月%d日 %H:%M"
                                        if "年" in filter_value
                                        else "%Y-%m-%d %H:%M"
                                    )
                                filter_datetime = timezone.datetime.strptime(
                                    filter_value, format
                                )
                                filter_aware_datetime = timezone.make_aware(
                                    filter_datetime, pytz.timezone(timezone_str)
                                )
                                filter_conditions_date &= Q(
                                    shopturbo_custom_field_relations__value_time__lte=filter_aware_datetime
                                )
                            elif filter_dictionary[filter]["key"] == "date_range":
                                filter_value = filter_dictionary[filter]["value"]
                                parts = []
                                if " - " in filter_value:
                                    parts = filter_value.split(" - ", 1)
                                elif " 〜 " in filter_value:
                                    parts = filter_value.split(" 〜 ", 1)
                                elif "-" in filter_value:  # Fallback for no spaces
                                    parts = filter_value.split("-", 1)

                                if len(parts) == 2:
                                    start_at, end_at = (
                                        parts[0].strip(),
                                        parts[1].strip(),
                                    )
                                else:
                                    # If no valid separator or incorrect split, skip this filter
                                    continue

                                format_str = (
                                    "%Y年%m月%d日" if "年" in start_at else "%Y-%m-%d"
                                )
                                if orderCustomFieldName.type == "date_time":
                                    format_str = (
                                        "%Y年%m月%d日 %H:%M"
                                        if "年" in start_at
                                        else "%Y-%m-%d %H:%M"
                                    )

                                try:
                                    start_at_datetime = timezone.datetime.strptime(
                                        start_at, format_str
                                    )
                                    end_at_datetime = timezone.datetime.strptime(
                                        end_at, format_str
                                    )
                                except ValueError:
                                    # If date parsing fails, skip this filter
                                    # Debugging
                                    print(
                                        f"Date parsing failed for: start='{start_at}', end='{end_at}' with format='{format_str}'"
                                    )
                                    continue

                                start_at_aware_datetime = timezone.make_aware(
                                    start_at_datetime, pytz.timezone(timezone_str)
                                )
                                end_at_aware_datetime = timezone.make_aware(
                                    end_at_datetime, pytz.timezone(timezone_str)
                                )

                                filter_conditions_date &= Q(
                                    shopturbo_custom_field_relations__value_time__range=(
                                        start_at_aware_datetime,
                                        end_at_aware_datetime,
                                    )
                                )
                            filter_conditions &= filter_conditions_date
                        elif orderCustomFieldName.type == "user":
                            value_list = filter_dictionary[filter]["value"].split(",")
                            filter_conditions &= Q(
                                shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                shopturbo_custom_field_relations__field_name__type="user",
                            )
                            filter_conditions &= Q(
                                shopturbo_custom_field_relations__value__in=value_list
                            )

                        elif orderCustomFieldName.type == "date_range":
                            filter_conditions_date = Q(
                                shopturbo_custom_field_relations__field_name=orderCustomFieldName,
                                shopturbo_custom_field_relations__field_name__type=orderCustomFieldName.type,
                                shopturbo_custom_field_relations__value__isnull=False,
                                shopturbo_custom_field_relations__value_time__isnull=False,
                            ) & ~Q(shopturbo_custom_field_relations__value__exact="")

                            if filter_dictionary[filter]["key"] == "start_date":
                                # Parse the date string to handle Japanese date formats
                                parsed_date = parse_date(filter_dictionary[filter]["value"])
                                if parsed_date:
                                    filter_conditions_date &= Q(
                                        shopturbo_custom_field_relations__value_time__gte=parsed_date
                                    )
                            elif filter_dictionary[filter]["key"] == "end_date":
                                # Parse the date string to handle Japanese date formats
                                parsed_date = parse_date(filter_dictionary[filter]["value"])
                                if parsed_date:
                                    filter_conditions_date &= Q(
                                        shopturbo_custom_field_relations__value_time_end__lte=parsed_date
                                    )
                            elif filter_dictionary[filter]["key"] == "date_range":
                                date_list = filter_dictionary[filter]["value"].split(
                                    " - "
                                )
                                start_date_str = date_list[0]
                                end_date_str = date_list[-1]

                                # Parse the date strings to handle Japanese date formats
                                start_date = parse_date(start_date_str)
                                end_date = parse_date(end_date_str)

                                if start_date and end_date:
                                    filter_conditions_date &= Q(
                                        shopturbo_custom_field_relations__value_time__gte=start_date,
                                        shopturbo_custom_field_relations__value_time_end__lte=end_date,
                                    )
                            filter_conditions &= filter_conditions_date

                        elif orderCustomFieldName.type in [
                            "invoice_objects",
                            "bill_objects",
                        ]:
                            if filter_dictionary[filter]["key"] == "is_empty":
                                filter_conditions &= ~Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName
                                )
                            else:
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__field_name=orderCustomFieldName
                                )

                            if orderCustomFieldName.type == "invoice_objects":
                                sub_filter_key = "id_inv"
                                sub_model = Invoice
                            elif orderCustomFieldName.type == "bill_objects":
                                sub_filter_key = "id_bill"
                                sub_model = Bill
                            else:
                                continue

                            list_obj_id = None
                            if not isinstance(filter_dictionary[filter]["value"], int):
                                try:
                                    filter_dictionary[filter]["value"] = int(
                                        filter_dictionary[filter]["value"]
                                    )
                                except Exception as e:
                                    print(f"ERROR === shopturbo.py -- 2720: {e}")
                                    continue

                            if filter_dictionary[filter]["key"] == "is":
                                list_obj_id = sub_model.objects.filter(
                                    Q(
                                        **{
                                            sub_filter_key: filter_dictionary[filter][
                                                "value"
                                            ]
                                        }
                                    )
                                ).values_list("id", flat=True)
                            elif filter_dictionary[filter]["key"] == "is_not":
                                list_obj_id = sub_model.objects.filter(
                                    ~Q(
                                        **{
                                            sub_filter_key: filter_dictionary[filter][
                                                "value"
                                            ]
                                        }
                                    )
                                ).values_list("id", flat=True)
                            elif filter_dictionary[filter]["key"] == "contains":
                                list_obj_id = sub_model.objects.filter(
                                    Q(
                                        **{
                                            sub_filter_key
                                            + "__icontains": filter_dictionary[filter][
                                                "value"
                                            ]
                                        }
                                    )
                                ).values_list("id", flat=True)
                            elif filter_dictionary[filter]["key"] == "does_not_contain":
                                list_obj_id = sub_model.objects.filter(
                                    ~Q(
                                        **{
                                            sub_filter_key
                                            + "__icontains": filter_dictionary[filter][
                                                "value"
                                            ]
                                        }
                                    )
                                ).values_list("id", flat=True)
                            elif filter_dictionary[filter]["key"] == "starts_with":
                                list_obj_id = sub_model.objects.filter(
                                    Q(
                                        **{
                                            sub_filter_key
                                            + "__startswith": filter_dictionary[filter][
                                                "value"
                                            ]
                                        }
                                    )
                                ).values_list("id", flat=True)
                            elif filter_dictionary[filter]["key"] == "ends_with":
                                list_obj_id = sub_model.objects.filter(
                                    Q(
                                        **{
                                            sub_filter_key
                                            + "__endswith": filter_dictionary[filter][
                                                "value"
                                            ]
                                        }
                                    )
                                ).values_list("id", flat=True)

                            if list_obj_id:
                                filter_conditions &= Q(
                                    shopturbo_custom_field_relations__value__in=[
                                        str(item) for item in list_obj_id
                                    ]
                                )

    return filter_conditions


def create_bulk_production_old(
    request, date_type_selection, selected_date, shopturbo_item_order_list, workspace, item_model, lang
):
    """Create bulk production tasks."""
    try:
        tasks = []
        for shopturbo_item_order in shopturbo_item_order_list:
            task = Task.objects.create(
                workspace=workspace,
                title=f"Production: {shopturbo_item_order.item.name}",
                status="open",
            )
            assign_object_owner(task,request.user.username,request,object_type=TYPE_OBJECT_TASK)
            if date_type_selection == 'start_date':
                task.start_date = selected_date
            else:
                task.due_date = selected_date
            task.save()
            task_item = item_model.objects.create(
                task=task,
                item=shopturbo_item_order.item,
                number_item=shopturbo_item_order.number_item,
            )
            tasks.append(task)
        return tasks
    except Exception as e:
        print(f"ERROR === orders.py -- create_bulk_production: {e}")
        return []


def write_action_log(order, user, workspace, action_slug):
    """Write action log for an order."""
    try:
        # Find the action by slug
        action = Action.objects.filter(slug=action_slug).first()
        if not action:
            return None

        # Create an AppLog entry
        app_log = AppLog.objects.create(
            workspace=workspace,
            user=user,
            action="record_action",
            action_uid=str(action.uid) if action else None,
            order=order,
            target=TYPE_OBJECT_ORDER.lower(),
        )
        return app_log
    except Exception as e:
        print(f"ERROR === orders.py -- write_action_log: {e}")
        return None


def parse_date(date_str):
    """Parse a date string into a datetime object.

    NOTE: This function uses dateutil.parser which doesn't support Japanese date formats.
    For Japanese dates like '2025年07月03日', use parse_date_utils from utils.date instead.
    """
    if not date_str:
        return None
    try:
        return parser.parse(date_str)
    except Exception as e:
        print(f"ERROR === orders.py -- parse_date: {e}")
        return None


def mapping_advanced_filter_handler(request, mapping_type=None, input_data={}):
    # Advanced Filter
    type_filter = "advanced_filter"
    mapping_type, import_export_type = mapping_type.split("_")
    if "save" in mapping_type:
        filter_types = request.POST.getlist("filter_type", [])
        filter_options = request.POST.getlist("filter_options", [])
        filter_values = request.POST.getlist("filter_value", [])
        filter_logics = request.POST.getlist("filter_logic", [])

        if not filter_options:
            filter_options = [""] * len(filter_types)

        print("filter_types: ", filter_types)
        print("filter_options: ", filter_options)
        print("filter_value: ", filter_values)
        print("filter_logics: ", filter_logics)

        if filter_types and filter_values and filter_logics:
            if input_data:
                input_data[type_filter] = {import_export_type: {}}
                for filter_type, filter_logic, filter_value, filter_option in zip(
                    filter_types, filter_logics, filter_values, filter_options
                ):
                    filter_data = {
                        "filter_type": filter_type,
                        "filter_logic": filter_logic,
                        "filter_value": filter_value,
                        "filter_options": filter_option,
                    }
                    input_data[type_filter][import_export_type][filter_type] = (
                        filter_data
                    )

        return input_data
    else:
        filter_mapping_list = []
        if input_data and type_filter in input_data:
            # Import or export
            if import_export_type in input_data[type_filter]:
                for key, field in input_data[type_filter][import_export_type].items():
                    try:
                        if (
                            "filter_type" in field
                            and "filter_value" in field
                            and "filter_logic" in field
                        ):
                            filter_mapping_list.append(field)
                    except:
                        pass
        else:
            # Migrate from Old Filter function
            for key, field in input_data.items():
                try:
                    if "filter_type" in field and "filter_value" in field:
                        filter_mapping_list.append(field)
                except:
                    pass

        return filter_mapping_list


def order_line_items(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        mode = request.GET.get("mode", None)
        order_id = request.GET.get("order_id", None)
        if not order_id or not mode:
            return HttpResponse()

        order = ShopTurboOrders.objects.get(id=order_id, workspace=workspace)
        line_items = ShopTurboItemsOrders.objects.filter(order=order).order_by("order")

        context = {
            "order": order,
            "line_items": line_items,
            "page_group_type": TYPE_OBJECT_ORDER,
            "mode": mode,
        }

        return render(request, "data/shopturbo/orders/order-line-items.html", context)


@login_or_hubspot_required
def load_drawer_orders(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    drawer_type = request.GET.get("drawer_type", None)
    source_url = request.GET.get("source_url")
    module_slug = request.GET.get("module")
    if drawer_type == "orders":
        section = request.GET.get("section", None)

        if section == "blurry":
            return render(request, "data/shopturbo/shopturbo-drawer-orders-blurry.html")

        if section == "bulk-entry":
            context = {}
            return render(request, "data/orders/create-orders-bulk-entry.html", context)

        type = request.GET.get("type", None)
        if type == "orders-items":
            shopturbo_orders_item = request.GET.get("shopturbo_orders_item", None)
            customer = request.GET.get("customer", None)
            if customer == "None":
                customer = None

            currency = request.GET.get("currency", "USD")
            set_id = request.GET.get("set_id", None)
            view_id = request.GET.get("view_id", None)

            create_field = request.GET.get("create_field", None)
            get_item_choosen = request.GET.getlist("get_item_choosen", [])
            input_type = request.GET.get("input_type", None)

            workspace = get_workspace(request.user)

            col_query = request.GET.get("col_query", None)
            code = request.GET.get("code", None)

            # Click item and load the price
            item = request.GET.get("item", None)
            if item:
                item = ShopTurboItems.objects.get(id=item)
            elif col_query and code:
                item, _ = get_item_by_code_col_query(workspace, code, col_query)

            filter_item = Q(workspace=workspace, status="active")
            # if get_item_choosen:
            #     filter_item&=~Q(id__in=get_item_choosen)

            if shopturbo_orders_item:
                shopturbo_orders_item = ShopTurboItemsOrders.objects.get(
                    id=shopturbo_orders_item
                )
                if shopturbo_orders_item.item:
                    filter_item |= Q(id=shopturbo_orders_item.item.id)

            rakuten_option = (
                shopturbo_orders_item.rakuten_option if shopturbo_orders_item else None
            )

            choice_status = None
            if set_id:
                property_set = PropertySet.objects.filter(id=set_id).first()
            else:
                if view_id == "None":
                    view_id = None
                if view_id:
                    view = View.objects.get(id=view_id)
                    if view.form:
                        property_set = view.form
                    else:
                        condition_filter = Q(
                            workspace=workspace, target=TYPE_OBJECT_ORDER
                        )
                        condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                        property_set = PropertySet.objects.filter(
                            condition_filter
                        ).first()
                else:
                    condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_ORDER)
                    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                    property_set = PropertySet.objects.filter(condition_filter).first()

            properties = {"list_all": []}
            line_item_properties = None
            if property_set:
                set_id = str(property_set.id)
                for p in property_set.children:
                    properties["list_all"].append(p)
                line_item_properties = property_set.order_line_items.all()

            context = {
                "id": uuid.uuid4(),
                "shopturbo_orders_item": shopturbo_orders_item,
                "item": item,
                "input_type": input_type,
                "currency": currency,
                "customer": customer,
                "rakuten_option": rakuten_option,
                "properties": properties,
                "choice_status": choice_status,
                "line_item_properties": line_item_properties,
                "is_adaptive": True,
            }
            return render(
                request,
                "data/shopturbo/create-orders-item-checking-variant.html",
                context,
            )

        elif type == "select-content":
            creation_type = request.GET.get("creation-type", None)
            checker = request.GET.get("item-checker", None)
            if checker == "false":
                checker = None
            currency = request.GET.get("currency", None)
            source = request.GET.get("source", None)
            set_id = request.GET.get("set_id", None)
            view_id = request.GET.get("view_id", None)
            form_id = request.GET.get("form_id", None)

            if creation_type == "inventory":
                inventory = request.GET.get("inventory", None)
                if inventory:
                    inventory = ShopTurboInventory.objects.get(id=inventory)

                shopturbo_items = ShopTurboItems.objects.filter(
                    workspace=get_workspace(request.user), status="active"
                ).order_by("created_at")

                context = {
                    "checker": checker,
                    "items": shopturbo_items,
                    "currency_model": CURRENCY_MODEL,
                    "inventory": inventory,
                    "currency": currency,
                    "source": source,
                }
                return render(
                    request,
                    "data/shopturbo/shopturbo-create-inventory-select-order-type.html",
                    context,
                )

            else:
                currency = request.GET.get("currency", None)
                order = request.GET.get("order", None)
                if order:
                    order = ShopTurboOrders.objects.get(id=order)

                ShopTurboShippingCosts = None
                if currency:
                    ShopTurboShippingCosts = ShopTurboShippingCost.objects.filter(
                        workspace=workspace,
                        number_format__in=[currency.upper(), currency.lower()],
                    ).order_by("created_at")

                context = {
                    "checker": checker,
                    "currency_model": CURRENCY_MODEL,
                    "order": order,
                    "currency": currency,
                    "ShopTurboShippingCosts": ShopTurboShippingCosts,
                    "ShopTurboOrderLevelTaxes": ShopTurboOrderLevelTax.objects.filter(
                        workspace=workspace
                    ).order_by("tax"),
                    "set_id": set_id,
                    "view_id": view_id,
                    "form_id": form_id,
                }
                return render(
                    request,
                    "data/shopturbo/shopturbo-create-orders-select-order-type.html",
                    context,
                )

        elif type == "create-contacts":
            add_on = request.GET.get("addon_source", "addon-order")
            module = request.GET.get("module", None)
            if module == "":
                module = None

            companies = Company.objects.filter(workspace=workspace)
            contact_list = ContactList.objects.filter(workspace=workspace)

            properties, CustomFieldMap = get_default_prompt_set(
                workspace, TYPE_OBJECT_CONTACT, ContactsNameCustomField
            )

            context = {
                "companies": companies,
                "contact_list": contact_list,
                "properties": properties,
                "CustomFieldMap": CustomFieldMap,
                "create_type": add_on,
                "country_code": COUNTRY_CODE,
                "page_type": TYPE_OBJECT_CONTACT,
                "associate_id": request.GET.get("associate_id", ""),
                "module": module,
            }
            return render(
                request, "data/contacts/account-explorer-contacts-drawer.html", context
            )

        elif type == "add-contact-company":
            order_id = request.GET.get("order_id", None)
            order = ShopTurboOrders.objects.filter(id=order_id).first()
            contact_id_preselected = request.GET.get("contact_id", None)
            company_id_preselected = request.GET.get("company_id", None)

            contact_preselected = None
            company_preselected = None
            if contact_id_preselected:
                contact_preselected = Contact.objects.filter(
                    id=contact_id_preselected
                ).first()
            if company_id_preselected:
                company_preselected = Company.objects.filter(
                    id=company_id_preselected
                ).first()
            context = {
                "order": order,
                "contact_preselected": contact_preselected,
                "company_preselected": company_preselected,
            }
            return render(
                request, "data/shopturbo/manage-orders-contact-company.html", context
            )

        elif type == "create-contacts-manage":
            add_on = request.GET.get("addon_source", "addon-order-manage")
            order_id = request.GET.get("order_id", "")

            companies = Company.objects.filter(workspace=workspace)
            contact_list = ContactList.objects.filter(workspace=workspace)

            properties, CustomFieldMap = get_default_prompt_set(
                workspace, TYPE_OBJECT_CONTACT, ContactsNameCustomField
            )

            context = {
                "companies": companies,
                "contact_list": contact_list,
                "properties": properties,
                "CustomFieldMap": CustomFieldMap,
                "create_type": add_on,
                "country_code": COUNTRY_CODE,
                "page_type": TYPE_OBJECT_CONTACT,
                "associate_id": request.GET.get("associate_id", ""),
                "order_id": order_id,
            }
            return render(
                request, "data/contacts/account-explorer-contacts-drawer.html", context
            )

        elif type == "create-company-manage":
            add_on = request.GET.get("addon_source", "addon-order-manage")
            order_id = request.GET.get("order_id", "")
            company_list = CompanyList.objects.filter(workspace=workspace)

            properties, CustomFieldMap = get_default_prompt_set(
                workspace, TYPE_OBJECT_COMPANY, CompanyNameCustomField
            )

            context = {
                "create_type": add_on,
                "company_list": company_list,
                "country_code": COUNTRY_CODE,
                "page_type": "companies",
                "properties": properties,
                "CustomFieldMap": CustomFieldMap,
                "associate_id": request.GET.get("associate_id", ""),
                "order_id": order_id,
            }
            return render(
                request,
                "data/contacts/account-explorer-contacts-company-drawer.html",
                context,
            )

        elif type == "bulk-action":
            order_ids = request.GET.getlist("order_ids")
            preselect_order_ids = []
            if request.GET.get("order_ids", None):
                # Handle different formats of order_ids
                if len(order_ids) == 1:
                    # Check if it's a string representation of a JavaScript array
                    if order_ids[0].startswith("[") and order_ids[0].endswith("]"):
                        # Parse JavaScript array format like "['id1','id2']"
                        import re

                        order_ids = re.findall(r"'([^']*)'", order_ids[0])
                    elif "," in order_ids[0]:
                        # Handle comma-separated string
                        order_ids = request.GET.get("order_ids", "").split(",")

                orders = ShopTurboOrders.objects.filter(id__in=order_ids)
                preselect_order_ids = [str(order.id) for order in orders]

            page = request.GET.get("page", 1)
            context = {
                "object_action": {
                    "additional_params": {"order_ids": preselect_order_ids},
                },
                "object_type": TYPE_OBJECT_ORDER,
                "page": page,
                "module": module_slug,
            }
            return render(
                request, "data/shopturbo/shopturbo-manage-action.html", context
            )
        else:
            return HttpResponse(200)

    elif drawer_type == "shopturbo-view-sync-orders":
        section = request.GET.get("section", None)

        if section == "integrations":
            import_export_type = request.GET.get("import_export_type", None)

            order_ids = request.GET.get("order_ids", None)
            if order_ids:
                order_ids = order_ids.split(",")
            else:
                if import_export_type == "export":
                    order_ids = ShopTurboOrders.objects.filter(
                        workspace=workspace, status="active"
                    ).values_list("id", flat=True)

            if import_export_type == "export":
                platforms = [
                    "hubspot",
                    "shopify",
                    "ecforce",
                    "nextengine",
                    "freee",
                    "yahoo-shopping",
                ]
            else:
                platforms = [
                    "shopify",
                    "amazon",
                    "square",
                    "ecforce",
                    "nextengine",
                    "rakuten",
                    "freee",
                    "ec-cube",
                    "makeshop",
                    "yahoo-shopping",
                    "b-cart",
                    "wordpress",
                    "salesforce",
                    "ebay",
                    "hubspot",
                ]

            channels = Channel.objects.filter(
                Q(
                    workspace=get_workspace(request.user),
                    integration__slug__in=platforms,
                )
                | Q(
                    workspace=get_workspace(request.user),
                    api_key=settings.HUBSPOT_CLIENT_ID,
                )
            ).exclude(status="draft")

            for name in EXCLUDE_SYNC_CHANNEL_NAME:
                channels = channels.exclude(name__icontains=name)

            channels = channels.filter(
                integration__slug__in=[
                    platform for platform in platforms
                ]
            )

            count_orders = len(order_ids)

            order_all_filters = ShopTurboOrdersNameCustomField.objects.filter(
                workspace=workspace
            )
            order_filters = ShopTurboOrdersNameCustomField.objects.filter(
                workspace=workspace, type="choice"
            )

            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module:
                module_slug = module.slug

            if import_export_type == "export":
                history = TransferHistory.objects.filter(
                    workspace=workspace, type="export_order"
                ).order_by("-created_at")[0:10]
            else:
                history = TransferHistory.objects.filter(
                    workspace=workspace, type="import_order"
                ).order_by("-created_at")[0:10]
            if lang == "ja":
                task_status = dict(TASK_STATUS_JA)
            else:
                task_status = dict(TASK_STATUS)

            columns = []
            itemsnamecustomfield = (
                ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace, unique=True
                )
                .exclude(type__in=["image", "user", "formula"])
                .values_list("name", flat=True)
            )
            columns.extend(itemsnamecustomfield)

            customer_columns = []
            customer_custom_fields = CompanyNameCustomField.objects.filter(workspace=workspace, unique=True).exclude(
                type__in=["image", "user", "formula"]).values_list('name', flat=True)
            customer_columns.extend(customer_custom_fields)

            customer_columns = []
            customer_custom_fields = CompanyNameCustomField.objects.filter(workspace=workspace, unique=True).exclude(
                type__in=["image", "user", "formula"]).values_list('name', flat=True)
            customer_columns.extend(customer_custom_fields)

            all_channels = Channel.objects.filter(workspace=workspace).exclude(
                status="draft"
            )
            if import_export_type == "export":
                distinct_slugs = {channel.integration.slug for channel in all_channels}
            else:
                distinct_slugs = {
                    channel.integration.slug
                    for channel in channels
                    if channel.integration.slug != "hubspot"
                }

            has_hubspot = False
            if "hubspot" in Channel.objects.filter(workspace=workspace).exclude(
                status="draft"
            ).values_list("integration__slug", flat=True):
                has_hubspot = True

            additional_action_channels = Channel.objects.filter(
                workspace=workspace, integration__slug="hubspot"
            ).exclude(status="draft")
            
            # Get Active Channel IDs from History
            channel_ids = None
            history_type = "export_order" if import_export_type == "export" else "import_order"
            history_active_channel = TransferHistory.objects.filter(
                workspace=workspace, type=history_type, channel__isnull=False, status='running',
            ).order_by("-created_at")
            channel_ids = [str(channel_id) for channel_id in history_active_channel.values_list('channel_id', flat=True)] if history_active_channel else None

            context = {
                "channels": channels,
                "order_ids": order_ids,
                "count_orders": count_orders,
                "filters": order_filters,
                "object_type": TYPE_OBJECT_ORDER,
                "menu_key": module_slug,
                "history": history,
                "task_status": task_status,
                "import_export_type": import_export_type,
                "columns": columns,
                'customer_columns': customer_columns,
                "nextengine_filters": order_all_filters,
                "distinct_slugs": distinct_slugs,
                "has_hubspot": has_hubspot,
                "additional_action_channels": additional_action_channels,
                "channel_ids": channel_ids or [],
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-orders-import-export.html",
                context,
            )

    else:
        return HttpResponse(200)
