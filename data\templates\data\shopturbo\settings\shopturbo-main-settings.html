{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

{% if setting_type|in_list:'commerce_items,commerce_inventory,commerce_inventory_warehouse,commerce_inventory_transaction' %}
    {% include 'data/shopturbo/settings/shopturbo-items-settings.html' %}
{% elif setting_type|in_list:'commerce_orders,commerce_subscription,estimates,receipts,slips,invoices,delivery_slips' %}
    {% include 'data/shopturbo/settings/shopturbo-orders-settings.html' %}
{% elif setting_type|in_list:'purchaseorder,purchasesupplier,purchaseitem,expenses,bill,expense' %}
    {% include 'data/shopturbo/settings/shopturbo-procurement-settings.html' %}
{% elif setting_type|in_list:'worker' %}
    {% include 'data/shopturbo/settings/shopturbo-worker-settings.html' %}
{% elif setting_type|in_list:'journal' %}
    {% include 'data/shopturbo/settings/accounting-settings.html' %}
{% elif setting_type|in_list:'session_event' %}
    {% include 'data/shopturbo/settings/shopturbo-event-settings.html' %}
{% elif setting_type|in_list:'contract' %}
    {% include 'data/shopturbo/settings/contract-settings.html' %}
{% elif setting_type|in_list:'panels' %}
    {% include 'data/shopturbo/settings/panel-settings.html' %}
{% elif setting_type|in_list:'dashboards' %}
    {% include 'data/shopturbo/settings/dashboard-settings.html' %}
{% elif setting_type|in_list:'campaigns' %}
    {% include 'data/shopturbo/settings/campaign-settings.html' %}
{% elif setting_type|in_list:'jobs' %}
    {% include 'data/jobs/jobs-settings.html' %}
    
{% elif setting_type != 'module' %}
    {% comment %} Other object but not module page {% endcomment %}
    {% comment %} {% include 'data/shopturbo/settings/basic-object-settings.html' %} {% endcomment %}
{% endif %}

<style>
    .items-part:hover .task-grip svg{
        color: #211F1C !important;
    }
    .item_amount{
        max-width:13%;
    }
</style>

<style>
    .stretch-select-custom span {
        height:100% !important;
    }
    .stretch-select-custom span span span span{
        display: flex !important;
        align-content: center;
        flex-wrap: wrap;
        margin-bottom: auto;
        margin-top: auto;
    }
</style>

{% endblock %}

{% block js %}
<script>
    $(document).ready(function(){
        $('.select2-this').select2();  
    })

    function copyToClipboard(elm) {
        var customfield_id = elm.id.split('_');
        customfield_id = customfield_id[customfield_id.length - 1];
        const anchorTag = document.getElementById('copyText_'+customfield_id);
        let customfield = anchorTag.getAttribute('customfield');
        const textArea = document.createElement('textarea');
        textArea.value = customfield;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    
        const copyStatus = document.getElementById('copyStatus_'+customfield_id);
        copyStatus.classList.remove('d-none');
        setTimeout(() => {
            copyStatus.classList.add('d-none');
        }, 1000); // Hide the message after 2 seconds
    }


    
    function delete_orders_custom_field_name(elm){
        elm.parentElement.parentElement.parentElement.parentElement.remove()
    }
    function delete_items_custom_field_name(elm){
        elm.parentElement.parentElement.parentElement.parentElement.remove()
    }
    function delete_subscriptions_custom_field_name(elm){
        elm.parentElement.parentElement.parentElement.parentElement.remove()
    }



    function add_tax(elm){
        var tax_field_Container = document.createElement('div')
        var custom_field_ElmId = String(Date.now())
        var custom_field_Elm_Str = 
        `
        <div class="">
            <div class="d-flex">

                <div class="mb-3 input-group">
                    <input name='tax' type="text" class="form-control" />
                    <div class="input-group-prepend">
                        <span class="input-group-text h-40px" style="border-radius: 0px 12px 12px 0px;">%</span>
                    </div>
                </div> 
                <div class="input-group-append ms-2">
                    <button class="btn btn-danger" onclick='delete_tax(this);' type="button">X</button>
                </div> 

            </div>
        </div>
        `

        tax_field_Container.insertAdjacentHTML('beforeend', custom_field_Elm_Str)
        elm.parentElement.parentElement.insertBefore(tax_field_Container, elm.parentElement.parentElement.firstChild.nextSibling.nextSibling)

    }

    function add_order_level_tax(elm){
        var tax_field_Container = document.createElement('div')
        var custom_field_ElmId = String(Date.now())
        var custom_field_Elm_Str = 
        `
        <div class="">
            <div class="d-flex">

                <div class="mb-3 input-group">
                    <input name='tax' type="text" class="h-40px form-control" />
                    <div class="input-group-prepend">
                        <span class="input-group-text h-40px" style="border-radius: 0px 12px 12px 0px;">%</span>
                    </div>
                </div> 
                <div class="input-group-append ms-2">
                    <button class="btn btn-danger" onclick='delete_tax(this);' type="button">X</button>
                </div> 

            </div>
        </div>
        `

        tax_field_Container.insertAdjacentHTML('beforeend', custom_field_Elm_Str)
        elm.parentElement.parentElement.insertBefore(tax_field_Container, elm.parentElement.parentElement.firstChild.nextSibling.nextSibling)

    }


    
    function delete_tax(elm){
        elm.parentElement.parentElement.parentElement.remove()
    }



    function add_discount(elm){
        var tax_field_Container = document.createElement('div')
        var custom_field_ElmId = String(Date.now())
        var custom_field_Elm_Str = 
        `
        <div class="">
            <input type="hidden" name="discount_field_id" value="" ></input>

            <div class="d-flex ">
                <div class="input-group flex-nowrap align-items-strecth">  
                    <div class="input-prehead">
                        <select class="bg-white border min-h-40px form-select form-select-solid input-prehead select2-this" name="discount_field_number_format" data-placeholder="value">
                            {% for number_format in number_formats %} 
                                {% if number_format.0 != 'number' %}
                                <option value="{{number_format.0}}">
                                        {{number_format.1}}
                                </option> 
                                {% endif %}   
                            {% endfor %}
                        </select>
                    </div>

                    <input class="form-control w-50 h-40px" name="discount_field_value"
                        {% if LANGUAGE_CODE == 'ja'%}
                        placeholder="割引の値" 
                        {% else %}
                        placeholder="Discount Value" 
                        {% endif %}
                      
                    />
                    
                    <input class="form-control d-flex w-25 h-40px" name="discount_field_name"
                        {% if LANGUAGE_CODE == 'ja'%}
                        placeholder="割引名" 
                        {% else %}
                        placeholder="Discount Name" 
                        {% endif %}

                       
                    />
                </div>
                <div class="input-group-append">
                    <button class="btn btn-danger btn-sm ms-1" onclick='delete_discount(this);' type="button">X</button>
                </div> 
            </div>
        </div>
        `

        tax_field_Container.insertAdjacentHTML('beforeend', custom_field_Elm_Str)
        elm.parentElement.parentElement.insertBefore(tax_field_Container, elm.parentElement.parentElement.firstChild.nextSibling.nextSibling)
        
        $('.select2-this').select2();  

    }

    
    function add_shippings(elm){
        var tax_field_Container = document.createElement('div')
        var custom_field_ElmId = String(Date.now())
        var custom_field_Elm_Str = 
        `
        <div class="mb-5">
            <input type="hidden" name="shipping_field_id" value="" ></input>

            <div class="d-flex ">
                <div class="input-group flex-nowrap align-items-strecth">  
                    <div class="input-prehead">
                        <select class="bg-white border min-h-40px form-select form-select-solid input-prehead select2-this" name="shipping_field_number_format" data-placeholder="value">
                            {% if workspace.currencies %}
                                <optgroup
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    label="デフォルト通貨"
                                    {% else %}
                                    label="Default Currency"
                                    {% endif %}
                                >
                                    {% for currency in workspace.currencies|string_list_to_list %}
                                        <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                    {% endfor %}

                                </optgroup>
                            {% endif %}

                            <optgroup
                                {% if LANGUAGE_CODE == 'ja'%}
                                label="すべての通貨"
                                {% else %}
                                label="All Currencies"
                                {% endif %}
                            >
                            {% include 'data/partials/money-currency.html' %}
                            </optgroup>
                        </select>
                    </div>

                    <input class="form-control w-50 h-40px" name="shipping_field_value"
                        {% if LANGUAGE_CODE == 'ja'%}
                        placeholder="送料の値" 
                        {% else %}
                        placeholder="Shipping Price Value" 
                        {% endif %}
                    />
                    
                    <input class="form-control d-flex w-25 h-40px" name="shipping_field_name"
                        {% if LANGUAGE_CODE == 'ja'%}
                        placeholder="送料名" 
                        {% else %}
                        placeholder="Shipping Fee Name" 
                        {% endif %}
                    />
                </div>
                <div class="input-group-append">
                    <button class="btn btn-danger btn-sm ms-1" onclick='delete_discount(this);' type="button">X</button>
                </div> 
            </div>
        </div>
        `

        tax_field_Container.insertAdjacentHTML('beforeend', custom_field_Elm_Str)
        elm.parentElement.parentElement.insertBefore(tax_field_Container, elm.parentElement.parentElement.firstChild.nextSibling.nextSibling)
        
        $('.select2-this').select2();  

    }



  
    function delete_subscription_shipping(elm){
        elm.parentElement.parentElement.parentElement.remove()
    }
    
    function add_subscription_shippings(elm){
        var shipping_fee_section = document.getElementById('shipping_fee_section');
        var new_shipping = document.createElement('div');
        new_shipping.className = 'mb-5';
        new_shipping.innerHTML = `
            <div class="d-flex">
                <input type="hidden" name="subscription_shipping_field_id" value="" ></input>

                <div class="input-group flex-nowrap align-items-strecth">  
                    <div class="input-prehead">
                        <select class="{% include 'data/utility/select-form.html' %} input-prehead select2-this" name="subscription_shipping_field_number_format" data-placeholder="value">
                            {% if workspace.currencies %}
                            <optgroup
                                {% if LANGUAGE_CODE == 'ja'%}
                                label="デフォルト通貨"
                                {% else %}
                                label="Default Currency"
                                {% endif %}
                            >
                                {% for currency in workspace.currencies|string_list_to_list %}
                                    {% if forloop.counter == 1 %}
                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                    {% else %}
                                        <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                    {% endif %}
                                {% endfor %}
                            </optgroup>
                            {% endif %}

                            <optgroup
                                {% if LANGUAGE_CODE == 'ja'%}
                                label="すべての通貨"
                                {% else %}
                                label="All Currencies"
                                {% endif %}
                            >
                            {% include 'data/partials/money-currency.html' %}
                            </optgroup>
                        </select>
                    </div>

                    <input class="form-control w-50 h-40px" name="subscription_shipping_field_value"
                        {% if LANGUAGE_CODE == 'ja'%}
                        placeholder="送料の値" 
                        {% else %}
                        placeholder="Shipping Price Value" 
                        {% endif %}
                    />
                    
                    <input class="form-control d-flex w-25 h-40px" name="subscription_shipping_field_name"
                        {% if LANGUAGE_CODE == 'ja'%}
                        placeholder="送料名" 
                        {% else %}
                        placeholder="Shipping Fee Name" 
                        {% endif %}
                    />
                </div>
                <div class="input-group-append">
                    <button class="btn btn-danger" onclick='delete_subscription_shipping(this);' type="button">X</button>
                </div> 
            </div>
        `;
        shipping_fee_section.appendChild(new_shipping);
        
        // Initialize Select2 for the new dropdown
        $(new_shipping).find('select').select2();
    }



    function delete_discount(elm){
        elm.parentElement.parentElement.parentElement.remove()
    }

</script>



<script>
    $(document).ready(function(){
        $('.task_wizard .select2-this').select2();
    })

    function checking_type(elm){
        console.log(elm.value);
        var number_format_id = elm.id.replace('type', 'number_format');
        var number_format_elm=document.getElementById(number_format_id)

        var choice_id = elm.id.replace('type', 'choice');
        var choice_elm=document.getElementById(choice_id)

        if (elm.value=='number'){
            if (number_format_elm){
                number_format_elm.classList.remove("d-none")
                choice_elm.classList.add("d-none")
            }
            
        } 
        else if (elm.value=='choice' ){
            if (choice_elm){
                choice_elm.classList.remove("d-none")
                number_format_elm.classList.add("d-none")
            }
        }
        else if (elm.value=='formula'){
            if (choice_elm){
                choice_elm.classList.add("d-none")
                // if (number_format_elm){number_format_elm.classList.add("d-none")}
                
                var input_choice_id = choice_id.replace('choice', 'formula');
                var input_choice_elm=document.getElementById(input_choice_id);
                input_choice_elm.classList.remove("d-none")
                // new Tagify(input_choice_elm);
            }
        }
        else{
            if (number_format_elm){
                number_format_elm.classList.add("d-none")
            }
            if (choice_elm){
                choice_elm.classList.add("d-none")
            }
        }
    }
</script>






{% endblock %}