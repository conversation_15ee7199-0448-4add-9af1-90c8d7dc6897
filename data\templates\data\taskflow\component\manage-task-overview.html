{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

{% if task %}
    {% if task.usage_status == 'archived' %}
        <div class="mt-0 mb-4 card-body">
            <div class="mb-10">
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span>
                        ID
                    </span>
                </label>
                <span class="mt-5 fw-bolder fs-4">
                    {{task.task_id|stringformat:"04d" }}
                </span>
            </div>
            <div class="fv-rowd-flex flex-column">
                <h3>
                    {% if LANGUAGE_CODE == 'ja'%}
                    このレコードはアーカイブされました。有効化して使用してください。
                    {% else %}
                    This record was archived. Please activate to see and use this record.
                    {% endif %}
                </h3>

                <form method="POST" action="{% host_url 'task_form_usage_status' task.id host 'app' %}" >
                    {% csrf_token %}
                    {% if view_id %}<input type="hidden" name="view_id" value="{{view_id}}">{% endif %}
                    {% if p_id %}<input type="hidden" name="p_id" value="{{p_id}}">{% endif %}
                    <input type="hidden" name="fn" value="activate">
                    <div class="border-0">
                        <button type="submit" name="restore-task" class="btn btn-success">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    {% endif %}
{% endif %}

{% if task.usage_status == 'active' %}
    <div id="task-overviews" class="tab-switcher-content">
        <div class="card-body w-100 pb-0 d-flex">
            <div class="w-100">
                {% if type != 'create-sub-task' %}
                <form method="POST" action="{% host_url 'task_form' host 'app' %}" enctype="multipart/form-data"
                    onsubmit="console.log('[TASK_FORM_DEBUG] Form submitting'); console.log('[TASK_FORM_DEBUG] view_id input:', document.querySelector('input[name=view_id]')?.value); return validateForm(event)"
                    id="task-form-{{type}}">
                {% else %}
                    <form 
                        id="task-form-{{type}}"
                        hx-post="{% host_url 'create_subtask' host 'app' %}" 
                        hx-trigger="on-submit-validate-form" 
                        hx-swap="innerHTML"
                        hx-target="#dataSubTaskItems"
                        hx-indicator=".loading-subtask-spinner"
                        onsubmit="return validateForm(event, '{{type}}')"
                    >
                {% endif %}
                    {% csrf_token %}
                    {% if view_id %}<input type="hidden" name="view_id" value="{{view_id}}">{% endif %}
                    {% if from %}<input type="hidden" name="from" value="{{from}}">{% endif %}
                    {% if task %}<input type="hidden" name="task_id" value="{{task.id}}">{% endif %}
                    {% if p_id %}<input type="hidden" name="p_id" value="{{p_id}}">{% endif %}

                    <!-- Debug: Show view_id value -->
                    <script>console.log('[TASK_FORM_DEBUG] Template view_id:', '{{view_id}}');</script>

                    {% if source %}<input type="hidden" name="source" value="{{source}}">{% endif %}
                    {% if object_id %}<input type="hidden" name="object_id" value="{{object_id}}">{% endif %}
                    {% if module %}<input type="hidden" name="module" value="{{module}}">{% endif %}

                    <div class="mb-10">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span>
                                ID
                            </span>
                        </label>
                        <span class="mt-5 fw-bolder fs-4">
                            {{task.task_id|stringformat:"04d" }}
                        </span>
                    </div>
                    
                    {% for property in properties.list_all %}
                        {% if property == 'title' %}
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="required">
                                        {% if LANGUAGE_CODE == 'ja' %}タスク名{% else %}Name{% endif %}
                                    </span>
                                </label>

                                <textarea required name="title" placeholder={% if LANGUAGE_CODE == 'ja'%}"例：議事録の作成"{% else %}"E.g. Create meeting notes"{% endif %}  
                                    id="task-form-name" class="form-control" rows="1">{% if task.title %}{{task.title}}{% endif %}</textarea>
                                <script>
                                    function dynamicHeightTextarea(elm) {
                                        elm.style.height = '1px'
                                        if (elm.clientHeight < elm.scrollHeight) {
                                            const lineHeight = parseInt(getComputedStyle(elm).lineHeight);
                                            const paddingTop = parseInt(getComputedStyle(elm).paddingTop);
                                            const paddingBottom = parseInt(getComputedStyle(elm).paddingBottom);
                                            const totalPadding = paddingTop + paddingBottom;
                                            const contentHeight = elm.scrollHeight - totalPadding;
                                            const effectiveRows = Math.floor(contentHeight / lineHeight);
                                            console.log(contentHeight / lineHeight, contentHeight, lineHeight)
                                            elm.rows = effectiveRows < 1 ? 1 : effectiveRows;
                                            elm.style.height = ''
                                        }
                                    }
                                    tfn = document.getElementById("task-form-name")
                                    dynamicHeightTextarea(tfn)
                                    tfn.addEventListener("input", function() {
                                        dynamicHeightTextarea(this)
                                    });
                                </script>
                            </div>     
                        
                        {% elif property == 'assignee' %}        
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}ユーザー{% else %}Users{% endif %}
                                    </span>
                                </label>
                                <select id="task-users-{{task.id}}" multiple class="form-select form-select-solid border bg-white select2-this-task-users-{{task.id}}" data-control="select2" name="users" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}ユーザー{% else %}Users{% endif %}">
                                    {% for user in workspace_users %}
                                        <option value="user|{{user.username}}" 
                                        {% if task %}
                                            {% if user in task.assignee.all %}selected{% endif %}
                                        {% else %}
                                            {% if user == member %}selected{% endif %}
                                        {% endif %}
                                        >{{user.first_name}} - {{user.email}}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <style>
                                .submission-indicator{
                                    display:none;
                                }
                                .htmx-request .submission-indicator{
                                    display:inline-block;
                                }
                                .htmx-request.submission-indicator{
                                    display:inline-block;
                                }
                            </style>
                            <span id="cost-spinner" class="d-none submission-indicator spinner-border spinner-border-sm text-secondary" style="margin-left: auto; margin-right: auto; margin-top: auto; margin-bottom: auto;" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </span>
                            <script>
                                {% if task %}
                                    $('.select2-this-task-users-{{task.id}}').select2();
                                    $('.select2-this-task-users-{{task.id}}').on('change', function (e) {
                                        var selectElement = $(this).closest('select').get(0);
                                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                        htmx.trigger("body", "updateTask");
                                    });
                                    {% else %}
                                    $('#create_task_wizard .select2-this-task-users-{{task.id}}').select2();
                                    $('.select2-this-task-users-{{task.id}}').on('change', function (e) {
                                        var selectElement = $(this).closest('select').get(0);
                                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                        htmx.trigger("body", "updateTask");
                                    });
                                {% endif %}
                            </script>

                        {% elif property == 'owner' %}
                            <div class="owner-form-{{object_type}} mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="min-w-100px">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        所有者
                                        {% else %}
                                        Owner
                                        {% endif %}
                                    </span>
                                </label>
                                <select data-allow-clear='true' id="owner-input-{{object_type}}" class="h-40px form-select form-select-solid border bg-white select2-this" data-control="select2" name="owner" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}所有者{% else %}Owner{% endif %}">
                                    <option></option>
                                    {% if task.id and task.owner and task.owner.user %}
                                        <option value="{{task.owner.user.username}}" selected>
                                            {{task.owner.user.first_name}} - {{task.owner.user.email}}
                                        </option>
                                    {% endif %}
                                    {% for member in permission|get_scope_from_permission:'edit'|get_users_by_scope:user %}
                                        {% if member != task.owner.user %}
                                        <option value="{{member.username}}">
                                            {{member.first_name}} - {{member.email}}
                                        </option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
        
                                <script>
                                    $('.select2-this').select2()
                                </script>
                            </div>
                        {% elif property == 'status' %}        
                            {% comment %} Status Form {% endcomment %}
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}ステータス{% else %}Status{% endif %}
                                    </span>
                                </label>
                                <select  
                                    id="task-status-{{task.id}}"
                                    class="h-40px form-select form-select-solid border bg-white select2-this-{{task.id}}"
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    data-placeholder="ステータス"
                                    {% else %}
                                    data-placeholder="Select Status"
                                    {% endif %}
                                    name='status'
                                    data-allow-clear="true"
                                >
                                    <option value=""></option>
                                    {% if 'status'|get_custom_property_object:task %}
                                        {% with value_map_label='status'|get_custom_property_object:task|get_attr:'value'|string_list_to_list %}
                                            {% for value, label in value_map_label.items %}
                                                <option value="{{value}}" {% if task.status == value %}selected{% endif %}>
                                                    {{label}}
                                                </option>
                                            {% endfor %}
                                        {% endwith %}
                                    {% else %}
                                        {% for task_status in PROJECT_TASK_STATUS %}
                                            {% if task.status ==  task_status.0%}
                                            <option value="{{task_status.0}}" selected>
                                                {% get_task_status task_status.0 LANGUAGE_CODE %}
                                            </option>
                                            {% else %}
                                            <option value="{{task_status.0}}">
                                                {% get_task_status task_status.0 LANGUAGE_CODE %}
                                            </option>
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                </select>
                            </div>

                            <script>
                                if ($('#workflow-form .select2-this').length) {
                                    $('#workflow-form .select2-this').select2();
                                    $('#workflow-form .select2-this').on('change', function (e) {
                                        var selectElement = $(this).closest('select').get(0);
                                        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                    });
                                }
                                $('.select2-this-{{task.id}}').select2();
                            </script>

                        {% elif property == 'line_item' %}
                            <input hidden name="task_line_items"></input>
                            {% get_predefined_currency_line_items custom_field_value as predefined_currency%}
                
                            <div class="fv-rowd-flex flex-column mb-8">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span class="min-w-100px">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        通貨
                                        {% else %}
                                        Currency
                                        {% endif %}
                                    </span>
                                </label>
                                
                                <div class="mb-2">
                                    <select 
                                        id="currency-select-{{constant.TYPE_OBJECT_TASK}}" 
                                        name="price-info-currency" 
                                        class="bg-white form-select form-select-solid border h-40px select2-this"

                                        {% if LANGUAGE_CODE == 'ja'%}
                                        data-placeholder="通貨の選択"
                                        {% else %}
                                        data-placeholder="Select Currency"
                                        {% endif %}
                                    >  
                                        {% if workspace.currencies %}
                                            <optgroup
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                label="デフォルト通貨"
                                                {% else %}
                                                label="Default Currency"
                                                {% endif %}
                                            >
                                                {% for currency in workspace.currencies|string_list_to_list %}
                    
                                                    {% if forloop.counter == 1 %}
                                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>                         
                                                    {% elif currency == predefined_currency %}
                                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>                         
                                                    {% else %}
                                                        <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>                         
                                                    {% endif %}
                                                {% endfor %}
                    
                                            </optgroup>
                                        {% endif %}
                                            <optgroup
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                label="すべての通貨"
                                                {% else %}
                                                label="All Currency"
                                                {% endif %}
                                            >
                                            {% for currency in currency_model|get_currencies %}
                                                {% if not currency.0 in workspace.currencies|string_list_to_list %}
                                                    {% if currency.0 == predefined_currency %}
                                                    <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                                    {% elif not predefined_currency and not workspace.currencies and LANGUAGE_CODE == 'en' and currency.0 == 'USD' %}
                                                    <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                                    {% elif not predefined_currency and not workspace.currencies and LANGUAGE_CODE == 'ja' and currency.0 == 'JPY' %}
                                                    <option value="{{currency.0}}" selected >{{currency.1}}</option>
                                                    {% else %}
                                                    <option value="{{currency.0}}">{{currency.1}}</option>
                                                    {% endif %}
                                                {% endif %}
                                            {% endfor %}
                                        </optgroup>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-2"
                                    id="price-task-line-items"
                                    hx-get="{% host_url 'load_object_price_info' host 'app' %}"
                                    hx-trigger="load,price-item-trigger"
                                    hx-target="#price-info"
                                    hx-swap="innerHTML"
                                    hx-vals='js:{"obj_id":"{{task.id}}","obj_type":"{{constant.TYPE_OBJECT_TASK}}", "currency":document.getElementById("currency-select-{{constant.TYPE_OBJECT_TASK}}").value }'
                                >
                                <div id="price-info"></div>
                            </div>
                
                            <script>
                                $('#currency-select-{{constant.TYPE_OBJECT_TASK}}').on('change', function (e) {
                                    document.getElementById('price-task-line-items').dispatchEvent(new Event('price-item-trigger'));
                                })
                                function delete_item(elm){
                                    elm.parentElement.parentElement.remove()
                                }

                                $('#currency-select-{{constant.TYPE_OBJECT_TASK}}').select2();
                            </script>

                        {% elif property == 'start_date' %}
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}開始日{% else %}Start Date{% endif %}
                                    </span>
                                </label>
                                <input autocomplete="off" placeholder="{% if LANGUAGE_CODE == 'ja'%}開始日{% else %}Start Date{% endif %}" 
                                    name="start_date" 
                                    {% if task %}
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        value="{{task.start_date|date:'Y年m月d日'}}"
                                        {% else %}
                                        value="{{task.start_date|date:'Y-m-d'}}"
                                        {% endif %}
                                    {% endif %}
                                    id="start_date_field-{{type}}" 
                                    class="form-control">
                                <script>
                                    $('#start_date_field-{{type}}').daterangepicker({
                                        singleDatePicker: true,
                                        drops: 'auto',
                                        locale: {
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            format: 'YYYY年MM月DD日', // Japanese date format
                                            separator: ' 〜 ',
                                            applyLabel: '選択',
                                            cancelLabel: 'キャンセル',
                                            fromLabel: 'From',
                                            toLabel: 'To',
                                            customRangeLabel: 'カスタム範囲',
                                            daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                                            monthNames: [
                                                '1月', '2月', '3月', '4月', '5月', '6月',
                                                '7月', '8月', '9月', '10月', '11月', '12月'
                                            ],
                                            firstDay: 0
                                            {% else %}
                                            format: "Y-M-DD",
                                            {% endif %}
                                        },
                                        autoUpdateInput: false
                                    }).on('apply.daterangepicker', function(ev, picker){
                                        $(this).val(picker.startDate.format(picker.locale.format));
                                    })
                                </script>
                            </div>

                        {% elif property == 'due_date' %}
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}期限{% else %}Due Date{% endif %}
                                    </span>
                                </label>
                                <input autocomplete="off" placeholder="{% if LANGUAGE_CODE == 'ja'%}期限{% else %}Due Date{% endif %}" 
                                    name="due_date" 
                                    {% if task %}
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        value="{{task.due_date|date:'Y年m月d日'}}"
                                        {% else %}
                                        value="{{task.due_date|date:'Y-m-d'}}"
                                        {% endif %}
                                    {% endif %}
                                    id="due_date_field-{{type}}" 
                                    class="form-control">
                                <script>
                                    $('#due_date_field-{{type}}').daterangepicker({
                                        singleDatePicker: true,
                                        drops: 'auto',
                                        locale: {
                                            {% if LANGUAGE_CODE == 'ja' %}
                                            format: 'YYYY年MM月DD日', // Japanese date format
                                            separator: ' 〜 ',
                                            applyLabel: '選択',
                                            cancelLabel: 'キャンセル',
                                            fromLabel: 'From',
                                            toLabel: 'To',
                                            customRangeLabel: 'カスタム範囲',
                                            daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                                            monthNames: [
                                                '1月', '2月', '3月', '4月', '5月', '6月',
                                                '7月', '8月', '9月', '10月', '11月', '12月'
                                            ],
                                            firstDay: 0
                                            {% else %}
                                            format: "Y-M-DD",
                                            {% endif %}
                                        },
                                        autoUpdateInput: false
                                    }).on('apply.daterangepicker', function(ev, picker){
                                        $(this).val(picker.startDate.format(picker.locale.format));
                                    })
                                </script>
                            </div>
                        
                        {% elif property == 'description' %}        
                            <div class="mb-5">
                                <label class="{% include 'data/utility/form-label.html' %}">

                                    <span>
                                        {% if LANGUAGE_CODE == 'ja' %}説明{% else %}Description{% endif %}
                                    </span>
                                </label>

                                <textarea id="task-form-desc-{{task.id}}" class="rounded form-control w-100 autosize-this" rows="3" name="task_desc" placeholder="{% if LANGUAGE_CODE == 'ja'%}説明{% else %}Description{% endif %}" >{% if task.description %}{{task.description}}{% endif %}</textarea>
                                <script>

                                    $(document).ready(function() {
                                        // Initialize autosize on the textarea
                                        autosize($('#task-form-desc-{{task.id}}'));
                                        
                                        // Force a resize to ensure it fits the content on load
                                        setTimeout(function() {
                                            autosize.update($('#task-form-desc-{{task.id}}'));
                                        }, 100);
                                    });

                                </script>
                            </div>
                        {% elif property == 'sub_tasks' %}
                            {% if not is_hide_associated_data and type != 'create-sub-task' and type != 'create' %}
                                {% include 'data/projects/component/subtask/sub-task-menu.html' %}
                            {% endif %}
                        {% else %}
                            {% with CustomFieldName=TaskNameCustomFieldMap|get_attr:property %} 
                                {% if CustomFieldName %}
                                    <div class="fv-rowd-flex flex-column mb-8">
                                        <div class="mb-4">
                                            
                                            <span class="{% include 'data/utility/form-label.html' %} {% if CustomFieldName.required_field %} required {% endif%}">
                                                {{CustomFieldName.name }}
                                            </span>
            
                                            {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=CustomFieldName obj=task object_type=object_type %}
            
                                        </div>
                                    </div>
                                {% endif %}
                            {% endwith %}
                        {% endif %}
                    {% endfor %}
                </form>

                {% if task %}



                <div class="d-flex">
                    <div class="mb-5 me-5 w-100">
                        {% if permission|check_permission:'edit' %}

                        <div class="separator separator-dashed my-5"></div>

                        <button class="btn btn-dark text-white w-100"
                        type="submit" form="task-form-{{type}}">
                            {% if type == 'create' or type == 'create-association' or type == 'create-sub-task' %}
                                {% if LANGUAGE_CODE == 'ja'%}作成{% else %}Create{% endif %}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja'%}更新{% else %}Update{% endif %}
                            {% endif %}
                        </button>
                        {% endif %}
                    </div>
                </div>
                
                {% endif %}
            </div>
        </div>
    </div>
{% endif %}