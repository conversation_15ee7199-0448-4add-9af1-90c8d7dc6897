import os
import django
import uuid
import traceback
from django.utils import timezone
from django.conf import settings
from django.core import management

# Ensure Django is set up before any imports
# This is kept at module level as it's required for Django imports
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "sanka.settings")
django.setup()

from hatchet_sdk import Hatchet, Context
from typing import Optional
from pydantic import BaseModel
import sentry_sdk

from data.models import (
    BackgroundJob, TransferHistory, 
    WorkflowHistory, Workspace, User
)
from data.models.log import Error

from utils.discord import DiscordNotification
from datetime import timedelta
from utils.slack import Slack
from utils.logger import logger
from utils.bgjobs.handler import set_bg_job_running, set_bg_job_failed, set_bg_job_completed

from data.orders.background.export_csv_orders import export_csv_orders
from data.orders.background.import_orders import (
    import_orders,
    import_shopify_orders,
    import_hubspot_orders,
    import_hubspot_orders_subprocess,
    import_square_orders,
    import_amazon_orders,
    import_ecforce_orders,
    import_rakuten_orders,
    import_eccube_orders,
    import_makeshop_orders,
    import_yahoo_shopping_orders,
    import_bcart_orders,
    import_woocommerce_orders,
    import_salesforce_opportunities,
    import_ebay_orders,
    import_stripe_orders,
    import_nextengine_orders
)
from data.item.background.export_csv_items import export_csv_items
from data.item.background.import_items import (
    import_items,
    import_shopify_items,
    import_makeshop_items,
    import_rakuten_items,
    import_eccube_items,
    import_yahoo_shopping_items,
    import_square_items,
    import_amazon_items,
    import_stripe_items,
    import_ecforce_items,
    import_freee_items
)
from data.item.background.import_salesforce_items import import_salesforce_items_task
from data.case.background.import_salesforce_cases import import_salesforce_cases_task
from data.invoice.background.import_salesforce_invoices import import_salesforce_invoices_task
from data.contact.background.export_csv_contacts import export_csv_contacts
from data.company.background.export_csv_company import export_csv_company
from data.company.background.import_salesforce_companies import import_salesforce_companies_task
from data.company.background.import_hubspot_companies import import_hubspot_companies_task
from data.company.background.export_salesforce_companies import export_salesforce_companies_task
from data.company.background.export_hubspot_companies import export_hubspot_companies_task
from data.contact.background.import_hubspot_contacts import import_hubspot_contacts_task
from data.contact.background.import_salesforce_contacts import import_salesforce_contacts_task
from data.contact.background.import_salesforce_contacts_as_companies import import_salesforce_contacts_as_companies_task
from data.contact.background.import_shopify_contacts import import_shopify_contacts_task
from data.contact.background.import_freee_contacts import import_freee_contacts_task
from data.contact.background.import_line_contacts import import_line_contacts_task
from data.contact.background.export_hubspot_contacts import export_hubspot_contacts_task
from data.contact.background.export_hubspot_contacts_as_companies import export_hubspot_contacts_as_companies_task
from data.contact.background.export_shopify_contacts import export_shopify_contacts_task
from data.contact.background.export_freee_contacts import export_freee_contacts_task
from data.custom_object.background.import_hubspot_custom_object import import_hubspot_custom_object_task
from data.custom_object.background.import_salesforce_custom_object import import_salesforce_custom_object_task
from data.custom_object.background.export_hubspot_custom_object import export_hubspot_custom_object_task
from data.inventory.background.export_csv_inventory import export_csv_inventory
from data.inventory.background.export_csv_locations import export_csv_locations
from data.inventory_transaction.background.export_csv_inventory_transactions import export_csv_inventory_transactions
from data.case.background.export_csv_cases import export_csv_cases
from data.case.background.export_cases import export_cases
from data.accounts.background.export_csv_system_log import export_csv_system_log
from data.accounts.background.delete_records import delete_records_task
from data.accounts.background.delete_workspace import delete_workspace_task
from data.custom_object.background.export_csv_custom_object import export_csv_custom_object
from data.item.background.shopify_sync_items import (
    shopify_sync_dag,
)
from data.estimate.background.export_csv_estimates import export_csv_estimates
from data.invoice.background.export_csv_invoices import export_csv_invoices
from data.commerce.background.send_record_email_action import run_send_record_email_action
from data.workflow.background.run_workflow_action import run_workflow_action_task
from data.subscriptions.background.import_salesforce_subscriptions import import_salesforce_subscriptions_task
from data.subscriptions.background.export_hubspot_subscriptions import export_hubspot_subscriptions_custom_object_task
from data.accounts.background.fix_total_price import fix_total_price

def initialize_environment():
    """Initialize environment variables needed for the application.
    
    This function should be called explicitly during application startup
    instead of setting environment variables at module import time.
    """
    # Set the HATCHET_CLIENT_TOKEN environment variable
    os.environ.setdefault("HATCHET_CLIENT_TOKEN", settings.HATCHET_CLIENT_TOKEN)
    os.environ.setdefault("HATCHET_CLIENT_WORKER_HEALTHCHECK_ENABLED", 'true')
    
    logger.info("Environment variables initialized")

# Initialize environment variables for Hatchet to work
initialize_environment()

# Initialize Hatchet at module level for decorators to work
try:
    # Initialize Hatchet
    hatchet = Hatchet(debug=True)
    logger.info("Hatchet initialized successfully!")
except Exception as e:
    logger.error(f"Failed to initialize Hatchet: {e}")
    logger.error("Please check your HATCHET_CLIENT_TOKEN or visit https://cloud.onhatchet.run for a valid token.")
    exit(1)

# Now import Django-related modules
sentry_sdk.init(
    dsn="https://<EMAIL>/6586186" if settings.PROD else "https://<EMAIL>/4505327258370048",
    send_default_pii=True,
    _experiments={
        "enable_logs": True,
    },
)


class BackgroundJobPayload(BaseModel):
    job_id: Optional[str] = None

    workspace_id: Optional[str] = None
    user_id: Optional[str] = None
    
    transfer_history_id: Optional[str] = None
    workflow_history_id: Optional[str] = None

    function: Optional[str] = None
    payload: Optional[dict] = None


@hatchet.task(name="RunBackgroundJob", input_validator=BackgroundJobPayload, execution_timeout=timedelta(hours=12), schedule_timeout=timedelta(hours=1))
def run_background_job(input: BackgroundJobPayload, ctx: Context):
    job_id = input.job_id or str(uuid.uuid4())

    job, created = BackgroundJob.objects.get_or_create(
        job_id=job_id,
        defaults={
            "name": f"[Background Job] {input.function}",
            "payload": input.payload,
            "status": BackgroundJob.BackgroundJobStatus.PENDING,

            "log": []
        }
    )
    if created:
        if input.workspace_id:
            try:
                workspace = Workspace.objects.get(id=input.workspace_id)
                job.workspace = workspace
                job.save()
            except Workspace.DoesNotExist:
                logger.error(f"Workspace {input.workspace_id} does not exist")
        if input.user_id:
            try:
                user = User.objects.get(id=input.user_id)
                job.user = user
                job.save()
            except User.DoesNotExist:
                logger.error(f"User {input.user_id} does not exist")
    
    
    if input.transfer_history_id:
        try:
            transfer_history = TransferHistory.objects.get(id=input.transfer_history_id)
            job.transfer_history = transfer_history
            job.save()
        except TransferHistory.DoesNotExist:
            logger.error(f"TransferHistory {input.transfer_history_id} does not exist")
    if input.workflow_history_id:
        try:
            workflow_history = WorkflowHistory.objects.get(id=input.workflow_history_id)
            job.workflow_history = workflow_history
            job.save()
        except WorkflowHistory.DoesNotExist:
            logger.error(f"WorkflowHistory {input.workflow_history_id} does not exist")

    # Store the log
    logger.info(
        f"[Background Job] {input.function} has been started. Job ID: {job_id}")

    job.log = (job.log or []) + [{
        "job_id": job_id,
        "status": "starting",
        "message": f"[Background Job] {input.function} has been started. Job ID: {job_id}",
    }]
    job.save()

    # Commented out because it was causing issues with the background job
    bg_jobs_id = str(job.id)
    bg_job_id_args = f"--background_job_id={bg_jobs_id}"

    try:
        set_bg_job_running(job_id=job_id)
        if 'args' in job.payload:
            # Ensure args are properly formatted strings
            args_ = []
            for val in job.payload["args"]:
                if isinstance(val, str):
                    args_.append(val.replace("'", '"'))
                else:
                    args_.append(str(val).replace("'", '"'))

            # Commented out because it was causing issues with the background job
            if bg_job_id_args:
                args_.append(bg_job_id_args.replace("'", '"'))

            management.call_command(input.function, *args_)
        else:
            management.call_command(input.function)
            
        set_bg_job_completed(job_id=job_id)
        logger.info(f"[Background Job] {input.function} has been completed. Job ID: {job_id}")
    except Exception as e:
        # Notification to Discord with traceback
        error_traceback = traceback.format_exc()

        # Truncate the traceback for logging to prevent Azure size limit issues
        # Azure has a 65KB limit, so we'll limit to 10KB for safety
        max_log_size = 10 * 1024  # 10KB
        truncated_log_traceback = error_traceback[:max_log_size] + \
            '...[TRUNCATED FOR LOGGING]' if len(error_traceback) > max_log_size else error_traceback

        logger.error(f"[Background Job] Job ID: {job_id}, Error: {str(e)[:1000]}...[TRUNCATED] Traceback: {truncated_log_traceback}")

        # Truncate the traceback if it's too long for Discord (separate limit)
        truncated_traceback = error_traceback[:1500] + \
            '...' if len(error_traceback) > 1500 else error_traceback
        error_msg = f"[Container] [ERROR] - Running command `{input.function}` failed.\n"
        error_msg += f"Error: {str(e)}\n"
        error_msg += f"```\n{truncated_traceback}\n```"
        DiscordNotification().send_message(
            error_msg,
            mention_owner=True
        )

        # Track non-database related errors
        if not settings.PROD or any(db_error in str(e).lower() for db_error in ['database', 'migration', 'db']):
            return None

        # Check if similar error occurred in the last hour
        similar_error = Error.objects.filter(
            error_type=type(e).__name__,
            created_at__gte=timezone.now() - timedelta(minutes=settings.SLACK_ERROR_NOFIF_INTERVAL)
        ).exists()

        # Always create an Error object
        Error.objects.create(
            error_type=type(e).__name__,
            error_desc=error_msg,
        )

        # # Only send Slack notification if this is a new type of error from the last hour
        if not similar_error:
            sn = Slack()
            sn.send_notif(error_msg)

        job.log = (job.log or []) + [{
            "job_id": job_id,
            "status": "error",
            "message": f"[Background Job] error_message: {str(e)}",
        }]

        # Add traceback to log (truncated for database storage)
        truncated_error_msg = error_msg[:2000] + '...[TRUNCATED FOR DB]' if len(error_msg) > 2000 else error_msg
        job.log = (job.log or []) + [{
            "job_id": job_id,
            "status": "error",
            "message": f"[Background Job] error_traceback: {truncated_error_msg}",
        }]
        
        job.status = BackgroundJob.BackgroundJobStatus.FAILED
        
        if job.transfer_history:
            job.transfer_history.status = 'failed'
            job.transfer_history.save()
        
        job.save()
        set_bg_job_failed(job_id=job_id)
        
        return

    # Success
    job.log = (job.log or []) + [{
        "job_id": job_id,
        "status": "success",
        "message": f"[Background Job] {input.function} has been completed.",
    }]
    
    job.status = BackgroundJob.BackgroundJobStatus.COMPLETED
    job.save()

def start_bg_worker():
    """Starts the background job worker."""
    logger.info("Starting Hatchet worker...")
    worker = hatchet.worker(
        "sanka-bg-worker",
        workflows=[
            run_background_job,
            export_csv_orders,
            export_csv_items,
            export_csv_contacts,
            export_csv_company,
            import_salesforce_companies_task,
            import_hubspot_companies_task,
            export_salesforce_companies_task,
            export_hubspot_companies_task,
            import_hubspot_contacts_task,
            import_salesforce_contacts_task,
            import_salesforce_contacts_as_companies_task,
            import_shopify_contacts_task,
            import_freee_contacts_task,
            import_line_contacts_task,
            export_hubspot_contacts_task,
            export_hubspot_contacts_as_companies_task,
            export_shopify_contacts_task,
            export_freee_contacts_task,
            import_hubspot_custom_object_task,
            import_salesforce_custom_object_task,
            export_hubspot_custom_object_task,
            export_csv_inventory,
            export_csv_locations,
            export_csv_inventory_transactions,
            export_csv_cases,
            export_cases,
            export_csv_system_log,
            delete_records_task,
            delete_workspace_task,
            export_csv_custom_object,
            export_csv_estimates,
            export_csv_invoices,
            run_send_record_email_action,
            run_workflow_action_task,
            shopify_sync_dag,
            import_orders,
            import_shopify_orders,
            import_hubspot_orders,
            import_hubspot_orders_subprocess,
            import_square_orders,
            import_amazon_orders,
            import_ecforce_orders,
            import_rakuten_orders,
            import_eccube_orders,
            import_makeshop_orders,
            import_yahoo_shopping_orders,
            import_bcart_orders,
            import_woocommerce_orders,
            import_salesforce_opportunities,
            import_ebay_orders,
            import_stripe_orders,
            import_nextengine_orders,
            import_items,
            import_shopify_items,
            import_makeshop_items,
            import_rakuten_items,
            import_eccube_items,
            import_yahoo_shopping_items,
            import_square_items,
            import_amazon_items,
            import_stripe_items,
            import_ecforce_items,
            import_freee_items,
            import_salesforce_items_task,
            import_salesforce_cases_task,
            import_salesforce_invoices_task,
            import_salesforce_subscriptions_task,
            export_hubspot_subscriptions_custom_object_task,
            fix_total_price,
        ],
        slots=100
    )
    logger.info("Background worker started.")
    worker.start()


def main():
    start_bg_worker()

if __name__ == "__main__":
    main()