{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}
{% generate_uuid as my_uuid %}


<div class="border-gray-300 border-bottom mb-2">
    <div class="{% include "data/utility/association.html" %}">
        <div class="accordion-header mx-5 my-3 d-flex collapsed" data-bs-toggle="collapse" data-bs-target="#related_{{my_uuid}}">
            <span class="accordion-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-down accordion-icon-off" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
                </svg>
            </span>
            <div class="fs-4 fw-bold mb-0 ms-4">

                {% if not label.created_by_sanka %}
                    {{label.label}} {% if LANGUAGE_CODE != 'ja' %} ( from {{from|page_group_to_object_singular:request.LANGUAGE_CODE}} ) {% else %} ( {{from|page_group_to_object_singular:request.LANGUAGE_CODE}}から ) {% endif %}
                {% else %}
                    {% with args=label.label|stringify|add:'|'|add:from %} 
                    {% with column_display=args|get_column_display:request %}
                        {{column_display.name}} {% if LANGUAGE_CODE != 'ja' %} ( from {{from|page_group_to_object_singular:request.LANGUAGE_CODE}} ) {% else %} ( {{from|page_group_to_object_singular:request.LANGUAGE_CODE}}から ) {% endif %}
                    {% endwith %}
                    {% endwith %}
                {% endif %}

            </div>
        </div>
    </div>
    <div id="related_{{my_uuid}}" class="collapse fs-6 mx-5 my-3" data-bs-parent="#related_label_{{my_uuid}}">

        {% get_parent_association_members label from obj as association_objects %}

        {% if constant.TYPE_OBJECT_CONTACT in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_CONTACT from obj.id as accordion_type %}
            {% for contact in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                        hx-vals='{"source":"{{source}}","hide_associated_data":true}'
                        hx-trigger="click"
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">  
                        
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{contact.contact_id|stringformat:"04d"}} | {{contact|display_contact_name:LANGUAGE_CODE}} ({% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %})
                            </div>
                        </div>

                    </button>
                </div>
            {% empty %}
            {% endfor %}
        {% endif %}
        
        {% if constant.TYPE_OBJECT_COMPANY in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_COMPANY from obj.id as accordion_type %}
            {% for company in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                        hx-vals='{"source":"{{source}}","hide_associated_data":true}'
                        hx-trigger="click"
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            
                            <div class="mb-1">
                                #{{company.company_id|stringformat:"04d"}} | {{company.name}} ({% if LANGUAGE_CODE == 'ja'%}企業{% else %}Company{% endif %})
                            </div>
                        </div>
                    </button>
               
                </div>
            {% empty %}
            {% endfor %}
        {% endif %}
            
        {% if constant.TYPE_OBJECT_ESTIMATE in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_ESTIMATE from obj.id as accordion_type %}
            {% for estimate in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        hx-get="{% host_url 'estimate_edit' estimate.id host 'app' %}" 
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true"}'
                        hx-indicator=".loading-drawer-spinner"
                        >  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{estimate.id_est|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}見積{% else %}Estimate{% endif %}
                            </div>
                        </div>
                    </button>
                </div>
            {% empty %}
                <div class="text-center text-gray-500">
                    {% if LANGUAGE_CODE == 'ja' %}
                        関連する見積はありません
                    {% else %}
                        No related data for estimates
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_PURCHASE_ORDER in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_PURCHASE_ORDER from obj.id as accordion_type %}
            {% for purchase_order in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        onclick="openRelatedDrawerWithParent()"
                        hx-get="{% host_url 'purchase_manage' purchase_order.id host 'app' %}?view_id={{view_id}}&page={{page}}&module={{menu_key}}&selected_order_id={{order.id}}&source={{constant.TYPE_OBJECT_ORDER}}" 
                        hx-vals='{"hide_associated_data":true, "from_drawer": "order"}'
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-indicator=".loading-drawer-spinner"
                        hx-on::after-request="ensureOrderDrawerStaysOpen()">
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{purchase_order.id_po|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}発注{% else %}Purchase Order{% endif %}
                            </div>
                        </div>
                    </button>
                </div>
            {% empty %}
                <div class="text-center text-gray-500">
                    {% if LANGUAGE_CODE == 'ja' %}
                        関連する発注はありません
                    {% else %}
                        No related data for purchase orders
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_INVENTORY_TRANSACTION in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_INVENTORY_TRANSACTION from obj.id as accordion_type %}
            {% for transaction in association_objects|get_association_object_by_type:accordion_type %}
                {% if transaction.usage_status == 'active' %}
                    <div class="p-1 px-3 w-100 justify-content-between rounded border align-items-center text-start bg-white text-primary d-flex mb-3"
                        id="related-inventory-transaction-{{transaction.id}}">
                        <button class="fs-6 bg-white fw-bold border-0 mb-2 py-4 px-3 text-start text-primary hover:text-primary-500 manage-related-wizard-button"
                            type="button"
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-transaction-manage", "transaction_id":"{{transaction.id}}", "from": "{{from}}", "hide_associated_data": true }'
                            hx-target="#manage-related-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">

                            <div class="mb-1">
                                {% get_object_display transaction 'commerce_inventory_transaction' %}
                            </div>
                            <div>
                                {% if transaction.inventory.inventory_status == 'available'%}
                                    {% if LANGUAGE_CODE == 'ja'%}販売可能{% else %}Available{% endif %}
                                {% elif transaction.inventory.inventory_status == 'committed'%}
                                    {% if LANGUAGE_CODE == 'ja'%}確定済み{% else %}Committed{% endif %}
                                {% elif transaction.inventory.inventory_status == 'unavailable'%}
                                    {% if LANGUAGE_CODE == 'ja'%}販売不可{% else %}Unavailable{% endif %}
                                {% endif %} - 
                                {% if transaction.transaction_type == 'in' %}
                                    {% if LANGUAGE_CODE == 'ja'%}入庫{% else %}Stock In{% endif %}
                                {% elif transaction.transaction_type == 'out'%}
                                    {% if LANGUAGE_CODE == 'ja'%}出庫{% else %}Stock Out{% endif %}
                                {% elif transaction.transaction_type == 'adjust' %}
                                    {% if LANGUAGE_CODE == 'ja'%}調整{% else %}Adjust{% endif %}
                                {% endif %}
                                : {{transaction.amount}}
                            </div>
                        </button>
                    </div>
                {% endif %}
            {% empty %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_INVOICE in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_INVOICE from obj.id as accordion_type %}
            {% for invoice in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        onclick="clickRelation()"
                        hx-get="{% host_url 'invoice_edit' invoice.id host 'app' %}"
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"hide_associated_data": "true"}'
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content"
                        >  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                {% if invoice.contact %}
                                    #{{invoice.id_inv|stringformat:'04d'}} {% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoice{% endif %} | {{invoice.contact|display_contact_name:LANGUAGE_CODE}}
                                {% elif invoice.company %}
                                    #{{invoice.id_inv|stringformat:'04d'}} {% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoice{% endif %} | {{invoice.company.name}}
                                {% else %}
                                    #{{invoice.id_inv|stringformat:'04d'}} {% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoice{% endif %} | (Unnamed)
                                {% endif %}
                            </div>
                        </div>
                    </button>
                    
                </div>
            {% empty %}
            {% endfor %}
        {% endif %}


        {% if constant.TYPE_OBJECT_SUBSCRIPTION in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_SUBSCRIPTION from obj.id as accordion_type %}
            {% for subscription in association_objects|get_association_object_by_type:accordion_type %}
            <div class="d-flex">
                <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                    hx-get="{% host_url 'load_manage_subscriptions_drawer' host 'app' %}" 
                    hx-target="#manage-related-drawer-content"
                    hx-trigger="click"
                    hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true", "drawer_type":"subscriptions-manage", "subscription_id":"{{subscription.id}}"}'
                    hx-indicator=".loading-drawer-spinner">  
                    <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                        <div class="mb-1">
                            #{{subscription.subscriptions_id|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}サブスクリプション{% else %}Subscription{% endif %}
                        </div>
                    </div>
                </button>
            </div>
            {% empty %}
                <div class="text-center text-gray-500">
                    {% if LANGUAGE_CODE == 'ja' %}
                        関連するサブスクリプションはありません
                    {% else %}
                        No related data for subscriptions
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_CASE in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_CASE from obj.id as accordion_type %}
            {% for case in association_objects|get_association_object_by_type:accordion_type %}

                <div class="d-flex w-100">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        onclick="clickRelation()"
                        hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                        hx-vals = '{"drawer_type":"manage-deal", "deal_id":"{{case.id}}" ,"view_id":"{{view_id}}", "page": "{{page}}"}'
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content"
                        >  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{case.deal_id|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}案件{% else %}Case{% endif %}
                            </div>
                            <div class="text-gray-600 fs-7">
                                {{case.name|default:"(Unnamed)"}}
                            </div>
                            {% if case.case_status %}
                            <div class="text-gray-500 fs-8 mt-1">
                                {% if LANGUAGE_CODE == 'ja' %}
                                    ステータス: {{case.case_status}}
                                {% else %}
                                    Status: {{case.case_status}}
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </button>
                </div>
            {% empty %}
                <div class="text-center text-gray-500">
                    {% if LANGUAGE_CODE == 'ja' %}
                        関連する案件はありません
                    {% else %}
                        No related data for cases
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}


        {% if constant.TYPE_OBJECT_DELIVERY_NOTE in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_DELIVERY_NOTE from obj.id as accordion_type %}
            {% for delivery_note in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        hx-get="{% host_url 'delivery_slip_edit' delivery_note.id host 'app' %}" 
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true"}'
                        hx-indicator=".loading-drawer-spinner">  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{delivery_note.id_ds|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}納品{% else %}Delivery Note{% endif %}
                            </div>
                        </div>
                    </button>
                </div>
            {% empty %}
                <div class="text-center text-gray-500">
                    {% if LANGUAGE_CODE == 'ja' %}
                        関連する納品データはありません
                    {% else %}
                        No related data for delivery notes
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_ORDER in label.object_source %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_ORDER from obj.id as accordion_type %}
            {% for order in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}" 
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true", "drawer_type":"manage-orders", "order_id":"{{order.id}}"}'
                        hx-indicator=".loading-drawer-spinner">  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{order.order_id|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}受注{% else %}Order{% endif %}
                            </div>
                        </div>
                    </button>
                </div>
            {% empty %}
                <div class="text-center text-gray-500">
                    {% if LANGUAGE_CODE == 'ja' %}
                        関連するデータはありません
                    {% else %}
                        No related data for orders
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}
        
        {% if label.object_source|is_uuid %}
            {% combine_text_with_pipe label.object_source from obj.id as accordion_type %}
            {% for row in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        hx-get="{% host_url 'custom_object_drawer' label.object_source host 'app' %}"
                        hx-vals = '{"drawer-type":"manage", "row_id":"{{row.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}"}'
                        hx-target="#manage-related-drawer-content"
                        hx-swap="innerHTML"
                        hx-indicator=".loading-drawer-spinner"
                        >
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                {% get_object_display row 'custom_object' %} 
                            </div>
                        </div>
                    </button>
                </div> 
            {% empty %}
                <div class="text-center text-gray-500">
                    {% if LANGUAGE_CODE == 'ja' %}
                        関連データなし
                    {% else %}
                        No related data
                    {% endif %}
                </div>
            {% endfor %}
        {% endif %}

    </div>
</div>


<script>
    function getDrawerURL() {
        return encodeURIComponent(window.location.href.replace(window.location.search, "") + "{{drawer_query_params|safe}}");
    }
</script>