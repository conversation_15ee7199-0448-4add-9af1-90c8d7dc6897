from datetime import datetime
import traceback

from data.constants.constant import COMPANY_COLUMNS_DISPLAY, CONTACTS_COLUMNS_DISPLAY
from utils.date import format_date
from data.models import (
    Contact,
    Company,
    ContactsNameCustomField,
    CompanyNameCustomField,
    ShopTurboOrders,
    DeliverySlip,
    Receipt,
    ShopTurboItems,
    ShopTurboOrdersNameCustomField,
    ContactsValueCustomField,
    CompanyValueCustomField,
    ShopTurboOrdersValueCustomField,
    DeliverySlipItem,
    ReceiptItem,
    Workspace,
    CompanyList,
    UserManagement,
    ContactList,
)
from utils.utility import is_valid_uuid


def display_contact_name(contact: Contact, lang="ja"):
    try:
        full_name = contact.name
        if contact.last_name:
            if lang == "ja":
                full_name = contact.last_name + " " + full_name
            else:
                full_name = full_name + " " + contact.last_name
        return full_name
    except:
        return ""


def display_contact_field(contact: Contact, lang="ja"):
    return f"#{'%04d' % contact.contact_id} | {display_contact_name(contact, lang)}"


def display_company_field(company: Company, lang="ja"):
    return f"#{'%04d' % company.company_id} | {company.name}"


def get_contact_custom_property(workspace, id):
    contactsCustomFieldName = None
    if is_valid_uuid(id):
        contactsCustomFieldName = ContactsNameCustomField.objects.filter(
            id=id, workspace=workspace
        ).first()
    return contactsCustomFieldName


def get_order_customer_custom_property(parts, workspace, lang):
    try:
        # customer|contact|"field"
        if len(parts) == 3 and parts[0] == "customer" and parts[1] == "contact":
            if is_valid_uuid(parts[2]):
                ctf = get_contact_custom_property(workspace, parts[2])
                name_display = ctf.name if ctf else None
            else:
                if parts[2] in CONTACTS_COLUMNS_DISPLAY:
                    name_display = CONTACTS_COLUMNS_DISPLAY[parts[2]][lang]
                else:
                    name_display = parts[2]

            return {
                "name": f"{'顧客 (連絡先)' if lang == 'ja' else 'Customer(Contact)'} - {name_display}",
                "id": "|".join(parts),
            }

        # customer|company|"field"
        elif len(parts) == 3 and parts[0] == "customer" and parts[1] == "company":
            if is_valid_uuid(parts[2]):
                ctf = None
                if is_valid_uuid(parts[2]):
                    ctf = CompanyNameCustomField.objects.filter(
                        id=parts[2], workspace=workspace
                    ).first()
                name_display = ctf.name if ctf else None
            else:
                name_display = parts[2]
                if "| child" in parts[2]:
                    if lang == "ja":
                        name_display = parts[2].replace("child", "子供")
                elif parts[2] in COMPANY_COLUMNS_DISPLAY:
                    name_display = COMPANY_COLUMNS_DISPLAY[parts[2]][lang]

            return {
                "name": f"{'顧客（企業）' if lang == 'ja' else 'Customer(Company)'} - {name_display}",
                "id": "|".join(parts),
            }

        # contact|"custom_field_id"|"field"
        elif len(parts) > 2 and parts[0] == "contact":
            customfield_name = ShopTurboOrdersNameCustomField.objects.filter(
                id=parts[1]
            ).first()
            if customfield_name:
                if is_valid_uuid(parts[2]):
                    ctf = get_contact_custom_property(workspace, parts[2])
                    name_display = ctf.name if ctf else None
                else:
                    name_display = parts[2]
                    if parts[2] in CONTACTS_COLUMNS_DISPLAY:
                        name_display = CONTACTS_COLUMNS_DISPLAY[parts[2]][lang]

                return {
                    "name": f"{customfield_name.name} - {name_display}",
                    "id": "|".join(parts),
                }

        # company|"custom_field_id"|"field"
        elif len(parts) > 2 and parts[0] == "company":
            customfield_name = ShopTurboOrdersNameCustomField.objects.filter(
                id=parts[1]
            ).first()
            if customfield_name:
                if is_valid_uuid(parts[2]):
                    ctf = None
                    if is_valid_uuid(parts[2]):
                        ctf = CompanyNameCustomField.objects.filter(
                            id=parts[2], workspace=workspace
                        ).first()
                    name_display = ctf.name if ctf else None
                else:
                    name_display = parts[2]
                    if "| child" in parts[2]:
                        if lang == "ja":
                            name_display = parts[2].replace("child", "子供")
                    elif parts[2] in COMPANY_COLUMNS_DISPLAY:
                        name_display = COMPANY_COLUMNS_DISPLAY[parts[2]][lang]

                return {
                    "name": f"{customfield_name.name} - {name_display}",
                    "id": "|".join(parts),
                }

    except:
        traceback.print_exc()
        pass
    return None


def _get_contact_value(contact, field):
    if not isinstance(contact, Contact):
        return None

    if is_valid_uuid(field):
        contactCustomFieldValue = ContactsValueCustomField.objects.filter(
            field_name__id=field, contact__id=contact.id
        ).last()
        if contactCustomFieldValue:
            if contactCustomFieldValue.field_name.type == "image":
                if contactCustomFieldValue.file:
                    return contactCustomFieldValue.file.url
            else:
                return contactCustomFieldValue.value
        return ""

    elif field == "name":
        return display_contact_name(contact, "ja")

    elif field == "first_name":
        return contact.name

    elif field == "image_url":
        if contact.image_url:
            return contact.image_url
        elif contact.image_file:
            return contact.image_file.url
        else:
            return None

    return getattr(contact, field)


def _get_company_value(company, field):
    if not isinstance(company, Company):
        return None

    if is_valid_uuid(field):
        companyCustomFieldValue = CompanyValueCustomField.objects.filter(
            field_name__id=field, company__id=company.id
        ).last()
        if companyCustomFieldValue:
            if companyCustomFieldValue.field_name.type == "image":
                if companyCustomFieldValue.file:
                    return companyCustomFieldValue.file.url
            else:
                return companyCustomFieldValue.value
        return ""

    elif field == "image_file":
        if company.image_file:
            return company.image_file.url
        else:
            return None

    return getattr(company, field)


def _get_contact_value(contact, field):
    if not isinstance(contact, Contact):
        return None

    if is_valid_uuid(field):
        contactCustomFieldValue = ContactsValueCustomField.objects.filter(
            field_name__id=field, contact__id=contact.id
        ).last()
        if contactCustomFieldValue:
            if contactCustomFieldValue.field_name.type == "image":
                if contactCustomFieldValue.file:
                    return contactCustomFieldValue.file.url
            else:
                return contactCustomFieldValue.value
        return ""

    elif field == "name":
        return display_contact_name(contact, "ja")

    elif field == "first_name":
        return contact.name

    elif field == "image_url":
        if contact.image_url:
            return contact.image_url
        elif contact.image_file:
            return contact.image_file.url
        else:
            return None

    return getattr(contact, field)


def _get_company_value(company, field):
    if not isinstance(company, Company):
        return None

    if is_valid_uuid(field):
        companyCustomFieldValue = CompanyValueCustomField.objects.filter(
            field_name__id=field, company__id=company.id
        ).last()
        if companyCustomFieldValue:
            if companyCustomFieldValue.field_name.type == "image":
                if companyCustomFieldValue.file:
                    return companyCustomFieldValue.file.url
            else:
                return companyCustomFieldValue.value
        return ""

    elif field == "image_file":
        if company.image_file:
            return company.image_file.url
        else:
            return None

    return getattr(company, field)


def get_value_customer_field(column_name, obj):
    parts = column_name.split("|")
    if len(parts) != 3:
        return None

    main_field = parts[0]
    sub_field = parts[1]
    extra_field = parts[2]

    try:
        if main_field == "customer":
            if sub_field == "contact":
                return _get_contact_value(obj, extra_field)

            elif sub_field == "company":
                return _get_company_value(obj, extra_field)

        elif main_field in ["contact", "company"]:
            if not isinstance(obj, ShopTurboOrders):
                return None

            OrdersCustomFieldValue = ShopTurboOrdersValueCustomField.objects.filter(
                field_name__id=sub_field, orders__id=obj.id
            ).last()
            if OrdersCustomFieldValue:
                if OrdersCustomFieldValue.field_name.type == "image":
                    value_custom_field = (
                        OrdersCustomFieldValue.shopturboordersvaluefile_set.last()
                    )

                elif OrdersCustomFieldValue.field_name.type == "image_group":
                    value_custom_field = (
                        OrdersCustomFieldValue.shopturboordersvaluefile_set.all()
                    )

                else:
                    value_custom_field = OrdersCustomFieldValue.value
            else:
                value_custom_field = ""

            if main_field == "contact":
                contact = None
                if value_custom_field:
                    if is_valid_uuid(value_custom_field):
                        try:
                            contact = Contact.objects.get(id=value_custom_field)
                        except:
                            pass
                return _get_contact_value(contact, extra_field)

            elif main_field == "company":
                company = None
                if value_custom_field:
                    if is_valid_uuid(value_custom_field):
                        try:
                            company = Company.objects.get(id=value_custom_field)
                            return company
                        except:
                            pass
                return _get_company_value(company, extra_field)

    except Exception as e:
        print("!!!!!!!!!!", e)

    return None


def get_special_field_value(lang, obj, field):
    if field == "customers":
        if isinstance(obj, (DeliverySlip, Receipt)):
            if obj.contact:
                from utils.contact import display_contact_name

                return f"#{'%04d' % obj.contact.contact_id} - {display_contact_name(obj.contact, lang)}"
            elif obj.company:
                return f"#{'%04d' % obj.company.company_id} - {obj.company.name}"
            elif obj.customers:
                return obj.customers

    elif field == "item":
        if isinstance(obj, (DeliverySlip, Receipt)):
            if isinstance(obj, DeliverySlip):
                items = DeliverySlipItem.objects.filter(deliveryslip=obj).order_by(
                    "created_at"
                )
            elif isinstance(obj, Receipt):
                items = ReceiptItem.objects.filter(receipt=obj).order_by("created_at")

            items_list = []
            for item in items:
                if item.item_link:
                    items_list.append(
                        f"#{'%04d' % item.item_link.item_id}| {item.item_link.name}"
                    )
                else:
                    items_list.append(item.item_name)
            return "\n".join(items_list) if len(items_list) > 0 else None

    elif " | child" in field:
        if isinstance(obj, (ShopTurboItems, Company)):
            _parts = field.split(" | ")
            if len(_parts) == 0 or not is_valid_uuid(_parts[0]):
                return None

            if isinstance(obj, Company):
                custom_field = CompanyNameCustomField.objects.filter(
                    id=_parts[0]
                ).first()
                value_custom_field = CompanyValueCustomField.objects.filter(
                    field_name=custom_field, company__id=str(obj.get("id"))
                ).last()
                if value_custom_field:
                    children = (
                        value_custom_field.company.property_child_item.all().order_by(
                            "-created_at"
                        )
                    )
                    return "\n".join(
                        [
                            f"#{'%04d' % c.company.company_id} - {c.company.name}"
                            for c in children
                        ]
                    )

    return None


def get_company_downloadable_value(
    company, column, custom_field_ids=[], timezone="UTC", lang="ja"
):
    workspace_id = company["workspace_id"]
    workspace = Workspace.objects.get(id=workspace_id)
    if not custom_field_ids:
        custom_field_ids = CompanyNameCustomField.objects.filter(
            workspace=workspace
        ).values_list("id", flat=True)
        custom_field_ids = [str(v) for v in custom_field_ids]
    data = None
    if column in custom_field_ids:
        data = None
        value_custom_field = CompanyValueCustomField.objects.filter(
            field_name__id=column, company__id=str(company["id"])
        ).last()
        if value_custom_field:
            if value_custom_field.field_name.type == "image":
                if value_custom_field.file:
                    data = value_custom_field.file.url
            elif value_custom_field.field_name.type in ["date", "date_time"]:
                if value_custom_field.value_time:
                    try:
                        user_tz = timezone
                        if not user_tz:
                            user_tz = None
                    except:
                        user_tz = None
                    data = format_date(
                        value_custom_field.value_time,
                        lang,
                        include_time=(
                            value_custom_field.field_name.type == "date_time"
                        ),
                        tz=user_tz,
                    )
                else:
                    data = None
            else:
                data = value_custom_field.value

    elif column == "company_id":
        data = "%04d" % company[column]

    elif column == "image_file":
        if company[column]:
            data = (
                "https://nyc3.digitaloceanspaces.com/sankafile/static/"
                + company[column]
            )
        else:
            data = ""

    elif column == "lists":
        data = []
        contact_lists = CompanyList.objects.filter(companies__id=str(company["id"]))
        if contact_lists:
            for contact_list in contact_lists:
                data.append(contact_list.name)

    elif " | child" in column:
        data = get_special_field_value(lang, company, column)

    elif column in company:
        data = company[column]
        if isinstance(company[column], datetime):
            data = format_date(company[column], lang, include_time=True, tz=timezone)

    elif column == "owner":
        if company.get("owner", ""):
            user_management = UserManagement.objects.filter(
                id=str(company.get("owner", ""))
            ).first()
            data = user_management.user.first_name if user_management else ""

    return data


def get_contact_downloadable_value(
    contact, column, custom_field_ids=[], timezone="UTC", lang="ja"
):
    workspace_id = contact["workspace_id"]
    workspace = Workspace.objects.get(id=workspace_id)
    if not custom_field_ids:
        custom_field_ids = ContactsNameCustomField.objects.filter(
            workspace=workspace
        ).values_list("id", flat=True)
        custom_field_ids = [str(v) for v in custom_field_ids]
    data = None
    if column in custom_field_ids:
        value_custom_field = ContactsValueCustomField.objects.filter(
            field_name__id=column, contact__id=str(contact["id"])
        ).last()
        if value_custom_field:
            if value_custom_field.field_name.type == "image":
                if value_custom_field.file:
                    data = value_custom_field.file.url
            elif value_custom_field.field_name.type in ["date", "date_time"]:
                if value_custom_field.value_time:
                    try:
                        user_tz = timezone
                        if not user_tz:
                            user_tz = None
                    except:
                        user_tz = None
                    data = format_date(
                        value_custom_field.value_time,
                        lang,
                        include_time=(
                            value_custom_field.field_name.type == "date_time"
                        ),
                        tz=user_tz,
                    )
                else:
                    data = None
            else:
                data = value_custom_field.value

    elif column == "lists":
        data = []
        contact_lists = ContactList.objects.filter(contacts__id=str(contact["id"]))
        if contact_lists:
            for contact_list in contact_lists:
                data.append(contact_list.name)

    elif column == "first_name":
        data = contact.get("name")

    elif column == "owner":
        if contact.get("owner", ""):
            user_management = UserManagement.objects.filter(
                id=str(contact.get("owner", ""))
            ).first()
            data = user_management.user.first_name if user_management else ""

    elif column in contact:
        data = contact[column]
        if column == "contact_id":
            data = "%04d" % data

        elif column == "name":
            name = contact.get("name")
            last_name = contact.get("last_name")

            if lang == "ja":
                data = f"{last_name}"
                if name:
                    data += f" {name}"
                data = f"{last_name} {name}"
            else:
                data = f"{name}"
                if last_name:
                    data += f" {last_name}"

        elif column == "image_url":
            if contact.get("image_file"):
                data = contact.get("image_file").get("url")

        elif isinstance(contact[column], datetime):
            data = format_date(contact[column], lang, include_time=True, tz=timezone)

        elif column == "companies":
            companies = []
            if isinstance(data, list):
                for company in data:
                    get_company = Company.objects.filter(id=str(company))
                    if get_company:
                        company_name = get_company.first().name
                        companies.append(company_name)
            data = companies
    return data
