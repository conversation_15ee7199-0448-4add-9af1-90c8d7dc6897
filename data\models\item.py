import ast
import uuid

from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone

from data.constants.constant import SHOPTURBO_NUMBER_FORMAT
from data.models import ObjectManager
from data.models.base import save_tags_values, validate_value_custom_field
from data.models.constant import PLATFORM, POST_MEDIA_TYPE
from data.models.order import SHOPTURBO_ITEMS_CF
from utils.bgjobs.runner import trigger_bg_job

SHOPTURBO_ITEM_STATUS = [
    ("active", "Active"),
    ("archived", "Archived"),
]


class ShopTurboItems(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    name = models.CharField(max_length=200, null=True, blank=True)
    item_id = models.IntegerField(null=True, blank=True)
    product_id = models.CharField(max_length=200, null=True, blank=True)
    platform = models.CharField(choices=PLATFORM, max_length=100, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    price = models.FloatField(null=True, blank=True)
    purchase_price = models.FloatField(null=True, blank=True)
    tax = models.FloatField(null=True, blank=True)
    file = models.FileField(verbose_name="ShopTurbo File", upload_to="shopturbo-files")
    status = models.CharField(
        choices=SHOPTURBO_ITEM_STATUS,
        max_length=30,
        null=True,
        blank=True,
        default="active",
    )
    variation_switcher = models.BooleanField(default=False)
    parent_switcher = models.BooleanField(default=False)
    property_set = models.ForeignKey(
        "PropertySet", on_delete=models.SET_NULL, null=True, blank=True
    )

    company = models.ForeignKey(
        "Company",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="item_objects",
    )
    contact = models.ForeignKey(
        "Contact",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="item_objects",
    )

    # Inventory tracking fields
    available_inventory_amount = models.FloatField(null=True, blank=True, default=0)
    committed_inventory_amount = models.FloatField(null=True, blank=True, default=0)
    unavailable_inventory_amount = models.FloatField(null=True, blank=True, default=0)
    total_inventory = models.FloatField(null=True, blank=True, default=0)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["workspace", "item_id"],
                condition=models.Q(item_id__isnull=False),
                name="unique_item_id_per_workspace",
            )
        ]

    def save(self, *args, **kwargs):
        from django.db import transaction, connection, IntegrityError
        import time
        import random

        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if not self.item_id:
            # Retry logic to handle race conditions
            max_retries = 5
            for attempt in range(max_retries):
                try:
                    # Use atomic transaction with table locking to prevent race conditions
                    with transaction.atomic():
                        # Lock the table to prevent concurrent access during ID generation
                        with connection.cursor() as cursor:
                            cursor.execute(
                                "LOCK TABLE data_shopturboitems IN EXCLUSIVE MODE"
                            )

                        # Generate a new item_id if it doesn't exist
                        last_item = (
                            ShopTurboItems.objects.filter(
                                workspace=self.workspace, item_id__isnull=False
                            )
                            .order_by("-item_id")
                            .first()
                        )
                        if last_item and last_item.item_id:
                            try:
                                last_item_id = int(last_item.item_id)
                            except (ValueError, TypeError):
                                last_item_id = 0
                            next_item_id = last_item_id + 1
                        else:
                            # If it's the first item, start with 1
                            next_item_id = 1

                        # Check if this item_id already exists (double-check)
                        while ShopTurboItems.objects.filter(
                            workspace=self.workspace, item_id=next_item_id
                        ).exists():
                            next_item_id += 1

                        self.item_id = next_item_id
                        break  # Success, exit retry loop

                except IntegrityError as e:
                    if attempt == max_retries - 1:
                        # Last attempt failed, raise the error
                        raise e
                    # Wait a random amount of time before retrying
                    time.sleep(random.uniform(0.1, 0.5))
                    continue

        try:
            if self.id:
                param = {
                    "function": "trigger_workflow_when_record_updated",
                    "job_id": str(uuid.uuid4()),
                    "workspace_id": str(self.workspace.id),
                    "user_id": None,
                    "args": [
                        f"--obj_id={self.id}",
                        f"--obj_type={self.__class__.__name__}",
                    ],
                }
                trigger_bg_job(param)
        except:
            pass

        try:  # Property Sync item
            property_sync_fields = ShopTurboItemsNameCustomField.objects.filter(
                workspace=self.workspace,
                type="property_sync",
                source_property__isnull=False,
            )

            if property_sync_fields:
                for property_sync_field in property_sync_fields:
                    source_property = property_sync_field.source_property
                    pair_property = property_sync_field.pair_property
                    sync_conditions = property_sync_field.sync_conditions
                    source_property_value = getattr(self, source_property)

                    if pair_property:
                        pair = ShopTurboItemsNameCustomField.objects.get(
                            id=pair_property
                        )
                    else:
                        pair = property_sync_field
                    if sync_conditions:
                        sync_conditions = ast.literal_eval(sync_conditions)
                        custom_field_value, _ = (
                            ShopTurboItemsValueCustomField.objects.get_or_create(
                                field_name=pair, items=self
                            )
                        )
                        for sync_condition in sync_conditions:
                            if sync_condition[0] == "is":
                                if sync_condition[1] == source_property_value:
                                    custom_field_value.value = sync_condition[2]
                                    custom_field_value.save()
                                    break
                            elif sync_condition[0] == "contains":
                                if sync_condition[1] in source_property_value:
                                    custom_field_value.value = sync_condition[2]
                                    custom_field_value.save()
                                    break
        except:
            pass

        super().save(*args, **kwargs)

    def delete(
        self, *args, **kwargs
    ):  # Delete Indifidual. Such as objects,get().delete()
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data
        super().delete(*args, **kwargs)

    def get_display_name(self):
        platforms = self.item.filter(platform_type="default")
        data = {}
        for platform in platforms:
            data[str(platform.channel.id)] = platform.platform_id
        return data

    def get_default_price(self):
        default_price = 0
        default_price_obj = self.shopturbo_item_price.filter(default=True).first()
        if default_price_obj:
            default_price = default_price_obj.price

        return default_price

    def get_platform_sku(self):
        platforms = self.item.filter(platform_type="default")
        data = {}
        for platform in platforms:
            data[str(platform.channel.id)] = platform.platform_sku
        return data

    def get_platform_variant(self):
        platforms = self.item.filter(platform_type="default")
        data = {}
        for platform in platforms:
            data[str(platform.channel.id)] = platform.variant_id
        return data


SHOPTURBO_ITEM_PLATFORM_TYPES = [
    ("default", "Default"),
    ("order", "Order"),
    ("subscription", "Subscription"),
    ("external", "External"),
    ("invoice", "Invoice"),
]

ITEM_INVENTORY_COMPONENT_PLATFORM_TYPES = [
    ("parent", "Parent"),
    ("child", "Child"),
]


class ShopTurboItemsPlatforms(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    old_id = models.BigIntegerField(blank=True, null=True)
    item = models.ForeignKey(
        ShopTurboItems,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="item",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    parent_platform_id = models.CharField(max_length=200, null=True, blank=True)
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    platform_sku = models.TextField(null=True, blank=True)
    variant_id = models.CharField(max_length=200, null=True, blank=True)
    platform_type = models.CharField(
        choices=SHOPTURBO_ITEM_PLATFORM_TYPES, max_length=100, default="default"
    )
    component_type = models.CharField(
        choices=ITEM_INVENTORY_COMPONENT_PLATFORM_TYPES,
        max_length=100,
        default="default",
    )
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    related_ids = models.JSONField(null=True, blank=True)

    def get_platform_variant(self):
        from utils.shopify_bg_job.shopify_orders import sync_shopify_item

        if self.channel.integration.slug == "shopify":
            api_key = self.channel.api_key
            access_token = self.channel.access_token
            shop_name = self.channel.account_id
            return sync_shopify_item(api_key, access_token, shop_name, self.platform_id)


class ShopTurboItemsMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    platform = models.CharField(choices=PLATFORM, max_length=100, null=True, blank=True)
    mapping_type = models.CharField(max_length=200, null=True, blank=True)
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class ShopTurboOrderLevelTax(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE, null=True)
    tax = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


DISCOUNTS_TYPES = [
    ("registered_discounts", "Registered Discounts"),
    ("free_writing_discounts", "Free Writing Discounts"),
]


class ShopTurboItemsDiscount(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE, null=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    value = models.FloatField(null=True, blank=True)
    number_format = models.CharField(max_length=200, null=True, blank=True)
    discount_type = models.CharField(
        choices=DISCOUNTS_TYPES, max_length=100, default="registered_discounts"
    )
    created_at = models.DateTimeField(default=timezone.now)


class ShopTurboItemsPrice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE, null=True)
    item = models.ForeignKey(
        ShopTurboItems,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_item_price",
    )
    price = models.FloatField(null=True, blank=True)
    default = models.BooleanField(default=False)
    currency = models.CharField(max_length=200, null=True, blank=True)
    tax = models.FloatField(default=0)
    archive = models.BooleanField(default=False)
    volume_price = models.BooleanField(default=False)

    contact = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.SET_NULL, null=True, blank=True
    )

    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if self.item:
            self.workspace = self.item.workspace

            # Update the related ShopTurboItems's tax and price if this is the default price
            if self.default:
                update_fields = {}
                if self.price is not None:
                    update_fields["price"] = self.price
                if self.tax is not None:
                    update_fields["tax"] = self.tax

                if update_fields:
                    # Avoid circular save by using update() directly
                    ShopTurboItems.objects.filter(id=self.item.id).update(
                        **update_fields
                    )

        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        # If this is the default price being deleted, find another price to set as default
        if self.default and self.item:
            # Find another price for this item
            other_price = (
                ShopTurboItemsPrice.objects.filter(item=self.item)
                .exclude(id=self.id)
                .first()
            )

            if other_price:
                # Set the other price as default
                other_price.default = True
                other_price.save()
            else:
                # No other price exists, reset the item's price and tax to None
                ShopTurboItems.objects.filter(id=self.item.id).update(
                    price=None, tax=None
                )

        super().delete(*args, **kwargs)


class ShopTurboItemsVolumePrice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    item_price = models.ForeignKey(
        ShopTurboItemsPrice,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_item_volume_price",
    )

    minimum = models.FloatField(null=True, blank=True)  # can be none
    maximum = models.FloatField(null=True, blank=True)  # can be none
    price = models.FloatField(null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        super().save(*args, **kwargs)


class ItemPurchasePrice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE, null=True)
    item = models.ForeignKey(
        ShopTurboItems,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="purchase_prices",
    )
    price = models.FloatField(null=True, blank=True)
    default = models.BooleanField(default=False)
    currency = models.CharField(max_length=200, null=True, blank=True)
    tax = models.FloatField(default=0)
    archive = models.BooleanField(default=False)

    contact = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.SET_NULL, null=True, blank=True
    )

    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if self.item:
            self.workspace = self.item.workspace

        super().save(*args, **kwargs)


class ShopTurboItemsVariations(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    items = models.ForeignKey(ShopTurboItems, on_delete=models.CASCADE)
    name = models.CharField(max_length=200, null=True, blank=True)
    sku = models.CharField(max_length=500, null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    price = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


SHIPPING_FEE_TYPES = [
    ("registered_shipping_fees", "Registered Shipping Fees"),
    ("manual_shipping_fees", "Manual Shipping Fees"),
]


class ShopTurboShippingCost(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE, null=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    value = models.FloatField(null=True, blank=True)
    number_format = models.CharField(max_length=200, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    shipping_fee_type = models.CharField(
        choices=SHIPPING_FEE_TYPES, max_length=100, default="registered_shipping_fees"
    )
    tax = models.FloatField(null=True, blank=True)


class ShopTurboShippingCostPlatforms(models.Model):
    shipping_cost = models.ForeignKey(
        ShopTurboShippingCost,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shipping_cost",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    platform_type = models.CharField(
        choices=SHOPTURBO_ITEM_PLATFORM_TYPES, max_length=100, default="default"
    )
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class ShopTurboCustomerItemsPrice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    target = models.CharField(max_length=500, null=True, blank=True)
    contact = models.ForeignKey(
        "Contact", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, null=True, blank=True
    )
    item = models.ForeignKey(
        ShopTurboItems,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_customer_item_price",
    )
    price = models.FloatField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    tax = models.FloatField(default=0)
    discount_rate = models.FloatField(default=0)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        # discount rate
        default_item_price = self.item.shopturbo_item_price.filter(default=True).first()
        if default_item_price:
            if not default_item_price.price:
                default_item_price.price = 0
            if not self.price:
                self.price = 0

            if isinstance(self.price, str):
                self.price = float(self.price)
            if isinstance(default_item_price.price, str):
                default_item_price.price = float(default_item_price.price)

            if default_item_price.price and self.price:
                self.discount_rate = (self.price / default_item_price.price) * 100
                print(
                    default_item_price.price, " ", self.price, " ", self.discount_rate
                )

        super().save(*args, **kwargs)


class ShopTurboItemsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=SHOPTURBO_ITEMS_CF, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    source_property = models.TextField(null=True, blank=True)
    pair_property = models.TextField(null=True, blank=True)
    sync_conditions = models.TextField(null=True, blank=True)

    # Only for Items-Platform Sync
    is_shopify_sync = models.BooleanField(default=False)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class ShopTurboItemsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ShopTurboItemsNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    items = models.ForeignKey(
        ShopTurboItems,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_item_custom_field_relations",
    )
    value = models.TextField(null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="shopturbo-items-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if self.field_name:
            if self.field_name.type in ["number", "formula", "date", "date_time"]:
                validate_value_custom_field(self, args, kwargs)
            elif self.field_name and (self.field_name.type == "tag"):
                save_tags_values(self, args, kwargs)
            else:
                super().save(*args, **kwargs)


        try:
            if (
                self.field_name.type == "property_sync"
                and not self.field_name.source_property
            ):
                property_sync_field = self.field_name
            else:
                property_sync_field = None

            if property_sync_field:
                source_property = property_sync_field.source_property
                pair_property = property_sync_field.pair_property
                sync_conditions = property_sync_field.sync_conditions
                if not source_property:
                    source_property_value = self.value
                else:
                    source_property_value = getattr(self.items, source_property)

                if pair_property:
                    pair = ShopTurboItemsNameCustomField.objects.get(id=pair_property)
                else:
                    pair = property_sync_field
                if sync_conditions:
                    sync_conditions = ast.literal_eval(sync_conditions)
                    custom_field_value, _ = (
                        ShopTurboItemsValueCustomField.objects.get_or_create(
                            field_name=pair, items=self.items
                        )
                    )
                    for sync_condition in sync_conditions:
                        if sync_condition[0] == "is":
                            if sync_condition[1] == source_property_value:
                                custom_field_value.value = sync_condition[2]
                                custom_field_value.save()
                                break
                        elif sync_condition[0] == "contains":
                            if sync_condition[1] in source_property_value:
                                custom_field_value.value = sync_condition[2]
                                custom_field_value.save()
                                break
        except:
            pass


class ShopTurboItemComponents(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    property = models.ForeignKey(
        ShopTurboItemsValueCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="components",
    )
    item = models.ForeignKey(
        ShopTurboItems, on_delete=models.CASCADE, null=True, blank=True
    )
    item_component = models.ForeignKey(
        ShopTurboItems,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="item_component",
    )
    quantity = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class ShopTurboItemsValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Shopturbo Items Custom Field file",
        upload_to="shopturbo-item-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        ShopTurboItemsValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)
