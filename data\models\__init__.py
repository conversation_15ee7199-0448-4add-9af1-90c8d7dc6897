from .apimodel import *
from .backgroundjob import *
from .base import *
from .calendar import *
from .campaign import *
from .community import *
from .content import *
from .contract import *
from .customer import *
from .customobject import *
from .deals import *
from .event import *
from .form import *
from .hubspot import *
from .integration import *
from .inventory import *
from .invitation import *
from .item import *
from .job import *
from .journal import *
from .localization import *
from .location import *
from .log import *
from .mapping import *
from .message import *
from .onboarding import *
from .order import *
from .pdf_template import *
from .reportpanel import *
from .san_ai import *
from .demo_bot import *
from .expensebill import *
from .static import *
from .store import *
from .subscription import *
from .task import *
from .user import *
from .view import *
from .workflowaction import *
from .workspace import *
from .workspace_user import *
from .commerce_meter import *
from .dataset import *

OBJECT_CLASS_TO_OBJECT_TYPE = {
    Deals: TYPE_OBJECT_CASE,
    Contact: TYPE_OBJECT_CONTACT,
    Company: TYPE_OBJECT_COMPANY,
    ShopTurboOrders: TYPE_OBJECT_ORDER,
    ShopTurboSubscriptions: TYPE_OBJECT_SUBSCRIPTION,
    InventoryTransaction: TYPE_OBJECT_INVENTORY_TRANSACTION,
    PurchaseOrders: TYPE_OBJECT_PURCHASE_ORDER,
    ShopTurboItems: TYPE_OBJECT_ITEM,
    ShopTurboInventory: TYPE_OBJECT_INVENTORY,
    InventoryWarehouse: TYPE_OBJECT_INVENTORY_WAREHOUSE,
    Bill: TYPE_OBJECT_BILL,
    Expense: TYPE_OBJECT_EXPENSE,
    Job: TYPE_OBJECT_JOBS,
    CustomObjectPropertyRow: TYPE_OBJECT_CUSTOM_OBJECT,
}
