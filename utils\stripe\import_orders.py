import traceback
from datetime import datetime

import stripe
from django.utils import timezone

from data.constants.constant import CONTACT_USAGE_CATEGORY
from data.models import (
    Company,
    Contact,
    ShopTurboOrdersPlatforms,
    ShopTurboOrdersValueCustomField,
)
from utils.meter import has_quota
from utils.stripe.stripe import retreive_stripe_checkout_sessions
from utils.utility import is_valid_uuid
from utils.logger import logger


def import_stripe_orders(channel, days_ago_filter=1):
    """
    Pull stripe checkout sessions to update existing orders status (is paid or not)
    """
    logger.debug("Pulling stripe sessions")
    start_unix_time = None
    start_unix_time = int(timezone.now().timestamp()) - (days_ago_filter * 24 * 60 * 60)

    # Get transactions from Stripe
    transactions = retreive_stripe_checkout_sessions(
        channel, start_time=start_unix_time
    )
    logger.debug("Total raw transactions", len(transactions))

    # Get all shopturbo orders that have shopturbo orders platforms with stripe channel
    order_ids = ShopTurboOrdersPlatforms.objects.filter(
        channel=channel,
        order__status="active",
        order_at__gte=datetime.fromtimestamp(start_unix_time, tz=timezone.utc),
    ).values_list("order_id", flat=True)
    order_ids = [str(order_id) for order_id in order_ids]

    filtered_transactions = []
    for transaction in transactions:
        if (
            hasattr(transaction, "metadata")
            and hasattr(transaction.metadata, "order_id")
            and transaction.metadata.order_id in order_ids
        ):
            filtered_transactions.append(transaction)
    logger.debug("Total filtered transactions", len(filtered_transactions))
    total_success = 0

    output_data = {
        "total": 0,
        "success": 0,
        "failed": 0,
    }
    stripe.api_key = channel.api_secret

    for stripe_transaction in filtered_transactions:
        try:
            # Check if order already exists for this transaction
            order_platform = ShopTurboOrdersPlatforms.objects.filter(
                platform_payment_id=stripe_transaction.id,
                channel=channel,
                order__status="active",
            ).first()
            if not order_platform:
                continue

            output_data["total"] += 1

            order_platform.payment_status = stripe_transaction.status
            order_platform.order_at = datetime.fromtimestamp(
                stripe_transaction.created, tz=timezone.utc
            )
            if (
                order_platform.payment_status in ["complete", "paid"]
                and stripe_transaction.payment_intent
            ):
                try:
                    payment_intent = stripe.PaymentIntent.retrieve(
                        stripe_transaction.payment_intent,
                        expand=["charges.data.balance_transaction"],
                    )

                    # Get the payment time from the first successful charge
                    if payment_intent.latest_charge:
                        charge = stripe.Charge.retrieve(payment_intent.latest_charge)
                        if charge.paid and charge.status == "succeeded":
                            payment_time = datetime.fromtimestamp(
                                charge.created, tz=timezone.utc
                            )
                            # Now you can use payment_time as needed
                            order_platform.paid_at = payment_time
                except Exception as e:
                    logger.error(
                        f"Error retrieving paid_at frompayment intent {stripe_transaction.payment_intent}: {str(e)}"
                    )
            order_platform.save()

            order = order_platform.order
            order.delivery_status = "payment_received"
            order.save()

            company = None
            contact = None
            stripe_customer = {}
            # Get or create customer
            if stripe_transaction.customer:
                try:
                    stripe_customer = stripe.Customer.retrieve(
                        stripe_transaction.customer
                    )
                except Exception:
                    pass

            if stripe_customer and "deleted" not in stripe_customer:
                # Try to find existing company/contact by email
                company = Company.objects.filter(
                    workspace=channel.workspace, email=stripe_customer.email
                ).first()
                contact = Contact.objects.filter(
                    workspace=channel.workspace, email=stripe_customer.email
                ).first()

                if not company and not contact:
                    # Create new contact if neither exists
                    if has_quota(channel.workspace, CONTACT_USAGE_CATEGORY):
                        contact = Contact.objects.create(
                            workspace=channel.workspace,
                            email=stripe_customer.email,
                            name=stripe_customer.name or stripe_customer.email,
                        )

                if company:
                    order.companies.add(company)
                elif contact:
                    order.contacts.add(contact)

            total_success += 1

        except Exception as e:
            traceback.print_exc()
            output_data["failed"] += 1
            logger.error(
                f"Error processing transaction {stripe_transaction.id}: {str(e)}"
            )

    output_data["success"] = total_success
    output_data["failed"] = output_data["total"] - total_success
    return output_data


def sync_stripe_orders_payment_status(
    channel, property_to_update, property_to_update_value
):
    """Sync stripe orders payment status

    Args:
        channel: Channel object
        property_to_update: The property to be updated when payment status is paid, e.g. "status"
        property_to_update_value: The value to be updated when payment status is paid, e.g. "active"

    Returns:
        dict: {'success': int, 'failed': int, 'total': int}
    """

    stripe.api_key = channel.api_secret
    # Get all shopturbo orders platforms that payment_status is not 'paid'
    order_platforms = ShopTurboOrdersPlatforms.objects.filter(
        channel=channel,
        order__status="active",
        platform_payment_id__isnull=False,
    ).exclude(payment_status__in=["paid", "complete"])
    for order_platform in order_platforms:
        try:
            stripe_transaction = stripe.checkout.Session.retrieve(
                order_platform.platform_payment_id
            )
            order_platform.payment_status = stripe_transaction.status
            order_platform.save()
        except Exception:
            logger.error(
                f"Error processing transaction {order_platform.platform_payment_id}: {traceback.format_exc()}"
            )
            continue

        if stripe_transaction.status not in ["paid", "complete"]:
            continue

        if stripe_transaction.payment_intent:
            try:
                payment_intent = stripe.PaymentIntent.retrieve(
                    stripe_transaction.payment_intent,
                    expand=["charges.data.balance_transaction"],
                )

                # Get the payment time from the first successful charge
                if payment_intent.latest_charge:
                    charge = stripe.Charge.retrieve(payment_intent.latest_charge)
                    if charge.paid and charge.status == "succeeded":
                        payment_time = datetime.fromtimestamp(
                            charge.created, tz=timezone.utc
                        )
                        # Now you can use payment_time as needed
                        order_platform.paid_at = payment_time
                        order_platform.save()
            except Exception as e:
                logger.error(
                    f"Error retrieving paid_at frompayment intent {stripe_transaction.payment_intent}: {str(e)}"
                )
        if is_valid_uuid(property_to_update):
            order_custom_prop_val = ShopTurboOrdersValueCustomField.objects.filter(
                field_name__id=property_to_update,
            ).first()
            if not order_custom_prop_val:
                order_custom_prop_val = ShopTurboOrdersValueCustomField.objects.create(
                    workspace=order_platform.order.workspace,
                    field_name_id=property_to_update,
                    value=property_to_update_value,
                )
            else:
                order_custom_prop_val.value = property_to_update_value
                order_custom_prop_val.save()
        else:
            order = order_platform.order
            setattr(order, property_to_update, property_to_update_value)
            order.save()
