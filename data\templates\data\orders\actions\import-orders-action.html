{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="mb-10">
    <div class="mb-5">
        <label class="{% include 'data/utility/form-label.html' %}">
            <span>
                {% if LANGUAGE_CODE == 'ja'%}
                連携サービス
                {% else %}
                Integrations
                {% endif %}

            </span>
        </label>
        <select required class="{% include 'data/utility/select-form.html' %} select2-this" 
            data-control="select2" data-hide-search="false" 
            id="channel-select-{{action_index}}"
            name="select_integration_ids-{{action_index}}" 
            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}連携サービス{% else %}Integration{% endif %}">
            {% for channel in channels %}
                <option {% if channel.id|stringformat:"s" == active_channel|stringformat:"s" %}selected{% endif %} value="{{ channel.id }}" data-checkbox="{{channel.ms_refresh_token}}" data-platform="{{ channel.integration.slug }}">
                    {{ channel.name|cut:" Power Order" }}
                </option>
            {% endfor %}
        </select>
    </div>

    <style>
        #import-orders-content-loader{{action_index}}{
            display:none;
        }
        .htmx-request#import-orders-content-loader{{action_index}}{
            display:inline-block;
        }
    </style>
    <span class="spinner-border spinner-border-sm text-secondary" id="import-orders-content-loader{{action_index}}" style="position:relative; right:-6px" role="status">
        <span class="visually-hidden">Loading...</span>
    </span>

    <div id="import-orders-content-{{action_index}}"></div>

    <span hx-get="{% host_url 'import_integration_orders' host 'app' %}"
        hx-target="#import-orders-content-{{action_index}}"
        hx-trigger="load, importIntegrationOrdersContent from:#channel-select-{{action_index}}"
        hx-include="#channel-select-{{action_index}}"
        hx-vals='{"action_index":"{{action_index}}" {% if action_node_id %},"action_node_id":"{{action_node_id}}"{% endif %}}'
        hx-indicator="#import-orders-content-loader{{action_index}}"
        /><span>
</div>
<script>
    $('#channel-select-{{action_index}}').select2().on('select2:select', function (e) {
        htmx.trigger("#channel-select-{{action_index}}", "importIntegrationOrdersContent", {})
    })
</script>