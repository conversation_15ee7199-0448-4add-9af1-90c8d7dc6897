import ast
import math

from django import template
from datetime import datetime

from data.models.order import ShopTurboOrders, ShopTurboItemsOrders
from data.models.item import ShopTurboItems
from data.templatetags.customtags.object_tags import get_object_from_basemodel_id
from data.templatetags.customtags.string_tags import is_uuid
from data.orders.utils.order_decimal_calculator import (
    calculate_order_total_with_decimal_point,
    calculate_order_total_without_tax_with_decimal_point,
    get_decimal_type_from_legacy_format
)

from utils.utility import get_workspace

register = template.Library()

@register.filter
def calculate_total_price(custom_field_value):
    """
    Calculates the total price from the custom field value.
    """
    total_price = float(0)
    for value in custom_field_value:
        try:
            if isinstance(value['item'], str) and is_uuid(value['item']):
                item_obj = get_object_from_basemodel_id(
                    value['item'], "commerce_items")
                total_price += item_obj.price * value['number_of_items']
            else:
                total_price += float(value['item_price']) * \
                    value['number_of_items']
        except:
            pass
    return total_price

@register.filter()
def calculate_total_price_decimal_point(order, type_list):
    """
    Calculate order total price with tax, applying decimal point configuration.
    
    Args:
        order: Order ID (string or UUID)
        type_list: Comma-separated string with decimal type and currency (e.g., "line_item_cut_off,USD")
        
    Returns:
        float: Calculated total price with tax and decimal point adjustments
    """
    try:
        # Convert order to string if needed
        order_id = str(order) if order else None
        if not order_id:
            return 0.0
            
        # Extract decimal type from legacy format
        decimal_type = get_decimal_type_from_legacy_format(type_list)
        if not decimal_type:
            return 0.0
        
        # Get workspace from order (we need to get it somehow - using request context would be better)
        # For now, get it from the order object
        try:
            order_obj = ShopTurboOrders.objects.select_related('workspace').filter(id=order_id).first()
            if not order_obj or not order_obj.workspace:
                return 0.0
            workspace = order_obj.workspace
        except Exception:
            # Fallback: try to get workspace from context or return 0
            return 0.0
            
        # Use the new utility function
        return calculate_order_total_with_decimal_point(order_id, workspace, decimal_type)
        
    except Exception as e:
        # Log error in production
        print(f"Error in calculate_total_price_decimal_point template tag: {e}")
        return 0.0

@register.filter()
def calculate_total_price_without_tax_decimal_point(order, type_list):
    """
    Calculate order total price without tax, applying decimal point configuration.
    
    Args:
        order: Order ID (string or UUID)
        type_list: Comma-separated string with decimal type and currency (e.g., "line_item_cut_off,USD")
        
    Returns:
        float: Calculated total price without tax and decimal point adjustments
    """
    try:
        # Convert order to string if needed
        order_id = str(order) if order else None
        if not order_id:
            return 0.0
            
        # Extract decimal type from legacy format
        decimal_type = get_decimal_type_from_legacy_format(type_list)
        if not decimal_type:
            return 0.0
        
        # Get workspace from order (we need to get it somehow - using request context would be better)
        # For now, get it from the order object
        try:
            order_obj = ShopTurboOrders.objects.select_related('workspace').filter(id=order_id).first()
            if not order_obj or not order_obj.workspace:
                return 0.0
            workspace = order_obj.workspace
        except Exception:
            # Fallback: try to get workspace from context or return 0
            return 0.0
            
        # Use the new utility function
        return calculate_order_total_without_tax_with_decimal_point(order_id, workspace, decimal_type)
        
    except Exception as e:
        # Log error in production
        print(f"Error in calculate_total_price_without_tax_decimal_point template tag: {e}")
        return 0.0

@register.filter()
def apply_tax(value, tax_rate):
    try:
        return float(value) * (100 + float(tax_rate))/100
    except (ValueError, TypeError):
        return value

@register.filter
def get_item_price(item: ShopTurboItems, key):
    value_return = None
    if item:
        item_price = item.shopturbo_item_price.filter(default=True).first()
        if item_price:
            if key == 'object':
                value_return = item_price
            else:
                value_return = getattr(item_price, key, None)
        else:
            if key == 'currency':
                value_return = item.currency

    return value_return

@register.filter
def get_item_purchase_price(item: ShopTurboItems, key):
    value_return = None
    item_price = item.purchase_prices.filter(default=True).first()
    if item_price:
        if key == 'object':
            value_return = item_price
        else:
            value_return = getattr(item_price, key, None)
    else:
        if key == 'currency':
            value_return = item.currency

    return value_return

@register.filter(name='value_to_float')
def value_to_float(value=None):
    if value:
        try:
            return float(value)
        except:
            return 0
    return value

@register.filter
def format_number(value):
    try:
        value = float(value)
        if value.is_integer():
            return int(value)
        return f"{value:.2f}".rstrip('0').rstrip('.')
    except (ValueError, TypeError):
        return value

@register.filter()
def decimal_point(value, type_list):
    try:
        type_list = type_list.split(",")
        type = type_list[0]
        currency = type_list[1]

        if type == "cut_off":
            value = float(value)
            if currency == "USD":
                value = math.floor(value*100)/100
            else:
                value = math.floor(value)

        elif type == "cut_over":
            value = float(value)
            if currency == "USD":
                value = math.ceil(value*100)/100
            else:
                value = math.ceil(value)
    except:
        pass

    return value

@register.filter(name='accounting_sum_receivable')
def accounting_sum_receivable(obj=None):
    if obj:
        try:
            value = obj.amount_debit - obj.amount_credit
            return float(value)
        except:
            return 0
    return obj

@register.filter(name='accounting_sum_payable')
def accounting_sum_payable(obj=None):
    if obj:
        try:
            value = obj.amount_credit - obj.amount_debit
            return float(value)
        except:
            return 0
    return obj

@register.simple_tag(name='calculate_assets')
def calculate_assets(request, objects, date_str, to_next_month='True'):  # Income Accounting
    workspace = get_workspace(request.user)
    workspace_currency = ast.literal_eval(workspace.currencies)[0].lower()
    total_amount = 0
    for object_ in objects:
        if object_.currency:
            if object_.transaction_date and object_.currency.lower() == workspace_currency:
                transaction_date = object_.transaction_date.strftime('%Y-%m')
                transaction_date = datetime.strptime(
                    transaction_date, '%Y-%m').date()
                selected_date = datetime.strptime(date_str, '%Y-%m').date()
                if to_next_month == 'False':
                    if transaction_date == selected_date:
                        if not object_.type_journal:
                            total_amount += object_.amount
                else:
                    if transaction_date <= selected_date:
                        if not object_.type_journal:
                            total_amount += object_.amount
    return total_amount

@register.simple_tag(name='calculate_liabilities')
# Expense Accounting
def calculate_liabilities(request, objects, date_str, to_next_month='True'):
    workspace = get_workspace(request.user)
    workspace_currency = ast.literal_eval(workspace.currencies)[0].lower()
    total_amount = 0
    for object_ in objects:
        if object_.currency:
            if object_.transaction_date and object_.currency.lower() == workspace_currency:
                transaction_date = object_.transaction_date.strftime('%Y-%m')
                transaction_date = datetime.strptime(
                    transaction_date, '%Y-%m').date()
                selected_date = datetime.strptime(date_str, '%Y-%m').date()
                if to_next_month == 'False':
                    if transaction_date == selected_date:
                        if object_.type_journal:
                            total_amount += object_.amount
                else:
                    if transaction_date <= selected_date:
                        if object_.type_journal:
                            total_amount += object_.amount
    return total_amount


@register.simple_tag(name='calculate_equity')
# Colaborate between income and expense Accounting
def calculate_equity(request, objects, date_str, to_next_month='True'):
    workspace = get_workspace(request.user)
    workspace_currency = ast.literal_eval(workspace.currencies)[0].lower()
    total_amount = 0
    for object_ in objects:
        if object_.currency:
            if object_.transaction_date and object_.currency.lower() == workspace_currency:
                transaction_date = object_.transaction_date.strftime('%Y-%m')
                transaction_date = datetime.strptime(
                    transaction_date, '%Y-%m').date()
                selected_date = datetime.strptime(date_str, '%Y-%m').date()
                if to_next_month == 'False':
                    if transaction_date == selected_date:
                        if object_.type_journal:
                            total_amount -= object_.amount
                        else:
                            total_amount += object_.amount
                else:
                    if transaction_date <= selected_date:
                        if object_.type_journal:
                            total_amount -= object_.amount
                        else:
                            total_amount += object_.amount
    return total_amount

@register.filter()
def is_two_decimal_currencies(currency):
    return currency and currency.upper() in [
        "USD", "EUR", "GBP", "AUD", "CAD",
        "NZD", "CHF", "SGD", "CNY", "INR",
        "BRL", "MXN", "HKD", "NOK", "SEK",
        "DKK", "RUB", "ZAR", "TRY"]
