{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<div class="accordion accordion-icon-collapse" id="related_item">
    {% include 'data/partials/manage-drawer/associated-action-component.html' with obj=deal page_group_type=constant.TYPE_OBJECT_CASE %}

    {% host_url 'get_cases_events' deal.id host 'app' as hx_get %}
    {% include 'data/partials/manage-drawer/associated-activities-component.html' with obj=deal page_group_type=constant.TYPE_OBJECT_CASE hx_get=hx_get %}

    {% comment %} {% include 'data/partials/manage-drawer/associated-case-single-customer-component.html' %}

    {% include 'data/partials/manage-drawer/associated-case-estimate-component.html' with source=constant.TYPE_OBJECT_CASE obj=deal estimate_objects=deal.estimates.all %}

    {% include 'data/partials/manage-drawer/associated-tasks-component.html' with source=constant.TYPE_OBJECT_CASE obj=deal task_objects=deal.tasks.all %}
    
    {% include 'data/partials/manage-drawer/associated-case-invoice-component.html'  with source=constant.TYPE_OBJECT_CASE obj=deal invoice_objects=deal.invoices.all %}

    {% for associate in case_associates %}
        {% include 'data/partials/manage-drawer/associated-custom-field-component.html' with source=constant.TYPE_OBJECT_CASE obj=deal associate=associate customfield=associate|get_association_customfield customfield_type=associate|get_association_customfield_type page=page view_id=view_id %}
    {% endfor %}

    {% for associate in associates %}
        {% include 'data/partials/manage-drawer/associated-custom-field-component.html' with source=constant.TYPE_OBJECT_CASE obj=deal associate=associate customfield=associate|get_association_customfield customfield_type=associate|get_association_customfield_type page=page view_id=view_id %}
    {% endfor %} {% endcomment %}

    {% comment %} Associate label {% endcomment %}

    {% for label in association_labels %}
        {% if label.created_by_sanka %}
            {% if label.label == constant.TYPE_OBJECT_ORDER %}
            {% host_url 'orders_association_drawer' host 'app' as add_association_url %}
            {% elif label.label == constant.TYPE_OBJECT_INVOICE %}
            {% host_url 'biling_association_drawer' host 'app' as add_association_url %}
            {% elif label.label == constant.TYPE_OBJECT_ESTIMATE %}
            {% host_url 'biling_association_drawer' host 'app' as add_association_url %}
            {% endif %}
        {% endif %}

        {% include 'data/partials/manage-drawer/associated-label-component.html' with obj=deal source=constant.TYPE_OBJECT_CASE from=constant.TYPE_OBJECT_CASE label=label add_association_url=add_association_url%}
    {% endfor %}

    {% for label in related_association_labels %}
        {% include 'data/partials/manage-drawer/related-associated-label-component.html' with obj=deal from=label.object_source label=label %}
    {% endfor %}


    {% comment %} Notes {% endcomment %}
    {% host_url 'case_notes_form_update' id=deal.id host 'app' as add_note_url %}
    {% include 'data/partials/manage-drawer/associated-notes-component.html' with add_note_url=add_note_url %} 
<div>
