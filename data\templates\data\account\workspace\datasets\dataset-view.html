{% extends 'base.html' %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}
{% block content %}

<div class="w-100 h-100">
    <div class="mt-5 mb-15 gx-5 h-100">
        <div class="px-5 px-lg-10 d-flex justify-content-between align-items-center mb-5">
            <div class="">
                <a href="{% host_url 'datasets' host 'app' %}" class="fw-bolder fs-4 mb-0">
                    {% if LANGUAGE_CODE == 'ja' %}
                    すべてのデータセット
                    {% else %}
                    All Datasets
                    {% endif %}
                </a>
                <div class="fw-bold fs-2">
                    {{ obj.name }}
                </div>
            </div>
            <div class="d-flex align-items-center">
                <form method="POST" id="download-panel-form" action="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?export=1">
                    {% csrf_token %}
                    <button class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1" type="submit" form="download-panel-form">
                        <span class="svg-icon svg-icon-4">
                            <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                                <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                            </svg>
                        </span>
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            ダウンロード
                            {% else %}
                            Download
                            {% endif %}
                        </span>
                    </button>
                </form>
                <div class="mx-2">
                    <button class="ms-2 w-100px align-items-center d-flex btn btn-primary py-1 rounded-1 dataset-create-button"
                        hx-get="{% url 'dataset_form' %}" 
                        hx-trigger="click"
                        hx-target="#dataset-drawer-content"
                        hx-indicator=".loading-drawer-spinner,.view-form"
                        hx-vals='{"dataset_id":"{{obj.id}}"}'
                        hx-swap="innerHTML"
                    >
                        <span class="svg-icon svg-icon-4">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.3" fill-rule="evenodd" clip-rule="evenodd" d="M2 4.63158C2 3.1782 3.1782 2 4.63158 2H13.47C14.0155 2 14.278 2.66919 13.8778 3.04006L12.4556 4.35821C11.9009 4.87228 11.1726 5.15789 10.4163 5.15789H7.1579C6.05333 5.15789 5.15789 6.05333 5.15789 7.1579V16.8421C5.15789 17.9467 6.05333 18.8421 7.1579 18.8421H16.8421C17.9467 18.8421 18.8421 17.9467 18.8421 16.8421V13.7518C18.8421 12.927 19.1817 12.1387 19.7809 11.572L20.9878 10.4308C21.3703 10.0691 22 10.3403 22 10.8668V19.3684C22 20.8218 20.8218 22 19.3684 22H4.63158C3.1782 22 2 20.8218 2 19.3684V4.63158Z" fill="currentColor"/>
                                <path d="M10.9256 11.1882C10.5351 10.7977 10.5351 10.1645 10.9256 9.77397L18.0669 2.6327C18.8479 1.85165 20.1143 1.85165 20.8953 2.6327L21.3665 3.10391C22.1476 3.88496 22.1476 5.15129 21.3665 5.93234L14.2252 13.0736C13.8347 13.4641 13.2016 13.4641 12.811 13.0736L10.9256 11.1882Z" fill="currentColor"/>
                                <path d="M8.82343 12.0064L8.08852 14.3348C7.8655 15.0414 8.46151 15.7366 9.19388 15.6242L11.8974 15.2092C12.4642 15.1222 12.6916 14.4278 12.2861 14.0223L9.98595 11.7221C9.61452 11.3507 8.98154 11.5055 8.82343 12.0064Z" fill="currentColor"/>
                            </svg>
                        </span>

                        <span class="">

                            {% if LANGUAGE_CODE == 'ja' %}
                            編集
                            {% else %}
                            Edit
                            {% endif %}
                        </span>
                    </button>
                </div>
            </div>
        </div>

        <div class="px-lg-10 px-5">
            {% if error %}
                <div class="alert alert-danger fs-6 gy-4 my-3 pb-3 mx-10" role="alert" id="error-alert">
                    {% if LANGUAGE_CODE == 'ja' %}
                    データセットの処理中に問題が発生しました。データセットのメトリクスを変更して再試行してください。
                    {% else %}
                    An issue occurred while processing the dataset. Try changing the dataset metrics.
                    {% endif %}

                    <!-- Hidden error debug area -->
                    <div id="real-error" style="display: none; margin-top: 10px; font-size: 0.85em; color: #721c24;">
                        <strong>[Debug Info]</strong><br>
                        {{ error }}
                    </div>
                </div>
            {% endif %}
            <table class="table table-row-dashed align-middle fs-6 gy-4 my-3 pb-3">
                <thead class="{% include "data/utility/table-header.html" %} ">
                    <tr>
                        {% for field in display_fields %}
                            <th class="text-start">{% parse_dataset_header field LANGUAGE_CODE %}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in data_rows %}
                        <tr>
                            {% for field in display_fields %}
                                <td>
                                    <div class="fw-bold py-2">
                                        {% get_item_with_prefix base_source row field as cell_value %}
                                        {% if cell_value or cell_value == 0 %}
                                            {{cell_value}}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </div>
                                </td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>

            <ul class="pagination pt-5">
                {% if paginator %}
                    {% if current_page == 1 %}
                        <li class="page-item previous disabled"><a href="#" class="page-link"><i class="previous"></i></a></li>
                    {% else %}
                        <li class="page-item previous"><a href="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?page={{prev_page}}" class="page-link"><i class="previous"></i></a></li>
                    {% endif %}

                    {% if current_page > 3 %}
                        <li class="page-item"><a href="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?page=1" class="page-link">1</a></li>
                        {% if current_page > 4 %}
                            <li class="page-item disabled"><a href="#" class="page-link">...</a></li>
                        {% endif %}
                    {% endif %}

                    {% for page in paginator.page_range %}
                        {% if page == current_page %}
                            <li class="page-item active"><a href="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?page={{page}}" class="page-link">{{page}}</a></li>
                        {% else %}
                            {% if page > current_page|add:"-3" and page < current_page|add:"3" %}
                                <li class="page-item"><a href="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?page={{page}}" class="page-link">{{page}}</a></li>
                            {% endif %}
                        {% endif %}
                    {% endfor %}

                    {% if current_page < paginator.num_pages|add:"-2" %}
                        {% if current_page < paginator.num_pages|add:"-3" %}
                            <li class="page-item disabled"><a href="#" class="page-link">...</a></li>
                        {% endif %}
                        <li class="page-item"><a href="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?page={{paginator.num_pages}}" class="page-link">{{paginator.num_pages}}</a></li>
                    {% endif %}

                    {% if current_page == paginator.num_pages %}
                        <li class="page-item next disabled"><a href="#"  class="page-link"><i class="next"></i></a></li>
                    {% else %}
                        <li class="page-item next"><a href="{% host_url 'dataset_view' dataset_id=obj.id host 'app' %}?page={{next_page}}"  class="page-link"><i class="next"></i></a></li>
                    {% endif %}
                {% endif %}
            </ul>
        </div>
    </div>
</div>

<script>
(function () {
    const alertBox = document.getElementById('error-alert');
    if (!alertBox) return;

    let clickCount = 0;
    let timer = null;

    alertBox.addEventListener('click', function () {
        clickCount++;

        if (clickCount === 3) {
            const debugBox = document.getElementById('real-error');
            if (debugBox) {
                debugBox.style.display = 'block';
            }
            clickCount = 0;
            clearTimeout(timer);
        } else {
            if (timer) clearTimeout(timer);
            timer = setTimeout(() => clickCount = 0, 500);  // Reset after 500ms
        }
    });
})();
</script>

{% endblock %}