#!/usr/bin/env python3
"""
Test script to verify report drawer performance improvements.
This script can be run to check if the optimizations are working correctly.
"""

import time
import requests
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.core.cache import cache
from data.models import Workspace, Channel, Integration


class ReportDrawerPerformanceTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test user and workspace
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.workspace = Workspace.objects.create(
            name='Test Workspace',
            owner=self.user
        )
        
        # Create test integrations and channels
        self.twitter_integration = Integration.objects.create(
            name='Twitter',
            slug='twitter'
        )
        
        self.instagram_integration = Integration.objects.create(
            name='Instagram', 
            slug='instagram'
        )
        
        self.twitter_channel = Channel.objects.create(
            name='Test Twitter',
            integration=self.twitter_integration,
            workspace=self.workspace
        )
        
        self.instagram_channel = Channel.objects.create(
            name='Test Instagram',
            integration=self.instagram_integration,
            workspace=self.workspace
        )
    
    def test_drawer_load_performance(self):
        """Test that the drawer loads quickly"""
        self.client.login(username='testuser', password='testpass123')
        
        # Clear cache to ensure fresh test
        cache.clear()
        
        # Measure time for first load (no cache)
        start_time = time.time()
        response = self.client.get('/reports/create-drawer/')
        first_load_time = time.time() - start_time
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Twitter')
        self.assertContains(response, 'Test Instagram')
        
        # Measure time for second load (with cache)
        start_time = time.time()
        response = self.client.get('/reports/create-drawer/')
        cached_load_time = time.time() - start_time
        
        self.assertEqual(response.status_code, 200)
        
        # Cached load should be faster
        self.assertLess(cached_load_time, first_load_time)
        
        print(f"First load time: {first_load_time:.3f}s")
        print(f"Cached load time: {cached_load_time:.3f}s")
        print(f"Performance improvement: {((first_load_time - cached_load_time) / first_load_time * 100):.1f}%")
    
    def test_channel_metrics_preloaded(self):
        """Test that channel metrics are preloaded in the response"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.get('/reports/create-drawer/')
        
        # Check that metrics are included in the response
        self.assertContains(response, 'channels_with_metrics')
        self.assertContains(response, 'metrics_')
        
        # Check that channel selection UI is present
        self.assertContains(response, 'channel-card')
        self.assertContains(response, 'selectChannel')
    
    def test_javascript_optimization(self):
        """Test that JavaScript is optimized for performance"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.get('/reports/create-drawer/')
        
        # Check that deferred initialization is present
        self.assertContains(response, 'requestIdleCallback')
        self.assertContains(response, 'initializeTagify')
        
        # Check that loading indicators are present
        self.assertContains(response, 'drawer-loading')
        self.assertContains(response, 'showLoading')
        self.assertContains(response, 'hideLoading')


if __name__ == '__main__':
    print("Report Drawer Performance Test")
    print("=" * 40)
    
    # This would be run as part of Django's test suite
    # python manage.py test test_report_drawer_performance
    
    print("To run this test:")
    print("python manage.py test test_report_drawer_performance")
    print("\nOptimizations implemented:")
    print("1. Database query optimization with select_related()")
    print("2. Caching of channel data for 5 minutes")
    print("3. Pre-calculation of channel metrics")
    print("4. Deferred JavaScript initialization")
    print("5. Improved UI with loading indicators")
    print("6. CSS transitions for better UX")
