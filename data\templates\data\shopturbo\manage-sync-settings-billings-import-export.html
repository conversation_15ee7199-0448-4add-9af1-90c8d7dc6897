{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div id="change-stamp-section" class="mb-10">
    <div class="">
        <form class="mb-10 px-0" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}" method="POST" >
            {% csrf_token %}

            <div class="task_wizard">
                <div class="mb-5">
                    <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                                連携サービス
                            {% else %}
                                Integrations
                            {% endif %}
                        </span>
                    </label>
                    <select required class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="false" 
                        id="channel-select"
                        data-allow-clear="false"
                        name="select_integration_ids" data-placeholder="{% if LANGUAGE_CODE == 'ja'%}連携サービス{% else %}Integrations{% endif %}">
                        <option></option>
                        {% for channel in channels %}
                            <option value="{{channel.id}}" data-platform="{{channel.integration.slug}}" data-name="{{channel.name}}">
                                {% if channel.integration.slug == 'hubspot' and channel.integration_app %}
                                    {% if LANGUAGE_CODE == 'ja' %}
                                        {{channel.integration_app.title_ja}} {{ channel.name|split:channel.integration_app.title|last }}
                                    {% else %} 
                                        {{channel.integration_app.title}} {{ channel.name|split:channel.integration_app.title|last }}
                                    {% endif %}
                                {% else %}
                                    {{channel.name}} 
                                {% endif %}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
    
            <div class="mb-8">
                {% for item_id in item_ids %}
                    <input type="hidden" name="item_ids" value="{{item_id}}">
                {% endfor %}
            </div>
                       
            <div id="shopify-mapping-table-section"  class="d-none">
                <div id="mapping-item-field" class="mb-10 mt-8">
                    {% csrf_token %}
                    <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                        <input id="item-checker" name="item-checker" class="form-check-input" type="checkbox"
                            
                            hx-post="{% host_url 'sync_item_header_extractor' host 'app' %}"
                            hx-target="#csvMappingContainer"
                            hx-trigger="load"
                            hx-encoding="multipart/form-data"
                            onchange="contactMappingHandler(this)"
                        >
                        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="allowchanges">
                            {% if LANGUAGE_CODE == 'ja' %}
                            連絡先情報のマッピング
                            {% else %}
                            Mapping Contact Infomation
                            {% endif %}
                        </label>
                    </div>

                    <div id="csvMappingContainer" class="mb-3"></div>
                </div>
            </div>
            <div id="mapping-table-section"  class="d-none">
                <div id="mapping-contact-field"  class="mb-10 mt-8">
                    {% csrf_token %}
                    <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                        <input id="contact-checker" name="item-checker" class="form-check-input" type="checkbox"

                            hx-post="{% host_url 'sync_order_header_extractor' host 'app' %}"
                            hx-target="#csvMappingContainerFreee"
                            hx-include="#channel-select"
                            hx-trigger="load, channelChanged"
                            hx-vals='{"mapping_type":"contact", "object_type":"{{ object_type }}"}'
                            hx-encoding="multipart/form-data"
                            onchange="contactMappingHandler(this)"
                        >
                    </div>

                    <div id="csvMappingContainerFreee" class="mb-3 mb-10 mt-8"></div>
                </div>
            </div>

            <div id="mapping-custom-object-field" class="d-none">
                <div id="custom-object-selector-layout" class="d-none">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span>
                            {% if LANGUAGE_CODE == 'ja' %}
                                カスタムオブジェクト
                            {% else %}
                                Custom Object 
                            {% endif %}
                        </span>
                    </label>
                    <select id="select-custom-object"
                    name="custom_object_options" 
                    class="bg-white form-select form-select-solid border h-40px" 
                    data-control="select2" 
                    data-hide-search="false" 
                    {% if LANGUAGE_CODE == 'ja'%}
                    data-placeholder="顧客を選択"
                    {% else %}
                    data-placeholder="Select Custom Object"
                    {% endif %}
                    data-allow-clear="true"
                    onchange="onChangeCustomObjectSelect(this)"
                    >
                        <option></option>
                    </select>

                </div>
                {% csrf_token %}
                <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none">
                    <input id="custom-object-checker" name="item-checker" class="form-check-input" type="checkbox"
                    
                        hx-post="{% url 'sync_custom_object_hubspot_header_extractor' %}"
                        hx-target="#integrationMappingContainer"
                        hx-include="#channel-select"
                        hx-trigger="channelChanged, customObjectChanged"
                        hx-vals='{"mapping_type":"custom_object","function_type":"{{import_export_type}}"}'
                        hx-encoding="multipart/form-data"

                        hx-on="htmx:beforeSend: 
                            document.getElementById('integrationMappingContainer').classList.add('d-none');
                            document.getElementById('loading-spinner-custom-object').classList.remove('d-none');
                        
                            htmx:afterRequest:
                            document.getElementById('loading-spinner-custom-object').classList.add('d-none');
                            document.getElementById('integrationMappingContainer').classList.remove('d-none')
                            "
                        
                    >
                </div>

                <div> 
                    <a type='button' class="d-none cursor-pointer fs-8 create-property-table-mapping-wizard create-property-elm-custom-object"
                    
                        hx-get="{% host_url 'new_property' host 'app' %}" 
                        hx-target="#create-property-table-mapping-content"
                        hx-vals='js:{"page_group_type":"custom_object","from-table-mapping":"True", "custom_object_id": "{{custom_object.id}}"}'
                        hx-trigger='click'
                    
                    ></a>
                </div>

                <div id="loading-spinner-custom-object" class="mt-5 text-center d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <div id="integrationMappingContainer" class="mb-3 mb-10 mt-8"></div>

                <button id="custom-object-export-btn" type="submit" class="btn btn-primary"

                    {% if object_type == 'commerce_inventory' %}
                    name="push_inventories"
                    {% elif object_type == 'commerce_items' %}
                    name="push_items"
                    {% endif %}>
                        <span class="svg-icon svg-icon-2 svg-icon-white">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-upload" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                                <path fill-rule="evenodd" d="M7.646 4.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 5.707V14.5a.5.5 0 0 1-1 0V5.707L5.354 7.854a.5.5 0 1 1-.708-.708z"/>
                            </svg>
                        </span>
                        {% if LANGUAGE_CODE == 'ja'%}
                        エクスポート{% if item_ids|length > 0 %}({{item_ids|length}}){% else %}({{count_items}}){% endif %}
                        {% else %}
                        Export {% if item_ids|length > 0 %}({{item_ids|length}}){% else %}({{count_items}}){% endif %}
                        {% endif %}
                </button>
            </div> 

            <div id="filter-panel" class="d-none">
                <div id="sync-filters-field" class="mb-10 mt-8 ">
                    <div class="mb-3">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                    フィルター
                                {% else %}
                                    Filters
                                {% endif %}
                            </span>
                        </label>
                        <select id="filter-select" name="filter" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}" data-allow-clear="true">
                            <option value=""></option>
                            {% for filter in filters %}
                                <option value="{{filter.id}}">
                                    {% if filter.name %}
                                        {{filter.name}}
                                    {% else %}
                                        {{filter.id}}
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div id="filter-choice-field" class="mb-3 d-none">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                    フィルターの選択肢
                                {% else %}
                                    Filter Choice
                                {% endif %}
                            </span>
                        </label>
                        <select id="filter-choice" name="filter-choice" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターの選択肢を選択{% else %}Select Filter Choice{% endif %}" data-hide-search="true" data-allow-clear="true">
                        </select>
                    </div>
                </div>

                <div id="sync-method-field" class="mb-10 mt-8">
                    <div class="mb-6">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                    同期方法
                                {% else %}
                                    Sync Method
                                {% endif %}
                            </span>
                        </label>
                        <select required name="sync-key" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="true">
                            <option value="platform" selected>
                                {% if LANGUAGE_CODE == 'ja' %}
                                プラットフォームID
                                {% else %}
                                Platform ID 
                                {% endif %}
                            </option>
                            <option value="sku">
                                {% if LANGUAGE_CODE == 'ja' %}
                                在庫SKU
                                {% else %}
                                Inventory SKU
                                {% endif %}
                            </option>
                        </select>
                    </div>
                </div>   
                
                <div class="mb-5"></div>
                <div class="mb-8">
                    {% if object_type == 'commerce_inventory' %}
                        {% for inventory_id in item_ids %}
                            <input type="hidden" name="inventory_ids" value="{{inventory_id}}">
                        {% endfor %}
    
                    {% elif object_type == 'commerce_items' %}
                        {% for item_id in item_ids %}
                            <input type="hidden" name="item_ids" value="{{item_id}}">
                        {% endfor %}
       
                    {% elif object_type == 'commerce_inventory_transaction' %}
                        {% for inventory_transactions_id in inventory_transactions_ids %}
                            <input type="hidden" name="inventory_transactions_ids" value="{{inventory_transactions_id}}">
                        {% endfor %}
                    {% elif object_type == 'commerce_inventory_warehouse' %}
                        {% for inventory_transactions_id in inventory_transactions_ids %}
                            <input type="hidden" name="inventory_transactions_ids" value="{{inventory_transactions_id}}">
                        {% endfor %}
                    {% endif %}
                </div>
            </div>

            <div id="mapping-status-field" class="d-none">
                {% csrf_token %}
                <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                    <input id="status-checker" name="item-checker" class="form-check-input" type="checkbox"
                        
                        hx-post="{% host_url 'sync_order_status_header_extractor' host 'app' %}"
                        hx-target="#statusMappingContainer"
                        hx-include="#channel-select"
                        hx-trigger="load, channelChanged"
                        hx-vals='{"function_type":"{{import_export_type}}"}'
                        hx-encoding="multipart/form-data"
                        onchange="contactMappingHandler(this)"

                        hx-on::before-send="
                            document.getElementById('statusMappingContainer').classList.add('d-none');
                            document.getElementById('loading-spinner-status').classList.remove('d-none');"
                        
                    >
                </div>

                <div id="loading-spinner-status" class="text-center d-none mt-8">
                    <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>
                    {% if LANGUAGE_CODE == 'ja' %}
                    ステータスのプロパティを読み込んでいます...
                    {% else %}
                    Loading Status Properties...
                    {% endif %}
                    </div>
                </div>

                <div id="statusMappingContainer" class="mb-3 mb-10 mt-8"></div>
            </div> 

            <div id="import-stripe-invoices" class="d-none"
                hx-get="{% host_url 'import_stripe_invoices' host 'app' %}"
                hx-target="this"
                hx-swap="innerHTML"
                hx-trigger="selectStripeIntegration from:body">
            </div>

            <div id="import-export-btns" class="d-none">
                <div id="sync-progress-message" class="mb-3 text-danger d-none">
                    {% if LANGUAGE_CODE == 'ja'%}
                        同期中です。しばらくお待ちください。
                    {% else %}
                        Sync in progress. Please wait a moment.
                    {% endif %}
                </div>

                <input hidden name="import_export_type" value="{{ import_export_type }}">
    
                <button id="import-btn" type="submit" class="btn btn-primary d-none"
    
                    {% if object_type == 'commerce_inventory' %}
                    name="sync_inventories"
                    {% elif object_type == 'commerce_items' %}
                    name="sync_items"
                    {% elif object_type == 'commerce_inventory_transaction' %}
                    name="sync_transactions"
                    {% elif object_type == 'commerce_inventory_warehouse' %}
                    name="sync_locations"
                    {% endif %}
                >
                    <span class="svg-icon svg-icon-2 svg-icon-white">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-download" viewBox="0 0 16 16">
                            <path d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                            <path d="M7.646 15.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 14.293V5.5a.5.5 0 0 0-1 0v8.793l-2.146-2.147a.5.5 0 0 0-.708.708z"/>
                        </svg>
                    </span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </button>
                
                {% if object_type|in_list:'commerce_inventory,commerce_items,invoices' %}
                    <button id="export-btn" type="submit" class="btn btn-primary d-none"
    
                        {% if object_type == 'commerce_inventory' %}
                        name="push_inventories"
                        {% elif object_type == 'commerce_items' %}
                        name="push_items"
                        {% endif %}>
                            <span class="svg-icon svg-icon-2 svg-icon-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-upload" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                                    <path fill-rule="evenodd" d="M7.646 4.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 5.707V14.5a.5.5 0 0 1-1 0V5.707L5.354 7.854a.5.5 0 1 1-.708-.708z"/>
                                </svg>
                            </span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            エクスポート{% if item_ids|length > 0 %}({{item_ids|length}}){% else %}({{count_items}}){% endif %}
                            {% else %}
                            Export {% if item_ids|length > 0 %}({{item_ids|length}}){% else %}({{count_items}}){% endif %}
                            {% endif %}
                    </button>
                {% endif %}
            </div>
        </form>
    </div>
</div>


{% if object_type|in_list:'commerce_inventory,commerce_items' %}
<script>
    // On Change Radio with name="switch"
    document.querySelectorAll('input[name="switch"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            if (document.querySelector('input[name="switch"]:checked')) {
                document.getElementById('export-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_3')
                document.getElementById('import-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_1')
            }
        });
    });
</script>
{% endif %}


<script>
    $('.select2-this').select2();
    $(document).ready(function() {
        const channelSelect = $('#channel-select');
        const shopifyMappingSection = $('#shopify-mapping-table-section');
        const hubspotMappingSection = $('#hubspot-mapping-table-section');
        const status_mapping = $('#mapping-status-field');
        const mappingSection = $('#mapping-table-section');
        const hubspotMapChecker = $('#hubspot-map-checker');
        const object_type = '{{ object_type }}';
        const import_export_type = '{{ import_export_type }}';
        var custom_object_mapping = document.getElementById("mapping-custom-object-field");
        var custom_object_selector_layout = document.getElementById("custom-object-selector-layout");
        let channel_id = $('#channel-select option:selected').val();

        toggleMappingSections();

        channelSelect.change(function() {
            toggleMappingSections();
        });

        function toggleMappingSections() {
            const selectedChannel = $('#channel-select option:selected');
            const platform = selectedChannel.data('platform');
            const channelName = selectedChannel.data('name');
            const filterPanel = $('#filter-panel');
            const importExportBtns = $('#import-export-btns');
            const channelId = selectedChannel.val();
            const customObjectChecker = document.getElementById('custom-object-checker');
            const customObjectExportBtn = $('#custom-object-export-btn');

            console.log("Toggling mapping sections...");
            console.log("Platform: ", platform);

            if (platform === 'hubspot') {
                shopifyMappingSection.addClass('d-none');
                hubspotMappingSection.removeClass('d-none');
                mappingSection.addClass('d-none');
                filterPanel.addClass('d-none');
                status_mapping.addClass('d-none');
                importExportBtns.addClass('d-none');
                customObjectExportBtn.removeClass('d-none');
                console.log("{{count_items}}")
                hubspotMapChecker.attr('hx-vals', JSON.stringify({
                    "channel_id": channelId,
                    "hub_domain": channelName,
                    "import_export_type": "{{ import_export_type }}",
                    "object_type": "{{ object_type }}",
                    "menu_key": "{{menu_key}}",
                    "item_ids": "{{item_ids}}",
                    "count_items": "{{count_items}}"
                }));

                console.log("HubSpot map checker: ", hubspotMapChecker);
                customObjectChecker.setAttribute('hx-post', "{% url 'sync_custom_object_hubspot_header_extractor' %}");
                console.log("Custom Object Checker: ", customObjectChecker);

                // Process the HTMX
                htmx.process(customObjectChecker)
                
                // Trigger the 'channelChanged' event on the body element
                htmx.trigger(customObjectChecker, 'channelChanged');

            } else if( platform === 'shopify') {
                shopifyMappingSection.removeClass('d-none');
                hubspotMappingSection.addClass('d-none');
                mappingSection.addClass('d-none');
                filterPanel.removeClass('d-none');
                status_mapping.addClass('d-none');
                importExportBtns.removeClass('d-none');

            } else if( platform === 'freee') {
                shopifyMappingSection.addClass('d-none');
                hubspotMappingSection.addClass('d-none');
                mappingSection.removeClass('d-none');
                filterPanel.removeClass('d-none');
                status_mapping.addClass('d-none');
                importExportBtns.removeClass('d-none');
            
            } else if (platform === 'moneyforward') {
                shopifyMappingSection.addClass('d-none');
                hubspotMappingSection.addClass('d-none');
                mappingSection.addClass('d-none');
                filterPanel.addClass('d-none');
                if (import_export_type === 'import') {
                    status_mapping.removeClass('d-none');
                } else {
                    status_mapping.addClass('d-none');
                }
                importExportBtns.removeClass('d-none');
            
            } else if (platform === 'salesforce') {
                shopifyMappingSection.addClass('d-none');
                hubspotMappingSection.addClass('d-none');
                mappingSection.addClass('d-none');
                filterPanel.addClass('d-none');
                status_mapping.addClass('d-none');
                importExportBtns.removeClass('d-none');
                customObjectExportBtn.addClass('d-none');
                channel_id = $('#channel-select option:selected').val();
                customObjectChecker.setAttribute('hx-post', "{% url 'sync_invoice_header_extractor' %}");
                console.log("Custom Object Checker: ", customObjectChecker);

                // Process the HTMX
                htmx.process(customObjectChecker)
                
                // Trigger the 'channelChanged' event on the body element
                htmx.trigger(customObjectChecker, 'channelChanged');

            } else {
                shopifyMappingSection.addClass('d-none');
                hubspotMappingSection.addClass('d-none');
                mappingSection.addClass('d-none');
                filterPanel.addClass('d-none');
                status_mapping.addClass('d-none');
                importExportBtns.addClass('d-none');

            }
            
            if (platform === 'hubspot' && object_type === 'invoices' && import_export_type === 'export'){
                custom_object_mapping.classList.remove('d-none')
                custom_object_selector_layout.classList.remove('d-none')
                var api_url = "{% url 'get_integration_custom_object_options' %}"

                //Get Current Selected Channel ID
                channel_id = $('#channel-select option:selected').val();
                fetch_all_custom_object(channel_id, api_url)
            } else if (platform === 'salesforce' && object_type === 'invoices' && import_export_type === 'import') {
                custom_object_mapping.classList.remove('d-none')
                custom_object_selector_layout.classList.remove('d-none')
                var api_url = "{% url 'get_integration_custom_object_options' %}"

                $('#import-btn').removeClass('d-none');
                $('#export-btn').addClass('d-none');

                //Get Current Selected Channel ID
                channel_id = $('#channel-select option:selected').val();
                fetch_all_custom_object(channel_id, api_url)
            } else {
                custom_object_mapping.classList.add('d-none')
                custom_object_selector_layout.classList.add('d-none')
            }

            // Reload mapping table
            Array.from(document.querySelectorAll("input[name='item-checker']")).forEach(element => {
                if (!Array.from(element.parentElement.parentElement.classList).includes('d-none')) {
                    htmx.trigger(`#${element.id}`, "channelChanged")
                }
            });
        }

        function fetch_all_custom_object(channel_id, api_url){
            $('#select-custom-object').select2({
                ajax: {
                    delay: 250, // Wait 250 milliseconds before triggering the request
                    dataType: 'json',
                    url: api_url,
                    data: function (params) {
                        var query = {
                            q: params.term,
                            page: params.page || 1,
                            json_response: true,
                            channel_id: channel_id
                        }
                        return query;
                    },
                    minimumInputLength: 2,
                },
                language: {
                    "noResults": function () {
                        return "{% if LANGUAGE_CODE == 'ja'%}商品が見つかりません{% else %}No item found{% endif %}";
                    },
                    searching: function () {
                        return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                    },
                    "loadingMore": function () {
                        return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                    },
                }
            })
        }

        // radio = document.querySelector('input[name="switch"]:checked');
        // if (radio) {
        //     document.getElementById('export-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_3')
        //     document.getElementById('import-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_1')
        // }

        const importExportType = '{{ import_export_type }}';
        console.log("Import/Export Type: ", importExportType);
        if (importExportType === 'import') {
            $('#import-btn').removeClass('d-none');
            $('#export-btn').addClass('d-none');
        } else if (importExportType === 'export') {
            $('#import-btn').addClass('d-none');
            $('#export-btn').removeClass('d-none');
        }
    });
    
    function contactMappingHandler(elm){
        var mapping = document.getElementById("csvMappingContainer")
        if (elm.checked === true){
            if (mapping){
                mapping.classList.add('d-none')
            }
            
        }
        else{
            if (mapping){
                mapping.classList.add('d-none')
            }
            
        }
    }

    function onChangeCustomObjectSelect(element){

        let selectedOption = element.options[element.selectedIndex];
        let selectedCustomObject = selectedOption.value;

        var customObjectChecker = document.getElementById('custom-object-checker');
        var currentHxVals = customObjectChecker.getAttribute('hx-vals');        
        var currentHxValsObject = JSON.parse(currentHxVals);

        currentHxValsObject.selected_custom_object = selectedCustomObject
         
        customObjectChecker.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
        console.log(customObjectChecker)

        htmx.trigger('#custom-object-checker', 'customObjectChanged');
    }
    
</script>

<script>
    //if filter selected
    $('#filter-select').on('change', function() {
        var filterId = this.value;
        if (filterId) {
            var filterChoice = document.getElementById("filter-choice");
            var filterChoiceField = document.getElementById("filter-choice-field");
            filterChoiceField.classList.remove('d-none');
            filterChoice.innerHTML = '';
            
            {% for filter in filters %}
                if (filterId === '{{filter.id}}') {
                    var choiceValueStr = `{{filter.choice_value|safe}}`
                    //Replace ' with "
                    choiceValueStr = choiceValueStr.replace(/'/g, '"');
                    var choiceValue = JSON.parse(choiceValueStr);
                    filterChoice.innerHTML += `<option value=""></option>`;
                    for (var i = 0; i < choiceValue.length; i++) {
                        filterChoice.innerHTML += `<option value="${choiceValue[i].value}">${choiceValue[i].label}</option>`;
                    }
                }
            {% endfor %}
        }
    })
</script>

<script>
    var channel_ids= {{ channel_ids|safe }};
    console.log(channel_ids);

    // Function to update sync status based on selected channel
    function updateSyncStatus(selectedChannelId) {
        var isRunning = channel_ids.includes(selectedChannelId);
        var syncMessage = document.getElementById('sync-progress-message');
        var importBtn = document.getElementById('import-btn');
        var exportBtn = document.getElementById('export-btn');
        var customObjectExportBtn = document.getElementById('custom-object-export-btn');
        
        // Show/hide sync message
        syncMessage.classList.toggle('d-none', !isRunning);
        
        // Enable/disable buttons
        importBtn.disabled = isRunning;
        if (exportBtn) {
            exportBtn.disabled = isRunning;
        }
        if (customObjectExportBtn) {
            customObjectExportBtn.disabled = isRunning;
        }
        
        if (isRunning) {
            console.log('Channel is running, sync in progress:', selectedChannelId);
        }
    }

    // Get selected channel ID from channel-select on page load
    $(document).ready(function() {
        var selectedChannelId = document.getElementById('channel-select').value;
        updateSyncStatus(selectedChannelId);

        // Listen for channel-select changes
        $('#channel-select').on('select2:select', function (e) {
            var selectedChannelId = $(this).val();
            updateSyncStatus(selectedChannelId);

            platform = $(this).find('option:selected').data('platform');
            if (platform == 'stripe') {
                document.body.dispatchEvent(new Event('selectStripeIntegration'))
                document.getElementById('import-stripe-invoices').classList.remove('d-none')
                document.getElementById('import-export-btns').classList.remove('d-none')
                document.getElementById('import-btn').classList.remove('d-none')
            } else {
                document.getElementById('import-stripe-invoices').classList.add('d-none')
                document.getElementById('import-export-btns').classList.add('d-none')
                document.getElementById('import-btn').classList.add('d-none')
            }
        });
    });

</script>
