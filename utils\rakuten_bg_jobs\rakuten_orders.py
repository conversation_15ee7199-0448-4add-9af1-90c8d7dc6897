import ast
import base64
import json
import re
import traceback

from django.utils import timezone
import requests
from django.core.exceptions import MultipleObjectsReturned
from datetime import datetime, timedelta

from data.constants.constant import ORDER_USAGE_CATEGORY
from data.models import (
    Channel,
    Company,
    CompanyPlatforms,
    Contact,
    ContactsPlatforms,
    Notification,
    ShopTurboInventory,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsOrders,
    ShopTurboItemsPlatforms,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
    ShopTurboItemsVariations,
    ShopTurboOrders,
    ShopTurboOrdersMappingFields,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersPlatforms,
    ShopTurboOrdersValueCustomField,
    TransferHistory,
    ShopTurboShippingCost,
    ShopTurboShippingCostPlatforms,
)
from utils.contact import display_contact_name
from utils.hubspot import push_hubspot_items, push_hubspot_orders
from utils.meter import has_quota
from utils.logger import logger


def import_rakuten_orders(
    channel_id,
    mapping_custom_fields,
    hubspot_channel_id=None,
    key_item_field=None,
    how_to_import=None,
    default_customer=None,
    import_filter=None,
):
    channel = Channel.objects.get(id=channel_id)
    service_secret = channel.api_key
    license_key = channel.api_secret
    platform = channel.integration.slug
    workspace = channel.workspace

    # HubSpot
    order_id_map_orders = {}

    try:
        current_page = 1

        headers = get_json_request_header(service_secret, license_key)
        now = timezone.now()
        start_datetime = now - timezone.timedelta(days=63)
        end_datetime = now
        if import_filter:
            try:
                if import_filter.get('created_at'):
                    key = import_filter.get('created_at').get('key')
                    value = import_filter.get('created_at').get('value')
                    if isinstance(value, str):
                        value = datetime.strptime(value, '%Y-%m-%d')
                        value = timezone.make_aware(value)

                    if key == 'start_date':
                        # Cannot be further than now - 63 days
                        start_datetime = value if value > start_datetime else start_datetime
                    elif key == 'end_date':
                        end_datetime = value
                elif import_filter.get('updated_at'):
                    key = import_filter.get('updated_at').get('key')
                    value = import_filter.get('updated_at').get('value')
                    if isinstance(value, str):
                        value = datetime.strptime(value, '%Y-%m-%d')
                        value = timezone.make_aware(value)

                    if key == 'start_date':
                        # Cannot be further than now - 63 days
                        start_datetime = value if value > start_datetime else start_datetime
                    elif key == 'end_date':
                        end_datetime = value
            except:
                pass
        
        start_datetime_str = start_datetime.strftime("%Y-%m-%dT%H:%M:%S") + "+0900"
        end_datetime_str = end_datetime.strftime("%Y-%m-%dT%H:%M:%S") + "+0900"
        payload = {
            "dateType": 1,
            "startDatetime": start_datetime_str,
            "endDatetime": end_datetime_str,
            "PaginationRequestModel": {
                "requestRecordsAmount": 100,
                "requestPage": current_page,
                "SortModelList": [{"sortColumn": 1, "sortDirection": 2}],
            },
        }

        # Handle Mapping custom field
        mapping, _ = ShopTurboOrdersMappingFields.objects.get_or_create(
            workspace=workspace, platform="rakuten"
        )
        # if mapping.input_data:
        #     mapping_custom_fields = parse_mapping(mapping.input_data)
        inventory_url = "https://api.rms.rakuten.co.jp/es/2.0/inventories/bulk-get"
        checked_items = []

        orders = []
        while True:
            payload["PaginationRequestModel"]["requestPage"] = current_page
            response = requests.post(
                url="https://api.rms.rakuten.co.jp/es/2.0/order/searchOrder/",
                headers=headers,
                json=payload,
            )
            current_page += 1
            if response.status_code != 200:
                continue

            data = response.json()
            total_page = data.get("PaginationResponseModel").get("totalPages")

            if current_page > total_page:
                break

            order_number_list = data.get("orderNumberList")
            page_orders = get_rakuten_orders(service_secret, license_key, order_number_list)
            orders.extend(page_orders)
        
        task = (
            TransferHistory.objects.filter(workspace=workspace, type="import_order", channel=channel)
            .order_by("-created_at")
            .first()
        )
        if task:
            task.total_number = len(orders)
            task.save()


        for order in orders:
            try:
                task = (
                    TransferHistory.objects.filter(workspace=workspace, type="import_order", channel=channel)
                    .order_by("-created_at")
                    .first()
                )
                if task:
                    if task.status == "canceled":
                        return False
                    task.success_number = orders.index(order) + 1
                    task.progress = (orders.index(order) + 1) / len(orders) * 100
                    task.save()
                print(order)

                order_progress = order.get("orderProgress")
                delivery_status = None
                if order_progress == 100:
                    delivery_status = "order_received"
                elif order_progress == 200:
                    delivery_status = "order_processing"
                elif order_progress == 300:
                    delivery_status = "order_shipping"
                elif order_progress == 400:
                    delivery_status = "order_processing"
                elif order_progress == 500:
                    delivery_status = "order_delivered"
                elif order_progress == 600:
                    delivery_status = "order_processing"
                elif order_progress == 700:
                    delivery_status = "payment_received"
                elif order_progress == 800:
                    delivery_status = "cancellation_received"
                elif order_progress == 900:
                    delivery_status = "cancellation_processed"

                order_id = order.get("orderNumber")
                order_display_name = order_id
                currency = "JPY"
                order_items = order.get("PackageModelList")[0].get("ItemModelList")
                try:
                    item_price = order_items[0].get("price")
                except:
                    traceback.print_exc()
                    item_price = None
                total_price = order.get("totalPrice", 0)
                platform_order_id = order_id

                order_platform, _ = ShopTurboOrdersPlatforms.objects.get_or_create(
                    channel=channel, platform_order_id=platform_order_id
                )

                if order_platform.order:
                    shopturbo_order = order_platform.order
                else:
                    if not has_quota(workspace, ORDER_USAGE_CATEGORY):
                        for user in workspace.user.all():
                            if user.verification.language == "ja":
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=user,
                                    message="制限を超えたため、受注レコードを作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。",
                                    type="error",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=workspace,
                                    user=user,
                                    message="Order could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                                    type="error",
                                )
                        return False
                    shopturbo_order = ShopTurboOrders.objects.create(
                        workspace=workspace,
                        currency=currency,
                        platform=platform,
                        status="active",
                        order_type="item_order",
                    )

                order_platform.order = shopturbo_order
                order_platform.order_display_name = order_display_name
                order_platform.order_at = order.get("orderDatetime")
                order_platform.save()

                print("=== rakuten_orders.py -- 72 ", order_platform)

                shopturbo_order.line_item_type = 'item_based_tax'
                
                shopturbo_order.tax_inclusive = order.get("includeTaxFlag", 0) == 1
                
                if item_price:
                    shopturbo_order.item_price_order = float(item_price)

                if total_price:
                    shopturbo_order.total_price = float(total_price)

                if total_price:
                    try:
                        total_price_without_tax = total_price - float(
                            order.get("TaxSummaryModelList")[0].get("reqPriceTax")
                        )
                        shopturbo_order.total_price_without_tax = (
                            total_price_without_tax
                        )
                        # tax = round(100*float((total_price - total_price_without_tax) / total_price_without_tax), 2)
                        # shopturbo_order.tax = tax
                    except:
                        traceback.print_exc()
                        pass

                if currency:
                    shopturbo_order.currency = currency

                if delivery_status:
                    shopturbo_order.delivery_status = delivery_status

                if order_platform.order_at:
                    shopturbo_order.order_at = order_platform.order_at

                shopturbo_order.save()
                shopturbo_order.shopturboitemsorders_set.all().delete()
                if order_items and len(order_items) > 0:
                    checked_items_in_order = []  # handle dupe items in order
                    for item in order_items:
                        item_id = item.get("SkuModelList")[0].get(
                            "merchantDefinedSkuId"
                        )
                        if item_id is None:
                            item_id = item.get("SkuModelList")[0].get("variantId")
                        item_name = item.get("itemName")
                        item_tax_rate = item.get("taxRate", 0)
                        item_price = item.get("price")
                        number_of_item = item.get("units")
                        manageNumber = item.get("manageNumber")
                        delvDateInfo = item.get("delvdateInfo")

                        item_platform, _ = (
                            ShopTurboItemsPlatforms.objects.get_or_create(
                                channel=channel,
                                platform_id=item_id,
                                platform_type="default",
                            )
                        )

                        shopturbo_item_order, _ = (
                            ShopTurboItemsOrders.objects.get_or_create(
                                platform_item_id=item_id,
                                order=shopturbo_order,
                                order_platform=order_platform,
                            )
                        )
                        update_value = False if how_to_import == "match" else True

                        if not key_item_field:
                            key_item_field = "None"
                        if item_platform.item:
                            shopturbo_item = item_platform.item
                            if key_item_field != "None":
                                custom_field = (
                                    ShopTurboItemsNameCustomField.objects.filter(
                                        workspace=workspace, name=key_item_field
                                    ).first()
                                )
                                custom_value = (
                                    ShopTurboItemsValueCustomField.objects.filter(
                                        field_name=custom_field, value=item_id
                                    ).first()
                                )
                                if custom_value:
                                    shopturbo_item.product_id = None
                                    item_platform.item = None
                                    shopturbo_item.save()
                                    item_platform.save()
                                    shopturbo_item = custom_value.items
                                    shopturbo_item.product_id = item_id
                                else:
                                    custom_value, _ = (
                                        ShopTurboItemsValueCustomField.objects.get_or_create(
                                            field_name=custom_field,
                                            items=shopturbo_item,
                                        )
                                    )
                                    custom_value.value = item_id
                                    custom_value.save()
                        else:
                            if shopturbo_item_order.item:
                                shopturbo_item = shopturbo_item_order.item
                                if key_item_field != "None":
                                    custom_field = (
                                        ShopTurboItemsNameCustomField.objects.filter(
                                            workspace=workspace, name=key_item_field
                                        ).first()
                                    )
                                    custom_value, _ = (
                                        ShopTurboItemsValueCustomField.objects.get_or_create(
                                            field_name=custom_field,
                                            items=shopturbo_item,
                                        )
                                    )
                                    custom_value.value = item_id
                                    custom_value.save()
                            else:
                                if key_item_field != "None":
                                    custom_field = (
                                        ShopTurboItemsNameCustomField.objects.filter(
                                            workspace=workspace, name=key_item_field
                                        ).first()
                                    )
                                    custom_value = (
                                        ShopTurboItemsValueCustomField.objects.filter(
                                            field_name=custom_field, value=item_id
                                        ).first()
                                    )
                                    if custom_value:
                                        shopturbo_item = custom_value.items
                                        shopturbo_item.product_id = item_id
                                    else:
                                        update_value = True
                                        shopturbo_item = ShopTurboItems.objects.create(
                                            workspace=workspace,
                                            platform=platform,
                                            product_id=item_id,
                                        )
                                        custom_value, _ = (
                                            ShopTurboItemsValueCustomField.objects.get_or_create(
                                                field_name=custom_field,
                                                items=shopturbo_item,
                                            )
                                        )
                                        custom_value.value = item_id
                                        custom_value.save()
                                else:
                                    update_value = True
                                    shopturbo_item = ShopTurboItems.objects.create(
                                        workspace=workspace,
                                        platform=platform,
                                        product_id=item_id,
                                    )
                        if number_of_item:
                            if item_id not in checked_items_in_order:
                                shopturbo_item_order.number_item = float(number_of_item)
                                checked_items_in_order.append(item_id)
                            else:
                                shopturbo_item_order.number_item += float(
                                    number_of_item
                                )
                        if item_price:
                            shopturbo_item_order.item_price_order = float(item_price)
                        if currency:
                            shopturbo_item_order.currency = currency
                        if delvDateInfo:
                            shopturbo_item_order.rakuten_option = delvDateInfo
                        shopturbo_item_order.save()

                        if update_value:
                            if item_name:
                                shopturbo_item.name = item_name
                            if item_price:
                                shopturbo_item.price = float(item_price)
                            if currency:
                                shopturbo_item.currency = currency

                        shopturbo_item.status = "active"
                        shopturbo_item.save()

                        item_platform.platform_id = item_id
                        item_platform.item = shopturbo_item
                        item_platform.save()

                        price, _ = ShopTurboItemsPrice.objects.get_or_create(
                            item=shopturbo_item,
                            price=float(item_price),
                            currency=currency,
                        )
                        check_default = ShopTurboItemsPrice.objects.filter(
                            item=shopturbo_item, default=True
                        )
                        if len(check_default) == 0:
                            price.default = True
                        price.name = item_name
                        price.tax = item_tax_rate * 100
                        price.save()

                        shopturbo_item_order.item = shopturbo_item
                        shopturbo_item_order.item_price = price
                        shopturbo_item_order.save()

                        if mapping_custom_fields:
                            item_detail = get_rakuten_item_detail(service_secret, license_key, item_id)
                            for field in mapping_custom_fields:
                                if field in ['genre_id|item', 'item_type|item']:
                                    sanka_field = mapping_custom_fields[field].replace(
                                        '|item', '')
                                    value = None
                                    if field == 'genre_id|item':
                                        value = item_detail['genreId'] if 'genreId' in item_detail else None
                                    if field == 'item_type|item':
                                        value = item_detail['itemType'] if 'itemType' in item_detail else None

                                    custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field).first()
                                    custom_value, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                                        field_name=custom_field, items=shopturbo_item)
                                    custom_value.value = value
                                    custom_value.save()

                        try:
                            if item_id not in checked_items:
                                inventory_url = f"https://api.rms.rakuten.co.jp/es/2.0/inventories/manage-numbers/{manageNumber}/variants/{item_id}"
                                inventory_response = requests.get(
                                    url=inventory_url, headers=headers
                                )

                                stock = inventory_response.json().get("quantity", 0)

                                shopturbo_inventory, _ = (
                                    ShopTurboInventory.objects.get_or_create(
                                        workspace=workspace,
                                        name=item_id,
                                        status="active",
                                        currency=currency,
                                    )
                                )
                                shopturbo_inventory.available = stock
                                shopturbo_inventory.total_inventory = stock
                                shopturbo_inventory.save()
                                if not shopturbo_inventory.item.filter(
                                    id=shopturbo_item.id
                                ).exists():
                                    shopturbo_inventory.item.add(shopturbo_item)
                                checked_items.append(item_id)
                        except Exception as e:
                            traceback.print_exc()
                            shopturbo_inventory, _ = (
                                ShopTurboInventory.objects.get_or_create(
                                    workspace=workspace,
                                    name=item_id,
                                    status="active",
                                    currency=currency,
                                )
                            )
                            shopturbo_inventory.available = 0
                            shopturbo_inventory.total_inventory = 0
                            shopturbo_inventory.save()
                            if not shopturbo_inventory.item.filter(
                                id=shopturbo_item.id
                            ).exists():
                                shopturbo_inventory.item.add(shopturbo_item)
                            checked_items.append(item_id)
                            print(f"ERROR === rakuten_orders.py -- 298: {e}")

                        # print("=== shopify_orders.py -- 92 ", shopturbo_item, shopturbo_item_order)
                    shopturbo_order.number_item = len(order_items)
                    shopturbo_order.save()

                if "deliveryPrice" in order:
                    delivery_price = order.get("deliveryPrice")
                    try:
                        delivery_price_float = float(delivery_price)
                    except (TypeError, ValueError):
                        delivery_price_float = 0

                    delivery_tax = round(order.get("deliveryTaxRate", 0) * 100, 2)  # Convert to percentage
                    shipping_cost = None
                    try:
                        shipping_cost, created = ShopTurboShippingCost.objects.get_or_create(
                            name=f"Shipping Cost {delivery_price_float}",
                            workspace=workspace,
                            value=delivery_price_float,
                            tax=delivery_tax,
                            number_format="JPY",
                        )
                    except MultipleObjectsReturned:
                        shipping_cost = ShopTurboShippingCost.objects.filter(
                            name=f"Shipping Cost {delivery_price_float}",
                            workspace=workspace,
                            value=delivery_price_float,
                            tax=delivery_tax,
                            number_format="JPY",
                        ).first()

                    shipping_platform = ShopTurboShippingCostPlatforms.objects.filter(
                        shipping_cost=shipping_cost,
                        platform_id=platform_order_id,
                        channel=channel,
                        platform_type='order'
                    ).first()

                    if not shipping_platform:
                        shipping_platform = ShopTurboShippingCostPlatforms.objects.create(
                            channel=channel,
                            platform_type='order',
                            platform_id=platform_order_id,
                            shipping_cost=shipping_cost,
                        )

                    shopturbo_order.shipping_cost = shipping_cost
                    shopturbo_order.save()
                            
                delivery_address = None
                prefecture = None
                city = None
                sub_address = None
                zipCode1 = None
                zipCode2 = None
                phone_number = None
                customer = None
                if "OrdererModel" in order:
                    customer = order["OrdererModel"]

                    default_contact = None
                    default_company = None
                    try:
                        default_contact = Contact.objects.filter(
                            id=default_customer
                        ).first()
                        default_company = Company.objects.filter(
                            id=default_customer
                        ).first()
                    except:
                        pass

                    if default_contact:
                        shopturbo_order.contact = default_contact
                    elif default_company:
                        shopturbo_order.company = default_company
                    elif "emailAddress" in customer:
                        email = customer.get("emailAddress")
                        first_name = customer.get("firstName")
                        last_name = customer.get("familyName")
                        phone_number = f"{customer.get('phoneNumber1')}-{customer.get('phoneNumber2')}-{customer.get('phoneNumber3')}"
                        if channel.ms_refresh_token:
                            company_platform, _ = (
                                CompanyPlatforms.objects.get_or_create(
                                    channel=channel, platform_id=email
                                )
                            )

                            if company_platform.company:
                                company = company_platform.company

                            if not company_platform.company:
                                company, _ = Company.objects.get_or_create(
                                    workspace=workspace, email=email, name=first_name
                                )
                                company_platform.company = company
                                company_platform.save()

                            if last_name:
                                company.name = first_name + " " + last_name
                            if phone_number:
                                company.phone_number = phone_number
                            company.save()
                            shopturbo_order.company = company
                        else:
                            contact_platform, _ = (
                                ContactsPlatforms.objects.get_or_create(
                                    channel=channel, platform_id=email
                                )
                            )

                            if contact_platform.contact:
                                contact = contact_platform.contact

                            if not contact_platform.contact:
                                contact, _ = Contact.objects.get_or_create(
                                    workspace=workspace, email=email, name=first_name
                                )
                                contact_platform.contact = contact
                                contact_platform.save()

                            if last_name:
                                contact.last_name = last_name
                            if phone_number:
                                contact.phone_number = phone_number
                            contact.save()
                            shopturbo_order.contact = contact

                    shopturbo_order.save()

                sender = None
                if "PackageModelList" in order:
                    if len(order["PackageModelList"]) > 0:
                        sender = order["PackageModelList"][0]["SenderModel"]

                if mapping_custom_fields:
                    shipping_info_fields = {}
                    checked_shipping_info_fields = {}
                    for field in mapping_custom_fields:
                        field_value = mapping_custom_fields[field]
                        if "sub_property" in field_value:
                            split_field = field_value.split("|")
                            # shipping info custom property
                            if split_field[-2] not in shipping_info_fields:
                                shipping_info_fields[split_field[-2]] = []
                                checked_shipping_info_fields[split_field[-2]] = []
                            # shipping info sub-property
                            shipping_info_fields[split_field[-2]].append(split_field[0])

                    for shipping_info_field in shipping_info_fields:
                        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
                            workspace=workspace, name=shipping_info_field
                        ).first()
                        custom_value, _ = (
                            ShopTurboOrdersValueCustomField.objects.get_or_create(
                                field_name=custom_field, orders=shopturbo_order
                            )
                        )
                        custom_value.value = None
                        custom_value.save()
                    for field in mapping_custom_fields:
                        try:
                            sanka_field = mapping_custom_fields[field]
                            if "sub_property" in sanka_field:
                                split_field = sanka_field.split("|")
                                sanka_field = split_field[-2]
                                key = split_field[0]
                                value = []
                            else:
                                value = None
                            if field in [
                                "delivery_address",
                                "recipient_name",
                                "sender_name",
                                "postal_code",
                                "phone_number",
                                "payment_method",
                                "notes",
                                "sender_address",
                                "sender_postal_code",
                                "sender_phone_number",
                                "arrival_date",
                                "coupons_used",
                                "points_used",
                                "order_handling_fee",
                                "gift_wrapping_fee",
                                "delivery_date",
                                "delivery_time",
                                "shipping_info",
                                "inventory_non_sync_flag",
                            ]:
                                if sender:  # SenderModel is Recipient
                                    for model in order["PackageModelList"]:
                                        if "SenderModel" in model:
                                            sender = model["SenderModel"]
                                        if field == "recipient_name":
                                            sender_first_name = sender.get(
                                                "firstName", ""
                                            )
                                            sender_last_name = sender.get(
                                                "familyName", ""
                                            )
                                            if isinstance(value, list):
                                                value.append(
                                                    f"{sender_last_name} {sender_first_name}"
                                                )
                                            else:
                                                value = f"{sender_last_name} {sender_first_name}"
                                        if field == "delivery_address":
                                            sender_prefecture = sender.get(
                                                "prefecture", ""
                                            )
                                            sender_city = sender.get("city", "")
                                            sender_sub_address = sender.get(
                                                "subAddress", ""
                                            )
                                            if isinstance(value, list):
                                                value.append(
                                                    f"{sender_prefecture}{sender_city}{sender_sub_address}"
                                                )
                                            else:
                                                value = f"{sender_prefecture}{sender_city}{sender_sub_address}"
                                        if field == "postal_code":
                                            sender_zipCode1 = sender.get("zipCode1", "")
                                            sender_zipCode2 = sender.get("zipCode2", "")
                                            if isinstance(value, list):
                                                value.append(
                                                    f"{sender_zipCode1}-{sender_zipCode2}"
                                                )
                                            else:
                                                value = f"{sender_zipCode1}-{sender_zipCode2}"
                                        if field == "phone_number":
                                            sender_phone1 = sender.get(
                                                "phoneNumber1", ""
                                            )
                                            sender_phone2 = sender.get(
                                                "phoneNumber2", ""
                                            )
                                            sender_phone3 = sender.get(
                                                "phoneNumber3", ""
                                            )
                                            if isinstance(value, list):
                                                value.append(
                                                    f"{sender_phone1}-{sender_phone2}-{sender_phone3}"
                                                )
                                            else:
                                                value = f"{sender_phone1}-{sender_phone2}-{sender_phone3}"
                                if customer:  # OrdererModel is Sender
                                    if field == "sender_name":
                                        first_name = customer.get("firstName", "")
                                        last_name = customer.get("familyName", "")
                                        value = f"{last_name} {first_name}"
                                    if field == "sender_address":
                                        prefecture = customer.get("prefecture", "")
                                        city = customer.get("city", "")
                                        sub_address = customer.get("subAddress", "")
                                        value = f"{prefecture}{city}{sub_address}"
                                    if field == "sender_postal_code":
                                        zipCode1 = customer.get("zipCode1", "")
                                        zipCode2 = customer.get("zipCode2", "")
                                        value = f"{zipCode1}-{zipCode2}"
                                    if field == "sender_phone_number":
                                        phone1 = customer.get("phoneNumber1", "")
                                        phone2 = customer.get("phoneNumber2", "")
                                        phone3 = customer.get("phoneNumber3", "")
                                        value = f"{phone1}-{phone2}-{phone3}"
                                if field == "payment_method":
                                    value = order.get("SettlementModel").get(
                                        "settlementMethod", ""
                                    )
                                if field == "notes":
                                    value = order.get("remarks", "")
                                if field == "delivery_date":
                                    value = order.get("deliveryDate", None)
                                try:
                                    if field == "delivery_time":
                                        h1h2 = order.get("shippingTerm", None)
                                        value = h1h2
                                        if h1h2:
                                            if len(h1h2) == 3:
                                                h1 = int(h1h2[0])
                                                h2 = int(h1h2[1:])
                                            else:
                                                h1 = int(h1h2[:2])
                                                h2 = int(h1h2[2:])
                                            if (
                                                not (7 <= h1 <= 24)
                                                or not (7 <= h2 <= 24)
                                                or h1 >= h2
                                            ):
                                                value = h1h2
                                            else:
                                                value = f"{h1}:00 - {h2}:00"
                                    if field == "arrival_date":
                                        if order.get("deliveryDate"):
                                            value = order.get("deliveryDate")
                                        else:
                                            if "PackageModelList" in order:
                                                if len(order["PackageModelList"]) > 0:
                                                    value = order["PackageModelList"][
                                                        0
                                                    ]["ItemModelList"][0].get(
                                                        "delvdateInfo", None
                                                    )
                                    if field == "coupons_used":
                                        value = order.get("couponAllTotalPrice", 0)
                                    if field == "points_used":
                                        if "PointModel" in order:
                                            value = order["PointModel"].get(
                                                "usedPoint", 0
                                            )
                                    if field == "order_handling_fee":
                                        value = order.get("TaxSummaryModelList")[0].get(
                                            "paymentCharge", 0
                                        )
                                    if field == "gift_wrapping_fee":
                                        value = 0
                                        if order.get("WrappingModel1"):
                                            value += order["WrappingModel1"].get(
                                                "price", 0
                                            )
                                        if order.get("WrappingModel2"):
                                            value += order["WrappingModel2"].get(
                                                "price", 0
                                            )
                                    if field == "inventory_non_sync_flag":
                                        value = order["PackageModelList"][0][
                                            "ItemModelList"
                                        ][0].get("restoreInventoryFlag", None)
                                        if value == 0:
                                            value = "商品の設定に従う"
                                        elif value == 1:
                                            value = "在庫数を0にする"
                                        elif value == 2:
                                            value = "在庫連動しない"
                                except:
                                    value = None

                                custom_field = (
                                    ShopTurboOrdersNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field
                                    ).first()
                                )
                                if custom_field:
                                    if custom_field.type != "shipping_info":
                                        custom_value, _ = (
                                            ShopTurboOrdersValueCustomField.objects.get_or_create(
                                                field_name=custom_field,
                                                orders=shopturbo_order,
                                            )
                                        )
                                        custom_value.value = value
                                        custom_value.save()
                                    else:
                                        if (
                                            key
                                            not in checked_shipping_info_fields[
                                                sanka_field
                                            ]
                                        ):
                                            checked_shipping_info_fields[
                                                sanka_field
                                            ].append(key)
                                            custom_value, _ = (
                                                ShopTurboOrdersValueCustomField.objects.get_or_create(
                                                    field_name=custom_field,
                                                    orders=shopturbo_order,
                                                )
                                            )
                                            all_value = custom_value.value
                                            if all_value:
                                                all_value = all_value.replace("'", '"')
                                                all_value = json.loads(all_value)
                                                if all_value.get(key, None):
                                                    for value in value:
                                                        if value not in all_value[key]:
                                                            all_value[key].append(value)
                                                else:
                                                    all_value[key] = value
                                            else:
                                                all_value = {key: value}

                                            for field_value in ast.literal_eval(
                                                custom_field.sub_property
                                            ):
                                                print("all_value", all_value)
                                                if (
                                                    field_value
                                                    not in shipping_info_fields[
                                                        sanka_field
                                                    ]
                                                ):
                                                    all_value[field_value] = [""]
                                                    shipping_info_fields[
                                                        sanka_field
                                                    ].append(field_value)
                                                else:
                                                    if all_value.get(field_value):
                                                        if len(
                                                            all_value[field_value]
                                                        ) < len(all_value[key]):
                                                            all_value[
                                                                field_value
                                                            ].append("")
                                                    try:
                                                        all_value = {
                                                            k: all_value[k]
                                                            for k in shipping_info_fields[
                                                                sanka_field
                                                            ]
                                                        }
                                                    except Exception as e:
                                                        print(e)

                                            custom_value.value = all_value
                                            custom_value.save()
                        except Exception as e:
                            traceback.print_exc()
                            print(f"ERROR === rakuten_orders.py -- 191: {e}")

                # HubSpot
                if hubspot_channel_id:
                    _items = []
                    _amount = 0
                    for item_order in shopturbo_order.shopturboitemsorders_set.all():
                        _item_platform = item_order.item.item.filter(
                            channel=channel
                        ).first()
                        if _item_platform and _item_platform.platform_id:
                            _items.append(
                                {
                                    "name": item_order.item.name,
                                    "description": item_order.item.description,
                                    "hs_sku": _item_platform.platform_id,
                                    "price": item_order.item.price,
                                    "quantity": item_order.number_item,
                                    # 'currency': None                            # TODO: update later
                                }
                            )
                            _amount += item_order.number_item * item_order.item.price

                    _contact = None
                    if shopturbo_order.contact:
                        _contact = {
                            "sanka_id": f"{shopturbo_order.contact.contact_id}",
                            "name": display_contact_name(shopturbo_order.contact),
                            "email": shopturbo_order.contact.email,
                            "phone": shopturbo_order.contact.phone_number,
                            "address": delivery_address,
                            "state": prefecture,
                            "city": city,
                        }

                    order_id_map_orders[f"{shopturbo_order.order_id}"] = {
                        "order_at": shopturbo_order.order_at,
                        "platform_order_id": platform_order_id,
                        "platform": channel.integration.title_ja
                        if channel.integration.title_ja
                        else channel.integration.title,
                        "amount": _amount,
                        "items": _items,
                        "company": _contact,
                    }
            except Exception as e:
                logger.info(f"ERROR === rakuten_orders.py -- 250: {e}")
                pass
        # return False

    except Exception as e:
        traceback.print_exc()
        print(f"ERROR === rakuten_orders.py -- 40: {e}")

    # HubSpot
    if hubspot_channel_id:
        order_ids = order_id_map_orders.keys()
        if len(order_ids) == 0:
            print("No order to export HubSpot")
            return True

        channel = Channel.objects.get(id=hubspot_channel_id)
        print(f">>>>>>>>>>>>>>>> Start PUSH orders to HubSpot channel {channel.name}")
        push_hubspot_orders(str(channel.id), order_id_map_orders)
        print(f"<<<<<<<<<<<<<<<< Finish PUSH orders to HubSpot channel {channel.name}")


def get_rakuten_orders(service_secret, license_key, order_number_list):
    orders = []
    try:
        url = "https://api.rms.rakuten.co.jp/es/2.0/order/getOrder/"
        headers = get_json_request_header(service_secret, license_key)
        payload = {"orderNumberList": order_number_list, "version": 7}
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code != 200:
            print(
                f"ERROR === rakuten_orders.py -- 80: {response.status_code} - {response.text}"
            )
            return orders

        data = response.json()
        orders = data.get("OrderModelList")

    except Exception as e:
        traceback.print_exc()
        print(f"ERROR === rakuten_orders.py -- 87: {e}")

    return orders

def get_rakuten_item_detail(service_secret, license_key, item_id):
    item = None
    try:
        url=f"https://api.rms.rakuten.co.jp/es/2.0/items/manage-numbers/{item_id}"
        headers = get_json_request_header(service_secret, license_key)
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            print(
                f"ERROR === rakuten_orders.py -- 80: {response.status_code} - {response.text}"
            )
            return item

        data = response.json()
        item = data

    except Exception as e:
        traceback.print_exc()
        print(f"ERROR === rakuten_orders.py -- 87: {e}")

    return item

def pull_rakuten_items(channel_id, hubspot_channel_id=None, key_item_field=None):
    channel = Channel.objects.get(id=channel_id)
    service_secret = channel.api_key
    license_key = channel.api_secret
    platform = channel.integration.slug
    workspace = channel.workspace
    task = (
        TransferHistory.objects.filter(workspace=workspace, type="import_item")
        .order_by("-created_at")
        .first()
    )
    task.progress = 50
    task.save()

    # HubSpot
    sku_map_items = {}

    try:
        headers = get_json_request_header(service_secret, license_key)
        offset = 0
        num_found = 0
        max_offset = 10

        item_url = "https://api.rms.rakuten.co.jp/es/2.0/items/manage-numbers/"
        inventory_url = "https://api.rms.rakuten.co.jp/es/2.0/inventories/bulk-get"
        checked_items = []

        while offset <= max_offset:
            url = f"https://api.rms.rakuten.co.jp/es/2.0/items/search?offset={offset}&hits=100"
            response = requests.get(url=url, headers=headers)
            if response.status_code != 200:
                print(response.status_code, response.text)
                return

            data = response.json()
            results = data.get("results")
            if len(results) == 0:
                break

            for result in results:
                task = (
                    TransferHistory.objects.filter(
                        workspace=workspace, type="import_item"
                    )
                    .order_by("-created_at")
                    .first()
                )
                if task.status == "canceled":
                    return False
                item = result.get("item")
                item_id = item.get("manageNumber")
                print(
                    f"INFO === rakuten_orders.py -- 309: offset: {offset}, get item {item_id}"
                )
                item_name = item.get("title")
                item_description = item.get("productDescription", None)
                if item_description:
                    item_description = item_description.get("pc")
                variants = item.get("variants")
                item_price = list(variants.values())[0].get("standardPrice")
                currency = "JPY"
                created_at = item.get("created")

                item_platform, _ = ShopTurboItemsPlatforms.objects.get_or_create(
                    channel=channel, platform_id=item_id, platform_type="default"
                )

                if not key_item_field:
                    key_item_field = "None"
                update_value = False if key_item_field == "None" else True

                genre_id = item.get("genreId")
                item_type = item.get("itemType")
                item_type_custom_field = ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace, name="item_type"
                ).first()
                item_type_value = ShopTurboItemsValueCustomField.objects.filter(
                    field_name=item_type_custom_field, items=item_platform.item
                ).first()
                if item_type_value and item_type_value.value != item_type:
                    item_type_value.value = item_type
                    item_type_value.save()
                elif not item_type_value:
                    item_type_value, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                        field_name=item_type_custom_field, items=item_platform.item
                    )
                    item_type_value.value = item_type
                    item_type_value.save()

                genre_id_custom_field = ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace, name="genre_id"
                ).first()
                genre_id_value = ShopTurboItemsValueCustomField.objects.filter(
                    field_name=genre_id_custom_field, items=item_platform.item
                ).first()
                if genre_id_value and genre_id_value.value != genre_id:
                    genre_id_value.value = genre_id
                    genre_id_value.save()
                elif not genre_id_value:
                    genre_id_value, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                        field_name=genre_id_custom_field, items=item_platform.item
                    )
                    genre_id_value.value = genre_id
                    genre_id_value.save()
                
                if item_platform.item:
                    shopturbo_item = item_platform.item
                    if key_item_field != "None":
                        custom_field = ShopTurboItemsNameCustomField.objects.filter(
                            workspace=workspace, name=key_item_field
                        ).first()
                        custom_value = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=custom_field, value=item_id
                        ).first()
                        if custom_value:
                            shopturbo_item.product_id = None
                            item_platform.item = None
                            shopturbo_item.save()
                            item_platform.save()
                            shopturbo_item = custom_value.items
                            shopturbo_item.product_id = item_id
                        else:
                            custom_value, _ = (
                                ShopTurboItemsValueCustomField.objects.get_or_create(
                                    field_name=custom_field, items=shopturbo_item
                                )
                            )
                            custom_value.value = item_id
                            custom_value.save()
                else:
                    if key_item_field != "None":
                        custom_field = ShopTurboItemsNameCustomField.objects.filter(
                            workspace=workspace, name=key_item_field
                        ).first()
                        custom_value = ShopTurboItemsValueCustomField.objects.filter(
                            field_name=custom_field, value=item_id
                        ).first()
                        if custom_value:
                            shopturbo_item = custom_value.items
                            shopturbo_item.product_id = item_id
                        else:
                            update_value = True
                            shopturbo_item = ShopTurboItems.objects.create(
                                workspace=workspace,
                                platform=platform,
                                product_id=item_id,
                            )
                            custom_value, _ = (
                                ShopTurboItemsValueCustomField.objects.get_or_create(
                                    field_name=custom_field, items=shopturbo_item
                                )
                            )
                            custom_value.value = item_id
                            custom_value.save()
                    else:
                        update_value = True
                        shopturbo_item, _ = ShopTurboItems.objects.get_or_create(
                            workspace=workspace,
                            product_id=item_id,
                            platform=platform,
                        )
                    item_platform.item = shopturbo_item
                    item_platform.save()

                price, _ = ShopTurboItemsPrice.objects.get_or_create(
                    item=shopturbo_item, price=float(item_price), currency=currency
                )
                check_default = ShopTurboItemsPrice.objects.filter(
                    item=shopturbo_item, default=True
                )
                if len(check_default) == 0:
                    price.default = True

                if update_value:
                    if currency:
                        shopturbo_item.currency = currency
                    if item_name:
                        shopturbo_item.name = item_name
                        price.name = item_name
                    if item_description:
                        shopturbo_item.description = item_description
                    if created_at:
                        shopturbo_item.created_at = created_at

                price.save()
                shopturbo_item.save()

                for sku, variant in variants.items():
                    shopturbo_item_variant, _ = (
                        ShopTurboItemsVariations.objects.get_or_create(
                            items=shopturbo_item,
                            sku=sku,
                            currency=shopturbo_item.currency,
                        )
                    )
                    shopturbo_item_variant.price = variant.get("standardPrice")
                    shopturbo_item_variant.save()

                sku_map_items[f"{item_id}"] = {
                    "platform": channel.integration.title_ja
                    if channel.integration.title_ja
                    else channel.integration.title,
                    "name": shopturbo_item.name,
                    "price": shopturbo_item.price,
                }

                if item_id not in checked_items:
                    item_response = requests.get(
                        url=item_url + item_id, headers=headers
                    )
                    item_data = item_response.json()

                    variants = item_data.get("variants")
                    item_list = []
                    for variant in variants:
                        item_list.append(
                            {"manageNumber": item_id, "variantId": variant}
                        )

                    inventory_payload = {"inventories": item_list}
                    inventory_response = requests.post(
                        url=inventory_url, headers=headers, json=inventory_payload
                    )

                    for inventory in inventory_response.json()["inventories"]:
                        shopturbo_inventory, created = ShopTurboInventory.objects.get_or_create(
                            workspace=workspace,
                            name=inventory['variantId'],
                            status="active",
                            currency=currency,
                        )
                        initial_value = int(inventory['quantity']) if inventory['quantity'] else 0
                        shopturbo_inventory.initial_value = initial_value
                        # Don't set total_inventory directly - let transactions calculate it
                        shopturbo_inventory.save()
                        if not shopturbo_inventory.item.filter(id=shopturbo_item.id).exists():
                            shopturbo_inventory.item.add(shopturbo_item)

                        # Create initial inventory transaction if this is a new inventory
                        if created and initial_value > 0:
                            from utils.inventory import create_inventory_transaction_helper, re_calculate_inventory_stock
                            create_inventory_transaction_helper(shopturbo_inventory, None, None)
                            shopturbo_inventory.refresh_from_db()
                            re_calculate_inventory_stock(shopturbo_inventory)

                    checked_items.append(item_id)

            offset += 1
            num_found += len(results)
            if num_found >= int(data.get("numFound")):
                break

    except Exception as e:
        traceback.print_exc()
        print(f"ERROR === rakuten_orders.py -- 298: {e}")

    # HubSpot
    if hubspot_channel_id:
        skus = sku_map_items.keys()
        if len(skus) == 0:
            return True

        channel = Channel.objects.get(id=hubspot_channel_id)
        print(f">>>>>>>>>>>>>>>> Start PUSH items to HubSpot channel {channel.name}")
        push_hubspot_items(str(channel.id), sku_map_items)
        print(f"<<<<<<<<<<<<<<<< Finish PUSH items to HubSpot channel {channel.name}")


def get_rakuten_shop_name(service_secret, license_key):
    shop_name = None
    expiry_date = "Invalid Authentication, Expiry Date is Missing"
    headers = get_xml_request_header(service_secret, license_key)
    response = requests.get(
        url="https://api.rms.rakuten.co.jp/es/1.0/shop/shopMaster", headers=headers
    )
    if response.status_code == 200:
        match = re.search(r"<shopName>(.*?)<\/shopName>", response.text)
        if match:
            shop_name = match.group(1)

    # Getting Expiry Date Information
    response = requests.get(
        url="https://api.rms.rakuten.co.jp/es/1.0/license-management/license-key/expiry-date?licenseKey="
        + license_key,
        headers=headers,
    )
    data_resp = response.json()

    if "expiryDate" in data_resp:
        expiry_date = data_resp["expiryDate"].split("T")[0]
    elif "errors" in data_resp:
        raise Exception(data_resp["errors"][0]["message"])

    if shop_name and expiry_date:
        return shop_name, expiry_date

    raise Exception("Your access token might be wrong, or expired.")


def get_xml_request_header(service_secret, license_key):
    key = "ESA " + base64.b64encode(
        service_secret.encode("utf-8") + b":" + license_key.encode("utf-8")
    ).decode("utf8")
    headers = {
        "Authorization": key,
        "Accept": "application/xml",
        "Accept-Encoding": "gzip,deflate,sdch",
        "Accept-Language": "ja,en-US;q=0.8,en;q=0.6",
    }
    return headers


def get_json_request_header(service_secret, license_key):
    key = "ESA " + base64.b64encode(
        service_secret.encode("utf-8") + b":" + license_key.encode("utf-8")
    ).decode("utf8")
    headers = {"Authorization": key, "Content-Type": "application/json; charset=utf-8"}
    return headers


def parse_mapping(mapping):
    data = {}
    for field in mapping:
        if mapping[field]["skip"] == "False":
            data[field] = mapping[field]["value"]
    return data

def push_rakuten_items(request, mapping_custom_fields, item_ids, channel_id=None, update_inventory_toggle=False):
    channel = Channel.objects.get(id=channel_id)
    service_secret = channel.api_key
    license_key = channel.api_secret
    platform = channel.integration.slug
    workspace = channel.workspace

    for item_id in item_ids:
        try:
            headers = get_json_request_header(service_secret, license_key)
            
            task = (
                TransferHistory.objects.filter(workspace=workspace, type="export_item")
                .order_by("-created_at")
                .first()
            )
            if task.status == "canceled":
                return False

            if not isinstance(item_id, str):
                item_id = str(item_id.id)  
            shopturbo_item = ShopTurboItems.objects.filter(id=item_id).first()
            if not shopturbo_item:
                continue

            item_platform = ShopTurboItemsPlatforms.objects.filter(item=shopturbo_item).first()
            inputs = {}
            item_platform_id = None
            if item_platform and item_platform.platform_id:
                item_platform_id = item_platform.platform_id  
            else:
                continue
            
            response_item_platform = requests.get(
                url=f"https://api.rms.rakuten.co.jp/es/2.0/items/manage-numbers/{item_platform_id}",
                headers=headers,
            )
            
            response_item_platform_json = response_item_platform.json()
            item_number = response_item_platform_json.get("itemNumber")
            variants = response_item_platform_json.get("variants")
            
            inputs['itemNumber'] = item_number
            
            variant_dict = {}
            inventories_payload = {"inventories": []}
            for sku, variant in variants.items():
                shopturbo_item_variant = ShopTurboItemsVariations.objects.filter(sku=sku).first()
                variant["standardPrice"] = int(shopturbo_item_variant.price) if shopturbo_item_variant.price else variant.get("standardPrice")
                variant_dict[sku] = variant

                if update_inventory_toggle == "update" or update_inventory_toggle == "only_inventory":
                    shopturbo_inventory = ShopTurboInventory.objects.filter(workspace=workspace, name=sku, shop_turbo_item=shopturbo_item).first()
                    if shopturbo_inventory:
                        inventories_payload['inventories'].append(
                            {"manageNumber": item_number, "variantId": sku, "quantity": shopturbo_inventory.available, "mode": "ABSOLUTE"}
                        )
            
            inputs['variants'] = variant_dict
            
            if mapping_custom_fields:
                for field, custom_field in mapping_custom_fields.items():
                    if custom_field["skip"] == "True":
                        continue
                    sanka_field = custom_field.get("value") if custom_field else ''
                    sanka_field_custom_value = ShopTurboItemsValueCustomField.objects.filter(
                        field_name=sanka_field, items=shopturbo_item
                    ).first()
                    sanka_field_value = sanka_field_custom_value.value if sanka_field_custom_value else ''
                    if field == 'item_type':
                        inputs['itemType'] = sanka_field_value
                    elif field == 'genre_id':
                        inputs['genreId']  = sanka_field_value
                    elif field == 'title':
                        inputs['title'] = sanka_field_value if sanka_field_value else shopturbo_item.name
                    elif field == 'description':
                        inputs['productDescription'] = {
                            "pc": sanka_field_value if sanka_field_value else shopturbo_item.description
                        }
            
            if update_inventory_toggle != "only_inventory":
                response = requests.put(
                    url="https://api.rms.rakuten.co.jp/es/2.0/items/manage-numbers/" + item_number,
                    headers=headers,
                    json=inputs,
                )

                logger.info("response",response.status_code)
                logger.info("inputs",inputs)
            if update_inventory_toggle == "update" or update_inventory_toggle == "only_inventory":
                if inventories_payload['inventories']:
                    inventory_response = requests.post(
                        url="https://api.rms.rakuten.co.jp/es/2.0/inventories/bulk-upsert",
                        headers=headers,
                        json=inventories_payload,
                    )
                    logger.info("inventory_response",inventory_response.status_code)


        except Exception as e:
            logger.error(str(e))
            continue