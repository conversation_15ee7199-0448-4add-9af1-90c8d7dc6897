
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

<div class="mt-5" style="width: 100%;">
    
    <div class="mt-5 container-fluid px-0 scroll-y scoll-x">


        <div class="mb-10">
            <label class="d-flex align-items-center fs-3 fw-bolder me-3">
                <span class="">
                    {{setting_type|object_group_type:request}}
                    {% if LANGUAGE_CODE == 'ja' %}
                    オブジェクト
                    {% else %}
                    Object
                    {% endif %}
                </span>
            </label>

        </div>

        <div class="mb-0-10">
            <div id="change-custom-property-section" class="mb-10">

                <form method="POST" action="{% host_url 'event' host 'app' %}" enctype="multipart/form-data" onkeydown="return event.key != 'Enter';">
                    {% csrf_token %}
                    <input type="hidden" class="" name="app_settings" value="manage_app"/>

                    <div class="mb-10"
                        hx-get="{% host_url 'properties' host 'app' %}"
                        hx-vals='{"page_group_type": "session_event"}'
                        hx-trigger="load"
                        hx-target="#properties-table"
                    >
                        <div id="properties-table"></div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .stretch-select-custom span {
        height:100% !important;
    }
    .stretch-select-custom span span span span{
        {% comment %} display: flex !important; {% endcomment %}
        align-content: center;
        flex-wrap: wrap;
        margin-bottom: auto;
        margin-top: auto;
    }
</style>
<style>
    .loading-spinner{
        display:none;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>

<script>
    $(document).ready(function() {
        $('.select2-this').select2();
    });

    function delete_deals_custom_field_name(elm){
        elm.parentElement.parentElement.parentElement.parentElement.remove()
    }
</script>
