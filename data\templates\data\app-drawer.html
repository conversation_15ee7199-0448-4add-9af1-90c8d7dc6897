{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<div id="manage-full-wizard-template" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".manage-full-wizard-button,.manage_full_wizard_button" data-kt-drawer-close="#manage_full_wizard_close"
    data-kt-drawer-width="{'default':'100%','lg':'100%'}">

    <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>

        <div id="manage-full-drawer-content" class="w-100 h-100">
        </div>
    </div>

    <style>
        .loading-drawer-spinner{
            display:none;
        }
        #manage-full-drawer-content{
            display:inline-block;
        }
        .htmx-request #manage-full-drawer-content{
            display:none;
        }
        .htmx-request#manage-full-drawer-content{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>
</div>

<div id="create-new-wizard" class="bg-white h-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle="#create-new-wizard-button, .create_new_wizard_button" data-kt-drawer-close="#create-new-wizard-close"
    data-kt-drawer-width={'default':'90%','lg':'70%'}
    >
    <div class="card shadow-none rounded-0 h-100 w-100 border-0 bg-white">

        <div id="create-new-drawer-content" class="create_new_form h-100">
        </div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>

        <style>
            .loading-drawer-spinner{
                display:none;
            }
            .create_new_form{
                display:inline-block;
            }
            .htmx-request .create_new_form{
                display:none;
            }
            .htmx-request.create_new_form{
                display:none;
            }
            .htmx-request .loading-drawer-spinner{
                display:inline-block;
            }
            .htmx-request.loading-drawer-spinner{
                display:inline-block;
            }
        </style>
        <script>
            document.body.addEventListener("closeLearnDrawer", function(evt){
                const create_new_wizard = document.getElementById('create-new-wizard');
                drawer = KTDrawer.getInstance(create_new_wizard)
                drawer.hide()
            })
        </script>

    </div>
</div>

{% comment %} Settings {% endcomment %}

{% if object_type|in_list:"taskflow" %}
    <div id="spend_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".spend-manage-view-button" data-kt-drawer-close="#spend_wizard_close"
    data-kt-drawer-width={'default':'300px','md':'700px'} >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">

            <div id="spend_view_form" class="spend-form">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>

            <style>
                .loading-drawer-spinner{
                    display:none;
                }
                .spend-form{
                    display:inline-block;
                }
                .htmx-request .spend-form{
                    display:none;
                }
                .htmx-request.spend-form{
                    display:none;
                }
                .htmx-request .loading-drawer-spinner{
                    display:inline-block;
                }
                .htmx-request.loading-drawer-spinner{
                    display:inline-block;
                }
            </style>
            <script>
                document.body.addEventListener("closeLearnDrawer", function(evt){
                    const spend_wizard = document.getElementById('spend_wizard');
                    drawer = KTDrawer.getInstance(spend_wizard)
                    drawer.hide()
                })

                $(document).ready(function() {
                    var drawerEl = document.querySelector("#spend_wizard");
                    var drawer = KTDrawer.getInstance(drawerEl);
                    drawer.on("kt.drawer.hide", function() {
                        document.getElementById('spend_view_form').innerHTML = ''
                    });
                })
            </script>

        </div>
    </div>

    <div id="calendar_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#calendar_wizard_button" data-kt-drawer-close="#calendar_wizard_close"
        data-kt-drawer-width={'default':'300px','md':'600px'} >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
            <div id="calendar_form" class="calendar-form">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>


            <script>
                document.body.addEventListener("closeLearnDrawer", function(evt){
                    const calendar_wizard = document.getElementById('calendar_wizard');
                    drawer = KTDrawer.getInstance(calendar_wizard)
                    drawer.hide()
                })

            $(document).ready(function() {
                var calendarDrawerEl = document.querySelector("#calendar_wizard");
                var calendarDrawer = KTDrawer.getInstance(calendarDrawerEl);
                calendarDrawer.on("kt.drawer.hide", function() {
                    document.getElementById('calendar_form').innerHTML = ''
                });
            })
            </script>

        </div>
    </div>

    <style>
        .loading-drawer-spinner{
            display:none;
        }
        .calendar-form{
            display:inline-block;
        }
        .htmx-request .calendar-form{
            display:none;
        }
        .htmx-request.calendar-form{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
        .loading-drawer-spinner{
            display:none;
        }
        .expenses-form{
            display:inline-block;
        }
        .htmx-request .expenses-form{
            display:none;
        }
        .htmx-request.expenses-form{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>

{% elif object_type|in_list:"campaigns,forms" %}
    <div id="create-wizard-template" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".campaign-create-wizard-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="campaign-drawer-content" class="w-100">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <style>
            .loading-drawer-spinner{
                display:none;
            }
            #campaign-drawer-content{
                display:inline-block;
            }
            .htmx-request #campaign-drawer-content{
                display:none;
            }
            .htmx-request#campaign-drawer-content{
                display:none;
            }
            .htmx-request .loading-drawer-spinner{
                display:inline-block;
            }
            .htmx-request.loading-drawer-spinner{
                display:inline-block;
            }
        </style>
    </div>

    {% include 'data/partials/send-email-template-modal.html' %}
    <div
        class="bg-white h-100"
        data-kt-drawer="true"
        data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".content_review_button"
        data-kt-drawer-width={'default':'90%','lg':'90%'}
        style="overflow-x: hidden;"
        id="content_review_drawer_wizard"
        >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
            <div id="content_review_wizard" class="content_form">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>

            <style>
                .loading-drawer-spinner{
                    display:none;
                }
                .content_form{
                    display:inline-block;
                }
                .htmx-request .content_form{
                    display:none;
                }
                .htmx-request.content_form{
                    display:none;
                }
                .htmx-request .loading-drawer-spinner{
                    display:inline-block;
                }
                .htmx-request.loading-drawer-spinner{
                    display:inline-block;
                }
            </style>
        </div>
    </div>

    <div id="ajaxModalPopup"></div>

    <div id="create-content-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".create-content-wizard-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >
        <div id="manage-drawer-campaigns" class="w-100"></div>
    </div>

    <div id="create-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".create-wizard-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >
        <div id="manage-drawer-sns" class="w-100"></div>
    </div>

    <div id="campaign_code_snippet-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".campaign_code_snippet-wizard-button"
        data-kt-drawer-width="{'default':'90%','lg':'600px'}"
        >
        <div id="manage-drawer-campaign_code_snippet" class="w-100"></div>
    </div>

    {% comment %} view-settings {% endcomment %}
    <div id="create-view-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".create-view-settings-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >
        <div id="manage-form-share-drawer" class="w-100"></div>

    </div>

    <script>
    $(document).ready(function() {
        var drawerEl = document.querySelector("#create-view-settings-wizard");
        var drawer = KTDrawer.getInstance(drawerEl);
        drawer.on("kt.drawer.hide", function() {
            document.getElementById('manage-form-share-drawer').innerHTML = ''
        });

        var drawerEl = document.querySelector("#campaign_code_snippet-wizard");
        var drawer = KTDrawer.getInstance(drawerEl);
        drawer.on("kt.drawer.hide", function() {
            document.getElementById('manage-drawer-campaign_code_snippet').innerHTML = ''
        });

        var drawerEl = document.querySelector("#create-wizard");
        var drawer = KTDrawer.getInstance(drawerEl);
        drawer.on("kt.drawer.hide", function() {
            document.getElementById('manage-drawer-sns').innerHTML = ''
        });

        var drawerEl = document.querySelector("#create-content-wizard");
        var drawer = KTDrawer.getInstance(drawerEl);
        drawer.on("kt.drawer.hide", function() {
            document.getElementById('manage-drawer-campaigns').innerHTML = ''
        });

        var drawerEl = document.querySelector("#content_review_drawer_wizard");
        var drawer = KTDrawer.getInstance(drawerEl);
        drawer.on("kt.drawer.hide", function() {
            document.getElementById('content_review_wizard').innerHTML = ''
        });

        var drawerEl = document.querySelector("#create-wizard-template");
        var drawer = KTDrawer.getInstance(drawerEl);
        drawer.on("kt.drawer.hide", function() {
            document.getElementById('manage-drawer-template').innerHTML = ''
        });
    })
    </script>


{% elif object_type|in_list:"task,contacts,company,customer_case,conversation,user_management,timegenie,worker,worker_review,absence,jobs" %}

    {% comment %} Customer General {% endcomment %}
    <div id="customer-create-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".customer-create-wizard"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">
    <div id="customer-drawer" class="w-100"></div>
    </div>

    {% comment %} Show Profile {% endcomment %}
    <div id="customer-manage-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".customer-manage-wizard"
    data-kt-drawer-width="{'default':'100%','lg':'100%'}">
    <div id="customer-manage-drawer" class="w-100"></div>
    </div>

    {% comment %} view-settings {% endcomment %}
    <div id="create-view-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-view-settings-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div id="manage-view-settings-drawer" class="w-100"></div>
    </div>

    <div id="update-view-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".update-view-settings-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div id="manage-update-view-settings-drawer" class="w-100"></div>
    </div>

    <div id="download-view-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".download-view-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div id="download-view-drawer" class="w-100"></div>
    </div>


    <div id="conversation-sync-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".conversation-sync-wizard-button"
    data-kt-drawer-close="#kt_drawer_example_permanent_close"
    data-kt-drawer-width="{'default':'300px','md':'500px'}"
    >
        <div id="conversation-sync-drawer-content" class="w-100">
        </div>
    </div>


    <div id="profilewizard" class=""
        data-kt-drawer="true"
        data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#profile_wizard_button"
        data-kt-drawer-close="#profilewizard_close"
        data-kt-drawer-width={'default':'90%','md':'600px'}>

        <div class="card  border-0 shadow-none rounded-0 w-100 h-100 hover-scroll-overlay-y">
            <div class="card-header" id="kt_help_header">
                <h5 class="{% include "data/utility/card-header.html" %}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    プロフィール名
                    {% else %}
                    Profile
                    {% endif %}
                </h5>
                <div class="card-toolbar">
                    <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5"
                        data-kt-drawer-dismiss="true">
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                    transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                                    fill="black"></rect>
                            </svg>
                        </span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="manage-profile" class="mb-10">
                </div>
            </div>
        </div>
    </div>
    <div id="message_review_wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".message_review_button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">
    </div>

    <div id="message_thread_wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".message_thread_button"
        data-kt-drawer-width="{'default':'90%','lg':'90%'}"
        >
        <div id="message-detail" class='w-100' >
            <div class="h-100 py-5">
                <div class="card border mx-10 h-100">
                    <div class="card-body">
                        <h1>
                            {% if LANGUAGE_CODE == 'ja'%}
                            メッセージが選択されていません
                            {% else %}
                            No Message Selected
                            {% endif %}
                        </h1>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div id="manage-view-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".manage-view-settings-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
            <div id="manage-contacts-view-settings-drawer" class="w-100 view-form"></div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="position-fixed top-0 translate-middle-x start-50 p-3" style="z-index: 100; margin-top:75px">
        <div id="ongoing-call-toast" class="toast bg-white" data-bs-autohide="false" role="alert" aria-live="assertive" aria-atomic="true" data-kt-initialized="1">
            <div class="toast-header">
                <i class="ki-duotone ki-abstract-19 fs-2 text-danger me-3"><span class="path1"></span><span class="path2"></span></i>
                <strong class="me-auto">{% if LANGUAGE_CODE == 'ja' %}発信中の通話{% else %}Ongoing Call{% endif %}</strong>
            </div>
            <div class="toast-body rounded-bottom d-flex justify-content-between align-items-center fs-3 px-5">
                <div class="target-phone"></div>
                <div class="d-flex justify-content-center align-items-center">
                    <button class="call-wizard-button btn btn-primary rounded-circle w-50px h-50px d-flex justify-content-center align-items-center me-1 fs-4">
                        <span class="d-flex justify-content-center align-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-box-arrow-up-right" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-1 0V14.5a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h6.636a.5.5 0 0 0 .5-.5"/>
                                <path fill-rule="evenodd" d="M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0z"/>
                            </svg>
                        </span>
                    </button>
                    <button class="hangupButton btn btn-danger rounded-circle w-50px h-50px d-flex justify-content-center align-items-center" style="padding-right:15px"><i class="fa fa-phone fs-4"></i></button>
                </div>
            </div>
        </div>
    </div>

    {% comment %} INCOMING CALL TOAST {% endcomment %}
    <div class="position-fixed top-0 translate-middle-x start-50 p-3" style="z-index: 100; margin-top:75px">
        <div id="incoming-call-toast" class="toast bg-white" data-bs-autohide="false" role="alert" aria-live="assertive" aria-atomic="true" data-kt-initialized="1">
            <div class="toast-header">
                <i class="ki-duotone ki-abstract-19 fs-2 text-danger me-3"><span class="path1"></span><span class="path2"></span></i>
                <strong class="me-auto">{% if LANGUAGE_CODE == 'ja' %}電話の着信{% else %}Incoming Call{% endif %}</strong>
            </div>
            <div class="toast-body rounded-bottom d-flex justify-content-between align-items-center fs-3 px-5">
                <div>
                    <div class="from-text"></div>

                    <div id="incoming-call-stopwatch" class="d-none d-flex justify-content-center">
                        <span class="minutes fs-1">00</span>
                        <span class="fs-1">:</span>
                        <span class="seconds fs-1">00</span>
                        <span class="fs-1">:</span>
                        <span class="milliseconds fs-1">000</span>
                    </div>
                </div>

                <div class="d-flex justify-content-center align-items-center">
                    <button class="acceptIncomingCallButton btn btn-success me-3 rounded-circle w-50px h-50px d-flex justify-content-center align-items-center" style="padding-right:15px"><i class="fa fa-phone fs-4"></i></button>
                    <button id="muteIncomingCallButton" class="btn btn-bg-light btn-active-color-danger d-none me-3 rounded-circle w-50px h-50px d-flex justify-content-center align-items-center" style="padding-bottom:15px">
                        <span id="micEnableIncomingCallButton">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-mic-fill" viewBox="0 0 16 16">
                                <path d="M5 3a3 3 0 0 1 6 0v5a3 3 0 0 1-6 0z"/>
                                <path d="M3.5 6.5A.5.5 0 0 1 4 7v1a4 4 0 0 0 8 0V7a.5.5 0 0 1 1 0v1a5 5 0 0 1-4.5 4.975V15h3a.5.5 0 0 1 0 1h-7a.5.5 0 0 1 0-1h3v-2.025A5 5 0 0 1 3 8V7a.5.5 0 0 1 .5-.5"/>
                            </svg>
                        </span>
                        <span id="micDisableIncomingCallButton" class="d-none">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-mic-mute-fill" viewBox="0 0 16 16">
                                <path d="M13 8c0 .564-.094 1.107-.266 1.613l-.814-.814A4.02 4.02 0 0 0 12 8V7a.5.5 0 0 1 1 0zm-5 4c.818 0 1.578-.245 2.212-.667l.718.719a4.973 4.973 0 0 1-2.43.923V15h3a.5.5 0 0 1 0 1h-7a.5.5 0 0 1 0-1h3v-2.025A5 5 0 0 1 3 8V7a.5.5 0 0 1 1 0v1a4 4 0 0 0 4 4m3-9v4.879L5.158 2.037A3.001 3.001 0 0 1 11 3"/>
                                <path d="M9.486 10.607 5 6.12V8a3 3 0 0 0 4.486 2.607m-7.84-9.253 12 12 .708-.708-12-12-.708.708z"/>
                            </svg>
                        </span>
                    </button>
                    <button class="rejectIncomingCallButton btn btn-danger rounded-circle w-50px h-50px d-flex justify-content-center align-items-center" style="padding-right:15px"><i class="fa fa-phone fs-4"></i></button>
                </div>
            </div>
        </div>
    </div>

    <div id="create-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".create-message-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">
        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="inbox-drawer-content" class="w-100">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <style>
            .loading-drawer-spinner{
                display:none;
            }
            #inbox-drawer-content{
                display:inline-block;
            }
            .htmx-request #inbox-drawer-content{
                display:none;
            }
            .htmx-request#inbox-drawer-content{
                display:none;
            }
            .htmx-request .loading-drawer-spinner{
                display:inline-block;
            }
            .htmx-request.loading-drawer-spinner{
                display:inline-block;
            }
        </style>
    </div>

    <div id="create-wizard-template" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".template_review_wizard"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >
        <div id="template-drawer-content" class="w-100">
        </div>
    </div>

    <div id="call-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".call-wizard-button"
        data-kt-drawer-close="#kt_drawer_example_permanent_close"
        data-kt-drawer-width="{'default':'300px','md':'500px'}"
        >
        <div id="call-drawer-content" class="w-100">
            {% include 'data/inbox/call-form.html' %}
        </div>
    </div>

    <div id="bot-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".bot-wizard-button"
        data-kt-drawer-close="#kt_drawer_example_permanent_close"
        data-kt-drawer-width="{'default':'300px','md':'500px'}"
        >
        <div id="bot-drawer-content" class="w-100">
        </div>
    </div>

    {% comment %} Show Profile {% endcomment %}
    <div id="create-channel-channels" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-channel-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
        <div id="channels-drawer-content" class="w-100">
        </div>
    </div>


    <div id="user_management_wizard" class="bg-white h-100 w-100 tw-overflow-x-hidden border"  data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#user_management_wizard_button,.user_management_wizard_button" data-kt-drawer-close="#user_management_wizard_close"
        data-kt-drawer-width="{'default':'100%','lg':'100%'}">

        <div id="user_management_form" class='{% include "data/utility/drawer-header.html" %}'>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <button id="user_management_wizard_close" class='d-none'></button>
    </div>
    <style>
        .loading-drawer-spinner{
            display:none;
        }
        #user_management_form{
            display:inline-block;
        }
        .htmx-request #user_management_form{
            display:none;
        }
        .htmx-request#user_management_form{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>


    <div id="user_management-related-wizard-template" class="bg-white h-100 w-100 tw-overflow-x-hidden border" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".user_management-manage-related-wizard-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">

        <div class="card border-gray-300 shadow-none rounded-0 w-100 bg-white border-0">
            <div id="user_management-manage-related-drawer-content" class="w-100 border-0">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div id="user_management_wizard_lg" class="bg-white h-100 w-100 tw-overflow-x-hidden border" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#user_management_wizard_lg_button,.user_management_wizard_lg_button" data-kt-drawer-close="#user_management_wizard_lg_close"
        data-kt-drawer-width="{'default':'70%','lg':'70%'}">

        <div id="user_management_form_lg" class='card border-0 shadow-none rounded-0 w-100 bg-white'>
            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>


        <button id="user_management_wizard_lg_close" class='d-none'></button>
    </div>
    <style>
        .loading-drawer-spinner{
            display:none;
        }
        #user_management_form_lg{
            display:inline-block;
        }
        .htmx-request #user_management_form_lg{
            display:none;
        }
        .htmx-request#user_management_form_lg{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>


    <div id="accounting_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#accounting_wizard_button,.accounting_wizard_button" data-kt-drawer-close="#accounting_wizard_close"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="accounting_form" class='accounting_form'>
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        <button id="accounting_wizard_close" class='d-none'></button>
    </div>
    <style>
        .loading-drawer-spinner{
            display:none;
        }
        #accounting_form{
            display:inline-block;
        }
        .htmx-request #accounting_form{
            display:none;
        }
        .htmx-request#accounting_form{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>
    <div id="accounting_wizard_lg" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#accounting_wizard_lg_button,.accounting_wizard_lg_button" data-kt-drawer-close="#accounting_wizard_lg_close"
        data-kt-drawer-width="{'default':'100%','lg':'100%'}">

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="accounting_form_lg" class='accounting_form_lg'>
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        <button id="accounting_wizard_lg_close" class='d-none'></button>
    </div>
    <style>
        .loading-drawer-spinner{
            display:none;
        }
        #accounting_form_lg{
            display:inline-block;
        }
        .htmx-request #accounting_form_lg{
            display:none;
        }
        .htmx-request#accounting_form_lg{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>

    <div id="timegenie-wizard-template" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".timegenie_wizard_button"
        data-kt-drawer-width="{'default':'90%','lg':'60%'}">
        <div class="card border-0 rounded-0 w-100 bg-white">
            <div id="timegenie-drawer-content" class="w-100"></div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>     
            </div> 
        </div>
    </div>

    <div id="worker_wizard" class="bg-white h-100 w-100 tw-overflow-x-hidden border"  data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#worker_wizard_button,.worker_wizard_button" data-kt-drawer-close="#worker_wizard_close"
        data-kt-drawer-width="{'default':'90%','lg':'90%'}">

        <div id="worker_form" class='{% include "data/utility/drawer-header.html" %}'>
            
            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>     
            </div> 
        </div>

        <button id="worker_wizard_close" class='d-none'></button>
    </div>
    <style>
        .loading-drawer-spinner{
            display:none;
        }
        #worker_form{
            display:inline-block;
        }
        .htmx-request #worker_form{
            display:none;
        }
        .htmx-request#worker_form{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>
    

    <div id="worker-related-wizard-template" class="bg-white h-100 w-100 tw-overflow-x-hidden border" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".worker-manage-related-wizard-button"
        data-kt-drawer-width="{'default':'90%','lg':'60%'}">

        <div class="card border-gray-300 shadow-none rounded-0 w-100 bg-white border-0">
            <div id="worker-manage-related-drawer-content" class="w-100 border-0">
            </div>
            
            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>     
            </div> 
        </div> 

    </div>

    <div id="worker_wizard_lg" class="bg-white h-100 w-100 tw-overflow-x-hidden border" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#worker_wizard_lg_button,.worker_wizard_lg_button" data-kt-drawer-close="#worker_wizard_lg_close"
        data-kt-drawer-width="{'default':'60%','lg':'60%'}">

        <div id="worker_form_lg" class='card border-0 shadow-none rounded-0 w-100 bg-white'>
            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>     
            </div> 
        </div>
        
    
        <button id="worker_wizard_lg_close" class='d-none'></button>
    </div>
    <style>
        .loading-drawer-spinner{
            display:none;
        }
        #worker_form_lg{
            display:inline-block;
        }
        .htmx-request #worker_form_lg{
            display:none;
        }
        .htmx-request#worker_form_lg{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>

    <script>
    $(document).ready(function() {
        var drawerEl = document.querySelector("#create-view-settings-wizard");
        var drawer = KTDrawer.getInstance(drawerEl);
        var customerCreateWizard = document.querySelector("#customer-create-wizard");
        var customerManageWizard = document.querySelector("#customer-manage-wizard");
        var updateViewSettingsWizard = document.querySelector("#update-view-settings-wizard");
        var downloadViewWizard = document.querySelector("#download-view-wizard");
        var conversationSyncWizard = document.querySelector("#conversation-sync-wizard");
        var profileWizard = document.querySelector("#profilewizard");
        var messageReviewWizard = document.querySelector("#message_review_wizard");
        var messageThreadWizard = document.querySelector("#message_thread_wizard");
        var createWizard = document.querySelector("#create-wizard");
        var createWizardTemplate = document.querySelector("#create-wizard-template");
        var callWizard = document.querySelector("#call-wizard");
        var botWizard = document.querySelector("#bot-wizard");
        var createChannelChannels = document.querySelector("#create-channel-channels");
        var timegenieWizardTemplate = document.querySelector("#timegenie-wizard-template");
        var user_managementWizard = document.querySelector("#user_management_wizard");
        var user_managementRelatedWizardTemplate = document.querySelector("#user_management-related-wizard-template");
        var user_managementWizardLg = document.querySelector("#user_management_wizard_lg");
        var accountingWizard = document.querySelector("#accounting_wizard");
        var accountingWizardLg = document.querySelector("#accounting_wizard_lg");
        var timegenieWizardTemplate = document.querySelector("#timegenie-wizard-template");
        var workerWizard = document.querySelector("#worker_wizard");
        var workerRelatedWizardTemplate = document.querySelector("#worker-related-wizard-template");
        var workerWizardLg = document.querySelector("#worker_wizard_lg");

        var customerCreateWizardDrawer = KTDrawer.getInstance(customerCreateWizard);
        var customerManageWizardDrawer = KTDrawer.getInstance(customerManageWizard);
        var updateViewSettingsWizardDrawer = KTDrawer.getInstance(updateViewSettingsWizard);
        var downloadViewWizardDrawer = KTDrawer.getInstance(downloadViewWizard);
        var conversationSyncWizardDrawer = KTDrawer.getInstance(conversationSyncWizard);
        var profileWizardDrawer = KTDrawer.getInstance(profileWizard);
        var messageReviewWizardDrawer = KTDrawer.getInstance(messageReviewWizard);
        var messageThreadWizardDrawer = KTDrawer.getInstance(messageThreadWizard);
        var createWizardDrawer = KTDrawer.getInstance(createWizard);
        var createWizardTemplateDrawer = KTDrawer.getInstance(createWizardTemplate);
        var callWizardDrawer = KTDrawer.getInstance(callWizard);
        var botWizardDrawer = KTDrawer.getInstance(botWizard);
        var createChannelChannelsDrawer = KTDrawer.getInstance(createChannelChannels);
        var timegenieWizardTemplateDrawer = KTDrawer.getInstance(timegenieWizardTemplate);
        var user_managementWizardDrawer = KTDrawer.getInstance(user_managementWizard);
        var user_managementRelatedWizardTemplateDrawer = KTDrawer.getInstance(user_managementRelatedWizardTemplate);
        var user_managementWizardLgDrawer = KTDrawer.getInstance(user_managementWizardLg);
        var accountingWizardDrawer = KTDrawer.getInstance(accountingWizard);
        var accountingWizardLgDrawer = KTDrawer.getInstance(accountingWizardLg);
        var timegenieWizardTemplateDrawer = KTDrawer.getInstance(timegenieWizardTemplate);
        var workerWizardDrawer = KTDrawer.getInstance(workerWizard);
        var workerRelatedWizardTemplateDrawer = KTDrawer.getInstance(workerRelatedWizardTemplate);
        var workerWizardLgDrawer = KTDrawer.getInstance(workerWizardLg);

        customerCreateWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('customer-drawer').innerHTML = ''
        });
        customerManageWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('customer-manage-drawer').innerHTML = ''
        });
        updateViewSettingsWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('manage-update-view-settings-drawer').innerHTML = ''
        });
        downloadViewWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('download-view-drawer').innerHTML = ''
        });
        conversationSyncWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('conversation-sync-drawer-content').innerHTML = ''
        });
        profileWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('manage-profile').innerHTML = ''
        });
        messageThreadWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('message-detail').innerHTML = ''
        });
        createWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('inbox-drawer-content').innerHTML = ''
        });
        createWizardTemplateDrawer.on("kt.drawer.hide", function() {
            document.getElementById('template-drawer-content').innerHTML = ''
        });
        callWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('call-drawer-content').innerHTML = ''
        });
        botWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('bot-drawer-content').innerHTML = ''
        });
        createChannelChannelsDrawer.on("kt.drawer.hide", function() {
            document.getElementById('channels-drawer-content').innerHTML = ''
        });
        timegenieWizardTemplateDrawer.on("kt.drawer.hide", function() {
            // No specific content ID provided
        });
        user_managementWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('user_management_form').innerHTML = ''
        });
        user_managementRelatedWizardTemplateDrawer.on("kt.drawer.hide", function() {
            document.getElementById('user_management-manage-related-drawer-content').innerHTML = ''
        });
        user_managementWizardLgDrawer.on("kt.drawer.hide", function() {
            document.getElementById('user_management_form_lg').innerHTML = ''
        });
        accountingWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('accounting_form').innerHTML = ''
        });
        accountingWizardLgDrawer.on("kt.drawer.hide", function() {
            document.getElementById('accounting_form_lg').innerHTML = ''
        });
        timegenieWizardTemplateDrawer.on("kt.drawer.hide", function() {
            // No specific content ID provided
        });
        workerWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('worker_form').innerHTML = ''
        });
        workerRelatedWizardTemplateDrawer.on("kt.drawer.hide", function() {
            document.getElementById('worker-manage-related-drawer-content').innerHTML = ''
        });
        workerWizardLgDrawer.on("kt.drawer.hide", function() {
            document.getElementById('worker_form_lg').innerHTML = ''
        });
    })
    </script>


{% elif object_type|in_list:"reports,panels,dashboards,task,calendar" %}
    <div id="create-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-wizard-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">
    {% include 'data/partials/create-report-drawer.html' %}
    </div>

    <div id="panel-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-panel-button, .edit-panel-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">
        <!-- Loading indicator for panel wizard -->
        <div id="panel-wizard-loading" class="d-flex justify-content-center align-items-center h-100">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <span class="ms-2">
                {% if LANGUAGE_CODE == 'ja' %}
                    レポートを読み込んでいます...
                {% else %}
                    Loading Report...
                {% endif %}
            </span>
        </div>
    </div>

    <div id="event-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-event-button, .edit-event-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">
    </div>

    <div id="report-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-report-button, .view-report-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">
    </div>

    <div id="create-manage-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle="#create-wizard-manage-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">

        <div id='report-manage' class='w-100'></div>

    </div>

    <div id="profilewizard" class=""
        data-kt-drawer="true"
        data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#profile_wizard_button"
        data-kt-drawer-close="#profilewizard_close"
        data-kt-drawer-width={'default':'90%','md':'600px'}>
        <div class="card border-0 shadow-none rounded-0 w-100 h-100 hover-scroll-overlay-y">
            <div class="card-header" id="kt_help_header">
                <h5 class="{% include "data/utility/card-header.html" %}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    プロフィール
                    {% else %}
                    Profile
                    {% endif %}

                </h5>
                <div class="card-toolbar">
                    <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5"
                        data-kt-drawer-dismiss="true">
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                    transform="rotate(-45 6 17.3137)" fill="black"></rect>
                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                                    fill="black"></rect>
                            </svg>
                        </span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="manage-profile" class="mb-10">
                </div>
            </div>
        </div>
    </div>

    <div id="calendar_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle="#calendar_wizard_button" data-kt-drawer-close="#calendar_wizard_close"
    data-kt-drawer-width={'default':'300px','md':'600px'} >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
            <div id="calendar_form" class="calendar-form">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>


            <script>
                document.body.addEventListener("closeLearnDrawer", function(evt){
                    const calendar_wizard = document.getElementById('calendar_wizard');
                    drawer = KTDrawer.getInstance(calendar_wizard)
                    drawer.hide()
                })
            </script>

        </div>
    </div>

    <script>
    $(document).ready(function() {
        var panelWizard = document.getElementById("panel-wizard");
        var eventWizard = document.getElementById("event-wizard");
        var reportWizard = document.getElementById("report-wizard");
        var createManageWizard = document.getElementById("create-manage-wizard");
        var profileWizard = document.getElementById("profilewizard");
        var calendarWizard = document.getElementById("calendar_wizard");

        var panelWizardDrawer = KTDrawer.getInstance(panelWizard);
        var eventWizardDrawer = KTDrawer.getInstance(eventWizard);
        var reportWizardDrawer = KTDrawer.getInstance(reportWizard);
        var createManageWizardDrawer = KTDrawer.getInstance(createManageWizard);
        var profileWizardDrawer = KTDrawer.getInstance(profileWizard);
        var calendarWizardDrawer = KTDrawer.getInstance(calendarWizard);

        panelWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('panel-wizard').innerHTML = `
                <!-- Loading indicator for panel wizard -->
                <div id="panel-wizard-loading" class="d-flex justify-content-center align-items-center h-100">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">
                        {% if LANGUAGE_CODE == 'ja' %}
                            レポートを読み込んでいます...
                        {% else %}
                            Loading Report...
                        {% endif %}
                    </span>
                </div>
            `;
        });
        eventWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('event-wizard').innerHTML = ''
        });
        reportWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('report-wizard').innerHTML = ''
        });
        createManageWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('report-manage').innerHTML = ''
        });
        profileWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('profilewizard').innerHTML = ''
        });
        calendarWizardDrawer.on("kt.drawer.hide", function() {
            document.getElementById('calendar_form').innerHTML = ''
        });
    });
    </script>


{% elif object_type|in_list:"shopturbo,commerce_items,commerce_orders,commerce_subscription,commerce_inventory,commerce_inventory_warehouse,commerce_inventory_transaction,expense,bill,delivery_slips,receipts,estimates,invoices,purchaseorder,slips,journal,commerce_meter" %}

    {% comment %} view-settings {% endcomment %}
    <div id="create-view-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".create-view-settings-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
            <div id="manage-contacts-view-settings-drawer" class="w-100 view-form"></div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="calendar_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle="#calendar_wizard_button" data-kt-drawer-close="#calendar_wizard_close"
    data-kt-drawer-width={'default':'300px','md':'600px'} >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
            <div id="calendar_form" class="calendar-form">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>


            <script>
                document.body.addEventListener("closeLearnDrawer", function(evt){
                    const calendar_wizard = document.getElementById('calendar_wizard');
                    drawer = KTDrawer.getInstance(calendar_wizard)
                    drawer.hide()
                })
            </script>

        </div>
    </div>
    <div id="spend_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".spend-manage-view-button" data-kt-drawer-close="#spend_wizard_close"
    data-kt-drawer-width={'default':'300px','md':'700px'} >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">

            <div id="spend_view_form" class="spend-form">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>

            <style>
                .loading-drawer-spinner{
                    display:none;
                }
                .spend-form{
                    display:inline-block;
                }
                .htmx-request .spend-form{
                    display:none;
                }
                .htmx-request.spend-form{
                    display:none;
                }
                .view-form{
                    display:inline-block;
                }
                .htmx-request .view-form{
                    display:none;
                }
                .htmx-request.view-form{
                    display:none;
                }
                .htmx-request .loading-drawer-spinner{
                    display:inline-block;
                }
                .htmx-request.loading-drawer-spinner{
                    display:inline-block;
                }
            </style>
            <script>
                document.body.addEventListener("closeLearnDrawer", function(evt){
                    const spend_wizard = document.getElementById('spend_wizard');
                    drawer = KTDrawer.getInstance(spend_wizard)
                    drawer.hide()
                })
            </script>

        </div>
    </div>

    <div id="payment_wizard" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#payment_wizard_button,.payment_wizard_button" data-kt-drawer-close="#payment_wizard_close"
        {% if payment %}
        data-kt-drawer-width="{'default':'90%','lg':'90%'}"
        {% else %}
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        {% endif %}
        >

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="expenses_form" class='expenses-form'>
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        <button id="payment_wizard_close" class='d-none'></button>
    </div>

    <div id="payment_wizard_lg" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#payment_wizard_button_lg,.payment_wizard_button_lg" data-kt-drawer-close="#payment_wizard_lg_close"
        data-kt-drawer-width="{'default':'100%','lg':'100%'}">

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="expenses_form_lg" class='expenses-form'>
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        <button id="payment_wizard_lg_close" class='d-none'></button>
    </div>



    <div id="procurement_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#procurement_wizard_button, .class-procurement_wizard_button" data-kt-drawer-close="#procurement_wizard_close"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">

            <div id="procurement_form" class='procurement-form target-procurement_form'>

            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        <button id="procurement_wizard_close" class='d-none'></button>
    </div>

    <div id="billings_wizard" class="bg-white h-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#billings_wizard_button, .billings_wizard_button" data-kt-drawer-close="#billings_wizard_close"
        data-kt-drawer-width={'default':'90%','lg':'70%'}
        >
        <div class="card shadow-none rounded-0 h-100 w-100 border-0 bg-white">

            <div id="billings_form" class="billings-form h-100">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>

            <style>
                .loading-drawer-spinner{
                    display:none;
                }
                .billings-form{
                    display:inline-block;
                }
                .procurement-form{
                    display:inline-block;
                }
                .htmx-request .billings-form{
                    display:none;
                }
                .htmx-request.billings-form{
                    display:none;
                }
                .expenses-form{
                    display:inline-block;
                }
                .htmx-request .expenses-form{
                    display:none;
                }
                .htmx-request.expenses-form{
                    display:none;
                }
                .htmx-request .procurement-form{
                    display:none;
                }
                .htmx-request.procurement-form{
                    display:none;
                }
                .htmx-request .loading-drawer-spinner{
                    display:inline-block;
                }
                .htmx-request.loading-drawer-spinner{
                    display:inline-block;
                }
            </style>
            <script>
                document.body.addEventListener("closeLearnDrawer", function(evt){
                    const billings_wizard = document.getElementById('billings_wizard');
                    drawer = KTDrawer.getInstance(billings_wizard)
                    drawer.hide()
                })
            </script>

        </div>
    </div>

    <div id="billings_wizard_large" class="bg-white h-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#billings_wizard_large_button" data-kt-drawer-close="#billings_wizard_large_close"
        data-kt-drawer-width={'default':'100%','lg':'100%'}
        >
        <div class="card shadow-none rounded-0 h-100 w-100 border-0 bg-white">

            <div id="billings_form_large" class="billings-form h-100">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>

            <style>
                .loading-drawer-spinner{
                    display:none;
                }
                .billings-form{
                    display:inline-block;
                }
                .procurement-form{
                    display:inline-block;
                }
                .htmx-request .billings-form{
                    display:none;
                }
                .htmx-request.billings-form{
                    display:none;
                }
                .expenses-form{
                    display:inline-block;
                }
                .htmx-request .expenses-form{
                    display:none;
                }
                .htmx-request.expenses-form{
                    display:none;
                }
                .htmx-request .procurement-form{
                    display:none;
                }
                .htmx-request.procurement-form{
                    display:none;
                }
                .htmx-request .loading-drawer-spinner{
                    display:inline-block;
                }
                .htmx-request.loading-drawer-spinner{
                    display:inline-block;
                }
            </style>
            <script>
                document.body.addEventListener("closeLearnDrawer", function(evt){
                    const billings_wizard = document.getElementById('billings_wizard');
                    drawer = KTDrawer.getInstance(billings_wizard)
                    drawer.hide()
                })
            </script>

        </div>
    </div>

    <div id="object-action-drawer" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".object-action-drawer-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="object-action-drawer-content" class="w-100">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <style>
            #object-action-drawer-content{
                display:inline-block;
            }
            .htmx-request #object-action-drawer-content{
                display:none;
            }
            .htmx-request#object-action-drawer-content{
                display:none;
            }
        </style>
        <script>
            $(document).ready(function() {
                var drawerEl = document.querySelector("#object-action-drawer");
                var drawer = KTDrawer.getInstance(drawerEl);
                drawer.on("kt.drawer.hide", function() {
                    document.getElementById('shopturbo-drawer-content').innerHTML = ''
                });
            })
        </script>
    </div>

    <div id="manage-wizard-template" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".shopturbo-manage-wizard-button"
        data-kt-drawer-width="{'default':'100%','lg':'100%'}">

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="shopturbo-drawer-content" class="w-100 h-100">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <style>
            #shopturbo-drawer-content{
                display:inline-block;
            }
            .htmx-request #shopturbo-drawer-content{
                display:none;
            }
            .htmx-request#shopturbo-drawer-content{
                display:none;
            }
        </style>
        <script>
            $(document).ready(function() {
                var drawerEl = document.querySelector("#manage-wizard-template");
                var drawer = KTDrawer.getInstance(drawerEl);
                drawer.on("kt.drawer.hide", function() {
                    document.getElementById('shopturbo-drawer-content').innerHTML = ''
                });
            })
        </script>
    </div>

    <div id="create-wizard-template" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".shopturbo-create-wizard-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="shopturbo-create-drawer-content" class="w-100">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div id="create-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".create-settings-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">
        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="manage-settings-drawer" class="w-100"></div>
            <div class="d-flex justify-content-center">
                <div class="loading-setting-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        <style>
            .loading-setting-drawer-spinner{
                display:none;
            }
            #manage-settings-drawer{
                display:inline-block;
            }
            .htmx-request #manage-settings-drawer{
                display:none;
            }
            .htmx-request#manage-settings-drawer{
                display:none;
            }
            .htmx-request .loading-setting-drawer-spinner{
                display:inline-block;
            }
            .htmx-request.loading-setting-drawer-spinner{
                display:inline-block;
            }
        </style>
    </div>

    <div id="billing-related-wizard-template" class="bg-white h-100 w-100 tw-overflow-x-hidden border" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".billing-manage-related-wizard-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}">

        <div class="card border-gray-300 shadow-none rounded-0 w-100 bg-white border-0">
            <div id="billing-manage-related-drawer-content" class="w-100 border-0">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div id="manage-full-wizard-template-being-used" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".manage_full_wizard_button-being-used"
        data-kt-drawer-width="{'default':'90%','lg':'90%'}">

        <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>

            <div id="manage-full-drawer-content-being-used" class="w-100 h-100">
            </div>
        </div>
    </div>


    <script>
        $(document).ready(function() {
            var wizardEl = document.querySelector("#create-view-settings-wizard");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('manage-contacts-view-settings-drawer').innerHTML = ''
            });

            var wizardEl = document.querySelector("#calendar_wizard");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('calendar_form').innerHTML = ''
            });

            var wizardEl = document.querySelector("#spend_wizard");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('spend_view_form').innerHTML = ''
            });

            var wizardEl = document.querySelector("#payment_wizard");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('expenses_form').innerHTML = ''
            });

            var wizardEl = document.querySelector("#payment_wizard_lg");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('expenses_form_lg').innerHTML = ''
            });

            var wizardEl = document.querySelector("#procurement_wizard");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('procurement_form').innerHTML = ''
            });

            var wizardEl = document.querySelector("#billings_wizard");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('billings_form').innerHTML = ''
            });

            var wizardEl = document.querySelector("#billings_wizard_large");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('billings_form_large').innerHTML = ''
            });

            var wizardEl = document.querySelector("#object-action-drawer");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('object-action-drawer-content').innerHTML = ''
            });

            var wizardEl = document.querySelector("#manage-wizard-template");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('shopturbo-drawer-content').innerHTML = ''
            });

            var wizardEl = document.querySelector("#create-wizard-template");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('shopturbo-create-drawer-content').innerHTML = ''
            });

            var wizardEl = document.querySelector("#billing-related-wizard-template");
            var wizard = KTDrawer.getInstance(wizardEl);
            wizard.on("kt.drawer.hide", function() {
                document.getElementById('billing-manage-related-drawer-content').innerHTML = ''
            });
        });
    </script>


{% elif object_type|in_list:"contract" %}

    <div id="document_template_wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#doc_template_wizard_button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >
    </div>

    <div id="signature_wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle="#signature_wizard_button"
        data-kt-drawer-width="{'default':'90','md':'700px'}"
        >
    </div>

    <div id="create-view-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".create-view-settings-wizard,.manage-view-settings-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >
        <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
            <div id="manage-contacts-view-settings-drawer" class="w-100 view-form"></div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        $(document).ready(function() {
            var wizards = [
                { id: "document_template_wizard", contentId: null },
                { id: "signature_wizard", contentId: null },
                { id: "create-view-settings-wizard", contentId: "manage-contacts-view-settings-drawer" },
            ];

            wizards.forEach(function(wizard) {
                var wizardEl = document.getElementById(wizard.id);
                var wizardInstance = KTDrawer.getInstance(wizardEl);
                wizardInstance.on("kt.drawer.hide", function() {
                    if (wizard.contentId) {
                        document.getElementById(wizard.contentId).innerHTML = '';
                    }
                });
            });
        });
    </script>

{% endif %}

<div
    id="filter-drawer"
    class="bg-white h-100 border-0"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".filter-drawer-btn"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
        <div id="filter-drawer-content" class="card w-100 border-0 bg-white">
        </div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        let isDrawerLoaded = false;

        const buttons = document.querySelectorAll('.filter-drawer-btn');

        buttons.forEach(btn => {
            btn.addEventListener("click", function (e) {
            if (isDrawerLoaded) {
                // Prevent HTMX from triggering hx-get again
                e.stopImmediatePropagation();
                const viewDrawer = document.getElementById('filter-drawer');
                drawer = KTDrawer.getInstance(viewDrawer);
                drawer.show();
            } else {
                // Wait for the content to be loaded via HTMX
                document.body.addEventListener("htmx:afterSwap", function handleSwap(evt) {
                if (evt.detail.target.id === "filter-drawer-content") {
                    isDrawerLoaded = true;
                    // Remove HTMX attributes to fully prevent re-trigger
                    
                    {% comment %} This Code making issue for search scope, comment this out for now to fix it {% endcomment %}
                    {% comment %} 
                        btn.removeAttribute("hx-get");
                        btn.removeAttribute("hx-vals");
                        btn.removeAttribute("hx-target");
                        btn.removeAttribute("hx-swap");
                        btn.removeAttribute("hx-indicator");
                        btn.removeAttribute("hx-trigger");

                        htmx.process(btn); 
                    {% endcomment %}

                    // Stop listening
                    document.body.removeEventListener("htmx:afterSwap", handleSwap);
                }
                });
            }
            });
        });
    });
</script>

<div
    id="custom-object-drawer"
    class="bg-white h-100 border-0"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".custom-object-btn"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div id="custom-object-content" class="card w-100 border-0 bg-white"></div>
</div>

<div id="create-custom-object-view-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".custom-object-view-wizard-btn"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div class="card shadow-none rounded-0 w-100 border-0 bg-white">
        <div id="custom-object-view-wizard-drawer" class="w-100 view-form"></div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="create-custom-object-wizard" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".custom-object-create-wizard-btn"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">

    <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
        <div id="custom-object-create-wizard-drawer" class="w-100">
        </div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="manage-custom-object-wizard" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".custom-object-manage-wizard-btn"
    data-kt-drawer-width="{'default':'90%','lg':'90%'}">

    <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
        <div id="custom-object-manage-wizard-drawer" class="w-100">
        </div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="create-new-property-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-new-property"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">
    <div id="create-new-property-content" class="w-100"></div>
</div>

<div id="create-property-table-mapping" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-property-table-mapping-wizard"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">
    <div id="create-property-table-mapping-content" class="w-100"></div>
</div>

<div id="message-template-drawer" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".message-template-drawer"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">
    <div id="message-template-content" class="w-100"></div>
</div>

<div id="price-table-drawer" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".price-table-drawer"
    data-kt-drawer-width="{'default':'90%','lg':'90%'}"
    >
    <div id="price-table-content" class="w-100"></div>
</div>
{% comment %} Set Taskflow Drawers as global {% endcomment %}
<div
    id="workflow-wizard"
    class="bg-white h-100"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".action-btn"
    data-kt-drawer-close=".workflow-wizard-close"
    data-kt-drawer-width="{'default':'90%','lg':'600px'}"
    >
    <span id="spinner" class="spinner-border spinner-border-sm text-secondary" style="width: 150px; height: 150px; margin-left: auto; margin-right: auto; margin-top: auto; margin-bottom: auto;" role="status">
        <span class="visually-hidden">Loading...</span>
    </span>
</div>

<script>
    document.body.addEventListener("closeTemplateDrawer", function(evt){
        const viewDrawer = document.getElementById('workflow-wizard');
        drawer = KTDrawer.getInstance(viewDrawer)
        drawer.hide()
    })
</script>

<div
    id="workflow_result_wizard"
    class="bg-white h-100"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".workflow_result_button"
    data-kt-drawer-close="#btn-dismiss"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div id="workflow-result-content" class="w-100"></div>
</div>

<div
    id="workflow_create_wizard"
    class="bg-white h-100 border-0"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".workflow-wizard-button"
    data-kt-drawer-close="#automation_wizard_close"
    data-kt-drawer-width={'default':'90%','lg':'70%'}
    >


    <div id="workflow_edit_content" class="w-100">

    </div>
    <script>
        $(document).ready(function() {
            // Not sure what drawerEl is, but its could broke the home-template.html if this not handled
            // ---
            try {
                KTDrawer.createInstances();
                document.body.addEventListener("closeWorkflowDrawer", function(evt){
                    const viewDrawer = document.getElementById('workflow_create_wizard');
                    drawer = KTDrawer.getInstance(viewDrawer)
                    drawer.hide()
                })
            } catch (error) {
                console.log("error", error)
            }
        })
    </script>
</div>

<div id="task_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".task-wizard-button" data-kt-drawer-close="#task_wizard_close"
    data-kt-drawer-width={'default':'90%','lg':'70%'} >
    <div class="card shadow-none rounded-0 w-100 border-0 bg-white">

        <div id="task-drawer" class="task-drawer">


        </div>

        <script>
            document.body.addEventListener("closeTaskDrawer", function(evt){
                const taskDrawer = document.getElementById('task_wizard');
                drawer = KTDrawer.getInstance(taskDrawer)
                drawer.hide()
            })
        </script>
    </div>
</div>

<div id="task_workflow_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-task-action-wizard-button" data-kt-drawer-close="#task_workflow_wizard_close"
    data-kt-drawer-width={'default':'300px','md':'600px'} >
    <div class="card shadow-none rounded-0 w-100 border-0 bg-white">

        <div id="task_action_wizard_content">

        </div>

        <script>
            document.body.addEventListener("closeTaskActionDrawer", function(evt){
                const taskDrawer = document.getElementById('task_workflow_wizard');
                drawer = KTDrawer.getInstance(taskDrawer)
                drawer.hide()
            })
        </script>

    </div>
</div>

<div id="project_view_wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".view-wizard-button" data-kt-drawer-close="#project_view_wizard_close"
    data-kt-drawer-width={'default':'90%','md':'70%'} >
    <div class="card shadow-none rounded-0 w-100 border-0 bg-white">

        <div id="view-drawer" class="h-100">

        </div>

        <script>
            document.body.addEventListener("closeViewDrawer", function(evt){
                const viewDrawer = document.getElementById('project_view_wizard');
                drawer = KTDrawer.getInstance(viewDrawer)
                drawer.hide()
            })
        </script>
    </div>
</div>


<script>

    function download(content, mimeType, filename){
        const a = document.createElement('a') // Create "a" element
        const blob = new Blob([content], {type: mimeType}) // Create a blob (file-like object)
        const url = URL.createObjectURL(blob) // Create an object URL from blob
        a.setAttribute('href', url) // Set "a" element link
        a.setAttribute('download', filename) // Set download filename
        a.click() // Start downloading
    }
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        a = evt.detail.xhr
        if(evt.detail.xhr.status == 200){
            if (evt.detail.xhr.getAllResponseHeaders().includes('content-disposition')) {
                if (evt.detail.xhr.getResponseHeader('content-disposition').includes('attachment;')){
                    evt.detail.shouldSwap = false;
                    var filename = evt.detail.xhr.getResponseHeader('content-disposition').split('"')[1]
                    var mime = evt.detail.xhr.getResponseHeader('content-type')
                    download(evt.detail.xhr.responseText.replace(/(\r\n|\n|\r)/gm,'\n'), mime, filename)
                    window.location.reload()
                }
            }
        }
    });

    function showWorkflowOnTaskDrawer() {
        console.log('Show Workflow Form')

        {% comment %} Widen the drawer {% endcomment %}
        const taskWizard = document.getElementById('task_wizard')
        taskWizard.setAttribute('data-kt-drawer-width', "{'default':'90%','lg':'70%'}")
        const drawer = KTDrawer.getInstance(taskWizard)
        drawer.update()

        const workflowForm = document.getElementById('workflow-form')
        workflowForm.classList.remove('d-none')

        {% comment %} Reduce the task form width, 50-50 with workflow form {% endcomment %}
        task_wizard.getElementsByClassName('card-body')[0].firstElementChild.classList.add('w-50')
        task_wizard.getElementsByClassName('card-body')[0].firstElementChild.classList.remove('w-100')

        if (task_wizard.getElementsByClassName('card-toolbar')){
            task_wizard.getElementsByClassName('card-toolbar')[0].innerHTML = ''
        }

        drawer.on("kt.drawer.hide", function() {
            setTimeout(function(){
                taskWizard.setAttribute('data-kt-drawer-width', "{'default':'90%','lg':'70%'}")
                drawer.update()
            }, 1500)
        });
    }
</script>


<div id="formroom_view_wizard" class="bg-white h-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".formroom_wizard_button" data-kt-drawer-close="#formroom_wizard_close"
    data-kt-drawer-width={'default':'90%','md':'70%'} >
    <div class="card shadow-none rounded-0 h-100 w-100 border-0 bg-white">

        <div id="formroom_form" class="formroom-form h-100">
        </div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>

        <style>
            .loading-drawer-spinner{
                display:none;
            }
            .formroom-form{
                display:inline-block;
            }
            .htmx-request .formroom-form{
                display:none;
            }
            .htmx-request.formroom-form{
                display:none;
            }
            .htmx-request .loading-drawer-spinner{
                display:inline-block;
            }
            .htmx-request.loading-drawer-spinner{
                display:inline-block;
            }
        </style>
    </div>
</div>

<div
    id="chat-drawer"
    class="bg-white h-100 border-0"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".chat-drawer-btn"
    data-kt-drawer-close="#chat-drawer-close-btn"
    data-kt-drawer-width={'default':'90%','lg':'70%'}
    >
    <div id="chat-drawer-content" class="w-100"></div>
</div>

<div
    id="san-ai-drawer"
    class="bg-white h-100 border-0"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".san-ai-drawer-btn"
    data-kt-drawer-close=".san-ai-drawer-close-btn"
    data-kt-drawer-width={'default':'100%','lg':'100%'}
    >
    <div id="san-ai-drawer-content" class="w-100"></div>
    <div class="d-flex justify-content-center">
        {% include "data/san/san_view.html" %}
    </div> 
</div>

<div
    id="demo-bot-drawer"
    class="bg-white h-100 border-0"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".demo-bot-drawer-btn"
    data-kt-drawer-close=".demo-bot-drawer-close-btn"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div id="demo-bot-drawer-content" class="card w-100 border-0 bg-white">
    </div>
</div>

<div
    id="module-templates-drawer"
    class="bg-white h-100 border-0"
    data-kt-drawer="true"
    data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".module-templates-btn"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}"
    >
    <div id="module-templates-content" class="card w-100 border-0 bg-white"></div>
</div>

<div id="manage-related-wizard-template" class="bg-white h-100 w-100 tw-overflow-x-hidden border" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".manage-related-wizard-button"
    data-kt-drawer-width="{'default':'100%','lg':'70%'}">

    <div class="card border-gray-300 shadow-none rounded-0 w-100 bg-white border-0">
        <div id="manage-related-drawer-content" class="w-100 border-0">
        </div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <style>
        .loading-drawer-spinner{
            display:none;
        }
        .htmx-request .loading-drawer-spinner{
            display:inline-block;
        }
        .htmx-request.loading-drawer-spinner{
            display:inline-block;
        }
    </style>
</div>


<div id="create-association-wizard" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".create-association-wizard-button" data-kt-drawer-close=".create-association-wizard-button-close"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">

    <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
        <div id="create-association-wizard-content" class="w-100">
        </div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>

</div>

<div id="manage-price-template" class="bg-white h-100 w-100 tw-overflow-x-hidden" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".manage-price-wizard-button"
    data-kt-drawer-width="{'default':'90%','lg':'70%'}">

    <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
        <div id="manage-price-content" class="w-100">
        </div>

        <div class="d-flex justify-content-center">
            <div class="loading-drawer-spinner mt-10 mb-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
    <style>  
        .loading-drawer-spinner {  
            display: none;  
        }  
        #manage-price-content {  
            display: inline-block;  
        }  
        .htmx-request #manage-price-content {  
            display: none;  
        }  
        .htmx-request .loading-drawer-spinner {  
            display: inline-block;  
        }  
    </style>  
</div>

<div id="workspace-log-download-wizard" class="bg-white" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".workspace-log-download-button" data-kt-drawer-close="#workspace_log_download_close"
    data-kt-drawer-width="{'default':'90%','md':'500px'}">
    <div id="workspace-log-download-content" class="w-100">
        {% include 'data/account/workspace/settings/workspace_log_download_drawer.html' %}
    </div>
</div>

<script>
    $(document).ready(function() {
        var wizards = [
            { id: "manage-full-wizard-template", contentId: "manage-full-drawer-content" },
            { id: "create-new-wizard", contentId: "create-new-drawer-content" },
            { id: "create-new-property-wizard", contentId: "create-new-property-content" },
            { id: "create-property-table-mapping", contentId: "create-property-table-mapping-content" },
            { id: "message-template-drawer", contentId: "message-template-content" },
            { id: "price-table-drawer", contentId: "price-table-content" },
            { id: "workflow-wizard", contentId: null },
            { id: "workflow_result_wizard", contentId: "workflow-result-content" },
            { id: "workflow_create_wizard", contentId: "workflow_edit_content" },
            { id: "task_wizard", contentId: null },
            { id: "task_workflow_wizard", contentId: "task_action_wizard_content" },
            { id: "project_view_wizard", contentId: "view-drawer" },
            { id: "formroom_view_wizard", contentId: "formroom_form" },
            { id: "chat-drawer", contentId: "chat-drawer-content" },
            { id: "module-templates-drawer", contentId: "module-templates-content" },
            { id: "manage-related-wizard-template", contentId: "manage-related-drawer-content" },
            { id: "create-association-wizard", contentId: "create-association-wizard-content" },
            { id: "create-custom-object-wizard", contentId: "custom-object-create-wizard-drawer"},
            { id: "manage-custom-object-wizard", contentId: "custom-object-manage-wizard-drawer"},
            { id: "custom-object-drawer", contentId: "custom-object-content"},
            { id: "workspace-log-download-wizard", contentId: "workspace-log-download-content" },
            { id: "manage-price-template", contentId: "manage-price-content" }
        ];

        wizards.forEach(function(wizard) {
            var wizardEl = document.getElementById(wizard.id);
            var wizardInstance = KTDrawer.getInstance(wizardEl);
            wizardInstance.on("kt.drawer.hide", function() {
                if (wizard.contentId) {
                    document.getElementById(wizard.contentId).innerHTML = '';
                    console.log('clear', wizard.contentId);
                }
            });
        });

        {% comment %} Set drawer overlay z-index for multiple drawer showing in the same time {% endcomment %}
        document.querySelectorAll('div[data-kt-drawer=true]').forEach(function(drawerElement){
            var drawerInstance = KTDrawer.getInstance(drawerElement);
            drawerInstance.on("kt.drawer.shown", function() {
                sortOverlayDrawer()
            })

            {% comment %} RESET {% endcomment %}
            drawerInstance.on("kt.drawer.after.hidden", function() {
                sortOverlayDrawer()
            })
        })

        function sortOverlayDrawer(){
            KTDrawer.createInstances();
            // Get all showing drawer
            const drawerElements = Array.from(document.querySelectorAll('.drawer-on'));

            // Sort elements front to back
            const drawersByDOMOrder = drawerElements.sort((a, b) => {
                if (a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING) {
                    // b is in front
                    return 1;
                } else {
                    // a is in front
                    return -1;
                }
            });
            var originalZIndex = 110
            drawersByDOMOrder.forEach(function(drawer_){
                drawer_.style.zIndex = originalZIndex
                originalZIndex -= 1;
            })
        }

        var san_ai_drawer = KTDrawer.getInstance(document.getElementById('san-ai-drawer'));
        san_ai_drawer.on("kt.drawer.hide", function() {
            // Show all bb-feedback-button elements
            document.querySelectorAll('.bb-feedback-button').forEach(function(el) {
                el.style.display = '';
            });
        });
        san_ai_drawer.on("kt.drawer.show", function() {
            // Hide all bb-feedback-button elements
            document.querySelectorAll('.bb-feedback-button').forEach(function(el) {
                el.style.display = 'none';
            });
        });
    });

</script>

<div id="dataset-create" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
    data-kt-drawer-toggle=".dataset-create-button"
    data-kt-drawer-width="{'default':'60%','lg':'40%'}">
    <div class="card border-0 shadow-none rounded-0 w-100 bg-white">
            <div id="dataset-drawer-content" class="w-100">
            </div>

            <div class="d-flex justify-content-center">
                <div class="loading-drawer-spinner mt-10 mb-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <style>
            #dataset-drawer-content{
                display:inline-block;
            }
            .htmx-request #dataset-drawer-content{
                display:none;
            }
            .htmx-request#dataset-drawer-content{
                display:none;
            }
        </style>
        <script>
            $(document).ready(function() {
                var drawerEl = document.querySelector("#object-action-drawer");
                var drawer = KTDrawer.getInstance(drawerEl);
                drawer.on("kt.drawer.hide", function() {
                    document.getElementById('shopturbo-drawer-content').innerHTML = ''
                });
            })
        </script>
</div>