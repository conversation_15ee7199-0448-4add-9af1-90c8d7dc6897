import copy
import traceback
from django.http.response import HttpResponse
from django.shortcuts import redirect, render
from data.constants.constant import *
from data.constants.date_constant import *
from data.constants.properties_constant import *
from data.models import *
from utils.amazon import *
from utils.bcart import *
from utils.eccube import *
from utils.ecforce_bg_jobs.ecforce import *
from utils.filter import TYPE_OBJECT_RECEIPT
from utils.freee_bg_jobs.freee import *
from utils.inventory import *
from utils.makeshop import *
from utils.nextengine import *
from utils.salesforce.orders import get_opportunity_mapping_fields
from utils.salesforce.orders import get_opportunity_line_item_mapping_fields
from utils.seal_subscription import *
from utils.serializer import *
from utils.shopify_bg_job.shopify_orders import *
from utils.square import *
from utils.stripe.stripe import *
from utils.utility import (apply_item_search_setting, bg_job_to_sanka_url,
                           build_redirect_url, get_attr,
                           get_workspace,
                           is_valid_number, is_valid_uuid, save_custom_property, update_query_params_url)
from utils.woocommerce import *
from utils.workspace import get_permission
from utils.yahoo import *
from hubspot import HubSpot
import datetime
from utils.hubspot import (get_schema,
                           get_schema_detail_by_object_type_id,
                           get_schema_properties, get_association_types,
                           refresh_hubspot_token)

def sync_order_header_extractor(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == 'POST':
        mapping_type = request.POST.get('mapping_type', None)
        platform_columns = []
        contacts_columns = ["name", "last_name", "email", "phone_number"]
        company_columns = ["name", "email", "phone_number"]
        if 'action_index' in request.POST:
            action_index = request.POST.get('action_index')
            select_integration_ids = request.POST.get(
                f'select_integration_ids-{action_index}')
        else:
            select_integration_ids = request.POST.get('select_integration_ids')

        try:
            print('here!', request.POST)
            channel = Channel.objects.get(id=select_integration_ids)
        except Exception as e:
            print(f'WARNING === shopturbo.py -- 7714: {e}')
            return HttpResponse(404)

        # header_list = ["first_name","last_name","email","phone","country","address",'state','city','postal_code']
        header_list = []
        function_type = request.POST.get('function_type', None)
        hs_header_ready = False

        if mapping_type == 'order':
            orders_company_custom_property = None
            orders_contact_custom_property = None
            default_mapping_field = None
            ordersnamecustomfield = ShopTurboOrdersNameCustomField.objects.filter(workspace=workspace).exclude(
                type__in=["image", "user", "formula"]).values_list('name', flat=True)
            if ordersnamecustomfield:
                default_mapping_field = ordersnamecustomfield[0]

            if channel.integration.slug == 'ecforce':
                platform_columns.extend(
                    [col for col in DEFAULT_COLUMNS_ORDER if col not in ('checkbox', 'order_id')])
                header_list = [
                    {
                        'name': 'Customer Number',
                        'name_ja': '顧客番号',
                        'value': 'customer_number',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer Status',
                        'name_ja': '顧客ステータス',
                        'value': 'customer_status',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Membership Rank Name',
                        'name_ja': '会員ランク名',
                        'value': 'membership_rank_name',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer Mobile Email Address',
                        'name_ja': '顧客携帯メールアドレス',
                        'value': 'customer_mobile_email_address',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer Gender',
                        'name_ja': '顧客性別',
                        'value': 'customer_gender',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer Purchase Count',
                        'name_ja': '顧客購入回数',
                        'value': 'purchase_count',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Total Purchase Amount',
                        'name_ja': '購入総額',
                        'value': 'total_purchase_amount',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Membership Date',
                        'name_ja': '入会日',
                        'value': 'membership_date',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Name (Full)',
                        'name_ja': '請求先 (名前)',
                        'value': 'billing_name_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Name (Kana Full)',
                        'name_ja': '請求先 (カナフル)',
                        'value': 'billing_name_kana_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Postal Code (Full)',
                        'name_ja': '請求先 (郵便番号フル)',
                        'value': 'billing_postal_code_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Address (Full)',
                        'name_ja': '請求先 (住所フル)',
                        'value': 'billing_address_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Prefecture',
                        'name_ja': '請求先(都道府県)',
                        'value': 'billing_prefecture',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Phone Number (Full)',
                        'name_ja': '請求先 (電話番号フル)',
                        'value': 'billing_phone_number_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Fax (Full)',
                        'name_ja': '請求先 (FAXフル)',
                        'value': 'billing_fax_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Name 1',
                        'name_ja': '請求先 (名前1)',
                        'value': 'billing_name_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Name 2',
                        'name_ja': '請求先 (名前2)',
                        'value': 'billing_name_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Kana 1',
                        'name_ja': '請求先 (カナ1)',
                        'value': 'billing_kana_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Kana 2',
                        'name_ja': '請求先 (カナ2)',
                        'value': 'billing_kana_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Postal Code 1',
                        'name_ja': '請求先 (郵便番号1)',
                        'value': 'billing_postal_code_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Postal Code 2',
                        'name_ja': '請求先 (郵便番号2)',
                        'value': 'billing_postal_code_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Address 1',
                        'name_ja': '請求先(住所1)',
                        'value': 'billing_address_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Address 2',
                        'name_ja': '請求先(住所2)',
                        'value': 'billing_address_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Address 3',
                        'name_ja': '請求先(住所3)',
                        'value': 'billing_address_3',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Phone Number 1',
                        'name_ja': '請求先 (電話番号1)',
                        'value': 'billing_phone_number_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Phone Number 2',
                        'name_ja': '請求先 (電話番号2)',
                        'value': 'billing_phone_number_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Phone Number 3',
                        'name_ja': '請求先 (電話番号3)',
                        'value': 'billing_phone_number_3',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Fax 1',
                        'name_ja': '請求先 (FAX1)',
                        'value': 'billing_fax_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Fax 2',
                        'name_ja': '請求先 (FAX2)',
                        'value': 'billing_fax_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Fax 3',
                        'name_ja': '請求先 (FAX3)',
                        'value': 'billing_fax_3',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Total Points',
                        'name_ja': '合計ポイント',
                        'value': 'total_points',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Point Expiry Date',
                        'name_ja': 'ポイントの有効期限',
                        'value': 'point_expiry_date',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer Type Name',
                        'name_ja': '顧客タイプ名',
                        'value': 'customer_type_name',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Email Newsletter Subscription',
                        'name_ja': 'メールマガジン受け取り',
                        'value': 'email_newsletter_subscription',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Memo',
                        'name_ja': 'メモ',
                        'value': 'memo',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Blacklist',
                        'name_ja': 'ブラックリスト',
                        'value': 'blacklist',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Blacklist Reason',
                        'name_ja': 'ブラックリスト理由',
                        'value': 'blacklist_reason',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Billing Address (Full Address with Spaces)',
                        'name_ja': '請求先（住所フル空白あり）',
                        'value': 'billing_address_full_with_spaces',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'LINE ID',
                        'name_ja': 'LINE ID',
                        'value': 'line_id',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Coupon',
                        'name_ja': 'クーポン',
                        'value': 'coupon',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer Label',
                        'name_ja': '顧客ラベル',
                        'value': 'customer_label',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Do Not Send Email',
                        'name_ja': 'メール送信しない',
                        'value': 'do_not_send_email',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'NP Postpaid Real-Time Royal Customer',
                        'name_ja': 'NP後払いリアルタイムロイヤルカスタマー',
                        'value': 'np_postpaid_real_time_royal_customer',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'subtotal',
                        'name': 'Subtotal',
                        'name_ja': '小計',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'subtotal8',
                        'name': 'Subtotal 8%',
                        'name_ja': '小計8%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'subtotal10',
                        'name': 'Subtotal 10%',
                        'name_ja': '小計10%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'deliv_fee',
                        'name': 'Delivery Fee',
                        'name_ja': '送料',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'charge',
                        'name': 'Charge',
                        'name_ja': '手数料',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'tax',
                        'name': 'Tax',
                        'name_ja': '消費税',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'tax8',
                        'name': 'Tax 8%',
                        'name_ja': '消費税8%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'tax10',
                        'name': 'Tax 10%',
                        'name_ja': '消費税10%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'total',
                        'name': 'Total',
                        'name_ja': '合計',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'total8',
                        'name': 'Total 8%',
                        'name_ja': '合計8%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'total10',
                        'name': 'Total 10%',
                        'name_ja': '合計10%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_total',
                        'name': 'Payment Total',
                        'name_ja': '支払い合計',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'shipping_carrier_name',
                        'name': 'Shipping Carrier',
                        'name_ja': '配送業者',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'shipping_slip',
                        'name': 'Shipping Slip Number',
                        'name_ja': '配送伝票番号',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_method_name',
                        'name': 'Payment Method',
                        'name_ja': '支払い方法',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'kind',
                        'name': 'Order Type',
                        'name_ja': '受注種別',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'nth',
                        'name': 'Order Nth',
                        'name_ja': '定期内受注 (N番目)',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'scheduled_delivery_time_code',
                        'name': 'Scheduled Delivery Time Code',
                        'name_ja': 'お届け時間コード',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Name (Full)',
                        'name_ja': 'お届け先 (名前)',
                        'value': 'shipping_name_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Name (Kana Full)',
                        'name_ja': 'お届け先 (カナフル)',
                        'value': 'shipping_name_kana_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Postal Code (Full)',
                        'name_ja': 'お届け先 (郵便番号フル)',
                        'value': 'shipping_postal_code_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Address (Full)',
                        'name_ja': 'お届け先 (住所フル)',
                        'value': 'shipping_address_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Prefecture',
                        'name_ja': 'お届け先(都道府県)',
                        'value': 'shipping_prefecture',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Phone Number (Full)',
                        'name_ja': 'お届け先 (電話番号フル)',
                        'value': 'shipping_phone_number_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Fax (Full)',
                        'name_ja': 'お届け先 (FAXフル)',
                        'value': 'shipping_fax_full',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Name 1',
                        'name_ja': 'お届け先 (名前1)',
                        'value': 'shipping_name_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Name 2',
                        'name_ja': 'お届け先 (名前2)',
                        'value': 'shipping_name_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Kana 1',
                        'name_ja': 'お届け先 (カナ1)',
                        'value': 'shipping_kana_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Kana 2',
                        'name_ja': 'お届け先 (カナ2)',
                        'value': 'shipping_kana_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Postal Code 1',
                        'name_ja': 'お届け先 (郵便番号1)',
                        'value': 'shipping_postal_code_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Postal Code 2',
                        'name_ja': 'お届け先 (郵便番号2)',
                        'value': 'shipping_postal_code_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Address 1',
                        'name_ja': 'お届け先(住所1)',
                        'value': 'shipping_address_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Address 2',
                        'name_ja': 'お届け先(住所2)',
                        'value': 'shipping_address_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Address 3',
                        'name_ja': 'お届け先(住所3)',
                        'value': 'shipping_address_3',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Phone Number 1',
                        'name_ja': 'お届け先 (電話番号1)',
                        'value': 'shipping_phone_number_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Phone Number 2',
                        'name_ja': 'お届け先 (電話番号2)',
                        'value': 'shipping_phone_number_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Phone Number 3',
                        'name_ja': 'お届け先 (電話番号3)',
                        'value': 'shipping_phone_number_3',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Fax 1',
                        'name_ja': 'お届け先 (FAX1)',
                        'value': 'shipping_fax_1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Fax 2',
                        'name_ja': 'お届け先 (FAX2)',
                        'value': 'shipping_fax_2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Fax 3',
                        'name_ja': 'お届け先 (FAX3)',
                        'value': 'shipping_fax_3',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'order_memo',
                        'name': 'Memo',
                        'name_ja': 'メモ',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'product_name_with_tax',
                        'name': 'Product Name (Tax)',
                        'name_ja': '購入商品 (商品名：印)',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'product_labels',
                        'name': 'Product Labels',
                        'name_ja': '商品ラベル',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'wrapping',
                        'name': 'Wrapping',
                        'name_ja': 'ラッピング',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'remark',
                        'name': 'Remark',
                        'name_ja': '通信欄',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_state',
                        'name': 'Payment State',
                        'name_ja': '決済状況',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'first_reminded_at',
                        'name': 'First Reminded At',
                        'name_ja': '初回督促日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'remindered_at',
                        'name': 'Remindered At',
                        'name_ja': '最終督促日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'reminder_count',
                        'name': 'Reminder Count',
                        'name_ja': '督促回数',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'due_at',
                        'name': 'Due At',
                        'name_ja': '支払期限',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_authed_at',
                        'name': 'Payment Authed At',
                        'name_ja': '仮売上日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_completed_at',
                        'name': 'Payment Completed At',
                        'name_ja': '実売上日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_paid_at',
                        'name': 'Payment Paid At',
                        'name_ja': '入金日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_voided_at',
                        'name': 'Payment Voided At',
                        'name_ja': '取消日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_access_id',
                        'name': 'Transaction ID',
                        'name_ja': '取引ID',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_access_pass',
                        'name': 'Transaction Password',
                        'name_ja': '取引パスワード',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'url_group',
                        'name': 'URL Group',
                        'name_ja': '広告URLグループ名',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'advertiser',
                        'name': 'Advertiser',
                        'name_ja': '広告主名',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'scheduled_to_be_delivered_at',
                        'name': 'Scheduled To Be Delivered At',
                        'name_ja': '次回配送予定日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'discount_with_point',
                        'name': 'Discount(With Point)',
                        'name_ja': '割引(ポイント含む)',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'discount_with_point8',
                        'name': 'Discount 8%(With Point)',
                        'name_ja': '割引8%(ポイント含む)',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'discount_with_point10',
                        'name': 'Discount 10%(With Point)',
                        'name_ja': '割引10%(ポイント含む)',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'discount',
                        'name': 'Discount',
                        'name_ja': '割引',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'discount8',
                        'name': 'Discount 8%',
                        'name_ja': '割引8%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'discount10',
                        'name': 'Discount 10%',
                        'name_ja': '割引10%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'point',
                        'name': 'Point',
                        'name_ja': '利用ポイント',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'point8',
                        'name': 'Point 8%',
                        'name_ja': '利用ポイント8%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'point10',
                        'name': 'Point 10%',
                        'name_ja': '利用ポイント10%',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'reward_point',
                        'name': 'Reward Point',
                        'name_ja': '付与ポイント',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'point_expired_at',
                        'name': 'Point Expired At',
                        'name_ja': 'ポイントの有効期限',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'scheduled_to_be_shipped_at',
                        'name': 'Scheduled To Be Shipped At',
                        'name_ja': '次回発送予定日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'previous_scheduled_to_be_shipped_at',
                        'name': 'Previous Scheduled To Be Shipped At',
                        'name_ja': '前回発送予定日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'previous_scheduled_to_be_delivered_at',
                        'name': 'Previous Scheduled To Be Delivered At',
                        'name_ja': '前回配送予定日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'store',
                        'name': 'Store',
                        'name_ja': '媒体',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'bundled_items',
                        'name': 'Bundled Items',
                        'name_ja': '同梱物',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'misc_fee',
                        'name': 'Misc Fee',
                        'name_ja': 'その他',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'adjustment',
                        'name': 'Adjustment',
                        'name_ja': '調整金額',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'tbc',
                        'name': 'Orders Required',
                        'name_ja': '要対応受注',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'tbc_order_reasons',
                        'name': 'Orders Required Reasons',
                        'name_ja': '要対応理由',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_number',
                        'name': 'Payment Number',
                        'name_ja': '決済番号',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'times',
                        'name': 'Times',
                        'name_ja': '定期回数',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'shipping_histories_count',
                        'name': 'Shipping Histories Count',
                        'name_ja': '発送回数',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'picked_list',
                        'name': 'Picked List',
                        'name_ja': '出荷リスト',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'picked_at',
                        'name': 'Picked At',
                        'name_ja': '出荷リスト出力日',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'payment_last_error_message',
                        'name': 'Payment Last Error Message',
                        'name_ja': '最新の決済履歴（エラー内容）',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'customer_contacts',
                        'name': 'Customer Contacts',
                        'name_ja': 'お問い合わせ履歴',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'ip',
                        'name': 'IP Address',
                        'name_ja': 'IPアドレス',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'device_variant',
                        'name': 'Device Variant',
                        'name_ja': 'デバイス',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'shipping_carrier_code',
                        'name': 'Shipping Carrier Code',
                        'name_ja': '配送業者コード',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'last_order',
                        'name': 'Last Order',
                        'name_ja': '定期最新受注フラグ',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'memo01',
                        'name': 'Memo 01',
                        'name_ja': '受注備考1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'memo02',
                        'name': 'Memo 02',
                        'name_ja': '受注備考2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'subs_order_memo01',
                        'name': 'Subs Order Memo 01',
                        'name_ja': 'サブスクリプション備考1',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'subs_order_memo02',
                        'name': 'Subs Order Memo 02',
                        'name_ja': 'サブスクリプション備考2',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'invite_code',
                        'name': 'Invite Code',
                        'name_ja': '招待コード',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'total_recurring_sales_price_discount',
                        'name': 'Total Recurring Sales Price Discount',
                        'name_ja': '定期設定による販売価格割引',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'pay_with_epos',
                        'name': 'Pay With EPOS',
                        'name_ja': 'エポスカード',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'grant_plan_point',
                        'name': 'Grant Plan Point',
                        'name_ja': '付与予定ポイント',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'value': 'o_plux_result',
                        'name': 'O-PLUX Result',
                        'name_ja': 'O-PLUX 与信結果',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Item - Product Number',
                        'name_ja': '商品 - 製品番号',
                        'value': 'product_number|item',
                        'skip': False,
                        'default': default_mapping_field,
                    },
                    {
                        'name': 'Item - Variant SKU',
                        'name_ja': '商品 - バリアントSKU',
                        'value': 'variant_sku|item',
                        'skip': False,
                        'default': default_mapping_field,
                    }
                ]
            elif channel.integration.slug == 'rakuten':
                header_list = [
                    {
                        'name': 'Customer Name',
                        'name_ja': '顧客名',
                        'value': 'sender_name',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Address',
                        'name_ja': '注文者 - 住所',
                        'value': 'sender_address',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Postal Code',
                        'name_ja': '注文者 - 郵便番号',
                        'value': 'sender_postal_code',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Phone Number',
                        'name_ja': '注文者 - 電話番号',
                        'value': 'sender_phone_number',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Payment Method',
                        'name_ja': '支払い方法',
                        'value': 'payment_method',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Notes',
                        'name_ja': '備考',
                        'value': 'notes',
                        'skip': False,
                        'default': default_mapping_field
                    },

                    {
                        'name': 'Arrival Date',
                        'name_ja': '納期情報',
                        'value': 'arrival_date',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Coupons Used',
                        'name_ja': '使用されたクーポン',
                        'value': 'coupons_used',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Points Used',
                        'name_ja': '使用されたポイント',
                        'value': 'points_used',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Order Handling Fee',
                        'name_ja': '注文手数料',
                        'value': 'order_handling_fee',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Gift Wrapping Fee',
                        'name_ja': 'のし・ラッピング代',
                        'value': 'gift_wrapping_fee',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Delivery Date',
                        'name_ja': '配送日',
                        'value': 'delivery_date',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Delivery Time',
                        'name_ja': '配送時間',
                        'value': 'delivery_time',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Recipient - Delivery Address',
                        'name_ja': '配送先 - 住所',
                        'value': 'delivery_address',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Name',
                        'name_ja': '配送先 - 受取人名',
                        'value': 'recipient_name',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Postal Code',
                        'name_ja': '配送先 - 郵便番号',
                        'value': 'postal_code',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Phone Number',
                        'name_ja': '配送先 - 電話番号',
                        'value': 'phone_number',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Inventory Non-Sync Flag',
                        'name_ja': '在庫非同期フラグ',
                        'value': 'inventory_non_sync_flag',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Genre Id',
                        'name_ja': 'ジャンルID',
                        'value': 'genre_id|item',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Item Type',
                        'name_ja': '商品タイプ',
                        'value': 'item_type|item',
                        'skip': False,
                        'default': default_mapping_field
                    }
                ]
            elif channel.integration.slug == 'makeshop':
                header_list = [
                    {
                        'name': 'Customer Name',
                        'name_ja': '注文者 - 名前',
                        'value': 'sender_name',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Address',
                        'name_ja': '注文者 - 住所',
                        'value': 'sender_address',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Postal Code',
                        'name_ja': '注文者 - 郵便番号',
                        'value': 'sender_postal_code',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Phone Number',
                        'name_ja': '注文者 - 電話番号',
                        'value': 'sender_phone_number',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Date',
                        'name_ja': '納期情報',
                        'value': 'shipping_date',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Payment Method',
                        'name_ja': '支払い方法',
                        'value': 'payment_method',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Notes',
                        'name_ja': '備考',
                        'value': 'notes',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Coupons Used',
                        'name_ja': '使用されたクーポン',
                        'value': 'coupons_used',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Points Used',
                        'name_ja': '使用されたポイント',
                        'value': 'points_used',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Order Handling Fee',
                        'name_ja': '注文手数料',
                        'value': 'order_handling_fee',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Gift Wrapping Fee',
                        'name_ja': 'のし・ラッピング代',
                        'value': 'gift_wrapping_fee',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Recipient - Name',
                        'name_ja': '配送先 - 受取人名',
                        'value': 'recipient_name',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Address',
                        'name_ja': '配送先 - 住所',
                        'value': 'delivery_address',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Postal Code',
                        'name_ja': '配送先 - 郵便番号',
                        'value': 'postal_code',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Phone Number',
                        'name_ja': '配送先 - 電話番号',
                        'value': 'phone_number',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Item - Variation Unique Code',
                        'name_ja': '商品 - バリエーション独自コード',
                        'value': 'variation_unique_code|item',
                        'skip': False,
                        'default': default_mapping_field,
                    },
                    {
                        'name': 'Item - JAN Code',
                        'name_ja': '商品 - JANコード',
                        'value': 'jan_code|item',
                        'skip': False,
                        'default': default_mapping_field,
                    }
                ]
            elif channel.integration.slug == 'yahoo-shopping':
                header_list = [
                    {
                        'name': 'Customer Name',
                        'name_ja': '注文者 - 名前',
                        'value': 'sender_name',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Address',
                        'name_ja': '注文者 - 住所',
                        'value': 'sender_address',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Postal Code',
                        'name_ja': '注文者 - 郵便番号',
                        'value': 'sender_postal_code',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Customer - Phone Number',
                        'name_ja': '注文者 - 電話番号',
                        'value': 'sender_phone_number',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Date',
                        'name_ja': '納期情報',
                        'value': 'shipping_date',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Payment Method',
                        'name_ja': '支払い方法',
                        'value': 'payment_method',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Notes',
                        'name_ja': '備考',
                        'value': 'notes',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Coupons Used',
                        'name_ja': '使用されたクーポン',
                        'value': 'coupons_used',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Points Used',
                        'name_ja': '使用されたポイント',
                        'value': 'points_used',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Order Handling Fee',
                        'name_ja': '注文手数料',
                        'value': 'order_handling_fee',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Gift Wrapping Fee',
                        'name_ja': 'のし・ラッピング代',
                        'value': 'gift_wrapping_fee',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Fee',
                        'name_ja': '送料',
                        'value': 'shipping_fee',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Recipient - Name',
                        'name_ja': '配送先 - 受取人名',
                        'value': 'recipient_name',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Address',
                        'name_ja': '配送先 - 住所',
                        'value': 'delivery_address',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Postal Code',
                        'name_ja': '配送先 - 郵便番号',
                        'value': 'postal_code',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                    {
                        'name': 'Recipient - Phone Number',
                        'name_ja': '配送先 - 電話番号',
                        'value': 'phone_number',
                        'skip': False,
                        'default': default_mapping_field,
                        'is_sub_property': True
                    },
                ]
            elif channel.integration.slug == 'amazon':
                header_list = [
                    {
                        'name': 'Delivery Date',
                        'name_ja': '納期情報',
                        'value': 'delivery_date',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Ship Date',
                        'name_ja': '出荷日',
                        'value': 'ship_date',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Ship Method',
                        'name_ja': '配送方法',
                        'value': 'ship_method',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Carrier Name',
                        'name_ja': '運送会社名',
                        'value': 'carrier_name',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Is Replacement Order',
                        'name_ja': '交換注文',
                        'value': 'is_replacement_order',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Ship Service Level',
                        'name_ja': '配送サービスレベル',
                        'value': 'ship_service_level',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Region',
                        'name_ja': '配送地域',
                        'value': 'shipping_region',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Postal Code',
                        'name_ja': '配送先の郵便番号',
                        'value': 'shipping_postal_code',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Shipping Country Code',
                        'name_ja': '配送先の国コード',
                        'value': 'shipping_country_code',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Payment Method',
                        'name_ja': '支払い方法',
                        'value': 'payment_method',
                        'skip': False,
                        'default': default_mapping_field
                    }
                ]
            elif channel.integration.slug == 'wordpress':
                header_list = [
                    {
                        'name': 'Delivery Address',
                        'name_ja': '配送先住所',
                        'value': 'delivery_address',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Postal Code',
                        'name_ja': '郵便番号',
                        'value': 'postal_code',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Phone Number',
                        'name_ja': '電話番号',
                        'value': 'phone_number',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Payment Method',
                        'name_ja': '支払い方法',
                        'value': 'payment_method',
                        'skip': False,
                        'default': default_mapping_field
                    },
                    {
                        'name': 'Notes',
                        'name_ja': '備考',
                        'value': 'notes',
                        'skip': False,
                        'default': default_mapping_field
                    }
                ]
            elif channel.integration.slug == 'b-cart':
                header_list = [
                    {
                        "name": "Order ID",
                        "name_ja": "受注ID",
                        "value": "id",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Order Number",
                        "name_ja": "受注番号",
                        "value": "code",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination ID",
                        "name_ja": "配送先ID",
                        "value": "logistics_id",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Price Group",
                        "name_ja": "価格グループ",
                        "value": "customer_price_group_id",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Company Name",
                        "name_ja": "配送先 会社名",
                        "value": "logistics_comp_name",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Department Name",
                        "name_ja": "配送先 部署名",
                        "value": "logistics_department",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Representative",
                        "name_ja": "配送先 担当者",
                        "value": "logistics_name",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Postal Code",
                        "name_ja": "配送先 郵便番号",
                        "value": "logistics_zip",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Prefecture",
                        "name_ja": "配送先 都道府県",
                        "value": "logistics_pref",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Address",
                        "name_ja": "配送先 住所",
                        "value": "logistics_address",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Address 1",
                        "name_ja": "配送先 住所1",
                        "value": "logistics_address1",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Address 2",
                        "name_ja": "配送先 住所2",
                        "value": "logistics_address2",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Address 3",
                        "name_ja": "配送先 住所3",
                        "value": "logistics_address3",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Destination Phone Number",
                        "name_ja": "配送先 電話番号",
                        "value": "logistics_tel",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Total Product Amount",
                        "name_ja": "商品総額",
                        "value": "total_price",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Consumption Tax",
                        "name_ja": "消費税",
                        "value": "tax",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Fee",
                        "name_ja": "送料",
                        "value": "shipping_cost",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Payment Handling Fee",
                        "name_ja": "決済手数料",
                        "value": "COD_cost",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Total Order Amount",
                        "name_ja": "受注総額",
                        "value": "final_price",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Order Total by Tax Rate",
                        "name_ja": "税率別受注総額",
                        "value": "order_totals_total_incl_tax",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Points Used",
                        "name_ja": "ポイント利用",
                        "value": "use_point",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Points Earned",
                        "name_ja": "ポイント獲得",
                        "value": "get_point",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Payment Method",
                        "name_ja": "決済方法",
                        "value": "payment",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Payment Confirmation Date",
                        "name_ja": "決済確認日",
                        "value": "payment_at",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Comments/Requests",
                        "name_ja": "弊社または弊社商品に関するご意見、ご要望がございましたらご記入ください。",
                        "value": "customer_message",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Memo",
                        "name_ja": "メモ",
                        "value": "memo",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Notes for the Customer",
                        "name_ja": "お客様への連絡事項",
                        "value": "admin_message",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Order Date & Time",
                        "name_ja": "受注日時",
                        "value": "ordered_at",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Handling Status",
                        "name_ja": "対応状況",
                        "value": "status",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Group",
                        "name_ja": "配送グループ",
                        "value": "shipping_group_id",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Preferred Delivery Date",
                        "name_ja": "配送希望日",
                        "value": "due_date",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Preferred Delivery Time",
                        "name_ja": "配送希望時間",
                        "value": "due_time",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Tracking Number",
                        "name_ja": "送り状番号",
                        "value": "logistics_destination_code",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Date",
                        "name_ja": "発送日",
                        "value": "logistics_shipment_date",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Delivery Date",
                        "name_ja": "納品日",
                        "value": "logistics_arrival_date",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Status",
                        "name_ja": "発送状況",
                        "value": "logistics_status",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Shipping Memo",
                        "name_ja": "発送メモ",
                        "value": "logistics_memo",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "B-Cart Member ID",
                        "name_ja": "Bカート会員ID",
                        "value": "customer_id|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Your Company Custom Member ID",
                        "name_ja": "貴社独自会員ID",
                        "value": "customer_ext_id|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Representative",
                        "name_ja": "担当者",
                        "value": "customer_name|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Company Name",
                        "name_ja": "会社名",
                        "value": "customer_comp_name|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Department Name",
                        "name_ja": "部署名",
                        "value": "customer_department|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Phone Number",
                        "name_ja": "電話番号",
                        "value": "customer_tel|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Mobile Phone Number",
                        "name_ja": "携帯番号",
                        "value": "customer_mobile_phone|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Email Address",
                        "name_ja": "メールアドレス",
                        "value": "customer_email|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Postal Code",
                        "name_ja": "郵便番号",
                        "value": "customer_zip|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Prefecture",
                        "name_ja": "都道府県",
                        "value": "customer_pref|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Address",
                        "name_ja": "住所",
                        "value": "customer_address|company",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Product Management Number",
                        "name_ja": "商品管理番号",
                        "value": "main_no|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Set Name",
                        "name_ja": "セット名",
                        "value": "set_name|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Product Number",
                        "name_ja": "品番",
                        "value": "product_no|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "JAN Code",
                        "name_ja": "JANコード",
                        "value": "jan_code|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Location",
                        "name_ja": "ロケーション",
                        "value": "location_no|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Set Custom Field Name 1",
                        "name_ja": "セットカスタム項目名1",
                        "value": "set_custom1|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Set Custom Field Name 2",
                        "name_ja": "セットカスタム項目名2",
                        "value": "set_custom2|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Set Custom Field Name 3",
                        "name_ja": "セットカスタム項目名3",
                        "value": "set_custom3|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Product Options",
                        "name_ja": "商品オプション",
                        "value": "options|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Unit Price",
                        "name_ja": "単価",
                        "value": "unit_price|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Quantity per Package",
                        "name_ja": "入数",
                        "value": "set_quantity|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Unit",
                        "name_ja": "単位",
                        "value": "set_unit|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Order Quantity",
                        "name_ja": "受注数",
                        "value": "order_pro_count|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Line Item Type",
                        "name_ja": "明細区分",
                        "value": "item_type|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Tax Rate",
                        "name_ja": "税率",
                        "value": "tax_rate|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Tax Classification",
                        "name_ja": "税区分",
                        "value": "tax_type_id|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    },
                    {
                        "name": "Tax-Included Classification",
                        "name_ja": "税込区分",
                        "value": "tax_incl|item",
                        "skip": False,
                        "default": "default_mapping_field"
                    }
                ]
            elif channel.integration.slug == 'hubspot':
                platform_columns.extend([col for col in DEFAULT_COLUMNS_ORDER if col in (
                    'platform_id', 'delivery_status')])

                if function_type == 'import':
                    hs_header_list = HSHeaderList.objects.filter(
                        channel=channel, function_type='import').first()
                else:
                    hs_header_list = HSHeaderList.objects.filter(
                        channel=channel, function_type='export').first()

                if hs_header_list:
                    header_list = ast.literal_eval(hs_header_list.header_list)
                    hs_header_ready = True

            elif channel.integration.slug == 'nextengine':     # NextEngine Mapping
                header_list = NEXT_ENGINE_HEADER_MAPPING_LIST.copy()
                contacts_columns.extend(ContactsNameCustomField.objects.filter(workspace=workspace).exclude(
                    type__in=["image", "user", "formula", "hierarchy"]).values_list('name', flat=True))
                platform_columns.extend([col for col in DEFAULT_COLUMNS_ORDER if col in (
                    'platform_id', 'delivery_status')])

                company_columns.extend(CompanyNameCustomField.objects.filter(workspace=workspace).exclude(
                    type__in=["image", "user", "formula", "hierarchy"]).values_list('name', flat=True))
                orders_contact_custom_property = list(ShopTurboOrdersNameCustomField.objects.filter(
                    workspace=workspace, type__in=["contact"]).values_list('name', flat=True))
                orders_company_custom_property = list(ShopTurboOrdersNameCustomField.objects.filter(
                    workspace=workspace, type__in=["company"]).values_list('name', flat=True))
                
            elif channel.integration.slug == 'salesforce':
                mapping_fields = get_opportunity_mapping_fields(channel.id)
                if mapping_fields:
                    # Convert contact_mapping_fields to header_list format
                    for field in mapping_fields:
                        field_name = field.get('name', '')
                        field_label = field.get('label', field_name)

                        # Skip system fields and certain lookup fields
                        if field_name not in ['sanka_id', 'platform', 'platform_id']:
                            default_value = ''

                            # Set default mappings for common fields
                            if field_name == 'FirstName':
                                default_value = 'name'
                            elif field_name == 'LastName':
                                default_value = 'last_name'
                            elif field_name == 'Email':
                                default_value = 'email'
                            elif field_name == 'Phone' or field_name == 'MobilePhone':
                                default_value = 'phone_number'

                            header_list.append({
                                'value': field_name,
                                'name': field_label,
                                'name_ja': field_label,  # Using same label for Japanese
                                'skip': False,
                                'default': default_value
                            })

                line_item_mapping_fields = get_opportunity_line_item_mapping_fields(channel.id)
                if line_item_mapping_fields:
                    for field in line_item_mapping_fields:
                        field_name = field.get('name', '')
                        field_label = field.get('label', field_name)

                        # Skip system fields and certain lookup fields
                        if field_name not in ['sanka_id', 'platform', 'platform_id']:
                            header_list.append({
                                'value': f'{field_name}|line_item',
                                'name': field_label,
                                'name_ja': field_label,  # Using same label for Japanese
                                'skip': False,
                                'default': ''
                            })

                
                platform_columns.extend(
                    [col for col in DEFAULT_COLUMNS_ORDER if col not in ('checkbox', 'order_id')])

            elif channel.integration.slug == 'shopify':
                header_list = [
                    {
                        'name': 'First Name',
                        'name_ja': '名前',
                        'value': 'first_name',
                        'skip': False,
                        'default': 'name'
                    },
                    {
                        'name': 'Last Name',
                        'name_ja': '苗字',
                        'value': 'last_name',
                        'skip': False,
                        'default': 'last_name'
                    },
                    {
                        'name': 'Email',
                        'name_ja': 'メールアドレス',
                        'value': 'email',
                        'default': 'email',
                        'skip': False,
                    },
                    {
                        'name': 'Phone',
                        'name_ja': '電話番号',
                        'value': 'phone',
                        'skip': False,
                        'default': 'phone_number'
                    },
                    {
                        'name': 'Address',
                        'name_ja': '住所',
                        'value': 'address1',
                        'skip': False,
                        'default': 'name'
                    },
                    {
                        'name': 'Apartment',
                        'name_ja': 'アパート',
                        'value': 'address2',
                        'skip': False,
                        'default': 'name'
                    },
                    {
                        'name': 'Country',
                        'name_ja': '国',
                        'value': 'country',
                        'skip': False,
                        'default': 'name'
                    },
                    {
                        'name': 'State',
                        'name_ja': '都道府県',
                        'value': 'province',
                        'skip': False,
                        'default': 'name'
                    },
                    {
                        'name': 'City',
                        'name_ja': '市区町村',
                        'value': 'city',
                        'skip': False,
                        'default': 'name'
                    },
                    {
                        'name': 'Postal Code',
                        'name_ja': '郵便番号',
                        'value': 'zip',
                        'skip': False,
                        'default': 'name'
                    },
                    {
                        'name': 'Buyer Accepts Marketing',
                        'name_ja': 'マーケティング許可',
                        'value': 'buyer_accepts_marketing|order',
                        'skip': False,
                        'default': 'buyer_accepts_marketing'
                    },
                    {
                        'name': 'Cancel Reason',
                        'name_ja': 'キャンセル理由',
                        'value': 'cancel_reason|order',
                        'skip': False,
                        'default': 'cancel_reason'
                    },
                    {
                        'name': 'Cancelled At',
                        'name_ja': 'キャンセル日時',
                        'value': 'cancelled_at|order',
                        'skip': False,
                        'default': 'cancelled_at'
                    },
                    {
                        'name': 'Checkout ID',
                        'name_ja': 'チェックアウトID',
                        'value': 'checkout_id|order',
                        'skip': False,
                        'default': 'checkout_id'
                    },
                    {
                        'name': 'Checkout Token',
                        'name_ja': 'チェックアウトトークン',
                        'value': 'checkout_token|order',
                        'skip': False,
                        'default': 'checkout_token'
                    },
                    {
                        'name': 'Confirmed',
                        'name_ja': '確認済み',
                        'value': 'confirmed|order',
                        'skip': False,
                        'default': 'confirmed'
                    },
                    {
                        'name': 'Financial Status',
                        'name_ja': '支払いステータス',
                        'value': 'financial_status|order',
                        'skip': False,
                        'default': 'financial_status'
                    },
                    {
                        'name': 'Fulfillment Status',
                        'name_ja': '配送ステータス',
                        'value': 'fulfillment_status|order',
                        'skip': False,
                        'default': 'fulfillment_status'
                    },
                    {
                        'name': 'Name',
                        'name_ja': '注文名',
                        'value': 'name|order',
                        'skip': False,
                        'default': 'name'
                    },
                    {
                        'name': 'Number',
                        'name_ja': '番号',
                        'value': 'number|order',
                        'skip': False,
                        'default': 'number'
                    },
                    {
                        'name': 'Order Number',
                        'name_ja': '注文番号',
                        'value': 'order_number|order',
                        'skip': False,
                        'default': 'order_number'
                    },
                    {
                        'name': 'Order Status URL',
                        'name_ja': '注文ステータスURL',
                        'value': 'order_status_url|order',
                        'skip': False,
                        'default': 'order_status_url'
                    },
                    {
                        'name': 'Payment Gateway Names',
                        'name_ja': '決済ゲートウェイ名',
                        'value': 'payment_gateway_names|order',
                        'skip': False,
                        'default': 'payment_gateway_names'
                    },
                    {
                        'name': 'Processed At',
                        'name_ja': '処理日時',
                        'value': 'processed_at|order',
                        'skip': False,
                        'default': 'processed_at'
                    },
                    {
                        'name': 'Source Name',
                        'name_ja': 'ソース名',
                        'value': 'source_name|order',
                        'skip': False,
                        'default': 'source_name'
                    },
                    {
                        'name': 'Subtotal Price',
                        'name_ja': '小計',
                        'value': 'subtotal_price|order',
                        'skip': False,
                        'default': 'subtotal_price'
                    },
                    {
                        'name': 'Tags',
                        'name_ja': 'タグ',
                        'value': 'tags|order',
                        'skip': False,
                        'default': 'tags'
                    },
                    {
                        'name': 'Tax Exempt',
                        'name_ja': '非課税',
                        'value': 'tax_exempt|order',
                        'skip': False,
                        'default': 'tax_exempt'
                    },
                    {
                        'name': 'Taxes Included',
                        'name_ja': '税込み',
                        'value': 'taxes_included|order',
                        'skip': False,
                        'default': 'taxes_included'
                    },
                    {
                        'name': 'Total Discounts',
                        'name_ja': '割引合計',
                        'value': 'total_discounts|order',
                        'skip': False,
                        'default': 'total_discounts'
                    },
                    {
                        'name': 'Total Line Items Price',
                        'name_ja': '商品合計金額',
                        'value': 'total_line_items_price|order',
                        'skip': False,
                        'default': 'total_line_items_price'
                    },
                    {
                        'name': 'Total Outstanding',
                        'name_ja': '未払い金額',
                        'value': 'total_outstanding|order',
                        'skip': False,
                        'default': 'total_outstanding'
                    },
                    {
                        'name': 'Total Price',
                        'name_ja': '合計金額',
                        'value': 'total_price|order',
                        'skip': False,
                        'default': 'total_price'
                    },
                    {
                        'name': 'Total Tax',
                        'name_ja': '税金合計',
                        'value': 'total_tax|order',
                        'skip': False,
                        'default': 'total_tax'
                    },
                    {
                        'name': 'Total Tip Received',
                        'name_ja': '受け取ったチップ',
                        'value': 'total_tip_received|order',
                        'skip': False,
                        'default': 'total_tip_received'
                    },
                    {
                        'name': 'Total Weight',
                        'name_ja': '総重量',
                        'value': 'total_weight|order',
                        'skip': False,
                        'default': 'total_weight'
                    }
                ]

                object_type = request.POST.get(
                    "object_type", TYPE_OBJECT_ORDER)
                mapping = ContactsMappingFields.objects.filter(
                    workspace=workspace, platform=channel.integration.slug, object_type=object_type).first()
                if not mapping:
                    mapping, _ = ContactsMappingFields.objects.get_or_create(
                        workspace=workspace, platform=channel.integration.slug, object_type=object_type)
                contactsnamecustomfield = ContactsNameCustomField.objects.filter(workspace=workspace).exclude(
                    type__in=["contact", "image", "user", "formula"]).values_list('name', flat=True)
                contacts_columns.extend(contactsnamecustomfield)

                if mapping.input_data:
                    for item in header_list:
                        if item['value'] in mapping.input_data:
                            item['default'] = mapping.input_data[item['value']]['value']
                            item['skip'] = mapping.input_data[item['value']]['skip']

            try:
                header_list_conditional = copy.deepcopy(header_list)
                mapping, _ = ShopTurboOrdersMappingFields.objects.get_or_create(
                    workspace=workspace, platform=channel.integration.slug)
                if mapping.input_data:
                    for item in header_list:
                        if item['value'] in mapping.input_data:
                            item['default'] = mapping.input_data[item['value']]['value']
                            item['skip'] = mapping.input_data[item['value']]['skip']

                mapping_conditional, _ = ShopTurboOrdersMappingFieldsConditional.objects.get_or_create(
                    workspace=workspace, platform=channel.integration.slug)
                if mapping_conditional.input_data:
                    for item in header_list_conditional:
                        if item['value'] in mapping_conditional.input_data:
                            item['default'] = mapping_conditional.input_data[item['value']]['value']
                            item['skip'] = mapping_conditional.input_data[item['value']]['skip']
            except Exception:
                traceback.print_exc()
            # platform_columns = ["item_price_order","total_price","total_price_without_tax","tax"]
            ordersnamecustomfield = ShopTurboOrdersNameCustomField.objects.filter(workspace=workspace).exclude(
                type__in=["image", "user", "formula", "shipping_info"]).values_list('name', flat=True)
            platform_columns.extend(ordersnamecustomfield)

            sub_property_columns = {}
            shipping_infos = ShopTurboOrdersNameCustomField.objects.filter(
                workspace=workspace, type='shipping_info')

            try:
                if shipping_infos:
                    for shipping_info in shipping_infos:
                        if shipping_info.sub_property:
                            sub_property_columns[shipping_info.name] = []
                            for sub_property in ast.literal_eval(shipping_info.sub_property):
                                sub_property_columns[shipping_info.name].append(
                                    sub_property)
            except Exception:
                traceback.print_exc()
            contacts_columns.extend(ContactsNameCustomField.objects.filter(workspace=workspace).exclude(
                type__in=["image", "user", "formula", "hierarchy"]).values_list('name', flat=True))

            company_columns = ["name", "email", "phone_number", "address"]
            company_columns.extend(CompanyNameCustomField.objects.filter(workspace=workspace).exclude(
                type__in=["image", "user", "formula", "hierarchy"]).values_list('name', flat=True))

            if channel.integration.slug in ['hubspot', 'nextengine', 'makeshop', 'ecforce', 'b-cart', 'salesforce', 'shopify', 'rakuten']:
                is_multi_object = True
            else:
                is_multi_object = False

            item_columns = ShopTurboItemsNameCustomField.objects.filter(workspace=workspace).exclude(
                type__in=["image", "user", "formula", "hierarchy"]).values_list('name', flat=True)
            line_item_columns = ShopTurboItemsOrdersNameCustomField.objects.filter(
                workspace=workspace).values_list('name', flat=True)

            context = {
                "header_list": header_list,
                "header_list_conditional": header_list_conditional,
                "platform_columns": platform_columns,
                "platform": channel.integration.slug,
                "sub_property_columns": sub_property_columns,
                "contacts_columns": contacts_columns,
                "company_columns": company_columns,
                "item_columns": item_columns,
                "line_item_columns": line_item_columns,
                "is_multi_object": is_multi_object,
                "function_type": function_type,
                "channel_id": channel.id,

                # partial rendering
                "orders_contact_custom_property": orders_contact_custom_property,
                "orders_company_custom_property": orders_company_custom_property,
            }

            if channel.integration.slug == 'hubspot' and not hs_header_ready:
                return render(request, 'data/shopturbo/hubspot-get-mapping-button.html', context)

            # print('header_list', header_list)
            # print('header_list_conditional', header_list_conditional)
            return render(request, 'data/shopturbo/sync-orders-import-mapping.html', context)

        elif mapping_type == 'contact':
            orders_columns = []
            orders_contact_company_ustom_property = []
            function_type = request.POST.get('function_type', None)

            if channel.integration.slug == 'freee':
                header_list = freee_mapping_header_list.copy()
                # contacts_columns = ["Full Name","Email","Phone Number"]
                mapping, _ = ContactsMappingFields.objects.get_or_create(
                    workspace=workspace, platform="freee")
                orders_columns_name = list(ShopTurboOrdersNameCustomField.objects.filter(
                    workspace=workspace).values_list('name', flat=True))

                if 'object_type' in request.POST:
                    if request.POST['object_type'] == 'invoices':
                        orders_columns_name = list(InvoiceNameCustomField.objects.filter(
                            workspace=workspace).values_list('name', flat=True))
                        if lang == 'ja':
                            orders_columns_name.insert(0, '発行日')
                            orders_columns_name.insert(0, '支払期日')
                        else:
                            orders_columns_name.insert(0, 'Issuing Date')
                            orders_columns_name.insert(0, 'Payment Due Date')
                else:
                    if lang == 'ja':
                        orders_columns_name.insert(0, '注文日時')
                    else:
                        orders_columns_name.insert(0, 'Order Time')

                contactsnamecustomfield = list(ContactsNameCustomField.objects.filter(workspace=workspace).exclude(
                    type__in=["contact", "image", "user", "formula"]).values_list('name', flat=True))
                contactsnamecustomfield_ = []
                for order in orders_columns_name:
                    contactsnamecustomfield_.append(order)
                contacts_columns = contactsnamecustomfield_
                orders_columns = list(ShopTurboOrdersNameCustomField.objects.filter(
                    workspace=workspace, type__in=["choice", "text"]).values_list('name', flat=True))

                if 'object_type' in request.POST:
                    if request.POST['object_type'] == 'invoices':
                        orders_columns = list(InvoiceNameCustomField.objects.filter(
                            workspace=workspace, type__in=["choice", "text"]).values_list('name', flat=True))

                if mapping.input_data:
                    for item in header_list:
                        if item['value'] in mapping.input_data:
                            item['default'] = mapping.input_data[item['value']]['value']
                            item['skip'] = mapping.input_data[item['value']]['skip']

            platform_columns = []
            ordersnamecustomfield = ShopTurboOrdersNameCustomField.objects.filter(workspace=workspace).exclude(
                            type__in=["image", "user", "formula", "shipping_info"]).values_list('name', flat=True)
            platform_columns.extend(ordersnamecustomfield)

            context = {
                "function_type": function_type,
                "header_list": header_list,
                "orders_contact_company_ustom_property": orders_contact_company_ustom_property,
                "company_columns": company_columns,
                "contacts_columns": contacts_columns,
                "orders_columns": orders_columns,
                "mapping_type": mapping_type,
                "platform": channel.integration.slug,
                "object_type": 'contact',
                "platform_columns": platform_columns,
            }
            return render(request, 'data/contacts/sync-contacts-import-mapping.html', context)

    return HttpResponse(200)

def sync_order_association_header_extractor(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    print(f'sync_order_association_header_extractor: {request.POST}')

    if request.method == 'POST':
        mapping_type = request.POST.get('mapping_type', None)
        platform_columns = []
        if 'action_index' in request.POST:
            action_index = request.POST.get('action_index')
            select_integration_ids = request.POST.get(
                f'select_integration_ids-{action_index}')
        else:
            select_integration_ids = request.POST.get('select_integration_ids')

        try:
            channel = Channel.objects.get(id=select_integration_ids)
        except Exception as e:
            print(f'WARNING === shopturbo.py -- 7714: {e}')
            return HttpResponse(404)

        # header_list = ["first_name","last_name","email","phone","country","address",'state','city','postal_code']
        header_list = []
        function_type = request.POST.get('function_type', None)

        default_mapping_field = None
        ordersnamecustomfield = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_ORDER).order_by('created_at')
        if ordersnamecustomfield:
            default_mapping_field = ordersnamecustomfield[0]
            
        platform_columns.extend(ordersnamecustomfield)

        if channel.integration.slug == 'hubspot':
            if function_type:
                try:
                    access_token = refresh_hubspot_token(channel.id)
                    platform_object = get_schema_detail_by_object_type_id(
                        channel.id, '0-3')
                    OBJECT_TYPE_MAP = {
                        "0-1": "contact",
                        "0-2": "company",
                        "0-3": "deal",
                        "0-69": "subscription",
                        "0-53": "invoice"
                    }
                    JAPANESE_OBJECT_TYPE_MAP = {
                        "0-1": "コンタクト",        # Contact
                        "0-2": "会社",            # Company
                        "0-3": "取引",            # Deal
                        "0-69": "サブスクリプション",  # Subscription
                        "0-53": "請求書"          # Invoice
                    }
                    excluded_assoc_types = ["0-18", "0-27", "0-46", "0-47", "0-48", "0-49", "0-51", "0-116",
                                            "0-72", "0-80", "0-101", "0-113", "0-35", "0-8", "0-420", "0-970",
                                            "0-4", "0-84", "0-11", "0-421", "0-107", "0-19", "0-7", "0-21", "0-123",
                                            "0-60", "0-20", "0-113", "0-63", "0-136", "0-118", "0-162", "0-68",
                                            "0-5", "0-76", "0-73", "0-125", "0-43", "0-14"]

                    # Process the custom object properties
                    for assoc in platform_object.associations:
                        if assoc.from_object_type_id == '0-3' and assoc.to_object_type_id not in excluded_assoc_types:
                            assoc_name = assoc.name
                            to_object_type = assoc.to_object_type_id
                            assoc_id = assoc.id

                            # Map to human-readable object name
                            to_object_label = OBJECT_TYPE_MAP.get(to_object_type, to_object_type)
                            to_object_label_ja = JAPANESE_OBJECT_TYPE_MAP.get(to_object_type, to_object_type)

                            if f'Association to {to_object_label} ({assoc_name})' not in [item['name'] for item in header_list]:
                                # Add the association to the header list
                                header_list.append({
                                    'value': f'{assoc_name}|{assoc_id}|0-3|{to_object_type}',
                                    'options': None,
                                    'name': f'Association to {to_object_label} ({assoc_name})',
                                    'name_ja': f'{to_object_label_ja}への関連付け ({assoc_name})',
                                    'skip': False,
                                    'default': default_mapping_field
                                })
                except:
                    traceback.print_exc()
            
        elif channel.integration.slug == 'salesforce':
            mapping_fields = get_opportunity_mapping_fields(channel.id)
            if mapping_fields:
                # Convert contact_mapping_fields to header_list format
                for field in mapping_fields:
                    field_name = field.get('name', '')
                    field_label = field.get('label', field_name)

                    # Skip system fields and certain lookup fields
                    if field_name not in ['sanka_id', 'platform', 'platform_id']:
                        default_value = ''

                        # Set default mappings for common fields
                        if field_name == 'FirstName':
                            default_value = 'name'
                        elif field_name == 'LastName':
                            default_value = 'last_name'
                        elif field_name == 'Email':
                            default_value = 'email'
                        elif field_name == 'Phone' or field_name == 'MobilePhone':
                            default_value = 'phone_number'

                        header_list.append({
                            'value': field_name,
                            'name': field_label,
                            'name_ja': field_label,  # Using same label for Japanese
                            'skip': False,
                            'default': default_value
                        })

        try:
            mapping, _ = ShopTurboOrdersAssociationMappingFields.objects.get_or_create(
                workspace=workspace, platform=channel.integration.slug)
            if mapping.input_data:
                for item in header_list:
                    if item['value'] in mapping.input_data:
                        item['default'] = mapping.input_data[item['value']]['value']
                        item['skip'] = mapping.input_data[item['value']]['skip']
        except Exception:
            traceback.print_exc()

        context = {
            "header_list": header_list,
            "platform_columns": platform_columns,
            "platform": channel.integration.slug,
            "function_type": function_type,
            "channel_id": channel.id,
        }
        return render(request, 'data/shopturbo/sync-orders-association-import-mapping.html', context)
    return HttpResponse(200)


def sync_order_status_header_extractor(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == 'POST':
        if 'action_index' in request.POST:
            action_index = request.POST.get('action_index')
            select_integration_ids = request.POST.get(
                f'select_integration_ids-{action_index}')
        else:
            select_integration_ids = request.POST.get('select_integration_ids')

        try:
            print('here!', request.POST)
            channel = Channel.objects.get(id=select_integration_ids)
        except Exception as e:
            print(f'WARNING === shopturbo.py -- 7715: {e}')
            return HttpResponse(404)

        header_list = []
        function_type = request.POST.get('function_type', None)

        if channel.integration.slug == 'makeshop':
            header_list = [
                {
                    'name': 'Unprocessed',
                    'name_ja': '未処理',
                    'value': 'N',
                    'skip': False
                },
                {
                    'name': 'Delivery Completed',
                    'name_ja': '配送完了',
                    'value': 'Y',
                    'skip': False
                },
                {
                    'name': 'Canceled',
                    'name_ja': 'キャンセル',
                    'value': 'C',
                    'skip': False
                },
                {
                    'name': 'Return',
                    'name_ja': '返送',
                    'value': 'R',
                    'skip': False
                },
            ]
        elif channel.integration.slug == 'moneyforward':
            header_list = [
                {
                    'name': 'Unprocessed',
                    'name_ja': '未設定',
                    'value': '未設定',
                    'skip': False
                },
                {
                    'name': 'Unpaid',
                    'name_ja': '未入金',
                    'value': '未入金',
                    'skip': False
                },
                {
                    'name': 'Paid',
                    'name_ja': '入金済み',
                    'value': '入金済み',
                    'skip': False
                },
                {
                    'name': 'Unsettled',
                    'name_ja': '未払い',
                    'value': '未払い',
                    'skip': False
                },
                {
                    'name': 'Transferred',
                    'name_ja': '振込済み',
                    'value': '振込済み',
                    'skip': False
                }
            ]
        elif channel.integration.slug == 'hubspot':
            token_url = 'https://api.hubapi.com/oauth/v1/token'
            data = {
                'grant_type': 'refresh_token',
                'client_id': settings.HUBSPOT_CLIENT_ID,
                'client_secret': settings.HUBSPOT_CLIENT_SECRET,
                'refresh_token': channel.refresh_token
            }
            response = requests.post(token_url, data=data)
            token_info = response.json()
            channel.access_token = token_info.get('access_token')
            channel.save()

            access_token = token_info.get('access_token') if token_info.get('access_token') else channel.access_token
            api_client = HubSpot(access_token=access_token)
            deal_stages = api_client.crm.pipelines.pipelines_api.get_all("deals").results[0].stages
            
            stage_translate = {
                'Appointment Scheduled': 'アポイント設定済み (販売パイプライン)',
                'Qualified To Buy': '購入見込みあり (販売パイプライン)',
                'Presentation Scheduled': 'プレゼン予定済み (販売パイプライン)',
                'Decision Maker Bought-In': '意思決定者の賛同 (販売パイプライン)',
                'Contract Sent': '契約書送付済み (販売パイプライン)',
                'Closed Won': '成約 (販売パイプライン)',
                'Closed Lost': 'クローズした不成立取引 (販売パイプライン)'
            }
            header_list = []
            
            for stage in deal_stages:
                label_ja = stage_translate.get(stage.label, stage.label)

                header_list.append({
                    'name': stage.label,
                    'name_ja': label_ja,
                    'value': stage.id,
                    'skip': False
                })

        try:
            mapping, _ = ShopTurboOrdersStatusMappingFields.objects.get_or_create(
                workspace=workspace, platform=channel.integration.slug)
            if mapping.input_data:
                for item in header_list:
                    if item['value'] in mapping.input_data:
                        item['default'] = mapping.input_data[item['value']]['value']
                        item['skip'] = mapping.input_data[item['value']]['skip']
        except Exception:
            traceback.print_exc()
        
        COMMERCE_STATUS = [
            ('draft', 'Draft'),
            ('approved', 'Approved'),
            ('received', 'Received'),
            ('paid', 'Paid'),
        ]
        context = {
            "header_list": header_list,
            "platform": channel.integration.slug,
            "function_type": function_type,
            "channel_id": channel.id,
            "SHOPTURBO_ORDER_DELIVERY_STATUS": SHOPTURBO_ORDER_DELIVERY_STATUS,
            "COMMERCE_STATUS": COMMERCE_STATUS,
            "order": ShopTurboOrders(workspace=workspace)
        }
        
        predefined_hubspot_filter_import = request.POST.get('predefined_hubspot_filter_import', None)
        if predefined_hubspot_filter_import:
            try:
                predefined_hubspot_filter_import = ast.literal_eval(predefined_hubspot_filter_import)
                context['predefined_hubspot_filter_import'] = predefined_hubspot_filter_import
            except:
                pass

        print('header_list', header_list)
        return render(request, 'data/shopturbo/sync-orders-status-import-mapping.html', context)

    return HttpResponse(200)

def sync_platform_id_mapping_extractor(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == 'POST':
        object_type = request.POST.get('object_type', None)

        try:
            print('[Log platform_id mapping]', request.POST)
            channels = Channel.objects.filter(workspace=workspace, integration__slug__in=['hubspot'])
        except Exception as e:
            print(f'WARNING === shopturbo.py -- 7715: {e}')
            return HttpResponse(404)

        header_list = []

        for channel in channels:
            header_list.append(
                {
                    'name': channel.name,
                    'name_ja': channel.name,
                    'value': channel.id,
                    'skip': False
                }
            )
        
        platform_columns = []
        source_platform_columns = []
        if object_type == 'subscription':
            subsnamecustomfield = ShopTurboSubscriptionsNameCustomField.objects.filter(workspace=workspace).exclude(
                    type__in=["image", "user", "formula", "shipping_info"]).values_list('name', flat=True)
            platform_columns.extend(subsnamecustomfield)
        else:
            subsnamecustomfield = ShopTurboSubscriptionsNameCustomField.objects.filter(workspace=workspace).exclude(
                    type__in=["image", "user", "formula", "shipping_info"]).values_list('name', flat=True)
            invoicenamecustomfield = InvoiceNameCustomField.objects.filter(workspace=workspace).exclude(
                    type__in=["image", "user", "formula", "shipping_info"]).values_list('name', flat=True)
            source_platform_columns.extend(subsnamecustomfield)
            platform_columns.extend(invoicenamecustomfield)

        context = {
            "header_list": header_list,
            "platform_columns": platform_columns,
            "source_platform_columns": source_platform_columns,
            "object_type": object_type,
        }

        print('header_list', header_list)
        return render(request, 'data/shopturbo/sync-platform-id-mapping.html', context)

    return HttpResponse(200)

def sync_hubspot_filter_selector(request):
    data_filter = request.GET.get('data_filter', None)
    header_list = request.GET.get('header_list', None)
    predefined_hubspot_filter_import = request.GET.get('predefined_hubspot_filter_import', None)
    predefined_hubspot_filter_value = request.GET.get('predefined_hubspot_filter_value', None)
    predefined_hubspot_filter_option = None
    predefined_hubspot_filter_val = None

    try:
        header_list = ast.literal_eval(header_list)
    except Exception as e:
        print(f'Error parsing header_list: {e}')
        header_list = []

    try:
        if predefined_hubspot_filter_import:
            data_filter = predefined_hubspot_filter_import
        if predefined_hubspot_filter_value:
            predefined_hubspot_filter_value = ast.literal_eval(predefined_hubspot_filter_value)
            predefined_hubspot_filter_option = predefined_hubspot_filter_value.get('key', None)
            predefined_hubspot_filter_val = predefined_hubspot_filter_value.get('value', None)
    except Exception as e:
        print(f'Error parsing predefined_hubspot_filter_value: {e}')
        predefined_hubspot_filter_value = {}

    context={
        'header_list': header_list,
        'data_filter': data_filter,
    }

    if predefined_hubspot_filter_option:
        context['predefined_hubspot_filter_option'] = predefined_hubspot_filter_option
    if predefined_hubspot_filter_val:
        context['predefined_hubspot_filter_val'] = predefined_hubspot_filter_val
        
    return render(request, 'data/shopturbo/list-data-hubspot-filter-selector.html', context)

def sync_item_import_filter_selector(request):
    data_filter = request.GET.get('data_filter', None)
    header_list = request.GET.get('header_list', None)

    try:
        header_list = ast.literal_eval(header_list)
    except Exception as e:
        print(f'Error parsing header_list: {e}')
        header_list = []

    context={
        'header_list': header_list,
        'data_filter': data_filter,
    }
        
    return render(request, 'data/shopturbo/list-data-item-import-filter-selector.html', context)

def sync_order_import_filter_selector(request):
    data_filter = request.GET.get('data_filter', None)
    header_list = request.GET.get('header_list', None)
    predefined_filter_import = request.GET.get('predefined_filter_import', None)
    predefined_filter_import_value = request.GET.get('predefined_filter_import_value', None)
    predefined_filter_option = None
    predefined_filter_val = None

    try:
        header_list = ast.literal_eval(header_list)
    except Exception as e:
        print(f'Error parsing header_list: {e}')
        header_list = []

    try:
        if predefined_filter_import:
            data_filter = predefined_filter_import
        if predefined_filter_import_value:
            predefined_filter_import_value = ast.literal_eval(predefined_filter_import_value)
            predefined_filter_option = predefined_filter_import_value.get('key', None)
            predefined_filter_val = predefined_filter_import_value.get('value', None)
    except Exception as e:
        print(f'Error parsing predefined_filter_val: {e}')
        predefined_hubspot_filter_value = {}

    context={
        'header_list': header_list,
        'data_filter': data_filter,
    }

    if predefined_filter_option:
        context['predefined_filter_option'] = predefined_filter_option
    if predefined_filter_val:
        context['predefined_filter_val'] = predefined_filter_val
    print('log context', context)
    
    return render(request, 'data/shopturbo/list-data-order-import-filter-selector.html', context)
