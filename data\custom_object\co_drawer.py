import ast
import uuid
from django.db.models import Q
from django.http.response import HttpResponse
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse

from data.models import (
    CustomObjectPropertyRow, Module, View, ViewFilter,
    TransferHistory, Channel, CustomProperty,
    CustomObject, CustomObjectPropertyName,
    Contact, Company,
    ShopTurboOrders, ShopTurboItems,
    ShopTurboSubscriptions, ShopTurboInventory, InventoryTransaction, InventoryWarehouse,
    ShopTurboOrdersNameCustomField, ShopTurboItemsNameCustomField, InventoryTransactionNameCustomField,
    ShopTurboInventoryNameCustomField, ShopTurboSubscriptionsNameCustomField, Deals,
    ContactsNameCustomField, CompanyNameCustomField, DealsNameCustomField, AssociationLabel,
    SHOPTURBO_ORDER_DELIVERY_STATUS, SHOPTURBO_ORDER_PLATFORM, SHOPTURBO_ORDER_TYPE,
    CASE_STATUS, VIEW_MODE, SHOPTURBO_INVENTORY_TYPES, SHOPTURBO_INVENTORY_TRANSACTION_TYPES,
    SHOPTURBO_SUBSCRIPTIONS_STATUS, FREQ_TIME, PRIOR_FREQ_TIME,
)
from data.constants.constant import (
    DEALS_COLUMNS_DISPLAY, DEFAULT_PERMISSION
)
from data.constants.properties_constant import (
    TYPE_OBJECT_CUSTOM_OBJECT, TYPE_OBJECT_ITEM, TYPE_OBJECT_ORDER, TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_INVENTORY, TYPE_OBJECT_INVENTORY_TRANSACTION, TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_COMPANY, TYPE_OBJECT_CASE, TYPE_OBJECT_INVOICE, TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_SLIP, TYPE_OBJECT_RECEIPT, TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_BILL, TYPE_OBJECT_EXPENSE, TYPE_OBJECT_INVENTORY_WAREHOUSE,
)
from data.constants.stripe_constant import STRIPE_SUBSCRIPTION_STATUS_OPTIONS
from utils.decorator import login_or_hubspot_required
from utils.properties.properties import get_page_object, is_valid_uuid, get_field_data_type
from utils.utility import get_workspace
from utils.workspace import get_permission
CUSTOM_OBJECT_TARGET = 'custom_object'


@login_or_hubspot_required
def custom_object_drawer(request, id=None):
    lang = request.LANGUAGE_CODE
    obj_type = request.GET.get('object_type', None)
    workspace = get_workspace(request.user)

    # Initialize custom_object to None to avoid UnboundLocalError
    custom_object = None

    if not id or not (workspace := get_workspace(request.user)):
        if obj_type == TYPE_OBJECT_CUSTOM_OBJECT or not obj_type:
            return redirect(reverse('main', host='app'))

    custom_object = CustomObject.objects.filter(
        workspace=workspace, id=id).first()
    if not custom_object:
        if obj_type == TYPE_OBJECT_CUSTOM_OBJECT or not obj_type:
            return redirect(reverse('main', host='app'))

    drawer_type = request.GET.get('drawer-type', None)
    if not drawer_type:
        return redirect(reverse('main', host='app'))

    if obj_type == TYPE_OBJECT_CUSTOM_OBJECT:
        page_obj = get_page_object(TYPE_OBJECT_CUSTOM_OBJECT, lang)
    else:
        page_obj = get_page_object(obj_type, lang)
    view_type = page_obj['view_types']

    module = request.GET.get('module', None)
    view_id = request.GET.get('view_id', None)
    page = request.GET.get('page', 1)
    row_id = request.GET.get('row_id', None)

    source = request.GET.get('source', None)
    object_id = request.GET.get('object_id', None)

    if not module:
        if id:
            module = Module.objects.filter(workspace=workspace, object_values__contains=str(
                id)).order_by('order', 'created_at').first()
        else:
            module = Module.objects.filter(workspace=workspace, object_values__contains=obj_type).order_by(
                'order', 'created_at').first()
        module = module.slug

    if drawer_type == "view-settings":
        lang = request.LANGUAGE_CODE
        type = request.GET.get('type', None)

        if type == 'advance-filter':
            if obj_type == TYPE_OBJECT_CUSTOM_OBJECT:
                # Check if custom_object is defined before using it
                if custom_object is None:
                    return redirect(reverse('main', host='app'))

                mode = request.GET.get("mode", None)
                data_filter = request.GET.get("data_filter", None)
                app_type = request.GET.get("app_type", None)
                filter_options = request.GET.get("filter_options", None)
                data_type = "string"
                field_choice = []
                related_model_fields = {}  # format: {'<related model field>': '<model name>'}

                if data_filter in ["usage_status"]:
                    if data_filter == "usage_status":
                        if mode == 'advance-search':
                            field_choice = [
                                ('all', 'All'), ('active', 'Active'), ('archived', 'Archived')]
                        else:
                            field_choice = [('active', 'Active'),
                                            ('archived', 'Archived')]
                if data_filter in ["updated_at", "created_at"]:
                    data_type = 'date'

                field_options = []
                if is_valid_uuid(data_filter):
                    custom_field = CustomObjectPropertyName.objects.filter(
                        id=data_filter, custom_object=custom_object).first()
                    if custom_field:
                        if custom_field.type in ['number', 'formula']:
                            data_type = 'number'
                        elif custom_field.type in ['date', 'date_time', 'date_range', 'choice']:
                            data_type = custom_field.type

                predefined_data_filter = request.GET.get(
                    "predefined_data_filter", None)
                if predefined_data_filter:
                    predefined_data_filter = ast.literal_eval(
                        predefined_data_filter)
                    if isinstance(predefined_data_filter, list):
                        pass
                    else:
                        if not filter_options:
                            filter_options = predefined_data_filter.get('key')

                context = {
                    "data_filter": data_filter,
                    "data_type": data_type,
                    "predefined_data_filter": predefined_data_filter,
                    'field_choice': field_choice,
                    'uuid': uuid.uuid4(),
                    "filter_options": filter_options,
                    "field_options": field_options,
                    'custom_object': custom_object,
                    'mode': mode,
                    'object_type': obj_type
                }
                return render(request, 'data/custom_object/list-data-filter-selector.html', context)

            elif obj_type in [TYPE_OBJECT_ITEM, TYPE_OBJECT_ORDER, TYPE_OBJECT_SUBSCRIPTION, TYPE_OBJECT_INVENTORY, TYPE_OBJECT_INVENTORY_TRANSACTION]:
                app_type_model_map = {
                    TYPE_OBJECT_ITEM: "items",
                    TYPE_OBJECT_ORDER: "orders",
                    TYPE_OBJECT_SUBSCRIPTION: "subscriptions",
                    TYPE_OBJECT_INVENTORY: "inventory",
                    TYPE_OBJECT_INVENTORY_TRANSACTION: "inventory-transaction",
                }
                app_type = app_type_model_map.get(obj_type, None)

                data_filters = []
                data_types = []
                filter_options_ = []
                filter_statuses = []
                predefined_data_filters_ = []
                field_choices = []
                field_options_ = []
                uuids = []

                predefined_data_filters = request.GET.get(
                    "predefined_data_filters", None)
                predefined_data_filters = ast.literal_eval(
                    predefined_data_filters) if predefined_data_filters else {}
                if predefined_data_filters:
                    predefined_data_filters = dict(sorted(
                        predefined_data_filters.items(), key=lambda x: (0 if 'id' in x[0] else 1, x[0])))

                predefined_data_filter_statuses = request.GET.get(
                    "predefined_data_filter_statuses", None)
                predefined_data_filter_statuses = ast.literal_eval(
                    predefined_data_filter_statuses) if predefined_data_filter_statuses else {}
                if predefined_data_filter_statuses:
                    predefined_data_filter_statuses = dict(sorted(
                        predefined_data_filter_statuses.items(), key=lambda x: (0 if 'id' in x[0] else 1, x[0])))

                for data_key, predefined_data_filter in predefined_data_filters.items():
                    data_filter = data_key
                    if data_filter == 'checkbox':
                        continue
                    data_type = "string"
                    field_choice = []
                    field_options = []
                    filter_options = 'is'
                    filter_status = False
                    related_model_fields = {}

                    # Checking Default Columns
                    if app_type == "orders":
                        fields = ShopTurboOrders._meta.fields

                        custom_properties_map = {}
                        custom_properties = CustomProperty.objects.filter(
                            workspace=workspace, model=ShopTurboOrders._meta.db_table)
                        for property in custom_properties:
                            if property.value:
                                custom_properties_map[property.name] = ast.literal_eval(
                                    property.value)

                        if data_filter in ["order_type", "status", "delivery_status", "platform"]:
                            if data_filter in custom_properties_map:
                                field_choice = []
                                for value, label in custom_properties_map[data_filter].items():
                                    field_choice.append((value, label))
                            elif data_filter == "order_type":
                                field_choice = SHOPTURBO_ORDER_TYPE
                            elif data_filter == "status":
                                field_choice = [('active', 'Active'),
                                                ('archived', 'Archived')]
                            elif data_filter == "delivery_status":
                                field_choice = SHOPTURBO_ORDER_DELIVERY_STATUS
                            elif data_filter == "platform":
                                field_choice = SHOPTURBO_ORDER_PLATFORM

                        elif data_filter == 'customer':
                            contact_choices = Contact.objects.filter(
                                status='active', workspace=workspace).order_by('contact_id')
                            company_choices = Company.objects.filter(
                                status='active', workspace=workspace).order_by('company_id')
                            field_choice = True

                    elif app_type == "items":
                        fields = ShopTurboItems._meta.fields
                        if data_filter in ["status"]:
                            if data_filter == "status":
                                field_choice = [('active', 'Active'),
                                                ('archived', 'Archived')]

                    elif app_type == "subscriptions":
                        fields = ShopTurboSubscriptions._meta.fields

                        custom_properties_map = {}
                        custom_properties = CustomProperty.objects.filter(
                            workspace=workspace, model=ShopTurboSubscriptions._meta.db_table)
                        for property in custom_properties:
                            if property.value:
                                custom_properties_map[property.name] = ast.literal_eval(
                                    property.value)

                        if data_filter in ["status", "subscription_status"]:
                            if data_filter in custom_properties_map:
                                field_choice = []
                                for value, label in custom_properties_map[data_filter].items():
                                    field_choice.append((value, label))
                            elif data_filter == "status":
                                field_choice = [('active', 'Active'),
                                                ('archived', 'Archived')]
                            elif data_filter == "subscription_status":
                                field_choice = SHOPTURBO_SUBSCRIPTIONS_STATUS
                        elif '_platform|' in data_filter:
                            data_type = 'integration_choice'
                            id_ = data_filter.replace(
                                'subscription_platform|', '')[:36]
                            channel = Channel.objects.filter(
                                workspace=workspace, id=id_,).first()
                            if channel and channel.integration.slug == 'stripe':
                                if 'platform_status' in data_filter:
                                    field_choice = [(val, STRIPE_SUBSCRIPTION_STATUS_OPTIONS[val][lang])
                                                    for val in STRIPE_SUBSCRIPTION_STATUS_OPTIONS]
                                    field_choice = [
                                        ('is_not_empty', '(空でない)' if lang == 'ja' else '(Is Not Empty)')] + field_choice

                    elif app_type == "inventory":
                        fields = ShopTurboInventory._meta.fields
                        if data_filter == "status":
                            field_choice = [('active', 'Active'),
                                            ('archived', 'Archived')]
                        elif data_filter == "frequency_time":
                            field_choice = FREQ_TIME
                        elif data_filter == "prior_to_time":
                            field_choice = PRIOR_FREQ_TIME
                        elif data_filter == 'warehouse':
                            warehouse_objs = InventoryWarehouse.objects.filter(
                                workspace=workspace, usage_status='active')
                            field_choice = [
                                (iw.location, f'#{iw.id_iw:04d} - {iw.location}') for iw in warehouse_objs]
                            data_type = 'warehouse'

                        mm_fields = {field.name: field.get_internal_type()
                                     for field in ShopTurboInventory._meta.many_to_many}
                        if data_filter in mm_fields.keys():
                            data_type = 'object'

                        related_model_fields = {
                            rf.name: rf.related_model.__name__ for rf in ShopTurboInventory._meta.get_fields() if rf.related_model}

                    elif app_type == "inventory-transaction":
                        fields = InventoryTransaction._meta.fields
                        if data_filter == "usage_status":
                            field_choice = [('active', 'Active'),
                                            ('archived', 'Archived')]
                        elif data_filter == "inventory_type":
                            field_choice = SHOPTURBO_INVENTORY_TYPES
                        elif data_filter == "transaction_type":
                            field_choice = SHOPTURBO_INVENTORY_TRANSACTION_TYPES
                        elif data_filter == "user":
                            field_choice = workspace.user.all().values_list('username')

                    field_options = []
                    default_columns_dict = {
                        field.name: field.get_internal_type() for field in fields}
                    if data_filter in default_columns_dict.keys():
                        for check in ["float", "integer"]:
                            if check in default_columns_dict[data_filter].lower():
                                data_type = "number"
                                break
                        if "date" in default_columns_dict[data_filter].lower():
                            data_type = "date"
                    elif '__' in data_filter and data_filter.split('__')[0] in related_model_fields:
                        model_name = related_model_fields[data_filter.split('__')[
                            0]]
                        field_data_type = get_field_data_type(
                            model_name, data_filter.split('__')[1]).lower()
                        for check in ["float", "integer"]:
                            if check in field_data_type:
                                data_type = "number"
                                break
                        if "date" in field_data_type:
                            data_type = "date"
                    else:
                        custom_field_model_map = {
                            "orders": ShopTurboOrdersNameCustomField,
                            "items": ShopTurboItemsNameCustomField,
                            "inventory-transaction": InventoryTransactionNameCustomField,
                            "inventory": ShopTurboInventoryNameCustomField,
                            "subscriptions": ShopTurboSubscriptionsNameCustomField,
                        }
                        custom_field_model = custom_field_model_map[app_type]
                        if is_valid_uuid(data_filter):
                            custom_field = custom_field_model.objects.filter(
                                id=data_filter).first()
                        else:
                            custom_field = custom_field_model.objects.filter(
                                name__iexact=data_filter, workspace=workspace).first()
                        if custom_field:
                            if custom_field.type in ['number', 'formula']:
                                data_type = 'number'
                            elif custom_field.type in ['date', 'date_time', 'date_range', 'choice']:
                                data_type = custom_field.type
                            elif custom_field.type == 'user':
                                field_options = workspace.user.all()

                    if predefined_data_filter:
                        if isinstance(predefined_data_filter, list):
                            pass
                        else:
                            if not filter_options:
                                filter_options = predefined_data_filter.get(
                                    'key')

                    if predefined_data_filter_statuses:
                        predefined_data_filter_status = predefined_data_filter_statuses.get(
                            data_filter, None)
                        if predefined_data_filter_status:
                            if isinstance(predefined_data_filter_status, list):
                                pass
                            else:
                                if predefined_data_filter_status == 'True':
                                    predefined_data_filter_status = True
                                else:
                                    predefined_data_filter_status = False
                                filter_status = predefined_data_filter_status

                    if data_filter == 'line_item_price':
                        data_type = 'number'

                    data_filters.append(data_filter)
                    data_types.append(data_type)
                    filter_options_.append(filter_options)
                    filter_statuses.append(filter_status)
                    predefined_data_filters_.append(predefined_data_filter)
                    field_choices.append(field_choice)
                    field_options_.append(field_options)
                    uuids.append(uuid.uuid4())

                context = {
                    "data_filters": data_filters,
                    "data_types": data_types,
                    "app_type": app_type,
                    "predefined_data_filters": predefined_data_filters_,
                    'field_choices': field_choices,
                    'uuids': uuids,
                    "filter_options_": filter_options,
                    "field_options_": field_options,
                    "filter_statuses": filter_statuses,
                    'is_advanced_search': True,
                }
                return render(request, 'data/shopturbo/list-data-filter-selector-loop.html', context)
            elif obj_type in [TYPE_OBJECT_CONTACT, TYPE_OBJECT_COMPANY, TYPE_OBJECT_CASE]:
                app_type_model_map = {
                    TYPE_OBJECT_CONTACT: 'contact',
                    TYPE_OBJECT_COMPANY: 'companies',
                    TYPE_OBJECT_CASE: 'deals'
                }
                app_type = app_type_model_map.get(obj_type, None)

                data_filters = []
                data_types = []
                filter_options_ = []
                filter_statuses = []
                predefined_data_filters_ = []
                field_choices = []
                uuids = []

                predefined_data_filters = request.GET.get(
                    "predefined_data_filters", None)
                predefined_data_filters = ast.literal_eval(
                    predefined_data_filters) if predefined_data_filters else {}
                if predefined_data_filters:
                    predefined_data_filters = dict(sorted(
                        predefined_data_filters.items(), key=lambda x: (0 if 'id' in x[0] else 1, x[0])))

                predefined_data_filter_statuses = request.GET.get(
                    "predefined_data_filter_statuses", None)
                predefined_data_filter_statuses = ast.literal_eval(
                    predefined_data_filter_statuses) if predefined_data_filter_statuses else {}
                if predefined_data_filter_statuses:
                    predefined_data_filter_statuses = dict(sorted(
                        predefined_data_filter_statuses.items(), key=lambda x: (0 if 'id' in x[0] else 1, x[0])))

                for data_key, predefined_data_filter in predefined_data_filters.items():
                    data_filter = data_key
                    if data_filter == 'checkbox':
                        continue
                    data_type = "string"
                    field_choice = []
                    filter_options = []
                    filter_options = 'is'
                    filter_status = False

                    # Checking Default Columns
                    if app_type == "contact":
                        fields = Contact._meta.fields
                        if data_filter in ["status"]:
                            if data_filter == "status":
                                field_choice = [('active', 'Active'),
                                                ('archived', 'Archived')]

                    elif app_type == "companies":
                        fields = Company._meta.fields
                        if data_filter in ["status"]:
                            if data_filter == "status":
                                field_choice = [('active', 'Active'),
                                                ('archived', 'Archived')]

                    elif app_type == "deals":
                        lang = request.LANGUAGE_CODE
                        fields = Deals._meta.fields
                        if data_filter in ["status", "case_status"]:
                            if data_filter == "status":
                                field_choice = [('active', 'Active'),
                                                ('archived', 'Archived')]
                                if lang == 'ja':
                                    field_choice = [(k, DEALS_COLUMNS_DISPLAY[k][lang])
                                                    for k in ['active', 'archived']]

                            elif data_filter == "case_status":
                                field_choice = CASE_STATUS

                                custom_property = CustomProperty.objects.filter(
                                    workspace=workspace,
                                    model="data_deals",
                                    name=data_filter
                                ).first()

                                if custom_property:
                                    if custom_property.value:
                                        field_choice = []
                                        custom_property.value = ast.literal_eval(
                                            custom_property.value)
                                        for item in custom_property.value:
                                            field_choice.append(
                                                (item, custom_property.value[item]))

                    default_columns_dict = {
                        field.name: field.get_internal_type() for field in fields}
                    if data_filter in default_columns_dict.keys():
                        for check in ["float", "integer"]:
                            if check in default_columns_dict[data_filter].lower():
                                data_type = "number"
                                break
                        for check in ["date"]:
                            if check in default_columns_dict[data_filter].lower():
                                data_type = "date"
                                break
                    else:
                        if app_type == "contacts":
                            if is_valid_uuid(data_filter):
                                customfieldname = ContactsNameCustomField.objects.filter(
                                    id=data_filter).first()
                            else:
                                customfieldname = ContactsNameCustomField.objects.filter(
                                    name__iexact=data_filter, workspace=workspace).first()
                            if customfieldname:
                                if customfieldname.type == 'number' or customfieldname.type == 'formula':
                                    data_type = 'number'
                                elif customfieldname.type == 'date' or customfieldname.type == 'date_time':
                                    data_type = customfieldname.type

                        elif app_type == "companies":
                            if is_valid_uuid(data_filter):
                                customfieldname = CompanyNameCustomField.objects.filter(
                                    id=data_filter).first()
                            else:
                                customfieldname = CompanyNameCustomField.objects.filter(
                                    name__iexact=data_filter, workspace=workspace).first()
                            if customfieldname:
                                if customfieldname.type == 'number' or customfieldname.type == 'formula':
                                    data_type = 'number'
                                elif customfieldname.type == 'date' or customfieldname.type == 'date_time':
                                    data_type = customfieldname.type

                        elif app_type == "deals":
                            if is_valid_uuid(data_filter):
                                customfieldname = DealsNameCustomField.objects.filter(
                                    id=data_filter).first()
                            else:
                                customfieldname = DealsNameCustomField.objects.filter(
                                    name__iexact=data_filter, workspace=workspace).first()
                            if customfieldname:
                                if customfieldname.type == 'number' or customfieldname.type == 'formula':
                                    data_type = 'number'
                                elif customfieldname.type == 'date' or customfieldname.type == 'date_time':
                                    data_type = customfieldname.type

                    if predefined_data_filter:
                        if isinstance(predefined_data_filter, list):
                            pass
                        else:
                            if not filter_options:
                                filter_options = predefined_data_filter.get(
                                    'key')

                    if predefined_data_filter_statuses:
                        predefined_data_filter_status = predefined_data_filter_statuses.get(
                            data_filter, None)
                        if predefined_data_filter_status:
                            if isinstance(predefined_data_filter_status, list):
                                pass
                            else:
                                if predefined_data_filter_status == 'True':
                                    predefined_data_filter_status = True
                                else:
                                    predefined_data_filter_status = False
                                filter_status = predefined_data_filter_status

                    data_filters.append(data_filter)
                    data_types.append(data_type)
                    filter_options_.append(filter_options)
                    filter_statuses.append(filter_status)
                    predefined_data_filters_.append(predefined_data_filter)
                    field_choices.append(field_choice)
                    uuids.append(uuid.uuid4())

                context = {
                    "data_filters": data_filters,
                    "data_types": data_types,
                    "app_type": app_type,
                    "predefined_data_filters": predefined_data_filters_,
                    'field_choices': field_choices,
                    'uuids': uuids,
                    "filter_options_": filter_options_,
                    "filter_statuses": filter_statuses,
                    'is_advanced_search': True,
                }
                return render(request, 'data/contacts/list-data-filter-selector-loop.html', context)
            elif obj_type in [TYPE_OBJECT_INVOICE, TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_SLIP, TYPE_OBJECT_RECEIPT, TYPE_OBJECT_PURCHASE_ORDER, TYPE_OBJECT_BILL, TYPE_OBJECT_EXPENSE, TYPE_OBJECT_INVENTORY_WAREHOUSE]:
                object_type = obj_type
                page_obj = get_page_object(object_type, lang)
                base_model = page_obj['base_model']
                custom_model = page_obj['custom_model']

                data_filters = []
                data_types = []
                filter_options_ = []
                filter_statuses = []
                predefined_data_filters_ = []
                field_choices = []
                from_propertys = []
                uuids = []

                predefined_data_filters = request.GET.get(
                    "predefined_data_filters", None)
                predefined_data_filters = ast.literal_eval(
                    predefined_data_filters) if predefined_data_filters else {}
                if predefined_data_filters:
                    predefined_data_filters = dict(sorted(
                        predefined_data_filters.items(), key=lambda x: (0 if 'id' in x[0] else 1, x[0])))

                predefined_data_filter_statuses = request.GET.get(
                    "predefined_data_filter_statuses", None)
                predefined_data_filter_statuses = ast.literal_eval(
                    predefined_data_filter_statuses) if predefined_data_filter_statuses else {}
                if predefined_data_filter_statuses:
                    predefined_data_filter_statuses = dict(sorted(
                        predefined_data_filter_statuses.items(), key=lambda x: (0 if 'id' in x[0] else 1, x[0])))

                fields = base_model._meta.fields
                default_columns_dict = {
                    field.name: field.get_internal_type() for field in fields}

                for data_key, predefined_data_filter in predefined_data_filters.items():
                    data_filter = data_key
                    if data_filter in ['line_item_status', 'checkbox']:
                        continue
                    data_type = "string"
                    field_choice = []
                    from_property = False
                    filter_options = 'is'
                    filter_status = False

                    if data_filter in default_columns_dict.keys():
                        if 'status' in data_filter:
                            custom_field = CustomProperty.objects.filter(
                                workspace=workspace, model=base_model._meta.db_table, name='status'
                            ).first()
                            if custom_field:
                                from_property = True
                                choices = ast.literal_eval(custom_field.value)
                                field_choice = [(value, choices[value])
                                                for value in choices]
                            else:
                                choice_status_display = page_obj.get(
                                    'editable_columns', {}).get(data_filter, {})
                                if choice_status_display:
                                    from_property = True
                                    field_choice = [
                                        (value,
                                         choice_status_display[value][lang])
                                        for value, label in getattr(base_model, data_filter).field.choices
                                    ]
                                else:
                                    field_choice = getattr(
                                        base_model, data_filter).field.choices

                        for check in ["float", "integer"]:
                            type_field = getattr(base_model, data_filter).field.get_internal_type()
                            if check in default_columns_dict[data_filter].lower() and type_field != 'UUIDField':
                                data_type = "number"
                                break
                        for check in ["date"]:
                            if check in default_columns_dict[data_filter].lower():
                                data_type = "date"
                                break

                    elif custom_model:
                        if is_valid_uuid(data_filter):
                            customfield_name = custom_model.objects.filter(
                                id=data_filter).first()
                        else:
                            customfield_name = custom_model.objects.filter(
                                name__iexact=data_filter, workspace=workspace
                            ).first()
                        if customfield_name:
                            if customfield_name.type in ['number', 'formula']:
                                data_type = 'number'
                            elif customfield_name.type in ['date', 'date_time']:
                                data_type = customfield_name.type
                            elif customfield_name.type == 'choice':
                                field_choice = ast.literal_eval(
                                    customfield_name.choice_value)

                        if 'assignee' in data_filter:
                            field_choice = workspace.user.all()

                    if predefined_data_filter:
                        if isinstance(predefined_data_filter, list):
                            pass
                        else:
                            filter_options = predefined_data_filter.get('key')

                    if predefined_data_filter_statuses:
                        predefined_data_filter_status = predefined_data_filter_statuses.get(
                            data_filter, None)
                        if predefined_data_filter_status:
                            if isinstance(predefined_data_filter_status, list):
                                pass
                            else:
                                if predefined_data_filter_status == 'True':
                                    predefined_data_filter_status = True
                                else:
                                    predefined_data_filter_status = False
                                filter_status = predefined_data_filter_status

                    data_filters.append(data_filter)
                    data_types.append(data_type)
                    filter_options_.append(filter_options)
                    filter_statuses.append(filter_status)
                    predefined_data_filters_.append(predefined_data_filter)
                    field_choices.append(field_choice)
                    from_propertys.append(from_property)
                    uuids.append(uuid.uuid4())

                context = {
                    "data_filters": data_filters,
                    "data_types": data_types,
                    "app_type": object_type,
                    "predefined_data_filters": predefined_data_filters_,
                    'field_choices': field_choices,
                    'uuids': uuids,
                    "filter_options_": filter_options_,
                    "filter_statuses": filter_statuses,
                    'is_advanced_search': True,
                    'from_propertys': from_propertys,
                    'object_type': object_type,
                }
                return render(request, 'data/commerce/list-data-filter-selector-loop.html', context)

        # Check if custom_object is defined before using it
        if custom_object is None:
            return redirect(reverse('main', host='app'))

        if view_id:
            view = View.objects.filter(
                workspace=workspace,
                target=TYPE_OBJECT_CUSTOM_OBJECT,
                id=view_id,
                custom_object=custom_object
            ).first()
        else:
            view, _ = View.objects.get_or_create(
                workspace=workspace,
                target=TYPE_OBJECT_CUSTOM_OBJECT,
                title__isnull=True,
                custom_object=custom_object
            )

        view_filter = ViewFilter.objects.filter(view=view).first()

        column_values = ['created_at', 'updated_at']
        property_name = CustomObjectPropertyName.objects.filter(
            workspace=workspace,
            custom_object=custom_object,
        ).order_by('created_at')
        for property in property_name:
            if str(property.id) not in column_values:
                column_values.append(str(property.id))

        related_custom_objects = CustomObject.objects.filter(
            workspace=workspace).exclude(id=custom_object.id)
        for related_co in related_custom_objects:
            column_values.append(
                f'related_custom_object|{related_co.name}|{related_co.id}')

        channels = Channel.objects.filter(
            workspace=get_workspace(request.user),
            integration__slug__in=['hubspot']
        )

        for channel in channels:
            column_values.append(f'{channel.id}_platform_id')

        context = {
            'VIEW_MODE': view_type,
            'view_filter': view_filter,
            'column_values': column_values,
            'module': module,

            'view_id': view.id,
            'view': view,

            'type': type,

            'custom_object': custom_object,
            'object_type': obj_type,
        }

        return render(request, 'data/custom_object/manage_view.html', context)

    elif drawer_type == 'custom-object-view-export-import':
        # Check if custom_object is defined before using it
        if custom_object is None:
            return redirect(reverse('main', host='app'))

        section = request.GET.get('section', None)
        if section == "integrations":
            import_export_type = request.GET.get('import_export_type', None)
            platforms = ['hubspot', 'salesforce']

            count_custom_objects = 0
            custom_object_ids = request.GET.get('custom_object_ids', None)
            if custom_object_ids:
                custom_object_ids = custom_object_ids.split(',')
                count_custom_objects = len(custom_object_ids)
            else:
                count_custom_objects = CustomObjectPropertyRow.objects.filter(
                    workspace=workspace,
                    custom_object=custom_object,
                    usage_status='active'
                ).count()
            channels = Channel.objects.filter(
                workspace=get_workspace(request.user),
                integration__slug__in=platforms
            )

            context = {
                'custom_object_ids': custom_object_ids,
                'channels': channels,
                'count_custom_objects': count_custom_objects,
                'import_export_type': import_export_type,
                'custom_object': custom_object,
                "module": module,
            }

            return render(request, 'data/custom_object/manage-sync-settings-custom-object-import-export.html', context)
        elif section == 'bulk-entry':
            # Check if custom_object is defined before using it
            if custom_object is None:
                return redirect(reverse('main', host='app'))

            context = {
                "custom_object": custom_object
            }
            return render(request, 'data/custom_object/custom-object-create-bulk-entry.html', context)

        elif section == "history":
            # Check if custom_object is defined before using it
            if custom_object is None:
                return redirect(reverse('main', host='app'))

            import_export_type = request.GET.get('import_export_type', None)

            filter_conditions = Q(workspace=workspace)
            if import_export_type == 'import':
                filter_conditions &= Q(type=f"import_{custom_object.slug}")
            elif import_export_type == 'export':
                filter_conditions &= Q(type=f"export_{custom_object.slug}")

            history = TransferHistory.objects.filter(
                filter_conditions).order_by('-created_at')

            context = {"history": history, 'custom_object': custom_object}
            return render(request, 'data/shopturbo/manage-sync-settings-shopturbo-items-import-export-history.html', context)

        elif section == "import_selector":
            # Check if custom_object is defined before using it
            if custom_object is None:
                return redirect(reverse('main', host='app'))

            context = {
                "page_group_type": TYPE_OBJECT_CUSTOM_OBJECT,
                "custom_object_ids": request.GET.get('custom_object_ids', None),
                "view_id": request.GET.get('view_id', None),
                'custom_object': custom_object,
                'module': module
            }
            return render(request, 'data/custom_object/manage-sync-settings-custom-object-import-source-selector.html', context)

        elif section == "export_selector":
            # Check if custom_object is defined before using it
            if custom_object is None:
                return redirect(reverse('main', host='app'))

            context = {
                "page_group_type": TYPE_OBJECT_CUSTOM_OBJECT,
                "view_id": request.GET.get('view_id', None),
                'custom_object': custom_object,
                'module': module
            }
            if request.GET.get('custom_object_ids', None):
                context['custom_object_ids'] = request.GET.get(
                    'custom_object_ids', '')

            return render(request, 'data/custom_object/manage-sync-settings-custom-object-export-source-selector.html', context)

        elif section == 'export_download':
            # Check if custom_object is defined before using it
            if custom_object is None:
                return redirect(reverse('main', host='app'))

            page = request.GET.get('page', False)
            view_type = VIEW_MODE

            column_values = []

            namecustomfieldchoices = []
            view_type = view_type[0:1]

            if view_id:
                view = View.objects.filter(
                    workspace=workspace,
                    target=TYPE_OBJECT_CUSTOM_OBJECT,
                    id=view_id,
                    custom_object=custom_object
                ).first()
            else:
                view, _ = View.objects.get_or_create(
                    workspace=workspace,
                    target=TYPE_OBJECT_CUSTOM_OBJECT,
                    title__isnull=True,
                    custom_object=custom_object
                )

            view_filter = ViewFilter.objects.filter(view=view).first()

            column_values = ['usage_status',
                             'created_at', 'updated_at']
            property_name = CustomObjectPropertyName.objects.filter(
                workspace=workspace,
                custom_object=custom_object,
            ).order_by('created_at')
            for property in property_name:
                if str(property.id) not in column_values:
                    column_values.append(str(property.id))

            related_custom_objects = CustomObject.objects.filter(
                workspace=workspace).exclude(id=custom_object.id)
            for related_co in related_custom_objects:
                column_values.append(
                    f'related_custom_object|{related_co.name}|{related_co.id}')

            channels = Channel.objects.filter(workspace=get_workspace(request.user), integration__slug__in=[
                                              'hubspot', 'salesforce'])
            for channel in channels:
                column_values.append(f'{channel.id}_platform_id')

            namecustomfieldchoices = CustomObjectPropertyName.objects.filter(
                workspace=workspace, custom_object=custom_object, type="choice")
            context = {
                'custom_object': custom_object,
                'VIEW_MODE': view_type,
                'view_filter': view_filter,
                'column_values': column_values,
                'page': 'custom_object',
                'namecustomfieldchoices': namecustomfieldchoices,
                'download_view': True,
                'object_type': TYPE_OBJECT_CUSTOM_OBJECT,
            }
            return render(request, 'data/custom_object/manage-sync-settings-custom-object-export-download.html', context)

        else:
            # Check if custom_object is defined before using it
            if custom_object is None:
                return redirect(reverse('main', host='app'))

            import_export_type = request.GET.get('import_export_type', None)
            view_id = request.GET.get('view_id', None)
            context = {
                'custom_object_ids': request.GET.get("custom_object_ids", ''),
                'custom_object': custom_object,
                "import_export_type": import_export_type,
                "page_group_type": TYPE_OBJECT_CUSTOM_OBJECT,
                "object_type": TYPE_OBJECT_CUSTOM_OBJECT,
                "view_id": view_id,
                'module': module,
            }
            return render(request, 'data/custom_object/manage-sync-settings-custom-object.html', context)

    elif drawer_type == 'create-object':
        # Check if custom_object is defined before using it
        if custom_object is None:
            return redirect(reverse('main', host='app'))

        custom_object_properties = CustomObjectPropertyName.objects.filter(
            custom_object=custom_object)

        association_labels = AssociationLabel.objects.filter(Q(workspace=workspace, object_source=custom_object.id)).order_by('created_at')
        association_label_list = [label.lower() for label in association_labels.values_list('label', flat=True)]

        context = {
            'object_type': TYPE_OBJECT_CUSTOM_OBJECT,
            'custom_object': custom_object,
            'custom_object_properties': custom_object_properties,
            'view_id': view_id,
            'module': module,

            'association': {
                'source': source,
                'object_id': object_id,
                'page': page,
            },
            'association_labels': association_labels,
            'association_label_list': association_label_list,
        }

        return render(request, 'data/custom_object/create_object.html', context)

    elif drawer_type == 'manage':
        # Check if custom_object is defined before using it
        if custom_object is None:
            return redirect(reverse('main', host='app'))

        custom_object_properties = CustomObjectPropertyName.objects.filter(
            custom_object=custom_object)
        row = CustomObjectPropertyRow.objects.filter(
            workspace=workspace, custom_object=custom_object, id=row_id).first()
        if not row:
            return HttpResponse(status=404)

        permission = get_permission(
            TYPE_OBJECT_CUSTOM_OBJECT, request.user, custom_object)
        if not permission:
            permission = DEFAULT_PERMISSION

        contacts = Contact.objects.filter(
            workspace=workspace, custom_object_relation__id=row.id)

        custom_object_relation_rows = CustomObjectPropertyRow.objects.filter(
            workspace=workspace, custom_object_relation__id=row.id)

        custom_objects = CustomObject.objects.filter(
            workspace=workspace).exclude(id=custom_object.id)

        association_labels = AssociationLabel.objects.filter(Q(workspace=workspace, object_source=custom_object.id)).order_by('created_at')
        association_label_list = [label.lower() for label in association_labels.values_list('label', flat=True)]

        # related associate label
        related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False, workspace=workspace, object_target__icontains=custom_object.id).order_by("created_at")

        context = {
            'permission': permission,
            'object_type': TYPE_OBJECT_CUSTOM_OBJECT,
            'custom_object': custom_object,
            'custom_objects': custom_objects,
            'custom_object_properties': custom_object_properties,
            'row': row,
            'view_id': view_id,
            'module': module,
            'contacts': contacts,
            'custom_object_relation_rows': custom_object_relation_rows,
            'object_action': {
                'additional_params': {
                    'row_id': str(row.id),
                    'object_type': TYPE_OBJECT_CUSTOM_OBJECT,
                    'custom_object_id': str(custom_object.id),
                }
            },
            'association_labels':association_labels,
            'association_label_list':association_label_list,
            'related_association_labels': related_association_labels,
        }

        return render(request, 'data/custom_object/manage_object.html', context)

    return HttpResponse(200)
