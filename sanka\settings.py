import os
import sys
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path

import boto3
import environ
from django.utils.translation import gettext_lazy as _

env = environ.Env()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

env.read_env(env_file=str(BASE_DIR) + "/.env")

# SECURITY WARNING: keep the secret key used in production secret
SECRET_KEY = env("SECRET_KEY")

# .env pass string so we're adding bool parser
PROD = env.bool("PROD", default=False)
# .env pass string so we're adding bool parser
STAGING = env.bool("STAGING", default=False)
# .env pass string so we're adding bool parser
LOCAL = env.bool("LOCAL", default=False)
# .env pass string so we're adding bool parser
DEBUG = env.bool("DEBUG", default=False)


ALLOWED_HOSTS = env.list("ALLOWED_HOSTS")
ALLOWED_CIDR_NETS = env.list("ALLOWED_CIDR_NETS")

# Application definition

INSTALLED_APPS = [
    # translation
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",
    "django.contrib.sites",
    "django.contrib.sitemaps",
    # redirect
    "django.contrib.redirects",
    # nondefault
    "data.apps.DataConfig",
    "django.contrib.postgres",
    # Third Party
    "storages",
    "widget_tweaks",
    "django_hosts",
    # test
    "django_extensions",
    "lockdown",  # test
    # REST
    "corsheaders",
    "rest_framework",
    "rest_framework_simplejwt.token_blacklist",
    # DJANGO SAML
    "django_saml",
]

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ],
}

# for local test
if not PROD:
    INSTALLED_APPS.append("sslserver")


# Set the desired maximum memory size (in bytes)
DATA_UPLOAD_MAX_MEMORY_SIZE = 10485760
DATA_UPLOAD_MAX_NUMBER_FILES = 1000
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000000

LOGIN_URL = "/login/"
LOGIN_REDIRECT_URL = "/login/"


MIDDLEWARE = [
    "django_hosts.middleware.HostsRequestMiddleware",
    "sanka.middleware.StreamableGZipMiddleware",  # should these three come first
    # "htmlmin.middleware.HtmlMinifyMiddleware",
    # 'htmlmin.middleware.MarkRequestMiddleware',
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    # language. order must be in this way.
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    # 'django.middleware.csrf.CsrfViewMiddleware',
    "sanka.middleware.ConditionalCsrfMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    # Add RequestMiddleware for thread-local request storage
    "data.templatetags.custom_tags.RequestMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_hosts.middleware.HostsResponseMiddleware",
    "corsheaders.middleware.CorsMiddleware",  # prevent error due CORS rules
    "lockdown.middleware.LockdownMiddleware",  # test
    "sanka.middleware.RequestLoggingMiddleware",  # Add this line for request logging
    "sanka.middleware.Log500ErrorsMiddleware",  # Internal server error logging
    "data.middleware.IPRestrictionMiddleware",
    "sanka.middleware.AllowIframeForSpecificRoutesMiddleware",
    "allow_cidr.middleware.AllowCIDRMiddleware",
    # Add request to user object for workspace caching
    "utils.middleware.RequestUserMiddleware",
]

if DEBUG:
    INSTALLED_APPS.append("debug_toolbar")
    MIDDLEWARE.append("debug_toolbar.middleware.DebugToolbarMiddleware")
    DEBUG_TOOLBAR_CONFIG = {"ROOT_TAG_EXTRA_ATTRS": "hx-preserve"}

LOCKDOWN_PASSWORDS = ("letmein", "beta")
# .env pass string so we're adding bool parser
LOCKDOWN_ENABLED = env.bool("LOCKDOWN", default=False)
LOCKDOWN_URL_EXCEPTIONS = (
    r"^/api/.*$",  # unlock /api/ for staging
    r"^/stripe_webhook/",  # unlock /stripe_webhook/ for staging
)

# IF THESE ARE TURNED ON, SAFARI DOESN'T WORK
CSRF_TRUSTED_ORIGINS = env.list("CSRF_TRUSTED_ORIGINS")
SESSION_COOKIE_SAMESITE = "None"
SESSION_COOKIE_SECURE = True  # Required for SAMESITE=None to work
# CSRF_COOKIE_SECURE = True
# SESSION_COOKIE_SECURE = True
# if PROD == 'True':
#     SECURE_SSL_REDIRECT=True

SESSION_COOKIE_AGE = int(env("SESSION_COOKIE_AGE"))
# Set SESSION_SAVE_EVERY_REQUEST to True to extend the session expiration with each request
SESSION_SAVE_EVERY_REQUEST = env.bool("SESSION_SAVE_EVERY_REQUEST", default=False)

USE_I18N = True
USE_L10N = True


ROOT_URLCONF = "sanka.urls.urls"
ROOT_HOSTCONF = "sanka.hosts"
DEFAULT_HOST = "static"
PARENT_HOST = env("PARENT_HOST")
MAIN_APP_HOST = env("MAIN_APP_HOST", default="")

# For testing and debugging
INTERNAL_IPS = [
    "127.0.0.1",
    "[::1]",
]


# match with the admin site
SITE_ID = int(env("SITE_ID"))
LANGUAGE_CODE = "en"
LOCALE_PATHS = [os.path.join(str(BASE_DIR), "locale")]

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "data.custom_context_processor.custom_list",  # custom_list
            ],
        },
    },
]


WSGI_APPLICATION = "sanka.wsgi.application"
ASGI_APPLICATION = "sanka.asgi.application"


# Database
DATABASES = {
    "default": {
        "ENGINE": env("DB_ENGINE"),
        "NAME": env("DB_NAME"),
        "USER": env("DB_USER"),
        "PASSWORD": env("DB_PASS"),
        "HOST": env("DB_HOST"),
        "PORT": env("DB_PORT"),
        # PROUDCTION CONNECTION
        # 'USER': env('DB_USER_PRODUCTION'),
        # 'PASSWORD': env('DB_PASS_PRODUCTION'),
        # 'HOST': env('DB_HOST_PRODUCTION'),
        # 'POOL_OPTIONS' : {
        #     'POOL_SIZE': int(env('DB_POOL_SIZE')),
        #     'MAX_OVERFLOW': int(env('DB_MAX_OVERFLOW')),
        # }
    },
}


# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Custom Auth Backend for Email
AUTHENTICATION_BACKENDS = [
    "sanka.backends.ExtendedUserModelBackend",
]


# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/
USE_I18N = True
LANGUAGE_CODE = "en"

LANGUAGES = [
    ("en", _("English")),
    ("ja", _("Japanese")),
]

TIME_ZONE = "UTC"

USE_TZ = True


# Static & Media Storage

# Storage Settings
USE_SPACE = env("USE_SPACE")

AWS_ACCESS_KEY_ID = env("AWS_ACCESS_KEY")
AWS_SECRET_ACCESS_KEY = env("AWS_SECRET_KEY")
AWS_STORAGE_BUCKET_NAME = "sankafile"
AWS_DEFAULT_ACL = "public-read"
AWS_S3_REGION_NAME = "nyc3"
AWS_S3_ENDPOINT_URL = "https://nyc3.digitaloceanspaces.com"
AWS_S3_OBJECT_PARAMETERS = {"CacheControl": "max-age=86400"}
AWS_LOCATION = "static"
MEDIA_LOCATION = "media"
MEDIA_URL = f"{AWS_S3_ENDPOINT_URL}/{MEDIA_LOCATION}/"
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"
DEFAULT_FILE_STORAGE = "sanka.storage_backends.PublicMediaStorage"
# private media settings
PRIVATE_MEDIA_LOCATION = "private"
PRIVATE_FILE_STORAGE = "sanka.storage_backends.PrivateMediaStorage"

if USE_SPACE == "True":
    STATIC_URL = f"{AWS_S3_ENDPOINT_URL}/{AWS_LOCATION}/"
else:
    STATIC_URL = "static/"

STATIC_ROOT = BASE_DIR / "staticfiles"
STATICFILES_DIRS = [
    BASE_DIR / "static",
]

# Fields
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


session = boto3.session.Session()
S3_CLIENT = session.client(
    "s3",
    # Find your endpoint in the control panel, under Settings. Prepend "https://".
    endpoint_url="https://nyc3.digitaloceanspaces.com",
    # Use the region in your endpoint.
    region_name="nyc3",
    # Access key pair. You can create access key pairs using the control panel or API.
    aws_access_key_id=env("AWS_ACCESS_KEY"),
    # Secret access key defined through an environment variable.
    aws_secret_access_key=env("AWS_SECRET_KEY"),
)

# Email
EMAIL_BACKEND = "sendgrid_backend.SendgridBackend"
DEFAULT_FROM_EMAIL = "<EMAIL>"
SENDGRID_API_KEY = env("SENDGRID_API_KEY")
# Set to True for local development to avoid actual email sending
SENDGRID_SANDBOX_MODE_IN_DEBUG = env.bool(
    "SENDGRID_SANDBOX_MODE_IN_DEBUG", default=False
)

SITE_URL = env("SITE_URL")
ACTIVATION_URL = env("ACTIVATION_URL")
DEFAULT_EMAIL_FROM = "Sanka <<EMAIL>>"
SITE_URL_STATIC = env("SITE_URL_STATIC")

APPEND_SLASH = True


# Twitter
TW_API_KEY = env("TW_API_KEY", default="")
TW_API_PASS = env("TW_API_PASS", default="")
TW_CLIENT_ID = env("TW_CLIENT_ID", default="")
TW_CLIENT_SECRET = env("TW_CLIENT_SECRET", default="")
TW_CALLBACK_URL = env("TW_CALLBACK_URL", default="")
TW_API_BEARER = env("TW_API_BEARER", default="")
TW_ACCESS = env("TW_ACCESS")
TW_ACCESS_PASS = env("TW_ACCESS_PASS")

# Shopify
SHOPIFY_API_VERSION = env("SHOPIFY_API_VERSION", default="2025-07")
SHOPIFY_CLIENT_ID = env("SHOPIFY_CLIENT_ID", default="")
SHOPIFY_CLIENT_SECRET = env("SHOPIFY_CLIENT_SECRET", default="")
SHOPIFY_CALLBACK_URL = env("SHOPIFY_CALLBACK_URL", default="")


# Square
SQUARE_APP_ID = env("SQUARE_APP_ID", default="")
SQUARE_APP_SECRET = env("SQUARE_APP_SECRET", default="")

# Facebook
FB_CLIENT_ID = env("FB_CLIENT_ID", default="")
FB_CLIENT_SECRET = env("FB_CLIENT_SECRET", default="")
FB_CALLBACK_URL = env("FB_CALLBACK_URL", default="")
FB_VERIFY_TOKEN = env("FB_VERIFY_TOKEN", default="")
FB_CONFIG_ID = env("FB_CONFIG_ID", default="")

# EC CUBE
ECCUBE_CALLBACK_URL = env("ECCUBE_CALLBACK_URL")

# FREEE
FREEE_CLIENT_ID = env("FREEE_CLIENT_ID", default="")
FREEE_CLIENT_SECRET = env("FREEE_CLIENT_SECRET", default="")
FREEE_CALLBACK_URL = env("FREEE_CALLBACK_URL", default="")

# YAHOO
YAHOO_CLIENT_ID = env("YAHOO_CLIENT_ID", default="")
YAHOO_CLIENT_SECRET = env("YAHOO_CLIENT_SECRET", default="")
YAHOO_CALLBACK_URL = env("YAHOO_CALLBACK_URL", default="")

# Makeshop
MAKESHOP_API_KEY = env("MAKESHOP_API_KEY", default="")

# Amazon
AMAZON_CLIENT_ID = env("AMAZON_CLIENT_ID", default="")
AMAZON_CLIENT_SECRET = env("AMAZON_CLIENT_SECRET", default="")
AMAZON_CALLBACK_URL = env("AMAZON_CALLBACK_URL", default="")

# Open AI
OPENAI_API_KEY = env("OPENAI_API_KEY", default="")


# Etherscan
ETHERSCAN = env("ETHERSCAN", default="")

# Stripe
STRIPE_PUBLISHABLE_KEY = env("STRIPE_PUBLISHABLE_KEY", default="")
STRIPE_SECRET_KEY = env("STRIPE_SECRET_KEY", default="")
STRIPE_PRICE_ID_TEAM = env("STRIPE_PRICE_ID_TEAM", default="")
STRIPE_ENDPOINT_SECRET = env("STRIPE_ENDPOINT_SECRET", default="")

# Rapid API
RAPID_API_KEY = env("RAPID_API_KEY", default="")

# Google API
GOOGLE_CLIENT_ID = env("GOOGLE_CLIENT_ID", default="")
GOOGLE_CLIENT_SECRET = env("GOOGLE_CLIENT_SECRET", default="")
GOOGLE_CALLBACK_URL = env("GOOGLE_CALLBACK_URL", default="")
GOOGLE_CLIENT_SECRET_FILE = env("GOOGLE_CLIENT_SECRET_FILE", default="")
GOOGLE_DEVELOPER_TOKEN = env("GOOGLE_DEVELOPER_TOKEN", default="")

# NEXT ENGINE API
if LOCAL:
    NEXT_ENGINE_CLIENT_ID = env("DEV_NEXT_ENGINE_CLIENT_ID", default="")
    NEXT_ENGINE_CLIENT_SECRET = env("DEV_NEXT_ENGINE_CLIENT_SECRET", default="")
    NEXT_ENGINE_CALLBACK_URL = env("DEV_NEXT_ENGINE_CALLBACK_URL", default="")
elif STAGING:
    NEXT_ENGINE_CLIENT_ID = env("STAGING_NEXT_ENGINE_CLIENT_ID", default="")
    NEXT_ENGINE_CLIENT_SECRET = env("STAGING_NEXT_ENGINE_CLIENT_SECRET", default="")
    NEXT_ENGINE_CALLBACK_URL = env("STAGING_NEXT_ENGINE_CALLBACK_URL", default="")
elif PROD:
    NEXT_ENGINE_CLIENT_ID = env("NEXT_ENGINE_CLIENT_ID", default="")
    NEXT_ENGINE_CLIENT_SECRET = env("NEXT_ENGINE_CLIENT_SECRET", default="")
    NEXT_ENGINE_CALLBACK_URL = env("NEXT_ENGINE_CALLBACK_URL", default="")

# Tiktok API
TIKTOK_CLIENT_ID = env("TIKTOK_CLIENT_ID", default="")
TIKTOK_CLIENT_SECRET = env("TIKTOK_CLIENT_SECRET", default="")
TIKTOK_CLIENT_KEY = env("TIKTOK_CLIENT_KEY", default="")
TIKTOK_CALLBACK_URL = env("TIKTOK_CALLBACK_URL", default="")

# Posthog
POSTHOG_API_KEY = env("POSTHOG_API_KEY", default="")

# GitHub API
GITHUB_TOKEN = env("GITHUB_TOKEN", default="")

# Azure API
AZURE_API_KEY = env("AZURE_API_KEY", default="")

REPLICATE_API_TOKEN = env("REPLICATE_API_TOKEN", default="")

MICROSOFT_CALLBACK_URL = env("MICROSOFT_CALLBACK_URL", default="")

# MULTI THREAD
THREAD_NUMBER = env("THREAD_NUMBER", default="")

# CHROME DRIVER PATH
CHROME_DRIVER_PATH = env("CHROME_DRIVER_PATH", default="")
TWITTER_USERNAME = env("TWITTER_USERNAME", default="")
TWITTER_PASSWORD = env("TWITTER_PASSWORD", default="")
SITE_KEY_RECAPTCHA = env("SITE_KEY_RECAPTCHA", default="")

# AZURE QUEUE STORAGE SETTINGS
AZURE_STORAGE_CONNECTION_STRING = env("AZURE_STORAGE_CONNECTION_STRING", default="")

# AZURE SANKA STORAGE SETTINGS
AZURE_BLOB_STORAGE_CONNECTION_STRING = env(
    "AZURE_BLOB_STORAGE_CONNECTION_STRING", default=""
)
AZURE_BLOB_CONTAINER_NAME = env("AZURE_BLOB_CONTAINER_NAME", default="")

# AZURE LOGGING SETTINGS
APPLICATIONINSIGHTS_CONNECTION_STRING = env(
    "APPLICATIONINSIGHTS_CONNECTION_STRING", default=""
)

# ENCRYPTION
FERNET_KEY = env("FERNET_KEY", str)


# If this is used then `CORS_ALLOWED_ORIGINS` will not have any effect
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=30),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=60),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
}

# CORS_WHITE_LIST = env.list('CORS_WHITE_LIST')
# CORS_ORIGIN_WHITELIST = CORS_WHITE_LIST


# PROXY
PROXY_HOST = env("PROXY_HOST", default="")
PROXY_USERNAME = env("PROXY_USERNAME", default="")
PROXY_PASSWORD = env("PROXY_PASSWORD", default="")

# SCRAPEIO
SCRAPEIO_API_KEY = env("SCRAPEIO_API_KEY", default="")

# GOOGLE SPEECH
GOOGLE_SPEECH_SERVICE_ACCOUNT = env("GOOGLE_SPEECH_SERVICE_ACCOUNT", default="")

# DEEPGRAM
DEEPGRAM_API_KEY = env("DEEPGRAM_API_KEY", default="")

# AVATAX
AVATAX_CLIENT_ID = env("AVATAX_CLIENT_ID", default="")
AVATAX_KEY = env("AVATAX_KEY", default="")

DISCORD_WEBHOOK_URL = env("DISCORD_WEBHOOK_URL", default="")
DISCORD_OWNER_ID = env("DISCORD_OWNER_ID", default="")
# SECURE_SSL_REDIRECT  = True
# SESSION_COOKIE_SECURE = True
# CSRF_COOKIE_SECURE = True

LOGIC_APP_URL = env("LOGIC_APP_URL", default="")

WORKFLOW_GALLERY_CREATOR_WORKSPACE = env(
    "WORKFLOW_GALLERY_CREATOR_WORKSPACE", default=""
)

# Debug
if PROD or STAGING:
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration

    sentry_sdk.init(
        dsn="https://<EMAIL>/6586186",
        integrations=[
            DjangoIntegration(),
        ],
        _experiments={
            "enable_logs": True,
        },
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
    )


LOGTAIL_SOURCE_TOKEN = env("LOGTAIL_SOURCE_TOKEN", default="")
LOGTAIL_HOST = env("LOGTAIL_HOST", default="")


# LOGGING CONFIGURATION
if PROD or STAGING:
    # Base logging configuration - start with console only
    handlers = ["console", "logtail"]

    LOGGING = {
        "version": 1,
        "disable_existing_loggers": False,
        "filters": {
            "azure_size_limit": {
                "()": "sanka.logging_filters.AzureSizeLimitFilter",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "stream": sys.stdout,
            },
            "logtail": {
                "class": "logtail.LogtailHandler",
                "source_token": LOGTAIL_SOURCE_TOKEN,
                "host": LOGTAIL_HOST,
            },
        },
        "root": {
            "handlers": handlers,
            "level": "INFO",
        },
        "loggers": {
            # Django's internal loggers - keep them at WARNING to reduce noise
            "django": {"level": "WARNING", "propagate": True},
            "django.db.backends": {"level": "WARNING", "propagate": True},
            "django.request": {"level": "WARNING", "propagate": True},
            # Sanka app logger - set to INFO to capture application logs
            "sanka": {"level": "INFO", "propagate": True},
        },
    }

    # Only add Azure handler if connection string is provided and valid
    # A valid connection string should contain 'InstrumentationKey='
    if (
        APPLICATIONINSIGHTS_CONNECTION_STRING
        and APPLICATIONINSIGHTS_CONNECTION_STRING.strip()
        and "InstrumentationKey=" in APPLICATIONINSIGHTS_CONNECTION_STRING
    ):
        LOGGING["handlers"]["azure"] = {
            "class": "opencensus.ext.azure.log_exporter.AzureLogHandler",
            "connection_string": APPLICATIONINSIGHTS_CONNECTION_STRING,
            "filters": ["azure_size_limit"],
        }
        LOGGING["root"]["handlers"].append("azure")

        # Prevent Azure telemetry from logging its own errors to avoid infinite loops
        LOGGING["loggers"]["opencensus"] = {
            "level": "ERROR",
            "handlers": ["console"],  # Only log to console, not back to Azure
            "propagate": False,  # Don't propagate to root logger
        }
        LOGGING["loggers"]["opencensus.ext.azure"] = {
            "level": "ERROR",
            "handlers": ["console"],  # Only log to console, not back to Azure
            "propagate": False,  # Don't propagate to root logger
        }
else:
    LOGGING = {
        "version": 1,
        "disable_existing_loggers": False,  # retain the default loggers
        "formatters": {
            "verbose": {
                "format": "{levelname} {asctime} {module} {message}",
                "style": "{",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "verbose",
                "stream": sys.stdout,
            },
        },
        "root": {
            "handlers": ["console"],
            "level": "INFO",
        },
        "loggers": {
            # Django's internal loggers - keep them at WARNING to reduce noise
            "django": {"level": "WARNING", "propagate": True},
            "django.db.backends": {"level": "WARNING", "propagate": True},
            "django.request": {"level": "WARNING", "propagate": True},
            # Sanka app logger - set to INFO to capture application logs
            "sanka": {"level": "INFO", "propagate": True},
        },
    }

TEMU_APP_KEY = env("TEMU_APP_KEY", default="")
TEMU_APP_SECRET = env("TEMU_APP_SECRET", default="")
TEMU_API_URL = env("TEMU_API_URL", default="")

HUBSPOT_CLIENT_ID = env("HUBSPOT_CLIENT_ID", default="")
HUBSPOT_CLIENT_SECRET = env("HUBSPOT_CLIENT_SECRET", default="")
HUBSPOT_APP_ID = env("HUBSPOT_APP_ID", default="3893571")
HUBSPOT_REDIRECT_URI = env("HUBSPOT_REDIRECT_URI", default="")

HUBSPOT_INTERNAL_ACCESS_TOKEN = env("HUBSPOT_INTERNAL_ACCESS_TOKEN", default="")
HUBSPOT_INTERNAL_CLIENT_SECRET = env("HUBSPOT_INTERNAL_CLIENT_SECRET", default="")


HUBSPOT_SCOPES = (
    "settings.users.read "
    "crm.lists.read "
    "crm.lists.write "
    "crm.objects.owners.read "
    "crm.objects.companies.read "
    "crm.objects.companies.write "
    "crm.objects.contacts.read "
    "crm.objects.contacts.write "
    "crm.objects.custom.read "
    "crm.objects.custom.write "
    "crm.objects.deals.read "
    "crm.objects.deals.write "
    "crm.objects.line_items.read "
    "crm.objects.line_items.write "
    "crm.schemas.companies.read "
    "crm.schemas.companies.write "
    "crm.schemas.contacts.read "
    "crm.schemas.contacts.write "
    "crm.schemas.custom.read "
    "crm.schemas.deals.read "
    "crm.schemas.deals.write "
    "e-commerce "
    "tickets "
    "oauth "
    "tax_rates.read"
)


CORS_ALLOWED_ORIGINS = ["https://app.hubspot.com"]

USE_X_FORWARDED_HOST = True
USE_X_FORWARDED_PORT = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# SAML SETTINGS
SAML_SP = {
    "entityId": f"{env('SP_ENTITY_ID', default='https://app.sanka.com/okta/saml/acs/')}",
    "assertionConsumerService": {
        "url": f"{env('SP_ACS_URL', default='https://app.sanka.com/okta/saml/acs/')}",
        # DO NOT CHANGE THIS
        "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST",
    },
    "NameIDFormat": "urn:oasis:names:tc:SAML:2.0:nameid-format:unspecified",
}
SAML_IDP = {
    "entityId": f"{env('IDP_ENTITY_ID', default='')}",
    "singleSignOnService": {
        "url": f"{env('IDP_SSO_URL', default='')}",
        "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect",
    },
    "x509cert": f"{env('IDP_CERT', default='')}",
}

SF_CLIENT_ID = env("SF_CLIENT_ID", default="")
SF_CLIENT_SECRET = env("SF_CLIENT_SECRET", default="")
SF_REDIRECT_URI = env("SF_REDIRECT_URI", default="")

EBAY_CLIENT_ID = env("EBAY_CLIENT_ID", default="")
EBAY_CLIENT_SECRET = env("EBAY_CLIENT_SECRET", default="")
EBAY_REDIRECT_URI = env("EBAY_REDIRECT_URI", default="")
EBAY_DEV_ID = env("EBAY_DEV_ID", default="")
EBAY_VERIFICATION_TOKEN = env("EBAY_VERIFICATION_TOKEN", default="")
EBAY_VERIFICATION_CALLBACK_URL = env("EBAY_VERIFICATION_CALLBACK_URL", default="")

MONEYFORWARD_CLIENT_ID = env("MONEYFORWARD_CLIENT_ID", default="")
MONEYFORWARD_CLIENT_SECRET = env("MONEYFORWARD_CLIENT_SECRET", default="")
MONEYFORWARD_REDIRECT_URI = env("MONEYFORWARD_REDIRECT_URI", default="")

SLACK_ERROR_NOFIF_INTERVAL = env("SLACK_ERROR_NOFIF_INTERVAL", default=60)  # in minutes


HATCHET_CLIENT_TOKEN = env("HATCHET_CLIENT_TOKEN", default="***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
