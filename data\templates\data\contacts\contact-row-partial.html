{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}

{% comment %} Debug: Contact row rendering {% endcomment %}
<!-- DEBUG: Rendering contact row for Contact ID: {{ contact.contact_id }}, Name: {{ contact.name }} {{ contact.last_name }} -->


{% for row_type in contacts_columns %}

    {% if "checkbox" == row_type %}
    <td class="text-nowrap w-10px">
        <input style="" 
            id="contact-selection-{{contact.id}}" 
            class="form-check-input cursor-pointer contact-selection check_input" 
            type="checkbox" name="checkbox" 
            value="{{contact.id}}"
            onclick="checking_checkbox(this, event)"
            data-owner="{{contact.owner.user.id}}"
        />
    </td>

    {% elif "image_url" == row_type %}
    <td class="text-nowrap">
        <a class="text-dark cursor-pointer customer-manage-wizard contact_{{contact.id}}"
            hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
            hx-target="#customer-manage-drawer"
            hx-trigger="click"
            >  
        
            {% if contact.image_url %}
            <div class="symbol symbol-30px">
                <img alt="Pic" src="{{ contact.image_url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
            </div>
            {% elif contact.image_file %}
            <div class="symbol symbol-30px">
                <img alt="Pic" src="{{ contact.image_file.url }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
            </div>
            {% else %}
            <span class="svg-icon svg-icon-muted svg-icon-2x">
                <svg class="h-30px w-30px" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor"/>
                <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor"/>
                <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor"/>
                </svg>
            </span>    
            {% endif %}
        </a>  
    </td>

    {% elif "lists" == row_type %}
    <td class="text-nowrap">
        {% for list in contact.contactlist.all %}
            <div class="rounded-2 p-2 bg-gray-200 mb-2" style="">
                {{list.name}}
            </div>
        {% endfor %}
    </td>

    {% elif "contact_id" == row_type %}
    <td class="fw-bolder text-nowrap special-col min-w-50px" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
        {% if contact.contact_id %}
        <div class="d-flex align-items-center">
            <span class="d-none shopturbo-manage-wizard-button contact_{{contact.id}}_activity"
                hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                hx-vals='{"tab": "activity", "view_id":"{{view_id}}"}'
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                hx-on::before-request="document.getElementById('customer-manage-drawer').innerHTML = '';"
                >
            </span>
            <a class="text-dark text-hover-primary cursor-pointer customer-manage-wizard view_form_trigger{{contact.id}} contact_{{contact.id}}"
                hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                hx-vals='{"view_id":"{{view_id}}"}'
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                hx-on::before-request="document.getElementById('customer-manage-drawer').innerHTML = '';"
                > 
                {{contact.contact_id|stringformat:"04d"}} 
            </a>
            {% if property_sets %}
            <div class="dropdown">
                <button type="button" class="btn btn-primary-outline text-dark text-hover-primary px-1 py-0 dropdown-toggle" data-bs-toggle="dropdown">
                    <span class="d-inline-flex align-items-center justify-content-center" style="width: 20px; height: 20px;">
                        <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu">
                    {% for set in property_sets %}
                    <li>
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden customer-manage-wizard" type="button"
                            hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                            hx-vals = '{"set_id": "{{set.id}}","view_id":"{{view_id}}"}'
                            hx-target="#customer-manage-drawer"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                            {% endif %}
                        </button>
                    </li>
                    {% endfor %}
                </ul>
            </div>  
            {% endif %}
        </div>
        {% endif %}
    </td>

    {% elif "name" == row_type %}
        <td class="fw-bolder text-nowrap">
            <a class="text-dark cursor-pointer customer-manage-wizard contact_{{contact.id}}"
                hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                > 
                {{contact|display_contact_name:LANGUAGE_CODE}} 
            </a>
        </td>
    {% elif "owner" == row_type %}
    <td class="fw-bold">
        {% if contact.owner and contact.owner.user %}
            {% with contact.owner.user as user %}
            {% if user.verification.profile_photo %}
            <img class="w-20px rounded-circle me-2" src="{{user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
            {% else %}
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                    <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                </svg>    
            {% endif %}
            <span >
                {{user.first_name}}
            </span>
            {% endwith %}
        {% endif %}
    </td>
    {% elif "first_name" == row_type %}
        <td class="fw-bolder text-nowrap">
            {% if contact.name %}
            <a class="text-dark cursor-pointer customer-manage-wizard contact_{{contact.id}}"
                hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                > 
                {{contact.name}} 
            </a>
            {% endif %}
        </td>

    {% elif "last_name" == row_type %}
    <td class="fw-bolder text-nowrap">
        {% if contact.last_name %}
            <a class="text-dark cursor-pointer customer-manage-wizard contact_{{contact.id}}"
                hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                hx-target="#customer-manage-drawer"
                hx-trigger="click"
                > 
                {{contact.last_name}} 
            </a>
        {% endif %}
    </td>

    {% elif "email" == row_type %}
    <td class="text-nowrap">
        {% if contact.email %}
            {{contact.email}}
        {% endif %}
    </td>
    {% elif "phone_number" == row_type %}
    <td class="text-nowrap">
        {% if contact.phone_number %}
            {{contact.phone_number}}
        {% endif %}
    </td>

    
    {% elif "created_at" == row_type %}
    <td class="text-nowrap">
        {% if contact.created_at %}
            {% date_format contact.created_at 1 %}
        {% endif %}
    </td>


    {% elif "updated_at" == row_type %}
    <td class="text-nowrap">
        {% if contact.updated_at %}
            {% date_format contact.updated_at 1 %}
        {% endif %}
    </td>


    {% elif "company" == row_type %}
    <td class="text-nowrap align-items-center">
        <div class="d-flex">
            {% for company in contact.companies.all %} 
            <div class="d-inline-block rounded-2 p-2 fw-bolder me-1" style="">
                <a class="text-dark cursor-pointer customer-manage-wizard fs-7 company_{{company.id}}"
                    hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                    hx-target="#customer-manage-drawer"
                    hx-trigger="click"
                    >  
                {{company.name}}
                </a>
            </div>
            {% endfor %}
        </div>
    </td>

    {% elif "location" == row_type %}
    <td class="text-nowrap">
        {% if contact.location %}
            {{contact.location}}
        {% endif %}
    </td>

    {% elif "lang" == row_type %}
    <td class="text-nowrap">
        {% if contact.lang %}
            {{contact.lang}}
        {% endif %}
    </td>

    {% elif "status" == row_type %}
    <td class="fw-bold text-nowrap">
        
        {% translate_lang contact.get_status_display LANGUAGE_CODE %}
        
    </td>
    {% elif "platform_id" == row_type %}
    <td class="fw-bolder text-nowrap">
    </td>

    {% elif " - line user id" in row_type|lower %}
    <td class="fw-bold text-nowrap">
        {% with contact_line_channels=contact|search_contact_line_channel_objects:request %}
            {% if contact_line_channels %}
                {% for contact_line_channel in contact_line_channels %}
                    {% if contact_line_channel.channel.id|stringify == row_type|get_line_channel_id %}
                    <span class="badge bg-gray-500">{{contact_line_channel.line_channel_id}}</span>
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endwith %}
    </td>

    {% elif " - contact id" in row_type|lower %}
    <td class="fw-bold text-nowrap">
        {% with contact_platforms=contact|search_contact_channel_objects:request %}
            {% if contact_platforms %}
                {% for contact_platform in contact_platforms %}
                    <div>
                        {{contact.id|contact_platform_name:row_type}}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </td>

    {% elif row_type|is_uuid %}
        <td class="fw-bold">
            <div class="text-start">
                {% with item_id=contact.id|stringify %}
                    {% with args=row_type|add:'|'|add:object_type|add:'|'|add:item_id %} 
                        {% with column_display=args|get_column_display:request %}
                            {% if column_display.type == 'formula' %}
                                {% include "data/common/custom_field/formula/row-partial.html" with obj_id=contact.id CustomFieldName_id=column_display.customfield_name.id %}   
                            {% elif column_display.type == 'image' %}
                                {% include "data/common/custom_field/row-partial-list.html" with CustomFieldName=column_display.customfield_name obj_id=column_display.value value=column_display.id|get_value_custom_field_contacts:contact.id %}                 
                            {% else %}
                            
                                {% include "data/common/custom_field/row-partial-list.html" with CustomFieldName=column_display.customfield_name obj_id=column_display.value value=column_display.value %}
                            
                            {% endif %}
                        {% endwith %}
                    {% endwith %}   
                {% endwith%}
            </div>
        </td>   

    {% else %}
        <td class="fw-bold">
            <div class="text-start">
                {% if contact|get_attr:row_type %}
                    {{contact|get_attr:row_type}}
                {% endif %}
            </div>
        </td>
    
    {% endif %}
    

{% endfor %}
