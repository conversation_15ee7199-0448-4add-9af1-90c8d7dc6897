import uuid
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User

from data.models.constant import *
from data.constants.constant import *

from data.models.base import save_tags_values, validate_value_custom_field


EVENT_CF_TYPES = [
    ('string', 'STRING'),
    ('numeric', 'NUMERIC'),
    ('date', 'Date'),
    ('date_time', 'Date Time'),
    ('boolean', 'BOOLEAN')
]

EVENT_DATA_TYPES = [
    ('parameter', 'Parameter'),
    ('data', 'Event Data'),
]


class EventNameCustomProperties(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    property_name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=EVENT_CF_TYPES,
                            max_length=20, null=True, blank=True)
    event_data_type = models.CharField(
        choices=EVENT_DATA_TYPES, max_length=20, null=True, blank=True)
    is_default = models.BooleanField(default=False)
    number_format = models.CharField(
        choices=NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    example = models.TextField(null=True, blank=True)
    order = models.CharField(max_length=800, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class Report(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.SET_NULL, blank=True, null=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    name_ja = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    description_ja = models.TextField(null=True, blank=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, related_name="report", blank=True, null=True)
    comparison_channel = models.ManyToManyField(
        "Channel", related_name="competitors")
    metrices = models.TextField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    web_url = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True, related_name="reports_created")
    updated_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True, related_name="reports_updated")
    is_template = models.BooleanField(default=False)
    type = models.CharField(max_length=200, null=True, blank=True)

    owner = models.ForeignKey('UserManagement', on_delete=models.SET_NULL, null=True, blank=True)

    def get_metrices(self):
        if self.metrices:
            return self.metrices.split(",")
        return []

    def get_competitors(self):
        if self.comparison_channel.count() > 0:
            return list(self.comparison_channel.values_list('account_username', flat=True))
        return []

    def get_panels(self):
        panels = ReportPanel.objects.filter(
            panelreportpanel__report=self).order_by('panelreportpanel__order')
        return panels


# ShopTurboShopTurbo
SHOPTURBO_NUMBER_FORMAT = [
    ('number', 'Number'),
    ('%', '%'),
    ('usd', 'USD'),
    ('jpy', 'JPY'),
    ('idr', 'IDR'),
]


class ReportNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=OBJECTS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    taxes = models.CharField(max_length=800, null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    is_association = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    sub_property = models.TextField(null=True, blank=True)
    source_property = models.TextField(null=True, blank=True)
    pair_property = models.TextField(null=True, blank=True)
    sync_conditions = models.TextField(null=True, blank=True)

    def __str__(self):
        return str(self.workspace.name) + ' - ' + str(self.type) + " - " + str(self.name) + " - " + str(self.id)


class ReportValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ReportNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    reports = models.ForeignKey(Report, on_delete=models.CASCADE, null=True,
                                blank=True, related_name='report_custom_field_relations')
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(verbose_name="Custom Field file",
                            upload_to="report-customfield-files", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)
    value_time_end = models.DateTimeField(
        null=True, blank=True)        # Use for date_range field

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)  # Log
            self._log_data = log_data

        if self.field_name and (self.field_name.type == 'tag'):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class ReportValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(verbose_name="Report Custom Field file",
                            upload_to="report-custom-field-files")
    valuecustomfield = models.ForeignKey(
        ReportValueCustomField, on_delete=models.CASCADE, null=True, blank=True)
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default='image')
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


PANEL_METRIC_ROLE = [
    ('x_axis', 'X Axis'),
    ('y_axis', 'Y Axis'),
    ('column', 'Column'),
    ('values', 'Values'),  # Pivot value
    ('row', 'Row'),  # Pivot rows
]


SORT_TYPE = [
    ('asc', 'Ascending'),
    ('des', 'Descending'),
]


METRIC_TYPE = [
    ('new', 'New'),
    ('cumulative', 'Cumulative'),
    ('total_revenue', 'Total Revenue'),
    ('daily_revenue', 'Daily Revenue'),
    ('manual', 'Manual'),
]


class PanelMetric(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(blank=True, null=True, max_length=255)
    metric = models.TextField(blank=True, null=True)
    metric_type = models.CharField(
        choices=METRIC_TYPE, max_length=20, blank=True, null=True)
    role = models.CharField(choices=PANEL_METRIC_ROLE,
                            max_length=50, blank=True, null=True)
    order = models.IntegerField(default=0)
    filter = models.JSONField(null=True)
    created_at = models.DateTimeField(default=timezone.now)
    data_source = models.CharField(blank=True, null=True, max_length=50)
    sort = models.CharField(choices=SORT_TYPE,
                            max_length=10, blank=True, null=True)
    meta_data = models.JSONField(null=True, blank=True)
    raw_sql = models.TextField(null=True, blank=True)
    display_result = models.CharField(max_length=50, blank=True, null=True)


PANEL_DATA_SOURCE_TYPE = [
    ('integration', 'Integration'),
    ('app', 'App'),
    ('custom_data_source', 'Custom Data Source'),
]

PANEL_TYPE = [
    ('chart', 'Chart'),
    ('stacked_chart', 'Stacked Chart'),
    ('table', 'Table'),
    ('pivot', 'Pivot Table'),
    ('summary_table', 'Summary Table'),
    ('forecast', 'Forecast'),
]


class ReportPanel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    panel_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    name_ja = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    description_ja = models.TextField(null=True, blank=True)
    data_source_type = models.CharField(
        choices=PANEL_DATA_SOURCE_TYPE, max_length=50, blank=True, null=True)
    data_source = models.CharField(max_length=250, blank=True, null=True)
    sheet_rows = models.CharField(max_length=250, blank=True, null=True)
    panel_type = models.CharField(
        choices=PANEL_TYPE, max_length=50, blank=True, null=True)
    metrics = models.ManyToManyField(PanelMetric, blank=True)
    filter = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True, related_name="panel_channel")
    comparison_channel = models.ManyToManyField(
        "Channel", related_name="panel_competitors")
    metrices = models.TextField(null=True, blank=True)
    breakdown = models.TextField(null=True, blank=True)
    x_axis = models.TextField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    web_url = models.TextField(null=True, blank=True)
    ratio = models.IntegerField(default=50)
    is_template = models.BooleanField(default=False)
    group_by = models.CharField(max_length=50, blank=True, null=True)
    row_sort_direction = models.CharField(max_length=15, blank=True, null=True)
    start_time = models.DateTimeField(blank=True, null=True)
    end_time = models.DateTimeField(blank=True, null=True)
    date_format = models.CharField(max_length=50, blank=True, null=True)
    hide_sheet_header = models.BooleanField(blank=True, null=True)
    blank_space_sheet = models.IntegerField(blank=True, null=True)
    hide_rows = models.CharField(max_length=100, blank=True, null=True)
    hide_cols = models.CharField(max_length=100, blank=True, null=True)
    is_duplicate_header = models.BooleanField(default=True)
    panel_property = models.JSONField(blank=True, null=True)
    is_realtime = models.BooleanField(default=True)
    meta_data = models.JSONField(null=True, blank=True)
    type_objects = models.TextField(blank=True, null=True)
    sheet_expanding_item = models.CharField(
        max_length=50, blank=True, null=True)
    sheet_template = models.CharField(max_length=200, blank=True, null=True)
    cohort_type = models.CharField(max_length=50, blank=True, null=True)
    is_stacked_chart = models.BooleanField(default=False)
    is_forecast = models.BooleanField(default=False)
    sheet_group_by = models.CharField(max_length=100, blank=True, null=True)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)

    owner = models.ForeignKey('UserManagement', on_delete=models.SET_NULL, null=True, blank=True)

    def save(self, *args, **kwargs):
        id_field = 'panel_id'
        if not getattr(self, id_field):
            # Generate a new order_id if it doesn't exist
            last_item = self.__class__.objects.filter(
                workspace=self.workspace, panel_id__isnull=False).order_by(f'-{id_field}').first()
            if last_item:
                if getattr(last_item, id_field):
                    try:
                        last_id = int(getattr(last_item, id_field))
                    except:
                        last_id = 0

                    next_id = last_id + 1
                else:
                    next_id = 1
            else:
                # If it's the first order, start with 1
                next_id = 1

            # Format the order_id as a three-digit string
            setattr(self, id_field, f"{next_id:04d}")

        super().save(*args, **kwargs)

    def get_reports(self):
        reports = Report.objects.filter(panelreportpanel__panel=self)
        return reports

    def list_metric_names(self):
        data_sources = self.data_source.split(',')
        queryset = self.metrics.filter(
            role='y_axis').values('metric', 'data_source')
        metrics = [''] * len(data_sources)
        for item in queryset:
            for i, dts in enumerate(data_sources):
                if item['data_source'] == dts:
                    metrics[i] = item['metric']
                    data_sources[i] = ''
                    break

        return metrics

    def list_metric_names_and_metric_types(self):
        data_sources = self.data_source.split(',')
        queryset = self.metrics.filter(role='y_axis').values(
            'metric', 'data_source', 'metric_type')
        metrics = [{"name": "", "type": ""} for _ in data_sources]
        for item in queryset:
            for i, dts in enumerate(data_sources):
                if item['data_source'] == dts:
                    # metrics[i] = item['metric']
                    metrics[i]['name'] = item['metric']
                    metrics[i]['type'] = item['metric_type']
                    data_sources[i] = ''
                    break

        return metrics

    def get_metrics(self):
        return self.metrics.filter(role='y_axis')


# ShopTurboShopTurbo
SHOPTURBO_NUMBER_FORMAT = [
    ('number', 'Number'),
    ('%', '%'),
    ('usd', 'USD'),
    ('jpy', 'JPY'),
    ('idr', 'IDR'),
]


class ReportPanelNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=OBJECTS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    taxes = models.CharField(max_length=800, null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    is_association = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    sub_property = models.TextField(null=True, blank=True)
    source_property = models.TextField(null=True, blank=True)
    pair_property = models.TextField(null=True, blank=True)
    sync_conditions = models.TextField(null=True, blank=True)

    def __str__(self):
        return str(self.workspace.name) + ' - ' + str(self.type) + " - " + str(self.name) + " - " + str(self.id)


class ReportPanelValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ReportPanelNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    panels = models.ForeignKey(ReportPanel, on_delete=models.CASCADE, null=True,
                               blank=True, related_name='reportpanel_custom_field_relations')
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(verbose_name="Custom Field file",
                            upload_to="reportpanel-customfield-files", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)
    value_time_end = models.DateTimeField(
        null=True, blank=True)        # Use for date_range field

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)  # Log
            self._log_data = log_data

        if self.field_name and (self.field_name.type == 'tag'):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class ReportPanelValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(verbose_name="Report Panel Custom Field file",
                            upload_to="report-panel-custom-field-files")
    valuecustomfield = models.ForeignKey(
        ReportPanelValueCustomField, on_delete=models.CASCADE, null=True, blank=True)
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default='image')
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class ReportPanelSheet(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    report_panel = models.ForeignKey(ReportPanel, on_delete=models.CASCADE)
    tab = models.ForeignKey("ReportPanelSheetTab", null=True, blank=True, on_delete=models.CASCADE)
    col = models.PositiveIntegerField()
    row = models.PositiveIntegerField()
    value = models.CharField(max_length=100)

    class Meta:
        unique_together = ('report_panel', 'tab', 'col', 'row')


class ReportPanelSheetRows(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    report_panel = models.ForeignKey(ReportPanel, on_delete=models.CASCADE)
    rows_source = models.CharField(max_length=200)
    rows_display = models.CharField(max_length=200, null=True, blank=True)
    grouped_date_field = models.CharField(max_length=200, null=True, blank=True)
    rows_formula = models.JSONField(null=True, blank=True)
    filter = models.JSONField(null=True, blank=True)

# Many to Many relationship between Report and ReportPanel


class PanelReportPanel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    report = models.ForeignKey(
        Report, on_delete=models.CASCADE, null=True, blank=True)
    panel = models.ForeignKey(
        ReportPanel, on_delete=models.CASCADE, null=True, blank=True)
    order = models.IntegerField(default=0)


class PanelTemplate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    name_ja = models.CharField(max_length=200, null=True, blank=True)
    category = models.CharField(max_length=200, null=True, blank=True)
    category_ja = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    description_ja = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    panel = models.ForeignKey(
        ReportPanel, on_delete=models.CASCADE, null=True, blank=True)
    order = models.IntegerField(default=0)
    hide = models.BooleanField(default=True)


class DashboardTemplate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    name_ja = models.CharField(max_length=200, null=True, blank=True)
    category = models.CharField(max_length=200, null=True, blank=True)
    category_ja = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    description_ja = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    report = models.ForeignKey(
        Report, on_delete=models.CASCADE, null=True, blank=True)
    order = models.IntegerField(default=0)
    hide = models.BooleanField(default=True)


class Widget(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    order = models.IntegerField(default=0)
    ratio = models.IntegerField(default=33)
    reports = models.ManyToManyField(Report, blank=True)
    app_target = models.CharField(max_length=100, null=True, blank=True)
    special_type = models.CharField(max_length=200, null=True, blank=True)
    solution = models.ForeignKey(
        "Solution", on_delete=models.SET_NULL, null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    private = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)

class ReportPanelSheetTab(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    report_panel = models.ForeignKey(ReportPanel, on_delete=models.CASCADE)
    order = models.PositiveIntegerField()
    name = models.CharField(max_length=100)
    
    class Meta:
        unique_together = ('report_panel', 'name') 
        
    def get_next_order_for_panel(self, panel_id):
        from django.db.models import F

        existing_orders = (
            ReportPanelSheetTab.objects
            .filter(report_panel_id=panel_id)
            .order_by('order')
            .values_list('order', flat=True)
        )

        order = 1
        for existing_order in existing_orders:
            if existing_order != order:
                break
            order += 1
        return order
    
    def save(self, *args, **kwargs):
        if self.order is None:
            self.order = self.get_next_order_for_panel(self.report_panel_id)
        super().save(*args, **kwargs)

