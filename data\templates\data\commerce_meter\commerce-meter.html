{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% block content %}

{% include "data/common/advance_search/advance-search-style.html" %}
{% include "data/utility/table-css.html" %}

<style>
    table {
        width: auto;
    }

    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Hide all sorting indicators */
    table.dataTable thead .sorting,
    table.dataTable thead .sorting_asc,
    table.dataTable thead .sorting_desc,
    table.dataTable thead .sorting_asc_disabled,
    table.dataTable thead .sorting_desc_disabled {
        background-image: none !important;
        background: none !important;
        padding-right: 0 !important;
    }

    /* Remove sorting cursor */
    table.dataTable thead th {
        cursor: default !important;
    }

    /* Remove sorting attributes and classes */
    #commerce_meter-table th {
        background-image: none !important;
        cursor: default !important;
        padding-right: 0 !important;
    }

    /* Override any Handsontable sorting indicators */
    .handsontable .columnSorting,
    .handsontable span.colHeader.columnSorting::before,
    .handsontable span.colHeader.columnSorting::after {
        background-image: none !important;
        content: none !important;
        display: none !important;
    }

    /* Remove sorting hover effects */
    .handsontable .columnSorting.sortAction:hover {
        text-decoration: none !important;
        cursor: default !important;
    }

    /* Additional rules to prevent sorting indicators */
    .dataTable thead th.sorting::after,
    .dataTable thead th.sorting_asc::after,
    .dataTable thead th.sorting_desc::after {
        display: none !important;
    }

    .dataTable thead th.sorting::before,
    .dataTable thead th.sorting_asc::before,
    .dataTable thead th.sorting_desc::before {
        display: none !important;
    }

    thead.position-sticky {
        position: sticky;
        top: 0;
        background: white;
        z-index: 2;
    }

    /* Ensure dropdown menus appear above sticky headers in inventory */
    .dropdown-menu {
        z-index: 25!important;
    }
</style>

{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-end mb-2 align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}
    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        {% if permission|check_permission:'edit' %}
            <div class="{% include "data/utility/table-button.html" %}">
                <button id='view-sync-{{object_type}}' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 create-view-settings-button"
                    hx-get="{% host_url 'data_transfer_commerce_meter_drawer' host 'app' %}?module={{menu_key}}&object_type={{object_type}}&section=drawer"
                    hx-trigger="click"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    hx-target="#manage-contacts-view-settings-drawer"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        インポート
                        {% else %}
                        Import
                        {% endif %}
                    </span>
                </button>
                <button id='view-sync-{{object_type}}' type="button" class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer create-view-settings-button hover-tooltip"
                    hx-get="{% host_url 'data_transfer_commerce_meter_drawer' host 'app' %}?import_export_type=import&module={{menu_key}}&object_type={{object_type}}&section=drawer" 
                    hx-trigger="click"
                    hx-target="#manage-contacts-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    >
                    <span class="search-wrapper-tooltip hover-tooltip-text">
                        {% if LANGUAGE_CODE == 'ja' %}インポート{% else %}Import{% endif %}
                    </span>
                    <span class="tw-flex svg-icon svg-icon-3">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.5 20.7259C14.6 21.2259 14.2 21.826 13.7 21.926C13.2 22.026 12.6 22.0259 12.1 22.0259C9.5 22.0259 6.9 21.0259 5 19.1259C1.4 15.5259 1.09998 9.72592 4.29998 5.82592L5.70001 7.22595C3.30001 10.3259 3.59999 14.8259 6.39999 17.7259C8.19999 19.5259 10.8 20.426 13.4 19.926C13.9 19.826 14.4 20.2259 14.5 20.7259ZM18.4 16.8259L19.8 18.2259C22.9 14.3259 22.7 8.52593 19 4.92593C16.7 2.62593 13.5 1.62594 10.3 2.12594C9.79998 2.22594 9.4 2.72595 9.5 3.22595C9.6 3.72595 10.1 4.12594 10.6 4.02594C13.1 3.62594 15.7 4.42595 17.6 6.22595C20.5 9.22595 20.7 13.7259 18.4 16.8259Z" fill="currentColor"/>
                            <path opacity="0.3" d="M2 3.62592H7C7.6 3.62592 8 4.02592 8 4.62592V9.62589L2 3.62592ZM16 14.4259V19.4259C16 20.0259 16.4 20.4259 17 20.4259H22L16 14.4259Z" fill="currentColor"/>
                        </svg>
                    </span>
                </button>
                <script>
                    function fillItemIds(elm) {
                        // Call your JavaScript function to generate the account IDs dynamically
                        var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                        var checkedIds = [];
                        checkboxes.forEach(function(checkbox) {
                            if (checkbox.checked) {
                                checkedIds.push(checkbox.value);
                            }
                        });
                        var itemIds = checkedIds;
                        itemIds = itemIds.filter(id => id !== 'on');
                        // Now set the hx-vals attribute with the updated account IDs
                        elm.setAttribute('hx-vals', '{"import_export_type":"import", "module": "{{menu_key}}"}');
                    }
                </script>
            </div>
            <div class="{% include "data/utility/table-button.html" %} btn-group mb-2 tw-h-[32px]">
                <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md create_new_wizard_button rounded-1 py-1" type="button"
                    hx-get="{% url 'create_commerce_meter' %}"
                    hx-target="#create-new-drawer-content"
                    hx-trigger="click"
                    hx-indicator=".loading-drawer-spinner"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                            <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                            <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                        </svg>
                    </span>

                    <span class="fs-7 ps-1 fw-bolder w-85px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New
                        {% endif %}
                    </span>
                </button>
            </div>
        {% endif %}
    </div>
</div>


{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="w-100 tw-pl-2">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %} pt-5" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="{% include "data/utility/table-content.html" %}" id="{{object_type}}-view-container">
                {% comment %} Desktop {% endcomment %}
                <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] mb-2 tw-w-[30px]" style="height: 26px;"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    {% include "data/projects/partial-dropdown-view-menu.html" %}

                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                        <a class="{% include "data/utility/view-menu-default.html" %}"
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                            <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="text-gray-900 w-20px nav-item justify-content-center d-flex fs-6">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button"
                                hx-get="{% url 'commerce_view_setting' %}" 
                                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{view_id}}"}'
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-trigger="click"
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>

                    {% comment %} Put Items here {% endcomment %}
                    {% include 'data/projects/partial-view-menu.html' %}

                </div>

                {% comment %} Mobile View {% endcomment %}
                <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                    <div class="d-flex align-items-center">
                        <!-- Example split danger button -->
                        <div class="mb-2 btn-group">
                            <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px]"
                                style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;">
                                <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                    {% if view_filter.view.title %}
                                        {{ view_filter.view.title }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                    {% endif %}
                                </span>
                            </button>
                            <button type="button"
                                class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split"
                                data-bs-toggle="dropdown"
                                aria-expanded="false"
                                style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                            >
                                <span class="svg-icon svg-icon-4">
                                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                    </svg>
                                </span>
                            </button>
                            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                                <div class="dropdown-divider"></div>
                                {% for view in views %}
                                    {% if view.title %}
                                        <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="mb-2 text-gray-900 fs-6">
                            <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="d-flex w-50 justify-content-end">
                    <div class="max-md:tw-hidden tw-flex me-2">
                        <div class="mb-2 search-wrapper expanded">
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="mb-0 d-flex position-relative" style="height: 26px;">
                                        <span class="svg-icon svg-icon-3 search-icon-view">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <input
                                        id="base-search-input" type="text" name="q" class="bg-white form-control ps-12 pe-16 tw-rounded-lg" 
                                        value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_inventory:request %}{% with channel_column=k|search_custom_field_object_inventory:request %}{{channel_column.name}}{% endwith %}{% else %}{% with column_display=k|display_column_inventory:request %}{{column_display}}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}" 
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                        {% if request.GET.status == 'archived' %}
                                        <input type="hidden" value="archived" name="status">
                                        {% endif %}
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        {% if view_id %}
                                        <input type="hidden" value="{{view_id}}" name="view_id">
                                        {% endif %}
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="{% include "data/utility/view-menu-search.html" %}">
                        <button
                            onclick="openSearch()"
                            class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <div id="{{object_type}}-bulk-action-container" class="tw-hidden w-100">
                <div class="d-flex w-100">
                    <div class="max-md:tw-block tw-flex w-50">
                        <div class="me-10 d-flex" style="min-width: 30px">
                            <input type="checkbox" class="d-none" name="select-all-in-view" id="select-all-in-view">
                            <button class="fs-8 px-1 w-250px py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold me-2" onclick="selectAllContacts()">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    ページ全件選択
                                    {% else %}
                                    Select All in Page
                                    {% endif %}
                                </span>
                            </button>
                            <button class="fs-8 px-1 w-250px py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold me-2" onclick="selectAllContactsInView()">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    ビュー全件選択
                                    {% else %}
                                    Select All in View
                                    {% endif %}
                                </span>
                            </button>
                            <button class="fs-8 px-1 w-250px py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold me-2" onclick="deselectAllContacts()">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    選択解除
                                    {% else %}
                                    Deselect
                                    {% endif %}
                                </span>
                            </button>

                            <script>
                                function countCheckedItems() {
                                    var checkboxes = document.querySelectorAll('.{{object_type}}-selection:checked');
                                    var totalChecked = checkboxes.length;
                                    document.getElementById('total-item-selected').innerText = totalChecked;
                                }
                                function selectAllContacts() {
                                    selectTaskInputs = document.getElementsByClassName('{{object_type}}-selection')
                                    for (var i = 0; i < selectTaskInputs.length; i++) {
                                        selectTaskInputs[i].checked = true;
                                    }
                                    document.getElementById('flag_all').checked = false
                                    countCheckedItems()
                                }

                                function selectAllContactsInView() {
                                    const totalItems = {{paginator.count}}
                                    document.getElementById('total-item-selected').innerText = totalItems;
                                    selectTaskInputs = document.getElementsByClassName('{{object_type}}-selection')
                                    for (var i = 0; i < selectTaskInputs.length; i++) {
                                        selectTaskInputs[i].checked = true;
                                    }
                                    document.getElementById('flag_all').checked = true
                                }

                                function deselectAllContacts() {
                                    selectTaskInputs = document.getElementsByClassName('{{object_type}}-selection')
                                    for (var i = 0; i < selectTaskInputs.length; i++) {
                                        selectTaskInputs[i].checked = false;
                                    }
                                    document.getElementById('{{object_type}}-bulk-action-container').classList.add('tw-hidden')
                                    document.getElementById('{{object_type}}-view-container').classList.remove('tw-hidden')
                                    document.getElementById('flag_all').checked = false
                                }
                            </script>
                        </div>

                        {% comment %} {% if permission|check_permission:'edit' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1 me-2 w-100px" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="{{object_type}}-form">
                            {% if LANGUAGE_CODE == 'ja'%}
                            複製
                            {% else %}
                            Duplicate
                            {% endif %}
                        </button>
                        {% endif %} {% endcomment %}

                        {% if permission|check_permission:'archive' %}
                        <button class="w-150px fs-8 px-1 py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light-success fw-bold me-2" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                        <button class="w-150px fs-8 px-1 py-1 mt-2 mb-1 btn btn-sm btn-light-danger rounded-1 fw-bold me-2" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive
                            {% endif %}
                        </button>
                        {% endif %}

                        <script>
                            document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                document.getElementById('{{object_type}}-bulk-action-container').classList.add('tw-hidden')
                                document.getElementById('{{object_type}}-view-contianer').classList.remove('tw-hidden')
                            })
                        </script>

                        {% if permission|check_permission:'edit' %}
                            <script>
                                function toggleSelectAll() {
                                }

                                function getSelectedObjects() {
                                    var selectedItems = [];
                                    var classNameElements = document.getElementsByClassName("{{object_type}}-selection");
                                    if (classNameElements){
                                        classNameElements.forEach(function(classNameElement) {
                                            if (classNameElement.checked) {
                                                selectedItems.push(classNameElement.value);
                                            }
                                        });
                                    }
                                    console.log(selectedItems)
                                    return selectedItems;
                                }
                            </script>
                        {% endif %}
                    </div>
                    {% comment %} <div class="d-flex w-50 align-items-center justify-content-end">
                        {% if permission|check_permission:'edit' %}
                            <div class="{% include "data/utility/table-button.html" %}">
                                <button id='view-sync-{{object_type}}-action-drawer' type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button"
                                    hx-get="{% url 'inventory_bulk_action_drawer' %}"
                                    hx-trigger="click"
                                    onclick="fillActionInventoryIds(this),check_permission_action(event, 'edit')"
                                    hx-target="#manage-contacts-view-settings-drawer"
                                    hx-indicator=".loading-drawer-spinner,.view-form"
                                    >
                                    <span class="svg-icon svg-icon-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                                            <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                                        </svg>
                                    </span>
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        アクション
                                        {% else %}
                                        Action
                                        {% endif %}
                                    </span>
                                </button>
                            </div>
                            <div class="{% include "data/utility/table-button.html" %}">
                                <button class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button" type="button"
                                    hx-get="{% url 'shopturbo_load_drawer' %}?module={{menu_key}}"
                                    hx-trigger="click"
                                    onclick="fillItemExportIds(this)"
                                    hx-target="#manage-contacts-view-settings-drawer"

                                    >

                                    <span class="svg-icon svg-icon-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                        </svg>
                                    </span>
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        エクスポート
                                        {% else %}
                                        Export
                                        {% endif %}
                                    </span>
                                </button>

                                <script>
                                    function fillItemExportIds(elm) {
                                        // Call your JavaScript function to generate the account IDs dynamically
                                        var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                                        var checkedIds = [];
                                        checkboxes.forEach(function(checkbox) {
                                            if (checkbox.checked) {
                                                checkedIds.push(checkbox.value);
                                            }
                                        });
                                        var itemIds = checkedIds;
                                        itemIds = itemIds.filter(id => id !== 'on');
                                        // Now set the hx-vals attribute with the updated account IDs
                                        elm.setAttribute('hx-vals', '{"drawer_type":"view-sync-{{object_type}}","page": "{{object_type}}","import_export_type":"export","view_id":"{{view_filter.view.id}}", "object_ids":"' + itemIds + '", "module": "{{menu_key}}"}');
                                    }
                                </script>
                            </div>
                        {% endif %}
                    </div> {% endcomment %}
                </div>
                <div class="w-100">
                    <span id="total-item-selected"></span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    つのレコードが選択されています。
                    {% else %}
                    records are selected.
                    {% endif %}
                </div>
            </div>

        </div>
    </div>
    {% comment %} End of Views {% endcomment %}

    <div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
        <form id="filter-form-search" method="get" class="w-100">
            <div class="mb-0 d-flex position-relative" style="height: 26px;">
                <span class="svg-icon svg-icon-3 search-icon-view">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                        <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                    </svg>
                </span>
                <input id="base-search-input" type="text" name="q" class="bg-white form-control ps-12 pe-16"
                value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_inventory:request %}{% with channel_column=k|search_custom_field_object_inventory:request %}{{channel_column.name}}{% endwith %}{% else %}{% with column_display=k|display_column_inventory:request %}{{column_display}}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}" 
                placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                >
                {% if request.GET.status == 'archived' %}
                <input type="hidden" value="archived" name="status">
                {% endif %}
                <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                    </svg>
                </span>
                <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                        <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                    </svg>
                </span>
                <input type="hidden" value="{{view_id}}" name="view_id">
            </div>
        </form>
    </div>


    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="px-5 pt-4 pb-3 text-center rounded status bg-light-dark">

            {% if LANGUAGE_CODE == 'ja'%}
            このページの全てのレコードが選択されました。
            {% else %}
            All records on this page are selected.
            {% endif %}

            <a onclick="toggleText()"
                class="btn btn-dark"
                data-bs-toggle="collapse"
                id="select-additional-options-toggle"
                role="button"
                aria-expanded="false"
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                全てのレコードを選択する
                {% else %}
                Select all records
                {% endif %}

                </a>
        </div>
    </div>

    <div hx-get="{% url 'commerce_meter_content' %}?{% if search_q %}&q={{search_q}}{% endif %}{% if view_id %}&view_id={{view_id}}{% endif %}{% if object_id %}&id={{object_id}}{% endif %}{% if menu_key %}&module={{menu_key}}{% endif %}" hx-trigger="load" hx-swap="outerHTML"></div>
</div>

{% endif %}

<style>
    .loading-spinner{
        display:none;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>


<script>
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
        // Ignore if input, textarea, or contenteditable is focused
        const active = document.activeElement;
        if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
            return;
        }

        // Check if the key pressed is 'n'
        if (event.key.toLowerCase() === 'n') {
            event.preventDefault(); // Prevent default 'n' behavior
            
            // Find the 'Create New' button (adjust selector if needed)
            const newButton = document.querySelector('.view_form_trigger'); 
            
            if (newButton) {
                newButton.click(); // Simulate click to open the drawer
            }
        }
    });
</script>

<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;

        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }

    }
</script>

{% endblock %}

