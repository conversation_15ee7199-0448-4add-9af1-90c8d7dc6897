import traceback
import uuid

from django.db.models import Q
from django.utils import timezone
import pytz

from data.constants.properties_constant import TYPE_OBJECT_INVENTORY_TRANSACTION
from data.models import (
    AppSetting,
    Company,
    Contact,
    InventoryTransaction,
    InventoryTransactionNameCustomField,
    ShopTurboInventory,
    ShopTurboItemComponents,
    ShopTurboItemsPrice,
)
from utils.bgjobs.runner import trigger_bg_job
from utils.list_action_trigger_event import trigger_workflow_by_inventory_less_than
from utils.logger import logger
from utils.utility import is_valid_uuid
from utils.workflow import get_workflows_by_first_action_trigger


def get_and_update_inventory_amount(inventory):
    """
    Calculates the current amount of inventory based on the specified inventory type.

    This function retrieves the current amount of inventory for a given inventory object.
    It considers the last transactions to calculate the total amount.
    If no transactions are found, it returns the amount stored in the inventory model.

    Parameters:
    - inventory (ShopTurboInventory): The inventory object for which to calculate the amount.

    Returns:
    - int: The calculated amount of inventory.
    """
    try:
        old_total_inventory = inventory.total_inventory
        current_time = timezone.now()
        last_transaction = (
            inventory.inventorytransaction_set.exclude(usage_status="archived")
            .filter(transaction_date__lte=current_time)
            .order_by("-transaction_date", "-created_at")
            .first()
        )

        # Calculate the new total_inventory value
        if last_transaction:
            new_total_inventory = last_transaction.transaction_amount
            logger.debug(
                f"[DEBUG inventory] Found transaction for inventory {inventory.id}: {new_total_inventory}"
            )
        else:
            # If no transactions exist, preserve the existing total_inventory value
            # Don't set it to 0 unless it was already 0
            new_total_inventory = (
                old_total_inventory if old_total_inventory is not None else 0
            )
            logger.debug(
                f"[DEBUG inventory] No transactions found for inventory {inventory.id}, using existing value: {new_total_inventory}"
            )

        # Only update and save if the value actually changed
        if old_total_inventory != new_total_inventory:
            inventory.total_inventory = new_total_inventory
            logger.debug(
                f"[DEBUG inventory] Updating total_inventory for {inventory.id}: {old_total_inventory} -> {new_total_inventory}"
            )
            inventory.save()
            trigger_workflow_by_inventory_less_than(inventory)

        # Return the calculated amount
        return new_total_inventory

    except Exception as e:
        logger.error(
            f"[ERROR inventory] Error in get_and_update_inventory_amount for inventory {inventory.id if inventory else 'None'}: {e}"
        )
        return 0


def item_inventory_amount(item, inventory_type="total"):
    try:
        logger.debug(
            f"[DEBUG inventory] item_inventory_amount called for item {item.id}, type: {inventory_type}"
        )
        # Since item is a ManyToManyField in ShopTurboInventory, we need to use the correct query
        inventories = ShopTurboInventory.objects.filter(item=item, status="active")
        logger.debug(
            f"[DEBUG inventory] Found {inventories.count()} active inventories for item {item.id}"
        )

        # Let's also see if there are any inventories for this item with different approaches
        alt_inventories = ShopTurboInventory.objects.filter(
            item__in=[item], status="active"
        )
        logger.debug(
            f"[DEBUG inventory] Alternative query found {alt_inventories.count()} active inventories"
        )

        all_inventories = ShopTurboInventory.objects.filter(item=item)
        logger.debug(
            f"[DEBUG inventory] All inventories (any status) for item: {all_inventories.count()}"
        )

        if all_inventories.count() > 0:
            for inv in all_inventories:
                logger.debug(
                    f"[DEBUG inventory]   - Inventory {inv.id}: status={inv.status}, total={inv.total_inventory}"
                )

        total = 0
        if inventory_type == "total":
            # Add amounts from inventory model for types without transactions
            for inv in inventories:
                inv_amount = (
                    inv.total_inventory if inv.total_inventory is not None else 0
                )
                logger.debug(
                    f"[DEBUG inventory] Inventory {inv.id}: total_inventory = {inv.total_inventory}, adding {inv_amount}"
                )
                total += inv_amount
            logger.debug(f"[DEBUG inventory] Calculated total: {total}")
            logger.debug(
                f"[DEBUG inventory] Item.total_inventory before: {item.total_inventory}"
            )
            if item.total_inventory != total:
                item.total_inventory = total
                item.save()
                logger.debug(
                    f"[DEBUG inventory] Updated item.total_inventory to {total}"
                )

            logger.debug(f"[DEBUG inventory] Returning total: {total}")
            return total
        else:
            inventories = inventories.filter(inventory_status=inventory_type)
            for inv in inventories:
                total += inv.total_inventory if inv.total_inventory is not None else 0
            if inventory_type == "available":
                if item.available_inventory_amount != total:
                    item.available_inventory_amount = total
                    item.save()
            elif inventory_type == "committed":
                if item.committed_inventory_amount != total:
                    item.committed_inventory_amount = total
                    item.save()
            elif inventory_type == "unavailable":
                if item.unavailable_inventory_amount != total:
                    item.unavailable_inventory_amount = total
                    item.save()

            return total

    except Exception as e:
        logger.error(f"Error in get_item_inventory_amount: {e}")
        return 0


def get_inventory_forecast_data(filter_conditions, user_timezone, view_filter):
    conditions = Q()

    for q in filter_conditions.children:
        if q[0].startswith("inventorytransaction__"):
            conditions &= Q(**{q[0].replace("inventorytransaction__", ""): q[1]})

    transactions = InventoryTransaction.objects.filter(
        inventory__in=ShopTurboInventory.objects.filter(filter_conditions),
        usage_status="active",
    ).order_by("transaction_date")
    forecast_data = {}
    for transaction in transactions:
        if not transaction.transaction_date:
            continue
        date = transaction.transaction_date.date().strftime("%Y-%m-%d")
        # logger.debug("date ==========\n", date, transaction.transaction_id)
        inventory_id = str(transaction.inventory.id)

        # transaction_time = datetime.strptime(transaction.transaction_date, '%Y-%m-%d %H:%M')
        if user_timezone is None:
            user_timezone = "UTC"
        local_tz = pytz.timezone(user_timezone)
        # Check if transaction_date is naive or aware
        if (
            transaction.transaction_date.tzinfo is not None
            and transaction.transaction_date.tzinfo.utcoffset(
                transaction.transaction_date
            )
            is not None
        ):
            # If aware, directly convert to desired timezone
            transaction_time = transaction.transaction_date.astimezone(local_tz)
        else:
            # If naive, localize to the user's timezone
            localized_time = local_tz.localize(transaction.transaction_date)
            transaction_time = localized_time.astimezone(pytz.utc)
        date = transaction_time.date().strftime("%Y-%m-%d")

        # Initialize the date dictionary if it does not exist
        if date not in forecast_data:
            forecast_data[date] = {}

        # Initialize the inventory dictionary if it does not exist
        if inventory_id not in forecast_data[date]:
            forecast_data[date][inventory_id] = {
                "available": {},  # Initialize default to 0
                "committed": {},  # Initialize default to 0
                "unavailable": {},  # Initialize default to 0
            }

        # Now safely assign values
        forecast_data[date][inventory_id] = {
            "value": transaction.transaction_amount,
            "transaction_id": transaction.id,
        }
    return forecast_data


def recalculate_transactions_after(inventory, transaction, request=None):
    """
    Recalculates transaction amounts for all subsequent transactions after a given transaction.

    This function updates the transaction amounts for all subsequent transactions in chronological order.
    It ensures that the total inventory amount is maintained accurately by considering the transaction type
    and amount.

    Parameters:
    - inventory (ShopTurboInventory): The inventory object containing the transaction
    - transaction (InventoryTransaction): The transaction after which subsequent transactions need to be recalculated

    Returns:
    - transaction (InventoryTransaction)
    """
    try:
        logger.debug(transaction.transaction_id)
        transaction_date = transaction.transaction_date or transaction.created_at
        if not inventory.date or transaction_date < inventory.date:
            return transaction

        # Get the previous transaction
        logger.debug(transaction_date)
        prev_ids = list(
            InventoryTransaction.objects.exclude(pk=transaction.id)
            .filter(
                usage_status="active",
                inventory=inventory,
                transaction_date__lt=transaction_date,
            )
            .order_by("-transaction_date", "-created_at")
            .values_list("id", flat=True)
        )
        if len(prev_ids) > 0:
            current_transaction = InventoryTransaction.objects.get(id=prev_ids[0])

            # If transaction have no inventory total amount, re-calculate from the very first inventory transaction
            if current_transaction.transaction_amount is None:
                current_transaction = (
                    InventoryTransaction.objects.filter(
                        usage_status="active", inventory=current_transaction.inventory
                    )
                    .order_by("transaction_date", "created_at")
                    .first()
                )
                current_transaction.transaction_amount = 0
                transaction_date = (
                    current_transaction.transaction_date
                    or current_transaction.created_at
                )
                prev_ids = []
        else:
            # No, previous transaction. The current transaction is the first transaction.
            transaction.transaction_amount = 0
            current_transaction = transaction
            if request:
                transaction.save(log_data={"user": request.user})
            else:
                transaction.save()
        # Update Next Transaction
        next_transactions = (
            InventoryTransaction.objects.exclude(pk__in=prev_ids)
            .filter(
                usage_status="active",
                inventory=inventory,
                transaction_date__gte=transaction_date,
            )
            .order_by("transaction_date", "created_at")
        )
        logger.debug(
            current_transaction.transaction_id,
            current_transaction.transaction_type,
            current_transaction.amount,
            current_transaction.transaction_amount,
        )
        for next_transaction in next_transactions:
            logger.debug(
                next_transaction.transaction_id,
                next_transaction.transaction_type,
                next_transaction.amount,
            )

            if next_transaction.transaction_type == "in":
                next_transaction.transaction_amount = int(
                    current_transaction.transaction_amount
                ) + int(next_transaction.amount)
            elif next_transaction.transaction_type == "out":
                next_transaction.transaction_amount = int(
                    current_transaction.transaction_amount
                ) - int(next_transaction.amount)
            else:
                next_transaction.amount = (
                    next_transaction.transaction_amount
                    - current_transaction.transaction_amount
                )
            logger.debug(next_transaction.transaction_amount)
            if next_transaction == transaction:
                if request:
                    next_transaction.save(log_data={"user": request.user})
                else:
                    next_transaction.save()
            else:
                next_transaction.save()
            current_transaction = next_transaction

        # Update for inventory amount
        current_inventory = current_transaction.inventory
        if current_inventory.total_inventory != current_transaction.transaction_amount:
            current_inventory.total_inventory = current_transaction.transaction_amount
            current_inventory.save()
            trigger_workflow_by_inventory_less_than(current_inventory)

    except Exception as e:
        traceback.logger.debug_exc()
        logger.error("recalculate transaction after ERROR", e)
    return transaction


def update_inventory_stock_price(inventory, request=None):
    try:
        items = inventory.item.all()
        if len(items) > 0:
            item = items[0]
            inventories = ShopTurboInventory.objects.filter(item=item, status="active")
            transactions = (
                InventoryTransaction.objects.exclude(usage_status="archived")
                .filter(inventory__in=inventories)
                .order_by("transaction_date", "created_at")
            )
            total = 0
            latest_transaction = None
            for transaction in transactions:
                try:
                    if not latest_transaction:
                        previous_price = transaction.price
                    else:
                        previous_price = latest_transaction.average_price
                    if transaction.transaction_type == "in":
                        total += transaction.amount
                    else:
                        total = total - transaction.amount
                    if total < 0:
                        total = 0
                        transaction.average_price = previous_price
                    elif total == 0:
                        transaction.average_price = (
                            transaction.price + previous_price
                        ) / 2
                    else:
                        if transaction.transaction_type == "in":
                            transaction.average_price = (
                                (transaction.amount * transaction.price)
                                + ((total - transaction.amount) * previous_price)
                            ) / total
                        else:
                            transaction.average_price = (
                                (transaction.amount * transaction.price)
                                + ((total) * previous_price)
                            ) / (total + transaction.amount)
                    if request:
                        transaction.save(log_data={"user": request.user})
                    else:
                        transaction.save()

                    latest_transaction = transaction
                except Exception:
                    continue

    except Exception as e:
        traceback.logger.debug_exc()
        logger.error("insert_inventory_stock ERROR", e)
    return True


def re_calculate_inventory_stock(inventory):
    """
    Recalculates the total, available, committed, and unavailable stock amounts, and updates the inventory model accordingly.
    It is used to ensure the inventory amounts are accurate and up-to-date.

    Parameters:
    - inventory (ShopTurboInventory): The inventory object for which to re-calculate the stock amounts.
    """
    get_and_update_inventory_amount(inventory)


def re_calculate_all_inventory_transactions(inventory):
    """
    Re-calculate all inventory transactions of given inventory from scratch.

    First, it sorts all transactions by transaction date and created at.
    Then, it goes through each transaction and update its transaction_amount.
    Finally, it calls `re_calculate_inventory_stock` to update the inventory latest stock.

    This function is used when someone manually changes the transaction_amount of a transaction.
    """
    transactions = inventory.inventorytransaction_set.exclude(
        usage_status="archived"
    ).order_by("transaction_date", "created_at")
    total = 0
    for transaction in transactions:
        if transaction.transaction_type == "in":
            total += transaction.amount
        elif transaction.transaction_type == "out":
            total = total - transaction.amount
        else:  # adjust
            transaction.amount = transaction.transaction_amount - total
            total = transaction.transaction_amount
        transaction.transaction_amount = total
        transaction.save()
    re_calculate_inventory_stock(inventory)


def apply_inventory_transaction_search_setting(appsetting: AppSetting, search_key):
    filter_conditions = Q()
    if appsetting:
        filter_dictionary = appsetting.search_setting_inventory_transaction
        if filter_dictionary and filter_dictionary != "unset":
            for filter in filter_dictionary.split(","):
                try:
                    if is_valid_uuid(filter):
                        custom_field = InventoryTransactionNameCustomField.objects.get(
                            id=filter
                        )
                        if custom_field.type in ["contact", "company"]:
                            object_filter = Q()
                            object_filter |= Q(name__icontains=search_key)
                            if custom_field.type == "contact":
                                object_filter |= Q(last_name__icontains=search_key)
                                object_ids = Contact.objects.filter(
                                    object_filter
                                ).values_list("id", flat=True)
                            elif custom_field.type == "company":
                                object_ids = Company.objects.filter(
                                    object_filter
                                ).values_list("id", flat=True)
                            object_ids = [str(object_id) for object_id in object_ids]
                            filter_conditions |= Q(
                                inventory_transaction_custom_field_relations__field_name__id=filter,
                                inventory_transaction_custom_field_relations__value__in=object_ids,
                            )
                        else:
                            filter_conditions |= Q(
                                inventory_transaction_custom_field_relations__field_name__id=filter,
                                inventory_transaction_custom_field_relations__value__icontains=search_key,
                            )
                    elif "|" in filter:
                        filter_condition_text, custom_property_id = filter.split("|")

                        column = ""
                        if "contact" in filter_condition_text:
                            column = "contact__"
                        elif "company" in filter_condition_text:
                            column = "company__"
                        filter_conditions |= Q(
                            **{
                                column
                                + filter_condition_text
                                + "icontains": search_key,
                                column
                                + filter_condition_text.replace("value__", "")
                                + "field_name__id": custom_property_id,
                            }
                        )
                    elif filter in ["inventory", "inventory_id"]:
                        try:
                            filter_conditions |= Q(
                                **{"inventory__inventory_id": int(search_key)}
                            )
                        except:
                            pass
                    else:
                        filter_conditions |= Q(**{filter + "__icontains": search_key})

                except Exception as e:
                    traceback.logger.debug_exc()
                    logger.error(
                        f"ERROR === shopturbo.py -- inventory transaction search setting: {e}"
                    )
    return filter_conditions


def create_inventory_transaction_helper(inventory, user, old_inventory=None, lang="en"):
    """
    Creates inventory transactions based on changes between old and new inventory states.

    This function handles both initial inventory creation and updates to existing inventory records.
    For updates, it calculates the difference between old and new values and creates appropriate
    in/out transactions. For new inventory items, it creates an initial "in" transaction with the
    starting quantity.

    Args:
        inventory (ShopTurboInventory): The current inventory object being created/updated
        user (User): The user performing the inventory change
        old_inventory (ShopTurboInventory, optional): The previous state of the inventory before changes.
            Defaults to None for new inventory creation.

    Note:
        - For updates (old_inventory exists):
            - If new value > old value: Creates an "in" transaction for the difference
            - If new value < old value: Creates an "out" transaction for the difference
        - For new inventory: Creates an "in" transaction with initial_value
    """
    inventory_transaction = None
    transaction_date = timezone.now().strftime("%Y-%m-%d %H:%M")
    if old_inventory:
        items = old_inventory.item.all()
        if len(items) > 0:
            item = items[0]
            inventories = ShopTurboInventory.objects.filter(item=item)
            latest_transaction = (
                InventoryTransaction.objects.exclude(usage_status="archived")
                .filter(inventory__in=inventories)
                .order_by("-transaction_date", "-created_at")
            )
            if latest_transaction:
                latest_transaction = latest_transaction[:1][0]
                current_price = latest_transaction.average_price

                if int(old_inventory.initial_value) != int(inventory.initial_value):
                    if int(old_inventory.initial_value) < int(inventory.initial_value):
                        inventory_transaction = InventoryTransaction.objects.create(
                            inventory=inventory,
                            transaction_type="in",
                            amount=int(inventory.initial_value)
                            - int(old_inventory.initial_value),
                            transaction_amount=int(inventory.initial_value),
                            workspace=inventory.workspace,
                            price=inventory.price,
                            average_price=current_price,
                            user=user,
                            transaction_date=transaction_date,
                        )
                    else:
                        inventory_transaction = InventoryTransaction.objects.create(
                            inventory=inventory,
                            transaction_type="out",
                            amount=int(old_inventory.initial_value)
                            - int(inventory.initial_value),
                            transaction_amount=int(inventory.initial_value),
                            price=inventory.price,
                            average_price=current_price,
                            workspace=inventory.workspace,
                            user=user,
                            transaction_date=transaction_date,
                        )
                    workflows = get_workflows_by_first_action_trigger(
                        "record-created",
                        inventory.workspace,
                        TYPE_OBJECT_INVENTORY_TRANSACTION,
                        additional_conditions={
                            "input_data__transaction_type": inventory_transaction.transaction_type
                        },
                    )
                    for workflow in workflows:
                        param = {
                            "function": "trigger_workflow_when_inventory_transaction_created",
                            "job_id": str(uuid.uuid4()),
                            "workspace_id": str(inventory.workspace.id),
                            "user_id": None,
                            "args": [
                                f"--inventory_transaction_id={inventory_transaction.id}",
                                f"--lang={lang}",
                                f"--workflow_id={workflow.id}",
                            ],
                        }
                        trigger_bg_job(param)
    else:
        items = inventory.item.all()
        price = inventory.unit_price
        average_price = price
        if len(items) > 0 and not price:
            item = items[0]
            item_price = ShopTurboItemsPrice.objects.filter(
                item=item, default=True
            ).first()
            if item_price:
                price = item_price.price
                average_price = item_price.price

        inventory_transaction = InventoryTransaction.objects.create(
            inventory=inventory,
            transaction_type="in",
            amount=int(inventory.initial_value),
            workspace=inventory.workspace,
            price=price,
            average_price=average_price,
            transaction_amount=int(inventory.initial_value),
            user=user,
            transaction_date=inventory.date,
        )
        workflows = get_workflows_by_first_action_trigger(
            "record-created",
            inventory.workspace,
            TYPE_OBJECT_INVENTORY_TRANSACTION,
            additional_conditions={
                "input_data__transaction_type": inventory_transaction.transaction_type
            },
        )
        for workflow in workflows:
            param = {
                "function": "trigger_workflow_when_inventory_transaction_created",
                "job_id": str(uuid.uuid4()),
                "workspace_id": str(inventory.workspace.id),
                "user_id": None,
                "args": [
                    f"--inventory_transaction_id={inventory_transaction.id}",
                    f"--lang={lang}",
                    f"--workflow_id={workflow.id}",
                ],
            }
            trigger_bg_job(param)

    logger.debug("Inventory Transaction CREATED")
    return inventory_transaction


def get_components_inventory(item, max_depth=3, current_depth=0, all_components=False):
    """
    Retrieves active item components inventory records from component layers associated with the given item.

    Args:
        item: The item to get components inventory for
        max_depth: Maximum depth of component hierarchy to traverse (default: 3)
        current_depth: Current depth in the component hierarchy (used internally for recursion)
        all_components: If True, returns all components at all levels, ignoring max_depth

    Returns:
        list: List of dictionaries with 'inventory' and 'amount' keys
    """
    if not all_components:
        if max_depth > 3:
            max_depth = 3
        if current_depth >= max_depth:
            return []

    inventory_list = []
    components = ShopTurboItemComponents.objects.filter(
        item=item, item_component__status="active"
    )

    for component in components:
        inventory = (
            ShopTurboInventory.objects.filter(
                item=component.item_component, status="active"
            )
            .order_by("created_at")
            .first()
        )
        if inventory:
            inventory_list.append(
                {"inventory": inventory, "amount": component.quantity}
            )

        # Recursively get components of components
        inventory_list += get_components_inventory(
            component.item_component, max_depth, current_depth + 1, all_components
        )

    return inventory_list
