import random
import time
import uuid

from django.contrib.auth.models import User
from django.db import IntegrityError, connection, transaction
from django.db import models
from django.utils import timezone

from data.constants.constant import OBJECTS_CF_TYPE, SHOPTURBO_NUMBER_FORMAT
from data.models.base import save_tags_values, validate_value_custom_field
from data.models.constant import POST_MEDIA_TYPE
from data.models.constant import USAGE_STATUS
from data.models.customer import Company, Contact
from data.models.item import ShopTurboItems
from data.models.subscription import ShopTurboSubscriptions
from data.models.workspace import Workspace


class CommerceMeter(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    meter_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        Workspace, on_delete=models.CASCADE, blank=True, null=True
    )
    contact = models.ForeignKey(
        Contact, on_delete=models.CASCADE, blank=True, null=True
    )
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, blank=True, null=True
    )
    item = models.ForeignKey(
        ShopTurboItems, on_delete=models.CASCADE, blank=True, null=True
    )
    subscription = models.ForeignKey(
        ShopTurboSubscriptions, on_delete=models.SET_NULL, blank=True, null=True
    )
    usage = models.FloatField(default=0)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["workspace", "meter_id"],
                condition=models.Q(meter_id__isnull=False),
                name="unique_meter_id_per_workspace",
            )
        ]

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if not self.meter_id:
            # Retry logic to handle race conditions
            max_retries = 5
            for attempt in range(max_retries):
                try:
                    # Use atomic transaction with table locking to prevent race conditions
                    with transaction.atomic():
                        # Lock the table to prevent concurrent access during ID generation
                        with connection.cursor() as cursor:
                            cursor.execute(
                                f"LOCK TABLE {self._meta.db_table} IN EXCLUSIVE MODE"
                            )

                        # Generate a new item_id if it doesn't exist
                        last_item = (
                            CommerceMeter.objects.filter(
                                workspace=self.workspace, meter_id__isnull=False
                            )
                            .order_by("-meter_id")
                            .first()
                        )
                        if last_item and last_item.meter_id:
                            try:
                                last_item_id = int(last_item.meter_id)
                            except (ValueError, TypeError):
                                last_item_id = 0
                            next_item_id = last_item_id + 1
                        else:
                            # If it's the first item, start with 1
                            next_item_id = 1

                        # Check if this item_id already exists (double-check)
                        while CommerceMeter.objects.filter(
                            workspace=self.workspace, meter_id=next_item_id
                        ).exists():
                            next_item_id += 1

                        self.meter_id = next_item_id
                        break  # Success, exit retry loop

                except IntegrityError as e:
                    if attempt == max_retries - 1:
                        # Last attempt failed, raise the error
                        raise e
                    # Wait a random amount of time before retrying
                    time.sleep(random.uniform(0.1, 0.5))
                    continue

        super().save(*args, **kwargs)


class CommerceMeterNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        Workspace, on_delete=models.CASCADE, blank=True, null=True
    )
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class CommerceMeterValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        CommerceMeterNameCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    meter = models.ForeignKey(
        CommerceMeter,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="commerce_meter_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="inventory-transaction-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["field_name", "meter"],
                name="unique_field_name_per_meter",
            )
        ]

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class CommerceMeterValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Commerce Meter Field file", upload_to="commerce-meter-field-files"
    )
    valuecustomfield = models.ForeignKey(
        CommerceMeterValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)
