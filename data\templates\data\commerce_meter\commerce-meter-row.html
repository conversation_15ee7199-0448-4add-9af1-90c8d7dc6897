{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% load formatting_tags %}
{% get_current_language as LANGUAGE_CODE %}

    <td class="{% include "data/utility/column-checkbox-size.html" %}">
        <input style="" id="commerce_meter-selection-{{commerce_meter.id}}" class="form-check-input cursor-pointer commerce_meter-selection check_input" type="checkbox" name="checkbox" value="{{commerce_meter.id}}" onclick="checking_checkbox(this,event)"/>
    </td>

    {% comment %} DO NOT ADD BELOW SCRIPT INSIDE OF <TD> !!!! {% endcomment %}
    <script>
        checkbox = document.getElementById('commerce_meter-selection-{{commerce_meter.id}}')
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('commerce_meter-bulk-action-container').classList.remove('tw-hidden')
                document.getElementById('commerce_meter-view-container').classList.add('tw-hidden')
            } else {
                taskSelections = document.getElementsByClassName('commerce_meter-selection')
                for (let i = 0; i < taskSelections.length; i++) {
                    const element = taskSelections[i];
                    if (element.checked) {
                        return
                    }
                }
                document.getElementById('commerce_meter-bulk-action-container').classList.add('tw-hidden')
                document.getElementById('commerce_meter-view-container').classList.remove('tw-hidden')
            }
        })
    </script>

    {% for column in columns %}
        {% if column == 'meter_id' %}
            <td class="fw-bold text-nowrap special-col min-w-50px" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
                {% comment %} <span class="d-none manage-full-wizard-button inventory_{{inventory.id}}_activity"
                    hx-get="{% url 'shopturbo_load_drawer' %}"
                    hx-vals = '{"tab":"activity", "drawer_type":"commerce_meter-manage", "inventory_id":"{{inventory.id}}", "view_id": "{{view_id}}", "module":"{{module}}", "page": "{{page}}" }'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                    hx-trigger="click"
                    hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
                    >
                </span> {% endcomment %}
                <a id="profile_wizard_button" class="{% include "data/utility/table-link.html" %} manage-full-wizard-button view_form_trigger{{commerce_meter.id}} commerce_meter_{{commerce_meter.id}}"
                    hx-get="{% host_url 'manage_commerce_meter_record' commerce_meter.id host 'app' %}"
                    hx-vals='{"module":"{{menu_key}}"{% if view_id %},"view_id": "{{view_id}}" {% endif %} {% if page %},"page": "{{page}}"{% endif %}}'
                    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                    hx-target="#manage-full-drawer-content"
                    hx-trigger="click"
                >
                {{ commerce_meter.meter_id|stringformat:"04d" }}
                </a>
            </td>
            <td class="" style="width: 20px;">
            </td>
        {% elif column == "customer" %}
            <td class="fw-bolder">
                {{commerce_meter.company_id}}
                {% if commerce_meter.company %}
                    {% include 'data/partials/partial-load-contact-company-drawer.html' with company=commerce_meter.company %}
                {% elif commerce_meter.contact %}
                    {% include 'data/partials/partial-load-contact-company-drawer.html' with contact=commerce_meter.contact %}
                {% endif %}
            </td>

        {% elif column == "item" %}
            <td class="fw-bold">
                <a id="profile_wizard_button_{{commerce_meter.item.id}}" class="{% include "data/utility/table-link-shopturbo.html" %} item_{{commerce_meter.item.id}}" 
                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                    hx-vals = '{"drawer_type":"item-manage", "item_id":"{{commerce_meter.item.id}}","source":"{{object_type}}"{% if view_id %},"view_id":"{{view_id}}"{% endif %} }'
                    hx-target="#shopturbo-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                    hx-trigger="click"
                >
                    {% get_object_display commerce_meter.item 'commerce_items' %} 
                </a>
            </td>
        
        {% elif column == "usage" %}
            <td class="fw-bold">
                {{commerce_meter.usage}}
            </td>
        
        {% elif column == "subscription" %}
            <td class="fw-bold">
                {% if commerce_meter.subscription %}
                    <div class="d-flex">
                        <a id="profile_wizard_button" class="{% include "data/utility/table-link.html" %} manage-full-wizard-button"
                            hx-get="{% host_url 'load_manage_subscriptions_drawer' host 'app' %}"
                            hx-target="#manage-full-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                            hx-trigger="click"
                            hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true", "drawer_type":"subscriptions-manage", "subscription_id":"{{commerce_meter.subscription.id}}"}'>
                            <div class="mb-1">
                                #{{commerce_meter.subscription.subscriptions_id|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}サブスクリプション{% else %}Subscription{% endif %}
                            </div>
                        </a>
                    </div>
                    <script>
                        function getDrawerURL(){
                            return encodeURIComponent(window.location.href.replace(window.location.search, "") + "{{drawer_query_params|safe}}");
                        }
                    </script>
                {% endif %}
            </td>
        
        {% elif column == "updated_at" %}
            <td class="fw-bold">
                {% date_format commerce_meter.updated_at 1 %}
            </td>
        
        {% elif column == "created_at" %}
            <td class="fw-bold">
                {% date_format commerce_meter.created_at 1 %}
            </td>
        {% else %}
            <td class="fw-bold">
                {% include "data/common/custom_field/row-partial-list.html" with CustomFieldName=custom_column_values|get_dict_value:column|get_dict_value:"field_name" obj_id=commerce_meter.id value=custom_column_values|get_dict_value:column|get_value:"value" object_type='commerce_meter' %}
            </td>
        {% endif %}
    {% endfor %}