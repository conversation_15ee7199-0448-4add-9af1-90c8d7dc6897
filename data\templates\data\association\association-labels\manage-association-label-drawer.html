{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="border-0 card shadow-none rounded-0 bg-white">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if association_label %}
                {% if LANGUAGE_CODE == 'ja'%}
                    アソシエーションの管理
                {% else %}
                    Manage Association
                {% endif %}
            {% else %}
                {% if LANGUAGE_CODE == 'ja'%}
                    アソシエーションを作成する
                {% else %}
                    Create Association
                {% endif %}
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    <div class="card-body">
        <form method="POST" action="{% host_url 'manage_association_label' host 'app' %}">
            {% csrf_token %}

            {% if association_label %}
            <input hidden name="association_label_id" value="{{association_label.id}}">
            {% endif %}
            <input hidden name="object_source" value="{{object_source}}">

            <div class="mb-5">
                <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0 required">
                    {% if LANGUAGE_CODE == 'ja'%}
                    ラベル
                    {% else %}
                    Label
                    {% endif %}
                </span>
            
                <input required class="form-control" type="text" name="label"
                    {% if association_label.created_by_sanka %}disabled{% endif %}
                    placeholder="{% if LANGUAGE_CODE == 'ja' %}ラベル{% else %}Label{% endif %}"
                    {% if association_label %}
                    value="{{association_label.label}}"
                    {% endif %}
                    onkeyup="check_property(this)"
                /> 

                <div id="existing-error" class="d-none mt-3 tw-text-red-500 tw-text-left" style="color:#ef4444 !important;">
                    {% if LANGUAGE_CODE == 'ja'%}
                    この値は既に存在します。
                    {% else %}
                    This value already exists.
                    {% endif %}
                </div>
            </div>

            <div class="mb-5 {% if property.id == 'counter_category' or property.id == 'category'%}d-none{% endif%}">
                <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
                    {% if LANGUAGE_CODE == 'ja'%}
                    関連オブジェクト
                    {% else %}
                    Association objects
                    {% endif %}
                </span>

                <select id="object-target-{{association_label.id}}" name="object_target"
                    {% if association_label.created_by_sanka %}disabled{% endif %}
                    class="h-40px form-select form-select-solid border select2-this {% if not association_label.created_by_sanka %}bg-white{% endif %}"
                    data-control="select2" multiple>

                    {% for object_target in ASSOCIATION_OBJECT_TARGETS %}
                        <option value="{{object_target}}"
                            {% if object_target|to_str|in_list:association_label.object_target %}
                                selected
                            {% endif %}
                            >

                            {% if LANGUAGE_CODE == 'ja' %}
                                {{object_source|page_group_to_object_singular:request.LANGUAGE_CODE}}から{{object_target|page_group_to_object_singular:request.LANGUAGE_CODE}}
                            {% else %}
                                {{object_source|page_group_to_object_singular:request.LANGUAGE_CODE}}-to-{{object_target|page_group_to_object_singular:request.LANGUAGE_CODE}}
                            {% endif %}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="mb-3 project_selector {% if constant.TYPE_OBJECT_TASK not in association_label.object_target or not association_label %}d-none{% endif %}">
                <label class="fs-5 fw-bold mb-2">
                    <span class=""> 
                        {% if LANGUAGE_CODE == 'ja'%}
                        プロジェクトID
                        {% else %}
                        Project ID
                        {% endif %}
                    </span>
                </label>
        
                <select
                    class="h-40px form-select form-select-solid border bg-white select2-this"
                    id="project-selector-{{association_label.id}}"
                    data-placeholder="{% if LANGUAGE_CODE == 'ja'%}プロジェクトを選択{% else %}Select Project{% endif %}"
                    name="project_target"
                    >
                    {% with projects=request|get_project_objects %}
                        {% for project in projects %}
                            <option value="{{ project.id }}" {% if association_label.project_target == project.id|to_str or association_label.created_by_sanka and project.default %} selected {% endif %}>
                                {{project.title}}
                            </option>
                        {% endfor %}
                    {% endwith %}
                </select>
            </div>

            <div class="mb-5 d-flex fs-6">
                <input class="form-check-input cursor-pointer" type="checkbox" id="one-to-one-association-type" name="one_to_one_association_type"
                {% if association_label.association_type == "one_to_one" %}checked{% endif %}
                {% if association_label.created_by_sanka %}disabled{% endif %}>
                <label for="one-to-one-association-type" class="ms-4 mb-3 cursor-pointer">{% if LANGUAGE_CODE == 'ja' %}関連するレコードを一つに限定する{% else %}Only one record to be associated{% endif %}
                </label>
            </div>

            {% if not association_label.created_by_sanka %}
            <div class=" d-flex">
                <button id='property_update' type="submit" class="btn btn-dark me-3">
                {% if association_label %}
                    {% if LANGUAGE_CODE == 'ja'%}
                    更新
                    {% else %}
                    Update
                    {% endif %}
                {% else %}
                    {% if LANGUAGE_CODE == 'ja'%}
                    作成
                    {% else %}
                    Create
                    {% endif %}
                {% endif %}
                </button>

                {% comment %} Delete button {% endcomment %}
                {% if association_label %}
                    <button name="delete-association-label" type="submit" class="btn btn-danger">
                        {% if LANGUAGE_CODE == 'ja'%}
                        削除
                        {% else %}
                        Delete
                        {% endif %}
                    </button>
                {% endif %}
            </div>
            {% endif %}
        </form>

        {% if association_label.label == constant.TYPE_OBJECT_INVENTORY or association_label.label == constant.TYPE_OBJECT_INVENTORY_TRANSACTION %}
            {% if language_code == 'ja' %}
                関連する在庫トランザクションに基づいて在庫が計算されます。
            {% else %}
                Inventory Calculated based on Associated Inventory Transaction
            {% endif %}
        {% endif %}
    </div>
</div>

<script>
    $('#object-target-{{association_label.id}}').select2();
    $('#project-selector-{{association_label.id}}').select2();

    // Function to toggle project selector visibility based on object_target selection
    function toggleProjectSelector() {
        const objectTargetSelect = document.querySelector('select[name="object_target"]');
        const projectSelector = document.querySelector('.project_selector');
        
        if (objectTargetSelect && projectSelector) {
            // Get selected values (for multiple select)
            const selectedValues = Array.from(objectTargetSelect.selectedOptions).map(option => option.value);
            
            // Check if TYPE_OBJECT_TASK is selected
            const taskTypeSelected = selectedValues.includes('{{constant.TYPE_OBJECT_TASK}}');
            
            if (taskTypeSelected) {
                projectSelector.classList.remove('d-none');
            } else {
                projectSelector.classList.add('d-none');
            }
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleProjectSelector();
    });

    // Listen for changes on the object_target select
    document.querySelector('select[name="object_target"]').addEventListener('change', function() {
        toggleProjectSelector();
    });

    // Also listen for Select2 changes since you're using Select2
    $('#object-target-{{association_label.id}}').on('change', function() {
        toggleProjectSelector();
    });

    function check_property(elm){
        {% if existing_custom_property %}
            var existing_custom_property = {{existing_custom_property|safe}}
            if (existing_custom_property.includes(elm.value.trim())) {
                // Code to execute if the value is in the array
                var existing_name = document.getElementById('existing-error')
                existing_name.classList.remove('d-none')

                var property_update = document.getElementById('property_update')
                if (property_update){property_update.disabled = true;}
            } else {
                // Code to execute if the value is not in the array
                var existing_name = document.getElementById('existing-error')
                existing_name.classList.add('d-none')
                var property_update = document.getElementById('property_update')
                if (property_update){property_update.disabled = false;}
            }
        {% endif %}
    }
</script>

