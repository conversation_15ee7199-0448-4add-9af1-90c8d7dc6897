from django.http import HttpResponse
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse

from data.constants.properties_constant import (DEAFULT_ASSOCIATION_OBJECT_MEMBER,DEAFULT_ASSOCIATION_OBJECT_TARGET,OBJECT_TYPE_TO_SLUG,
                                                TYPE_OBJECT_ORDER, TYPE_OBJECT_CUSTOM_OBJECT, TYPE_OBJECT_TASK,
                                                TYPE_OBJECT_ITEM, TYPE_OBJECT_SUBSCRIPTION, TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_INVOICE, TYPE_OBJECT_RECEIPT, TYPE_OBJECT_PURCHASE_ORDER,
                                                TYPE_OBJECT_COMPANY,TYPE_OBJECT_INVENTORY_WAREHOUSE,TYPE_OBJECT_INVENTORY, TYPE_OBJECT_CASE, TYPE_OBJECT_CONTACT,TYPE_OBJECT_ESTIMATE,TYPE_OBJECT_INVENTORY_TRANSACTION,TYPE_OBJECT_BILL,TYPE_OBJECT_EXPENSE)
from data.models import (AssociationLabel, AssociationLabelImplementation,AssociationLabelObject,Company,Contact,Estimate,Invoice,InventoryTransaction,PurchaseOrders,ShopTurboSubscriptions,
                         Module, Workspace, ShopTurboOrders,CustomObject,CustomObjectPropertyRow, Projects)
from utils.decorator import login_or_hubspot_required
from utils.utility import build_redirect_url, get_workspace
from django.utils import timezone
from utils.properties.properties import get_properties_with_details
from utils.utility import is_valid_uuid
from utils.properties.properties import get_page_object,property_display


@login_or_hubspot_required
def association_labels_settings(request):
    workspace = get_workspace(request.user)
    object_source = request.GET.get('page_group_type')

    #auto populate label
    # delete -> developer mode
    # AssociationLabel.objects.filter(workspace=workspace, object_source=object_source, created_by_sanka=True).delete()
    auto_create_necessary_association_label_setting(workspace, object_source)

    association_labels = AssociationLabel.objects.filter(
        workspace=workspace, object_source=object_source).order_by('created_at')
    
    related_association_labels = AssociationLabel.objects.filter(created_by_sanka=False, workspace=workspace, object_target__icontains=object_source).exclude(
        object_source=object_source).order_by("created_at")

    context = {
        'object_source': object_source,
        'association_labels': association_labels,
        'related_association_labels': related_association_labels,
    }
    return render(request, 'data/association/association-labels/association-label-settings.html', context)


@login_or_hubspot_required
def manage_association_label(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == 'GET':
        object_source = request.GET.get('object_source')
        association_label_id = request.GET.get('association_label_id')
        association_label = None

        ASSOCIATION_OBJECT_TARGETS=[]
        if object_source in DEAFULT_ASSOCIATION_OBJECT_MEMBER:
            ASSOCIATION_OBJECT_TARGETS = DEAFULT_ASSOCIATION_OBJECT_MEMBER[object_source]
            if 'customer' in ASSOCIATION_OBJECT_TARGETS:ASSOCIATION_OBJECT_TARGETS.remove('customer')

            # self association
            if not object_source in ASSOCIATION_OBJECT_TARGETS:
                ASSOCIATION_OBJECT_TARGETS.append(object_source)

        elif is_valid_uuid(object_source):
            ASSOCIATION_OBJECT_TARGETS = DEAFULT_ASSOCIATION_OBJECT_MEMBER[TYPE_OBJECT_CUSTOM_OBJECT]
        custom_objects = CustomObject.objects.filter(
            workspace=workspace)
        for custom_object in custom_objects:
            if str(custom_object.id) != object_source and custom_object.id not in ASSOCIATION_OBJECT_TARGETS:
                ASSOCIATION_OBJECT_TARGETS.append(custom_object.id)
  
        if association_label_id:
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
        
        page_obj = get_page_object(object_source, lang)
        custom_model = page_obj['custom_model']
        try:
            properties = get_properties_with_details(object_source, workspace, lang)
            default_properties = [property_display(f"{p['id']}|{object_source}", lang, workspace)['name'] for p in properties if not is_valid_uuid(p['id'])]
            names = custom_model.objects.filter(workspace=workspace).values_list('name', flat=True)
        except:
            default_properties = []
            names = []
        
        context = {
            'object_source': object_source,
            'association_label': association_label,
            'ASSOCIATION_OBJECT_TARGETS': ASSOCIATION_OBJECT_TARGETS,
            'existing_custom_property':default_properties + list(names)
        }
        return render(request, 'data/association/association-labels/manage-association-label-drawer.html', context)

    # POST
    association_label_id = request.POST.get('association_label_id')
    label = request.POST.get('label')
    object_source = request.POST.get('object_source')
    object_targets = request.POST.getlist('object_target')
    project_targets = request.POST.get('project_target', None)
    one_to_one_association_type = request.POST.get(
        'one_to_one_association_type')
    

    if 'delete-association-label' in request.POST and association_label_id:
        if association_label_id:
            AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace).delete()
            
        if is_valid_uuid(object_source):
            try:
                custom_object = CustomObject.objects.get(
                    id=object_source)
                return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
            except CustomObject.DoesNotExist:
                print('Custom object does not exist')
                return redirect(reverse('workspace_setting', host='app'))
        return redirect(reverse('workspace_setting', host='app')+f'?setting_type={object_source}')

    if association_label_id:
        try:
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
        except AssociationLabel.DoesNotExist:
            print('Association label does not exist')
            if is_valid_uuid(object_source):
                try:
                    custom_object = CustomObject.objects.get(
                        id=object_source)
                    return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
                except CustomObject.DoesNotExist:
                    print('Custom object does not exist')
                    return redirect(reverse('workspace_setting', host='app'))
            return redirect(reverse('workspace_setting', host='app')+f'?setting_type={object_source}')
        association_label.label = label
        association_label.label_ja = label  # Set label_ja to the same value for user-created custom objects
        association_label.project_target = project_targets if project_targets else None
        association_label.object_source = object_source
        association_label.object_target = ','.join(object_targets)
        association_label.association_type = 'one_to_one' if one_to_one_association_type else 'many_to_many'
        association_label.save()
    else:
        association_label = AssociationLabel.objects.create(
            workspace=workspace, label=label, label_ja=label,  # Set label_ja to the same value for user-created custom objects
            object_source=object_source,
            project_target=project_targets if project_targets else None,
            object_target=','.join(object_targets),
            association_type='one_to_one' if one_to_one_association_type else 'many_to_many')

    if is_valid_uuid(object_source):
        try:
            custom_object = CustomObject.objects.get(
                id=object_source)
            return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
        except CustomObject.DoesNotExist:
            print('Custom object does not exist')
            return redirect(reverse('workspace_setting', host='app'))
    return redirect(reverse('workspace_setting', host='app')+f'?setting_type={object_source}')

@login_or_hubspot_required
def manage_related_association_label(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == 'GET':
        object_source = request.GET.get('object_source')
        redirect_object_source = request.GET.get('redirect_object_source')
        association_label_id = request.GET.get('association_label_id')
        association_label = None

        ASSOCIATION_OBJECT_TARGETS=[]
        if object_source in DEAFULT_ASSOCIATION_OBJECT_MEMBER:
            ASSOCIATION_OBJECT_TARGETS = DEAFULT_ASSOCIATION_OBJECT_MEMBER[object_source]
            if 'customer' in ASSOCIATION_OBJECT_TARGETS:ASSOCIATION_OBJECT_TARGETS.remove('customer')

            # self association
            if not object_source in ASSOCIATION_OBJECT_TARGETS:
                ASSOCIATION_OBJECT_TARGETS.append(object_source)

        elif is_valid_uuid(object_source):
            ASSOCIATION_OBJECT_TARGETS = DEAFULT_ASSOCIATION_OBJECT_MEMBER[TYPE_OBJECT_CUSTOM_OBJECT]
        custom_objects = CustomObject.objects.filter(
            workspace=workspace)
        for custom_object in custom_objects:
            if str(custom_object.id) != object_source and custom_object.id not in ASSOCIATION_OBJECT_TARGETS:
                ASSOCIATION_OBJECT_TARGETS.append(custom_object.id)
  
        if association_label_id:
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
        
        page_obj = get_page_object(object_source, lang)
        custom_model = page_obj['custom_model']
        try:
            properties = get_properties_with_details(object_source, workspace, lang)
            default_properties = [property_display(f"{p['id']}|{object_source}", lang, workspace)['name'] for p in properties if not is_valid_uuid(p['id'])]
            names = custom_model.objects.filter(workspace=workspace).values_list('name', flat=True)
        except:
            default_properties = []
            names = []
        
        context = {
            'object_source': object_source,
            'redirect_object_source': redirect_object_source,
            'association_label': association_label,
            'ASSOCIATION_OBJECT_TARGETS': ASSOCIATION_OBJECT_TARGETS,
            'existing_custom_property':default_properties + list(names)
        }
        return render(request, 'data/association/association-labels/manage-related-association-label-drawer.html', context)

    # POST
    association_label_id = request.POST.get('association_label_id')
    label = request.POST.get('label')
    object_source = request.POST.get('object_source')
    redirect_object_source = request.POST.get('redirect_object_source')
    object_targets = request.POST.getlist('object_target')
    project_targets = request.POST.get('project_target', None)
    one_to_one_association_type = request.POST.get(
        'one_to_one_association_type')
    

    if 'delete-association-label' in request.POST and association_label_id:
        if association_label_id:
            AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace).delete()
            
        if is_valid_uuid(redirect_object_source):
            try:
                custom_object = CustomObject.objects.get(
                    id=redirect_object_source)
                return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
            except CustomObject.DoesNotExist:
                print('Custom object does not exist')
                return redirect(reverse('workspace_setting', host='app'))
        return redirect(reverse('workspace_setting', host='app')+f'?setting_type={redirect_object_source}')

    if association_label_id:
        try:
            association_label = AssociationLabel.objects.get(
                id=association_label_id, workspace=workspace, object_source=object_source)
        except AssociationLabel.DoesNotExist:
            print('Association label does not exist')
            if is_valid_uuid(redirect_object_source):
                try:
                    custom_object = CustomObject.objects.get(
                        id=redirect_object_source)
                    return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
                except CustomObject.DoesNotExist:
                    print('Custom object does not exist')
                    return redirect(reverse('workspace_setting', host='app'))
            return redirect(reverse('workspace_setting', host='app')+f'?setting_type={redirect_object_source}')
        association_label.label = label
        association_label.project_target = project_targets if project_targets else None
        association_label.object_source = object_source
        association_label.association_type = 'one_to_one' if one_to_one_association_type else 'many_to_many'
        association_label.save()
    else:
        association_label = AssociationLabel.objects.create(
            workspace=workspace, label=label,
            object_source=object_source,
            project_target=project_targets if project_targets else None,
            object_target=','.join(object_targets),
            association_type='one_to_one' if one_to_one_association_type else 'many_to_many')

    if is_valid_uuid(redirect_object_source):
        try:
            custom_object = CustomObject.objects.get(
                id=redirect_object_source)
            return redirect(reverse('workspace_setting', host='app')+f'?custom_object={custom_object.slug}')
        except CustomObject.DoesNotExist:
            print('Custom object does not exist')
            return redirect(reverse('workspace_setting', host='app'))
    return redirect(reverse('workspace_setting', host='app')+f'?setting_type={redirect_object_source}')



def auto_create_necessary_association_label_setting(workspace:Workspace,page_group_type):

    if page_group_type in [TYPE_OBJECT_ORDER,TYPE_OBJECT_CASE,TYPE_OBJECT_CONTACT,TYPE_OBJECT_COMPANY,TYPE_OBJECT_INVENTORY_TRANSACTION,TYPE_OBJECT_INVENTORY,TYPE_OBJECT_SUBSCRIPTION,TYPE_OBJECT_ESTIMATE,TYPE_OBJECT_INVOICE,TYPE_OBJECT_RECEIPT]:

        default_properties = get_properties_with_details(page_group_type, workspace, 'en')
        default_association = [item for item in default_properties if item['type'] == 'association']
        
        for association in default_association:
            
            if association['id'] in DEAFULT_ASSOCIATION_OBJECT_TARGET:
                object_targets = DEAFULT_ASSOCIATION_OBJECT_TARGET[association['id']]
            else:
                object_targets = [association['id']]

            association_label,created = AssociationLabel.objects.get_or_create(workspace=workspace, label=association['id'], object_source=page_group_type, object_target=",".join(object_targets), association_type='one_to_one', created_by_sanka=True)
            if not created:
                association_label.updated_at=timezone.now()
            association_label.save()

    return True

def load_association_label_form(request):
    workspace = get_workspace(request.user)
    obj_id = request.GET.get('obj_id')
    property = request.GET.get('property')
    page_group_type = request.GET.get('page_group_type')

    created_by_sanka = False
    if is_valid_uuid(property):
        association_label = AssociationLabel.objects.filter(workspace=workspace,id=property).first()
    else:
        association_label = AssociationLabel.objects.filter(workspace=workspace,label__iexact=property,object_source=page_group_type).first()
        if association_label.created_by_sanka:
            created_by_sanka = True
    
    companies=[]
    contacts=[]
    subscriptions=[]
    estimates=[]
    purchaseorders=[]
    invoices=[]
    inventory_transactions=[]
    cases=[]
    delivery_notes=[]
    orders=[]
    custom_objects = []
    tasks = []
    receipts = []
    bills = []
    expenses = []
    inventory = []
    items = []
    inventory_warehouses = []

    print('Loading association label form for:', property, 'on page group type:', page_group_type)
    object_targets = association_label.object_target.split(',')
    for object_target in object_targets:
        page_obj = get_page_object(object_target, 'en')
        if object_target == TYPE_OBJECT_COMPANY:
            companies = page_obj['base_model'].objects.filter(workspace=workspace,status='active').order_by("-"+page_obj['id_field'])
            if obj_id:companies = companies.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_CONTACT:
            contacts = page_obj['base_model'].objects.filter(workspace=workspace,status='active').order_by("-"+page_obj['id_field'])
            if obj_id:contacts = contacts.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_SUBSCRIPTION:
            subscriptions = page_obj['base_model'].objects.filter(workspace=workspace,status='active').order_by("-"+page_obj['id_field'])
            if obj_id:subscriptions = subscriptions.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_ESTIMATE:
            estimates = page_obj['base_model'].objects.filter(workspace=workspace).order_by("-"+page_obj['id_field'])
            if obj_id:estimates = estimates.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_PURCHASE_ORDER:
            purchaseorders = page_obj['base_model'].objects.filter(workspace=workspace,usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:purchaseorders = purchaseorders.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_INVOICE:
            invoices = page_obj['base_model'].objects.filter(workspace=workspace,usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:invoices = invoices.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_INVENTORY_TRANSACTION:
            inventory_transactions = page_obj['base_model'].objects.filter(workspace=workspace,usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:inventory_transactions = inventory_transactions.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_CASE:
            cases = page_obj['base_model'].objects.filter(workspace=workspace,status='active').order_by("-"+page_obj['id_field'])
            if obj_id:cases = cases.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_DELIVERY_NOTE:
            delivery_notes = page_obj['base_model'].objects.filter(workspace=workspace,usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:delivery_notes = delivery_notes.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_ORDER:
            orders = page_obj['base_model'].objects.filter(workspace=workspace,status='active').order_by("-"+page_obj['id_field'])
            if obj_id:orders = orders.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_RECEIPT:
            receipts = page_obj['base_model'].objects.filter(workspace=workspace,usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:receipts = receipts.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_BILL:
            bills = page_obj['base_model'].objects.filter(workspace=workspace,usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:bills = bills.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_EXPENSE:
            expenses = page_obj['base_model'].objects.filter(workspace=workspace,usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:expenses = expenses.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_INVENTORY:
            inventory = page_obj['base_model'].objects.filter(workspace=workspace,status='active').order_by("-"+page_obj['id_field'])
            if obj_id:inventory = inventory.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_ITEM:
            items = page_obj['base_model'].objects.filter(workspace=workspace,status='active').order_by("-"+page_obj['id_field'])
            if obj_id:items = items.exclude(id=obj_id)
        elif object_target == TYPE_OBJECT_INVENTORY_WAREHOUSE:
            inventory_warehouses = page_obj['base_model'].objects.filter(workspace=workspace,usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:inventory_warehouses = inventory_warehouses.exclude(id=obj_id)
        elif is_valid_uuid(object_target):
            custom_objects = CustomObjectPropertyRow.objects.filter(workspace=workspace, custom_object_id=object_target).order_by("-row_id")
        elif object_target == TYPE_OBJECT_TASK:
            project_target = association_label.project_target
            if not project_target:
                project_target = Projects.objects.filter(
                    workspace=workspace, default=True).first()
                if project_target:
                    project_target = str(project_target.id)
            tasks = page_obj['base_model'].objects.filter(project_target=project_target,
                workspace=workspace, usage_status='active').order_by("-"+page_obj['id_field'])
            if obj_id:tasks = tasks.exclude(id=obj_id)

    # choose stored data related to this obj_id and pagegouptype and label
    selected_associations=None
    if obj_id:
        try:
            page_obj = get_page_object(page_group_type, 'en')
            object = page_obj['base_model'].objects.filter(id=obj_id).first()
        except:
            object = CustomObjectPropertyRow.objects.filter(id=obj_id).first()
        if object:
            selected_associations = AssociationLabelObject.get_for_object(object,workspace,association_label).values_list('source_object_id','target_object_id')
            selected_associations = list(set(str(item) for tuple_item in selected_associations for item in tuple_item))
            if obj_id in selected_associations:
                selected_associations.remove(obj_id)

    print('Selected associations:', selected_associations, created_by_sanka)
    if not selected_associations and created_by_sanka and obj_id:
        reverse_association_label = AssociationLabel.objects.filter(workspace=workspace,created_by_sanka=True, object_source__icontains=association_label.object_target, object_target__icontains=page_group_type).first()
        if reverse_association_label:
            selected_associations = AssociationLabelObject.get_for_target(object,workspace,reverse_association_label).values_list('source_object_id','target_object_id')
            selected_associations = list(set(str(item) for tuple_item in selected_associations for item in tuple_item))
            if obj_id in selected_associations:
                selected_associations.remove(obj_id)

    context = {
        'obj_id': obj_id,
        'property': property,
        'page_group_type': page_group_type,
        'association_label': association_label,

        'companies': companies,
        'contacts': contacts,
        'subscriptions': subscriptions,
        'estimates': estimates,
        'purchaseorders': purchaseorders,
        'invoices': invoices,
        'inventory_transactions': inventory_transactions,
        'inventory': inventory,
        'cases':cases,
        'delivery_notes':delivery_notes,
        'orders':orders,
        'custom_objects': custom_objects,
        'tasks': tasks,
        'receipts': receipts,
        'bills': bills,
        'expenses': expenses,
        'items': items,
        'inventory_warehouses': inventory_warehouses,

        'selected_associations': selected_associations,
    }
    
    side_menu= request.GET.get('side_menu', False) == 'True'
    if side_menu:
        print('logging side menu', request.GET)

        return render(request, 'data/partials/manage-drawer/associated-label-form-side-menu.html', context)

    return render(request, 'data/partials/manage-drawer/associated-label-form.html', context)


def save_association_label(request, obj, page_group_type=None):
    workspace = get_workspace(request.user)

    # In your view handling the POST request
    for key in request.POST.keys():
        if key.startswith('association_label#'):
            # Extract the ID from the key
            association_label_id = key.split('#')[-1]
            association_label = AssociationLabel.objects.filter(id=association_label_id).first()
     
            if association_label:
                # Reset associations for this label first
                AssociationLabelObject.reset_associations_for_object(
                    obj, 
                    workspace, 
                    association_label
                )
                
                # Get ALL values for this key (this is the fix!)
                values = request.POST.getlist(key)
                # Process each value
                for value in values:
                    if value:  # Make sure value is not empty
                        # Process the value
                        value_parts = value.split('#')
                        if len(value_parts) == 2:
                            object_id = value_parts[0]
                            object_type = value_parts[1]
                            try:
                                page_obj = get_page_object(object_type, 'en')
                                target_object = page_obj['base_model'].objects.filter(id=object_id).first()
                            except:
                                target_object = CustomObjectPropertyRow.objects.filter(id=object_id).first()                    

                            if target_object:
                                AssociationLabelObject.create_association(obj, target_object, workspace, association_label)

                                #keep old one -> commented out because it's not working
                                # old_save_association_label(obj,target_object,object_type, page_group_type)


    return True           
       

def save_association_label_side_menu(request):
    workspace = get_workspace(request.user)
    obj_id = request.POST.get('obj_id')
    page_group_type = request.POST.get('page_group_type')

    obj = None
    custom_object = None
    if page_group_type:
        try:
            page_obj = get_page_object(page_group_type, 'en')
            obj = page_obj['base_model'].objects.filter(id=obj_id).first()
        except Exception:
            obj = CustomObjectPropertyRow.objects.filter(id=obj_id).first()
            if obj:
                custom_object = obj.custom_object

    if not obj:
        return redirect(reverse("main", host="app"))

    save_association_label(request, obj, page_group_type)

    module = Module.objects.filter(workspace=workspace, object_values__contains=str(
        page_group_type)).order_by('order', 'created_at').first()

    if module:
        module_slug = module.slug
        if custom_object:
            module_object_slug = custom_object.slug
        else:
            module_object_slug = OBJECT_TYPE_TO_SLUG.get(page_group_type, page_group_type)

        url = reverse(
            "load_object_page",
            host="app",
            kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
        )
        url = url + f"?id={obj_id}&target={page_group_type}"
        return redirect(url)
    return redirect(reverse("main", host="app"))

#======= not being used

def old_save_association_label(obj:ShopTurboOrders,target_object,target_object_type, page_group_type=None):

    if page_group_type == TYPE_OBJECT_ORDER and obj:
        if target_object_type == TYPE_OBJECT_COMPANY:
            obj.company = target_object
            obj.contact = None
        elif target_object_type == TYPE_OBJECT_CONTACT:
            obj.company = None
            obj.contact = target_object
        elif target_object_type == TYPE_OBJECT_ESTIMATE:
            obj.estimate.add(target_object)
        elif target_object_type == TYPE_OBJECT_INVOICE:
            obj.invoice.add(target_object)
        elif target_object_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
            obj.inventory_transactions.add(target_object)
        elif target_object_type == TYPE_OBJECT_PURCHASE_ORDER:
            obj.purchase_orders.add(target_object)
        elif target_object_type == TYPE_OBJECT_CASE:
            obj.cases.add(target_object)
        elif target_object_type == TYPE_OBJECT_DELIVERY_NOTE:
            obj.deliveryslip.add(target_object)
        obj.save()


@login_or_hubspot_required
def apply_association_label(request):
    workspace = get_workspace(request.user)
    if request.method == 'GET':
        object_source = request.GET.get('object_source')
        object_source_id = request.GET.get('object_source_id')
        object_target = request.GET.get('object_target')
        object_target_id = request.GET.get('object_target_id')
        association_label_implementation_id = request.GET.get(
            'association_label_implementation_id')
        
        association_label_implementation = None
        if association_label_implementation_id:
            try:
                association_label_implementation = AssociationLabelImplementation.objects.get(
                    id=association_label_implementation_id, label__workspace=workspace, object_source_id=object_source_id)
            except AssociationLabelImplementation.DoesNotExist:
                print('Association label implementation does not exist')
                return HttpResponse('Association label implementation does not exist', status=404)
        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=object_source, object_target__contains=object_target)
        
        context = {
            'association_label_implementation': association_label_implementation,
            'object_source': object_source,
            'object_source_id': object_source_id,
            'object_target': object_target,
            'object_target_id': object_target_id,
            'association_labels': association_labels,
            'view': request.GET.get('view'),
        }
        return render(request, 'data/association/association-labels/apply-association-label-drawer.html', context)

    # POST
    association_label_implementation_id = request.POST.get(
        'association_label_implementation_id')
    association_label_id = request.POST.get('association_label_id')
    object_source = request.POST.get('object_source')
    object_source_id = request.POST.get('object_source_id')
    object_target = request.POST.get('object_target')
    object_target_id = request.POST.get('object_target_id')
    view = request.POST.get('view')
    page = request.POST.get('page')

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
    module_slug = request.POST.get('module')
    module = Module.objects.filter(
        workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER).order_by('order', 'created_at')
    if module_slug:
        module = module.filter(slug=module_slug).first()
    else:
        module = module.first()
        module_slug = module.slug
    redirect_url = reverse('main', host='app')
    if module:
        redirect_url = build_redirect_url(reverse('load_object_page', host='app', kwargs={
                                          'module_slug': module_slug, 'object_slug': module_object_slug}), view_id=view, page=page, id=object_source_id, target=TYPE_OBJECT_ORDER)
    if not (association_label_id and object_source_id and object_target_id and object_source and object_target):
        print(
            f'Invalid request {association_label_id} {object_source_id} {object_target_id}')
        return redirect(redirect_url)

    try:
        association_label = AssociationLabel.objects.get(
            id=association_label_id, workspace=workspace)
    except AssociationLabel.DoesNotExist:
        print('Association label does not exist')
        return redirect(redirect_url)

    if association_label_implementation_id:
        try:
            association_label_implementation = AssociationLabelImplementation.objects.get(
                id=association_label_implementation_id, label__workspace=workspace, object_source_id=object_source_id, object_target_id=object_target_id)
            association_label_implementation.label = association_label
            association_label_implementation.save()
        except AssociationLabel.DoesNotExist:
            print('Association label does not exist')
            return redirect(redirect_url)
    else:
        association_label_implementation = AssociationLabelImplementation.objects.create(
            label=association_label, object_source_id=object_source_id, object_target_id=object_target_id)
        
    if association_label.association_type == 'one_to_one':
        try:
            AssociationLabelImplementation.objects.filter(
                label=association_label, object_source_id=object_source_id).exclude(id=association_label_implementation.id).delete()
        except:
            pass

    return redirect(redirect_url)


@login_or_hubspot_required
def unapply_association_label(request, id):
    workspace = get_workspace(request.user)
    try:
        AssociationLabelImplementation.objects.get(
            id=id, label__workspace=workspace).delete()
    except AssociationLabel.DoesNotExist:
        print('Association label does not exist')
        return HttpResponse('Association label does not exist', status=404)

    return HttpResponse()