from data.models import (
    Channel,
    ContactsNameCustomField,
    ContactsValueCustomField,
    ShopTurboItemsDiscount,
    TransferHistory,
    ShopTurboOrdersPlatforms,
    ShopTurboOrders,
    ShopTurboShippingCost,
    ShopTurboShippingCostPlatforms,
    ShopTurboItemsPlatforms,
    ShopTurboItemsOrders,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsValueCustomField,
    ShopTurboItemsPrice,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    Contact,
    Company,
    ContactsPlatforms,
    CompanyPlatforms,
    Notification,
)

from utils.logger import logger
from utils.meter import has_quota
from django.core.exceptions import MultipleObjectsReturned

import traceback

from ..utilities.import_order_utils import parsed_phone_number
from .get_shopify_orders import get_shopify_orders, get_shopify_order_discount
from .get_shopify_customers import pull_shopify_customer

from data.constants.constant import ORDER_USAGE_CATEGORY

import shopify
api_version = "2025-07"


def import_shopify_orders(
    channel_id,
    mapping_custom_fields=None,
    mapping_contact_custom_fields=None,
    default_customer=None,
    key_item_field=None,
    how_to_import=None,
    sync_method=None
):
    """
    Import Shopify orders using GraphQL API.
    This function maintains the exact same business logic as the REST version,
    only changing the data fetching mechanism to use GraphQL.
    """
    channel = Channel.objects.get(id=channel_id)
    access_token = channel.access_token
    shop_name = channel.account_id
    platform = channel.integration.slug
    workspace = channel.workspace
    task = (
        TransferHistory.objects.filter(
            workspace=workspace, type="import_order")
        .order_by("-created_at")
        .first()
    )

    logger.info(f"Starting GraphQL import for platform: {platform}, shop_name: {shop_name}")

    if not mapping_custom_fields:
        mapping_custom_fields = {}
        try:
            mapping_custom_fields = {
                k: v for k, v in mapping_contact_custom_fields.items() if "|order" in v
            }
            for k in mapping_custom_fields:
                mapping_contact_custom_fields.pop(k)
        except:
            logger.info(f"Error at Order mapping")
            traceback.print_exc()
    if not mapping_contact_custom_fields:
        mapping_contact_custom_fields = {'en'}
        try:
            mapping_contact_custom_fields = {
                k: v for k, v in mapping_custom_fields.items() if "|contact" in v
            }
            for k in mapping_contact_custom_fields:
                mapping_custom_fields.pop(k)
        except:
            logger.info(f"Error at Contact mapping")
            traceback.print_exc()

    logger.info(f"Order mapping: {mapping_custom_fields}")
    logger.info(f"Contact mapping: {mapping_contact_custom_fields}")

    # Fetch orders using GraphQL
    all_orders = get_shopify_orders(access_token, shop_name)
    all_orders = all_orders[::-1]

    if task:
        task.total_number = len(all_orders)
        task.save()

    for order in all_orders:
        try:
            logger.info(
                f"Processing order: {order.data['number']}, klass: {order.klass}, is_draft: {order.klass == shopify.resources.draft_order.DraftOrder}")
            
            task = (
                TransferHistory.objects.filter(
                    workspace=workspace, type="import_order")
                .order_by("-created_at")
                .first()
            )
            if task.status == "canceled":
                return False

            progress = 100 * (all_orders.index(order) + 1) / len(all_orders)
            task.success_number = all_orders.index(order) + 1
            task.progress = progress
            task.save()
            logger.info(
                f"Getting Order Discounts for order: {order.data['number']}")
            # Extract order data - preserving exact same logic
            financial_status = (
                order.financial_status if hasattr(
                    order, "financial_status") else None
            )
            fulfillment_status = (
                order.fulfillment_status
                if hasattr(order, "fulfillment_status")
                else None
            )
            return_status = (
                order.return_status if hasattr(
                    order, "return_status") else None
            )

            order_id = order.id
            order_display_name = order.name
            currency = order.currency
            item_price = order.subtotal_price
            total_price = order.total_price
            tax = order.total_tax
            order_items = order.line_items
            platform_order_id = order_id

            # Order platform handling - exact same logic
            order_platform, _ = ShopTurboOrdersPlatforms.objects.get_or_create(
                channel=channel, platform_order_id=platform_order_id
            )

            if order_platform.order:
                shopturbo_order = order_platform.order
            else:
                if not has_quota(workspace, ORDER_USAGE_CATEGORY):
                    for user in workspace.user.all():
                        if user.verification.language == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=user,
                                message="制限を超えたため、受注レコードを作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=user,
                                message="Order could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                                type="error",
                            )
                    return False
                shopturbo_order = ShopTurboOrders.objects.create(
                    workspace=workspace,
                    currency=currency,
                    platform=platform,
                    status="active",
                    order_type="item_order",
                )

            # Update order platform - exact same logic
            order_platform.order = shopturbo_order
            order_platform.order_display_name = order_display_name
            order_platform.order_at = order.created_at
            if financial_status:
                order_platform.payment_status = financial_status
            if fulfillment_status:
                order_platform.fulfillment_status = fulfillment_status
            if return_status:
                order_platform.return_status = return_status
            order_platform.save()

            logger.info(f"Order platform saved: {order_platform}")

            # Order pricing - exact same logic
            if item_price:
                shopturbo_order.item_price_order = float(item_price)

            if total_price:
                shopturbo_order.total_price = float(total_price)

            # Tax handling - exact same logic
            tax_inclusive = False
            if hasattr(order, "tax_inclusive") or hasattr(order, "taxes_included"):
                tax_inclusive = order.tax_inclusive or order.taxes_included
            if tax_inclusive:
                shopturbo_order.line_item_type = 'item_based_tax'
                shopturbo_order.tax_inclusive = True

            if tax and total_price and float(total_price) > 0 and not tax_inclusive:
                shopturbo_order.tax = round(
                    100 * float(tax) /
                    float(shopturbo_order.item_price_order), 2
                )
                shopturbo_order.tax_applied_to = "only_items"
                shopturbo_order.total_price_without_tax = float(
                    total_price) - float(tax)
            else:
                shopturbo_order.tax = 0.0
                shopturbo_order.total_price_without_tax = shopturbo_order.total_price

            if tax:
                shopturbo_order.total_price_without_tax = float(
                    total_price) - float(tax)

                tax_rate = round((float(tax) /
                                 shopturbo_order.total_price_without_tax) * 100, 2)
            else:
                tax_rate = 0.0


            # Shipping handling - exact same logic
            if order.shipping_line:
                shipping_line = order.shipping_line
                shipping_cost_price = shipping_line.original_price_set["shop_money"]["amount"]
                shipping_cost_currency = shipping_line.original_price_set[
                    "shop_money"]["currency_code"]
                shipping_cost_price = float(shipping_cost_price)
                shipping_cost_tax = sum([float(
                    tl.rate) for tl in shipping_line.tax_lines]) if shipping_line.tax_lines else 0.0
                shipping_cost = None
                try:
                    shipping_cost = ShopTurboShippingCost.objects.get(
                        name=f"Shipping Cost {shipping_cost_price}",
                        workspace=workspace,
                        value=shipping_cost_price,
                        tax=shipping_cost_tax
                    )
                except MultipleObjectsReturned:
                    shipping_cost = ShopTurboShippingCost.objects.filter(
                        name=f"Shipping Cost {shipping_cost_price}",
                        workspace=workspace,
                        value=shipping_cost_price,
                        tax=shipping_cost_tax
                    ).first()
                    if not shipping_cost:
                        logger.exception(
                            "MultipleObjectsReturned but no shipping_cost found after filtering.")
                except Exception as e:
                    logger.exception(e)
                    logger.info(
                        f"ShopTurboShippingCost {shipping_cost_price} not found in workspace {workspace}. Creating new one..."
                    )
                    shipping_cost = ShopTurboShippingCost.objects.create(
                        name=f"Shipping Cost {shipping_cost_price}",
                        workspace=workspace,
                    )

                if shipping_cost:
                    shipping_cost.value = shipping_cost_price
                    shipping_cost.tax = float(shipping_cost_tax) * 100
                    shipping_cost.number_format = shipping_cost_currency
                    shipping_cost.save()

                    shipping_platform = ShopTurboShippingCostPlatforms.objects.filter(
                        shipping_cost=shipping_cost,
                        channel=channel,
                        platform_type='order'
                    ).first()
                    if not shipping_platform:
                        ShopTurboShippingCostPlatforms.objects.create(
                            channel=channel,
                            platform_type='order',
                            platform_id=platform_order_id,
                            shipping_cost=shipping_cost,
                        )

                    shopturbo_order.shipping_cost = shipping_cost

            if currency:
                shopturbo_order.currency = currency

            # adding discount data
            order_discount_data = get_shopify_order_discount(
                access_token, shop_name, order.data['id'])
            discount_value = 0
            discount_percentage = 0
            discount_price = None
            for discount in order_discount_data:
                if currency == discount.currency:
                    if discount.target == 'LINE_ITEM':
                        discount_value += float(discount.amount)
                    elif discount.target == 'ORDER':
                        if discount.type == 'PricingPercentageValue':
                            discount_percentage = int(discount.percentage)
            if discount_value:
                discount_price, _ = ShopTurboItemsDiscount.objects.get_or_create(
                    value=discount_value, number_format=currency.lower(), discount_type='free_writing_discounts')
            elif discount_percentage:
                discount_price, _ = ShopTurboItemsDiscount.objects.get_or_create(
                    value=discount_percentage, number_format='%', discount_type='free_writing_discounts')
            if discount_price:
                shopturbo_order.discounts = discount_price
                shopturbo_order.discount_tax_option = 'post_tax'


            # Draft order handling - exact same logic
            if order.klass == shopify.resources.draft_order.DraftOrder:
                shopturbo_order.delivery_status = "draft"

            shopturbo_order.save()
            shopturbo_order.shopturboitemsorders_set.all().delete()
            
            # Line items processing - exact same logic preserved
            if order_items and len(order_items) > 0:
                update_value = False if how_to_import == "match" else True

                if not key_item_field:
                    key_item_field = "None"

                for item in order_items:
                    item_name = item.name
                    item_price = item.price
                    number_of_item = item.quantity
                    if item.product_id:
                        if sync_method == 'platform':
                            item_id = item.product_id
                        else:
                            item_id = item.sku if item.sku else item.product_id
                        item_platform, _ = (
                            ShopTurboItemsPlatforms.objects.get_or_create(
                                channel=channel,
                                platform_id=item_id,
                                platform_type="default",
                            )
                        )

                        shopturbo_item_order, _ = (
                            ShopTurboItemsOrders.objects.get_or_create(
                                platform_item_id=item_id,
                                order=shopturbo_order,
                                order_platform=order_platform,
                            )
                        )
                        if item_platform.item:
                            shopturbo_item = item_platform.item
                            if key_item_field != "None":
                                custom_field = (
                                    ShopTurboItemsNameCustomField.objects.filter(
                                        workspace=workspace, name=key_item_field
                                    ).first()
                                )
                                custom_value = (
                                    ShopTurboItemsValueCustomField.objects.filter(
                                        field_name=custom_field, value=item_id
                                    ).first()
                                )
                                if custom_value:
                                    shopturbo_item.product_id = None
                                    item_platform.item = None
                                    shopturbo_item.save()
                                    item_platform.save()
                                    shopturbo_item = custom_value.items
                                    shopturbo_item.product_id = item_id
                                else:
                                    custom_value, _ = (
                                        ShopTurboItemsValueCustomField.objects.get_or_create(
                                            field_name=custom_field,
                                            items=shopturbo_item,
                                        )
                                    )
                                    custom_value.value = item_id
                                    custom_value.save()
                        else:
                            if shopturbo_item_order.item:
                                shopturbo_item = shopturbo_item_order.item
                                if key_item_field != "None":
                                    custom_field = (
                                        ShopTurboItemsNameCustomField.objects.filter(
                                            workspace=workspace, name=key_item_field
                                        ).first()
                                    )
                                    custom_value, _ = (
                                        ShopTurboItemsValueCustomField.objects.get_or_create(
                                            field_name=custom_field,
                                            items=shopturbo_item,
                                        )
                                    )
                                    custom_value.value = item_id
                                    custom_value.save()
                            else:
                                if key_item_field != "None":
                                    custom_field = (
                                        ShopTurboItemsNameCustomField.objects.filter(
                                            workspace=workspace, name=key_item_field
                                        ).first()
                                    )
                                    custom_value = (
                                        ShopTurboItemsValueCustomField.objects.filter(
                                            field_name=custom_field, value=item_id
                                        ).first()
                                    )
                                    if custom_value:
                                        shopturbo_item = custom_value.items
                                        shopturbo_item.product_id = item_id
                                    else:
                                        update_value = True
                                        shopturbo_item = ShopTurboItems.objects.create(
                                            workspace=workspace,
                                            platform=platform,
                                            product_id=item_id,
                                        )
                                        custom_value, _ = (
                                            ShopTurboItemsValueCustomField.objects.get_or_create(
                                                field_name=custom_field,
                                                items=shopturbo_item,
                                            )
                                        )
                                        custom_value.value = item_id
                                        custom_value.save()
                                else:
                                    update_value = True
                                    shopturbo_item = ShopTurboItems.objects.create(
                                        workspace=workspace,
                                        platform=platform,
                                    )
                        if number_of_item:
                            shopturbo_item_order.number_item = float(
                                number_of_item)
                        if item_price:
                            shopturbo_item_order.item_price_order = float(
                                item_price)
                        if tax_rate:
                            shopturbo_item_order.item_price_order_tax = tax_rate
                        if currency:
                            shopturbo_item_order.currency = currency
                        shopturbo_item_order.save()

                        if update_value:
                            if item_name:
                                shopturbo_item.name = item_name
                            if item_price:
                                shopturbo_item.price = float(item_price)
                            if currency:
                                shopturbo_item.currency = currency

                        shopturbo_item.status = "active"
                        shopturbo_item.save()

                        item_platform.platform_id = item_id
                        item_platform.item = shopturbo_item
                        item_platform.save()

                        price, _ = ShopTurboItemsPrice.objects.get_or_create(
                            item=shopturbo_item,
                            price=float(item.price),
                            currency=currency,
                        )
                        check_default = ShopTurboItemsPrice.objects.filter(
                            item=shopturbo_item, default=True
                        )
                        if len(check_default) == 0:
                            price.default = True
                        price.name = item_name
                        price.save()

                        shopturbo_item_order.item = shopturbo_item
                        shopturbo_item_order.save()
                    else:
                        shopturbo_item_order, _ = (
                            ShopTurboItemsOrders.objects.get_or_create(
                                order=shopturbo_order,
                                custom_item_name=item_name,
                                number_item=number_of_item,
                                item_price_order=item_price,
                                item_price_order_tax=tax_rate,
                                total_price=float(
                                    item_price) * float(number_of_item),
                                currency=currency,
                            )
                        )

                shopturbo_order.number_item = len(order_items)
                shopturbo_order.save()

            # Customer handling - exact same logic preserved
            default_contact = None
            default_company = None
            try:
                default_contact = Contact.objects.filter(
                    id=default_customer).first()
                default_company = Company.objects.filter(
                    id=default_customer).first()
            except:
                pass

            if default_contact:
                shopturbo_order.contact = default_contact
            elif default_company:
                shopturbo_order.company = default_company
            elif hasattr(order, "customer"):
                if hasattr(order.customer, "id"):
                    if channel.ms_refresh_token:
                        company_platforms = CompanyPlatforms.objects.filter(
                            channel=channel, platform_id=order.customer.id
                        ).order_by("-created_at")

                        if len(company_platforms) > 0:
                            company_platform = company_platforms[0]
                        else:
                            company_platform = CompanyPlatforms.objects.create(
                                channel=channel, platform_id=order.customer.id
                            )

                        if company_platform.company:
                            company = company_platform.company

                        if not company_platform.company:
                            company, _ = Company.objects.get_or_create(
                                workspace=workspace,
                                name=order.customer.first_name,
                                email=order.customer.email,
                            )
                            company_platform.company = company
                            company_platform.save()

                        if order.customer.last_name:
                            company.name = (
                                order.customer.first_name
                                + " "
                                + order.customer.last_name
                            )
                        if order.customer.phone:
                            company.phone_number = parsed_phone_number(
                                order.customer.phone
                            )
                        company.status = "active"
                        company.save()
                        shopturbo_order.company = company
                    else:
                        contact_platforms = ContactsPlatforms.objects.filter(
                            channel=channel, platform_id=order.customer.id
                        ).order_by("-created_at")

                        if len(contact_platforms) > 0:
                            contact_platform = contact_platforms[0]
                        else:
                            contact_platform = ContactsPlatforms.objects.create(
                                channel=channel, platform_id=order.customer.id
                            )

                        if contact_platform.contact:
                            contact = contact_platform.contact

                        if not contact_platform.contact:
                            contact, _ = Contact.objects.get_or_create(
                                workspace=workspace,
                                email=order.customer.email,
                                name=order.customer.first_name,
                            )

                        if mapping_contact_custom_fields and contact:
                            for key, field in mapping_contact_custom_fields.items():
                                custom_value = None
                                sanka_field = field.replace("|contact", "")
                                contactsnamecustomfields = (
                                    ContactsNameCustomField.objects.filter(
                                        workspace=workspace, name=sanka_field
                                    )
                                ).first()
                                if contactsnamecustomfields:
                                    custom_value, _ = (
                                        ContactsValueCustomField.objects.get_or_create(
                                            field_name=contactsnamecustomfields,
                                            contact=contact,
                                        )
                                    )

                                if key in order.data['shippingAddress']:
                                    new_order_field = order.data['shippingAddress']
                                elif key in order.data:
                                    new_order_field = order.data
                                else:
                                    if key in order.data['customer']:
                                        new_order_field = order.data['customer']
                                    elif len(order.data['customer']['addresses']) > 0:
                                        new_order_field = order.data['customer']['addresses'][0]
                                    else:
                                        new_order_field = None

                                logger.info(
                                    f"CONTACT: Found value for field: {key} - {new_order_field}")
                                if new_order_field:
                                    value = new_order_field.get(key)
                                else:
                                    value = None

                                if custom_value:  # In Contact can add custom property value
                                    custom_value.value = value
                                    custom_value.save()
                                else:
                                    if key == "phone":
                                        item_phone_number = value
                                        if item_phone_number:
                                            phone_number = parsed_phone_number(
                                                item_phone_number)
                                            contact.__setattr__(
                                                sanka_field, phone_number)
                                    else:
                                        contact.__setattr__(
                                            sanka_field, value)
                            # Save contact with custom fields
                            contact.save()

                            contact_platform.contact = contact
                            contact_platform.save()

                        if order.customer.last_name:
                            contact.last_name = order.customer.last_name
                        if order.customer.phone:
                            contact.phone_number = parsed_phone_number(
                                order.customer.phone
                            )
                        contact.status = "active"
                        contact.save()
                        shopturbo_order.contact = contact

            shopturbo_order.save()

            # Custom fields handling - exact same logic preserved
            if mapping_custom_fields:
                logger.info(
                    f"Setting custom fields for order")
                for key_field, field in mapping_custom_fields.items():
                    try:
                        # Enabled multi object in mapping shopify
                        sanka_field = field.replace("|order", "")
                        value = None
                        key_field = key_field.replace("|order", "")
                        if key_field in [
                            "country",
                            "address1",
                            "province",
                            "city",
                            "zip",
                            "address2",
                        ]:
                            if key_field in order.data['shippingAddress']:
                                new_order_field = order.data['shippingAddress']
                            else:
                                if len(order.data['customer']['addresses']) > 0:
                                    value = order.data['customer']['addresses'].get(
                                        key_field, None)
                                else:
                                    new_order_field = None

                            if new_order_field:
                                try:
                                    value = new_order_field.get(key_field)
                                    logger.info('Found value for field: ' +
                                                str(key_field) + ' - ' + str(value))
                                except:
                                    logger.info('value not found for field: ' +
                                                str(new_order_field) + ' - ' + str(key_field))
                            else:
                                value = None

                        else:
                            if hasattr(order, key_field):
                                value = getattr(order, key_field)
                                logger.info('Found value for field: ' +
                                            str(key_field) + ' - ' + str(value) + ' - ' + str(sanka_field))

                        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
                            workspace=workspace, name=sanka_field
                        ).first()
                        if custom_field:
                            logger.info(
                                f"Custom field {sanka_field} found: {custom_field.name}")
                            custom_value, _ = (
                                ShopTurboOrdersValueCustomField.objects.get_or_create(
                                    field_name=custom_field, orders=shopturbo_order
                                )
                            )
                            custom_value.value = value
                            custom_value.save()
                    except:
                        traceback.print_exc()
                        logger.error(
                            f"Error setting custom field {field} for order {order.id}")

                logger.info(f"Set props for order: {shopturbo_order.__dict__}")
                shopturbo_order.save()
        except Exception as e:
            logger.error(
                f"Error processing order: {str(e)} - {str(traceback.print_exc())}")
            continue

    # Customer sync - exact same logic preserved
    if (
        mapping_contact_custom_fields
        and not default_customer
        and not channel.ms_refresh_token
    ):
        pull_shopify_customer(channel_id, mapping_contact_custom_fields)

    return True