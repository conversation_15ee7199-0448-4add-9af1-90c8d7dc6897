{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}


{% comment %} Zengin Invoice {% endcomment %}
    {% csrf_token %}
    
    
    <div class="mb-3">
        <label class="mb-2 mt-5 fs-5 ">
            {% if LANGUAGE_CODE == 'ja' %}
            請求書の情報
            {% else %}
            Invoices Informations
            {% endif %}
        </label>
    </div>
    

    <div class="mb-6">
        <label class="{% include 'data/utility/form-label.html' %}">
            <span class="required">
                {% if LANGUAGE_CODE == 'ja'%}
                日付範囲を選択
                {% else %}
                Select Date Range  
                {% endif %}
            </span>
        </label>

        <div class="mb-0">
            <input autocomplete="off" form="object-action-form" id="date_select{% if action_index %}-{{action_index}}{% endif %}" required name="date_select{% if action_index %}-{{action_index}}{% endif %}" value="{{selected_date}}"
                class="form-control form_date cursor-pointer" 
                placeholder="{% if LANGUAGE_CODE == 'ja'%}日付範囲を選択{% else %}Select Date Range{% endif %}" 
            >
        </div>
    </div>
    <div class="mb-6">
        <span class="py-0 mb-3 border-0 fs-5 fw-bold text-active-primary ms-0 required">
            {% if LANGUAGE_CODE == 'ja'%}
            連絡先プロパティ
            {% else %}
            Contact Property  
            {% endif %}
        </span>
        <select data-allow-clear="true" name="contact_cf{% if action_index %}-{{action_index}}{% endif %}"
            required id="contact_cf{% if action_index %}-{{action_index}}{% endif %}"
            class="min-h-40px form-select action-select2 form-select-solid border bg-white"
            hx-trigger="htmx-change,load"
            hx-get="{% host_url 'create_invoice_zengin_format' host 'app' %}?action_index={% if action_index %}{{action_index}}{% endif %}{% if action_node_id %}&action_node_id={{action_node_id}}{% endif %}"
            hx-swap="innerHTML"
            hx-target="#zengin-format-list-container"
            
            {% if LANGUAGE_CODE == 'ja'%}
                data-placeholder="連絡先を選択"
            {% else %}
                data-placeholder="Select Contact Property"
            {% endif %}
            >
            {% for data in cf_data %}
                <option {% if data.slug|stringify in contact_cf %}selected{% endif %} value="{{data.slug}}">
                    {{data.display}}
                </option>
            {% endfor %}
        </select>
    </div>
    
    <div class="mb-3 form-check form-switch form-check-custom form-check-solid">
        <input id="download_with_pdf{% if action_index %}-{{action_index}}{% endif %}" name="download_with_pdf{% if action_index %}-{{action_index}}{% endif %}" class="form-check-input" type="checkbox" {% if download_with_pdf %} checked {% endif %} value="download">
        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="download_with_pdf{% if action_index %}-{{action_index}}{% endif %}">
            {% if LANGUAGE_CODE == 'ja' %}
            統合PDFをダウンロード
            {% else %}
            Download the combined PDFs
            {% endif %}
        </label>
    </div>
    
    <div class="mb-3 form-check form-switch form-check-custom form-check-solid">
        <input id="exclude_status{% if action_index %}-{{action_index}}{% endif %}" name="exclude_status_toggle{% if action_index %}-{{action_index}}{% endif %}" class="form-check-input" type="checkbox" {% if exclude_status and exclude_status|to_str != "['']" %} checked {% endif %} value="exclude">
        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="exclude_status{% if action_index %}-{{action_index}}{% endif %}">
            {% if LANGUAGE_CODE == 'ja' %}
            売上請求のステータスを除外
            {% else %}
            Exclude Invoice Status
            {% endif %}
        </label>
        
        <script>
            $(document).ready(function() {
                $('#exclude_status{% if action_index %}-{{action_index}}{% endif %}').change(function() {
                    if ($(this).is(':checked')) {
                        $('#invoice_status_selector{% if action_index %}-{{action_index}}{% endif %}').removeClass('d-none');
                    } else {
                        $('#invoice_status_selector{% if action_index %}-{{action_index}}{% endif %}').addClass('d-none');
                    }
                });
            });
        </script>
    </div>

    <div class="{% if not exclude_status or exclude_status|to_str == "['']" %}d-none{% endif%}" id="invoice_status_selector{% if action_index %}-{{action_index}}{% endif %}">
        <select id="exclude-status-select" 
            class="border min-h-40px form-select select2-zengin" 
            name="exclude_status{% if action_index %}-{{action_index}}{% endif %}"
            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}除外ステータスを選択{% else %}Choose Excluded Status{% endif %}"
            data-allow-clear="true"
            multiple="multiple"
        >
        <option value=""></option>
        {% if 'status'|get_custom_property_object:obj %}
            {% with value_map_label='status'|get_custom_property_object:obj|get_attr:'value'|string_list_to_list %}
                {% for value, label in value_map_label.items %}
                    <option value="{{value}}" {% if value in exclude_status %}selected{% endif %}>
                        {{label}}
                    </option>
                {% endfor %}
            {% endwith %}
        {% else %}
            {% with status=obj|get_default_status_display:'status' %}
                {% for key, value in  status.items %}
                    <option value="{{key}}" {% if key in exclude_status %}selected{% endif %}>
                        {% if LANGUAGE_CODE == 'ja' %}
                            {{value.ja}}
                        {% else %}
                            {{value.en}}
                        {% endif %}
                    </option>
                {% endfor %}
            {% endwith %}
        {% endif %}
    </select>    
    
    <script>
        $('.select2-zengin').select2()
    </script>
    </div>

    <div class="" id="zengin-format-list-container">
    </div>
    

    
    <script>
        $('.form_date').daterangepicker({
            singleDatePicker: false,
            drops: 'auto',
            autoUpdateInput: true,
            
            locale: {
                {% if LANGUAGE_CODE == 'ja' %}
                cancelLabel: 'クリア',
                format: 'YYYY年MM月DD日', // Japanese date format
                separator: ' 〜 ',
                applyLabel: '選択',
                cancelLabel: 'キャンセル',
                fromLabel: 'From',
                toLabel: 'To',
                customRangeLabel: 'カスタム範囲',
                daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                monthNames: [
                    '1月', '2月', '3月', '4月', '5月', '6月',
                    '7月', '8月', '9月', '10月', '11月', '12月'
                ],
                {% else %}
                format: "Y-M-DD",
                cancelLabel: 'Clear',
                {% endif %}
            }
        });
        $('.action-select2').select2()
        $('#contact_cf{% if action_index %}-{{action_index}}{% endif %}').on('select2:select', function (e) {
            var selectedOption = e.params.data.id;
            // Clear the previous selection if no matching option is found
            var selectElement = $(this).closest('select').get(0);
            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
        });
    </script>