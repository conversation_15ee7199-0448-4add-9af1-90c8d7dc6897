from django.db.models import QuerySet
from data.models import Invoice, ShopTurboDecimalPoint
from data.models.workspace import Workspace
import math
from typing import List
from django.db import transaction


def invoice_total_fix(invoice: Invoice, workspace: Workspace):
    """
    Fix individual invoice total amounts based on decimal point configuration.
    This is to fix the amount issue when Decimal Point settings are updated.
    
    Args:
        invoice: Invoice instance to fix
        workspace: Workspace instance
        
    Returns:
        Updated invoice instance
    """
    # Get general decimal point setting
    general_decimal_point = ShopTurboDecimalPoint.objects.filter(
        workspace=workspace, app_name="general"
    ).first()
    
    if not general_decimal_point:
        return invoice
    
    # Get invoice line items
    line_items = invoice.invoice_object.all()
    
    total_price_without_tax = 0.0
    total_price_with_tax = 0.0
    line_item_totals_without_tax = []
    line_item_totals_with_tax = []
    
    # Process each line item
    for line_item in line_items:
        if not line_item.amount_price or not line_item.amount_item:
            continue
            
        unit_price = float(line_item.amount_price)
        quantity = float(line_item.amount_item)
        tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
        
        # Calculate line item totals based on tax configuration
        if invoice.tax_option == "item_based_tax":
            # Use item-specific tax rates
            if invoice.tax_inclusive:
                # Tax-inclusive: derive tax-exclusive from inclusive price
                line_item_total_with_tax = unit_price * quantity
                line_item_total_without_tax = line_item_total_with_tax / (1 + tax_rate / 100.0)
                
                # Apply JPY integer rounding for tax-exclusive amounts
                if invoice.currency and invoice.currency.lower() == "jpy":
                    line_item_total_without_tax = int(line_item_total_without_tax)
            else:
                # Tax-exclusive: calculate inclusive from exclusive price
                line_item_total_without_tax = unit_price * quantity
                line_item_total_with_tax = line_item_total_without_tax * (1 + tax_rate / 100.0)
        else:
            # Use unified tax rate from invoice
            unified_tax_rate = float(invoice.tax_rate) if invoice.tax_rate else 0.0
            
            if invoice.tax_inclusive:
                # Tax-inclusive
                line_item_total_with_tax = unit_price * quantity
                line_item_total_without_tax = line_item_total_with_tax / (1 + unified_tax_rate / 100.0)
                
                # Apply JPY integer rounding for tax-exclusive amounts
                if invoice.currency and invoice.currency.lower() == "jpy":
                    line_item_total_without_tax = int(line_item_total_without_tax)
            else:
                # Tax-exclusive
                line_item_total_without_tax = unit_price * quantity
                line_item_total_with_tax = line_item_total_without_tax * (1 + unified_tax_rate / 100.0)
        
        # Apply line-item specific decimal point formatting if configured
        if general_decimal_point.type in ['line_item_cut_off', 'line_item_cut_over']:
            math_func = math.floor if general_decimal_point.type == 'line_item_cut_off' else math.ceil
            line_item_total_without_tax = math_func(line_item_total_without_tax)
            line_item_total_with_tax = math_func(line_item_total_with_tax)
        
        line_item_totals_without_tax.append(line_item_total_without_tax)
        line_item_totals_with_tax.append(line_item_total_with_tax)
        
        total_price_without_tax += line_item_total_without_tax
        total_price_with_tax += line_item_total_with_tax
    
    # Handle discount if present
    if invoice.discount and float(invoice.discount) > 0:
        discount_amount = float(invoice.discount)
        
        if invoice.discount_option == "%":
            # Percentage discount
            if invoice.discount_tax_option == "pre_tax":
                discount_amount = total_price_without_tax * (discount_amount / 100.0)
                total_price_without_tax -= discount_amount
                
                # Recalculate tax on discounted amount
                if invoice.tax_option == "item_based_tax":
                    # Proportionally distribute discount across items and recalculate tax
                    total_price_with_tax = 0.0
                    for line_item, orig_without_tax in zip(line_items, line_item_totals_without_tax):
                        if total_price_without_tax + discount_amount > 0:  # Avoid division by zero
                            item_discount = (orig_without_tax / (total_price_without_tax + discount_amount)) * discount_amount
                            discounted_without_tax = orig_without_tax - item_discount
                            item_tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
                            discounted_with_tax = discounted_without_tax * (1 + item_tax_rate / 100.0)
                            total_price_with_tax += discounted_with_tax
                else:
                    # Unified tax
                    unified_tax_rate = float(invoice.tax_rate) if invoice.tax_rate else 0.0
                    total_price_with_tax = total_price_without_tax * (1 + unified_tax_rate / 100.0)
            else:
                # Post-tax discount
                discount_amount = total_price_with_tax * (discount_amount / 100.0)
                
                if invoice.tax_option == "item_based_tax":
                    # For item-based tax with post-tax discount, apply discount to without-tax amount
                    total_price_without_tax -= discount_amount
                    
                    # Calculate total tax from original prices (unchanged)
                    total_tax = 0
                    for line_item, orig_without_tax in zip(line_items, line_item_totals_without_tax):
                        item_tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
                        if invoice.currency and invoice.currency.lower() == "jpy":
                            orig_without_tax = int(float(orig_without_tax))
                        item_tax = float(orig_without_tax) * (item_tax_rate / 100.0)
                        item_tax = round(item_tax)
                        total_tax += item_tax
                    
                    total_price_with_tax = total_price_without_tax + total_tax
                else:
                    # For unified tax, keep original logic
                    total_price_with_tax -= discount_amount
        else:
            # Fixed amount discount
            if invoice.discount_tax_option == "pre_tax":
                total_price_without_tax -= discount_amount
                # Recalculate tax on discounted amount (similar to percentage discount)
                if invoice.tax_option != "item_based_tax":
                    unified_tax_rate = float(invoice.tax_rate) if invoice.tax_rate else 0.0
                    total_price_with_tax = total_price_without_tax * (1 + unified_tax_rate / 100.0)
            else:
                # Post-tax discount
                if invoice.tax_option == "item_based_tax":
                    total_price_without_tax -= discount_amount
                    # Recalculate tax as above
                else:
                    total_price_with_tax -= discount_amount
    
    # Apply final decimal point formatting
    if general_decimal_point.type == 'cut_off':
        if invoice.currency and invoice.currency.lower() == "usd":
            total_price_without_tax = math.floor(total_price_without_tax * 100) / 100
            total_price_with_tax = math.floor(total_price_with_tax * 100) / 100
        else:
            total_price_without_tax = math.floor(total_price_without_tax)
            total_price_with_tax = math.floor(total_price_with_tax)
    elif general_decimal_point.type == 'cut_over':
        if invoice.currency and invoice.currency.lower() == "usd":
            total_price_without_tax = math.ceil(total_price_without_tax * 100) / 100
            total_price_with_tax = math.ceil(total_price_with_tax * 100) / 100
        else:
            total_price_without_tax = math.ceil(total_price_without_tax)
            total_price_with_tax = math.ceil(total_price_with_tax)
    
    # Update invoice if values changed
    needs_update = False
    if invoice.total_price_without_tax != total_price_without_tax:
        invoice.total_price_without_tax = total_price_without_tax
        needs_update = True
    
    if invoice.total_price != total_price_with_tax:
        invoice.total_price = total_price_with_tax
        needs_update = True
    
    if needs_update:
        invoice.save(update_fields=["total_price_without_tax", "total_price"])
    
    return invoice


async def invoices_total_fix_bulk_async(invoices: QuerySet[Invoice], workspace: Workspace) -> List[Invoice]:
    """
    Async version of invoices_total_fix_bulk for efficient processing of multiple invoices.
    Uses abulk_update to minimize database queries and native async operations.
    
    Args:
        invoices: QuerySet of Invoice instances to fix
        workspace: Workspace instance
        
    Returns:
        List of updated invoices
    """
    # Get general decimal point setting (async)
    general_decimal_point = await ShopTurboDecimalPoint.objects.filter(
        workspace=workspace, app_name="general"
    ).afirst()
    
    if not general_decimal_point:
        return [invoice async for invoice in invoices]
    
    # Prefetch related line items to avoid N+1 queries (async)
    invoices_with_prefetch = invoices.prefetch_related('invoice_object')
    
    invoices_to_update = []
    updated_invoices = []
    
    # Async iteration over QuerySet
    async for invoice in invoices_with_prefetch:
        # Get line items asynchronously
        line_items = [item async for item in invoice.invoice_object.all()]
        
        total_price_without_tax = 0.0
        total_price_with_tax = 0.0
        line_item_totals_without_tax = []
        line_item_totals_with_tax = []
        
        # Process each line item (same logic as sync version)
        for line_item in line_items:
            if not line_item.amount_price or not line_item.amount_item:
                continue
                
            unit_price = float(line_item.amount_price)
            quantity = float(line_item.amount_item)
            tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
            
            # Calculate line item totals based on tax configuration
            if invoice.tax_option == "item_based_tax":
                # Use item-specific tax rates
                if invoice.tax_inclusive:
                    # Tax-inclusive: derive tax-exclusive from inclusive price
                    line_item_total_with_tax = unit_price * quantity
                    line_item_total_without_tax = line_item_total_with_tax / (1 + tax_rate / 100.0)
                    
                    # Apply JPY integer rounding for tax-exclusive amounts
                    if invoice.currency and invoice.currency.lower() == "jpy":
                        line_item_total_without_tax = int(line_item_total_without_tax)
                else:
                    # Tax-exclusive: calculate inclusive from exclusive price
                    line_item_total_without_tax = unit_price * quantity
                    line_item_total_with_tax = line_item_total_without_tax * (1 + tax_rate / 100.0)
            else:
                # Use unified tax rate from invoice
                unified_tax_rate = float(invoice.tax_rate) if invoice.tax_rate else 0.0
                
                if invoice.tax_inclusive:
                    # Tax-inclusive
                    line_item_total_with_tax = unit_price * quantity
                    line_item_total_without_tax = line_item_total_with_tax / (1 + unified_tax_rate / 100.0)
                    
                    # Apply JPY integer rounding for tax-exclusive amounts
                    if invoice.currency and invoice.currency.lower() == "jpy":
                        line_item_total_without_tax = int(line_item_total_without_tax)
                else:
                    # Tax-exclusive
                    line_item_total_without_tax = unit_price * quantity
                    line_item_total_with_tax = line_item_total_without_tax * (1 + unified_tax_rate / 100.0)
            
            # Apply line-item specific decimal point formatting if configured
            if general_decimal_point.type in ['line_item_cut_off', 'line_item_cut_over']:
                math_func = math.floor if general_decimal_point.type == 'line_item_cut_off' else math.ceil
                line_item_total_without_tax = math_func(line_item_total_without_tax)
                line_item_total_with_tax = math_func(line_item_total_with_tax)
            
            line_item_totals_without_tax.append(line_item_total_without_tax)
            line_item_totals_with_tax.append(line_item_total_with_tax)
            
            total_price_without_tax += line_item_total_without_tax
            total_price_with_tax += line_item_total_with_tax
        
        # Handle discount if present (same complex logic as sync version)
        if invoice.discount and float(invoice.discount) > 0:
            discount_amount = float(invoice.discount)
            
            if invoice.discount_option == "%":
                # Percentage discount
                if invoice.discount_tax_option == "pre_tax":
                    discount_amount = total_price_without_tax * (discount_amount / 100.0)
                    total_price_without_tax -= discount_amount
                    
                    # Recalculate tax on discounted amount
                    if invoice.tax_option != "item_based_tax":
                        unified_tax_rate = float(invoice.tax_rate) if invoice.tax_rate else 0.0
                        total_price_with_tax = total_price_without_tax * (1 + unified_tax_rate / 100.0)
                else:
                    # Post-tax discount
                    discount_amount = total_price_with_tax * (discount_amount / 100.0)
                    
                    if invoice.tax_option == "item_based_tax":
                        total_price_without_tax -= discount_amount
                        # Calculate total tax from original prices
                        total_tax = 0
                        for line_item, orig_without_tax in zip(line_items, line_item_totals_without_tax):
                            item_tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
                            if invoice.currency and invoice.currency.lower() == "jpy":
                                orig_without_tax = int(float(orig_without_tax))
                            item_tax = float(orig_without_tax) * (item_tax_rate / 100.0)
                            item_tax = round(item_tax)
                            total_tax += item_tax
                        total_price_with_tax = total_price_without_tax + total_tax
                    else:
                        total_price_with_tax -= discount_amount
            else:
                # Fixed amount discount
                if invoice.discount_tax_option == "pre_tax":
                    total_price_without_tax -= discount_amount
                    if invoice.tax_option != "item_based_tax":
                        unified_tax_rate = float(invoice.tax_rate) if invoice.tax_rate else 0.0
                        total_price_with_tax = total_price_without_tax * (1 + unified_tax_rate / 100.0)
                else:
                    # Post-tax discount
                    if invoice.tax_option == "item_based_tax":
                        total_price_without_tax -= discount_amount
                    else:
                        total_price_with_tax -= discount_amount
        
        # Apply final decimal point formatting
        if general_decimal_point.type == 'cut_off':
            if invoice.currency and invoice.currency.lower() == "usd":
                total_price_without_tax = math.floor(total_price_without_tax * 100) / 100
                total_price_with_tax = math.floor(total_price_with_tax * 100) / 100
            else:
                total_price_without_tax = math.floor(total_price_without_tax)
                total_price_with_tax = math.floor(total_price_with_tax)
        elif general_decimal_point.type == 'cut_over':
            if invoice.currency and invoice.currency.lower() == "usd":
                total_price_without_tax = math.ceil(total_price_without_tax * 100) / 100
                total_price_with_tax = math.ceil(total_price_with_tax * 100) / 100
            else:
                total_price_without_tax = math.ceil(total_price_without_tax)
                total_price_with_tax = math.ceil(total_price_with_tax)
        
        # Check if updates are needed and prepare for bulk update
        needs_update = False
        if invoice.total_price_without_tax != total_price_without_tax:
            invoice.total_price_without_tax = total_price_without_tax
            needs_update = True
        
        if invoice.total_price != total_price_with_tax:
            invoice.total_price = total_price_with_tax
            needs_update = True
        
        if needs_update:
            invoices_to_update.append(invoice)
        
        updated_invoices.append(invoice)
    
    # Perform bulk update if there are invoices to update (async)
    if invoices_to_update:
        await Invoice.objects.abulk_update(
            invoices_to_update,
            ['total_price_without_tax', 'total_price'],
            batch_size=1000  # Process in batches to avoid memory issues
        )
    
    return updated_invoices