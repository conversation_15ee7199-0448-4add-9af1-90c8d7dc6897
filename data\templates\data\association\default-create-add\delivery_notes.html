{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}


<div class="card shadow-none rounded-0 w-100 vh-100 overflow-hidden border-0 bg-white">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if LANGUAGE_CODE == 'ja' %}
                納品書を追加
            {% else %}
                Add Delivery Notes
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon me-5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    <div class="row px-10 mt-5">
        <div class="col-6">
            <div class="btn-group w-100" role="group">
                <input checked type="radio" class="w-100 text-center btn-check single-entry-section-switcher" name="switch" id="kt_switch_option_1"
                /> 
                <label class="w-100 btn btn-outline btn-outline-dashed btn-outline-default p-4" for="kt_switch_option_1">
                    <span class="text-dark fw-bolder d-block fs-6">
                        {% if LANGUAGE_CODE == 'ja'%}
                        納品レコードの作成
                        {% else %}
                        Create  Delivery Notes Record
                        {% endif %}
                    </span>
                </label>
            </div>
        </div>
        <div class="col-6">
            <div class="btn-group w-100" role="group">
                <input type="radio" class="w-100 text-center btn-check upload-entry-section-switcher" name="switch" id="kt_switch_option_3"
                />
                <label class="w-100 btn btn-outline btn-outline-dashed btn-outline-default p-4" for="kt_switch_option_3">
                    <span class="text-dark fw-bolder d-block fs-6">
                        {% if LANGUAGE_CODE == 'ja'%}
                        納品レコードを選択
                        {% else %}
                        Select Delivery Notes Record
                        {% endif %}
                    </span>
                </label>
            </div>
        </div>
    </div>
    
    <div class='d-none'
        hx-get="{% url object_type|to_drawer_type|add:'_drawer' %}" 
        hx-vals='{"drawer_type":"{{object_type|to_drawer_type}}", {% if view_id %}"view_id":"{{view_id}}", {% endif %} "object_id":"{{object_id}}", "source":"{{source}}", "module":"{{module}}", "type":"create-association"}'
        hx-swap="beforeend"
        hx-target="#create-drawer-content" 
        hx-trigger="load"
    >
    </div>
    
    <div id="create-drawer-content" class="scroll-y px-0">
        
    </div>
    
    <div id="select-drawer-content" class="d-none scroll-y">
        <form id="select-delivery-notes-form" class="px-10 mt-5" method="POST" name="" 
            {% if source == 'company' or source == 'contacts' %}
                action="{% host_url 'customer_update_association_object' object_id host 'app' %}"
            {% elif source == 'commerce_orders' %}
                action="{% host_url 'order_update_association_object' object_id host 'app' %}"
            {% else %}
                action="{% host_url 'save_object_association' host 'app' %}"
            {% endif %}
        >
            {% csrf_token %}

            {% if view_id %}
                <input type="hidden" name="view_id" value="{{view_id}}">
            {% endif %}
            <input type="hidden" name="object_id" value="{{object_id}}">
            <input type="hidden" name="page" value="{{page}}">
            <input type="hidden" name="module" value="{{module}}">
            <input type="hidden" name="update_delivery_notes" value="true">
            <input type="hidden" name="target_object_type" value="{{constant.TYPE_OBJECT_DELIVERY_NOTE}}">
            <input type="hidden" name="source" value="{{source}}">
    
            <div class="{% include "data/utility/form-div.html" %}">          
                <label class="mb-2 d-flex align-items-center fs-4 fw-bold">
                    <span class="" >
                    {% if LANGUAGE_CODE == 'ja'%}
                    納品書
                    {% else %}
                    Delivery Notes
                    {% endif %}
                    </span>
                </label>
                
                <select id="delivery-notes-association-select" placeholder="Choose Delivery Notes" class="border min-h-40px form-select select2-delivery_notes-lazy-association" name="delivery_notes"
                    {% if LANGUAGE_CODE == 'ja'%}
                    data-placeholder="納品書"
                    {% else %}
                    data-placeholder="Delivery Notes"
                    {% endif %}
                    data-allow-clear="true"
                    multiple="multiple"
                    >
                    <option value=""></option>                    
                    {% if source == 'company' %}
                    {% for deliveryslip in commerce_objs %}
                        <option value="{{deliveryslip.id}}" selected>
                            {% if deliveryslip.contact %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | {{deliveryslip.contact|display_contact_name:LANGUAGE_CODE}}
                            {% elif deliveryslip.company %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | {{deliveryslip.company.name}}
                            {% else %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | (Unnamed)
                            {% endif %}
                        </option>
                    {% endfor %}
                    {% elif obj.deliveryslip_set.all %}
                    {% for deliveryslip in obj.deliveryslip_set.all %}
                        <option value="{{deliveryslip.id}}" selected>
                            {% if deliveryslip.contact %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | {{deliveryslip.contact|display_contact_name:LANGUAGE_CODE}}
                            {% elif deliveryslip.company %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | {{deliveryslip.company.name}}
                            {% else %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | (Unnamed)
                            {% endif %}
                        </option>
                    {% endfor %}
                    {% else %}
                    {% for deliveryslip in obj.deliveryslip.all %}
                        <option value="{{deliveryslip.id}}" selected>
                            {% if deliveryslip.contact %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | {{deliveryslip.contact|display_contact_name:LANGUAGE_CODE}}
                            {% elif deliveryslip.company %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | {{deliveryslip.company.name}}
                            {% else %}
                                #{{deliveryslip.id_ds|stringformat:'04d'}} | (Unnamed)
                            {% endif %}
                        </option>
                    {% endfor %}
                    {% endif %}
                </select>     
            
                <script>
                    $('.select2-delivery_notes-lazy-association').select2({
                        ajax: {
                            delay: 250, // wait 250 milliseconds before triggering the request
                            dataType: 'json',
                            url: '{% host_url "get_delivery_notes_object_options" host "app" %}',
                            data: function (params) {
                                    var query = {
                                        q: params.term,
                                        page: params.page || 1,
                                        json_response: true,
                                    }
                                    return query;
                                },
                            minimumInputLength: 2,
                        },
                        language: {
                            "noResults": function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}レコードが見つかりません{% else %}No record found{% endif %}";
                            },
                            searching: function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                            },
                            "loadingMore": function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                            },
                        },
                    })
                </script>
            </div>
    
            <button type="submit" class="btn btn-dark mt-5">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </form>
    </div>
</div>

{% block js %}
    <script>
        $(document).ready(function(){
            $('.single-entry-section-switcher').click(function(){
                $('#create-drawer-content').toggleClass('d-none');
                $('#select-drawer-content').toggleClass('d-none');
            });
            $('.upload-entry-section-switcher').click(function(){
                $('#create-drawer-content').toggleClass('d-none');
                $('#select-drawer-content').toggleClass('d-none');
            });
        });
    </script>
{% endblock %}