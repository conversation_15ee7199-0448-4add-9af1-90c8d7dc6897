import csv
from django.db import models
from django.db.models import Max, Prefetch, OuterRef, Subquery, Q
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from data.models.dataset import Dataset, DatasetMetric
from data.constants.constant import REPORT_OBJECT_GROUP_TYPE
from data.constants.properties_constant import (TYPE_OBJECT_ITEM, TYPE_OBJECT_ORDER, TYPE_OBJECTS, TYPE_OBJECT_PANEL, TYPE_OBJECT_DASHBOARD
                                                , TYPE_OBJECT_INVENTORY_TRANSACTION, TYPE_OBJECT_CONTACT, TYPE_OBJECT_COMPANY, TYPE_OBJECT_INVOICE
                                                , TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_ORDER_LINE_ITEM
                                                )

from utils.decorator import login_or_hubspot_required
from utils.utility import get_workspace
from utils.properties.properties import get_page_object, get_prefix_rel

page_type = "datasets"
@login_or_hubspot_required
def datasets(request):
    workspace = get_workspace(request.user)
    
    if request.method == "GET":
        datasets = Dataset.objects.filter(workspace=workspace)
        
        if request.GET.get('q'):
            q = request.GET.get('q')
            datasets = datasets.filter(
                Q(name__icontains=q) |
                Q(dataset_id__icontains=q) |
                Q(data_source__icontains=q) |
                Q(metrics__metric__icontains=q)
            ).distinct()
            
        datasets = datasets.order_by(f"-updated_at")
        
        context = {
            "datasets": datasets,
            "page_type": page_type,
            "menu_key": 'workspace',
            "permission": "",
            "search_q": request.GET.get('q'),
        }
        
        return render(request, 'data/account/workspace/datasets/index.html', context)
    
def dataset_form(request):
    workspace = get_workspace(request.user)
    
    if request.method == "GET":
        table_properties = {}
        trans_filter = {}
        obj_trans = REPORT_OBJECT_GROUP_TYPE
        
        for table in TYPE_OBJECTS:
            if table not in [TYPE_OBJECT_PANEL, TYPE_OBJECT_DASHBOARD]:
                obj = get_page_object(table)
                base_model = obj['base_model']
                custom_model = obj['custom_model']
                columns_display = obj['columns_display']
                table_properties[table] = []
                trans_filter[table] = {}
                # related_models = obj.get('related_data', None)
                related_models = []

                # Skip processing if base_model is None (object type not fully implemented)
                if base_model is None:
                    continue

                obj_trans_ = obj_trans.get(table)
                obj_trans_en = obj_trans_.get('en')
                obj_trans_ja = obj_trans_.get('ja')

                for field in base_model._meta.fields:
                    if field.is_relation or field.primary_key:
                        continue
                    
                    value = field.name
                    trans = columns_display.get(field.name)

                    display_en = trans.get('en') if trans else value
                    display_ja = trans.get('ja') if trans else value

                    display_en = f"{obj_trans_en} - {display_en}"
                    display_ja = f"{obj_trans_ja} - {display_ja}"

                    table_properties[table].append({
                        'value': f"{table} - {value}",
                        'display': display_en,
                        'display_ja': display_ja,
                    })
                    trans_filter[table][value] = {
                        'en': display_en,
                        'ja': display_ja
                    }

                if custom_model:
                    for field in custom_model.objects.filter(workspace=workspace):
                        if field.name:
                            table_properties[table].append({
                                'value': f"{table} - custom_field__{field.id}",
                                'display': f"{obj_trans_en} - {field.name}",
                                'display_ja': f"{obj_trans_ja} - {field.name}",
                            })

                            trans_filter[table][f"{field.id}"] = {
                                'en': field.name,
                                'ja': field.name
                            }

        context = {
            "columns": table_properties,
            "objects": {
                x: {
                    "en": REPORT_OBJECT_GROUP_TYPE[x]["en"],
                    "ja": REPORT_OBJECT_GROUP_TYPE[x]["ja"],
                }
                for x in TYPE_OBJECTS
                if x not in [TYPE_OBJECT_PANEL, TYPE_OBJECT_DASHBOARD]
            }
        }
        
        if request.GET.get('dataset_id'):
            dataset_id = request.GET.get('dataset_id')
            dataset_qs = Dataset.objects.prefetch_related(
                Prefetch('metrics', queryset=DatasetMetric.objects.order_by('order'))
            )
            dataset_obj = dataset_qs.get(id=dataset_id)
            context['dataset_obj'] = dataset_obj
        
        return render(request, 'data/account/workspace/datasets/form.html', context)
    

    if 'bulk_duplicate_panel' in request.POST:
        dataset_ids = request.POST.getlist('dataset_ids')
        datasets = Dataset.objects.filter(id__in=dataset_ids)
        
        max_dataset_id = Dataset.objects.filter(workspace=workspace).aggregate(
            max_id=Max('dataset_id')
        )['max_id'] or 0
        next_id = max_dataset_id + 1

        for dataset in datasets:
            old_metrics = dataset.metrics.all()
            dataset.id = None
            dataset.dataset_id = next_id
            dataset.save()
            
            new_metrics = []
            for metric in old_metrics:
                new_metric = DatasetMetric.objects.create(
                    metric=metric.metric,
                    order=metric.order
                )
                new_metrics.append(new_metric)
                
            dataset.metrics.set(new_metrics)
            next_id += 1
            
        return redirect('datasets')
    
    if 'bulk_delete_panel' in request.POST:
        dataset_ids = request.POST.getlist('dataset_ids')
        Dataset.objects.filter(id__in=dataset_ids).delete()
        
        return redirect('datasets')

    dataset_id = request.POST.get('dataset_obj_id')
    name = request.POST.get('name')
    object_source = request.POST.get('object_source')
    
    if dataset_id:
        dataset_obj = Dataset.objects.get(id=dataset_id)
    else:
        max_dataset_id = Dataset.objects.filter(workspace=workspace).aggregate(
            max_id=Max('dataset_id')
        )['max_id'] or 0
        next_id = max_dataset_id + 1

        dataset_obj = Dataset(
            workspace=workspace,
            dataset_id=next_id,
            name=name,
            data_source=object_source,
        )
        
    dataset_obj.name = name
    dataset_obj.data_source = object_source
    dataset_obj.edited_by = request.user
    dataset_obj.save()

    metric_keys = [key for key in request.POST if key.startswith('column-picker-')]
    metric_values = [request.POST[key] for key in metric_keys]
    
    dataset_metrics = []
    for idx, metric_value in enumerate(metric_values):
        metric_obj = DatasetMetric.objects.create(
            metric=metric_value,
            order=idx,
        )
        dataset_metrics.append(metric_obj)
        
    dataset_obj.metrics.all().delete()
    dataset_obj.metrics.set(dataset_metrics)
    
    return redirect('dataset_view', dataset_id=dataset_obj.id)
    
def dataset_view(request, dataset_id):
    dataset_obj = Dataset.objects.prefetch_related(
        Prefetch('metrics', queryset=DatasetMetric.objects.order_by('order'))
    ).get(id=dataset_id)
    
    context = {
        "obj": dataset_obj,
        "page_type": page_type,
        "menu_key": 'workspace',
    }
    
    metrics = dataset_obj.metrics.all()
    base_source = dataset_obj.data_source
    relation_sources = []
    
    if ',' in base_source:
        parts = base_source.split(',')
        base_source = parts[0]
        relation_sources = parts[1:]

    selected_fields = []
    display_fields = []
    custom_field_annotations = {}
    for metric in metrics:
        if not metric.metric:
            continue
        metric_path = metric.metric
        if ' - ' in metric_path:
            rel, field = metric_path.split(' - ')
            prefix = get_prefix_rel(base_source, rel)
            
            if field.startswith('custom_field__'):
                field_id = field.split('custom_field__', 1)[1]
                tmp_prefix = prefix
                tmp_prefix = tmp_prefix.replace("__", "")
                alias = f"{tmp_prefix}_cf_{field_id}"
                
                rel_obj = get_page_object(rel)
                rel_obj_model = rel_obj['base_model']
                rel_custom_value_model = rel_obj['custom_value_model']

                rel_custom_field = rel_obj['custom_model'].objects.filter(
                    id=field_id).first()

                related_name = ""
                for field in rel_custom_value_model._meta.fields:
                    if field.related_model == rel_obj_model:
                        related_name = field.name
                        break
                
                if rel_custom_field and rel_custom_field.type in ['date', 'datetime']:
                    custom_field_annotations[alias] = Subquery(rel_custom_value_model.objects.filter(**{
                        f"{related_name}__id": models.OuterRef(f"{prefix}id"),
                        'field_name__id': field_id
                    }).values('value_time')[:1])
                else:
                    custom_field_annotations[alias] = Subquery(rel_custom_value_model.objects.filter(**{
                        f"{related_name}__id": models.OuterRef(f"{prefix}id"),
                        'field_name__id': field_id
                    }).values('value')[:1])

                resolved_field = alias
            else:
                resolved_field = f"{prefix}{field}"
        else:
            resolved_field = metric_path
        selected_fields.append(resolved_field)
        display_fields.append(metric_path)
        
    page_obj = get_page_object(base_source)
    base_model = page_obj['base_model']
    base_id_field = page_obj['id_field']
    try:
        queryset = base_model.objects.filter(workspace=dataset_obj.workspace)
        
        if custom_field_annotations:
            queryset = queryset.annotate(**custom_field_annotations)
            
        queryset = queryset.order_by(base_id_field).values(*selected_fields)
        
        if request.GET.get('export', None):
            return generate_csv(request.LANGUAGE_CODE, base_source, dataset_obj, display_fields, queryset)
        paginator = Paginator(queryset, 25)
        
        page = request.GET.get('page', 1) or 1
        page = int(page)
        try:
            data_rows = paginator.page(page)
        except PageNotAnInteger:
            data_rows = paginator.page(1)
        except EmptyPage:
            data_rows = paginator.page(paginator.num_pages)
            
        context['base_source'] = base_source
        context['display_fields'] = display_fields
        context['data_rows'] = data_rows
        context['paginator'] = paginator
        context['current_page'] = page
        context['prev_page'] = page - 1
        context['next_page'] = page + 1
    except Exception as e:
        context['error'] = e
    
    return render(request, 'data/account/workspace/datasets/dataset-view.html', context)

def generate_csv(lang, base_source, dataset, display_fields, data_queryset):
    trans_display_fields = []
    for field in display_fields:
        src, field_name = field.split(' - ')
        page_obj = get_page_object(src)
        custom_model = page_obj['custom_model']
        columns_display = page_obj['columns_display']
        
        obj_trans = REPORT_OBJECT_GROUP_TYPE.get(src, {
            'en': src,
            'ja': src
        })
        obj_trans = obj_trans.get(lang)
        
        if "custom_field__" in field_name:
            custom_field = field_name.replace("custom_field__", "")
            try:
                custom_props = custom_model.objects.get(id=custom_field)
                display_trans = f"{obj_trans} - {custom_props.name}"
            except:
                display_trans = f"{obj_trans} - {custom_field}"
        else:
            trans = columns_display.get(field_name, {
                'en': field_name,
                'ja': field_name
            })
            trans = trans.get(lang)
            
            display_trans = f"{obj_trans} - {trans}"
            
        trans_display_fields.append(display_trans)

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="dataset_{dataset.name}.csv"'
    writer = csv.writer(response)
    writer.writerow(trans_display_fields)
    
    for row in data_queryset:
        writer.writerow([parse_field_name(base_source, row, field) for field in display_fields])
        
    return response

def parse_field_name(base_source, dictionary, key):
    if dictionary:
        if " - " in key:
            rel, key = key.split(" - ", 1)
            prefix = get_prefix_rel(base_source, rel)
            if "custom_field__" in key:
                key = key.replace("custom_field__", "")
                tmp_prefix = prefix.replace("__", "")
                key = f"{tmp_prefix}_cf_{key}"
            else:
                key = f"{prefix}{key}"
            
        return dictionary.get(key, None)
    return None