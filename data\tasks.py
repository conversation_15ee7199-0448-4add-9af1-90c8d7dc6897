import ast
import json
import traceback
import uuid
from datetime import datetime

import pytz
import requests
from django.conf import settings
from django.core.paginator import EmptyPage, Paginator
from django.db.models import Case, IntegerField, OuterRef, Q, Subquery, Value, When
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from django.urls import resolve
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse

from action import action as sanka_action
from data.constants.constant import (
    DEFAULT_PERMISSION,
    DEFAULT_COLUMNS_WORKFLOW,
    DEFAULT_COLUMNS_TASK,
)
from data.constants.projects_constant import POSTS_PER_PAGE, POSTS_PER_PAGE_BEGIN
from data.constants.properties_constant import (
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_TASK,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_CUSTOM_OBJECT,
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_WORKFLOW,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_PANEL,
    TYPE_OBJECT_DASHBOARD,
    TYPE_OBJECT_GOAL,
    TYPE_OBJECTS,
)
from data.models import (
    Module,
    Task,
    TaskFile,
    TaskItem,
    TaskCustomFieldValue,
    Notification,
    View,
    ViewFilter,
    PropertySet,
    User,
    Verification,
    Deals,
    ShopTurboItems,
    ShopTurboItemsPrice,
    CustomObject,
    ActionTracker,
    WorkflowActionTracker,
    Action,
    ActionNode,
    Workflow,
    Integration,
    Group,
    Projects,
    TaskCustomFieldName,
    ShopTurboOrders,
    WorkflowHistory,
    TaskActionTracker,
    Log,
    AutomationTag,
    BackgroundJob,
    PROJECT_TASK_STATUS,
)
from sanka.settings import WORKFLOW_GALLERY_CREATOR_WORKSPACE
from utils.actions import get_rollback_options
from utils.date import parse_date
from utils.decorator import login_or_hubspot_required
from utils.discord import DiscordNotification
from utils.filter import build_view_filter, is_valid_uuid
from utils.bgjobs.runner import trigger_bg_job
from utils.bgjobs.handler import create_bg_job, add_hatchet_run_id
from data.workflow.background.run_workflow_action import run_workflow_action_task, RunWorkflowActionPayload
from utils.bgjobs.handler import add_bgjob_log_by_bg_job_id
from utils.project import (
    apply_view_filter,
    generate_task_by_view,
    get_ordered_items,
    get_ordered_project_views,
    get_ordered_projects,
    get_ordered_views,
    notif_assigned_assignee,
    remove_assigned_notif,
)
from utils.properties.properties import get_default_property_set
from utils.query_workflows import query_workflows
from utils.utility import (
    check_staff,
    get_workspace,
    get_permission_filter,
    assign_object_owner,
    save_custom_property,
    build_redirect_url,
)
from utils.workflow import get_nodes, is_action_condition_met
from utils.workspace import get_permission
from utils.properties.properties import get_page_object
from utils.logger import logger


@login_or_hubspot_required
def tasks_data(request, id=None):
    """
    Retrieves and renders a paginated, filtered list of tasks for the current workspace.

    Filters tasks by project, search query, view, usage status (active or archived), and user permissions. Supports sorting by custom fields or default fields, applies view-specific filters, and handles pagination. Prepares context including available views, projects, members, property sets, and group members for rendering the task list. Returns a partial HTML template with the task list and related context, and sets an HX-Trigger header to update frontend UI components.
    """
    lang = request.LANGUAGE_CODE
    if not lang:
        lang = "ja"

    workspace = get_workspace(request.user)
    project_id = request.GET.get("project")
    from_page = request.GET.get("from")
    highlight_task_id = request.GET.get("h_task")
    show_task_id = request.GET.get("show_task")
    task_q = request.GET.get("task_q", "")
    module = request.GET.get("module", None)
    menu_key = request.GET.get("menu_key")

    permission = get_permission(object_type=TYPE_OBJECT_TASK, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    if not menu_key:
        module_slug = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
            )
            .order_by("order", "created_at")
            .first()
        )
        if module_slug:
            menu_key = module_slug.slug

    p_id = request.GET.get("p_id")
    project_target = None

    # Robust project handling with fallback logic
    if p_id:
        try:
            # Validate UUID format first
            import uuid

            uuid.UUID(p_id)
            # If valid UUID, try to get the project by the provided p_id
            project_target = Projects.objects.get(id=p_id)
        except (Projects.DoesNotExist, ValueError, TypeError):
            # If project doesn't exist or p_id is invalid, fall back to default project
            project_target = None

    # If no project found or p_id not provided, get or create default project
    if not project_target:
        # Try to get existing projects for the workspace
        projects = Projects.objects.filter(workspace=workspace).order_by("created_at")

        if projects.exists():
            # Use the first existing project as default
            project_target = projects.first()
        else:
            # No projects exist, create a default project following existing patterns
            # Check if there are orphaned tasks or custom fields that need a project
            tasks = Task.objects.filter(workspace=workspace)
            task_csfs = TaskCustomFieldName.objects.filter(workspace=workspace)

            # Create default project with empty title (following existing pattern)
            project_target, _ = Projects.objects.get_or_create(
                workspace=workspace, title=""
            )

            # Associate orphaned tasks with the new project
            if tasks.exists():
                for task in tasks:
                    if not task.project_target:
                        task.project_target = project_target
                        task.save(
                            log_data={"user": request.user, "workspace": workspace}
                        )

            # Associate orphaned task custom fields with the new project
            if task_csfs.exists():
                for task_csf in task_csfs:
                    if not task_csf.project_target:
                        task_csf.project_target = project_target
                        task_csf.save()

        # Update p_id to reflect the actual project being used
        p_id = project_target.id

    view = None
    view_filter = None
    if not id:
        id = request.GET.get("view_id", None)
    if id and id != "None":
        try:
            # Validate that id is a valid UUID - check if it's already a UUID object
            if not isinstance(id, uuid.UUID):
                uuid.UUID(id)
            view = View.objects.get(id=id, project_target=project_target)
        except (ValueError, TypeError, View.DoesNotExist):
            pass

    if project_id:
        # Project task views
        try:
            project = Group.objects.get(id=project_id)
        except Group.DoesNotExist:
            print(f"Project with ID {project_id} does not exist.")
            return HttpResponse(status=404)

        views = get_ordered_project_views(
            project, TYPE_OBJECT_TASK, project_target=project_target, user=request.user
        )
        if task_q:
            tasks = Task.objects.filter(
                workspace=workspace, project=project, title__icontains=task_q
            ).order_by("order")
        else:
            tasks = Task.objects.filter(workspace=workspace, project=project).order_by(
                "order"
            )
    else:
        # General task views
        views = (
            View.objects.filter(
                workspace=workspace, target="task", project_target=project_target
            )
            .annotate(
                is_main=Case(
                    When(title="main", then=Value(0)),
                    # Other titles should come after
                    When(title__isnull=False, then=Value(1)),
                    output_field=IntegerField(),
                )
            )
            .order_by("is_main", "created_at")
        )

        if task_q:
            tasks = Task.objects.filter(
                workspace=workspace, title__icontains=task_q
            ).order_by("-task_id")
        else:
            tasks = Task.objects.filter(workspace=workspace).order_by("-task_id")

    if view:
        view_filter = view.viewfilter_set.filter(property__isnull=True).first()
        if view_filter and view_filter.filter_value:
            conditions = build_view_filter(
                Q(workspace=workspace), view_filter, TYPE_OBJECT_TASK, request=request
            )
            print(".....................", conditions)
            tasks = tasks.filter(conditions)

    if views:
        views = views.filter(project_target=project_target)
    if tasks:
        tasks = tasks.filter(project_target=project_target).order_by("-task_id")

    conditions = Q(workspace=workspace)

    # Handle status filter for archived items
    status = request.GET.get("status")
    if status == "archived":
        conditions &= Q(usage_status="archived")
    else:
        # By default, show only active items
        conditions &= Q(usage_status="active")

    conditions &= get_permission_filter(permission, request.user)

    tasks = tasks.filter(conditions)

    if views:
        hide_private_views = []
        for view in views:
            if view.is_private and (not request.user or view.user != request.user):
                hide_private_views.append(view)

        views = [view for view in views if view not in hide_private_views]

    paginator = None
    # page_length = int(request.GET.get('length', 0))

    # Sorting
    try:
        if view_filter and view_filter.sort_order_by:
            sort_order_method = view_filter.sort_order_method
            sort_order_by = view_filter.sort_order_by

            if is_valid_uuid(sort_order_by):
                field_name = TaskCustomFieldName.objects.filter(
                    id=sort_order_by
                ).first()
                if field_name:
                    custom_value_subquery = TaskCustomFieldValue.objects.filter(
                        task=OuterRef("pk"), field_name=field_name
                    )

                    if field_name.type in ["date", "date_time"]:
                        custom_value_subquery = custom_value_subquery.values(
                            "value_time"
                        )[:1]
                    else:
                        custom_value_subquery = custom_value_subquery.values("value")[
                            :1
                        ]

                    tasks = tasks.annotate(custom_value=Subquery(custom_value_subquery))

                    if sort_order_method == "asc":
                        tasks = tasks.distinct("id", "custom_value").order_by(
                            "custom_value"
                        )
                    else:
                        tasks = tasks.distinct("id", "custom_value").order_by(
                            "-custom_value"
                        )
            else:
                if sort_order_by == "manual_sort":
                    tasks = tasks.distinct("id", "order").order_by("order")
                else:
                    if sort_order_method == "asc":
                        tasks = tasks.distinct("id", sort_order_by).order_by(
                            sort_order_by
                        )
                    else:
                        tasks = tasks.distinct("id", sort_order_by).order_by(
                            "-" + sort_order_by
                        )
        else:
            tasks = tasks.distinct("id", "task_id").order_by("-task_id")
    except Exception as e:
        print("[DEBUG] ERROR on Tasks:", e)
        # Create a fresh queryset to avoid any annotation conflicts
        tasks = Task.objects.filter(conditions).distinct("id", "task_id").order_by("-task_id")

    try:
        pagination_number = view_filter.pagination if view_filter else None
        if not pagination_number:
            pagination_number = POSTS_PER_PAGE
    except:
        pagination_number = POSTS_PER_PAGE

    paginator = Paginator(tasks, pagination_number)
    page = request.GET.get("page", 1)
    page = int(page)
    if page:
        page_content = paginator.page(page)
        task_list = page_content.object_list
        paginator_item_begin = (pagination_number * int(page)) - POSTS_PER_PAGE_BEGIN
        paginator_item_end = pagination_number * int(page)
    else:
        page_content = paginator.page(1)
        paginator_item_begin = pagination_number * 1 - POSTS_PER_PAGE_BEGIN
        paginator_item_end = pagination_number * 1

    try:
        highlight_task = Task.objects.get(id=highlight_task_id)
    except Task.DoesNotExist:
        highlight_task = None

    try:
        show_task = Task.objects.get(id=show_task_id)
    except Task.DoesNotExist:
        show_task = None

    projects = Group.objects.filter(workspace=workspace)
    members = workspace.user.all()

    _ = get_default_property_set(TYPE_OBJECT_TASK, workspace, lang)

    property_sets = PropertySet.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_TASK
    ).order_by("created_at")
    set_id = request.GET.get("set_id")
    if view_filter and view_filter.view and view_filter.view.form:
        set_id = view_filter.view.form.id
    else:
        if not set_id:
            property_set_default = property_sets.filter(as_default=True).first()
            if property_set_default:
                set_id = property_set_default.id

    group_members = []
    groups = request.user.group_set.all()
    for group in groups:
        ids = group.user.all().values_list("id", flat=True)
        group_members.extend(ids)

    group_members = list(set(group_members))
    group_members = ",".join(str(id) for id in group_members)

    context = {
        "views": views,
        "view_id": id,
        "tasks": task_list,
        "target": TYPE_OBJECT_TASK,
        "project_id": project_id,
        "from": from_page,
        "task_q": task_q,
        "q": task_q,
        "highlight_task": highlight_task,
        "show_task": show_task,
        "projects": projects,
        "members": members,
        "page": page,
        "paginator_item_begin": paginator_item_begin,
        "paginator_item_end": paginator_item_end,
        "page_content": page_content,
        "paginator": paginator,
        "view": view,
        "view_filter": view_filter,
        "p_id": p_id,
        "object_type": TYPE_OBJECT_TASK,
        "menu_key": menu_key,
        "permission": permission,
        "module": module,
        "PROJECT_TASK_STATUS": PROJECT_TASK_STATUS,
        "set_id": set_id,
        "pagination_url": reverse(
            "load_object_page",
            host="app",
            kwargs={
                "module_slug": menu_key,
                "object_slug": OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK],
            },
        ),
        "group_members": group_members,
    }
    response = render(request, "data/projects/partial-tasks.html", context)

    response["HX-Trigger"] = json.dumps(
        {"updateCreateTaskButtonContext": {"view_id": str(id) if id else None}}
    )
    return response


@login_or_hubspot_required
def workflows_data(request, id=None):
    """
    Retrieves and renders a filtered, paginated list of workflows for the current workspace.

    Filters workflows based on query parameters such as project, search query, view, and user permissions. Applies view-specific filters and column customizations, and includes context data like available actions, projects, members, and group membership. Returns an HTML response rendering the partial workflows list template with all relevant context for frontend display.
    """
    workspace = get_workspace(request.user)
    project_id = request.GET.get("project")
    module_slug = request.GET.get("menu_key")
    length = int(request.GET.get("length", 0))
    from_page = request.GET.get("from")
    h_workflow = request.GET.get("h_workflow")
    workflow_q = request.GET.get("workflow_q")

    lang = request.LANGUAGE_CODE
    # for timezone etc.
    verification = Verification.objects.get(user=request.user)
    email_verified = verification.verified

    if not module_slug:
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        if module.slug:
            module_slug = module.slug

    selected_timezone = "UTC"
    if workspace.timezone:
        selected_timezone = workspace.timezone

    if request.user.is_staff:
        actions = Action.objects.all()
    else:
        actions = Action.objects.filter(public=True).order_by("order")
        actions = list(actions)

    view = None
    if not id:
        id = request.GET.get("view_id", None)
    else:
        try:
            # Validate that id is a valid UUID - check if it's already a UUID object
            if not isinstance(id, uuid.UUID):
                uuid.UUID(id)
            view = View.objects.get(id=id)
        except (ValueError, TypeError, View.DoesNotExist):
            pass

    is_gallery_creator = str(workspace.id) == WORKFLOW_GALLERY_CREATOR_WORKSPACE

    permission = get_permission(object_type=TYPE_OBJECT_WORKFLOW, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    logger.info(view)
    if project_id:
        # Project task views
        try:
            project = Group.objects.get(id=project_id)
        except Group.DoesNotExist:
            print(f"Project with ID {project_id} does not exist.")
            return HttpResponse(status=404)

        views = get_ordered_project_views(
            project, TYPE_OBJECT_WORKFLOW, user=request.user
        )
        conditions = Q(workspace=workspace)
        conditions &= get_permission_filter(permission, request.user)
        conditions &= Q(project=project)
        if not (request.user.is_staff and request.user.verification.admin):
            conditions &= Q(created_by_sanka__isnull=True)
        if workflow_q:
            if lang == "ja":
                conditions &= Q(title_ja__icontains=workflow_q)
            else:
                conditions &= Q(title__icontains=workflow_q)
        workflows = Workflow.objects.filter(conditions).order_by("-created_at")
    else:
        # General task views
        views = (
            View.objects.filter(workspace=workspace, target="workflow")
            .annotate(
                is_main=Case(
                    When(title=None, then=Value(0)),
                    # Other titles should come after
                    When(title__isnull=False, then=Value(1)),
                    output_field=IntegerField(),
                )
            )
            .order_by("is_main")
        )

        hide_private_views = []
        for view_ in views:
            if view_.is_private and (not request.user or view_.user != request.user):
                hide_private_views.append(view_)

        views = [view_ for view_ in views if view not in hide_private_views]

        conditions = Q()
        if request.user.is_staff and request.user.verification.admin:
            conditions &= Q(workspace=workspace) | Q(created_by_sanka=True)
        else:
            conditions &= Q(workspace=workspace)
            conditions &= ~Q(created_by_sanka=True)

        conditions &= get_permission_filter(permission, request.user)

        if workflow_q:
            if lang == "ja":
                conditions &= Q(title_ja__icontains=workflow_q)
            else:
                conditions &= Q(title__icontains=workflow_q)
        workflows = Workflow.objects.filter(conditions).order_by("-created_at")

    columns = ["name", "trigger"]
    view_filter = None
    if view:
        view_filter = view.viewfilter_set.filter(property__isnull=True).first()
        if view_filter and view_filter.column:
            try:
                columns += ast.literal_eval(view_filter.column)
            except (ValueError, SyntaxError):
                # If literal_eval fails, try to parse as comma-separated string
                if isinstance(view_filter.column, list):
                    columns += view_filter.column
                else:
                    columns += [col.strip() for col in view_filter.column.split(",")]
            try:
                if view_filter.filter_value:
                    conditions = build_view_filter(
                        Q(), view_filter, TYPE_OBJECT_WORKFLOW, request=request
                    )
                    print(".....................", conditions)
                    workflows = workflows.filter(conditions)
                filters = ViewFilter.objects.filter(view=view)
                workflows = apply_view_filter(filters, workflows)
            except:
                print(
                    f"ERROR on applying view filter to Workflows: {traceback.format_exc()}"
                )
        else:
            columns += DEFAULT_COLUMNS_WORKFLOW.copy()
        workflows = get_ordered_items(view, workflows)
    if length:
        workflows = workflows[:length]

    try:
        highlight_workflow = Workflow.objects.get(id=h_workflow)
    except:
        highlight_workflow = None

    projects = Group.objects.filter(workspace=workspace)
    members = workspace.user.all()

    group_members = []
    groups = request.user.group_set.all()
    for group in groups:
        ids = group.user.all().values_list("id", flat=True)
        group_members.extend(ids)

    group_members = list(set(group_members))
    group_members = ",".join(str(id) for id in group_members)

    context = {
        "views": views,
        "view": view,
        "view_id": id,
        "view_filter": view_filter,
        "columns": columns,
        "target": TYPE_OBJECT_WORKFLOW,
        "object_type": TYPE_OBJECT_WORKFLOW,
        "actions": actions,
        "workflows": workflows,
        "email_verified": email_verified,
        "selected_timezone": selected_timezone,
        "project_id": project_id,
        "from": from_page,
        "search_q": workflow_q,
        "highlight_workflow": highlight_workflow,
        "projects": projects,
        "members": members,
        "is_gallery_creator": is_gallery_creator,
        "permission": permission,
        "menu_key": module_slug,
        "group_members": group_members,
    }
    response = render(request, "data/projects/partial-workflows.html", context)
    response["HX-Trigger"] = json.dumps(
        {"updateCreateWorkflowButtonContext": {"view_id": str(id) if id else None}}
    )
    return response


@login_or_hubspot_required
def task_form(request):
    """
    Handles creation and editing of tasks, including assignment of owners, assignees, projects, custom fields, and line items.

    On POST, creates or updates a task with provided details such as title, status, owner, assignees, dates, description, line items, and associations with cases or orders. Also manages custom field values and file uploads. Redirects to the appropriate module or object page after saving.

    On GET, renders the task creation or editing form with context including available projects, users, views, property sets, permissions, and custom fields.
    """
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if request.method == "POST":
        title = request.POST.get("title")
        id = request.POST.get("task_id")
        prev_task_id = request.POST.get("prev_task_id")
        view_id = request.POST.get("view_id")
        status = request.POST.get("status", None)
        owner = request.POST.get("owner", None)

        # Debug logging for view_id
        logger.info(f"[TASK_FORM_DEBUG] POST data keys: {list(request.POST.keys())}")
        logger.info(f"[TASK_FORM_DEBUG] view_id from POST: {view_id}")
        logger.info(f"[TASK_FORM_DEBUG] task_id: {id}")

        user_list = request.POST.getlist("users")
        due_date = None
        from_page = request.POST.get("from")
        p_id = request.POST.get("p_id", None)
        project_target = None
        task_desc = request.POST.get("task_desc", "")

        start_date = request.POST.get("start_date", None)
        if start_date:
            start_date = parse_date(start_date)
        due_date = request.POST.get("due_date", None)
        if due_date:
            due_date = parse_date(due_date)

        # Line Item
        item_id_list = request.POST.getlist("item", [])
        item_price_list = request.POST.getlist("item_price", [])
        number_of_items_list = request.POST.getlist("number_of_items", [])
        currency = request.POST.get("price-info-currency", "USD")

        if p_id:
            project_target = Projects.objects.filter(id=p_id).first()

        view = None
        if view_id and view_id != "None":
            logger.info(f"[TASK_FORM_DEBUG] Processing view_id: {view_id}")
            try:
                # Validate that view_id is a valid UUID - check if it's already a UUID object
                if not isinstance(view_id, uuid.UUID):
                    uuid.UUID(view_id)
                view = View.objects.get(id=view_id)
                logger.info(f"[TASK_FORM_DEBUG] Found view: {view.id}")
            except (ValueError, TypeError, View.DoesNotExist) as e:
                logger.info(
                    f"[TASK_FORM_DEBUG] View lookup failed: {e}, setting view_id to None"
                )
                view_id = None
                pass
        else:
            logger.info(f"[TASK_FORM_DEBUG] No valid view_id provided: {view_id}")

        line_item_allowed = False
        if view:
            property_set = view.form if view.form else None
            if property_set:
                if "line_item" in property_set.children:
                    line_item_allowed = True

        new_task = None
        prev_task = None
        create_task_flag = False
        if id:
            task = Task.objects.filter(id=id).first()
            if not task:
                task = Task.objects.create(
                    id=id,
                    workspace=workspace,
                    project_target=project_target,
                    usage_status="active",
                )
                create_task_flag = True

            source = request.POST.get("source", None)
            module_slug = request.POST.get("module", "")
            if "update_cases" in request.POST:
                cases = request.POST.getlist("cases", [])
                deal = None
                if cases != [""] and cases and task:
                    try:
                        deals = task.deals.all()
                        for deal in deals:
                            deal.tasks.remove(task)
                            deal.save()
                    except:
                        print("[DEBUG] Failed to remove task from deal.")
                        pass

                    try:
                        deals = Deals.objects.filter(id__in=cases)
                        for deal in deals:
                            deal.tasks.add(task)
                            deal.save()
                    except:
                        print("[DEBUG] Failed to add task to deal.")
                        pass
            elif "update_orders" in request.POST:
                orders = request.POST.getlist("orders", [])
                if task:
                    task.orders.clear()
                if orders != [""]:
                    order_objs = ShopTurboOrders.objects.filter(id__in=orders)
                    task.orders.add(*order_objs)
            else:
                task.title = title
                if status:
                    task.status = status
                else:
                    task.status = None

                if project_target:
                    task.project_target = project_target

                if task_desc:
                    task.description = task_desc

                if start_date:
                    task.start_date = start_date

                if due_date:
                    task.due_date = due_date

                assign_object_owner(task, owner, request, TYPE_OBJECT_TASK)

                if user_list:
                    try:
                        users_list = []
                        groups_list = []
                        for item in user_list:
                            if item.startswith("user|"):
                                users_list.append(item.replace("user|", ""))
                            elif item.startswith("group|"):
                                groups_list.append(item.replace("group|", ""))

                        users = User.objects.filter(
                            workspace=workspace, username__in=users_list
                        )
                        projects = Group.objects.filter(
                            workspace=workspace, id__in=groups_list
                        )

                        for user_ in users:
                            if user_ not in task.assignee.all():
                                notif_assigned_assignee(
                                    task=task,
                                    assignee=user_,
                                    view_id=view_id,
                                    lang=lang,
                                )

                        for project_ in projects:
                            for user_ in project_.user.all():
                                if user_ not in task.assignee.all():
                                    notif_assigned_assignee(
                                        task=task,
                                        assignee=user_,
                                        view_id=view_id,
                                        lang=lang,
                                    )

                        task.assignee.clear()
                        task.assignee.add(*users)

                        task.project.clear()
                        task.project.add(*projects)
                    except Exception as e:
                        print(e)
                else:
                    task.project.clear()
                    task.assignee.clear()

                if line_item_allowed:
                    task_items = task.task_items.all()
                    task_items.delete()

                    order_view = 0
                    for idx, item_id in enumerate(item_id_list):
                        if not item_id or item_id == "":
                            continue

                        number_item = number_of_items_list[idx]
                        if number_of_items_list[idx] == "":
                            number_item = 0
                        elif isinstance(number_of_items_list[idx], str):
                            number_item = float(number_of_items_list[idx])

                        price = item_price_list[idx]
                        item_price = None
                        if item_price_list[idx] == "":
                            price = 0
                        elif is_valid_uuid(item_price_list[idx]):
                            shopturbo_item_prices = ShopTurboItemsPrice.objects.filter(
                                id=item_price_list[idx]
                            )
                            if shopturbo_item_prices.exists():
                                price = shopturbo_item_prices.first().price
                            else:
                                price = 0
                        else:
                            # Clean comma-formatted price strings (e.g., "3,000.0" -> "3000.0")
                            original_price = price
                            if isinstance(price, str):
                                price = price.replace(',', '')
                            try:
                                price = float(price)
                                if original_price != str(price):
                                    logger.info(f"[TASK_PRICE_FIX] Cleaned price format: '{original_price}' -> {price}")
                            except (ValueError, TypeError):
                                logger.warning(f"[TASK_PRICE_FIX] Failed to convert price '{original_price}' to float, using 0")
                                price = 0

                        try:
                            item = ShopTurboItems.objects.get(id=item_id)
                            custom_item_name = item.name
                        except:
                            custom_item_name = item_id
                            item = None

                        try:
                            total_price = price * number_item
                        except:
                            total_price = 0

                        TaskItem.objects.create(
                            task=task,
                            # Item Related
                            item=item,
                            item_price=item_price,
                            # Task Item Related
                            custom_item_name=custom_item_name,
                            item_price_task=price,
                            currency=currency,
                            number_item=number_item,
                            total_price=total_price,
                            # Ordering
                            order_view=order_view,
                        )
                        order_view += 1

                if create_task_flag:
                    task.save(
                        log_data={
                            "user": request.user,
                            "status": "create",
                            "workspace": workspace,
                        }
                    )
                else:
                    task.save(log_data={"user": request.user, "workspace": workspace})

                # Custom Fields
                # == Object Properties handler
                page_obj = get_page_object(TYPE_OBJECT_TASK, lang)
                save_custom_property(request, task, page_obj)

            # Made Association with Case when Task is created
            if source:
                if source == TYPE_OBJECT_CASE:
                    object_id = request.POST.get("object_id")
                    case = None

                    if object_id:
                        case = Deals.objects.filter(id=object_id).first()
                        if case:
                            case.tasks.add(task)
                            case.save()

                    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
                    module_slug = request.POST.get("module")

                    if not module_slug:
                        module = (
                            Module.objects.filter(
                                workspace=workspace,
                                object_values__contains=TYPE_OBJECT_CASE,
                            )
                            .order_by("order", "created_at")
                            .first()
                        )
                        module_slug = module.slug

                    if module_slug:
                        return redirect(
                            build_redirect_url(
                                reverse(
                                    "load_object_page",
                                    host="app",
                                    kwargs={
                                        "module_slug": module_slug,
                                        "object_slug": module_object_slug,
                                    },
                                ),
                                view_id=view_id,
                                p_id=p_id,
                            )
                        )
                    else:
                        return redirect(reverse("main", host="app"))
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
            module_slug = request.POST.get("module")
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug

            if module_slug:
                redirect_url = build_redirect_url(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    ),
                    view_id=view_id,
                    p_id=p_id,
                )
                logger.info(f"[TASK_FORM_DEBUG] Built redirect URL: {redirect_url}")
                return redirect(redirect_url)

            return redirect(reverse("main", host="app"))

        else:
            if view:
                task = generate_task_by_view(view, request.user)
            else:
                task = Task.objects.create(
                    workspace=workspace,
                    project_target=project_target,
                    usage_status="active",
                )
                task.save(
                    log_data={
                        "user": request.user,
                        "status": "create",
                        "workspace": get_workspace(request.user),
                    }
                )
            new_task = task

            assign_object_owner(task, owner, request, TYPE_OBJECT_TASK)

            if task and project_target:
                task.project_target = project_target
                task.save()

            if start_date:
                task.start_date = start_date

            if due_date:
                task.due_date = due_date

            try:
                if prev_task_id:
                    prev_task = Task.objects.get(id=prev_task_id)
            except:
                pass

            for assignee_ in task.assignee.all():
                notif_assigned_assignee(
                    task=task, assignee=assignee_, view_id=view_id, lang=lang
                )

            # Set up the order for new created task
            tasks = Task.objects.filter(workspace=workspace)
            if view:
                filters = ViewFilter.objects.filter(view=view)
                tasks = apply_view_filter(filters, tasks)
                tasks = get_ordered_items(
                    view, tasks, new_item=new_task, new_item_after=prev_task
                )
            hx_swap = "innerHTML"
            exclude_dummy_element = False
            if len(tasks) > 1:
                hx_swap = "afterbegin"
                exclude_dummy_element = True

            module_slug = request.GET.get("module", "")

            context = {
                "target": TYPE_OBJECT_TASK,
                "object_type": TYPE_OBJECT_TASK,
                "from": from_page,
                "view_id": view_id,
                "tasks": [task],
                "show_task": task,
                "highlight_task": task,
                "exclude_dummy_element": exclude_dummy_element,
                "module": module,
            }
            if context["view_id"] and context["view_id"] != "None":
                try:
                    # Validate that view_id is a valid UUID - check if it's already a UUID object
                    if not isinstance(context["view_id"], uuid.UUID):
                        uuid.UUID(context["view_id"])
                    context["view_filter"] = ViewFilter.objects.filter(
                        view__id=context["view_id"], property__isnull=True
                    ).first()
                except (ValueError, TypeError):
                    # If view_id is not a valid UUID, skip the filter
                    context["view_filter"] = None

            response = render(request, "data/projects/partial-task-rows.html", context)
            hx_trigger_data = {
                "reloadNotifNumber": "",
                "updateCreateTaskButtonContext": {
                    "view_id": view_id,
                    "hx_swap": hx_swap,
                },
            }
            response["HX-Trigger"] = json.dumps(hx_trigger_data)
            return response
    else:
        task_id = request.GET.get("task_id")
        view_id = request.GET.get("view_id")
        type = request.GET.get("type")
        from_page = request.GET.get("from", None)
        p_id = request.GET.get("p_id")
        project_target = None
        module = request.GET.get("module", None)
        source = request.GET.get("source", None)
        object_id = request.GET.get("object_id", None)

        if p_id:
            project_target = Projects.objects.filter(id=p_id).first()

        projects = Group.objects.filter(workspace=workspace)

        workspace_users = workspace.user.all()

        permission = get_permission(object_type=TYPE_OBJECT_TASK, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if project_target:
            task = (
                Task.objects.get(id=task_id, project_target=project_target)
                if task_id and project_target
                else None
            )
        else:
            task = Task.objects.get(id=task_id) if task_id else None

        if not task:
            task = Task(
                id=uuid.uuid4(), project_target=project_target, workspace=workspace
            )

        TaskNameCustomFieldMap = {}
        cf_ = TaskCustomFieldName.objects.filter(workspace=workspace).order_by("order")
        for ctf in cf_:
            TaskNameCustomFieldMap[str(ctf.id)] = ctf

        task_custom_field_values = []
        if task:
            # Validate Task's Workspace
            if not task.workspace:
                task.workspace = workspace

            task_custom_field_values = TaskCustomFieldValue.objects.filter(
                task=task
            ).order_by("field_name__order")

        view_filter = None
        if view_id and view_id != "None":
            try:
                # Validate that view_id is a valid UUID - check if it's already a UUID object
                if not isinstance(view_id, uuid.UUID):
                    uuid.UUID(view_id)
                view_filter = ViewFilter.objects.filter(
                    view__id=view_id, property__isnull=True
                ).first()
            except (ValueError, TypeError):
                # If view_id is not a valid UUID, skip the filter
                view_filter = None

        # Simplify Run Case
        is_hide_associated_data = type in [
            "create",
            "create-association",
            "create-sub-task",
        ]
        if type in ["create", "create-association", "create-sub-task"]:
            task.usage_status = "active"

        # Module Check
        module_slug = None
        if not module:
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
                )
                .order_by("order", "created_at")
                .first()
            )
            if module:
                module_slug = module.slug
                module = module_slug
        else:
            module_slug = module

        # Setup Property Sets
        _ = get_default_property_set(TYPE_OBJECT_TASK, workspace, lang)

        # Set Properties
        set_id = request.GET.get("set_id", None)
        property_set = None
        if set_id:
            property_set = PropertySet.objects.filter(id=set_id).first()
        elif view_id and view_id != "None":
            try:
                # Validate that view_id is a valid UUID - check if it's already a UUID object
                if not isinstance(view_id, uuid.UUID):
                    uuid.UUID(view_id)
                view = View.objects.get(id=view_id)
                property_set = view.form if view.form else None
            except (ValueError, TypeError, View.DoesNotExist):
                # If view_id is not a valid UUID or view doesn't exist, skip
                property_set = None

        if not property_set:
            condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_TASK)
            condition_filter &= Q(as_default=True) | Q(name__isnull=True)
            property_set = PropertySet.objects.filter(condition_filter).first()

        properties = {"list_all": property_set.children if property_set else []}
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_TASK
        ).order_by("created_at")
        if task and task.owner and task.owner.user:
            permission += f"|{task.owner.user.id}#{request.user.id}"
        context = {
            # Workspace Related
            "permission": permission,
            "workspace_users": workspace_users,
            "workspace": workspace,
            # Projects
            "projects": projects,
            # Views
            "view_id": view_id,
            "view_filter": view_filter,
            # Property Sets
            "properties": properties,
            # Task Object
            "task": task,
            "TaskNameCustomFieldMap": TaskNameCustomFieldMap,
            "task_custom_field_values": task_custom_field_values,
            # Current Module
            "module": module,
            "module_slug": module_slug,
            # Association And SubTask
            "source": source,
            "object_id": object_id,
            "property_sets": property_sets,
            "set_id": set_id,
            # Constant
            "p_id": p_id,
            "type": type,
            "from": from_page,
            "object_type": TYPE_OBJECT_TASK,
            "PROJECT_TASK_STATUS": PROJECT_TASK_STATUS,
            "hide_associated_data": is_hide_associated_data,
        }

        return render(request, "data/projects/task-form.html", context)


@require_POST
@login_or_hubspot_required
def tasks_bulk_edit(request):
    logger.info("Bulk edit tasks request received.")
    logger.info("Request data: %s", request.POST)
    task_ids = request.POST.getlist("selected_task")
    properties = request.POST.getlist("properties")
    view_id = request.POST.get("view_id")
    status = request.POST.get("status")
    project_id = request.POST.get("project")
    assignee_list = request.POST.getlist("assignee")
    due_date = request.POST.get("due_date")

    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if "updates" in request.POST:
        updates = request.POST.get("updates")
        if updates:
            try:
                updates = json.loads(updates)
                for update in updates:
                    task_id = update.get("id", None)
                    start_date = update.get("start_date", None)
                    due_date = update.get("due_date", None)
                    production_line_id = update.get("production_line_id", None)
                    production_line_name_id = update.get("production_line_name_id", None)
                    for task in Task.objects.filter(id=task_id, workspace=workspace):
                        if start_date:
                            task.start_date = parse_date(start_date)
                        if due_date:
                            task.due_date = parse_date(due_date)
                        if production_line_id and production_line_name_id:
                            custom_model_name = TaskCustomFieldName.objects.filter(
                                id=production_line_name_id,workspace=workspace).first()
                            custom_value = TaskCustomFieldValue.objects.filter(
                                field_name=custom_model_name, task=task).first()
                            if custom_value:
                                custom_value.value = production_line_id
                                custom_value.save()
                        task.save(
                            log_data={"user": request.user, "workspace": workspace}
                        )
                return HttpResponse(status=200)
            except json.JSONDecodeError:
                logger.error("Failed to decode JSON from updates: %s", updates)
                return HttpResponse(status=400)

    if not task_ids:
        return HttpResponse(status=400)

    tasks = Task.objects.filter(id__in=task_ids, workspace=workspace)
    if not tasks:
        return HttpResponse(status=404)

    if "status" in properties and status in ["todo", "doing", "done"]:
        tasks.update(status=status)

    if "project" in properties and project_id:
        try:
            project = Group.objects.get(id=project_id, workspace=workspace)
            tasks.update(project=project)
        except Group.DoesNotExist:
            pass

    if "assignee" in properties:
        for task in tasks:
            task.assignee.clear()
        if assignee_list:
            assignees = User.objects.filter(
                username__in=assignee_list, workspace=workspace
            )
            if assignees:
                for task in tasks:
                    task.assignee.set(assignees)

    if "due_date" in properties and due_date:
        if lang == "ja":
            endpoint = "https://translate.googleapis.com/translate_a/single?client=gtx&sl=ja&tl=en&dt=t&ie=UTF-8&oe=UTF-8&otf=1&ssel=0&tsel=0&kc=7&dt=at&dt=bd&dt=ex&dt=ld&dt=md&dt=qca&dt=rw&dt=rm&dt=ss&q="
            parsed_date_str = requests.get(endpoint + due_date).json()[0][0][0]
            input_format = "%B %d, %Y"  # The format of the input date
            # Step 1: Parse the input date
            parsed_date = datetime.strptime(parsed_date_str, input_format)
            # Step 2: Format the datetime object
            desired_format = "%Y-%m-%d"
            due_date = parsed_date.strftime(desired_format)
        tasks.update(due_date=due_date)

    context = {
        "view_id": view_id,
        "tasks": tasks,
        "swap_existing_task": True,
    }
    if context["view_id"] and context["view_id"] != "None":
        try:
            # Validate that view_id is a valid UUID - check if it's already a UUID object
            if not isinstance(context["view_id"], uuid.UUID):
                uuid.UUID(context["view_id"])
            context["view_filter"] = ViewFilter.objects.filter(
                view__id=context["view_id"], property__isnull=True
            ).first()
        except (ValueError, TypeError):
            # If view_id is not a valid UUID, skip the filter
            context["view_filter"] = None

    response = render(request, "data/projects/partial-task-rows.html", context)

    response["HX-Trigger"] = json.dumps(
        {
            "reloadNotifNumber": "",
            "closeTaskDrawer": "",
            "closeTaskEditBulkModal": "",
            "hideTaskBulkActionBtn": "",
        }
    )
    return response


@require_POST
@login_or_hubspot_required
def task_description(request, id):
    workspace = get_workspace(request.user)
    desc = request.POST.get("description")

    try:
        task = Task.objects.get(id=id)
    except:
        return HttpResponse(status=404)

    task.description = desc
    task.save(log_data={"user": request.user, "workspace": workspace})
    # Put sync modify here
    return HttpResponse(status=200)


@login_or_hubspot_required
def project_form(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if request.method == "POST":
        title = request.POST.get("title")
        description = request.POST.get("description")
        id = request.POST.get("project_id")
        users = request.POST.getlist("users_group")
        child_groups = request.POST.getlist("child_groups")

        if id:
            project = Group.objects.get(id=id)
            project.title = title
            project.description = description
            project.save()
        else:
            project = Group.objects.create(
                title=title,
                description=description,
                workspace=workspace,
            )
            users = User.objects.filter(username__in=users)
            project.user.set(users)
            groups = Group.objects.filter(id__in=child_groups)
            project.child_groups.set(groups)
            project.save()

            View.objects.create(
                title="新しいビュー" if lang == "ja" else "New View", project=project
            )
            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="プロジェクトが作成されました。",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Project created successfully.",
                    type="success",
                )
        return redirect(reverse("workspace_groups", host="app"))

    else:
        id = request.GET.get("project_id")
        if id:
            project = Group.objects.get(id=id)
            context = {
                "project": project,
            }
        else:
            context = {
                "users": User.objects.filter(workspace=workspace),
                "groups": Group.objects.filter(workspace=workspace),
            }

        return render(request, "data/projects/project-form.html", context)


@login_or_hubspot_required
def get_workflow_history(request):
    id = request.GET.get("history_id")
    if not id:
        return HttpResponse(status=400)
    view_id = request.GET.get("view_id")
    from_page = request.GET.get("from")

    history = WorkflowHistory.objects.get(id=id)
    history_reference = history.workflowactiontracker_set.filter(
        status__in=["waiting_trigger", "stop_trigger"]
    ).first()

    action_reference = {}
    if history_reference:
        action_reference = {
            "history_action_tracker": history_reference,
            "action_trackers": [],
        }
        for node in get_nodes(history):
            action_reference["action_trackers"] += (
                history_reference.actiontracker_set.filter(node=node).order_by(
                    "created_at"
                )
            )
        history_action_tracker = (
            history.workflowactiontracker_set.filter()
            .exclude(status__in=["waiting_trigger", "stop_trigger"])
            .order_by("-created_at")
        )
    else:
        history_action_tracker = history.workflowactiontracker_set.all().order_by(
            "-created_at"
        )
    trackers = []
    total_cost = 0
    run_by_trigger = True
    if history_action_tracker or history_reference:
        for history_tracker in history_action_tracker:
            tracker_data = {"history_action_tracker": history, "action_trackers": []}
            for node in get_nodes(history):
                tracker_data["action_trackers"] += (
                    history_tracker.actiontracker_set.filter(node=node).order_by(
                        "created_at"
                    )
                )

            tracker_data["rollback_options"] = get_rollback_options(
                history, tracker_data["action_trackers"]
            )

            tracker_data["nodes"] = []
            for at in tracker_data["action_trackers"]:
                if at.node in tracker_data["nodes"]:
                    continue
                tracker_data["nodes"].append(at.node)

            tracker_data["actions"] = []
            for node in tracker_data["nodes"]:
                tracker_data["actions"].append(node.action)

            tracker_data["total_cost"] = 0

            trackers.append(tracker_data)
            total_cost += tracker_data["total_cost"]

        context = {
            "workflow_action": history,  # can be empty
            "action_reference": action_reference,
            "trackers": trackers,
            "total_cost": total_cost,
            "run_by_trigger": run_by_trigger,
            "view_id": view_id,
            "from": from_page,
        }
        return render(request, "data/ai/partial-workflow-res.html", context)

    assignees = ",".join([str(assignee.id) for assignee in history.assignee.all()])
    if check_staff(request.user, get_workspace(request.user)):
        actions = Action.objects.all()
        actions = list(actions)
    else:
        actions = Action.objects.filter(public=True).order_by("order")
        actions = list(actions)

    context = {
        "history": history,
        "actions": actions,
        "view_id": view_id,
        "assignees": assignees,
        "from": from_page,
    }
    return render(request, "data/projects/task-action-form.html", context)


@login_or_hubspot_required
def project_form_delete(request, id):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if request.method == "POST":
        project = Group.objects.get(id=id, workspace=workspace)
        project.delete()
        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="プロジェクトが削除されました。",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Project deleted successfully.",
                type="success",
            )
        return redirect(reverse("projects", host="app"))


@login_or_hubspot_required
def delete_view(request, id):
    lang = request.LANGUAGE_CODE
    project_id = request.POST.get("project")
    from_page = request.POST.get("from")
    workspace = get_workspace(request.user)
    if request.method != "POST":
        return HttpResponse(status=405)

    target = request.POST.get("target")
    if target not in ["goal", "task", "workflow", "project"]:
        return HttpResponse(status=400)

    try:
        view = View.objects.get(workspace=workspace, id=id, target=target)
    except View.DoesNotExist:
        return HttpResponse(status=404)

    project = None
    if target == "project":
        project = view.project
    view.delete()

    if lang == "ja":
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="ビューは正常に削除されました。",
            type="success",
        )
    else:
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="View deleted successfully.",
            type="success",
        )

    # Define view that will be displayed, after current view is deleted
    if project:
        views = get_ordered_project_views(project, target, user=request.user)
    else:
        views = get_ordered_views(workspace, target, user=request.user)
    if not views:
        # create new view if empty
        current_view = View.objects.create(
            workspace=workspace,
            title="新しいビュー" if lang == "ja" else "New View",
            target=target,
            project=project,
        )
        id = current_view.id
    else:
        id = views[0].id

    request.GET = request.GET.copy()
    request.GET["project"] = project_id
    request.GET["from"] = from_page
    if target == "task":
        response = tasks_data(request, id)
        if from_page == "project":
            response["HX-Replace-Url"] = reverse("tasks", kwargs={"id": id}, host="app")
        response["HX-Trigger"] = json.dumps(
            {
                "reloadNotifNumber": "",
                "closeViewDrawer": "",
                "updateCreateTaskButtonContext": {"view_id": str(id)},
            }
        )
    elif target == "workflow":
        response = workflows_data(request, id)
        if from_page == "project":
            response["HX-Replace-Url"] = reverse(
                "workflows", kwargs={"id": id}, host="app"
            )
        response["HX-Trigger"] = json.dumps(
            {
                "reloadNotifNumber": "",
                "closeViewDrawer": "",
                "updateCreateWorkflowButtonContext": {"view_id": str(id)},
            }
        )
    return response


@login_or_hubspot_required
def task_form_usage_status(request, id):
    view_id = request.POST.get("view_id")
    p_id = request.POST.get("p_id")
    fn = request.POST.get("fn")

    task = Task.objects.get(id=id)

    if fn == "archive":
        for assignee_ in task.assignee.all():
            remove_assigned_notif(task, assignee_)
        task.usage_status = "archived"
    else:
        task.usage_status = "active"
    task.save()

    query_string = f"?p_id={p_id}" if p_id else ""
    if query_string:
        query_string += f"&view_id={view_id}" if view_id else ""
    else:
        query_string = f"?view_id={view_id}" if view_id else ""

    workspace = get_workspace(request.user)
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug

        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + query_string
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def bulk_task_usage_status(request):
    view_id = request.POST.get("view_id")
    task_ids = request.POST.getlist("selected_task")
    p_id = request.POST.get("p_id")
    fn = request.POST.get("fn")
    workspace = get_workspace(request.user)

    if not task_ids:
        return HttpResponse(status=400)

    tasks = Task.objects.filter(id__in=task_ids, workspace=workspace)
    tasks_dict = tasks.values("id")
    for td in tasks_dict:
        td["not_here"] = True

    for task in tasks:
        if fn == "archive":
            for assignee_ in task.assignee.all():
                remove_assigned_notif(task, assignee_)
            task.usage_status = "archived"
        else:
            task.usage_status = "active"
        task.save()

    query_string = f"?p_id={p_id}" if p_id else ""
    if query_string:
        query_string += f"&view_id={view_id}" if view_id else ""
    else:
        query_string = f"?view_id={view_id}" if view_id else ""

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
        )
        .order_by("order", "created_at")
        .first()
    )

    response = HttpResponse()
    if module:
        module_slug = module.slug
        response["HX-Redirect"] = (
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + query_string
        )
    else:
        response["HX-Redirect"] = reverse("main", host="app")

    return response


def duplicate_task_obj(task_obj: Task):
    """
    Creates a duplicate of the given task, including its fields, owner, assignees, projects, and custom field values.

    Args:
        task_obj: The Task instance to duplicate.

    Returns:
        The newly created Task instance that is a copy of the original.
    """
    new_task = Task.objects.create(
        workspace=task_obj.workspace,
        workflow=task_obj.workflow,
        project_target=task_obj.project_target,
        title=task_obj.title,
        description=task_obj.description,
        status=task_obj.status,
        usage_status=task_obj.usage_status,
        due_date=task_obj.due_date,
        owner=task_obj.owner,
    )
    new_task.assignee.set(task_obj.assignee.all())
    new_task.project.set(task_obj.project.all())
    new_task.save()

    task_cf = TaskCustomFieldName.objects.filter(workspace=task_obj.workspace)
    for cf in task_cf:
        task_cf_value = TaskCustomFieldValue.objects.filter(
            task=task_obj, field_name=cf
        ).first()
        if task_cf_value:
            TaskCustomFieldValue.objects.create(
                task=new_task,
                field_name=cf,
                value=task_cf_value.value,
                value_number=task_cf_value.value_number,
                value_time=task_cf_value.value_time,
            )

    return new_task


@login_or_hubspot_required
def bulk_task_duplicate(request):
    view_id = request.POST.get("view_id")
    task_ids = request.POST.getlist("selected_task")
    p_id = request.POST.get("p_id")
    workspace = get_workspace(request.user)

    if not task_ids:
        return HttpResponse(status=400)

    tasks = Task.objects.filter(id__in=task_ids, workspace=workspace)
    tasks_dict = tasks.values("id")
    for td in tasks_dict:
        td["not_here"] = True

    for task in tasks:
        duplicate_task_obj(task)

    query_string = f"?p_id={p_id}" if p_id else ""
    if query_string:
        query_string += f"&view_id={view_id}" if view_id else ""
    else:
        query_string = f"?view_id={view_id}" if view_id else ""

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
        )
        .order_by("order", "created_at")
        .first()
    )

    response = HttpResponse()
    if module:
        module_slug = module.slug
        response["HX-Redirect"] = (
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + query_string
        )
    else:
        response["HX-Redirect"] = reverse("main", host="app")

    return response


@login_or_hubspot_required
def workflow_history_delete(request, id):
    # histories = request.POST.getlist('selected_history')
    workspace = get_workspace(request.user)

    # if not histories:
    #     return HttpResponse(status=400)

    workflow = Workflow.objects.get(id=id)
    histories = WorkflowHistory.objects.filter(workspace=workspace, workflow=workflow)
    histories_dict = histories.values("id")
    for hd in histories_dict:
        hd["not_here"] = True

    for history in histories:
        for assignee_ in history.assignee.all():
            remove_assigned_notif(history, assignee_)

    if histories:
        histories.delete()

    response = HttpResponse(status=200)
    response["HX-Trigger"] = json.dumps(
        {"reloadNotifNumber": "", "showValidationMessage": ""}
    )
    return response


@login_or_hubspot_required
def action_history(request, category=None):
    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse("start", host="app"))

    projects = workspace.get_ordered_projects()

    lang = request.LANGUAGE_CODE
    # for timezone etc.
    verification = Verification.objects.get(user=request.user)
    email_verified = verification.verified
    selected_timezone = "UTC"
    if workspace.timezone:
        selected_timezone = workspace.timezone

    action_history = []
    if category == "completed":
        action_history = (
            TaskActionTracker.objects.filter(
                workspace=workspace, status__in=["success", "failed"]
            )
            .exclude(completed_at=None)
            .order_by("-created_at")
        )
        Log.objects.create(
            workspace=workspace,
            user=request.user,
            page_name="workflows",
            sub_page_name="history - completed",
        )
    if category == "all" or not category:
        action_history = TaskActionTracker.objects.filter(
            workspace=workspace, status__isnull=False
        ).order_by("-created_at")
        Log.objects.create(
            workspace=workspace,
            user=request.user,
            page_name="workflows",
            sub_page_name="history - all",
        )
    elif category == "ongoing":
        action_history = TaskActionTracker.objects.filter(
            workspace=workspace, status__in=["running", "scheduled"]
        ).order_by("-created_at")
        Log.objects.create(
            workspace=workspace,
            user=request.user,
            page_name="workflows",
            sub_page_name="history - ongoing",
        )

    action_history_set = []
    for ah in action_history:
        d = {}
        d["task_action_tracker"] = ah
        d["total"] = 0
        action_history_set.append(d)

    if lang == "ja":
        page_title = "ワークフロー履歴"
    else:
        page_title = "Workflow History"

    workflows = Workflow.objects.filter(
        created_by_sanka=True, status="published"
    ).exclude(status="draft")
    automation_tags = (
        AutomationTag.objects.filter()
        .exclude(slug="recommended")
        .exclude(slug="featured")
        .order_by("order")
    )
    integrations = Integration.objects.filter()

    context = {
        "page_title": page_title,
        "email_verified": email_verified,
        "category": category,
        "verification": verification,
        "selected_timezone": selected_timezone,
        "action_history_set": action_history_set,
        "projects": projects,
        "workflows": workflows,
        "automation_tags": automation_tags,
        "integrations": integrations,
    }
    return render(request, "data/projects/project-template.html", context)


@login_or_hubspot_required
def gallery(request, category=None):
    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse("start", host="app"))
    menu_key = "home"

    page_group_type = "templates"

    lang = request.LANGUAGE_CODE
    # for timezone etc.
    verification = Verification.objects.get(user=request.user)

    if lang == "ja":
        page_title = "ギャラリー"
    else:
        page_title = "Gallery"

    workflows = Workflow.objects.filter(
        created_by_sanka=True, status="published"
    ).exclude(status="draft")
    automation_tags = (
        AutomationTag.objects.filter()
        .exclude(slug="recommended")
        .exclude(slug="featured")
        .order_by("order")
    )
    integrations = Integration.objects.filter()
    type_ = "workflows"

    context = {
        # 'menu_key':menu_key,
        "page_title": page_title,
        "page_group_type": page_group_type,
        "category": category,
        "verification": verification,
        "workflows": workflows,
        "automation_tags": automation_tags,
        "integrations": integrations,
        "type": type_,
        "menu_key": menu_key,
    }
    return render(request, "data/projects/project-template.html", context)


@login_or_hubspot_required
def query_workflows_view(request):
    context = query_workflows(request, "app")
    return render(request, "data/static/query-workflows.html", context)


@login_or_hubspot_required
def view_form(request):
    workspace = get_workspace(request.user)
    if request.method == "GET":
        view_id = request.GET.get("view_id")
        target = request.GET.get("target")
        active_view = request.GET.get("active_view")
        project_id = request.GET.get("project")
        from_page = request.GET.get("from")
        print(request.GET)

        view = None
        status_filter = None
        assignee_filter = None
        due_date_filter = None
        project_filter = None
        users = workspace.user.all()
        projects = Group.objects.filter(workspace=workspace)
        print(projects)
        if view_id and view_id != "None":
            try:
                # Validate that view_id is a valid UUID - check if it's already a UUID object
                if not isinstance(view_id, uuid.UUID):
                    uuid.UUID(view_id)
                view = View.objects.get(id=view_id)
            except (ValueError, TypeError):
                return HttpResponse(status=400)
            except View.DoesNotExist:
                return HttpResponse(status=404)

        view_filter = None
        if view:
            status_filter = ViewFilter.objects.filter(
                view=view, property="status"
            ).first()
            assignee_filter = ViewFilter.objects.filter(
                view=view, property="assignee"
            ).first()
            due_date_filter = ViewFilter.objects.filter(
                view=view, property="due_date"
            ).first()
            project_filter = ViewFilter.objects.filter(
                view=view, property="project"
            ).first()
            view_filter = view.viewfilter_set.filter(property__isnull=True).first()

        context = {
            "view": view,
            "view_filter": view_filter,
            "active_view": active_view,
            "status_filter": status_filter,
            "assignee_filter": assignee_filter,
            "due_date_filter": due_date_filter,
            "project_filter": project_filter,
            "users": users,
            "target": target,
            "project_id": project_id,
            "projects": projects,
            "from": from_page,
            "default_columns": DEFAULT_COLUMNS_TASK
            if target == "task"
            else DEFAULT_COLUMNS_WORKFLOW,
        }
        return render(request, "data/projects/view-form.html", context)

    elif request.method == "POST":
        print(request.POST)
        project_id = request.POST.get("project_id")
        view_id = request.POST.get("view_id")
        active_view = request.POST.get("active_view")
        title = request.POST.get("title")
        target = request.POST.get("target")
        from_page = request.POST.get("from")

        workspace = get_workspace(request.user)
        column_str = request.POST.get("column")
        columns = column_str.split(",") if column_str else []

        # Update view
        if view_id and view_id != "None":
            try:
                # Validate that view_id is a valid UUID - check if it's already a UUID object
                if not isinstance(view_id, uuid.UUID):
                    uuid.UUID(view_id)
                view = View.objects.get(id=view_id)
            except (ValueError, TypeError):
                return HttpResponse(status=400)
            except View.DoesNotExist:
                return HttpResponse(status=400)

            view.title = title
            view.save()

            view_filter = view.viewfilter_set.filter(property__isnull=True).first()
            if columns:
                view_filter.column = columns
            view_filter.save()

        # create view
        else:
            if target not in ["goal", "task", "workflow"]:
                return HttpResponse(status=400)

            print("created")
            view = View.objects.create(workspace=workspace, title=title, target=target)
            view_filter, _ = ViewFilter.objects.get_or_create(
                view=view, view_type="list", column=columns
            )
            if project_id:
                try:
                    project = Group.objects.get(id=project_id)
                except Group.DoesNotExist:
                    return HttpResponse(status=400)

                view.project = project
                view.save()
            view_id = active_view

        request.GET = request.GET.copy()
        request.GET["from"] = from_page
        if project_id:
            request.GET["project"] = project_id
        if target == "task":
            response = tasks_data(request, view_id)
            response["HX-Trigger"] = json.dumps(
                {
                    "closeViewDrawer": "",
                    "updateCreateTaskButtonContext": {"view_id": view_id},
                }
            )
        elif target == "workflow":
            response = workflows_data(request, view_id)
            response["HX-Trigger"] = json.dumps(
                {
                    "closeViewDrawer": "",
                    "updateCreateWorkflowButtonContext": {"view_id": view_id},
                }
            )

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
            response["HX-Redirect"] = (
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?view_id={view.id}"
            )
        else:
            response["HX-Redirect"] = reverse("main", host="app")

        return response


@login_or_hubspot_required
@csrf_exempt
def task_upload_img(request):
    malformed_input_resp = JsonResponse(
        data={"error": {"message": "User input malformed."}}
    )

    if not request.method == "POST":
        return malformed_input_resp
    try:
        print("Get Files")
        file = request.FILES.get("file")
        # Rename uploaded file
        file.name = str(uuid.uuid4()) + "." + file.name.split(".")[-1]
        imgfile = TaskFile.objects.create(file=file)
        return JsonResponse(
            data={"resourceType": "Images", "uploaded": 1, "url": imgfile.file.url}
        )
    except Exception as e:
        print(e)

    # Validate CSRF Token
    csrf_token_cookies = request.COOKIES.get("ckCsrfToken")
    csrf_token_body = request.POST.get("ckCsrfToken")
    if csrf_token_body != csrf_token_cookies and not (
        csrf_token_cookies and csrf_token_body
    ):
        print("csrftoken")
        return malformed_input_resp

    # Validate input
    task_id = request.GET.get("task_id")
    upload_file = request.FILES.get("upload")
    if not (upload_file):
        return malformed_input_resp

    # Rename uploaded file
    upload_file.name = str(uuid.uuid4()) + "." + upload_file.name.split(".")[-1]
    task_file = TaskFile.objects.create(file=upload_file)

    if task_id:
        # Get task object
        try:
            task = Task.objects.get(id=task_id)
            task_file.task = task
            task_file.save()
        except Task.DoesNotExist:
            pass
    print(task_file.file.url)
    return JsonResponse(
        data={
            "resourceType": "Images",
            "fileName": upload_file.name,
            "uploaded": 1,
            "url": task_file.file.url,
        }
    )


@login_or_hubspot_required
def project_order(request):
    project_ids = request.POST.getlist("project")
    workflow_ids = request.POST.getlist("workflow")
    goal_ids = request.POST.getlist("goal")
    task_ids = request.POST.getlist("task")
    views_ids = request.POST.getlist("views")
    project_id = request.POST.get("project_id")
    view_id = request.POST.get("view_id")
    target = request.POST.get("target")
    custom_object_id = request.POST.get("custom_object_id")
    p_id = request.POST.get("p_id", None)

    if not (project_ids or goal_ids or task_ids or views_ids or workflow_ids):
        print(
            "[project_order] Require one of project_ids, goal_ids, task_ids, views_ids, workflow_ids."
        )
        return HttpResponse(status=400)

    custom_object = None
    if custom_object_id:
        try:
            custom_object = CustomObject.objects.get(id=custom_object_id)
        except CustomObject.DoesNotExist:
            print("[project_order] Custom object not found.")
            pass

    if target and target not in [
        "goal",
        "task",
        "workflow",
        "project",
        "contacts",
        "companies",
        TYPE_OBJECT_ITEM,
        TYPE_OBJECT_ORDER,
        "commerce_subscription",
        "commerce_inventory",
        "customer_case",
        "invoices",
        "estimates",
        "receipts",
        "delivery_slips",
        "purchaseorder",
        "purchasesupplier",
        "purchaseitem",
        TYPE_OBJECT_BILL,
        TYPE_OBJECT_EXPENSE,
        "panels",
        "dashboards",
        TYPE_OBJECT_CUSTOM_OBJECT,
    ]:
        print(
            "[project_order] Target value must be one of 'goal', 'task', 'workflow', 'project'."
        )
        return HttpResponse(status=400)

    if target not in ["contacts", "companies"]:
        if view_id and view_id != "None":
            try:
                # Validate that view_id is a valid UUID - check if it's already a UUID object
                if not isinstance(view_id, uuid.UUID):
                    uuid.UUID(view_id)
                view = View.objects.get(id=view_id)
            except (ValueError, TypeError):
                return HttpResponse(status=400)
            except View.DoesNotExist:
                return HttpResponse(status=404)
        else:
            return HttpResponse(status=400)

    project = None
    if project_id:
        try:
            project = Group.objects.get(id=project_id)
        except Group.DoesNotExist:
            return HttpResponse(status=404)

    workspace = get_workspace(request.user)
    if project_ids:
        res = [str(project_id) for project_id in project_ids]
        workspace.group_order = res
        workspace.save()
        projects = get_ordered_projects(workspace)

        context = {
            "projects": projects,
        }
        return render(request, "data/projects/partial-projects-menu.html", context)

    elif task_ids:
        res = [str(task_id) for task_id in task_ids]
        res = list(dict.fromkeys(res))

        view.item_order = res
        view.save()

        for id, task_id in enumerate(res):
            task = Task.objects.filter(workspace=workspace, id=task_id).first()
            if task:
                task.order = id + 1
                task.save(log_data={"user": request.user, "workspace": workspace})

        tasks = Task.objects.filter(workspace=workspace, id__in=res).order_by("order")
        filters = ViewFilter.objects.filter(view=view)
        tasks = apply_view_filter(filters, tasks)
        tasks = get_ordered_items(view, tasks)
        context = {
            "view_id": str(view.id),
            "project": project,
            "tasks": tasks,
            "p_id": p_id,
            "LANGUAGE_CODE": request.LANGUAGE_CODE,
        }

        return HttpResponse(status=200)

    elif workflow_ids:
        res = [str(workflow_id) for workflow_id in workflow_ids]
        view.item_order = res
        view.save()

        conditions = Q(id__in=res)
        is_gallery_creator = str(workspace.id) == WORKFLOW_GALLERY_CREATOR_WORKSPACE
        if is_gallery_creator:
            conditions &= Q(created_by_sanka=True)
        else:
            conditions &= Q(workspace=workspace)
            conditions &= Q(created_by_sanka__isnull=True)
        workflows = Workflow.objects.filter(conditions)
        filters = ViewFilter.objects.filter(view=view)
        workflows = apply_view_filter(filters, workflows)
        workflows = get_ordered_items(view, workflows)

        columns = ["name", "trigger"]
        view_filter = view.viewfilter_set.filter(property__isnull=True).first()
        if view_filter:
            columns += ast.literal_eval(view_filter.column)
        context = {
            "columns": columns,
            "view_id": str(view.id),
            "project": project,
            "workflows": workflows,
            "p_id": p_id,
            "LANGUAGE_CODE": request.LANGUAGE_CODE,
            "is_gallery_creator": str(workspace.id)
            == WORKFLOW_GALLERY_CREATOR_WORKSPACE,
        }

        return render(request, "data/projects/partial-workflows-rows.html", context)

    elif views_ids:
        res = [str(view_id) for view_id in views_ids]
        if project:
            project.view_order = res
            project.save()
            views = get_ordered_project_views(project, target, user=request.user)

            context = {
                "views": views,
                "view_id": view_id,
                "project": project,
                "p_id": p_id,
                "LANGUAGE_CODE": request.LANGUAGE_CODE,
            }
            return render(request, "data/projects/partial-view-menu.html", context)

    view_order_mapping = {
        TYPE_OBJECT_GOAL: "goal_view_order",
        TYPE_OBJECT_TASK: "task_view_order",
        TYPE_OBJECT_WORKFLOW: "workflow_view_order",
        TYPE_OBJECT_CONTACT: "contacts_view_order",
        TYPE_OBJECT_COMPANY: "companies_view_order",
        TYPE_OBJECT_ITEM: "commerce_items_view_order",
        TYPE_OBJECT_ORDER: "commerce_orders_view_order",
        TYPE_OBJECT_SUBSCRIPTION: "commerce_subscription_view_order",
        TYPE_OBJECT_INVENTORY: "commerce_inventory_view_order",
        TYPE_OBJECT_CASE: "commerce_deals_view_order",
        TYPE_OBJECT_INVOICE: "invoices_view_order",
        TYPE_OBJECT_ESTIMATE: "estimates_view_order",
        TYPE_OBJECT_RECEIPT: "receipts_view_order",
        TYPE_OBJECT_DELIVERY_NOTE: "delivery_slips_view_order",
        TYPE_OBJECT_PURCHASE_ORDER: "purchaseorder_view_order",
        # TYPE_OBJECT_PURCHASE_SUPPLIER: 'purchasesupplier_view_order',
        TYPE_OBJECT_BILL: "bill_view_order",
        TYPE_OBJECT_EXPENSE: "expense_view_order",
        TYPE_OBJECT_PANEL: "panel_view_order",
        TYPE_OBJECT_DASHBOARD: "dashboard_view_order",
        TYPE_OBJECT_CUSTOM_OBJECT: "custom_object_view_order",
    }

    if target in view_order_mapping:
        if custom_object:
            prev_res = workspace.custom_object_view_order
            prev_res[str(custom_object.id)] = res

            setattr(workspace, view_order_mapping[target], prev_res)
            workspace.save()
        else:
            setattr(workspace, view_order_mapping[target], res)
            workspace.save()

        views = get_ordered_views(
            workspace, target, user=request.user, custom_object=custom_object
        )
        context = {
            "views": views,
            "view_id": view_id,
            "target": target,
            "p_id": p_id,
            "custom_object": custom_object,
        }
        return render(request, "data/projects/partial-view-menu.html", context)
    else:
        return HttpResponse(status=500)


@login_or_hubspot_required
def generate_workflow_history(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    workflow_id = request.POST.get("workflow_id")

    if not workflow_id:
        msg = "Failed to create task. Unable to retrieve workflow information."
        if lang == "ja":
            msg = "タスクの作成に失敗しました。ワークフロー情報を取得できません。"
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message=msg,
            type="error",
        )
        response = HttpResponse(status=404)
        response["HX-Trigger"] = json.dumps(
            {
                "showFailedBar": "",
                "reloadNotifNumber": "",
            }
        )
        return response

    try:
        workflow = Workflow.objects.get(id=workflow_id)
    except Workflow.DoesNotExist:
        msg = "Failed to create task. Unable to retrieve workflow information."
        if lang == "ja":
            msg = "タスクの作成に失敗しました。ワークフロー情報を取得できません。"
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message=msg,
            type="error",
        )
        response = HttpResponse(status=400)
        response["HX-Trigger"] = json.dumps(
            {
                "showFailedBar": "",
                "reloadNotifNumber": "",
            }
        )
        return response

    if not workflow.valid_to_run:
        response = HttpResponse(status=400)
        response["HX-Trigger"] = json.dumps(
            {
                "showFailedBar": "",
                "reloadNotifNumber": "",
            }
        )
        return response

    # Create WorkflowHistory
    print("+++\nCreating WorkflowHistory\n+++")
    workflow_history = WorkflowHistory.objects.create(
        workspace=get_workspace(request.user),
        title=workflow.title,
        workflow=workflow,
        status="todo",
    )
    [workflow_history.assignee.add(user) for user in workflow.assignee.all()]

    # Create Nodes
    prev_node = None
    nodes = []
    for node in get_nodes(workflow):
        current_node = ActionNode.objects.create(
            workflow_history=workflow_history,
            action=node.action,
            predefined_input=node.predefined_input,
            previous_output_data=node.previous_output_data,
            input_data=node.input_data,
            is_base=node.is_base,
        )
        current_node.display_name.add(*node.display_name.all())
        current_node.save()
        if prev_node:
            prev_node.next_node = current_node
            prev_node.save()

        prev_node = current_node
        nodes.append(current_node)

    # Create Task Action Tracker
    wat = WorkflowActionTracker.objects.create(
        workspace=workspace,
        workflow_history=workflow_history,
        created_by=request.user,
        status="running",
    )

    # Create Action Trackers
    print("Create Action Trackers", wat)
    print()
    params = {}
    run_node_in_bg = None
    run_node_directly = None
    for i, node in enumerate(nodes):
        at = ActionTracker.objects.create(
            workspace=workspace,
            workflow_action_tracker=wat,
            node=node,
            status="initialized",
        )
        if node.input_data:
            at.input_data = node.input_data
            at.initial_input_data = node.input_data
        if node.previous_output_data:
            at.previous_output_data = node.previous_output_data
        print("[Generate Workflow Action] - created workflow node", node.__dict__)
        print("[Generate Workflow Action] - created action tracker", at.__dict__)
        print("[Generate Workflow Action] - action", node.action)
        if i == 0:
            at.status = "running"
        at.save()

        wat.refresh_from_db()
        # Define which node to run directly and which node to start run in background
        if i == 0 or (
            i > 0 and run_node_directly == nodes[i - 1] and wat.status == "running"
        ):
            params["action-tracker-id"] = str(at.id)
            params["workflow-action-tracker-id"] = str(wat.id)
            params["action-node-id"] = str(node.id)
            params["user"] = request.user.id
            params["lang"] = lang
            print(
                "[Generate Workflow Action] - is run directly", node.action.run_directly
            )
            if node.action.run_directly:
                run_node_directly = node
                if not is_action_condition_met(at):
                    at.status = "skipped"
                    at.completed_at = timezone.now()
                    at.save()

                    wat.status = "success"
                    wat.save()

                    next_node = None
                    if node:
                        next_node = node.next_node
                    if next_node:
                        sanka_action.trigger_next_action(
                            current_action_tracker=at,
                            workflow_action_tracker=wat,
                            current_node=node,
                            user_id=str(request.user.id),
                            lang=lang,
                        )

                    module_slug = request.POST.get("module")
                    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
                    if not module_slug:
                        module = (
                            Module.objects.filter(
                                workspace=workspace,
                                object_values__contains=TYPE_OBJECT_WORKFLOW,
                            )
                            .order_by("order", "created_at")
                            .first()
                        )
                        module_slug = module.slug
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
                    )

                at.status = "running"
                at.save()
                print("[Generate Workflow Action] - Run directly", run_node_directly)

                # NEW ACTION
                if node.action.slug:
                    failed_to_run = False
                    view_res = None
                    try:
                        view_func = resolve(
                            reverse("action_form_mapper", host="app")[2:].replace(
                                "app." + settings.PARENT_HOST, ""
                            )
                            + node.action.slug
                            + "/",
                            urlconf="data.urls",
                        ).func
                        mutable_post = request.POST.copy()
                        mutable_post.update({"action_tracker_id": str(at.id)})
                        mutable_post.update({"workflow_action_tracker_id": str(wat.id)})
                        mutable_post.update({"action_node_id": str(node.id)})
                        mutable_post.update({"action_slug": node.action.slug})
                        request.POST = mutable_post
                        view_res = view_func(request)
                        if view_res.status_code < 400:
                            return view_res
                        else:
                            failed_to_run = True
                    except Exception as e:
                        print(f"[Generate Workflow Action] [Run Action] [ERROR1] - {e}")
                        traceback.print_exc()
                        failed_to_run = True

                    if failed_to_run:
                        at.refresh_from_db()
                        at.status = "failed"
                        at.save()

                        wat.status = "failed"
                        wat.save()

                        if lang == "ja":
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=f"タスクアクション実行に失敗しました: {workflow_history.title}",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=f"Task action failed to run: {workflow_history.title}",
                                type="error",
                            )
                        print("[Generate Workflow Action] - Action stopped.")

                else:
                    try:
                        resp_action = sanka_action.run_flow(
                            action_id=str(nodes[i].action.uid),
                            single_task=True,
                            input=params,
                        )
                        if not isinstance(resp_action, bool):
                            if lang == "ja":
                                Notification.objects.create(
                                    workspace=get_workspace(request.user),
                                    user=request.user,
                                    message="ダウンロードファイルのために実行されるアクション",
                                    type="success",
                                )
                            else:
                                Notification.objects.create(
                                    workspace=get_workspace(request.user),
                                    user=request.user,
                                    message="Action running For Download File",
                                    type="success",
                                )

                            return resp_action
                    except Exception as e:
                        print(f"[Generate Workflow Action] [Run Action] [ERROR1] - {e}")

                        traceback.print_exc()
                        at.refresh_from_db()
                        at.status = "failed"
                        at.save()

                        wat.status = "failed"
                        wat.save()

                        if lang == "ja":
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=f"タスクアクション実行に失敗しました: {workflow_history.title}",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=f"Task action failed to run: {workflow_history.title}",
                                type="error",
                            )
                        print("[Generate Workflow Action] - Action stopped.")
            else:
                run_node_in_bg = node
                at.status = "running"
                at.save()

    # Define Response based on previous page
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug
        response = redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + f"?h_workflow={workflow.id}&drawer_tab=history"
        )
    else:
        response = redirect(reverse("main", host="app"))
    if wat.status == "failed" or not run_node_in_bg:
        return response

    # Send queue to Run Actions
    at = ActionTracker.objects.get(
        workspace=workspace, workflow_action_tracker=wat, node=run_node_in_bg
    )
    print(
        "[Generate Workflow Action] - Run action in background", run_node_in_bg.action
    )
    params["user_id"] = str(request.user.id)

    # Create payload for background job
    payload = RunWorkflowActionPayload(
        action_node_id=params.get("action-node-id"),
        action_tracker_id=params.get("action-tracker-id"),
        workflow_action_tracker_id=params.get("workflow-action-tracker-id"),
        user_id=str(request.user.id),
        workspace_id=str(workspace.id),
        workflow_history_id=str(wat.workflow_history.id) if wat.workflow_history else None,
        language=lang,
    )
    
    # Create background job
    job_id = create_bg_job(
        workspace=workspace,
        user=request.user,
        function_name="run_workflow_action",
        payload=payload.model_dump(mode="json"),
    )
    payload.background_job_id = job_id
    
    print(
        f"[Generate Workflow Action] - Adding run action queue message to bg-job: {payload.model_dump()}"
    )
    
    try:
        ref = run_workflow_action_task.run_no_wait(input=payload)
    except Exception as e:
        logger.error(f"Generate Workflow Action: Exception occurred during run_workflow_action_task: {str(e)}", exc_info=True)
        ref = None
    
    is_running = None
    if ref:
        add_hatchet_run_id(job_id, ref)
        is_running = True
    else:
        is_running = False

    if not is_running:
        dn = DiscordNotification()
        dn.send_message("[Trigger next action] - Send queue to run-action failed.")
        at.status = "failed"
        at.save()


    print("[Generate Workflow Action] - Action running successfully")
    if lang == "ja":
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="アクションは正常に実行されます",
            type="success",
        )
    else:
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="Action running successfully",
            type="success",
        )

    return response


@login_or_hubspot_required
@require_POST
def task_custom_field(request):
    workspace = get_workspace(request.user)

    custom_field_id = request.POST.get("custom_field_id", "")
    custom_field_value_id = request.POST.get("custom_field_value_id", "")
    task = None

    if not (custom_field_id or custom_field_value_id):
        print(
            "[Task Custom Field] [ERROR] - Require one of custom_field_id or custom_field_value_id"
        )
        return HttpResponse(status=400)

    if custom_field_id:
        value = request.POST.get(f"task_custom_field-{custom_field_id}", None)
        task_id = request.POST.get("task_id", None)
        if value is None or not task_id:
            value = request.FILES.get(f"task_custom_field-{custom_field_id}", None)
            if value is None:
                print("[Task Custom Field] [ERROR] - Require value and task_id.")
                return HttpResponse(status=400)
    else:
        value = request.POST.get(
            f"task_custom_field_value-{custom_field_value_id}", None
        )
        if value is None:
            print("[Task Custom Field] [ERROR] - Require value.")
            return HttpResponse(status=400)

    if custom_field_id:
        try:
            task_custom_field = TaskCustomFieldName.objects.get(id=custom_field_id)
            task, _ = Task.objects.get_or_create(id=task_id, workspace=workspace)
            task.usage_status = "active"
            task.save()
        except:
            return HttpResponse(status=404)
        if task_custom_field.type == "date":
            date_value = value
            if "年" in value:
                date_value = (
                    datetime.strptime(value, "%Y年%m月%d日").date().strftime("%Y-%m-%d")
                )
            task_custom_field_value = TaskCustomFieldValue.objects.create(
                field_name=task_custom_field, task=task, value=date_value
            )
        elif task_custom_field.type == "file":
            file_obj = value
            file_obj.name = (
                str(uuid.uuid4())
                + "-"
                + file_obj.name.split(".")[0]
                + "."
                + file_obj.name.split(".")[-1]
            )
            task_custom_field_value = TaskCustomFieldValue.objects.create(
                field_name=task_custom_field,
                task=task,
                value=file_obj.name,
                file=file_obj,
            )
        else:
            task_custom_field_value = TaskCustomFieldValue.objects.create(
                field_name=task_custom_field, task=task, value=value
            )

        if task_custom_field_value:
            task_custom_field_value.save(
                log_data={"user": request.user, "workspace": workspace}
            )
    else:
        try:
            task_custom_field_value = TaskCustomFieldValue.objects.get(
                id=custom_field_value_id
            )
        except:
            return HttpResponse(status=404)
        task_custom_field_value.value = value
        if task_custom_field_value.field_name.type == "date":
            task_custom_field_value.value = (
                datetime.strptime(value, "%Y年%m月%d日").date().strftime("%Y-%m-%d")
            )
        task_custom_field_value.save(
            log_data={"user": request.user, "workspace": workspace}
        )

    context = {"task_custom_field_value": task_custom_field_value, "task": task}
    print("....", context)
    return render(request, "data/projects/task-custom-field.html", context)


@login_or_hubspot_required
def projects_settings(request):
    if request.method == "POST":
        logger.info("POST request received for project settings")
        logger.info(request.POST)
        workspace = get_workspace(request.user)
        p_id = request.POST.get("p_id")
        project_default = request.POST.get("project_default",False)
        module_slug = request.POST.get("module_slug",None)
        source = request.POST.get("source", "taskflow")
        p_title = request.POST.get("p_title")

        if "update-prj-button-delete" in request.POST:
            project = Projects.objects.get(id=p_id)
            project.delete()

            if source == "workspace_setting":
                return redirect(reverse(source, host="app") + "?setting_type=task")
            else:
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if not module_slug:
                    module_slug = module.slug

                redirect_url = reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                return redirect(redirect_url)
        else:
            if p_id:
                project = Projects.objects.get(id=p_id)
                if p_title:
                    project.title = p_title
                project.save()
            else:
                project = Projects.objects.create(workspace=workspace, title=p_title)
                p_id = project.id
                
            if project_default:
                # Set default properties for the project
                projects = Projects.objects.filter(workspace=workspace).exclude(id=project.id)
                projects.update(default=False)
                
                # Set the current project as default
                project.default = True
                project.save()
                
            if source == "workspace_setting":
                return redirect(
                    reverse(source, host="app") + f"?setting_type=task&p_id={p_id}"
                )
            else:
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if not module_slug:
                    module_slug = module.slug

                redirect_url = reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                return redirect(redirect_url + f"?p_id={p_id}")

    p_id = request.GET.get("p_id")
    source = request.GET.get("source", "taskflow")
    module_slug = request.GET.get("module_slug", None)
    project = None
    if p_id:
        project = Projects.objects.get(id=p_id)

    context = {
        "module_slug": module_slug,
        "project": project,
        "source": source,
    }
    return render(request, "data/projects/component/manage_project.html", context)


def projects_list_api(request):
    if request.method == "POST":
        # Get Param
        print(request.POST)
        workspace = get_workspace(request.user)
        start = request.POST["start"]
        end = request.POST["end"]
        p_id = request.POST.get("p_id")
        view_id = request.POST.get("view_id")
        tasks = []
        tasks = Task.objects.filter(
            workspace=workspace, created_at__range=[start, end]
        ).order_by("-created_at")
        if p_id:
            project_target = Projects.objects.get(id=p_id)
            tasks = tasks.filter(project_target=project_target)

        if view_id:
            view = View.objects.filter(id=view_id).first()
            print("Checking View", view)
            if view:
                view_filter = view.viewfilter_set.filter(property__isnull=True).first()
                if view_filter and view_filter.filter_value:
                    conditions = build_view_filter(
                        Q(workspace=workspace), view_filter, TYPE_OBJECT_TASK
                    )
                    print(".....................", conditions)
                    tasks = tasks.filter(conditions)

        workspace = get_workspace(request.user)
        tz = pytz.timezone(
            workspace.timezone if workspace and workspace.timezone else "UTC"
        )
        # Compile to event format
        event = []
        if tasks:
            for task in tasks:
                try:
                    # === Start Date with Timezone set up
                    start_date = task.created_at.astimezone(tz)
                    tz_offset = start_date.strftime("%z")

                    start_date = start_date.strftime("%Y-%m-%dT%H:%M:%S")
                    # Add colon between hours and minutes
                    tz_offset_formatted = f"{tz_offset[:3]}:{tz_offset[3:]}"
                    start_date = f"{start_date}{tz_offset_formatted}"

                    # === End Date with Timezone set up
                    end_date = task.created_at.astimezone(tz)
                    tz_offset = end_date.strftime("%z")

                    end_date = end_date.strftime("%Y-%m-%dT%H:%M:%S")
                    # Add colon between hours and minutes
                    tz_offset_formatted = f"{tz_offset[:3]}:{tz_offset[3:]}"
                    end_date = f"{end_date}{tz_offset_formatted}"

                    event.append(
                        {
                            "title": task.title,
                            "start": start_date,
                            "end": end_date,
                            "display": "block",
                            "extendedProps": {"task_id": str(task.id)},
                        }
                    )
                except:
                    pass
        # print(event)
    return HttpResponse(json.dumps(event), content_type="application/json")


@login_or_hubspot_required
def projects_options(request):
    lang = request.LANGUAGE_CODE

    workspace = get_workspace(request.user)
    q = request.GET.get("q")
    page = request.GET.get("page", 1)

    filter_conditions = Q(workspace=workspace)
    if q:
        filter_conditions &= Q(title__icontains=q)

    prj = Projects.objects.filter(filter_conditions).order_by("-created_at")

    res = []
    ITEMS_PER_PAGE = 30
    prj_paginator = Paginator(prj, ITEMS_PER_PAGE)
    paginated_prj = []
    more_pagination = False
    if page:
        try:
            prj_page_content = prj_paginator.page(page if page else 1)
            paginated_prj = prj_page_content.object_list
            more_pagination = prj_page_content.has_next()
        except EmptyPage:
            pass

    for project in paginated_prj:
        try:
            if project.title:
                title = project.title
            else:
                if lang == "ja":
                    title = "プロジェクト"
                else:
                    title = "Project"

            text = f"{title}"
        except Exception:
            pass

        res.append(
            {
                "id": str(project.id),
                "text": text,
            }
        )

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


@login_or_hubspot_required
def views_dropdown(request):
    workspace = get_workspace(request.user)
    q_ = request.GET.get("q")
    object_type = request.GET.get("object_type")
    module = request.GET.get("menu_key")
    if not object_type or object_type not in TYPE_OBJECTS:
        return HttpResponse(status=400)

    if not q_:
        views = get_ordered_views(workspace, object_type, user=request.user)
        context = {
            "views": views,
            "object_type": object_type,
            "menu_key": module,
            "q": q_,
        }
        return render(
            request, "data/projects/partial-dropdown-view-menu-list.html", context
        )

    views = get_ordered_views(workspace, object_type, q=q_, user=request.user)
    context = {
        "views": views,
        "object_type": object_type,
        "menu_key": module,
        "q": q_,
    }
    return render(
        request, "data/projects/partial-dropdown-view-menu-list.html", context
    )


def add_subtask_drawer(request):
    module_slug = request.GET.get("module", "")
    view_id = request.GET.get("view_id", "")

    task_id = request.GET.get("task_id", None)
    if not task_id:
        return HttpResponse(status=400)

    task = Task.objects.filter(id=task_id).first()
    if not task:
        return HttpResponse(status=404)

    context = {
        "task": task,
        "module": module_slug,
        "view_id": view_id,
    }

    return render(
        request, "data/projects/component/subtask/sub-task-drawer.html", context
    )


@login_or_hubspot_required
def create_subtask(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if request.method == "POST":
        title = request.POST.get("title")
        id = request.POST.get("task_id")
        view_id = request.POST.get("view_id")
        status = request.POST.get("status", None)

        user_list = request.POST.getlist("users")
        due_date = None
        project_target = None
        task_desc = request.POST.get("task_desc", "")

        start_date = request.POST.get("start_date", None)
        if start_date:
            start_date = parse_date(start_date)
        due_date = request.POST.get("due_date", None)
        if due_date:
            due_date = parse_date(due_date)

        # Line Item
        item_id_list = request.POST.getlist("item", [])
        item_price_list = request.POST.getlist("item_price", [])
        number_of_items_list = request.POST.getlist("number_of_items", [])
        currency = request.POST.get("price-info-currency", "USD")

        object_id = request.POST.get("object_id", None)
        if object_id:
            main_task = Task.objects.filter(id=object_id).first()
            if not main_task:
                return load_subtask(request)

        project_target = main_task.project_target

        view = None
        if view_id and view_id != "None":
            try:
                # Validate that view_id is a valid UUID - check if it's already a UUID object
                if not isinstance(view_id, uuid.UUID):
                    uuid.UUID(view_id)
                view = View.objects.get(id=view_id)
            except (ValueError, TypeError, View.DoesNotExist):
                view_id = None
                pass

        line_item_allowed = False
        if view:
            property_set = view.form if view.form else None
            if property_set:
                if "line_item" in property_set.children:
                    line_item_allowed = True

        if id:
            task = Task.objects.create(
                id=id,
                workspace=workspace,
                project_target=project_target,
                usage_status="active",
            )
        else:
            task = Task.objects.create(
                workspace=workspace,
                project_target=project_target,
                usage_status="active",
            )

        task.title = title
        if status:
            task.status = status
        else:
            task.status = None

        if project_target:
            task.project_target = project_target

        if task_desc:
            task.description = task_desc

        if start_date:
            task.start_date = start_date

        if due_date:
            task.due_date = due_date

        if user_list:
            try:
                users_list = []
                groups_list = []
                for item in user_list:
                    if item.startswith("user|"):
                        users_list.append(item.replace("user|", ""))
                    elif item.startswith("group|"):
                        groups_list.append(item.replace("group|", ""))

                users = User.objects.filter(
                    workspace=workspace, username__in=users_list
                )
                projects = Group.objects.filter(workspace=workspace, id__in=groups_list)

                for user_ in users:
                    if user_ not in task.assignee.all():
                        notif_assigned_assignee(
                            task=task, assignee=user_, view_id=view_id, lang=lang
                        )

                for project_ in projects:
                    for user_ in project_.user.all():
                        if user_ not in task.assignee.all():
                            notif_assigned_assignee(
                                task=task, assignee=user_, view_id=view_id, lang=lang
                            )

                task.assignee.clear()
                task.assignee.add(*users)

                task.project.clear()
                task.project.add(*projects)
            except Exception as e:
                print(e)
        else:
            task.project.clear()
            task.assignee.clear()

        if line_item_allowed:
            task_items = task.task_items.all()
            task_items.delete()

            order_view = 0
            for idx, item_id in enumerate(item_id_list):
                if not item_id or item_id == "":
                    continue

                number_item = number_of_items_list[idx]
                if number_of_items_list[idx] == "":
                    number_item = 0
                elif isinstance(number_of_items_list[idx], str):
                    number_item = float(number_of_items_list[idx])

                price = item_price_list[idx]
                item_price = None
                if item_price_list[idx] == "":
                    price = 0
                elif is_valid_uuid(item_price_list[idx]):
                    shopturbo_item_prices = ShopTurboItemsPrice.objects.filter(
                        id=item_price_list[idx]
                    )
                    if shopturbo_item_prices.exists():
                        price = shopturbo_item_prices.first().price
                    else:
                        price = 0
                else:
                    # Clean comma-formatted price strings (e.g., "3,000.0" -> "3000.0")
                    original_price = price
                    if isinstance(price, str):
                        price = price.replace(',', '')
                    try:
                        price = float(price)
                        if original_price != str(price):
                            logger.info(f"[SUBTASK_PRICE_FIX] Cleaned price format: '{original_price}' -> {price}")
                    except (ValueError, TypeError):
                        logger.warning(f"[SUBTASK_PRICE_FIX] Failed to convert price '{original_price}' to float, using 0")
                        price = 0

                try:
                    item = ShopTurboItems.objects.get(id=item_id)
                    custom_item_name = item.name
                except:
                    custom_item_name = item_id
                    item = None

                try:
                    total_price = price * number_item
                except:
                    total_price = 0

                TaskItem.objects.create(
                    task=task,
                    # Item Related
                    item=item,
                    item_price=item_price,
                    # Task Item Related
                    custom_item_name=custom_item_name,
                    item_price_task=price,
                    currency=currency,
                    number_item=number_item,
                    total_price=total_price,
                    # Ordering
                    order_view=order_view,
                )
                order_view += 1

        # Set the main task
        if main_task:
            task.main_task = main_task

        task.save(
            log_data={"user": request.user, "status": "create", "workspace": workspace}
        )

    return load_subtask(request)


@login_or_hubspot_required
def remove_subtask_relation(request):
    print(request.POST)
    if request.method == "POST":
        task_id = request.POST.get("task_id", "")

        object_id = request.POST.get("object_id", "")
        module_slug = request.POST.get("module", "")

        if not object_id or not module_slug:
            return HttpResponse(status=400)

        main_task = Task.objects.filter(id=object_id).first()
        if not main_task:
            return HttpResponse(status=404)

        task = main_task.sub_tasks.filter(id=task_id).first()
        if not task:
            return HttpResponse(status=404)

        task.main_task = None
        task.save()

        return load_subtask(request)

    return HttpResponse(status=400)


@login_or_hubspot_required
def load_subtask(request):
    workspace = get_workspace(request.user)

    object_id = ""
    module_slug = ""
    view_id = ""
    if request.method == "GET":
        object_id = request.GET.get("object_id", "")
        module_slug = request.GET.get("module", "")
        view_id = request.GET.get("view_id", "")
    if request.method == "POST":
        object_id = request.POST.get("object_id", "")
        module_slug = request.POST.get("module", "")
        view_id = request.POST.get("view_id", "")

    if not object_id:
        return HttpResponse(status=400)

    task = Task.objects.filter(id=object_id).first()
    if not task:
        return HttpResponse(status=404)

    subtasks = task.sub_tasks.all()

    if not module_slug:
        module_slug = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
            )
            .order_by("order", "created_at")
            .first()
        )

    context = {
        "task": task,
        "subtasks": subtasks,
        "module": module_slug,
        "view_id": view_id,
    }

    return render(
        request, "data/projects/component/subtask/sub-task-list.html", context
    )
