{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load hosts %}
{% load static %}

{% block static_content %}
<style>
    .solution-card:hover {
        color: #0d6efd !important;
    }
    .active {
      font-weight: bold;
      background-color: #f3f4f6;
    }

    /* --- Horizontal Arrow (Desktop: lg and up) --- */
    .arrow-pointer-horizontal {
        width: 95%;
        height: 10px;
        background: linear-gradient(to right, #EB3678, #FB8B24);
        position: relative;
        /* margin-bottom is handled by utility class mb-5 */

        /* Left arrowhead (start) - looks like pointing left */
        &:after {
          content: '';
          position: absolute;
          left: 0; bottom: 0; width: 0; height: 0;
          /* This creates a triangle pointing right, cut out by the background */
          /* To make it look like it points left, make border same as background */
          border-left: 5px solid #fff; /* Match page background */
          border-top: 5px solid transparent;
          border-bottom: 5px solid transparent;
        }

        /* Right arrowhead (end) - pointing right */
        &:before {
          content: '';
          position: absolute;
          right: -5px; /* Position outside the right edge */
          bottom: 0; width: 0; height: 0;
          border-left: 5px solid #FB8B24; /* End color of gradient */
          border-top: 5px solid transparent;
          border-bottom: 5px solid transparent;
        }
    }

    /* --- Vertical Arrow (Mobile: below lg) --- */
    .arrow-pointer-vertical {
        width: 10px;
        height: auto;
        background: linear-gradient(to bottom, #EB3678, #FB8B24);
        position: relative;
        flex-shrink: 0;

        /* Bottom arrowhead (end) - pointing down */
        &:before {
          content: '';
          position: absolute;
          bottom: -5px;
          left: 0; width: 0; height: 0;
          /* Triangle pointing down */
          border-top: 5px solid #FB8B24;
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
        }
    }

    /* Remove bullet points from menu list */
    #menu {
        list-style: none;
        padding-left: 0;
        margin: 0;
    }

    /* Menu item styling */
    #menu li {
        border-bottom: 1px solid #e5e7eb;
        margin: 0;
    }

    #menu li:last-child {
        border-bottom: none;
    }

    #menu div {
        height: 77.5px;
        width: 100%;
        display: flex;
        align-items: center;
        padding: 0 1.5rem;
        transition: background-color 0.2s ease;
    }

    #menu div:hover {
        background-color: #f3f4f6 !important;
    }

    /* Equal height layout for sidebar and content */
    .equal-height-container {
        display: flex;
        min-height: 600px; /* Minimum height for the container */
    }

    .equal-height-container > div {
        display: flex;
        flex-direction: column;
    }

    /* Ensure menu fills the sidebar height */
    #menu {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    #menu li {
        flex: 1;
        display: flex;
    }

    #menu li button {
        flex: 1;
        height: auto;
        min-height: 77.5px;
    }

    /* Fix border radius for proper rounded corners */
    .equal-height-container {
        border-radius: 0.75rem !important;
        overflow: hidden;
    }

    /* Ensure sidebar border extends full height */
    .equal-height-container .w-lg-25 {
        border-right: 2px solid #e5e7eb;
    }

    /* Remove default list styling */
    #menu li {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    /* Ensure proper border styling */
    .equal-height-container {
        border: 2px solid #e5e7eb;
    }

    /* Fix sidebar border to extend full height */
    .equal-height-container .w-lg-25 {
        border-right: 2px solid #e5e7eb;
        margin-right: -2px; /* Overlap with container border */
    }

    /* Ensure content area has proper background */
    .equal-height-container .w-lg-75 {
    }

    /* Mobile Accordion Styles */
    @media (max-width: 991.98px) {
        /* Reset mobile browser default link styling */
        .mobile-accordion button,
        .mobile-accordion a,
        .mobile-accordion span {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        /* Hide desktop sidebar/content layout on mobile */
        .equal-height-container {
            display: none !important;
        }

        /* Show mobile accordion */
        .mobile-accordion {
            display: block !important;
        }

        /* Accordion item styling */
        .accordion-item {
            margin-bottom: 0.5rem;
            overflow: hidden;
        }

        .accordion-header {
            background: white;
            border: none;
            width: 100%;
            padding: 1rem 1.5rem;
            text-align: left;
            font-weight: bold;
            font-size: 1.1rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
            /* Explicit color declarations to override mobile browser defaults */
            color: #000000 !important;
            -webkit-text-fill-color: #000000 !important;
            -webkit-appearance: none;
        }

        .accordion-header:hover {
            background-color: #f3f4f6;
            color: #000000 !important;
            -webkit-text-fill-color: #000000 !important;
        }

        .accordion-header.active {
            background-color: #f3f4f6;
            color: #000000 !important;
            -webkit-text-fill-color: #000000 !important;
        }

        /* Ensure accordion header spans maintain black color */
        .accordion-header span {
            color: #000000 !important;
            -webkit-text-fill-color: #000000 !important;
        }

        .accordion-icon {
            transition: transform 0.2s ease;
            font-size: 1.2rem;
        }

        .accordion-header.active .accordion-icon {
            transform: rotate(180deg);
        }

        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: #f9fafb;
        }

        .accordion-content.active {
            max-height: 1000px; /* Large enough to accommodate content */
        }

        .accordion-content-inner {
            padding: 1.5rem;
        }

        .accordion-content img {
            width: 100%;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .accordion-content p {
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        /* Mobile-specific overrides for btn-light-primary links */
        .mobile-accordion .btn-light-primary,
        .mobile-accordion .btn-light-primary:link,
        .mobile-accordion .btn-light-primary:visited,
        .mobile-accordion .btn-light-primary:hover,
        .mobile-accordion .btn-light-primary:active,
        .mobile-accordion .btn-light-primary:focus {
            color: #472CF5 !important;
            -webkit-text-fill-color: #472CF5 !important;
            -webkit-appearance: none;
            text-decoration: none !important;
        }

        /* iOS Safari specific overrides */
        @supports (-webkit-touch-callout: none) {
            .accordion-header,
            .accordion-header span,
            .mobile-accordion .btn-light-primary {
                color: #000000 !important;
                -webkit-text-fill-color: #000000 !important;
            }

            .mobile-accordion .btn-light-primary {
                color: #472CF5 !important;
                -webkit-text-fill-color: #472CF5 !important;
            }
        }

        /* Additional mobile browser compatibility */
        .mobile-accordion * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Force color inheritance for all mobile accordion text */
        .mobile-accordion .accordion-header,
        .mobile-accordion .accordion-header *,
        .mobile-accordion button,
        .mobile-accordion button * {
            color: inherit !important;
        }

        /* Specific color enforcement for accordion headers */
        .mobile-accordion .accordion-header {
            color: #000000 !important;
        }

        /* Specific color enforcement for child solution links */
        .mobile-accordion .btn-light-primary {
            color: #472CF5 !important;
            background-color: #D1C9FF !important;
            border-color: #D1C9FF !important;
        }
    }

    /* Desktop: Hide mobile accordion */
    @media (min-width: 992px) {
        .mobile-accordion {
            display: none !important;
        }

        /* Desktop content fade animation */
        #content {
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            opacity: 1;
            transform: translateX(0);
        }

        #content.fade-out {
            opacity: 0;
            transform: translateX(10px);
        }

        #content.fade-in {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Ensure rounded corners display properly */
    .hero-gradient,
    .container-xxl {
        border-radius: inherit !important;
    }

    /* Force rounded corners on specific sections */
    .new-hero .hero-gradient {
        border-top-left-radius: 1rem !important;
        border-top-right-radius: 1rem !important;
        overflow: hidden;
    }

    .container-xxl[style*="border-top-left-radius"] {
        border-top-left-radius: 3rem !important;
        border-top-right-radius: 3rem !important;
        overflow: hidden;
    }

</style>

<div class="new-hero my-20">
    <div class="hero-gradient" style="background: linear-gradient(180deg, rgba(255, 255, 255, 0) 46.77%, #F4F4FF 94.76%); border-radius: 0px; border-top-left-radius: 1rem !important; border-top-right-radius: 1rem !important; position: relative; z-index: 1; overflow: hidden;">
        <div class="container-xxl px-0 pb-20">
            <div class="row gy-5 align-items-center gx-0">
                <div class="col-12 col-lg-12 mb-10 mb-lg-0">
                    <div class="px-5">
                        <h2 class="tw-text-h2 md:tw-text-h2-lg tw-font-bold tw-text-center mb-4">
                        {% if LANGUAGE_CODE == 'ja'%}
                            バックオフィスは、<span class="d-block d-lg-none"></span>Sankaにおまかせ。

                        {% else %}
                            Let Sanka take care <span class="d-block d-lg-none"></span>of your backoffice.
                        {% endif %}
                        </h2>
                        <div class="mb-7 tw-text-center mw-600px mx-auto tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary">
                            {% if LANGUAGE_CODE == 'ja'%}
                                受注から在庫、発注、請求、支出、HR、会計まで。<br>
                                Sankaがすべての業務を効率化します - スマートなAIで。
                            </div>
                            {% else %}
                            From orders to inventory, billing, POs, accounting, HR, and beyond - <span class="d-none d-lg-block"></span> Sanka streamlines every process in one place with powerful AI.
                            {% endif %}
                        </div>
                        <div class="text-center">
                            <a href="{% host_url 'signup' host 'app' %}" class="fs-4 mx-1 btn btn-primary btn-lg bg-hover-opacity-50 fw-bolder border" >
                                {% if LANGUAGE_CODE == 'ja'%}
                                無料で始める
                                {% else %}
                                Start Free
                                {% endif %}
                            </a> 
                            <a href="{% host_url 'contact' host 'static' %}" class="fs-4 mx-1 btn bg-transparent btn-lg bg-hover-opacity-50 fw-bolder" >
                                {% if LANGUAGE_CODE == 'ja'%}
                                導入を相談する
                                {% else %}
                                Get a Demo
                                {% endif %}
                            </a> 

                        </div>
                    </div>  
                </div>
                <div class="col-12 col-lg-12 px-0 px-lg-0">
                    <div class="text-center" style=>
                        {% if LANGUAGE_CODE == 'ja'%}
                        <img class="w-100 d-block d-md-none" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/fd9e5c64-9827-4aea-a514-e88745bb2122.png" alt="">
                        <img class="w-100 d-none d-md-block" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/6ee8175d-c923-4eb5-9498-156e109341bc.png" alt="" style="margin-top: -10rem !important">

                        {% else %}
                        <img class="w-100 d-block d-md-none" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/f7668636-0444-4709-bb4b-2827a46f8c21.png" alt="">
                        <img class="w-100 d-none d-md-block mt-n20" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/1a4346e7-9160-41da-8a92-a5c98e69f5c8.png" alt="">
                        {% endif %}
                    </div>
                </div>
            </div>

        </div>

    </div>

</div>


<div style="background-color: white; border-top-left-radius: 3rem !important; border-top-right-radius: 3rem !important; margin-top: -7rem; padding-top: 4rem; position: relative; z-index: 10; overflow: hidden;">
    <div class="my-10 mx-auto container-xxl" >

        <!-- Container for Arrows and Solution Cards -->
        <div class="mb-20 mx-auto mw-1000px">
            <div class="mb-10">
                <h3 class="text-gray-800 fw-boldest tw-text-h3 md:tw-text-h3-lg tw-font-bold">
                        {% if LANGUAGE_CODE == 'ja'%}
                            Excelだと業務が回らない、<span class="d-block"></span>
                            バックオフィスチームのために。
                        {% else %}
                            Built for backoffice teams<span class="d-block"></span>
                            who think Excel isn’t enough.
                        {% endif %} 

                </h3>
                <div class="mb-10 tw-text-start tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary w-lg-75 mw-w-600px">

                {% if LANGUAGE_CODE == 'ja'%}
                    もう散らばったExcelファイルにガマンする必要ありません。<span class="d-lg-block">バックオフィス管理システムSankaを使うことで、<span class="d-lg-block">より早く、よりシンプルで、より効率的に仕事ができます。
                {% else %}
                No need to put up with the scattered Excel files anymore. <span class="d-lg-block">
                You can work faster, more simply, and more efficiently with Sanka.
                {% endif %}
                </div>
            </div>
            
            <div class="border d-flex row rounded-3 mw-1000px mb-5" style="background: linear-gradient(180deg, #FFFFFF 68.03%, #F7F7F8 102.41%); border: 1px solid #E8E8E9; border-radius: 16px; flex: none; order: 0; flex-grow: 0;">
                <div class="col-12 col-lg-4 p-10 pb-0 pb-lg-10">
                    <div class="md:tw-text-subtitle-lg tw-text-subtitle fw-bolder mb-3">
                        {% if LANGUAGE_CODE == 'ja'%}
                            連携サービス
                        {% else %}
                            Native Integrations
                        {% endif %}
                    </div>

                    <div>
                        {% if LANGUAGE_CODE == 'ja'%}
                            <div class=" tw-text-start tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary">
                                ご利用中のCRM、EC、POSや会計<span class="d-lg-block"></span>ツールとシームレスにデータを<span class="d-lg-block"></span>連携、すぐに業務を効率化。
                            </div>
                        {% else %}
                            <div class="tw-text-start tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary">
                                Dozens of Integrations natively work with your workflows from CRM, e-commerce and more.
                            </div>
                        {% endif %}
                    </div>

                </div>
                <div class="col-12 col-lg-8 px-0">
                    {% if LANGUAGE_CODE == 'ja'%}
                    <img class="w-100 rounded-3" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/fd9a3f9b-cc2b-4615-98cc-3abe8f5301b2.png" alt="">
                    {% else %}
                    <img class="w-100 rounded-3" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/fd9a3f9b-cc2b-4615-98cc-3abe8f5301b2.png" alt="">
                    {% endif %}
                </div>

            </div>

            <div class="mt-5 row border-0 gx-10">

                <div class="col-12 col-lg-6 d-flex flex-column">
                    <div class="border rounded-3 d-flex row flex-grow mb-5" style="background: linear-gradient(180deg, #FFFFFF 68.03%, #F7F7F8 102.41%); border: 1px solid #E8E8E9; border-radius: 16px; flex: none; order: 0; flex-grow: 0;">
                        <div class="col-12 p-10 pb-0">
                            <div class="md:tw-text-subtitle-lg tw-text-subtitle fw-bolder mb-5">
                                {% if LANGUAGE_CODE == 'ja'%}
                                    AIワークフロー
                                {% else %}
                                    AI Workflows
                                {% endif %}
                            </div>
                            <div>
                                {% if LANGUAGE_CODE == 'ja'%}
                                <div class="tw-text-start tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary">
                                    受注、承認、請求、在庫引当、経費精算、仕訳まで。24時間365日不眠不休のAIが、あなた作業を自動化します。
                                </div>
                                {% else %}
                                <div class="tw-text-start tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary">
                                        Meet San, the AI agent that gets your back office tasks done, just by talking to it. 24/7/365 no time off. 
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-12 px-0">
                            <img class="w-100 rounded-3" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/a29c0884-eba5-4b2a-838f-5326b30cae81.png" alt="">
                        </div>
                    </div>
                </div>

                <div class="col-12 col-lg-6 d-flex flex-column mb-5">
                    <div class="border rounded-3 d-flex row flex-grow-1" style="background: linear-gradient(180deg, #FFFFFF 68.03%, #F7F7F8 102.41%); border: 1px solid #E8E8E9; border-radius: 16px; flex: none; order: 0; flex-grow: 0;">
                        <div class="col-12 p-10  pb-0">
                            <div class="md:tw-text-subtitle-lg tw-text-subtitle fw-bolder mb-5">
                                {% if LANGUAGE_CODE == 'ja'%}
                                    クラウドERP
                                    {% else %}
                                    Cloud ERP
                                {% endif %}
                            </div>
                            <div>
                                {% if LANGUAGE_CODE == 'ja'%}
                            <div class=" tw-text-start tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary">
                                すべてのバックオフィスデータとプロセスを一つのクラウドで管理。複数ツールを行き来する必要はありません。
                            </div>
                            {% else %}
                            <div class="tw-text-start tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary">
                                    Full-fledged ERP to store all the data and processes of orders, billing, inventory, HR, and more. 
                            </div>
                            {% endif %}


                            </div>
                        </div>
                        <div class="col-12 px-0">
                            {% if LANGUAGE_CODE == 'ja'%}
                            <img class="w-100 rounded-3" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/f6c5bbde-55e4-4696-83e9-4530e106f11e.png" alt="">
                            {% else %}
                            <img class="w-100 rounded-3" src="https://nyc3.digitaloceanspaces.com/sankafile/static/content-files/f6c5bbde-55e4-4696-83e9-4530e106f11e.png" alt="">
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div> <!-- End Main Section Wrapper -->

    </div>

</div>

<div class="new-hero mb-20 mw-1000px mx-auto" style="border-top-left-radius: 1rem !important; border-top-right-radius: 1rem !important; overflow: hidden;">
    <div class="container px-lg-0 bg-white">
        <h3 class="text-gray-800 fw-boldest tw-text-h3 md:tw-text-h3-lg tw-font-bold">
            {% if LANGUAGE_CODE == 'ja'%}
                すべての業務フローを、<br>ひとつのシステムで効率化。
                {% else %}
                Entire backoffice.<br> In one platform.
            {% endif %} 
        </h3>
        <div class="mb-10 tw-text-start tw-text-body1-regular md:tw-text-body1-regular-lg tw-text-secondary w-lg-75 mw-w-600px">

        {% if LANGUAGE_CODE == 'ja'%}
            受注、在庫、請求、発注、HR、会計やCRMとの連携まで。<span class="d-lg-block">すべてのバックオフィス業務を効率化する機能を揃えています。
        {% else %}
            Managing orders, inventory, billing, procurement, HR, and accounting, fast.<span class="d-lg-block">
            We offer all the features you need to streamline every back-office operation.
        {% endif %}
        </div>

        <div class="min-h-screen d-flex equal-height-container mb-10">
            <!-- Sidebar -->
            <div class="w-full w-lg-25 p-0 d-flex flex-column" style="border-top-left-radius: 0.75rem; border-bottom-left-radius: 0.75rem;">
                    <ul id="menu" class="flex-grow-1 d-flex flex-column m-0 p-0" style="border-top-left-radius: 0.75rem; border-bottom-left-radius: 0.75rem; overflow: hidden;">
                    {% for solution in public_solutions|slice:"0:8" %}
                    <li class="flex-fill" style="list-style: none;">
                        <div class="menu-button text-left hover:bg-gray-100 h-100 bg-white px-0" data-content="solution-{{ solution.id }}" style="cursor: pointer; border: none; width: 100%; {% if forloop.first %}border-top-left-radius: 0.75rem;{% endif %} {% if forloop.last %}border-bottom-left-radius: 0.75rem;{% endif %}" onclick="console.log('Button clicked directly:', this.getAttribute('data-content'));">
                            <div class="px-0">
                                <div class="{% if forloop.first %} border-2 {% endif %} h-50px fw-bolder fs-4">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                        {{solution.keyword_ja}}
                                    {% else %}
                                        {{solution.keyword}}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </li>
                    {% endfor %}
                    </ul>
                </div>

            <!-- Content -->
            <div class="w-full w-lg-75 p-6 bg-gray-50 d-flex flex-column" style="border-top-right-radius: 0.75rem; border-bottom-right-radius: 0.75rem;">
                <div id="content" class="space-y-4 flex-grow-1">
                {% with public_solutions|first as first_solution %}
                <img 
                {% if LANGUAGE_CODE == 'ja' %}
                src="{{first_solution.image_url_ja}}"
                {% else %}
                src="{{first_solution.image_url}}" 
                {% endif %}
                alt="Demo Screenshot" class="w-100 rounded shadow-md max-w-full h-auto mb-4">

                <p class="fs-6 tw-text-secondary">
                    {% if LANGUAGE_CODE == 'ja'%}
                        {{first_solution.body_ja|safe|remove_linebreaks|truncatechars:74}}
                    {% else %}
                        {{first_solution.body|safe|remove_linebreaks|truncatechars:120}}
                    {% endif %}
                </p>

                <div class="mb-4">
                    <a href="{% host_url 'solution_detail' first_solution.slug host 'static' %}" class="btn btn-primary fw-bolder fs-6 px-4 py-2">
                        {% if LANGUAGE_CODE == 'ja'%}
                            さらに詳しく
                        {% else %}
                            Learn More
                        {% endif %}
                    </a>
                </div> 

                {% comment %} Child Solutions Links {% endcomment %}
                {% comment %} {% if first_solution.childrens.all %}
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        {% for child_solution in first_solution.childrens.all %}
                        {% if child_solution.public %}
                        <a href="{% host_url 'solution_detail' child_solution.slug host 'static' %}" class="btn btn-light-primary btn-sm fw-bold">
                            {% if LANGUAGE_CODE == 'ja'%}
                                {{child_solution.keyword_ja}}
                            {% else %}
                                {{child_solution.keyword}}
                            {% endif %}
                        </a>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endif %} {% endcomment %}
                {% endwith %}
                </div>
            </div>
        </div>

        <!-- Mobile Accordion Layout (hidden on desktop) -->
        <div class="mobile-accordion" style="display: none;">
            {% for solution in public_solutions|slice:"0:8" %}
            <div class="accordion-item border-2 rounded-2 mb-2" data-solution-id="{{ solution.id }}">
                <button class="accordion-header" type="button">
                    <span>
                        {% if LANGUAGE_CODE == 'ja'%}
                            {{solution.keyword_ja}}
                        {% else %}
                            {{solution.keyword}}
                        {% endif %}
                    </span>
                    <span class="accordion-icon">▼</span>
                </button>
                <div class="accordion-content">
                    <div class="accordion-content-inner bg-white">
                        <img
                        {% if LANGUAGE_CODE == 'ja' %}
                        src="{{solution.image_url_ja}}"
                        {% else %}
                        src="{{solution.image_url}}"
                        {% endif %}
                        alt="Demo Screenshot" class="w-100 rounded shadow-md max-w-full h-auto mb-4">

                        <p class="fs-6 tw-text-secondary">
                            {% if LANGUAGE_CODE == 'ja'%}
                                {{solution.body_ja|safe|remove_linebreaks|truncatechars:54}}
                            {% else %}
                                {{solution.body|safe|remove_linebreaks|truncatechars:70}}
                            {% endif %}
                        </p>
                        <div class="mb-4">
                            <a href="{% host_url 'solution_detail' solution.slug host 'static' %}" class="btn btn-primary fw-bolder fs-6 px-4 py-2">
                                {% if LANGUAGE_CODE == 'ja'%}
                                    さらに詳しく
                                {% else %}
                                    Learn More
                                {% endif %}
                            </a>
                        </div> 

                        {% comment %} Child Solutions Links for Mobile Accordion {% endcomment %}
                        {% comment %} {% if solution.childrens.all %}
                        <div class="mb-4">
                            <div class="d-flex flex-wrap gap-2">
                                {% for child_solution in solution.childrens.all %}
                                {% if child_solution.public %}
                                <a href="{% host_url 'solution_detail' child_solution.slug host 'static' %}" class="btn btn-light-primary btn-sm fw-bold">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                        {{child_solution.keyword_ja}}
                                    {% else %}
                                        {{child_solution.keyword}}
                                    {% endif %}
                                </a>
                                {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %} {% endcomment %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Wrapper using Bootstrap Grid for Mobile Layout -->
        {% comment %} <div class=" px-2 px-sm-3 px-lg-0"> <!-- Fluid container with padding adjustments -->
            <div class="row justify-content-center align-items-start w-100 mx-auto"> <!-- Main row -->
                <div class="col w-100 mx-auto px-0">
                    <div id="solution-cards-row" class="row container-xxl mx-lg-auto text-center row-eq-height justify-content-center gx-2 px-0">

                        {% for solution in public_solutions|slice:"0:12" %}
                            {# Use original column classes or adjust if needed #}
                            <div class="col-lg-3 col-md-3 col-sm-4 col-6 col-eq-height mb-5">
                                <div class="solution-box h-100" style="border-top: 4px solid #{{solution.color}};">
                                    <div class="p-4 px-2">
                                        <div class="rounded-3 w-50px h-50px justify-content-start justify-content-lg-center shadow mb-2 align-items-center d-flex app-box" style="background:#{% if solution.color %}{{solution.color}}{% else %}fff{% endif %}">
                                            <span class="w-50px svg-icon-1">
                                                {{ solution.icon|safe }}
                                            </span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div class="text-start fw-bolder fs-4 w-100">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    {{solution.keyword_ja}}
                                                    {% else %}
                                                    {{solution.keyword}}
                                                    {% endif %}
                                                {# Use original font sizes or adjust #}
                                                <div class="fw-bold fs-5 fs-lg-6 mb-4">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    {{solution.body_ja|safe|remove_linebreaks|truncatechars:54}}
                                                    {% else %}
                                                    {{solution.body|safe|remove_linebreaks|truncatechars:70}}
                                                    {% endif %}

                                                </div>
                                                <a href="{% host_url 'solution_detail' solution.slug host 'static' %}" class="btn solution-button text-white text-hover-white fw-bolder fs-6 px-4 py-2" style="background-color: #{{solution.color}}; position: relative; z-index: 10;" onclick="console.log('Solution link clicked:', this.href, 'Slug:', '{{solution.slug}}'); return true;">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                        さらに詳しく
                                                    {% else %}
                                                        Learn More
                                                    {% endif %}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div> <!-- End Inner Card Row -->
                </div> <!-- End Solution Cards Column -->

            </div> <!-- End Outer Row -->
        </div>  {% endcomment %}


    </div>
    
</div> 



{% comment %} <div class=" container-xxl mb-20">

    <div class="mb-10 text-center mx-auto" style="">
        <div class="mb-10">
            <h5 id="services" class="text-center fs-1 text-muted fw-boldest mb-3" style="letter-spacing:0.25px">    
                {% if LANGUAGE_CODE == 'ja'%}
                連携サービス
                {% else %}
                Integrations
                {% endif %}

            </h5>
            <div class="mb-5  fs-lg-1 fs-3 ">
                {% if LANGUAGE_CODE == 'ja'%}
                <div class="fw-boldest mb-3">
                    <span class="strong">ご利用中のツールとSankaを連携させることで、<br>
                        理想の業務フローを楽々構築。</span>
                </div>
                {% else %}
                <div class=" fw-bolder mb-3">
                    <span class="strong">Integrate with your favorite tools and <br> build ideal workflows for your back office.</span>
                </div>

                {% endif %} 
            </div>
        </div> 
        <div class="row w-100 mx-auto">
            {% for tag in integration_tags %}
            <div class="col-lg-3 col-6 mb-10">
                <div class="border-bottom-1 border-bottom pb-5 mb-5 fs-3 fw-bolder text-dark">
        
                    {% if LANGUAGE_CODE == 'ja'%}
                    {{tag.name_ja}}
                    {% else %}
                    {{tag.name}}
                    {% endif %}
                </div>
                <div class="row">
                    {% with tag.integrations.all|sort_order as integrations %}
                        {% for integration in integrations|slice:":6" %}
                        <div class="col-lg-4 col-6">
                            <div class="d-flex align-items-center mb-3">
                                {% if integration.image_file %}
                                <div class="mx-auto">
                                    <img loading="lazy" src="{{integration.image_file.url}}" class="border rounded-2 w-60px h-60px w-md-70px h-md-70px" alt="{{integration.title}}">
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% endwith %}
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center">
            {% if LANGUAGE_CODE == 'ja'%}
                <a target="blank" class="text-center fw-bolder text-center fs-3 mx-2 btn btn-dark" href="https://help.sanka.com/ja/articles/9-%E5%A4%96%E9%83%A8%E9%80%A3%E6%90%BA%E3%82%B5%E3%83%BC%E3%83%93%E3%82%B9%E4%B8%80%E8%A6%A7">    
                すべての連携サービス
                {% else %}
                <a target="blank" class="text-center fw-bolder text-center fs-3 mx-2 btn btn-dark" href="https://help.sanka.com/en/articles/9-integrations">    
                All Integrations
                {% endif %}
            </a>
        </div>
    </div>
</div> {% endcomment %}

{% comment %} <div class="bg-gray-100 mb-20 py-20">
    <div class="container-xxl  mb-10 text-center mx-auto" style="">
        <div class="mb-10">
            {% for solution in integration_solutions|slice:"0:1" %}
            <div class="solution-box h-100">
                <div class="p-4 px-2">
                    <div class="mx-auto rounded-3 w-75px h-75px justify-content-start justify-content-lg-center shadow mb-2 align-items-center d-flex app-box" style="background:#{% if solution.color %}{{solution.color}}{% else %}fff{% endif %}">
                        <span class="w-100px svg-icon-4x">
                            {{ solution.icon|safe }}
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
            <h5 id="services" class="text-center fs-1 fw-boldest mb-3" style="letter-spacing:0.25px">    
                {% if LANGUAGE_CODE == 'ja'%}
                ERP for HubSpot 
                {% else %}
                ERP for HubSpot
                {% endif %}

            </h5>
            <div class="mb-5  fs-lg-1 fs-3 ">
                {% if LANGUAGE_CODE == 'ja'%}
                <div class="fw-boldest mb-3">
                    <span class="strong">HubSpot CRMとSanka ERPを組み合わせて、<br>売上改善とコスト削減を同時に実現。
                </div>
                {% else %}
                <div class=" fw-bolder mb-3">
                    <span class="strong">
                        Combine HubSpot's MA and CRM features with Sanka ERP to achieve<br>
                        sales growth and cost reduction simultaneously.
                    </span>
                </div>

                {% endif %} 
            </div>
        </div> 
        <div>
            <div class="container-fluid px-2 px-sm-3 px-lg-0"> <!-- Fluid container with padding adjustments -->
                <div class="row justify-content-center align-items-start w-100 mx-auto"> <!-- Main row -->
                    <div class="col w-100 mx-auto">    
                        <div class="row justify-content-center d-flex ">
                            {% for service in services %}
                            <div class="col-md-4 px-5 order-1 mb-3">
                                {% if service.code_ja %}
                                    {{service.code_ja|safe}}
                                {% else %}
                                    {{service.code|safe}}
                                {% endif %}

                                <div class="mb-3">
                                    <div class="fs-2 fs-lg-3 fw-bolder ">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        {{service.title_ja}}
                                        {% else %}
                                        {{service.title}}
                                        {% endif %}
                                    </div>
                                    <div class="fs-3 fs-lg-4 fw-bold ">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        {{service.description_ja|safe}}
                                        {% else %}
                                        {{service.description|safe}}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}                    

                        </div>
                    </div> <!-- End Solution Cards Column -->
    
                </div> <!-- End Outer Row -->
            </div> <!-- End Grid Wrapper Container -->
    
        </div>
    </div>
</div> {% endcomment %}

{% include "data/static/referrals.html" %}


{% comment %} {% include "data/static/onboard.html" %} {% endcomment %}

{% comment %} {% include "data/static/static-pricing.html" %} {% endcomment %}

{% include "data/static/why-sanka.html" %}

{% include 'data/static/cta-product.html' %}
{% endblock %}

{% block js %}



<script type="text/javascript">
    var swiper = new Swiper(".mySwiper", {
        slidesPerView: "auto",
        centeredSlides: true,
        spaceBetween: 30,
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      });
</script>

<script>

    {% if LANGUAGE_CODE == 'ja'%}
    var typed = new Typed("#kt_typedjs_example_1", {
        strings: [
        "生産性を最大化する",
        "ダントツ使いやすい",
        "コストを削減する",
        "柔軟性の高い",
        "コスパが最高な",
        "業務を一元化する",
        "バックオフィス改革",
        "業界に特化する",
        "事業成長を支援する",
        "エクセルがいらない",
        ],
        typeSpeed: 80,
        loop: true,
        loopCount: Infinity,
        showCursor: false,

    });
    {% else %}
    var typed = new Typed("#kt_typedjs_example_1", {
        strings: [
        "Productivity-boosting",
        "Cost-cutting",
        "Easy-to-use",
        "Highly-flexible",
        "Cost-effective",
        "All-in-one",
        "Back-office-friendly",
        "Industry-specific",
        "Growth-focused",
        "No-more-Excel",
        ],
        typeSpeed: 70,
        loop: true,
        loopCount: Infinity,
        showCursor: false,

    });

    {% endif %} 
</script>

<script>
    tabControl();

    var resizeTimer;
    $(window).on('resize', function(e) {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(function() {
        tabControl();
      }, 250);
    });
    
    function tabControl() {
      var tabs = $('.tabbed-content').find('.tabs');
      if(tabs.is(':visible')) {
        tabs.find('a').on('click', function(event) {
          event.preventDefault();
          var target = $(this).attr('href'),
              tabs = $(this).parents('.tabs'),
              buttons = tabs.find('a'),
              item = tabs.parents('.tabbed-content').find('.item');
          buttons.removeClass('active');
          item.removeClass('active');
          $(this).addClass('active');
          $(target).addClass('active');
        });
      } else {
        $('.item').on('click', function() {
          var container = $(this).parents('.tabbed-content'),
              currId = $(this).attr('id'),
              items = container.find('.item');
          container.find('.tabs a').removeClass('active');
          items.removeClass('active');
          $(this).addClass('active');
          container.find('.tabs a[href$="#'+ currId +'"]').addClass('active');
        });
      } 
    }
</script>


{% comment %} rentrax {% endcomment %}
<script type="text/javascript">
    (function(callback){
    var script = document.createElement("script");
    script.type = "text/javascript";
    script.src = "https://www.rentracks.jp/js/itp/rt.track.js?t=" + (new Date()).getTime();
    if ( script.readyState ) {
    script.onreadystatechange = function() {
    if ( script.readyState === "loaded" || script.readyState === "complete" ) {
    script.onreadystatechange = null;
    callback();
    }
    };
    } else {
    script.onload = function() {
    callback();
    };
    }
    document.getElementsByTagName("head")[0].appendChild(script);
    }(function(){}));
    </script>


    <script>
        // Function to debounce calls (improves resize performance)
        function debounce(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        };
    
        // Function to match the vertical arrow height to the solution cards row height
        function matchArrowHeight() {
            const verticalArrow = document.getElementById('vertical-arrow');
            const cardsRow = document.getElementById('solution-cards-row');
            const lgBreakpoint = 992; // Bootstrap 5 default lg breakpoint
    
            if (verticalArrow && cardsRow) {
                // Check if we are below the large breakpoint
                if (window.innerWidth < lgBreakpoint) {
                    // Get the height of the cards row
                    const cardsRowHeight = cardsRow.offsetHeight;
                    // Set the arrow's height, subtracting 30px from the total height
                    verticalArrow.style.height = `${cardsRowHeight - 30}px`;
                } else {
                    // On large screens, remove the inline style to let CSS handle it
                    verticalArrow.style.height = '';
                }
            }
        }
    
        // Debounced version of the height matching function
        const debouncedMatchHeight = debounce(matchArrowHeight, 150); // Adjust delay (ms) as needed
    
        // Run on initial load (making sure DOM is ready)
        document.addEventListener('DOMContentLoaded', function() {
            matchArrowHeight();
        });
    
        // Run on window resize
        window.addEventListener('resize', debouncedMatchHeight);
            
    </script>


    <script>
    document.addEventListener('DOMContentLoaded', function() {

        // Add a small delay to ensure everything is rendered
        setTimeout(() => {
            let menuItems = document.querySelectorAll('#menu .menu-button');
            const content = document.getElementById('content');

            // Fallback: try different selectors if no buttons found
            if (menuItems.length === 0) {
                menuItems = document.querySelectorAll('button[data-content^="solution-"]');
                if (menuItems.length === 0) {
                    menuItems = document.querySelectorAll('ul#menu button');
                }
            }

            // Dynamic pages object built from solutions data
            const pages = {
                {% for solution in public_solutions|slice:"0:8" %}
                'solution-{{ solution.id }}': `

                {% if LANGUAGE_CODE == 'ja'%}
                <img src="{{solution.image_url_ja}}" alt="Demo Screenshot" class="w-100 rounded shadow-md border max-w-full h-auto mb-4">
                {% else %}
                <img src="{{solution.image_url}}" alt="Demo Screenshot" class="w-100 rounded shadow-md border max-w-full h-auto mb-4">
                {% endif %}


                <p class="fs-6 tw-text-secondary">
                    {% if LANGUAGE_CODE == 'ja'%}
                        {{solution.body_ja|safe|remove_linebreaks}}
                    {% else %}
                        {{solution.body|safe|remove_linebreaks}}
                    {% endif %}
                </p>
                <div class="mb-4">
                    <a href="{% host_url 'solution_detail' solution.slug host 'static' %}" class="btn btn-primary fw-bolder fs-6 px-4 py-2">
                        {% if LANGUAGE_CODE == 'ja'%}
                            さらに詳しく
                        {% else %}
                            Learn More
                        {% endif %}
                    </a>
                </div> 

                {% comment %} Child Solutions Links for JavaScript 
                {% if solution.childrens.all %}
                <div class="mb-4">
                    <div class="d-flex flex-wrap gap-2">
                        {% for child_solution in solution.childrens.all %}
                        {% if child_solution.public %}
                        <a href="{% host_url 'solution_detail' child_solution.slug host 'static' %}" class="btn btn-light-primary btn-sm fw-bold">
                            {% if LANGUAGE_CODE == 'ja'%}
                                {{child_solution.keyword_ja|escapejs}}
                            {% else %}
                                {{child_solution.keyword|escapejs}}
                            {% endif %}
                        </a>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endif %}{% endcomment %}
                `{% if not forloop.last %},{% endif %}
                {% endfor %}
            };

            if (menuItems.length > 0 && content) {
                // Initialize: ensure first item has the border and is marked as active
                if (menuItems.length > 0) {
                    const firstBtn = menuItems[0];
                    firstBtn.classList.add('font-bold', 'bg-gray-100');
                    const firstInnerDiv = firstBtn.querySelector('div > div');
                    if (firstInnerDiv) {
                        firstInnerDiv.classList.add('border-start', 'border-primary', 'border-2');
                    }
                }

                menuItems.forEach((btn, index) => {
                    btn.addEventListener('click', () => {
                        // Remove active classes from all menu buttons
                        menuItems.forEach((i, idx) => {
                            i.classList.remove('font-bold', 'bg-gray-100');
                            // Remove border classes from the inner div
                            const innerDiv = i.querySelector('div > div');
                            if (innerDiv) {
                                innerDiv.classList.remove('border-start', 'border-primary', 'border-2');
                            }
                        });

                        // Add active classes to clicked button
                        btn.classList.add('font-bold', 'bg-gray-100');
                        // Add border classes to the inner div of clicked button
                        const clickedInnerDiv = btn.querySelector('div > div');
                        if (clickedInnerDiv) {
                            clickedInnerDiv.classList.add('border-start', 'border-primary', 'border-2');
                        }

                        const key = btn.getAttribute('data-content');

                        if (pages[key]) {
                            console.log('📄 Page content length:', pages[key].length);

                            // Add fade animation for desktop
                            if (window.innerWidth >= 992) {
                                // Start fade out
                                content.classList.add('fade-out');

                                // After fade out completes, update content and fade in
                                setTimeout(() => {
                                    content.innerHTML = pages[key];
                                    content.classList.remove('fade-out');
                                    content.classList.add('fade-in');

                                    // Remove fade-in class after animation completes
                                    setTimeout(() => {
                                        content.classList.remove('fade-in');
                                    }, 300);
                                }, 150); // Half of the transition duration
                            } else {
                                // Mobile: no animation, direct update
                                content.innerHTML = pages[key];
                            }

                            console.log('✅ Content updated successfully');
                        } else {
                            console.log('❌ No content found for key:', key);
                            console.log('❌ Available keys:', Object.keys(pages));
                        }
                    });
                });
            } else {
                console.log('❌ Menu setup failed - menuItems:', menuItems.length, 'content:', content);
            }
        }, 100); // End setTimeout
    }); // End DOMContentLoaded
    </script>

    <script>
    // Mobile Accordion Functionality
    document.addEventListener('DOMContentLoaded', function() {
        const accordionHeaders = document.querySelectorAll('.accordion-header');
        let hasUserInteracted = false; // Track if user has clicked any accordion
        let isInitialLoad = true; // Track if this is the initial page load

        // Open first accordion item by default on mobile (only on initial load)
        function initializeAccordion() {
            if (window.innerWidth < 992 && accordionHeaders.length > 0 && isInitialLoad && !hasUserInteracted) {
                const firstHeader = accordionHeaders[0];
                const firstContent = firstHeader.nextElementSibling;

                firstHeader.classList.add('active');
                firstContent.classList.add('active');
                console.log('📱 First accordion item opened by default on initial load');
            }
        }

        // Handle accordion click events
        accordionHeaders.forEach((header, index) => {
            // Debug: Log computed styles
            const computedStyle = window.getComputedStyle(header);

            header.addEventListener('click', function() {
                // Mark that user has interacted with accordion
                hasUserInteracted = true;

                const content = this.nextElementSibling;
                const isCurrentlyActive = this.classList.contains('active');

                // Close all accordion items
                accordionHeaders.forEach((otherHeader) => {
                    const otherContent = otherHeader.nextElementSibling;
                    otherHeader.classList.remove('active');
                    otherContent.classList.remove('active');
                });

                // If the clicked item wasn't active, open it
                if (!isCurrentlyActive) {
                    this.classList.add('active');
                    content.classList.add('active');
                } else {
                    console.log('📝 Accordion item closed (was already active)');
                }
            });
        });

        // Initialize accordion on page load
        initializeAccordion();
        // Mark initial load as complete after initialization
        setTimeout(() => {
            isInitialLoad = false;
        }, 100);

        // Re-initialize on window resize (but respect user interaction)
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                // Only initialize if user hasn't interacted and it's not initial load anymore
                // This prevents auto-opening on resize
                console.log('📱 Window resized, but not auto-opening first accordion (user interaction:', hasUserInteracted, ')');
            }, 250);
        });
    });
    </script>

{% endblock %}

