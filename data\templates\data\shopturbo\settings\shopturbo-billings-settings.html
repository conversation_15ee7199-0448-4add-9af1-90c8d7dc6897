{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}


{% if setting_type == "estimates" %}
<div class="pb-10 mb-5">
    <div id="estimate_setting_section">
        <form method="POST" action="{% host_url 'commerce_settings' host 'app' %}" class="" id="setting_form_estimate" enctype="multipart/form-data">
            <div class="mb-10" 
                hx-get="{% host_url 'properties' host 'app' %}"
                hx-vals='{"page_group_type": "estimates"}'
                hx-trigger="load"
                hx-target="this"
                hx-swap="innerHTML"
                >
            </div>

            {% comment %} ASSOCIATION LABEL SETTINGS {% endcomment %}
            <div
                hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                hx-vals='{"page_group_type": "estimates"}'
                hx-trigger="load"
            ></div>

            {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

            <div id='estimate_setting' class='mb-10'
                hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                hx-swap="innerHTML" 
                hx-select="#estimate_setting_section" 
                hx-vals='{"setting_type": "estimate"}'
                hx-trigger="load"
                hx-target="this">
            </div>

            {% comment %} Shipping Fee {% endcomment %}
            <div class="mb-10">
                <div class="fv-rowd-flex flex-column">
                    <div class="mb-5" id="shipping_fee_section">
                        <label class="fs-4 fw-bold mb-2">
                            <span class=""> 
                                {% if LANGUAGE_CODE == 'ja'%}
                                送料
                                {% else %}
                                Shipping Cost
                                {% endif %}
                                </h2>
                            </span>
                            <a href='javascript:;' onclick='add_shippings(this);'>
                                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24"/>
                                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                    </g>
                                </svg>
                            </a>
                        </label>

                        {% for ShopTurboShippingCost in ShopTurboShippingCosts %}
                        <div class="mb-5">

                            <input type="hidden" name="shipping_field_id" value="{{ShopTurboShippingCost.id}}"></input>

                            <div class="d-flex ">
                                <div class="input-group flex-nowrap align-items-strecth">  
                                    <div class="input-prehead">
                                        <select class="{% include 'data/utility/select-form.html' %} input-prehead select2-this" name="shipping_field_number_format" data-placeholder="value">
                                            
                                        {% if workspace.currencies %}
                                        <optgroup
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            label="デフォルト通貨"
                                            {% else %}
                                            label="Default Currency"
                                            {% endif %}
                                        >
                                            {% for currency in workspace.currencies|string_list_to_list %}
                                                {% if forloop.counter == 1 %}
                                                    <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                {% elif ShopTurboShippingCost.number_format == currency %}
                                                    <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                {% else %}
                                                    <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                                {% endif %}
                                            {% endfor %}

                                        </optgroup>
                                        {% endif %}

                                        <optgroup
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            label="すべての通貨"
                                            {% else %}
                                            label="All Currencies"
                                            {% endif %}
                                        >
                                        {% include 'data/partials/money-currency.html' with obj=ShopTurboShippingCost.number_format %}
                                        </optgroup>
                                        </select>
                                    </div>

                                    <input class="form-control w-50 h-40px" name="shipping_field_value"
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        placeholder="送料の値" 
                                        {% else %}
                                        placeholder="Shipping Price Value" 
                                        {% endif %}
                                        
                                        {% if ShopTurboShippingCost.value %}
                                        value = '{{ShopTurboShippingCost.value}}'
                                        {% endif %}
                                    />
                                    
                                    <input class="form-control d-flex w-25 h-40px" name="shipping_field_name"
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        placeholder="送料名" 
                                        {% else %}
                                        placeholder="Shipping Fee Name" 
                                        {% endif %}

                                        {% if ShopTurboShippingCost.name %}
                                        value = '{{ShopTurboShippingCost.name}}'
                                        {% endif %}
                                    />
                                </div>
                                <div class="input-group-append">
                                    <button class="btn btn-danger btn-sm ms-1" onclick='delete_shipping(this);' type="button">X</button>
                                </div> 
                            </div>
                        </div>
                        {% endfor %}
                        
                    </div>
                </div>
                
            </div>
        </form>





        <div id="estimate-logo-uploads">
            <div class="mb-5" id="">
                <div class="d-flex justify-content-between">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span >
                            {% if LANGUAGE_CODE == 'ja' %}
                            企業ロゴ
                            {% else %}
                            Company Logo
                            {% endif %}
                        </span>

                    </label>

                    <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"

                    onclick="document.getElementById('company-logo-column-button').click();"

                    >
                        <span class="fs-7 ps-1 fw-bolder">
                            {% if LANGUAGE_CODE == 'ja' %}
                            他のオブジェクトに適用
                            {% else %}
                            Apply To Other Objects
                            {% endif %}
                        </span>
                    </button>
                </div>
                
                <span
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "estimate_logo", "get-image-file":"True", "app_settings":"manage_app"}'
                    hx-trigger="load"
                    hx-target="this">
                </span>

            </div> 
        </div>

        <div id="estimate-stamp-uploads">
            <div class="mb-5" id="">
                <div class="d-flex justify-content-between">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span >
                            {% if LANGUAGE_CODE == 'ja' %}
                            社印
                            {% else %}
                            Company Signature
                            {% endif %}
                        </span>
                        
                    </label>

                    <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"

                    onclick="document.getElementById('company-signature-column-button').click();"

                    >
                        <span class="fs-7 ps-1 fw-bolder">
                            {% if LANGUAGE_CODE == 'ja' %}
                            他のオブジェクトに適用
                            {% else %}
                            Apply To Other Objects
                            {% endif %}
                        </span>
                    </button>
                </div>

                <span
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "estimate_stamp", "get-image-file":"True", "app_settings":"manage_app"}'
                    hx-trigger="load"
                    hx-target="this">
                </span>
            </div> 
        </div>

        <div id="submit-custom-button">
            <button form="setting_form_estimate" name="post" type="submit" class="btn btn-dark create-custom-button">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </div>
    </div>
</div>

{% elif setting_type == 'invoices'%}
<div class="pb-10 mb-5">
    <div class="">
        <form method="POST" action="{% host_url 'commerce_settings' host 'app' %}" class="" id="setting_form_invoice" enctype="multipart/form-data">
            <div class="mb-10" 
                hx-get="{% host_url 'properties' host 'app' %}"
                hx-vals='{"page_group_type": "invoices"}'
                hx-trigger="load"
                hx-target="this"
                hx-swap="innerHTML"
                >
            </div>

            {% comment %} ASSOCIATION LABEL SETTINGS {% endcomment %}
            <div
                hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                hx-vals='{"page_group_type": "invoices"}'
                hx-trigger="load"
            ></div>

            {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

            <div id="invoice_setting_section">
                <div id='invoice_setting' class='mb-10'
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "invoice"}'
                    hx-select="#invoice_setting_section" 
                    hx-trigger="load"
                    hx-target="this">
                </div>
            </div>

            <div class="mb-10">
                <div class="fv-rowd-flex flex-column">
                    <div class="mb-5" id="shipping_fee_section">
                        <label class="fs-4 fw-bold mb-2">
                            <span class=""> 
                                {% if LANGUAGE_CODE == 'ja'%}
                                送料
                                {% else %}
                                Shipping Cost
                                {% endif %}
                                </h2>
                            </span>
                            <a href='javascript:;' onclick='add_shippings(this);'>
                                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24"/>
                                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                    </g>
                                </svg>
                            </a>
                        </label>

                        {% for ShopTurboShippingCost in ShopTurboShippingCosts %}
                        <div class="mb-5">

                            <input type="hidden" name="shipping_field_id" value="{{ShopTurboShippingCost.id}}"></input>

                            <div class="d-flex ">
                                <div class="input-group flex-nowrap align-items-strecth">  
                                    <div class="input-prehead">
                                        <select class="{% include 'data/utility/select-form.html' %} input-prehead select2-this" name="shipping_field_number_format" data-placeholder="value">
                                            
                                        {% if workspace.currencies %}
                                        <optgroup
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            label="デフォルト通貨"
                                            {% else %}
                                            label="Default Currency"
                                            {% endif %}
                                        >
                                            {% for currency in workspace.currencies|string_list_to_list %}
                                                {% if forloop.counter == 1 %}
                                                    <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                {% elif ShopTurboShippingCost.number_format == currency %}
                                                    <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                {% else %}
                                                    <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                                {% endif %}
                                            {% endfor %}

                                        </optgroup>
                                        {% endif %}

                                        <optgroup
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            label="すべての通貨"
                                            {% else %}
                                            label="All Currencies"
                                            {% endif %}
                                        >
                                        {% include 'data/partials/money-currency.html' with obj=ShopTurboShippingCost.number_format %}
                                        </optgroup>
                                        </select>
                                    </div>

                                    <input class="form-control w-50 h-40px" name="shipping_field_value"
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        placeholder="送料の値" 
                                        {% else %}
                                        placeholder="Shipping Price Value" 
                                        {% endif %}
                                        
                                        {% if ShopTurboShippingCost.value %}
                                        value = '{{ShopTurboShippingCost.value}}'
                                        {% endif %}
                                    />
                                    
                                    <input class="form-control d-flex w-25 h-40px" name="shipping_field_name"
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        placeholder="送料名" 
                                        {% else %}
                                        placeholder="Shipping Fee Name" 
                                        {% endif %}

                                        {% if ShopTurboShippingCost.name %}
                                        value = '{{ShopTurboShippingCost.name}}'
                                        {% endif %}
                                    />
                                </div>
                                <div class="input-group-append">
                                    <button class="btn btn-danger btn-sm ms-1" onclick='delete_shipping(this);' type="button">X</button>
                                </div> 
                            </div>
                        </div>
                        {% endfor %}
                        
                    </div>
                </div>
                
            </div>

        </form>
        <div id="invoice-logo-uploads">
            <div class="mb-5" id="">
                <div class="d-flex justify-content-between">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span >
                            {% if LANGUAGE_CODE == 'ja' %}
                            企業ロゴ
                            {% else %}
                            Company Logo
                            {% endif %}
                        </span>

                    </label>

                    <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"

                    onclick="document.getElementById('company-logo-column-button').click();"

                    >
                        <span class="fs-7 ps-1 fw-bolder">
                            {% if LANGUAGE_CODE == 'ja' %}
                            他のオブジェクトに適用
                            {% else %}
                            Apply To Other Objects
                            {% endif %}
                        </span>
                    </button>
                </div>
                
                <span
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "invoice_logo", "get-image-file":"True", "app_settings":"manage_app"}'
                    hx-trigger="load"
                    hx-target="this">
                </span>
            </div> 
        </div>

        <div id="invoice-stamp-uploads">
            <div class="mb-5" id="">
                <div class="d-flex justify-content-between">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span >
                            {% if LANGUAGE_CODE == 'ja' %}
                            社印
                            {% else %}
                            Company Signature
                            {% endif %}
                        </span>
                        
                    </label>

                    <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"

                    onclick="document.getElementById('company-signature-column-button').click();"

                    >
                        <span class="fs-7 ps-1 fw-bolder">
                            {% if LANGUAGE_CODE == 'ja' %}
                            他のオブジェクトに適用
                            {% else %}
                            Apply To Other Objects
                            {% endif %}
                        </span>
                    </button>
                </div>
                
                <span
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "invoice_stamp", "get-image-file":"True", "app_settings":"manage_app"}'
                    hx-trigger="load"
                    hx-target="this">
                </span>
            </div> 
        </div>

        <div id="submit-custom-button">
            <button form="setting_form_invoice" name="post" type="submit" class="btn btn-dark create-custom-button">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </div>
    </div>
</div>

{% elif setting_type == 'delivery_slips'%}
<div class="pb-10 mb-5">
    <div class="">
        <form method="POST" action="{% host_url 'commerce_settings' host 'app' %}" class="" id="setting_form_delivery_note" enctype="multipart/form-data">
            <div id="delivery_slip_setting_section">
                <div class="mb-10" 
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "delivery_slips"}'
                    hx-trigger="load"
                    hx-target="this"
                    hx-swap="innerHTML"
                    >
                </div>

                {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

                <div id='delivery_slip_setting' class='mb-10'
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "delivery_slip"}'
                    hx-select="#delivery_slip_setting_section" 
                    hx-trigger="load"
                    hx-target="this">
                </div>

                <div class="mb-10">
                    <div class="fv-rowd-flex flex-column">
                        <div class="mb-5" id="shipping_fee_section">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    送料
                                    {% else %}
                                    Shipping Cost
                                    {% endif %}
                                    </h2>
                                </span>
                                <a href='javascript:;' onclick='add_shippings(this);'>
                                    <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <polygon points="0 0 24 0 24 24 0 24"/>
                                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                        </g>
                                    </svg>
                                </a>
                            </label>

                            {% for ShopTurboShippingCost in ShopTurboShippingCosts %}
                            <div class="mb-5">

                                <input type="hidden" name="shipping_field_id" value="{{ShopTurboShippingCost.id}}"></input>

                                <div class="d-flex ">
                                    <div class="input-group flex-nowrap align-items-strecth">  
                                        <div class="input-prehead">
                                            <select class="{% include 'data/utility/select-form.html' %} input-prehead select2-this" name="shipping_field_number_format" data-placeholder="value">
                                                
                                            {% if workspace.currencies %}
                                            <optgroup
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                label="デフォルト通貨"
                                                {% else %}
                                                label="Default Currency"
                                                {% endif %}
                                            >
                                                {% for currency in workspace.currencies|string_list_to_list %}
                                                    {% if forloop.counter == 1 %}
                                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                    {% elif ShopTurboShippingCost.number_format == currency %}
                                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                    {% else %}
                                                        <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                                    {% endif %}
                                                {% endfor %}

                                            </optgroup>
                                            {% endif %}

                                            <optgroup
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                label="すべての通貨"
                                                {% else %}
                                                label="All Currencies"
                                                {% endif %}
                                            >
                                            {% include 'data/partials/money-currency.html' with obj=ShopTurboShippingCost.number_format %}
                                            </optgroup>
                                            </select>
                                        </div>

                                        <input class="form-control w-50 h-40px" name="shipping_field_value"
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            placeholder="送料の値" 
                                            {% else %}
                                            placeholder="Shipping Price Value" 
                                            {% endif %}
                                            
                                            {% if ShopTurboShippingCost.value %}
                                            value = '{{ShopTurboShippingCost.value}}'
                                            {% endif %}
                                        />
                                        
                                        <input class="form-control d-flex w-25 h-40px" name="shipping_field_name"
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            placeholder="送料名" 
                                            {% else %}
                                            placeholder="Shipping Fee Name" 
                                            {% endif %}

                                            {% if ShopTurboShippingCost.name %}
                                            value = '{{ShopTurboShippingCost.name}}'
                                            {% endif %}
                                        />
                                    </div>
                                    <div class="input-group-append">
                                        <button class="btn btn-danger btn-sm ms-1" onclick='delete_shipping(this);' type="button">X</button>
                                    </div> 
                                </div>
                            </div>
                            {% endfor %}
                            
                        </div>
                    </div>
                    
                </div>

                
            </form>
            <div id="delivery-slip-logo-uploads">
                <div class="mb-5" id="">
                    <div class="d-flex justify-content-between">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span >
                                {% if LANGUAGE_CODE == 'ja' %}
                                企業ロゴ
                                {% else %}
                                Company Logo
                                {% endif %}
                            </span>
    
                        </label>
    
                        <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"
    
                        onclick="document.getElementById('company-logo-column-button').click();"
    
                        >
                            <span class="fs-7 ps-1 fw-bolder">
                                {% if LANGUAGE_CODE == 'ja' %}
                                他のオブジェクトに適用
                                {% else %}
                                Apply To Other Objects
                                {% endif %}
                            </span>
                        </button>
                    </div>
                    
                    <span
                        hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                        hx-swap="innerHTML" 
                        hx-vals='{"setting_type": "delivery_slip_logo", "get-image-file":"True", "app_settings":"manage_app"}'
                        hx-trigger="load"
                        hx-target="this">
                    </span>
                </div> 
            </div>

            <div id="delivery-slip-stamp-uploads">
                <div class="mb-5" id="">
                    <div class="d-flex justify-content-between">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span >
                                {% if LANGUAGE_CODE == 'ja' %}
                                社印
                                {% else %}
                                Company Signature
                                {% endif %}
                            </span>
                            
                        </label>
    
                        <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"
    
                        onclick="document.getElementById('company-signature-column-button').click();"
    
                        >
                            <span class="fs-7 ps-1 fw-bolder">
                                {% if LANGUAGE_CODE == 'ja' %}
                                他のオブジェクトに適用
                                {% else %}
                                Apply To Other Objects
                                {% endif %}
                            </span>
                        </button>
                    </div>
                    
                    <span
                        hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                        hx-swap="innerHTML" 
                        hx-vals='{"setting_type": "delivery_slip_stamp", "get-image-file":"True", "app_settings":"manage_app"}'
                        hx-trigger="load"
                        hx-target="this">
                    </span>
                </div> 
            </div>

            <div id="submit-custom-button">
                <button form="setting_form_delivery_note" name="post" type="submit" class="btn btn-dark create-custom-button">
                    {% if LANGUAGE_CODE == 'ja'%}
                    更新
                    {% else %}
                    Update
                    {% endif %}
                </button>
            </div>
        </div>
    </div>
</div>


{% elif setting_type == 'receipts'%}
<div class="pb-10 mb-5">
    <div class="">
        <div id="receipt_setting_section">
            <form method="POST" action="{% host_url 'commerce_settings' host 'app' %}" class="" id="setting_form_receipt" enctype="multipart/form-data">
                <div class="mb-10" 
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "receipts"}'
                    hx-trigger="load"
                    hx-target="this"
                    hx-swap="innerHTML"
                    >
                </div>

                {% comment %} ASSOCIATION LABEL SETTINGS {% endcomment %}
                <div
                    hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                    hx-vals='{"page_group_type": "receipts"}'
                    hx-trigger="load"
                ></div>

                {% include 'data/account/workspace/settings/workspace-object-manager.html' %}
                
                <div id='receipt_setting' class='mb-10'
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "receipt"}'
                    hx-select="#receipt_setting_section" 
                    hx-trigger="load"
                    hx-target="this">
                </div>
                
                <div class="mb-10">
                    <div class="fv-rowd-flex flex-column">
                        <div class="mb-5" id="shipping_fee_section">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    送料
                                    {% else %}
                                    Shipping Cost recipt
                                    {% endif %}
                                    </h2>
                                </span>
                                <a href='javascript:;' onclick='add_shippings(this);'>
                                    <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <polygon points="0 0 24 0 24 24 0 24"/>
                                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                        </g>
                                    </svg>
                                </a>
                            </label>

                            {% for ShopTurboShippingCost in ShopTurboShippingCosts %}
                            <div class="mb-5">

                                <input type="hidden" name="shipping_field_id" value="{{ShopTurboShippingCost.id}}"></input>

                                <div class="d-flex ">
                                    <div class="input-group flex-nowrap align-items-strecth">  
                                        <div class="input-prehead">
                                            <select class="{% include 'data/utility/select-form.html' %} input-prehead select2-this" name="shipping_field_number_format" data-placeholder="value">
                                                
                                            {% if workspace.currencies %}
                                            <optgroup
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                label="デフォルト通貨"
                                                {% else %}
                                                label="Default Currency"
                                                {% endif %}
                                            >
                                                {% for currency in workspace.currencies|string_list_to_list %}
                                                    {% if forloop.counter == 1 %}
                                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                    {% elif ShopTurboShippingCost.number_format == currency %}
                                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                    {% else %}
                                                        <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                                    {% endif %}
                                                {% endfor %}

                                            </optgroup>
                                            {% endif %}

                                            <optgroup
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                label="すべての通貨"
                                                {% else %}
                                                label="All Currencies"
                                                {% endif %}
                                            >
                                            {% include 'data/partials/money-currency.html' with obj=ShopTurboShippingCost.number_format %}
                                            </optgroup>
                                            </select>
                                        </div>

                                        <input class="form-control w-50 h-40px" name="shipping_field_value"
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            placeholder="送料の値" 
                                            {% else %}
                                            placeholder="Shipping Price Value" 
                                            {% endif %}
                                            
                                            {% if ShopTurboShippingCost.value %}
                                            value = '{{ShopTurboShippingCost.value}}'
                                            {% endif %}
                                        />
                                        
                                        <input class="form-control d-flex w-25 h-40px" name="shipping_field_name"
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            placeholder="送料名" 
                                            {% else %}
                                            placeholder="Shipping Fee Name" 
                                            {% endif %}

                                            {% if ShopTurboShippingCost.name %}
                                            value = '{{ShopTurboShippingCost.name}}'
                                            {% endif %}
                                        />
                                    </div>
                                    <div class="input-group-append">
                                        <button class="btn btn-danger btn-sm ms-1" onclick='delete_shipping(this);' type="button">X</button>
                                    </div> 
                                </div>
                            </div>
                            {% endfor %}
                            
                        </div>
                    </div>
                    
                </div>

            </form>
            <div id="receipt-logo-uploads">
                <div class="mb-5" id="">
                    <div class="d-flex justify-content-between">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span >
                                {% if LANGUAGE_CODE == 'ja' %}
                                企業ロゴ
                                {% else %}
                                Company Logo
                                {% endif %}
                            </span>
    
                        </label>
    
                        <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"
    
                        onclick="document.getElementById('company-logo-column-button').click();"
    
                        >
                            <span class="fs-7 ps-1 fw-bolder">
                                {% if LANGUAGE_CODE == 'ja' %}
                                他のオブジェクトに適用
                                {% else %}
                                Apply To Other Objects
                                {% endif %}
                            </span>
                        </button>
                    </div>
                    <span
                        hx-post="{% host_url 'commerce_settings' host 'app' %}" 
                        hx-swap="innerHTML" 
                        hx-vals='{"setting_type": "receipt_logo", "get-image-file":"True", "app_settings":"manage_app"}'
                        hx-trigger="load"
                        hx-target="this">
                    </span>
                </div> 
            </div>


            <div id="invoice-stamp-uploads">
                <div class="mb-5" id="">
                    <div class="d-flex justify-content-between">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span >
                                {% if LANGUAGE_CODE == 'ja' %}
                                社印
                                {% else %}
                                Company Signature
                                {% endif %}
                            </span>
                            
                        </label>
    
                        <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"
    
                        onclick="document.getElementById('company-signature-column-button').click();"
    
                        >
                            <span class="fs-7 ps-1 fw-bolder">
                                {% if LANGUAGE_CODE == 'ja' %}
                                他のオブジェクトに適用
                                {% else %}
                                Apply To Other Objects
                                {% endif %}
                            </span>
                        </button>
                    </div>
                    
                    <span
                        hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                        hx-swap="innerHTML" 
                        hx-vals='{"setting_type": "receipt_stamp", "get-image-file":"True", "app_settings":"manage_app"}'
                        hx-trigger="load"
                        hx-target="this">
                    </span>
                </div> 
            </div>

            <div id="submit-custom-button">
                <button form="setting_form_receipt" name="post" type="submit" class="btn btn-dark create-custom-button">
                    {% if LANGUAGE_CODE == 'ja'%}
                    更新
                    {% else %}
                    Update
                    {% endif %}
                </button>
            </div>
        </div>
    </div>
</div>

{% elif setting_type == 'slips'%}
<div class="pb-10 mb-5">
    <div class="">
        <form method="POST" action="{% host_url 'commerce_settings' host 'app' %}" class="" id="setting_form_slip" enctype="multipart/form-data">
            {% csrf_token %}
            <div id="slip_setting_section">
                <div class="mb-10" 
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "slips"}'
                    hx-trigger="load"
                    hx-target="this"
                    hx-swap="innerHTML"
                    >
                </div>
                {% include 'data/account/workspace/settings/workspace-object-manager.html' %}
            </div>
                        
            <div id="slip_setting_section">
                <div id='slip_setting' class='mb-10'
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "slip"}'
                    hx-select="#slip_setting_section" 
                    hx-trigger="load"
                    hx-target="this">
                </div>
            </div>

            <div class="mb-10">
                <div class="fv-rowd-flex flex-column">
                    <div class="mb-5" id="shipping_fee_section">
                        <label class="fs-4 fw-bold mb-2">
                            <span class=""> 
                                {% if LANGUAGE_CODE == 'ja'%}
                                送料
                                {% else %}
                                Shipping Cost
                                {% endif %}
                                </h2>
                            </span>
                            <a href='javascript:;' onclick='add_shippings(this);'>
                                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24"/>
                                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                    </g>
                                </svg>
                            </a>
                        </label>

                        {% for ShopTurboShippingCost in ShopTurboShippingCosts %}
                        <div class="mb-5">

                            <input type="hidden" name="shipping_field_id" value="{{ShopTurboShippingCost.id}}"></input>

                            <div class="d-flex ">
                                <div class="input-group flex-nowrap align-items-strecth">  
                                    <div class="input-prehead">
                                        <select class="{% include 'data/utility/select-form.html' %} input-prehead select2-this" name="shipping_field_number_format" data-placeholder="value">
                                            
                                        {% if workspace.currencies %}
                                        <optgroup
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            label="デフォルト通貨"
                                            {% else %}
                                            label="Default Currency"
                                            {% endif %}
                                        >
                                            {% for currency in workspace.currencies|string_list_to_list %}
                                                {% if forloop.counter == 1 %}
                                                    <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                {% elif ShopTurboShippingCost.number_format == currency %}
                                                    <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                {% else %}
                                                    <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                                {% endif %}
                                            {% endfor %}

                                        </optgroup>
                                        {% endif %}

                                        <optgroup
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            label="すべての通貨"
                                            {% else %}
                                            label="All Currencies"
                                            {% endif %}
                                        >
                                        {% include 'data/partials/money-currency.html' with obj=ShopTurboShippingCost.number_format %}
                                        </optgroup>
                                        </select>
                                    </div>

                                    <input class="form-control w-50 h-40px" name="shipping_field_value"
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        placeholder="送料の値" 
                                        {% else %}
                                        placeholder="Shipping Price Value" 
                                        {% endif %}
                                        
                                        {% if ShopTurboShippingCost.value %}
                                        value = '{{ShopTurboShippingCost.value}}'
                                        {% endif %}
                                    />
                                    
                                    <input class="form-control d-flex w-25 h-40px" name="shipping_field_name"
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        placeholder="送料名" 
                                        {% else %}
                                        placeholder="Shipping Fee Name" 
                                        {% endif %}

                                        {% if ShopTurboShippingCost.name %}
                                        value = '{{ShopTurboShippingCost.name}}'
                                        {% endif %}
                                    />
                                </div>
                                <div class="input-group-append">
                                    <button class="btn btn-danger btn-sm ms-1" onclick='delete_shipping(this);' type="button">X</button>
                                </div> 
                            </div>
                        </div>
                        {% endfor %}
                        
                    </div>
                </div>
                
            </div>
            
            <button hidden type="submit" id="default_form" class="btn btn-dark">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </form>
        <div id="slip-logo-uploads">
            <div class="mb-5" id="">
                <div class="d-flex justify-content-between">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span >
                            {% if LANGUAGE_CODE == 'ja' %}
                            企業ロゴ
                            {% else %}
                            Company Logo
                            {% endif %}
                        </span>

                    </label>

                    <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"

                    onclick="document.getElementById('company-logo-column-button').click();"

                    >
                        <span class="fs-7 ps-1 fw-bolder">
                            {% if LANGUAGE_CODE == 'ja' %}
                            他のオブジェクトに適用
                            {% else %}
                            Apply To Other Objects
                            {% endif %}
                        </span>
                    </button>
                </div>
                
                <span
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "slip_logo", "get-image-file":"True", "app_settings":"manage_app"}'
                    hx-trigger="load"
                    hx-target="this">
                </span>
            </div> 
        </div>

        <div id="slip-stamp-uploads">
            <div class="mb-5" id="">
                <div class="d-flex justify-content-between">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span >
                            {% if LANGUAGE_CODE == 'ja' %}
                            社印
                            {% else %}
                            Company Signature
                            {% endif %}
                        </span>
                        
                    </label>

                    <button type="button" class="btn btn-light-primary btn-md py-1 rounded-1"

                    onclick="document.getElementById('company-signature-column-button').click();"

                    >
                        <span class="fs-7 ps-1 fw-bolder">
                            {% if LANGUAGE_CODE == 'ja' %}
                            他のオブジェクトに適用
                            {% else %}
                            Apply To Other Objects
                            {% endif %}
                        </span>
                    </button>
                </div>
                
                <span
                    hx-get="{% host_url 'commerce_settings' host 'app' %}" 
                    hx-swap="innerHTML" 
                    hx-vals='{"setting_type": "slip_stamp", "get-image-file":"True", "app_settings":"manage_app"}'
                    hx-trigger="load"
                    hx-target="this">
                </span>
            </div> 
        </div>

        <div id="submit-custom-button">
            <button form="setting_form_slip" name="post" type="submit" class="btn btn-dark create-custom-button">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </div>
    </div>
</div>

{% endif %}

{% endblock %}