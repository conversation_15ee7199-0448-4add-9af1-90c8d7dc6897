{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div id="change-stamp-section" class="mb-10">
    <div class="">
        <form class="mb-10 px-0" action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}" method="POST" >
            {% csrf_token %}
            <input type="hidden" name="module" value="{{menu_key}}">

            <div class="task_wizard">
                <div class="mb-5">
                    <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                                連携サービス
                            {% else %}
                                Integrations
                            {% endif %}
                        </span>
                    </label>
                    <select required class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="false" 
                        id="channel-select"
                        data-allow-clear="false" onchange="toggleMappingSections()"
                        name="select_integration_ids" placeholder="{% if LANGUAGE_CODE == 'ja' %}担当者{% else %}Assignee{% endif %}">
                        {% for channel in channels %}
                            <option value="{{channel.id}}" data-platform="{{channel.integration.slug}}" data-name="{{channel.name}}">
                                {% if channel.integration.slug == 'hubspot' %}
                                    {{ channel.name|cut:" Power Inventory" }}
                                {% else %}
                                    {{ channel.name }}
                                {% endif %}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            {% if import_export_type == 'import' %}
            <div id="mapping-location-field" class="d-none">
                <div class="mb-3 border bg-gray-100 rounded p-5">
                    <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                                Shopify ロケーションマッピング
                            {% else %}
                                Shopify Location Mapping
                            {% endif %}
                        </span>
                    </label>
                    <div class="loading-drawer-spinner mt-10 mb-5 p-2">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <div id="shopifyMappingContainer" class="mapping-form mb-3">
                        {% if shopify_location_mapping %}
                            {% for index, val in shopify_location_mapping.items %}
                                <div class="mt-5" id="mapping-{{index}}">
                                    <div class="mb-5">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="{% include 'data/utility/form-label.html' %} mt-5">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                Shopifyロケーションをマッピング {{index}}
                                                {% else %}
                                                Mapping Shopify Locations {{index}}
                                                {% endif %}
                                            </span>
                                            <button type="button" class="btn btn-danger btn-dashed rounded-pill px-3 py-1"
                                                onclick="document.getElementById('mapping-{{index}}').remove()" >
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                マッピングを追加
                                                {% else %}
                                                Remove Mapping
                                                {% endif %}
                                            </button>
                                        </div>
                                        {% for channel_id, value in val.items %}
                                            {% with channel=channel_id|get_channel_obj%}
                                            <label class="fs-6 fw-bold text-dark mt-3 mb-2">{{channel.name}}</label>
                                            <select
                                                class="h-40px form-select form-select-solid border bg-white select2-inventory-warehouse-lazy-{{channel.id}}-{{index}}"
                                                data-control="select2" 
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                    data-placeholder="Shopifyロケーションをマッピング"
                                                {% else %}
                                                    data-placeholder="Mapping Shopify Locations"
                                                {% endif %}
                                                name='shopify_mapping|{{index}}|{{channel.id}}|{{action_index}}'
                                                data-allow-clear="true"
                                                >
                                                <option value=""></option>
                                                {% if value %}
                                                    {% with warehouse=value|get_warehouse_obj %}
                                                    <option value="{{value}}" selected>#{{warehouse.id_iw|stringformat:"04d"}} - {{warehouse.location}}</option>
                                                    {% endwith %}
                                                {% endif %}
                                            </select>
                                    
                                            <script>
                                                $('.select2-inventory-warehouse-lazy-{{channel.id}}-{{index}}').select2({
                                                    ajax: {
                                                        delay: 250, // wait 250 milliseconds before triggering the request
                                                        dataType: 'json',
                                                        url: '{% host_url "inventory_warehouse_options" host "app" %}',
                                                        data: function (params) {
                                                                var query = {
                                                                    q: params.term,
                                                                    page: params.page || 1,
                                                                    json_response: true,
                                                                    channel_id: '{{channel.id}}',
                                                                }
                                                                return query;
                                                            },
                                                        minimumInputLength: 2,
                                                    },
                                                    language: {
                                                        "noResults": function(){
                                                            return "{% if LANGUAGE_CODE == 'ja'%}ロケーションが見つかりません{% else %}No Location found{% endif %}";
                                                        },
                                                        searching: function(){
                                                            return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                                                        },
                                                        "loadingMore": function(){
                                                            return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                                                        },
                                                    },
                                                })
                                            </script>
                                            {% endwith %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <button type="button" class="btn btn-primary ms-2" id="first-sync-shopify-locations"
                                hx-get="{% host_url 'shopify_sync_map_locations' host 'app' %}"
                                hx-vals='js:{"action_index": "{{action_index}}", "method": "sync", "channel_id": get_ch_ids(), "all_map_id": get_all_map_ids()}'
                                hx-target="#shopifyMappingContainer"
                                hx-indicator=".loading-drawer-spinner,.mapping-form"
                                hx-swap="beforeend"
                                onclick="document.getElementById('button-mapping-location').classList.remove('d-none');document.getElementById('first-sync-shopify-locations').classList.add('d-none');toggleShopifyInput();"
                            >
                                {% if LANGUAGE_CODE == 'ja' %}
                                    同期
                                {% else %}
                                    Sync
                                {% endif %}
                            </button>
                        {% endif %}
                    </div>
                    <style>
                        .loading-drawer-spinner{
                            display:none;
                        }
                        .mapping-form{
                            display:block;
                        }
                        .htmx-request .mapping-form{
                            display:none;
                        }
                        .htmx-request.mapping-form{
                            display:none;
                        }
                        .htmx-request .loading-drawer-spinner{
                            display:block;
                        }
                        .htmx-request.loading-drawer-spinner{
                            display:block;
                        }
                    </style>
                    <div class="{% if not shopify_location_mapping %}d-none{% endif %} d-flex mt-3" id="button-mapping-location">
                        <button type="button" class="btn btn-dark" id="sync-shopify-locations"
                            hx-get="{% host_url 'shopify_sync_map_locations' host 'app' %}"
                            hx-vals='js:{"action_index": "{{action_index}}", "channel_id": get_ch_ids(), "all_map_id": get_all_map_ids()}'
                            hx-target="#shopifyMappingContainer"
                            hx-indicator=".loading-drawer-spinner,.mapping-form"
                            hx-swap="beforeend"
                        >
                            {% if LANGUAGE_CODE == 'ja' %}
                                マッピングを追加
                            {% else %}
                                Add Mapping
                            {% endif %}
                        </button>
                        <button type="button" class="btn btn-primary ms-2"
                            hx-get="{% host_url 'shopify_sync_map_locations' host 'app' %}"
                            hx-vals='js:{"action_index": "{{action_index}}", "method": "sync", "channel_id": get_ch_ids(), "all_map_id": get_all_map_ids()}'
                            hx-target="#shopifyMappingContainer"
                            hx-indicator=".loading-drawer-spinner,.mapping-form"
                            hx-swap="beforeend"
                        >
                            {% if LANGUAGE_CODE == 'ja' %}
                                同期
                            {% else %}
                                Sync
                            {% endif %}
                        </button>
                        <button type="submit" class="btn btn-light-primary ms-2" name="save_shopify_location_mapping">
                            {% if LANGUAGE_CODE == 'ja'%}
                            保存場所マッピング
                            {% else %}
                            Save Location Mapping
                            {% endif %}
                        </button>
                    </div>
                    <script>
                        function get_ch_ids(){
                            var channel_ids = {{shopify_channels_id|safe}};
                            return channel_ids;
                        }

                        function get_all_map_ids(){
                            var parentDiv = [];
                            $("#shopifyMappingContainer > div").each((index, elem) => {
                                parentDiv.push(elem.id);
                            });
                            if (parentDiv.length > 0){
                                return parentDiv;
                            } else {
                                return [];
                            }
                        }
                    </script>
                </div>
            </div>
            {% else %}
            <div id="mapping-location-field" class="d-none">
            </div>
            {% endif %}
                            
            <div id="hubspot-mapping-table-section" class="d-none">
                <div id="mapping-hubspot-field" class="mb-10 mt-8">
                    {% csrf_token %}
                    <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                        <input id="hubspot-map-checker" name="hubspot-map-checker" class="form-check-input" type="checkbox"
                            hx-post="{% host_url 'hubspot_inventory_fields_mapping_url' host 'app' %}"
                            hx-vals='{"module": "{{menu_key}}"}'
                            hx-target="#hubspotMappingContainer"
                            hx-trigger="channelChanged from:body"
                            hx-encoding="multipart/form-data">
                    </div>

                    <div id="hubspotMappingContainer" class="mb-3"></div>
                </div>
            </div>
            
            <div id="shopify-mapping-table-section" class="d-none">
                <div id="mapping-item-field" class="mb-10 mt-8">
                    {% csrf_token %}
                    <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                        <input id="item-checker" name="item-checker" class="form-check-input" type="checkbox"
                            
                            hx-post="{% host_url 'sync_item_header_extractor' host 'app' %}"
                            hx-vals='{"module": "{{menu_key}}"}'
                            hx-target="#csvMappingContainer"
                            hx-include="#channel-select"
                            hx-trigger="load, channelChanged"
                            hx-encoding="multipart/form-data"
                            onchange="contactMappingHandler(this)"
                        >
                        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="allowchanges">
                            {% if LANGUAGE_CODE == 'ja' %}
                            連絡先情報のマッピング
                            {% else %}
                            Mapping Contact Infomation
                            {% endif %}
                        </label>
                    </div>

                    <div id="csvMappingContainer" class="mb-3"></div>
                </div>
            </div>

            <div id="general-mapping-table-section" class="d-none">
                <div id="mapping-item-field" class="mb-10 mt-8">
                    {% csrf_token %}
                    <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                        <input id="item-checker" name="item-checker" class="form-check-input" type="checkbox"
                            
                            hx-post="{% host_url 'sync_item_header_extractor' host 'app' %}"
                            hx-vals='{"module": "{{menu_key}}"}'
                            hx-target="#GeneralItemMappingContainer"
                            hx-include="#channel-select"
                            hx-trigger="load, channelChanged"
                            hx-encoding="multipart/form-data"
                            onchange="contactMappingHandler(this)"
                        >
                        <label class="form-check-label fw-semibold text-gray-700 ms-3" for="allowchanges">
                            {% if LANGUAGE_CODE == 'ja' %}
                            マッピングテーブル
                            {% else %}
                            Mapping Table
                            {% endif %}
                        </label>
                    </div>

                    <div id="GeneralItemMappingContainer" class="mb-3"></div>
                </div>
            </div>

            <div id="filter-method-container" class="d-none mb-10 mt-8">
                <div class="mb-3">
                    <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                        <span>
                            {% if LANGUAGE_CODE == 'ja' %}
                                フィルターメソッド
                            {% else %}
                                Filter Method
                            {% endif %}
                        </span>
                    </label>
                    <select id="filter-method" name="filter-method" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターメソッド{% else %}Filter Method{% endif %}" data-allow-clear="true">
                        <option value=""></option>
                        <option value="choice_filter" {% if filter_method == 'choice_filter' %} selected {% endif %}>
                            {% if LANGUAGE_CODE == 'ja' %}
                                選択肢フィルター
                            {% else %}
                                Choice Filter
                            {% endif %}
                        </option>
                        <option value="sku" {% if filter_method == 'sku' %} selected {% endif %}>
                            {% if LANGUAGE_CODE == 'ja' %}
                                SKUフィルター
                            {% else %}
                                SKU Filter
                            {% endif %}
                        </option>
                    </select>
                </div>
            </div>

            <div id="filter-sku" class="d-none">
                <div class="mb-3">
                    <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                                SKUフィルター
                            {% else %}
                                SKU Filter
                            {% endif %}
                        </span>
                    </label>
                    <input type="text" name="sku-filter" class="form-control form-control-solid bg-white" value="{{input_data.filter_sku}}">
                </div>
            </div>

            <div id="filter-panel" class="d-none">
                <div id="sync-filters-field" class="mb-10 mt-8 d-none">
                    <div class="mb-3">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                    フィルターの種類
                                {% else %}
                                    Filter Type
                                {% endif %}
                            </span>
                        </label>
                        <select id="filter-type" name="filter-type" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターの種類{% else %}Filter Type{% endif %}" data-allow-clear="true">
                            <option value=""></option>
                            <option value="include">{% if LANGUAGE_CODE == 'ja'%} 含める {% else %} Include {% endif %}</option>
                            <option value="exclude">{% if LANGUAGE_CODE == 'ja'%} 除外する {% else %} Exclude {% endif %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                    フィルター
                                {% else %}
                                    Filters
                                {% endif %}
                            </span>
                        </label>
                        <select id="filter-select" name="filter" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}" data-allow-clear="true">
                            <option value=""></option>
                            {% for filter in filters %}
                                <option value="{{filter.id}}">
                                    {% if filter.name %}
                                        {{filter.name}}
                                    {% else %}
                                        {{filter.id}}
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div id="filter-choice-field" class="mb-3 d-none">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                    フィルターの選択肢
                                {% else %}
                                    Filter Choice
                                {% endif %}
                            </span>
                        </label>
                        <select id="filter-choice" name="filter-choice" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターの選択肢を選択{% else %}Select Filter Choice{% endif %}" data-hide-search="true" data-allow-clear="true">
                        </select>
                    </div>
                </div>

                <div id="advanced-filter-create" class="mt-5">
                    <div class="mb-2 fs-4 justify-content-start fw-bolder">
                        <div class="">
                            {% if LANGUAGE_CODE == 'ja' %}フィルター{% else %}Filters{% endif %}
                        </div>
                        <select id="filter-select-2" class="bg-white border min-h-40px form-select form-select-solid w-100"      
                            data-control="select2" 
                            hx-get="{% host_url 'shopturbo_load_drawer' host 'app' %}"
                            hx-target="#data-selector-content"
                            hx-trigger="htmx-change"
                            name="data_filter"
                            data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}"
                            hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "app_type":"items"}'
                            hx-swap="beforeend" 
                            >
                            <option value=''></option>

                            {% for column_value in all_columns %}
                                <option value="{{column_value}}">
                                    {% if column_value|search_custom_field_object_items:request %}
                                        {% with channel_column=column_value|search_custom_field_object_items:request %}
                                            {{channel_column.name}}
                                        {% endwith %} 
                                    {% else %}
                                        {% with column_display=column_value|display_column_items:request %}
                                            {{column_display}}
                                        {% endwith %}
                                    {% endif %}
                                </option>            
                            {% endfor %}
                        </select>
                    </div>

                    <form id="filter-form"
                    hx-post="{% host_url 'get_count_of_items_by_filter' host 'app' %}"
                    hx-target="#item-count"
                    hx-swap="innerHTML">
                        <button type="submit" id="filter-submit-button" class="d-none"></button>
                    </form>
                    <div id="data-selector-content" class="mt-3"></div>

                    <div id="filter-content-loaders" class="d-none"></div>
                </div>

                <div id="sync-method-field" class="mb-10 mt-8 d-none">
                    <div class="mb-6">
                        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                    同期方法
                                {% else %}
                                    Sync Method
                                {% endif %}
                            </span>
                        </label>
                        <select required name="sync-key" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="true">
                            <option value="sku" selected>
                                {% if LANGUAGE_CODE == 'ja' %}
                                在庫SKU
                                {% else %}
                                Inventory SKU
                                {% endif %}
                            </option>
                            <option value="platform">
                                {% if LANGUAGE_CODE == 'ja' %}
                                プラットフォームID
                                {% else %}
                                Platform ID 
                                {% endif %}
                            </option>
                        </select>
                    </div>
                </div>

                <!-- <div id="exclude-procurement-field" class="form-check mb-5 d-none">
                    <input id="exclude-procurement-input" class="form-check-input" type="checkbox" id="exclude-procurement" name="exclude-procurement" value=1>
                        <label class="form-check-label ms-1 fs-5 text-gray-700" for="exclude-procurement" >
                            {% if LANGUAGE_CODE == 'ja' %}調達関連品を除く{% else %}Exclude procurement-related Items{% endif %}
                        </label>
                </div> -->
            </div>

            <div id="update-inventory-toggle" class="d-none mb-5">
                <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                    <span>
                        {% if LANGUAGE_CODE == 'ja'%}在庫同期{% else %}Update Inventory{% endif %}
                    </span>
                </label>
        
                <select id="update_inventory" name="update_inventory" class="bg-white form-select form-select-solid border h-40px select2-this" 
                    {% if LANGUAGE_CODE == 'ja'%}
                    data-placeholder="在庫同期"
                    {% else %}
                    data-placeholder="Update Inventory"
                    {% endif %}
                    data-allow-clear="true">
                    <option value="no_update">
                        {% if LANGUAGE_CODE == 'ja' %}
                            在庫を更新しない
                        {% else %}
                            Do Not Update Inventory
                        {% endif %}
                    </option>
                    <option value="update">
                        {% if LANGUAGE_CODE == 'ja' %}
                            在庫を更新する
                        {% else %}
                            Update Inventory
                        {% endif %}
                    </option>
                    <option value="only_inventory">
                        {% if LANGUAGE_CODE == 'ja' %}
                            在庫のみ更新（商品情報は更新しない）
                        {% else %}
                            Update Inventory Only (do not update item)
                        {% endif %}
                    </option>
                </select>
            </div>

                <div class="mb-8">
                    {% if object_type == 'commerce_inventory' %}
                        {% for inventory_id in item_ids %}
                            <input type="hidden" name="inventory_ids" value="{{inventory_id}}">
                        {% endfor %}
    
                    {% elif object_type == 'commerce_items' %}
                        {% for item_id in item_ids %}
                            <input type="hidden" name="item_ids" value="{{item_id}}">
                        {% endfor %}
       
                    {% elif object_type == 'commerce_inventory_transaction' %}
                        {% for inventory_transactions_id in inventory_transactions_ids %}
                            <input type="hidden" name="inventory_transactions_ids" value="{{inventory_transactions_id}}">
                        {% endfor %}
                    {% elif object_type == 'commerce_inventory_warehouse' %}
                        {% for inventory_transactions_id in inventory_transactions_ids %}
                            <input type="hidden" name="inventory_transactions_ids" value="{{inventory_transactions_id}}">
                        {% endfor %}
                    {% endif %}
                </div>
    
            <div id="update-options" class="fv-row d-flex flex-column mb-7 d-none">
                <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}マッチする商品プロパティを選択 (オプション){% else %}Choose Item Property to Match (Optional){% endif %}
                    </span>
                </label>
            
                <select id="update-key-select" name="update-options" class="update-options bg-white form-select form-select-solid border h-40px select2-this" 
                    {% if LANGUAGE_CODE == 'ja'%}
                    data-placeholder="キープロパティを選択"
                    {% else %}
                    data-placeholder="Select Key Property"
                    {% endif %}
                    data-allow-clear="true"
                    >
                    <option value="" selected disabled>
                        {% if LANGUAGE_CODE == 'ja' %}
                            キープロパティを選択
                        {% else %}
                            Select Key Property
                        {% endif %}
                    </option>
                    {% for column_value in columns %}
                        <option value="{{column_value}}">
                            {% if column_value|search_custom_field_object_items:request %}
                                {% with channel_column=column_value|search_custom_field_object_items:request %}
                                    {{channel_column.name}}
                                {% endwith %} 
                            {% else %}
                                {% with column_display=column_value|display_column_items:request %}
                                    {{column_display}}
                                {% endwith %}
                            {% endif %}
                        </option>    
                    {% endfor %}  
                </select>
            </div>
    
                <div id="sync-progress-message" class="mb-3 text-danger d-none">
                    {% if LANGUAGE_CODE == 'ja'%}
                        同期中です。しばらくお待ちください。
                    {% else %}
                        Sync in progress. Please wait a moment.
                    {% endif %}
                </div>
            <div id="export-import-btn" class="d-none">
                <button id="import-btn" type="submit" class="btn btn-primary d-none"
    
                    {% if object_type == 'commerce_inventory' %}
                    name="sync_inventories"
                    {% elif object_type == 'commerce_items' %}
                    name="sync_items"
                    {% elif object_type == 'commerce_inventory_transaction' %}
                    name="sync_transactions"
                    {% elif object_type == 'commerce_inventory_warehouse' %}
                    name="sync_locations"
                    {% endif %}
                >
                    <span class="svg-icon svg-icon-2 svg-icon-white">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-download" viewBox="0 0 16 16">
                            <path d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                            <path d="M7.646 15.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 14.293V5.5a.5.5 0 0 0-1 0v8.793l-2.146-2.147a.5.5 0 0 0-.708.708z"/>
                        </svg>
                    </span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </button>
                
                {% if object_type|in_list:'commerce_inventory,commerce_items' %}
                    <button id="export-btn" type="submit" class="btn btn-primary d-none"
    
                        {% if object_type == 'commerce_inventory' %}
                        name="push_inventories"
                        {% elif object_type == 'commerce_items' %}
                        name="push_items"
                        {% endif %}>
                            <span class="svg-icon svg-icon-2 svg-icon-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-download" viewBox="0 0 16 16">
                                    <path d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                                    <path d="M7.646 15.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 14.293V5.5a.5.5 0 0 0-1 0v8.793l-2.146-2.147a.5.5 0 0 0-.708.708z"/>
                                </svg>
                            </span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            エクスポート
                            <span id="item-count">
                                {% if item_ids|length > 0 %}({{item_ids|length}}){% else %}({{count_items}}){% endif %}
                            </span>
                            {% else %}
                            Export
                            <span id="item-count">
                                {% if item_ids|length > 0 %}({{item_ids|length}}){% else %}({{count_items}}){% endif %}
                            </span>
                            {% endif %}
                    </button>
                {% endif %}
            </div>
        </form>
    </div>

</div>


{% if object_type|in_list:'commerce_inventory,commerce_items' %}
<script>
    // On Change Radio with name="switch"
    document.querySelectorAll('input[name="switch"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            if (document.querySelector('input[name="switch"]:checked')) {
                document.getElementById('export-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_3')
                document.getElementById('import-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_1')
            }
        });
    });
</script>
{% endif %}


<script>
    $('.select2-this').select2();
    $(document).ready(function() {
        const channelSelect = $('#channel-select');
        const shopifyLocationMappingSection = $('#mapping-location-field');
        const shopifyMappingSection = $('#shopify-mapping-table-section');
        const hubspotMappingSection = $('#hubspot-mapping-table-section');
        const hubspotMapChecker = $('#hubspot-map-checker');
        const filterMethodContainer = $('#filter-method-container');
        const filterPanel = $('#filter-panel'); 
        const exportImportBtn = $('#export-import-btn');
        const drawer_type = '{{drawer_type}}'
        const generalMappingSection = $('#general-mapping-table-section');
        const syncFiltersField = $('#sync-filters-field');
        const advancedFilterCreate = $('#advanced-filter-create');
        const updateInventoryToggle = $('#update-inventory-toggle');
        
        function getExportImportItemConfig(platform, object_type){
            const import_export_type = '{{import_export_type}}'

            var selected_platform = platform
            var update_inventory = ""
            $.ajax({
                url: '{% url "get_export_import_item_configuration" %}',
                type: 'GET',
                data: {
                    platform: platform,
                    object_type: object_type,
                },
                success: function(response) {
                    var update_inventory = response?.['update-inventory-toggle'] ?? null;
                    var filter_dictionary = response?.['filter_dictionary'] ?? null;

                    console.log("Update inventory: ", update_inventory);
                    console.log("Filter dictionary: ", filter_dictionary);
                    // container
                    var updateOptionsContainer = document.getElementById('update-options');
                    var updateInventoryToggle = document.getElementById('update-inventory-toggle');

                    // get import method and default customer element
                    var updateOptions = document.getElementById('update-key-select');
                    var update_inventory_selector = document.getElementById('update_inventory');

                    // default state
                    updateOptions.value = '';
                    update_inventory_selector.value = '';
                    
                    // set the value of the element dynamically
                    if (update_inventory_selector && import_export_type === 'export'){
                        update_inventory_selector.value = update_inventory;
                        update_inventory_selector.dispatchEvent(new Event('change'));
                    }

                    // set for the filter dictionary
                    if (filter_dictionary) {
                        console.log("Filter dictionary: ", filter_dictionary);

                        Object.entries(filter_dictionary).forEach(([predefinedDataFilter, filterObj]) => {
                            console.log("Predefined data filter: ", predefinedDataFilter);
                            console.log("Filter object: ", filterObj);

                            // Create div
                            let div = document.createElement('div');
                            div.setAttribute('hx-get', `{% host_url 'shopturbo_load_drawer' host 'app' %}`);
                            div.setAttribute('hx-target', '#data-selector-content');
                            div.setAttribute('hx-trigger', 'dataSelectorChanged');
                            div.setAttribute('hx-swap', 'beforeend');

                            const hxVals = {
                                "drawer_type": "shopturbo-view-settings",
                                "type": "advance-filter",
                                "data_filter": predefinedDataFilter,
                                "predefined_data_filter": filterObj,
                                "app_type": 'items'
                            };

                            div.setAttribute('hx-vals', JSON.stringify(hxVals));

                            document.getElementById('filter-content-loaders').appendChild(div);

                            console.log(document.getElementById('filter-content-loaders'));
                            setTimeout(() => {
                                div.dispatchEvent(new Event('dataSelectorChanged'));
                            }, 2000);

                        });
                    }

                },
                error: function(xhr, status, error) {
                    console.log(error)
                }
            })   
        }

        toggleMappingSections();

        channelSelect.change(function() {
            toggleMappingSections();
            var platform = channelSelect.find('option:selected').data('platform')
            var object_type = "{{object_type}}"
            getExportImportItemConfig(platform, object_type)
        });

        // radio = document.querySelector('input[name="switch"]:checked');
        // if (radio) {
        //     document.getElementById('export-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_3')
        //     document.getElementById('import-btn').classList.toggle('d-none', radio.id !== 'kt_switch_option_1')
        // }

        const importExportType = '{{ import_export_type }}';
        if (importExportType === 'import') {
            $('#import-btn').removeClass('d-none');
        } else {
            $('#export-btn').removeClass('d-none');
        }
    });

    function toggleMappingSections() {
        const channelSelect = $('#channel-select');
        const shopifyLocationMappingSection = $('#mapping-location-field');
        const shopifyMappingSection = $('#shopify-mapping-table-section');
        const hubspotMappingSection = $('#hubspot-mapping-table-section');
        const hubspotMapChecker = $('#hubspot-map-checker');
        const filterMethodContainer = $('#filter-method-container');
        const filterPanel = $('#filter-panel'); 
        const exportImportBtn = $('#export-import-btn');
        const drawer_type = '{{drawer_type}}'
        const generalMappingSection = $('#general-mapping-table-section');
        const syncFiltersField = $('#sync-filters-field');
        const advancedFilterCreate = $('#advanced-filter-create');
        const updateInventoryToggle = $('#update-inventory-toggle');
        const selectedChannel = $('#channel-select option:selected');
        const platform = selectedChannel.data('platform');
        const channelName = selectedChannel.data('name');
        const channelId = selectedChannel.val();

        if (platform === 'hubspot' && drawer_type != 'shopturbo-view-sync-items') {
            shopifyLocationMappingSection.addClass('d-none');
            shopifyMappingSection.addClass('d-none');
            hubspotMappingSection.removeClass('d-none');
            filterPanel.addClass('d-none')
            exportImportBtn.addClass('d-none');
            generalMappingSection.addClass('d-none');

            const inputs = {
                "channel_id": channelId,
                "hub_domain": channelName,
                "import_export_type": "{{ import_export_type }}",
                "object_type": "{{ object_type }}",
                "menu_key": "{{menu_key}}",
                "count_items": "{{count_items}}"
            }
            hubspotMapChecker.attr('hx-vals', JSON.stringify(inputs));

            console.log("HubSpot map checker: ", hubspotMapChecker);

            // Trigger the 'channelChanged' event on the body element
            htmx.trigger('body', 'channelChanged');

        } else if (platform === 'shopify') {
            shopifyLocationMappingSection.removeClass('d-none');
            hubspotMappingSection.addClass('d-none');
            generalMappingSection.addClass('d-none');

            {% if shopify_location_mapping %}
            shopifyMappingSection.removeClass('d-none');
            filterPanel.removeClass('d-none');
            exportImportBtn.removeClass('d-none');
            filterMethodContainer.removeClass('d-none');
            {% endif %}
        } else {
            if (platform === 'makeshop' || platform === 'hubspot' || platform === 'smaregi' || platform == 'rakuten' || platform === 'salesforce' || platform === 'amazon') {
                shopifyMappingSection.removeClass('d-none');
            } else {
                shopifyMappingSection.addClass('d-none');
            }

            if (platform === 'makeshop' || platform === 'rakuten' || platform === 'amazon') {
                filterPanel.removeClass('d-none')
                advancedFilterCreate.removeClass('d-none')
                updateInventoryToggle.removeClass('d-none')
            } else {
                filterPanel.addClass('d-none')
                advancedFilterCreate.addClass('d-none')
                updateInventoryToggle.addClass('d-none')
            }
            hubspotMappingSection.addClass('d-none');
            //Need to clear hubspot map, remove inside hubspotMappingContainer
            document.getElementById('hubspotMappingContainer').innerHTML = '';
            
            exportImportBtn.removeClass('d-none');
        }

        Array.from(document.querySelectorAll("input[name='item-checker']")).forEach(element => {
            if (!Array.from(element.parentElement.parentElement.classList).includes('d-none')) {
                htmx.trigger(`#${element.id}`, "channelChanged")
            }
        });
    }
    
    function contactMappingHandler(elm){
        var mapping = document.getElementById("csvMappingContainer")
        if (elm.checked === true){
            if (mapping){
                mapping.classList.add('d-none')
            }
            
        }
        else{
            if (mapping){
                mapping.classList.add('d-none')
            }
            
        }
    }

    function toggleShopifyInput(elm){
        const shopifyMappingSection = $('#shopify-mapping-table-section');
        const filterPanel = $('#filter-panel'); 
        const exportImportBtn = $('#export-import-btn');
        const filterMethodContainer = $('#filter-method-container');

        shopifyMappingSection.removeClass('d-none');
        filterPanel.removeClass('d-none');
        exportImportBtn.removeClass('d-none');
        filterMethodContainer.removeClass('d-none');
    }

    function toggleFilterSelect(){
        var filterMethod = document.getElementById("filter-method");
        var filterSku = document.getElementById("filter-sku");
        var filterChoice = document.getElementById("sync-filters-field");

        if (filterMethod.value === 'sku') {
            filterSku.classList.remove('d-none');
            filterChoice.classList.add('d-none');
        } else if (filterMethod.value === 'choice_filter') {
            filterSku.classList.add('d-none');
            filterChoice.classList.remove('d-none');
        } else {
            filterSku.classList.add('d-none');
            filterChoice.classList.add('d-none');
        }
    }

    $('#filter-method').on('change', function() {
        toggleFilterSelect();
    });
    
</script>

<script>
    //if filter selected
    $('#filter-select').on('change', function() {
        var filterId = this.value;
        if (filterId) {
            var filterChoice = document.getElementById("filter-choice");
            var filterChoiceField = document.getElementById("filter-choice-field");
            filterChoiceField.classList.remove('d-none');
            filterChoice.innerHTML = '';
            
            {% for filter in filters %}
                if (filterId === '{{filter.id}}') {
                    var choiceValueStr = `{{filter.choice_value|safe}}`
                    //Replace ' with "
                    choiceValueStr = choiceValueStr.replace(/'/g, '"');
                    var choiceValue = JSON.parse(choiceValueStr);
                    filterChoice.innerHTML += `<option value=""></option>`;
                    for (var i = 0; i < choiceValue.length; i++) {
                        filterChoice.innerHTML += `<option value="${choiceValue[i].value}">${choiceValue[i].label}</option>`;
                    }
                }
            {% endfor %}
        }
    })
    
    $('#filter-select-2').select2();
    $('#filter-select-2').on('select2:select', function (e) {
        var selectElement = $(this).closest('select').get(0);
        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
    });
    htmx.on('htmx:afterRequest', (evt) => {
        //check which element triggered the htmx request. If it's the one you want call the function you need
        //you have to add htmx: before the event ex: 'htmx:afterRequest'
        if (evt.detail.elt.id === 'filter-select-2') {
            // Your custom code here
            $('#filter-select-2').val(null).trigger('change.select2');
        }
    })
</script>

<script>
    var channel_ids= {{ channel_ids|safe }};
    console.log(channel_ids);

    // Function to update sync status based on selected channel
    function updateSyncStatus(selectedChannelId) {
        var isRunning = channel_ids.includes(selectedChannelId);
        var syncMessage = document.getElementById('sync-progress-message');
        var importBtn = document.getElementById('import-btn');
        var exportBtn = document.getElementById('export-btn');
        
        // Show/hide sync message
        syncMessage.classList.toggle('d-none', !isRunning);
        
        // Enable/disable buttons
        importBtn.disabled = isRunning;
        if (exportBtn) {
            exportBtn.disabled = isRunning;
        }
        
        if (isRunning) {
            console.log('Channel is running, sync in progress:', selectedChannelId);
        }
    }

    // Get selected channel ID from channel-select on page load
    $(document).ready(function() {
        var selectedChannelId = document.getElementById('channel-select').value;
        updateSyncStatus(selectedChannelId);

        // Listen for channel-select changes
        $('#channel-select').on('select2:select', function (e) {
            var selectedChannelId = $(this).val();
            updateSyncStatus(selectedChannelId);
        });
    });
    
</script>

<!-- read filters changes -->
<script>
    function setFormAttrOnControls() {
        document.querySelectorAll('#data-selector-content select, #data-selector-content input, #data-selector-content button')
            .forEach(el => {
                el.setAttribute('form', 'filter-form');
            });
    }
    function removeFormAttrFromControls() {
        document.querySelectorAll(
            '#data-selector-content select, #data-selector-content input, #data-selector-content button'
        ).forEach(el => {
            el.removeAttribute('form');
        });
    }

    document.getElementById('data-selector-content').addEventListener('click', function(e) {
        if (e.target.matches('button')) {
            document.querySelectorAll('#data-selector-content input, #data-selector-content select').forEach(el => {
                el.setAttribute('hx-trigger', 'change');
                el.setAttribute('hx-include', 'closest form');
            });
            setFormAttrOnControls();
            document.getElementById('filter-submit-button').click();
            removeFormAttrFromControls();
        }
    });
    
    document.getElementById('data-selector-content').addEventListener('change', function(e) {
        if (
            e.target.matches('input') ||
            e.target.matches('select')
        ) {
            document.querySelectorAll('#data-selector-content input, #data-selector-content select').forEach(el => {
                el.setAttribute('hx-trigger', 'change');
                el.setAttribute('hx-include', 'closest form');
            });
            setFormAttrOnControls();
            document.getElementById('filter-submit-button').click();
            removeFormAttrFromControls();
        }
    });


</script>