import uuid
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User

class Dataset(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    dataset_id = models.IntegerField()
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    data_source = models.CharField(max_length=250, blank=True, null=True)
    metrics = models.ManyToManyField("DatasetMetric")
    edited_by = models.ForeignKey(User, on_delete=models.CASCADE)
    updated_at = models.DateTimeField(default=timezone.now)
    
class DatasetMetric(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    metric = models.TextField(blank=True, null=True)
    order = models.IntegerField(default=0)
    created_at = models.DateTimeField(default=timezone.now)