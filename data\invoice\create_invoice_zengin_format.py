import io
import traceback
from datetime import datetime
from io import BytesIO, StringIO
from django.conf import settings
from django.core.files.base import File
from django.core.mail import EmailMessage
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse

from action.action import trigger_next_action
from data.commerce.commerce_functions import generate_zip
from data.commerce import commerce_pdf_download
from data.commerce.commerce_functions import aggregate_commerce_object
from data.constants.associate_constant import *
from data.constants.procurement_constant import PROCUREMENT_APP_TARGET
from data.constants.properties_constant import *
from data.models import (InvoiceNameCustomField, ContactsNameCustomField, ContactsValueCustomField,
                         ActionNode, ActionTracker, WorkflowActionTracker, Contact, Invoice, User, Module)
from data.property import properties as forward_properties
from utils.actions import transfer_output_to_target_input
from utils.decorator import login_or_hubspot_required
from utils.freee_bg_jobs.freee import *
from utils.serializer import *
from utils.smtp import *
from utils.utility import (bg_job_to_sanka_url, customer_converter, get_workspace, is_valid_uuid,
                           send_discord_notification)

from django.template.loader import render_to_string

type_http = settings.SITE_URL.split("//")[0]


@login_or_hubspot_required  # NOTE: Applied on Workflow aswel, Handled by Faris
def create_invoice_zengin_format(request):
    workspace = get_workspace(request.user)
    user = request.user
    lang = request.LANGUAGE_CODE

    if request.method == 'GET':
        selected_data_classification_header = None
        selected_type_code_header = None
        selected_code_classification_header = None
        selected_tf_req_code_header = None
        selected_tf_req_code_kana_header = None
        selected_tf_date_header = None
        selected_bank_code_header = None
        selected_bank_name_kana_header = None
        selected_branch_code_header = None
        selected_branch_name_kana_header = None
        selected_account_type_header = None
        selected_account_number_header = None
        selected_dummy_header = None

        selected_data_classification_main = None
        selected_bank_code_main = None
        selected_bank_name_kana_main = None
        selected_branch_code_main = None
        selected_clearing_house_number_main = None
        selected_branch_name_kana_main = None
        selected_account_type_main = None
        selected_account_number_main = None
        selected_recepient_name_kana_main = None
        selected_transfer_amount_main = None
        selected_new_code_main = None
        selected_cust_code_1_main = None
        selected_cust_code_2_main = None
        selected_tf_specs_classification = None
        selected_identification_code_main = None
        selected_dummy_main = None
        selected_date = None
        selected_contact_cf = None
        selected_exclude_status = None
        download_with_pdf = None
        selected_data_classification_footer = None
        selected_total_number_item_footer = None
        selected_total_amount_footer = None
        selected_dummy_footer = None

        selected_data_classification_footer_2 = None
        selected_dummy_footer_2 = None

        action_index = request.GET.get('action_index')
        action_node_id = request.GET.get('action_node_id')

        # Getting Info if have action_index, it means from workflow
        postfix = ''
        if action_index:
            postfix = '-' + str(action_index)
        selected_property = request.GET.get('contact_cf' + postfix, None)
        if not selected_property:
            selected_property = 'Customer (Contact)' if lang == 'en' else '顧客（連絡先）'
        else:
            selected_property = selected_property.split('_')[1]
            if selected_property == 'customers':
                selected_property = 'Customer (Contact)' if lang == 'en' else '顧客（連絡先）'
            else:
                selected_property = InvoiceNameCustomField.objects.filter(
                    id=selected_property)
                if selected_property:
                    selected_property = selected_property.first().name

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print('Action node does not exist')
                return HttpResponse(status=404)

        if postfix:
            if node and node.input_data:
                if 'contact_cf' in node.input_data:
                    selected_contact_cf = node.input_data['contact_cf']
                if 'exclude_status' in node.input_data:
                    selected_exclude_status = node.input_data['exclude_status']
                if 'download_with_pdf' in node.input_data:
                    download_with_pdf = node.input_data['download_with_pdf']
                if 'date_select' in node.input_data:
                    selected_date = node.input_data['date_select']
                if 'data_classification_header' in node.input_data:
                    selected_data_classification_header = node.input_data['data_classification_header']
                if 'type_code_header' in node.input_data:
                    selected_type_code_header = node.input_data['type_code_header']
                if 'code_classification_header' in node.input_data:
                    selected_code_classification_header = node.input_data['code_classification_header']
                if 'tf_req_code_header' in node.input_data:
                    selected_tf_req_code_header = node.input_data['tf_req_code_header']
                if 'tf_req_code_kana_header' in node.input_data:
                    selected_tf_req_code_kana_header = node.input_data['tf_req_code_kana_header']
                if 'tf_date_header' in node.input_data:
                    selected_tf_date_header = node.input_data['tf_date_header']
                if 'bank_code_header' in node.input_data:
                    selected_bank_code_header = node.input_data['bank_code_header']
                if 'bank_name_kana_header' in node.input_data:
                    selected_bank_name_kana_header = node.input_data['bank_name_kana_header']
                if 'branch_code_header' in node.input_data:
                    selected_branch_code_header = node.input_data['branch_code_header']
                if 'branch_name_kana_header' in node.input_data:
                    selected_branch_name_kana_header = node.input_data['branch_name_kana_header']
                if 'account_type_header' in node.input_data:
                    selected_account_type_header = node.input_data['account_type_header']
                if 'account_number_header' in node.input_data:
                    selected_account_number_header = node.input_data['account_number_header']
                if 'dummy_header' in node.input_data:
                    selected_dummy_header = node.input_data['dummy_header']

                if 'data_classification_main' in node.input_data:
                    selected_data_classification_main = node.input_data['data_classification_main']
                if 'bank_code_main' in node.input_data:
                    selected_bank_code_main = node.input_data['bank_code_main']
                if 'bank_name_kana_main' in node.input_data:
                    selected_bank_name_kana_main = node.input_data['bank_name_kana_main']
                if 'branch_code_main' in node.input_data:
                    selected_branch_code_main = node.input_data['branch_code_main']
                if 'clearing_house_number_main' in node.input_data:
                    selected_clearing_house_number_main = node.input_data['clearing_house_number_main']
                if 'branch_name_kana_main' in node.input_data:
                    selected_branch_name_kana_main = node.input_data['branch_name_kana_main']
                if 'account_type_main' in node.input_data:
                    selected_account_type_main = node.input_data['account_type_main']
                if 'account_number_main' in node.input_data:
                    selected_account_number_main = node.input_data['account_number_main']
                if 'recepient_name_kana_main' in node.input_data:
                    selected_recepient_name_kana_main = node.input_data['recepient_name_kana_main']
                if 'transfer_amount_main' in node.input_data:
                    selected_transfer_amount_main = node.input_data['transfer_amount_main']
                if 'new_code_main' in node.input_data:
                    selected_new_code_main = node.input_data['new_code_main']
                if 'cust_code_1_main' in node.input_data:
                    selected_cust_code_1_main = node.input_data['cust_code_1_main']
                if 'cust_code_2_main' in node.input_data:
                    selected_cust_code_2_main = node.input_data['cust_code_2_main']
                if 'tf_specs_classification' in node.input_data:
                    selected_tf_specs_classification = node.input_data['tf_specs_classification']
                if 'identification_code_main' in node.input_data:
                    selected_identification_code_main = node.input_data['identification_code_main']
                if 'dummy_main' in node.input_data:
                    selected_dummy_main = node.input_data['dummy_main']
                if 'data_classification_footer' in node.input_data:
                    selected_data_classification_footer = node.input_data['data_classification_footer']
                if 'total_number_item_footer' in node.input_data:
                    selected_total_number_item_footer = node.input_data['total_number_item_footer']
                if 'total_amount_footer' in node.input_data:
                    selected_total_amount_footer = node.input_data['total_amount_footer']
                if 'dummy_footer' in node.input_data:
                    selected_dummy_footer = node.input_data['dummy_footer']

                if 'data_classification_footer_2' in node.input_data:
                    selected_data_classification_footer_2 = node.input_data[
                        'data_classification_footer_2']
                if 'dummy_footer_2' in node.input_data:
                    selected_dummy_footer_2 = node.input_data['dummy_footer_2']

        contacts_list = Contact.objects.filter(
            workspace=workspace, status="active")
        data = {
            'slug': '',
            'display': ''
        }
        contacts = []
        cf_data = []
        contact_cf_data = []
        # ... existing code ...
        if selected_date:
            try:
                if lang == 'ja':
                    # Parse date range and convert to Japanese format
                    date_start, date_end = selected_date.split(' - ')
                    less_date = datetime.strptime(date_start, '%Y-%m-%d')
                    greater_date = datetime.strptime(date_end, '%Y-%m-%d')

                    # Format both dates in Japanese style
                    less_date_jp = f"{less_date.year}年{less_date.month:02d}月{less_date.day:02d}日"
                    greater_date_jp = f"{greater_date.year}年{greater_date.month:02d}月{greater_date.day:02d}日"

                    # Combine with Japanese range separator
                    selected_date = f"{less_date_jp} 〜 {greater_date_jp}"
            except Exception as e:
                print("Error: ", e)
                pass
        # ... existing code ...

        if selected_tf_date_header:
            try:
                if lang == 'ja':
                    # Handle Japanese date format without locale
                    date_obj = datetime.strptime(
                        selected_tf_date_header, '%Y-%m-%d')
                    selected_tf_date_header = f"{date_obj.year}年{date_obj.month:02d}月{date_obj.day:02d}日"
            except Exception as e:
                print("Error: ", e)
                pass
        for contact in contacts_list:
            data_contact = {
                'slug': str(contact.id)
            }
            data_contact['display'] = customer_converter(contact, lang)
            contacts.append(data_contact)

        if lang == 'ja':
            data = {
                'slug': 'invoice_customers',
                'display': '請求書 - 顧客（連絡先）'
            }

        else:
            data = {
                'slug': 'invoice_customers',
                'display': 'Invoice - Customers (Contact)'
            }
        cf_data.append(data)

        custom_field_name = InvoiceNameCustomField.objects.filter(
            workspace=workspace, type='contact').order_by('name')
        for contact_ in custom_field_name:
            if lang == 'ja':
                data = {
                    'slug': 'invoice_' + str(contact_.id),
                    'display': '請求書 - 請求書の連絡先プロパティ - ' + contact_.name
                }
            else:
                data = {
                    'slug': 'invoice_' + str(contact_.id),
                    'display': 'Invoice - Invoice Contact Property - ' + contact_.name
                }
            cf_data.append(data)

        sanka_contact_field = ['name', 'first_name',
                               'last_name', 'email', 'phone_number']

        for field in sanka_contact_field:
            if lang == 'ja':
                data = {
                    'slug': 'invoice_contact_property_' + field,
                    'display': '請求書 - ' + selected_property + ' - 連絡先プロパティ - ' + CONTACTS_COLUMNS_DISPLAY[field][lang]
                }
            else:
                data = {
                    'slug': 'invoice_contact_property_' + field,
                    'display': 'Invoice - ' + selected_property + ' - Contact Property - ' + CONTACTS_COLUMNS_DISPLAY[field][lang]
                }
            contact_cf_data.append(data)

        custom_field_name = ContactsNameCustomField.objects.filter(
            workspace=workspace).order_by('name')
        for name in custom_field_name:
            if lang == 'ja':
                data = {
                    'slug': 'invoice_contact_property_' + str(name.id),
                    'display': '請求書 - ' + selected_property + ' - 連絡先プロパティ - ' + name.name
                }
            else:
                data = {
                    'slug': 'invoice_contact_property_' + str(name.id),
                    'display': 'Invoice - ' + selected_property + ' - Contact Property - ' + name.name
                }
            contact_cf_data.append(data)
        if lang == 'ja':
            disp_total_amount = '請求書 - 合計金額'
            # Please recheck it and dont miss translate this (previously this is translated as '商品合計金額')
            disp_total_amount_item = '請求書 - アイテムの総数'

        else:
            disp_total_amount = 'Invoice - Total Amount'
            disp_total_amount_item = 'Invoice - Total Number of Items'

        context = {
            'action_node_id': action_node_id,
            'action_index': action_index,
            'invoice_total_amount': [{'slug': 'invoice_total_amount', 'display': disp_total_amount}],
            'invoice_total_number_item': [{'slug': 'invoice_total_number_item', 'display': disp_total_amount_item}],
            'contacts': contacts,
            'cf_data': cf_data,
            'contact_cf_data': contact_cf_data,

            'data_classification_header': selected_data_classification_header,
            'type_code_header': selected_type_code_header,
            'code_classification_header': selected_code_classification_header,
            'tf_req_code_header': selected_tf_req_code_header,
            'tf_req_code_kana_header': selected_tf_req_code_kana_header,
            'tf_date_header': selected_tf_date_header,
            'bank_code_header': selected_bank_code_header,
            'bank_name_kana_header': selected_bank_name_kana_header,
            'branch_code_header': selected_branch_code_header,
            'branch_name_kana_header': selected_branch_name_kana_header,
            'account_type_header': selected_account_type_header,
            'account_number_header': selected_account_number_header,
            'dummy_header': selected_dummy_header,

            'data_classification_main': selected_data_classification_main,
            'bank_code_main': selected_bank_code_main,
            'bank_name_kana_main': selected_bank_name_kana_main,
            'branch_code_main': selected_branch_code_main,
            'clearing_house_number_main': selected_clearing_house_number_main,
            'branch_name_kana_main': selected_branch_name_kana_main,
            'account_type_main': selected_account_type_main,
            'account_number_main': selected_account_number_main,
            'recepient_name_kana_main': selected_recepient_name_kana_main,
            'transfer_amount_main': selected_transfer_amount_main,
            'new_code_main': selected_new_code_main,
            'cust_code_1_main': selected_cust_code_1_main,
            'cust_code_2_main': selected_cust_code_2_main,
            'tf_specs_classification': selected_tf_specs_classification,
            'identification_code_main': selected_identification_code_main,
            'dummy_main': selected_dummy_main,
            'selected_date': selected_date,
            'contact_cf': selected_contact_cf,
            'exclude_status': selected_exclude_status,
            'download_with_pdf': download_with_pdf,
            'data_classification_footer': selected_data_classification_footer,
            'total_number_item_footer': selected_total_number_item_footer,
            'total_amount_footer': selected_total_amount_footer,
            'dummy_footer': selected_dummy_footer,

            'data_classification_footer_2': selected_data_classification_footer_2,
            'dummy_footer_2': selected_dummy_footer_2,

            'obj': Invoice(workspace=workspace)
        }
        if request.GET.get('contact_cf' + postfix):
            return render(request, 'data/invoice/create-invoice-zengin-list.html', context)
        else:
            return render(request, 'data/invoice/create-invoice-zengin-format.html', context)

    # Workflow related input
    submit_option = request.POST.get('submit-option')
    action_index = request.POST.get('action_index')
    action_node_id = request.POST.get('action_node_id')
    action_slug = request.POST.get('action_slug')
    at_id = request.POST.get('action_tracker_id')
    wat_id = request.POST.get('workflow_action_tracker_id')
    node = None

    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print('ActionNode does not exist')
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print('ActionTracker does not exist')
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print('WorkflowActionTracker does not exist')
            return HttpResponse(status=404)

    exclude_status = ['']
    postfix = ''
    if action_index:
        postfix = '-' + action_index

    date_select = request.POST.get('date_select' + postfix, '')
    contact_cf = request.POST.get('contact_cf' + postfix, '')

    if 'exclude_status_toggle' + postfix in request.POST:
        try:
            exclude_status = request.POST.getlist(
                'exclude_status' + postfix, [''])
        except:
            pass

    if 'download_with_pdf' + postfix in request.POST:
        try:
            download_with_pdf = request.POST.getlist(
                'download_with_pdf' + postfix, [''])
        except:
            pass
    else:
        download_with_pdf = None
    data_classification_header = request.POST.get(
        'data_classification_header' + postfix, '')
    type_code_header = request.POST.get('type_code_header' + postfix, '')
    code_classification_header = request.POST.get(
        'code_classification_header' + postfix, '')
    tf_req_code_header = request.POST.get('tf_req_code_header' + postfix, '')
    tf_req_code_kana_header = request.POST.get(
        'tf_req_code_kana_header' + postfix, '')
    tf_date_header = request.POST.get('tf_date_header' + postfix, '')
    bank_code_header = request.POST.get('bank_code_header' + postfix, '')
    bank_name_kana_header = request.POST.get(
        'bank_name_kana_header' + postfix, '')
    branch_code_header = request.POST.get('branch_code_header' + postfix, '')
    branch_name_kana_header = request.POST.get(
        'branch_name_kana_header' + postfix, '')
    account_type_header = request.POST.get('account_type_header' + postfix, '')
    account_number_header = request.POST.get(
        'account_number_header' + postfix, '')
    dummy_header = request.POST.get('dummy_header' + postfix, '')

    data_classification_main = request.POST.get(
        'data_classification_main' + postfix, '')
    bank_code_main = request.POST.get('bank_code_main' + postfix, '')
    bank_name_kana_main = request.POST.get('bank_name_kana_main' + postfix, '')
    branch_code_main = request.POST.get('branch_code_main' + postfix, '')
    clearing_house_number_main = request.POST.get(
        'clearing_house_number_main' + postfix, '')
    branch_name_kana_main = request.POST.get(
        'branch_name_kana_main' + postfix, '')
    account_type_main = request.POST.get('account_type_main' + postfix, '')
    account_number_main = request.POST.get('account_number_main' + postfix, '')
    recepient_name_kana_main = request.POST.get(
        'recepient_name_kana_main' + postfix, '')
    transfer_amount_main = request.POST.get(
        'transfer_amount_main' + postfix, '')
    new_code_main = request.POST.get('new_code_main' + postfix, '')
    cust_code_1_main = request.POST.get('cust_code_1_main' + postfix, '')
    cust_code_2_main = request.POST.get('cust_code_2_main' + postfix, '')
    tf_specs_classification = request.POST.get(
        'tf_specs_classification' + postfix, '')
    identification_code_main = request.POST.get(
        'identification_code_main' + postfix, '')
    dummy_main = request.POST.get('dummy_main' + postfix, '')

    data_classification_footer = request.POST.get(
        'data_classification_footer' + postfix, '')
    total_number_item_footer = request.POST.get(
        'total_number_item_footer' + postfix, '')
    total_amount_footer = request.POST.get('total_amount_footer' + postfix, '')
    dummy_footer = request.POST.get('dummy_footer' + postfix, '')

    data_classification_footer_2 = request.POST.get(
        'data_classification_footer_2' + postfix, '')
    dummy_footer_2 = request.POST.get('dummy_footer_2' + postfix, '')

    action_name = request.POST.get('action_name' + postfix, '')
    # POST
    print(request.POST)
    print('Running Create New Invoice Zengin Action:')
    try:
        if submit_option == 'save':
            input_data = {}
            node.valid_to_run = True
            if date_select:
                try:
                    date_start, date_end = date_select.split(' 〜 ')
                    less_date = datetime.strptime(
                        date_start, '%Y年%m月%d日').strftime('%Y-%m-%d')
                    greater_date = datetime.strptime(
                        date_end, '%Y年%m月%d日').strftime('%Y-%m-%d')
                    date_select = less_date + ' - ' + greater_date
                except:
                    pass
            input_data['date_select'] = date_select
            input_data['contact_cf'] = contact_cf
            input_data['exclude_status'] = exclude_status
            input_data['download_with_pdf'] = download_with_pdf

            input_data['data_classification_header'] = data_classification_header
            input_data['type_code_header'] = type_code_header
            input_data['code_classification_header'] = code_classification_header
            input_data['tf_req_code_header'] = tf_req_code_header
            input_data['tf_req_code_kana_header'] = tf_req_code_kana_header
            input_data['tf_date_header'] = tf_date_header

            input_data['bank_code_header'] = bank_code_header
            input_data['bank_name_kana_header'] = bank_name_kana_header
            input_data['branch_code_header'] = branch_code_header
            input_data['branch_name_kana_header'] = branch_name_kana_header
            input_data['account_type_header'] = account_type_header
            input_data['account_number_header'] = account_number_header
            input_data['dummy_header'] = dummy_header

            input_data['data_classification_main'] = data_classification_main

            input_data['bank_code_main'] = bank_code_main
            input_data['bank_name_kana_main'] = bank_name_kana_main
            input_data['branch_code_main'] = branch_code_main

            input_data['clearing_house_number_main'] = clearing_house_number_main

            input_data['branch_name_kana_main'] = branch_name_kana_main
            input_data['account_type_main'] = account_type_main
            input_data['account_number_main'] = account_number_main
            input_data['recepient_name_kana_main'] = recepient_name_kana_main
            input_data['transfer_amount_main'] = transfer_amount_main

            input_data['new_code_main'] = new_code_main

            input_data['cust_code_1_main'] = cust_code_1_main

            input_data['cust_code_2_main'] = cust_code_2_main
            input_data['tf_specs_classification'] = tf_specs_classification
            input_data['identification_code_main'] = identification_code_main
            input_data['dummy_main'] = dummy_main
            input_data['data_classification_footer'] = data_classification_footer

            input_data['total_number_item_footer'] = total_number_item_footer
            input_data['total_amount_footer'] = total_amount_footer

            input_data['dummy_footer'] = dummy_footer
            input_data['data_classification_footer_2'] = data_classification_footer_2
            input_data['dummy_footer_2'] = dummy_footer_2

            input_data['action_name'] = action_name

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW).order_by(
                'order', 'created_at').first()
            if module:
                module_slug = module.slug
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
            return redirect(reverse('main', host='app'))

        else:
            print("Action Tracker: ", at.input_data)
            ## MAIN ACTION ##
            workflow_history = node.workflow_history
            # Ordering Inputs
            inputs_data_header = []
            inputs_data_main = []
            inputs_data_footer = []

            date_select = node.input_data['date_select']
            contact_cf = node.input_data['contact_cf']
            exclude_status = node.input_data['exclude_status']
            download_with_pdf = node.input_data['download_with_pdf']

            inputs_data_header.append(
                node.input_data['data_classification_header'])
            inputs_data_header.append(node.input_data['type_code_header'])
            inputs_data_header.append(
                node.input_data['code_classification_header'])
            inputs_data_header.append(node.input_data['tf_req_code_header'])
            inputs_data_header.append(
                node.input_data['tf_req_code_kana_header'])

            tf_date_header = node.input_data['tf_date_header']
            try:
                tf_date_header = datetime.strptime(
                    tf_date_header, '%Y-%m-%d').strftime('%m%d')
            except:
                tf_date_header = datetime.strptime(
                    tf_date_header, '%Y年%m月%d日').strftime('%m%d')
            inputs_data_header.append(tf_date_header)

            inputs_data_header.append(node.input_data['bank_code_header'])
            inputs_data_header.append(node.input_data['bank_name_kana_header'])
            inputs_data_header.append(node.input_data['branch_code_header'])
            inputs_data_header.append(
                node.input_data['branch_name_kana_header'])
            inputs_data_header.append(node.input_data['account_type_header'])
            inputs_data_header.append(node.input_data['account_number_header'])
            inputs_data_header.append(node.input_data['dummy_header'])

            inputs_data_main.append(
                node.input_data['data_classification_main'])
            inputs_data_main.append(node.input_data['bank_code_main'])
            inputs_data_main.append(node.input_data['bank_name_kana_main'])
            inputs_data_main.append(node.input_data['branch_code_main'])
            inputs_data_main.append(node.input_data['branch_name_kana_main'])
            inputs_data_main.append(
                node.input_data['clearing_house_number_main'])
            inputs_data_main.append(node.input_data['account_type_main'])
            inputs_data_main.append(node.input_data['account_number_main'])
            inputs_data_main.append(
                node.input_data['recepient_name_kana_main'])
            inputs_data_main.append(node.input_data['transfer_amount_main'])
            inputs_data_main.append(node.input_data['new_code_main'])
            inputs_data_main.append(node.input_data['cust_code_1_main'])
            inputs_data_main.append(node.input_data['cust_code_2_main'])
            inputs_data_main.append(node.input_data['tf_specs_classification'])
            inputs_data_main.append(
                node.input_data['identification_code_main'])
            inputs_data_main.append(node.input_data['dummy_main'])

            inputs_data_footer.append(
                node.input_data['data_classification_footer'])
            inputs_data_footer.append(
                node.input_data['total_number_item_footer'])
            inputs_data_footer.append(node.input_data['total_amount_footer'])
            inputs_data_footer.append(node.input_data['dummy_footer'])

            inputs_data_footer.append(
                node.input_data['data_classification_footer_2'])
            inputs_data_footer.append(node.input_data['dummy_footer_2'])

            list_price_invoice = []

            if ' - ' in date_select:
                date_start, date_end = date_select.split(' - ')
                less_date = date_start
                greater_date = date_end
            else:
                date_start, date_end = date_select.split(' 〜 ')
                less_date = datetime.strptime(
                    date_start, '%Y年%m月%d日').date().strftime('%Y-%m-%d')
                greater_date = datetime.strptime(
                    date_end, '%Y年%m月%d日').date().strftime('%Y-%m-%d')

            query = Q(
                workspace=workspace,
                start_date__gte=less_date,
                start_date__lte=greater_date,
                usage_status='active')  # only accept active invoices

            if is_valid_uuid(contact_cf[8:]):
                custom_relation = 'invoice_custom_field_relations'
                customfield_name = InvoiceNameCustomField.objects.filter(
                    id=contact_cf[8:]).first()
                filter_id = str(customfield_name.id)
                _filter_dict = {
                    custom_relation+"__field_name__id": filter_id,
                    custom_relation+"__field_name__type": customfield_name.type,
                }
                query &= Q(**{custom_relation+"__value__isnull": False})
            else:
                _filter_dict = {
                    'contact__isnull': False,
                    'company__isnull': True,
                }
            query &= Q(**_filter_dict)

            invoices = Invoice.objects.filter(query)

            # == adding exclude so customers pull is match ##
            if exclude_status and exclude_status != [''] and exclude_status != "['']":
                # Handle both list and string formats
                if isinstance(exclude_status, str):
                    try:
                        exclude_status_list = ast.literal_eval(exclude_status)
                    except (ValueError, SyntaxError):
                        # If ast.literal_eval fails, treat as a single status
                        exclude_status_list = [exclude_status] if exclude_status else []
                elif isinstance(exclude_status, list):
                    exclude_status_list = exclude_status
                else:
                    exclude_status_list = []
                
                # Filter out empty strings
                exclude_status_list = [status for status in exclude_status_list if status and status != '']
                
                if exclude_status_list:
                    invoices = invoices.exclude(status__in=exclude_status_list)
            # Remove invoice that din't have total price yet
            invoices = invoices.exclude(total_price=0.0)
            customer_list = []
            for invoice in invoices:
                try:
                    if contact_cf[8:] == 'customers':
                        contact_ = invoice.contact
                    else:
                        contact_id = invoice.invoice_custom_field_relations.get(
                            field_name=customfield_name).value
                        contact_ = Contact.objects.get(id=contact_id)
                except:
                    pass

                if contact_ not in customer_list:
                    customer_list.append(contact_)
            objects_combined = []
            if type(customer_list) == list:
                for customer in customer_list:
                    price_invoice = 0.0
                    if contact_cf[8:] == 'customers':
                        contact_ = customer
                        query = Q(contact=customer)
                    else:
                        query = Q(
                            **{custom_relation+"__value": str(customer.id)})
                    invoices_customer = invoices.filter(query)
                    if download_with_pdf:
                        msg, object_combined, amount_aggregated = aggregate_commerce_object(
                            objects=invoices_customer, object_type=TYPE_OBJECT_INVOICE)
                        objects_combined.append(object_combined)
                    for invoice_ in invoices_customer:
                        if invoice_.total_price:
                            price_invoice += invoice_.total_price
                    if price_invoice and price_invoice != 0.0 and type(price_invoice) == float:
                        list_price_invoice.append(price_invoice)
                    else:
                        print("Error in price: ", price_invoice)
                        send_discord_notification(
                            'Error in Zengin Action: ', price_invoice)
            else:
                return HttpResponse(status=500)

            if list_price_invoice:
                total_amount_invoices = 0.0
                total_amount_for_each_invoice = []
                for total_price in list_price_invoice:
                    if total_price:
                        total_amount_invoices += int(total_price)
                    else:
                        continue
                    amount_for_each_invoice = int(
                        total_price)
                    total_amount_for_each_invoice.append(
                        amount_for_each_invoice)

                zengin_text = ''
                format_space_spec = 0

                # Header
                for index, data in enumerate(inputs_data_header):
                    text_ = zengin_format_strings(
                        ZENGIN_HEADER_SPEC[index], data, format_space_spec)
                    zengin_text += text_
                    format_space_spec += 1

                print("First SPEC: ", format_space_spec - 1)
                zengin_text += '\n'

                sanka_contact_field = ['name', 'first_name',
                                       'last_name', 'email', 'phone_number']
                # Data Record
                for index_contact, customer in enumerate(customer_list):
                    for index, data in enumerate(inputs_data_main):
                        text_ = ''
                        contact = customer
                        if 'invoice_contact_property_' in data:
                            n, contact_data = data.split(
                                'invoice_contact_property_')
                            if contact_data in sanka_contact_field:
                                if contact_data.lower() == 'name':  # If full name selected
                                    contact_data = customer_converter(
                                        contact, lang, with_id=False)
                                    text_ = zengin_format_strings(
                                        ZENGIN_MAIN_SPEC[index], contact_data, format_space_spec)
                                    zengin_text += text_
                                    format_space_spec += 1
                                    continue

                                if contact_data.lower() == 'first_name':
                                    contact_data = 'name'
                                contact_data = getattr(
                                    contact, contact_data.lower(), '')
                                text_ = contact_data if contact_data else ''
                            elif is_valid_uuid(contact_data):
                                contact_cf_name = ContactsNameCustomField.objects.filter(
                                    id=contact_data)
                                if contact_cf_name:
                                    contact_cf_name = contact_cf_name.first()
                                    contact_cf_value = ContactsValueCustomField.objects.filter(
                                        field_name=contact_cf_name, contact=contact)
                                    if contact_cf_value:
                                        contact_cf_value = contact_cf_value.first()
                                        contact_cf_value = contact_cf_value.value
                                    else:
                                        contact_cf_value = ''
                                    text_ = contact_cf_value
                        elif 'invoice_total_amount' in data:
                            try:
                                text_ = total_amount_for_each_invoice[index_contact]
                            except:
                                pass
                        else:
                            text_ = data

                        text_ = zengin_format_strings(
                            ZENGIN_MAIN_SPEC[index], text_, format_space_spec)
                        zengin_text += text_
                        format_space_spec += 1

                    zengin_text += '\n'
                    if index_contact < (len(customer_list) - 1):
                        format_space_spec = 13

                print("Mid SPEC: ", format_space_spec - 1)

                # Footer
                for index, data in enumerate(inputs_data_footer):
                    text_ = ''

                    if index == 4:
                        zengin_text += '\n'

                    if 'invoice_total_amount' in data:
                        text_ = str(int(total_amount_invoices))
                    elif 'invoice_total_number_item' in data:
                        num = len(list_price_invoice)
                        text_ = str(num)
                    else:
                        text_ = data

                    # Trailer Record
                    if index < 4:
                        text_ = zengin_format_strings(
                            ZENGIN_FOOTER_1_SPEC[index], text_, format_space_spec)
                    # End Record
                    else:
                        index = index - 4
                        text_ = zengin_format_strings(
                            ZENGIN_FOOTER_2_SPEC[index], text_, format_space_spec)

                    zengin_text += text_
                    format_space_spec += 1

                filename = f"{at.created_at.strftime('%Y%m%d')[2:]}Zengin_File.txt"
                print("LAST SPEC: ", format_space_spec - 1)
                at.status = 'success'
                at.output_data = {}
                time_elapsed_delta = timezone.now() - at.created_at
                total_seconds = int(time_elapsed_delta.total_seconds())
                minutes = total_seconds // 60
                seconds = total_seconds % 60
                formatted_time_elapsed = f"{minutes:02d}:{seconds:02d}"

                if lang == 'ja':
                    # No need to add row character checker
                    # at.output_data['各行の合計スペック'] = [f"{i+1}. {len(row)}" for i, row in enumerate(zengin_text.split('\\n'))]
                    at.output_data['顧客合計請求書'] = str(
                        len(list_price_invoice))
                    at.output_data['顧客リスト'] = ', '.join(
                        str(customer.name) for customer in customer_list)
                    at.output_data['合計金額'] = str(
                        int(total_amount_invoices))
                    at.output_data['処理時間'] = formatted_time_elapsed
                    # Activate this if needs the proper japanese name for the file name
                    # filename = "全銀フォーマットファイル.txt"

                elif lang == 'en':
                    # No need to add row character checker
                    # at.output_data['Total Specs each row'] = [f"{i+1}. {len(row)}" for i, row in enumerate(zengin_text.split('\\n'))]
                    at.output_data['Total Customer Aggregated Invoice'] = str(
                        len(list_price_invoice))

                    at.output_data['List Customers'] = ', '.join(
                        str(customer.name) for customer in customer_list)

                    at.output_data['Total Amount'] = str(
                        int(total_amount_invoices))
                    at.output_data['Processing Time'] = formatted_time_elapsed

                file_ = save_string_as_txt(
                    user, workspace, text=zengin_text, filename=filename)
                at.result_link = bg_job_to_sanka_url(
                    type_http + reverse('preview_generated_file', host='app', kwargs={'id': str(file_.id)}))
                at.completed_at = timezone.now()

                at.save()

                # Send Email
                # we'll use this for signup users without invitation code
                if lang == 'ja':
                    mail_subject = 'Sanka - 全銀フォーマットへの請求書エクスポート'
                    filename = f"{at.completed_at.strftime('%Y%m%d')[2:]}全銀フォーマットファイル.txt"
                    message = render_to_string(
                        'data/email/export-zengin-ja.html')
                else:
                    mail_subject = 'Sanka - Invoice Export to Zengin Format'
                    filename = f"{at.completed_at.strftime('%Y%m%d')[2:]}Zengin_Format_File.txt"
                    message = render_to_string('data/email/export-zengin.html')
                user = request.user
                attach_data = StringIO(zengin_text).getvalue()
                from_email = 'Sanka <<EMAIL>>'

                try:
                    to_email = [user.email]

                    email_message = EmailMessage(
                        mail_subject,
                        message,
                        from_email,
                        to_email
                    )
                    mimetype = 'text/plain'
                    email_message.attach(filename, attach_data, mimetype)
                    if download_with_pdf:
                        try:
                            logger.info("Downloading PDF for invoices" +
                                        str(objects_combined))
                            file_tuple = []
                            for obj in objects_combined:
                                try:
                                    invoice = Invoice.objects.get(id=obj.id)
                                    invoice_id = f"{invoice.id_inv:04d}"
                                    buffer_pdf = commerce_pdf_download(
                                                request=request,
                                                id=str(invoice.id),
                                                object_type=TYPE_OBJECT_INVOICE,
                                            )
                                    file_tuple.append(
                                        (
                                            invoice_id + ".pdf", BytesIO(buffer_pdf.content)
                                        )
                                    )
                                    invoice.delete()
                                except Exception as e:
                                    logger.info(
                                        "Error while Zipping PDF for invoices: " + str(e))

                            zip_data = generate_zip(file_tuple)
                            mimetype = 'application/zip'
                            email_message.attach(
                                'Invoices.zip', zip_data, mimetype)
                        except Exception as e:
                            logger.info(
                                "Error while downloading PDF for invoices: " + str(e))
                    res = email_message.send(fail_silently=False)
                    logger.info("Email already sent: " + str(res))
                    if lang == 'ja':
                        at.output_data['出力メッセージ'] = "全銀ファイルはすでにあなたのメールに送信されています"
                    elif lang == 'en':
                        at.output_data['Output Message'] = "Zengin File already sent to your email"

                except Exception as e:
                    logger.info("Error while sending email: " + str(e))
                    logger.info(traceback.format_exc())
                    if lang == 'ja':
                        at.output_data['出力メッセージ'] = "全銀ファイルをメールに送信中にエラーが発生しました"
                    elif lang == 'en':
                        at.output_data['Output Message'] = "Error while sending Zengin File to your Email"

                at.save()

                next_node = None
                if node:
                    next_node = node.next_node
                if next_node:
                    next_at = ActionTracker.objects.get(
                        node=next_node, workflow_action_tracker=wat)
                    transfer_output_to_target_input(at, next_at)
                    trigger_next_action(current_action_tracker=at, workflow_action_tracker=wat,
                                        current_node=node, user_id=str(request.user.id), lang=lang)
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
                module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW).order_by(
                    'order', 'created_at').first()
                if module:
                    module_slug = module.slug
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history')
                return redirect(reverse('main', host='app'))
            else:
                at.status = 'failed'
                at.input_data = {
                    f"{'エラーログ:選択した日付から請求書に関連する請求書はありません - ' if lang == 'ja' else 'Error Log: There is no Invoice related from selected date - '}" + date_select: str(len(list_price_invoice)),
                }
                at.output_data = {}
                at.save()
            return HttpResponse(status=500)
    except Exception as e:
        traceback.print_exc()
        print(f"Zengin error 3: {e}")
        if submit_option == 'run':
            if node:
                at.input_data = {
                    f"{'エラーログ' if lang == 'ja' else 'Error Log'}": str(e),
                }
                at.output_data = {}
                at.save()
        return HttpResponse(status=500)


def zengin_format_strings(spec, word, format_space_spec):
    # Initialize an empty result string
    formatted_str = ""
    try:
        if ZENGIN_SPEC_FORMAT_DATA[format_space_spec] == 1:  # Number Format
            try:
                formatted_str = f"%0{spec}d" % int(word)
            except:
                formatted_str = f"%0{spec}d" % 0
        else:  # Str Format
            if word:
                if '\t' in word:  # Remove all '\t' characters from the word
                    word = word.replace('\t', ' ')
            else:
                word = ''
            word = word[0:spec]
            formatted_str += word.ljust(spec)
    except Exception as e:
        traceback.print_exc()
        print("Error in Zengin Action - Spec Format not correct: ",
              e, ' - ', format_space_spec)
        error_str = f"{word}, - {e}"
        send_discord_notification(
            'Error in Zengin Action - Spec Format not correct: ', error_str)
    return formatted_str


def old_zengin_format_strings(spec, word, format_space_spec):
    # Initialize an empty result string
    formatted_str = ""
    try:
        # Check the 'word' string is it in integer or not
        word = int(word)
        formatted_str = f"%0{spec}d" % word
    except:
        if '\t' in word:  # Remove all '\t' characters from the word
            word = word.replace('\t', ' ')
        # Accept it as string
        # Loop over the words and their corresponding length specifications
        word = word[0:spec]
        formatted_str += word.ljust(spec)
    return formatted_str


def save_string_as_txt(user: User, workspace, text: str, filename: str = "String_data.txt"):
    # Convert string to bytes stream
    byte_stream = io.BytesIO(text.encode("utf-8"))
    # Wrap it in a Django File object
    uploaded_file = File(byte_stream, name=filename)

    storage_instance = GeneralStorage(
        user=user,
        workspace=workspace,
        file_type='text',  # Or whatever your `GeneralFileType` choice is
    )
    storage_instance.file.save(filename, uploaded_file)  # Save to model
    storage_instance.save()
    return storage_instance
