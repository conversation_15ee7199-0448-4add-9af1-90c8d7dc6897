import traceback
from data.models import (
    Channel, User, TransferHistory,
    ShopTurboOrders, ShopTurboOrdersPlatforms, ShopTurboItemsOrders, ShopTurboItemsPlatforms, ShopTurboItemsPrice,
    ShopTurboItems, ContactsPlatforms, Contact, ShopTurboOrdersMappingFields, CompanyPlatforms, Company, CompanyValueCustomField,
    ShopTurboOrdersValueCustomField, ShopTurboOrdersNameCustomField, Notification, models, AssociationLabel, AssociationLabelObject, CustomObjectPlatforms, DealsPlatforms,
    ShopTurboItemsOrdersNameCustomField, ShopTurboItemsOrdersValueCustomField
)
from data.constants.properties_constant import TYPE_OBJECT_COMPANY, TYPE_OBJECT_CONTACT, TYPE_OBJECT_CASE, TYPE_OBJECT_ORDER
from django.utils import timezone
from simple_salesforce import Salesforce
from django.utils.dateparse import parse_datetime
from data.models.customer import CompanyNameCustomField
from utils.salesforce.utility import refresh_salesforce_token, get_bulk_owner_emails_by_owner_ids
from utils.meter import has_quota, ORDER_USAGE_CATEGORY


def get_opportunity_mapping_fields(channel_id):
    """
    Get available fields/columns for Salesforce Opportunity object mapping.

    Args:
        channel_id (str): The ID of the channel to use for Salesforce connection

    Returns:
        list: A list of dictionaries containing field name, label, and type
    """
    EXCEPTION_FIELDS = [
        'SystemModstamp', 'UserRecordAccessId',
        'IsDeleted', 'LastActivityDate', 'LastViewedDate',
        'LastReferencedDate', 'CreatedById', 'LastModifiedById',
        'HasOpportunityLineItem', 'Pricebook2Id', 'HasOpenActivity',
        'HasOverdueTask', 'IsClosed', 'IsWon', 'ForecastCategoryName'
    ]

    channel = Channel.objects.get(id=channel_id)

    try:
        access_token = refresh_salesforce_token(channel_id)
        sf = Salesforce(instance_url=channel.account_id,
                        session_id=access_token)

        # Query field definitions for the Opportunity object
        query = """
            SELECT QualifiedApiName, DataType, Label
            FROM FieldDefinition
            WHERE EntityDefinition.QualifiedApiName = 'Opportunity'
        """
        fields = sf.query_all(query)

        # Format the fields, filtering out exception fields
        opportunity_fields = [
            {'name': field['QualifiedApiName'],
             'label': field['Label'],
             'type': field['DataType']}
            for field in fields['records']
            if (field['QualifiedApiName'] not in EXCEPTION_FIELDS and
                not field['QualifiedApiName'].startswith('_')) or
            field['QualifiedApiName'].endswith('__c')
        ]

        return opportunity_fields

    except Exception as e:
        traceback.print_exc()
        print(f"[ERROR] Failed to fetch Opportunity fields: {e}")

    return None

def get_opportunity_line_item_mapping_fields(channel_id): # Custom Fields
    """
    Get available fields/columns for Salesforce OpportunityLineItem object mapping.

    Args:
        channel_id (str): The ID of the channel to use for Salesforce connection

    Returns:
        list: A list of dictionaries containing field name, label, and type
    """
    EXCEPTION_FIELDS = [
        'SystemModstamp', 'UserRecordAccessId',
        'IsDeleted', 'LastActivityDate', 'LastViewedDate',
        'LastReferencedDate', 'CreatedById', 'LastModifiedById',
        'HasOpenActivity', 'HasOverdueTask'
    ]

    channel = Channel.objects.get(id=channel_id)

    try:
        access_token = refresh_salesforce_token(channel_id)
        sf = Salesforce(instance_url=channel.account_id,
                        session_id=access_token)

        # Query field definitions for the OpportunityLineItem object
        query = """
            SELECT QualifiedApiName, DataType, Label
            FROM FieldDefinition
            WHERE EntityDefinition.QualifiedApiName = 'OpportunityLineItem'
            AND QualifiedApiName LIKE '%__c'
        """
        fields = sf.query_all(query)

        # Format the fields, filtering out exception fields
        opportunity_line_item_fields = [
            {'name': field['QualifiedApiName'],
             'label': field['Label'],
             'type': field['DataType']}
            for field in fields['records']
            if (field['QualifiedApiName'] not in EXCEPTION_FIELDS and
                not field['QualifiedApiName'].startswith('_')) or
            field['QualifiedApiName'].endswith('__c')
        ]

        return opportunity_line_item_fields

    except Exception as e:
        traceback.print_exc()
        print(f"[ERROR] Failed to fetch OpportunityLineItem fields: {e}")

    return None

def import_salesforce_opportunities(channel_id, field_mapping=None, user_id=None, mapping_custom_fields_association=None):
    """
    Import Salesforce opportunities as orders with custom field mapping

    Args:
        channel_id (str): The ID of the channel to use for Salesforce connection
        field_mapping (dict): Optional mapping of Salesforce fields to Sanka fields
                             Format: {'SalesforceField': 'sanka_field', ...}
        user_id (str): Optional user ID for task history tracking
    """
    print("Importing Salesforce opportunities...")
    print("Field mapping:", field_mapping)
    # Get channel and workspace info
    channel = Channel.objects.get(id=channel_id)
    workspace = channel.workspace
    user = User.objects.get(id=user_id) if user_id else None

    # Fetch or create task history for tracking
    task = None
    if user_id:
        task = TransferHistory.objects.create(
            workspace=workspace,
            user=user,
            type='import_order',
            status='Processing',
            progress=0,
            channel = channel or None
        )
    else:
        task = TransferHistory.objects.filter(
            workspace=workspace, type='import_order', 
            channel = channel or None
            ).order_by('-created_at').first()
        if not task:
            task = TransferHistory.objects.create(
                workspace=workspace,
                type='import_order',
                status='Processing',
                progress=0,
                channel = channel or None
            )

    try:
        # Authenticate with Salesforce
        access_token = refresh_salesforce_token(channel_id)
        sf = Salesforce(instance_url=channel.account_id,
                        session_id=access_token)

        task.progress = 10
        task.save()

        # If no mapping provided, try to get from database
        if not field_mapping:
            try:
                mapping_obj = ShopTurboOrdersMappingFields.objects.get(
                    workspace=workspace,
                    platform=channel.integration.slug
                )
                if mapping_obj.input_data:
                    field_mapping = {}
                    for sf_field, mapping_info in mapping_obj.input_data.items():
                        # Handle different mapping data structures
                        if isinstance(mapping_info, dict) and mapping_info.get('value'):
                            # Dictionary format with 'value' key
                            field_mapping[sf_field] = mapping_info.get('value')
                        elif isinstance(mapping_info, str):
                            # Direct string mapping
                            field_mapping[sf_field] = mapping_info
            except (ShopTurboOrdersMappingFields.DoesNotExist, KeyError, AttributeError):
                # Use default mapping if nothing found
                field_mapping = {
                    'Name': 'name',
                    'Amount': 'total_price',
                    'CloseDate': 'order_at',
                    'StageName': 'status'
                }

        task.progress = 20
        task.save()

        # Build query based on mapping fields
        fields_to_query = list(field for field in field_mapping.keys() if '|line_item' not in field)

        required_fields = ['Id', 'AccountId']
        for field in required_fields:
            if field not in fields_to_query:
                fields_to_query.append(field)
        fields_str = ', '.join(fields_to_query)

        # Query Opportunities
        query = f"SELECT {fields_str} FROM Opportunity"
        print('Query: ', query)
        opportunities = sf.query_all(query)

        task.progress = 30
        task.save()

        total_opportunities = len(opportunities['records'])
        processed = 0

        print(f"Found {total_opportunities} opportunities to import")

        # Get order model fields to check if mapping exists in the model
        order_model_fields = [
            field.name for field in ShopTurboOrders._meta.get_fields() if not field.is_relation]

        # Get All Owner ID
        owner_ids = [record.get('OwnerId')
                     for record in opportunities['records']]
        owner_ids = list(set(owner_ids))
        owner_emails = get_bulk_owner_emails_by_owner_ids(
            channel_id, owner_ids)

        for opportunity in opportunities['records']:
            order_id = opportunity.get('Id')
            account_id = opportunity.get('AccountId')

            # Extract basic values directly from opportunity
            order_display_name = opportunity.get('Name', order_id)
            currency = "USD"  # Salesforce does not store currency per record by default
            total_price = opportunity.get('Amount', 0)
            order_at = opportunity.get('CloseDate')

            # Process values based on mapping
            order_data = {}
            for sf_field, sanka_field in field_mapping.items():
                if '|line_item' in sf_field:
                    continue

                if sf_field in opportunity:
                    value = opportunity.get(sf_field)

                    if value is not None:
                        # Handle date fields
                        if sf_field == 'CloseDate':
                            value = parse_datetime(value) if value else None
                        # Handle numeric fields
                        elif sf_field in ['Amount']:
                            value = float(value) if value else 0
                        # Handle stage name mapping to status
                        elif sf_field == 'StageName':
                            # Map Salesforce opportunity stages to order statuses
                            stage_map = {
                                'Prospecting': 'active',
                                'Qualification': 'active',
                                'Needs Analysis': 'active',
                                'Value Proposition': 'active',
                                'Id. Decision Makers': 'active',
                                'Perception Analysis': 'active',
                                'Proposal/Price Quote': 'active',
                                'Negotiation/Review': 'active',
                                'Closed Won': 'completed',
                                'Closed Lost': 'cancelled'
                            }
                            value = stage_map.get(value, 'active')

                        order_data[sanka_field] = value

            # Override basic values with mapped values if available
            if 'name' in order_data:
                order_display_name = order_data['name']
            if 'total_price' in order_data:
                total_price = order_data['total_price']
            if 'order_at' in order_data:
                order_at = order_data['order_at']
            if 'currency' in order_data:
                currency = order_data['currency']
            status = order_data.get('status', 'active')

            # Map delivery status based on stage
            stage_name = opportunity.get('StageName', '')
            if stage_name in ['Closed Won']:
                delivery_status = 'order_delivered'
            elif stage_name in ['Closed Lost']:
                delivery_status = 'order_cancelled'
            else:
                delivery_status = 'order_processing'

            # Link order with OrderPlatforms
            order_platform, _ = ShopTurboOrdersPlatforms.objects.get_or_create(
                channel=channel,
                platform_order_id=order_id
            )

            if order_platform.order:
                shopturbo_order = order_platform.order
            else:
                # Check quota before creating new order
                if not has_quota(workspace, ORDER_USAGE_CATEGORY):
                    for user in workspace.user.all():
                        Notification.objects.create(
                            workspace=workspace,
                            user=user,
                            message="Order limit exceeded. Upgrade your plan or archive old records.",
                            type="error"
                        )
                    task.status = 'Failed'
                    task.save()
                    return False

                # Create new order
                shopturbo_order = ShopTurboOrders.objects.create(
                    workspace=workspace,
                    currency=currency,
                    platform='salesforce',
                    status=status,
                    order_type='item_order'
                )

            # Update order platform
            order_platform.order = shopturbo_order
            order_platform.order_display_name = order_display_name

            # Ensure order_at is never null (use current time as fallback)
            if order_at:
                order_platform.order_at = parse_datetime(order_at)
            else:
                order_platform.order_at = timezone.now()

            order_platform.save()

            # Update order fields
            if not total_price:
                total_price = 0
            shopturbo_order.item_price_order = float(total_price)
            shopturbo_order.total_price = float(total_price)
            shopturbo_order.total_price_without_tax = float(
                total_price)  # Salesforce does not store tax separately
            shopturbo_order.currency = currency
            shopturbo_order.delivery_status = delivery_status
            shopturbo_order.order_at = order_platform.order_at

            # Apply any additional mapped fields not already handled
            for sf_field, sanka_field in field_mapping.items():
                if '|line_item' in sf_field:
                    continue
                sf_value = opportunity.get(sf_field)

                # Replace OwnerId with email if available
                if sf_field == 'OwnerId' and sf_value:
                    owner_email = owner_emails.get(sf_value)
                    if owner_email:
                        sf_value = owner_email

                if sf_value is not None and sanka_field not in ['name', 'total_price', 'order_at', 'status', 'currency']:
                    # Check if the field exists in Order model
                    if sanka_field in order_model_fields:
                        # Handle specific field types
                        if isinstance(getattr(ShopTurboOrders, sanka_field, None), models.FloatField):
                            sf_value = float(sf_value) if sf_value else 0
                        elif isinstance(getattr(ShopTurboOrders, sanka_field, None), models.IntegerField):
                            sf_value = int(sf_value) if sf_value else 0
                        elif isinstance(getattr(ShopTurboOrders, sanka_field, None), models.DateTimeField):
                            sf_value = parse_datetime(
                                sf_value) if sf_value else None
                        elif isinstance(getattr(ShopTurboOrders, sanka_field, None), models.BooleanField):
                            sf_value = bool(sf_value)

                        # Set the field directly on the order object
                        setattr(shopturbo_order, sanka_field, sf_value)
                    else:
                        # Check if this is a custom field
                        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
                            workspace=workspace,
                            name=sanka_field
                        ).first()

                        if custom_field:
                            # Create or update the custom field value
                            custom_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                                field_name=custom_field,
                                orders=shopturbo_order
                            )
                            custom_value.value = sf_value
                            custom_value.save()
                        else:
                            # Store in memo field if not a model field or custom field
                            memo = shopturbo_order.memo or ''
                            memo += f"\n{sanka_field}: {sf_value}"
                            shopturbo_order.memo = memo.strip()

            print(f'[DEBUG] mapping_custom_fields_association: {mapping_custom_fields_association}')
            for salesforce_property, sanka_association in mapping_custom_fields_association.items():
                object_platform = None
                obj = None

                association_label = AssociationLabel.objects.filter(id=sanka_association).first()
                if association_label:
                    object_targets = association_label.object_target.split(',')
                    salesforce_value = opportunity.get(salesforce_property)
                    if salesforce_value:
                        if TYPE_OBJECT_COMPANY in object_targets:
                            object_platform = CompanyPlatforms.objects.filter(
                                channel=channel, platform_id=salesforce_value).first()

                            if object_platform:
                                obj = object_platform.company

                        if TYPE_OBJECT_CONTACT in object_targets:
                            object_platform = ContactsPlatforms.objects.filter(
                                channel=channel, platform_id=salesforce_value).first()
                            
                            if object_platform:
                                obj = object_platform.contact

                        if TYPE_OBJECT_ORDER in object_targets:
                            object_platform = ShopTurboOrdersPlatforms.objects.filter(
                                channel=channel, platform_order_id=salesforce_value).first()
                            
                            if object_platform:
                                obj = object_platform.order

                        if TYPE_OBJECT_CASE in object_targets:
                            object_platform = DealsPlatforms.objects.filter(
                                channel=channel, platform_id=salesforce_value).first()
                            
                            if object_platform:
                                obj = object_platform.deal

                        if not object_platform:
                            object_platform = CustomObjectPlatforms.objects.filter(
                                channel=channel, platform_id=salesforce_value).first()
                            if object_platform:
                                obj = object_platform.object

                        print(f'[DEBUG] Object platform: {object_platform} {salesforce_value}')
                        print(f'[DEBUG] Object found: {obj}')

                        if obj:
                            AssociationLabelObject.reset_associations_for_object(
                                shopturbo_order, 
                                workspace, 
                                association_label
                            )
                            AssociationLabelObject.create_association(shopturbo_order, obj, workspace, association_label)

            shopturbo_order.save()

            # Clear existing line items before importing new ones
            shopturbo_order.shopturboitemsorders_set.all().delete()
            line_item_fields_to_query = list(field.replace("|line_item", "") for field in field_mapping.keys() if '|line_item' in field)

            # Default fields
            default_fields = [
                'Id', 'Product2Id', 'Product2.Name', 'Product2.StockKeepingUnit',
                'Quantity', 'UnitPrice'
            ]

            # Merge default and custom fields, avoid duplicates
            all_fields = list(dict.fromkeys(default_fields + line_item_fields_to_query))

            # Join fields for SELECT statement
            select_clause = ', '.join(all_fields)

            # Query OpportunityLineItems (Order Items)
            query_items = f"""
            SELECT {select_clause}
            FROM OpportunityLineItem 
            WHERE OpportunityId = '{order_id}'
            """
            items = sf.query_all(query_items)['records']

            for item in items:
                item_id = item.get('Product2', {}).get('StockKeepingUnit')
                if not item_id:
                    item_id = item.get('Product2Id')

                item_name = item.get('Product2', {}).get('Name')
                number_of_item = item.get('Quantity')
                item_price = item.get('UnitPrice')

                item_platform, _ = ShopTurboItemsPlatforms.objects.get_or_create(
                    channel=channel,
                    platform_id=item_id,
                    platform_type='salesforce'
                )

                shopturbo_item_order, _ = ShopTurboItemsOrders.objects.get_or_create(
                    platform_item_id=item_id,
                    order=shopturbo_order,
                    order_platform=order_platform
                )

                if item_platform.item:
                    shopturbo_item = item_platform.item
                else:
                    if shopturbo_item_order.item:
                        shopturbo_item = shopturbo_item_order.item
                    else:
                        shopturbo_item = ShopTurboItems.objects.create(
                            workspace=workspace,
                            platform=channel.integration.slug,
                            product_id=item_id,
                        )

                shopturbo_item_order.number_item = float(number_of_item)
                shopturbo_item_order.item_price_order = float(item_price)
                shopturbo_item_order.currency = currency
                shopturbo_item_order.save()

                shopturbo_item.name = item_name
                shopturbo_item.price = float(item_price)
                shopturbo_item.currency = currency
                shopturbo_item.status = 'active'
                shopturbo_item.save()

                item_platform.platform_id = item_id
                item_platform.item = shopturbo_item
                item_platform.save()

                price, _ = ShopTurboItemsPrice.objects.get_or_create(
                    item=shopturbo_item,
                    price=float(item_price),
                    currency=currency
                )

                check_default = ShopTurboItemsPrice.objects.filter(
                    item=shopturbo_item, default=True)
                if len(check_default) == 0:
                    price.default = True
                price.name = item_name
                price.save()

                shopturbo_item_order.item = shopturbo_item
                shopturbo_item_order.save()

                for field in field_mapping:
                    try:
                        if "|line_item" in field:
                            sanka_field = field_mapping[field].replace("|line_item", "")
                            value = item.get(field.replace("|line_item", ""), None)

                            custom_field = ShopTurboItemsOrdersNameCustomField.objects.filter(
                                workspace=workspace, name=sanka_field
                            ).first()
                            if custom_field and value:
                                custom_value, _ = (
                                    ShopTurboItemsOrdersValueCustomField.objects.get_or_create(
                                        field_name=custom_field,
                                        item_order=shopturbo_item_order,
                                    )
                                )
                                custom_value.value = value
                                custom_value.save()
                    except Exception as e:
                        print(f"Error processing field {field} for item {item_id}: {e}")

            shopturbo_order.number_item = len(items)
            shopturbo_order.save()

            # Link to Account (Customer) if available
            if account_id:
                # First try to find existing company linked to this account
                company_platform = CompanyPlatforms.objects.filter(
                    channel=channel,
                    platform_id=account_id
                ).first()

                if company_platform and company_platform.company:
                    # Link order to existing company
                    shopturbo_order.company = company_platform.company
                    shopturbo_order.save()
                else:
                    # Fetch account details from Salesforce
                    account_query = f"""
                    SELECT Id, OwnerId, Name, Phone, BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountry
                    FROM Account 
                    WHERE Id = '{account_id}'
                    """
                    try:
                        account = sf.query(account_query)['records'][0]

                        # Get All Owner ID
                        owner_ids = [account.get('OwnerId')]
                        owner_ids = list(set(owner_ids))
                        owner_emails = get_bulk_owner_emails_by_owner_ids(
                            channel_id, owner_ids)

                        # Try to find existing company platform
                        company_platform, _ = CompanyPlatforms.objects.get_or_create(
                            channel=channel,
                            platform_id=account_id
                        )

                        if company_platform.company:
                            company = company_platform.company
                        else:
                            company, _ = Company.objects.get_or_create(
                                workspace=workspace,
                                name=account.get('Name'),
                                phone_number=account.get('Phone')
                            )
                            company_platform.company = company
                            company_platform.save()

                        shopturbo_order.company = company
                        shopturbo_order.save()

                        company.phone_number = account.get('Phone')

                        if 'OwnerId' in account:
                            owner_id = account.get('OwnerId')
                            owner_email = owner_emails.get(owner_id)
                            if owner_email:
                                custom_field, _ = CompanyNameCustomField.objects.get_or_create(
                                    workspace=workspace,
                                    type='text',
                                    name='Owner Email'
                                )

                                custom_value, _ = CompanyValueCustomField.objects.get_or_create(
                                    field_name=custom_field,
                                    company=company
                                )
                                custom_value.value = owner_email
                                custom_value.save()

                        company.save()
                    except Exception as account_error:
                        print(
                            f"Error fetching account {account_id}: {account_error}")

            # Update progress
            processed += 1
            if total_opportunities > 0:  # Prevent division by zero
                progress = int((processed / total_opportunities) * 100)
                # Ensure progress is between 30 and 90 during processing
                task.progress = min(max(progress, 30), 90)
                task.save()

        task.status = 'Completed'
        task.progress = 100
        task.save()
        return True

    except Exception as e:
        print(f"Error importing Salesforce opportunities: {e}")
        traceback.print_exc()

        # Update task status
        try:
            task.status = 'Failed'
            task.save()
        except Exception as save_error:
            print(f"Error updating task status: {save_error}")

        return False
