import uuid
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from data.models.base import save_tags_values, validate_value_custom_field
from data.models.constant import *
from data.constants.constant import *
from data.models.user import UserManagement


class JournalEntry(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_journal = models.IntegerField(null=True, blank=True)
    settle_choice = models.CharField(
        choices=JOURNAL_ACCOUNT, max_length=20, null=True, blank=True)
    category = models.CharField(max_length=256, null=True, blank=True)
    type_journal = models.BooleanField(default=False)
    settle_journal = models.BooleanField(default=False)
    currency = models.CharField(max_length=256, null=True, blank=True)
    # Real amount (wothout_tax)
    amount = models.FloatField(null=True, blank=True)

    amount_credit = models.FloatField(
        null=True, blank=True)  # Real amount (wothout_tax)
    amount_debit_with_tax = models.FloatField(
        null=True, blank=True)  # Real amount (wothout_tax)
    amount_debit = models.FloatField(
        null=True, blank=True)  # Real amount (wothout_tax)
    amount_credit_with_tax = models.FloatField(
        null=True, blank=True)  # Real amount (wothout_tax)
    amount_with_tax = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True, choices=JOURNAL_TAX_CATEGORY)
    amount_after_settlement = models.FloatField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    contact = models.ForeignKey(
        "Contact", on_delete=models.CASCADE, null=True, blank=True)
    company = models.ForeignKey(
        "Company", on_delete=models.CASCADE, null=True, blank=True)
    transaction_date = models.DateField(
        null=True, blank=True)  # Accounting Date
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default='active')

    payment = models.OneToOneField(
        "Expense", on_delete=models.CASCADE, null=True, blank=True, related_name='journal_entry')
    invoice = models.OneToOneField(
        "Invoice", on_delete=models.CASCADE, null=True, blank=True)
    subscription = models.OneToOneField(
        "ShopTurboSubscriptions", on_delete=models.CASCADE, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    owner = models.ForeignKey(UserManagement, on_delete=models.SET_NULL, null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)  # Log
            self._log_data = log_data

        if not self.id_journal:
            # Generate a new order_id if it doesn't exist
            last_ = JournalEntry.objects.filter(
                workspace=self.workspace, id_journal__isnull=False).order_by('id_journal').last()
            if last_:
                if last_.id_journal:
                    try:
                        last_ = int(last_.id_journal)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.id_journal = f"{next_id:04d}"

        super().save(*args, **kwargs)


class JournalEntryTransaction(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    journal = models.ForeignKey(JournalEntry, on_delete=models.CASCADE,
                                null=True, blank=True, related_name='journal_transaction_relations')
    settle_choice = models.TextField(
        choices=JOURNAL_ACCOUNT, null=True, blank=True)
    tax_category = models.TextField(
        choices=JOURNAL_TAX_CATEGORY, null=True, blank=True)
    type_transaction = models.BooleanField(default=False)
    currency = models.TextField(null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    transaction_date = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)  # Log
            self._log_data = log_data

        super().save(*args, **kwargs)


class JournalEntryNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=OBJECTS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)


class JournalEntryValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        JournalEntryNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    journal = models.ForeignKey(JournalEntry, on_delete=models.CASCADE,
                                null=True, blank=True, related_name='journal_custom_field_relations')
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(verbose_name="Journal Field file",
                            upload_to="journal-customfield-files", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == 'tag'):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class JournalEntryValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(verbose_name="Journal Custom Field file",
                            upload_to="journal-custom-field-files")
    valuecustomfield = models.ForeignKey(
        JournalEntryValueCustomField, on_delete=models.CASCADE, null=True, blank=True)
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default='image')
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)
