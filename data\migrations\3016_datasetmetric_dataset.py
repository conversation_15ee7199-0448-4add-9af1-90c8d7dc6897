# Generated by Django 4.2.4 on 2025-07-21 06:10

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('data', '3015_commercemetervaluefile'),
    ]

    operations = [
        migrations.CreateModel(
            name='DatasetMetric',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('metric', models.TextField(blank=True, null=True)),
                ('order', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.CreateModel(
            name='Dataset',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('dataset_id', models.IntegerField()),
                ('name', models.CharField(max_length=255)),
                ('data_source', models.CharField(blank=True, max_length=250, null=True)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('edited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('metrics', models.ManyToManyField(to='data.datasetmetric')),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data.workspace')),
            ],
        ),
    ]
