
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

{% if setting_type == 'conversation' %}
<div class="mb-10">
    <div id="change-stamp-section" class="mb-10">
        <form method="POST" action="{% host_url 'messagerunner_settings' host 'app' %}" enctype="multipart/form-data" onkeydown="return event.key != 'Enter';">
            {% csrf_token %}

            <div class="mb-10"
                hx-get="{% host_url 'properties' host 'app' %}"
                hx-vals='{"page_group_type": "{{setting_type}}"}'
                hx-trigger="load"
                hx-target="#properties-table"
            >
                <div id="properties-table"></div>
            </div>

            <div class="mb-10"
                hx-get="{% host_url 'message_templates' host 'app' %}"
                hx-vals='{"page_group_type": "{{setting_type}}"}'
                hx-trigger="load"
                hx-swap="#message-templates-table"
            >
                <div id="message-templates-table"></div>
            </div>

            <div class="mt-5 mb-5">
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span >
                        {% if LANGUAGE_CODE == 'ja' %}
                        検索設定
                        {% else %}
                        Search Settings
                        {% endif %}
                    </span>
                </label>

                <input
                {% if LANGUAGE_CODE == 'ja' %}
                placeholder="検索設定"
                {% else %}
                placeholder="Search Settings"
                {% endif %}
                id="search_setting_conversation"
                name="search_setting_conversation" class="form-control" 
                
                value=""></input>
            </div>

            <button id="submit-customfield-deals-button" name="submit-customfield-deals-button" type="submit" class="btn btn-dark my-5">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </form> 

    </div>
</div>

<style>
    .stretch-select-custom span {
        height:100% !important;
    }
    .stretch-select-custom span span span span{
        {% comment %} display: flex !important; {% endcomment %}
        align-content: center;
        flex-wrap: wrap;
        margin-bottom: auto;
        margin-top: auto;
    }
</style>
<style>
    .loading-spinner{
        display:none;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2-this').select2();
    });

    function delete_deals_custom_field_name(elm){
        elm.parentElement.parentElement.parentElement.parentElement.remove()
    }

    var searchSettingElm = document.querySelector("#search_setting_conversation");
    var tagify = new Tagify(searchSettingElm, {
        whitelist: [
        { "value": "ID", "id": "id_predefined"},
        {% for column in column_values %}
            {% with args=column|stringify|add:'|'|add:"message_thread" %} 
                {% with column_display=args|get_column_display:request %}
                    { "value": "{{column_display.name}}", "id": "{{column_display.id}}" },
                {% endwith %}
            {% endwith %}
        {% endfor %}
        ],
        dropdown: {
            maxItems: 20,           // <- mixumum allowed rendered suggestions
            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
            enabled: 0,             // <- show suggestions on focus
            closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
        },
        enforceWhitelist: true,
        searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
        originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
        });
    
    tagify.addTags([{ value: "ID", id: "id_predefined", readonly:"true" }]);

    {% if app_setting.search_setting_conversation %}
        {% with app_setting.search_setting_conversation|split:"," as search_setting_value %}
            {% for value in search_setting_value %}
                {% if value in column_values %}
                    {% with args=value|stringify|add:'|'|add:"message_thread" %} 
                        {% with column_display=args|get_column_display:request %}
                                tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                        {% endwith %}
                    {% endwith %}
                {% endif %}
            {% endfor %}
        {% endwith %}
    {% endif %}
</script>

{% endif %}