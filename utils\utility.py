import ast
import csv
from datetime import datetime, timedelta
import hashlib
from io import BytesIO, String<PERSON>
from itertools import islice
import json
import os
import random
import re
import tempfile
import traceback
import unicodedata
import urllib.parse
from urllib.parse import parse_qs, unquote, urlencode, urlparse, urlunparse
from urllib.request import Request, urlopen
import uuid
import zipfile

from PIL import Image
import boto3
import chardet
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.files import File
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import Q
from django.db.models import Count, Func
from django.utils import timezone
from django.utils.timezone import now
from django_hosts.resolvers import reverse
import requests
from xhtml2pdf.files import pisaFileObject

from data.constants.constant import (
    COLUMNS_CAPITALIZE_LAST_WORD,
    CURRENCY_MODEL,
    DEFAULT_PERMISSION,
    SANKA_ID_COLUMN,
)
from data.constants.constant_icon import SVG_ICON
from data.constants.properties_constant import OBJECT_TYPE_TO_SLUG, TYPE_OBJECT_WORKFLOW
from data.models import (
    AppLog,
    AppSetting,
    Bill,
    BillNameCustomField,
    BillValueCustomField,
    BillValueFile,
    Company,
    CompanyNameCustomField,
    CompanyValueCustomField,
    CompanyValueFile,
    Contact,
    ContactList,
    ContactsNameCustomField,
    ContactsValueCustomField,
    ContactsValueFile,
    CustomObjectPropertyName,
    CustomObjectPropertyValue,
    DealsNameCustomField,
    DealsValueCustomField,
    DealsValueFile,
    ExpenseNameCustomField,
    ExpenseValueCustomField,
    ExpenseValueFile,
    InventoryTransactionNameCustomField,
    InventoryTransactionValueCustomField,
    InventoryTransactionValueFile,
    InventoryWarehouse,
    InventoryWarehouseNameCustomField,
    InventoryWarehouseValueCustomField,
    InventoryWarehouseValueFile,
    JobNameCustomField,
    JobValueCustomField,
    JobValueFile,
    Module,
    Notification,
    Post,
    PurchaseOrders,
    PurchaseOrdersNameCustomField,
    PurchaseOrdersValueCustomField,
    PurchaseOrdersValueFile,
    ReportNameCustomField,
    ReportPanelNameCustomField,
    ReportPanelValueCustomField,
    ReportPanelValueFile,
    ReportValueCustomField,
    ReportValueFile,
    ShopTurboInventory,
    ShopTurboInventoryNameCustomField,
    ShopTurboInventoryValueCustomField,
    ShopTurboInventoryValueFile,
    ShopTurboItemComponents,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsValueCustomField,
    ShopTurboItemsValueFile,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    ShopTurboOrdersValueFile,
    ShopTurboSubscriptionsNameCustomField,
    ShopTurboSubscriptionsValueCustomField,
    ShopTurboSubscriptionsValueFile,
    String,
    User,
    UserManagement,
    UserManagementNameCustomField,
    UserManagementValueCustomField,
    Verification,
    View,
    ViewFilter,
)
from sanka.settings import (
    AWS_ACCESS_KEY_ID,
    AWS_LOCATION,
    AWS_S3_REGION_NAME,
    AWS_SECRET_ACCESS_KEY,
    AWS_STORAGE_BUCKET_NAME,
    PROD,
    S3_CLIENT,
)
from utils.barcode import barcode_jan13_from_svg, generate_ean_barcode_img
from utils.date import parse_date
from utils.discord import DiscordNotification
from utils.html_utils import validate_html
from utils.logger import logger
from utils.openaiAPI import OpenaiAPI

LIST_ICONS = [icon for category in SVG_ICON for icon in category["icon_list"]]


def is_serializable(value):
    try:
        json.dumps(value)
        return True
    except (TypeError, OverflowError):
        return False


def check_dictionary(key, dict, default=None):
    if key in dict:
        return dict[key]

    return default


def camel_case(s):
    res = re.sub(r"(_|-)+", " ", s).title().replace(" ", "")

    return "".join([res[0].lower(), res[1:]])


def sentence_case(s):
    res = re.sub("([A-Z])", r" \1", s)
    return "".join(res)


def get_workspace(user):
    """
    return current workspace with optimized query
    """
    # Check if the user has a request attribute and if the workspace is already cached
    if hasattr(user, "request") and hasattr(user.request, "_cached_workspace"):
        return user.request._cached_workspace

    # Ensure the user is authenticated before querying
    if not user or not user.is_authenticated:
        # Cache None for anonymous user if request object exists
        if hasattr(user, "request"):
            user.request._cached_workspace = None
        return None

    # If not cached, query the database
    try:
        verification = Verification.objects.select_related("workspace").get(user=user)
        workspace = verification.workspace if verification else None
    except Verification.DoesNotExist:
        workspace = None

    # Cache the workspace if possible
    if hasattr(user, "request"):
        user.request._cached_workspace = workspace

    return workspace


def check_if_int_castable(input_var):
    try:
        int(input_var)
        return True
    except (ValueError, TypeError):
        return False


def check_staff(user, workspace):
    """
    return if user and workspace is staff
    """
    if user.is_staff and workspace.staff:
        return True
    else:
        return False


# Helper function to filter the list
# remove only the element immediately after the first matching text
def delete_item_after_some_item_in_list(lst, text):
    try:
        idx = lst.index(text)
        # Exclude the element immediately after the first match
        return [item for i, item in enumerate(lst) if i != idx + 1]
    except ValueError:
        # text not found, return original list
        return list(lst)


def get_subscription(workspace):  # pragma: no cover
    """
    return workspace subscription
    """
    if workspace:
        if workspace.subscription:
            subscription = workspace.subscription
            return subscription
        else:
            return None
    else:
        return None


def get_space(user):  # pragma: no cover
    """
    return current workspace
    """
    verification = Verification.objects.get(user=user)
    space = verification.space
    return space


def duplicate():  # pragma: no cover
    # delete duplicates
    duplicated_post_list = (
        Post.objects.values("twitter_id").annotate(Count("id")).filter(id__count__gt=1)
    )

    for duplicated_post_item in duplicated_post_list:
        duplicated_post_latest = (
            Post.objects.only("twitter_id")
            .filter(twitter_id=duplicated_post_item["twitter_id"])
            .latest("-id")
        )  # delete the older ones
        duplicated_posts = (
            Post.objects.filter(twitter_id=duplicated_post_item["twitter_id"])
            .only("twitter_id")
            .exclude(id=duplicated_post_latest.id)
        )
        logger.info(duplicated_posts)
        duplicated_posts.delete()

    # fast
    duplicated_post_list = (
        Post.objects.values("twitter_id")
        .annotate(Count("id"))
        .filter(id__count__gt=150000)
    )
    for duplicated_post_item in duplicated_post_list:
        logger.info(duplicated_post_item)
        duplicated_posts = (
            Post.objects.filter(twitter_id=duplicated_post_item["twitter_id"])
            .only("twitter_id")
            .delete()
        )

    duplicated_post_list = (
        Post.objects.values("twitter_id")
        .annotate(Count("id"))
        .filter(id__count__gte=5, id__count__lte=10)
    )
    for duplicated_post_item in duplicated_post_list:
        logger.info(duplicated_post_item)
        duplicated_posts = (
            Post.objects.filter(twitter_id=duplicated_post_item["twitter_id"])
            .only("twitter_id")
            .delete()
        )

    duplicated_post_list = (
        Post.objects.values("twitter_id")
        .annotate(Count("id"))
        .filter(id__count__gt=1, id__count__lt=5)
    )
    for duplicated_post_item in duplicated_post_list:
        logger.info(duplicated_post_item)
        duplicated_posts = (
            Post.objects.filter(twitter_id=duplicated_post_item["twitter_id"])
            .only("twitter_id")
            .delete()
        )

    # bulk updates
    Post.objects.filter(cat="tweet").update(cat="post", platform="twitter")


def url_shortner(url):  # pragma: no cover
    target = "https://url-shortener-service.p.rapidapi.com/shorten"
    # Add payload with the properly encoded url
    payload = "url=" + urllib.parse.quote(url)
    headers = {
        "content-type": "application/x-www-form-urlencoded",
        "X-RapidAPI-Key": settings.RAPID_API_KEY,
        "X-RapidAPI-Host": "url-shortener-service.p.rapidapi.com",
    }
    response = requests.request("POST", target, data=payload, headers=headers)
    if response.status_code != 200:
        DiscordNotification().send_message(
            f"[RAPID API URL Shortener Service] [ERROR] - Skip the URL Shortener process due API error: {response.status_code} {response.text}. ",
            mention_owner=True,
        )
        return url

    return response.json()["result_url"]


def is_json_key_present(json, key):  # pragma: no cover
    if not isinstance(json, dict):
        return False
    try:
        _ = json[key]
    except KeyError:
        return False

    return True


def url_expander(url):  # pragma: no cover
    req = Request(url=url, headers={"User-Agent": "Mozilla/5.0"})
    try:
        response = urlopen(req, timeout=3)
        if response.geturl() == url:
            logger.info("Request EXPAND failed - SAME")
            return url
    except Exception:
        logger.info("Request EXPAND failed")
        return url

    return response.geturl()


def check_number(value, return_value=0):  # pragma: no cover
    if not value:
        return return_value
    return value


def merge_dict_keys(dict1, dict2):
    """Merge two dictionaries, with values from dict2 taking precedence.

    Args:
        dict1: First dictionary
        dict2: Second dictionary (values will override dict1 if keys conflict)

    Returns:
        dict: A new dictionary with keys from both dictionaries
    """
    result = dict1.copy()  # Start with a copy of dict1
    result.update(dict2)  # Update with values from dict2
    return result


def update_nested_dict_value(nested_dict, keys, new_value):
    """Update a value in a nested dictionary using a list of keys.

    Args:
        nested_dict: The dictionary to update
        keys: A list of keys representing the path to the value
        new_value: The new value to set

    Returns:
        None: The dictionary is modified in place
    """
    if len(keys) == 1:
        nested_dict[keys[0]] = new_value
    else:
        # Create the key if it doesn't exist
        if keys[0] not in nested_dict:
            nested_dict[keys[0]] = {}
        # Recursively update the nested dictionary
        update_nested_dict_value(nested_dict[keys[0]], keys[1:], new_value)


def date_format_ja(date):  # pragma: no cover
    endpoint = "https://translate.googleapis.com/translate_a/single?client=gtx&sl=ja&tl=en&dt=t&ie=UTF-8&oe=UTF-8&otf=1&ssel=0&tsel=0&kc=7&dt=at&dt=bd&dt=ex&dt=ld&dt=md&dt=qca&dt=rw&dt=rm&dt=ss&q="
    parsed_date_str = requests.get(endpoint + date).json()[0][0][0]
    input_format = "%B %d, %Y"  # The format of the input date
    # Step 1: Parse the input date
    parsed_date = datetime.strptime(parsed_date_str, input_format)
    # Step 2: Format the datetime object
    desired_format = "%Y-%m-%d"
    date = parsed_date.strftime(desired_format)
    return date


def duration_string_to_timedelta(duration: str):
    logger.debug(duration)
    days, hours, minutes, seconds = 0, 0, 0, 0

    if "day" in duration or "days" in duration:
        duration_list = duration.split(" days," if "days" in duration else " day,")
        time_part = duration_list[1].split(":")
        day_part = duration_list[0].strip()

        days = int(day_part)
        hours = int(time_part[0].strip())
        minutes = int(time_part[1].strip())
        seconds = int(time_part[2].strip())
    else:
        duration_list = duration.split(":")
        hours = int(duration_list[0].strip())
        minutes = int(duration_list[1].strip())
        seconds = int(duration_list[2].strip())

    duration = timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)

    return duration


def remove_http(str_input):
    if str_input is None:
        return None

    str_input = str(str_input)  # Convert to string to handle non-string inputs

    if "https://" in str_input:
        str_input = str_input.replace("https://", "")
    elif "http://" in str_input:
        str_input = str_input.replace("http://", "")
    return str_input


def replace_link(content):
    if content is None:
        return None

    content = str(content)  # Convert to string to handle non-string inputs

    pattern = re.compile(r"https?://\S+")
    urls = re.findall(pattern, content)

    for url in urls:
        # Create HTML link
        html_link = f'<a href="{url}" target="_blank">{remove_http(url)}</a>'
        # Replace URL with HTML link
        content = content.replace(url, html_link)

    return content


def PIL_image_to_django_file(img, seek_mode=False):
    """Convert a PIL Image to a Django File object.

    Args:
        img: PIL Image object
        seek_mode: If True, return the raw image content instead of a File object

    Returns:
        Django File object or raw image content
    """
    try:
        buffer = BytesIO()
        # Use a higher quality setting and optimize to reduce file size
        img.save(buffer, format="JPEG", quality=85, optimize=True)

        if seek_mode:
            buffer.seek(0)
            # Read the content of the image
            image_content = buffer.read()
            return image_content
        else:
            buffer.seek(0)  # Make sure to reset the buffer position
            django_file = File(buffer)
            return django_file
    except Exception as e:
        logger.debug(f"Error converting PIL image to Django file: {str(e)}")
        # Re-raise the exception to be handled by the caller
        raise


def get_hash(
    merchant_id, settlement_method="00", order_id="1234", amount="100", SEED_VAL="1010"
):  # pragma: no cover
    # SEED_VAL = 'your_seed_value_here'  # Replace with your actual seed value
    ctx = hashlib.sha512()

    settlement_method = (
        "00"
        if settlement_method is None or len(settlement_method) == 0
        else settlement_method
    )
    str_to_hash = (
        f"{SEED_VAL},{merchant_id},{settlement_method},{order_id},{amount}".encode(
            "utf-8"
        )
    )

    ctx.update(str_to_hash)
    hash_result = ctx.digest()
    return hash_result.hex()


def get_private_file(file_key):  # pragma: no cover
    # By Faris : Function to get the private file from  digitalocean storage
    # Replace these with your actual AWS credentials and configuration
    aws_access_key_id = AWS_ACCESS_KEY_ID
    aws_secret_access_key = AWS_SECRET_ACCESS_KEY
    aws_region = AWS_S3_REGION_NAME
    space_name = AWS_STORAGE_BUCKET_NAME
    endpoint_url = f"https://{aws_region}.digitaloceanspaces.com"

    # file_path = filename  # Replace with the actual path to your file
    file_key = unquote(file_key)  # Convert Unicode char
    file_path = f"{endpoint_url}/{space_name}/"
    file_key = file_key.replace(file_path, "", 1)

    # using Boto3
    session = boto3.session.Session()
    s3 = session.client(
        "s3",
        region_name=aws_region,
        endpoint_url=endpoint_url,
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
    )

    # Get the private file
    try:
        response = s3.get_object(Bucket=space_name, Key=file_key)
        file_content = response["Body"].read()

        return file_content
    except Exception as e:
        return f"Error: {e}"


def htmlpdf_link_callback(uri, rel):  # pragma: no cover
    sUrl = settings.STATIC_URL[1:]  # Typically /static/
    # Typically /home/<USER>/project_static/
    sRoot = settings.STATICFILES_DIRS[0]

    if uri.startswith(sUrl):
        path = os.path.join(sRoot, uri.replace(sUrl, ""))
    else:
        return uri

    pisaFileObject.getNamedFile = lambda self: path

    if not os.path.isfile(path):
        raise Exception("media URI must start with %s your url is %s" % (sUrl, uri))
    return path


def is_valid_uuid(input_str):
    try:
        if input_str is None:
            return False

        # Check if input_str is a model instance with an id attribute
        if hasattr(input_str, "id"):
            input_str = input_str.id

        input_str = str(input_str).replace(" ", "")
        uuid.UUID(input_str)
        return True
    except (ValueError, AttributeError, TypeError):
        return False


def is_valid_number(input_str):
    try:
        input_str = float(input_str)
        return True
    except Exception:
        return False


def is_valid_int(value):
    # Remove whitespace
    if isinstance(value, str):
        value = value.strip()

    try:
        # Try converting to float first to handle numeric strings
        float_val = float(value)
        # Check if it's a whole number
        return float_val.is_integer()
    except (ValueError, TypeError):
        return False


def generate_zip(list_of_tuples):  # pragma: no cover
    mem_zip = BytesIO()
    with zipfile.ZipFile(mem_zip, mode="w", compression=zipfile.ZIP_DEFLATED) as zf:
        for file_name, file_content in list_of_tuples:
            if isinstance(file_content, str):
                file_content = file_content.encode()  # Convert string to bytes
            elif not isinstance(file_content, bytes):
                raise TypeError(f"Unsupported content type for file {file_name}")

            zf.writestr(file_name, file_content)

    mem_zip.seek(0)
    return mem_zip.getvalue()


def remove_unsupported_characters(text, encoding="shift_jis"):  # pragma: no cover
    """
    Remove characters that cannot be encoded in the specified encoding.

    Args:
        text (str): The input text to filter
        encoding (str): The target encoding (default: 'shift_jis')

    Returns:
        str: Text with unsupported characters replaced with '?'
    """
    if not text:
        return text

    result = []
    for char in text:
        try:
            # Try to encode the character in the target encoding
            char.encode(encoding)
            result.append(char)
        except UnicodeEncodeError:
            # Character cannot be encoded, replace with '?'
            result.append("?")

    return "".join(result)


def get_next_month_first_day_timestamp():  # pragma: no cover
    """Gets the UNIX timestamp for the first day of the next month."""
    today = now()
    if today.month < 12:
        first_day_of_next_month = today.replace(
            day=1, month=today.month + 1, hour=0, minute=0, second=0
        )
    else:
        first_day_of_next_month = today.replace(
            day=1, month=1, year=today.year + 1, hour=0, minute=0, second=0
        )
    timestamp = first_day_of_next_month.timestamp()
    return int(timestamp)


def get_end_of_month():  # pragma: no cover
    return (now() + relativedelta(months=1)).replace(day=1) - timedelta(days=1)


def is_valid_view_id(view_id):
    if view_id is None or view_id == "None":
        return None
    try:
        return str(uuid.UUID(view_id))
    except (ValueError, TypeError):
        return None


def modular_view_filter(workspace, target, view_id=None, column_view=[], user=None):
    # Log function entry with parameters
    logger.info(
        "modular_view_filter called - workspace: %s, target: %s, view_id: %s, user: %s",
        workspace.id if workspace else None,
        target,
        view_id,
        user.id if user else None,
    )

    view_filter = None
    original_view_id = view_id
    view_id = is_valid_view_id(view_id)

    logger.debug(
        "View ID validation - original: %s, validated: %s", original_view_id, view_id
    )

    if view_id:
        # Custom view path
        logger.info("Processing custom view - view_id: %s", view_id)
        view = View.objects.filter(id=view_id).first()

        if not view:
            logger.warning("Custom view not found - view_id: %s", view_id)
        elif view.is_private and (not user or view.user != user):
            logger.warning(
                "Custom view access denied - view_id: %s, is_private: %s, user_match: %s",
                view_id,
                view.is_private,
                user == view.user if user and view.user else False,
            )

        if not view or (view.is_private and (not user or view.user != user)):
            try:
                view = View.objects.filter(id=view_id).first()
            except View.MultipleObjectsReturned:
                logger.warning("Multiple views returned for view_id: %s", view_id)
                view = View.objects.filter(
                    workspace=workspace, title__isnull=True, target=target
                ).first()
                View.objects.filter(
                    workspace=workspace, title__isnull=True, target=target
                ).exclude(id=view.id).delete()
            view_id = view.id
    else:
        # Default view path
        logger.info("Processing default view - target: %s", target)
        try:
            view, created = View.objects.get_or_create(
                workspace=workspace, title__isnull=True, target=target
            )
            view_id = view.id
            logger.debug("Default view - created: %s, view_id: %s", created, view_id)
        except View.MultipleObjectsReturned:
            logger.warning("Multiple default views found for target: %s", target)
            view = View.objects.filter(
                workspace=workspace, title__isnull=True, target=target
            ).first()
            View.objects.filter(
                workspace=workspace, title__isnull=True, target=target
            ).exclude(id=view.id).delete()
            view_id = view.id

    view_filter, vf_created = ViewFilter.objects.get_or_create(view=view)

    logger.info(
        "ViewFilter obtained - view_filter_created: %s, view_id: %s, view_title: %s",
        vf_created,
        view.id if view else None,
        view.title if view else None,
    )

    # default_value
    if view_filter:
        original_column = view_filter.column
        logger.debug(
            "Processing ViewFilter columns - original_column_type: %s, original_column_value: %s",
            type(original_column).__name__,
            str(original_column)[:200] if original_column else None,
        )

        if view_filter.column:
            try:
                column_view = ast.literal_eval(view_filter.column)
                logger.debug("Successfully parsed column with ast.literal_eval")
            except (ValueError, SyntaxError) as e:
                logger.warning(
                    "Failed to parse column with ast.literal_eval - error: %s", str(e)
                )
                # If literal_eval fails, try to parse as comma-separated string or use default
                if isinstance(view_filter.column, list):
                    column_view = view_filter.column
                    logger.debug("Using column as list directly")
                else:
                    column_view = (
                        [col.strip() for col in view_filter.column.split(",")]
                        if view_filter.column
                        else column_view
                    )
                    logger.debug("Parsed column as comma-separated string")
        else:
            # it is for contact_list
            logger.debug("ViewFilter has no column, setting default column_view")
            view_filter.column = column_view
            view_filter.save()

        # it is for contact_list
        if "contacts" in target:
            target = "contacts"

        logger.debug(f"[DEBUG process_view_filter] target: {target}")
        logger.debug(
            f"[DEBUG process_view_filter] SANKA_ID_COLUMN keys: {list(SANKA_ID_COLUMN.keys())}"
        )
        logger.debug(
            f"[DEBUG process_view_filter] column_view before processing: {column_view}"
        )

        try:
            for idx, column in enumerate(["checkbox", SANKA_ID_COLUMN[target]]):
                if column not in column_view:
                    column_view.insert(idx, column)
                    logger.debug(f"[DEBUG process_view_filter] Added column: {column}")
        except KeyError as e:
            logger.debug(
                f"[DEBUG process_view_filter] KeyError for target '{target}': {e}"
            )
            logger.debug(
                "[DEBUG process_view_filter] Skipping ID column addition due to missing SANKA_ID_COLUMN entry"
            )
            # Just add checkbox if target not in SANKA_ID_COLUMN
            if "checkbox" not in column_view:
                column_view.insert(0, "checkbox")
                logger.debug("[DEBUG process_view_filter] Added checkbox only")

        column_view = [data.lower() for data in column_view]
        view_filter.column = column_view
        logger.debug(f"[DEBUG process_view_filter] Final column_view: {column_view}")

        logger.info(
            "ViewFilter processing complete - final_column_count: %s, final_columns: %s, filter_value_present: %s",
            len(column_view) if column_view else 0,
            column_view[:10] if column_view else [],  # Log first 10 columns only
            view_filter.filter_value is not None,
        )

    return view_filter


def chunks_dict(data, SIZE: int = 100):  # pragma: no cover
    it = iter(data)
    for i in range(0, len(data), SIZE):
        yield {k: data[k] for k in islice(it, SIZE)}


def chunks_list(data, SIZE: int = 100):  # pragma: no cover
    it = iter(data)
    for i in range(0, len(data), SIZE):
        yield list(islice(it, SIZE))


def get_attr(obj, name):
    if isinstance(obj, dict):
        return obj.get(name, None)
    else:
        return getattr(obj, name)


def display_bill_field(id, lang="ja"):
    try:
        bill = Bill.objects.get(id=id)
        return f"#{'%04d' % bill.id_bill} - {bill.title}"
    except Bill.DoesNotExist:
        return id


def get_download_value(value_custom_field, lang):
    try:
        if value_custom_field.field_name.type == "warehouse_objects":
            return display_warehouse_field(value_custom_field.value, lang)

        elif value_custom_field.field_name.type == "bill_objects":
            return display_bill_field(value_custom_field.value, lang)

        elif value_custom_field.field_name.type == "purchase_order":
            if is_valid_uuid(value_custom_field.value):
                purchase_order = PurchaseOrders.objects.get(id=value_custom_field.value)
                return f"#{'%04d' % purchase_order.id_po}"

        elif value_custom_field.field_name.type == "choice":
            choice_values = ast.literal_eval(value_custom_field.field_name.choice_value)
            for choice_value in choice_values:
                if choice_value["value"] == value_custom_field.value:
                    return (
                        choice_value["label"]
                        if choice_value["label"]
                        else choice_value["value"]
                    )
        else:
            return value_custom_field.value
    except Exception as e:
        traceback.logger.debug_exc()
        logger.debug(f"... ERROR === utility -- 817: {e}")

    return None


def display_warehouse_field(id, lang="ja"):
    try:
        warehouse = InventoryWarehouse.objects.get(id=id)
        return f"#{warehouse.id_iw} - {warehouse.location}"
    except InventoryWarehouse.DoesNotExist:
        return id


def update_query_params_url(url: str, query: dict[str, list], overwrite=False):
    """
    Updates the query parameters of the given URL with the given query dict.

    Args:
        url (str): The URL to update.
        query (dict[str, list]): A dict of query parameters to update.
        overwrite (bool, optional): If True, the existing query parameters will be overwritten. Defaults to False.

    Returns:
        str: The updated URL with the new query parameters.
    """
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    for key, values in query.items():
        if key not in query_params:
            query_params[key] = values
            continue

        if overwrite:
            query_params[key] = values

    updated_query_string = urlencode(query_params, doseq=True)
    updated_url = urlunparse(parsed_url._replace(query=updated_query_string))
    return updated_url


def build_redirect_url(base_url, **kwargs):
    valid_kwargs = {k: v for k, v in kwargs.items() if v is not None}
    query_string = urlencode(valid_kwargs)
    if "?" in base_url:
        return f"{base_url}&{query_string}"

    return f"{base_url}?{query_string}"


class LPAD(Func):
    function = "LPAD"
    template = "%(function)s(%(expressions)s, 4, '0')"


def save_custom_property(request, obj, page_obj=None):
    workspace = get_workspace(request.user)

    CUSTOM_PROPERTY_NAME_OBJ = None
    CUSTOM_PROPERTY_VALUE_OBJ = None
    CUSTOM_PROPERTY_VALUE_FILE_OBJ = None
    CUSTOM_PROPERTY_DB_COLUMN = None

    if page_obj:
        CUSTOM_PROPERTY_NAME_OBJ = page_obj["custom_model"]
        CUSTOM_PROPERTY_VALUE_OBJ = page_obj["custom_value_model"]
        CUSTOM_PROPERTY_VALUE_FILE_OBJ = page_obj["custom_value_file_model"]
        CUSTOM_PROPERTY_DB_COLUMN = page_obj["custom_value_relation"]
        CUSTOM_PROPERTY_DB_FILE = page_obj["file_upload_to"]
    else:
        if "deals" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = DealsNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = DealsValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = DealsValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "deals"
            CUSTOM_PROPERTY_DB_FILE = "deals-custom-field-files"
        elif "contact" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = ContactsNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = ContactsValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ContactsValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "contact"
            CUSTOM_PROPERTY_DB_FILE = "contacts-custom-field-files"
        elif "company" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = CompanyNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = CompanyValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = CompanyValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "company"
            CUSTOM_PROPERTY_DB_FILE = "company-custom-field-files"
        elif "shopturboorders" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = ShopTurboOrdersNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = ShopTurboOrdersValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ShopTurboOrdersValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "orders"
            CUSTOM_PROPERTY_DB_FILE = "shopturbo-order-custom-field-files"
        elif "shopturbosubscription" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = ShopTurboSubscriptionsNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = ShopTurboSubscriptionsValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ShopTurboSubscriptionsValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "subscriptions"
            CUSTOM_PROPERTY_DB_FILE = "shopturbo-subscription-custom-field-files"
        elif "inventorytransaction" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = InventoryTransactionNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = InventoryTransactionValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = InventoryTransactionValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "transaction"
            CUSTOM_PROPERTY_DB_FILE = "inventory-transaction-custom-field-files"
        elif "purchaseorder" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = PurchaseOrdersNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = PurchaseOrdersValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = PurchaseOrdersValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "purchaseorders"
            CUSTOM_PROPERTY_DB_FILE = "purchase-order-custom-field-files"
        elif "shopturboitems" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = ShopTurboItemsNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = ShopTurboItemsValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ShopTurboItemsValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "items"
            CUSTOM_PROPERTY_DB_FILE = "shopturbo-item-custom-field-files"
        elif "shopturboinventory" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = ShopTurboInventoryNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = ShopTurboInventoryValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ShopTurboInventoryValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "inventory"
            CUSTOM_PROPERTY_DB_FILE = "shopturbo-inventory-custom-field-files"
        elif "inventorywarehouse" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = InventoryWarehouseNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = InventoryWarehouseValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = InventoryWarehouseValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "warehouse"
            CUSTOM_PROPERTY_DB_FILE = "inventory-warehouse-custom-field-files"
        elif "bill" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = BillNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = BillValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = BillValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "bill"
            CUSTOM_PROPERTY_DB_FILE = "spendpocket-custom-field-files"
        elif "payment" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ExpenseValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "expense"
            CUSTOM_PROPERTY_DB_FILE = "spendpocket-custom-field-files"
        elif "job" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = JobNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = JobValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = JobValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "job"
            CUSTOM_PROPERTY_DB_FILE = "job-custom-field-files"
        elif "reportpanel" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = ReportPanelNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = ReportPanelValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ReportPanelValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "panels"
            CUSTOM_PROPERTY_DB_FILE = "report-panel-custom-field-files"
        elif "report" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = ReportNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = ReportValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ReportValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "reports"
            CUSTOM_PROPERTY_DB_FILE = "report-customfield-files"
        elif "customobject" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = CustomObjectPropertyName
            CUSTOM_PROPERTY_VALUE_OBJ = CustomObjectPropertyValue
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = None
            CUSTOM_PROPERTY_DB_COLUMN = "object"
            CUSTOM_PROPERTY_DB_FILE = None
        elif "usermanagement" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = UserManagementNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = UserManagementValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = None
            CUSTOM_PROPERTY_DB_COLUMN = "usermanagement"
            CUSTOM_PROPERTY_DB_FILE = None
        elif "expense" in obj.__class__.__name__.lower():
            CUSTOM_PROPERTY_NAME_OBJ = ExpenseNameCustomField
            CUSTOM_PROPERTY_VALUE_OBJ = ExpenseValueCustomField
            CUSTOM_PROPERTY_VALUE_FILE_OBJ = ExpenseValueFile
            CUSTOM_PROPERTY_DB_COLUMN = "expense"
            CUSTOM_PROPERTY_DB_FILE = "spendpocket-custom-field-files"

    if (
        CUSTOM_PROPERTY_NAME_OBJ
        and CUSTOM_PROPERTY_VALUE_OBJ
        and CUSTOM_PROPERTY_DB_COLUMN
    ):
        post_dict = request.POST.keys()
        keys_with_pipes = [key for key in post_dict if "|" in key]

        process_master_label = {}
        process_master_value = {}
        process_master_cf_vals = []

        production_line_label = {}
        production_line_value = {}
        production_line_cf_vals = []

        logger.debug("== DEBUG", "Keys with pipes", keys_with_pipes)

        for key_pipe in keys_with_pipes:
            div_name = key_pipe
            data = request.POST.get(div_name, None)

            key_pipe = key_pipe.split("|")
            if len(key_pipe) < 4:
                continue

            # deal_id = key_pipe[2]
            custom_field_name_id = key_pipe[3]
            if is_valid_uuid(custom_field_name_id):
                try:
                    obj_name_custom_field = CUSTOM_PROPERTY_NAME_OBJ.objects.get(
                        id=custom_field_name_id
                    )
                except CUSTOM_PROPERTY_NAME_OBJ.DoesNotExist:
                    logger.debug(
                        f"[DEBUG] Custom field with id {custom_field_name_id} does not exist, skipping"
                    )
                    continue

                if obj_name_custom_field.type != "hierarchy":
                    try:
                        CustomFieldValue, _ = (
                            CUSTOM_PROPERTY_VALUE_OBJ.objects.get_or_create(
                                **{
                                    "field_name": obj_name_custom_field,
                                    CUSTOM_PROPERTY_DB_COLUMN: obj,
                                }
                            )
                        )
                    except Exception:
                        CustomFieldValue = CUSTOM_PROPERTY_VALUE_OBJ.objects.filter(
                            **{
                                "field_name": obj_name_custom_field,
                                CUSTOM_PROPERTY_DB_COLUMN: obj,
                            }
                        ).first()

                if obj_name_custom_field.type == "choice":
                    if obj_name_custom_field.multiple_select:
                        data = request.POST.getlist(div_name, None)
                        data = ";".join(filter(None, data))
                    CustomFieldValue.value = data
                elif obj_name_custom_field.type == "contact_list":
                    if data:
                        data = ast.literal_eval(data)

                    # remove contact from all contact lists
                    removes_contact_from_contact_lists = ContactList.objects.filter(
                        workspace=workspace,
                        contact_name_customfield=obj_name_custom_field,
                    )
                    for (
                        removes_contact_from_contact_list
                    ) in removes_contact_from_contact_lists:
                        removes_contact_from_contact_list.contacts.remove(
                            CustomFieldValue.contact
                        )

                    for contact_list_ in data:
                        if "value" in contact_list_:
                            if (
                                not contact_list_["value"]
                                or contact_list_["value"] == "None"
                            ):
                                continue

                        if "id" in contact_list_:
                            if is_valid_uuid(contact_list_["id"]):
                                contact_list = ContactList.objects.get(
                                    id=contact_list_["id"]
                                )
                                contact_list.contacts.add(CustomFieldValue.contact)

                    CustomFieldValue.value = data
                #
                elif obj_name_custom_field.type == "hierarchy":
                    company_list = request.POST.getlist(div_name, None)
                    class_instance = None
                    if "class" in div_name and len(key_pipe) == 8:
                        class_instance = key_pipe[5]
                        if key_pipe[7] != "#":
                            class_instance = key_pipe[5] + "#" + key_pipe[7]

                    # Create multi Custom Value model for Hierarchy
                    CustomFieldValue, _ = (
                        CUSTOM_PROPERTY_VALUE_OBJ.objects.get_or_create(
                            **{
                                "field_name": obj_name_custom_field,
                                "value": class_instance,
                                CUSTOM_PROPERTY_DB_COLUMN: obj,
                            }
                        )
                    )

                    companies_list = []

                    for company_id in company_list:
                        company = Company.objects.filter(id=company_id)
                        if company:
                            company = company.first()
                            companies_list.append(company)

                    CustomFieldValue.property_parent.clear()
                    CustomFieldValue.property_parent.add(*companies_list)
                    # For checking the related other object
                    check_hierarchy(CustomFieldValue, obj)
                elif obj_name_custom_field.type in [
                    "attendance",
                    "purchase_order",
                    "task",
                ]:
                    if (
                        obj_name_custom_field.multiple_select
                        or obj_name_custom_field.type == "task"
                    ):
                        data = request.POST.getlist(div_name, None)
                        data = ";".join(filter(None, data))
                    CustomFieldValue.value = data
                elif obj_name_custom_field.type in ["process_master"]:
                    if len(key_pipe) == 6:
                        # Handle both process_master and production_line
                        if obj_name_custom_field.type == "process_master":
                            if str(CustomFieldValue.id) not in process_master_cf_vals:
                                process_master_cf_vals.append(str(CustomFieldValue.id))

                            if str(CustomFieldValue.id) not in process_master_label:
                                process_master_label[str(CustomFieldValue.id)] = {}

                            if str(CustomFieldValue.id) not in process_master_value:
                                process_master_value[str(CustomFieldValue.id)] = {}

                            if key_pipe[4] == "label":
                                process_master_label[str(CustomFieldValue.id)][
                                    key_pipe[5]
                                ] = data
                            else:
                                process_master_value[str(CustomFieldValue.id)][
                                    key_pipe[5]
                                ] = data
                        elif obj_name_custom_field.type == "production_line":
                            if str(CustomFieldValue.id) not in production_line_cf_vals:
                                production_line_cf_vals.append(str(CustomFieldValue.id))

                            if str(CustomFieldValue.id) not in production_line_label:
                                production_line_label[str(CustomFieldValue.id)] = {}

                            if str(CustomFieldValue.id) not in production_line_value:
                                production_line_value[str(CustomFieldValue.id)] = {}

                            if key_pipe[4] == "label":
                                production_line_label[str(CustomFieldValue.id)][
                                    key_pipe[5]
                                ] = data
                            else:
                                production_line_value[str(CustomFieldValue.id)][
                                    key_pipe[5]
                                ] = data
                    elif len(key_pipe) == 4:
                        CustomFieldValue.value = ""
                        if (
                            obj_name_custom_field.type == "process_master"
                            and process_master_label
                            and process_master_value
                        ) or (
                            obj_name_custom_field.type == "production_line"
                            and production_line_label
                            and production_line_value
                        ):
                            CustomFieldValue.save()
                        else:
                            try:
                                CustomFieldValue.save(log_data={"user": request.user})
                            except Exception:
                                logger.debug(
                                    "[DEBUG] CustomFieldValue.save Not support log data"
                                )
                                CustomFieldValue.save()
                        continue

                elif obj_name_custom_field.type == "price-information":
                    item = request.POST.getlist("item", None)
                    item_price = request.POST.getlist("item_price", None)
                    number_of_items = request.POST.getlist("number_of_items", None)
                    currency = request.POST.getlist("price-info-currency", None)
                    line_item_properties_key = []
                    if obj_name_custom_field.line_item_properties:
                        line_item_properties = ast.literal_eval(
                            obj_name_custom_field.line_item_properties
                        )
                        line_item_properties_key = [
                            line_item_property["name"]
                            for line_item_property in line_item_properties
                        ]

                    data = []
                    for i, (item_name, price, quantity) in enumerate(
                        zip(item, item_price, number_of_items)
                    ):
                        entry = {
                            "item": item_name,
                            "item_price": price.replace(",", "")
                            if "," in price
                            else price,
                            "number_of_items": int(quantity) if quantity else 0,
                            # Ensure currency exists
                            "currency": currency[i] if len(currency) > i else None,
                        }

                        # Check for additional properties dynamically
                        for key in line_item_properties_key:
                            # Get list of values for this key
                            value = request.POST.getlist(key, None)
                            # Ensure value exists and has the correct index
                            if value and len(value) > i:
                                entry[key] = value[i]
                            else:
                                entry[key] = ""

                        data.append(entry)

                    CustomFieldValue.value = data

                elif obj_name_custom_field.type == "components":
                    data = request.POST.getlist(div_name, [])
                    if data == [""]:
                        data = []
                    data_qty = request.POST.getlist("components-quantity", [])
                    if data_qty == [""]:
                        data_qty = []

                    if CUSTOM_PROPERTY_DB_COLUMN == "inventory":
                        if data:
                            inventory_component_ids = ShopTurboInventory.objects.filter(
                                id__in=data
                            ).values_list("id", flat=True)
                            inventory_component_ids = ",".join(
                                [str(id) for id in inventory_component_ids]
                            )
                            CustomFieldValue.value = inventory_component_ids
                            CustomFieldValue.save()
                    else:
                        active_item_components = []
                        if data and data_qty:
                            for idx, item_component_id in enumerate(data):
                                if item_component_id != "":
                                    if not data_qty[idx]:
                                        data_qty[idx] = 0

                                    item_component = ShopTurboItems.objects.filter(
                                        id=item_component_id
                                    ).first()
                                    if item_component:
                                        item_components, _ = (
                                            ShopTurboItemComponents.objects.get_or_create(
                                                property=CustomFieldValue,
                                                item=obj,
                                                item_component=item_component,
                                            )
                                        )
                                        item_components.quantity = float(data_qty[idx])
                                        item_components.save()
                                        active_item_components.append(
                                            str(item_components.id)
                                        )

                            if active_item_components:
                                ShopTurboItemComponents.objects.filter(
                                    property=CustomFieldValue, item=obj
                                ).exclude(id__in=active_item_components).delete()
                            CustomFieldValue.save()
                        else:
                            logger.debug("==DEBUG", "Removing")
                            ShopTurboItemsValueCustomField.objects.filter(
                                field_name=obj_name_custom_field, items=obj
                            ).delete()
                            ShopTurboItemComponents.objects.filter(
                                property=CustomFieldValue, item=obj
                            ).delete()

                elif obj_name_custom_field.type == "svg":
                    html_text_value = data
                    # Checking if the SVG-HTML Tag is valid
                    if html_text_value == "":
                        CustomFieldValue.value = ""
                    else:
                        html_is_valid = validate_html(html_text_value)
                        check_valid_svg = bool(
                            ("<svg" in html_text_value)
                            or ("</svg>" in html_text_value)
                            or ("< svg" in html_text_value)
                        )
                        if html_is_valid and check_valid_svg:
                            CustomFieldValue.value = html_text_value

                            code_ = barcode_jan13_from_svg(html_text_value)
                            if code_:
                                try:
                                    # Create Image file
                                    with tempfile.NamedTemporaryFile(
                                        suffix=".jpeg", delete=False
                                    ) as temp_file:
                                        img_bytes = generate_ean_barcode_img(code_)
                                        temp_file.write(img_bytes)

                                        temp_filename = temp_file.name
                                    img_file = InMemoryUploadedFile(
                                        open(temp_filename, "rb"),
                                        "image/jpeg",
                                        temp_filename,
                                        "image/jpeg",
                                        os.path.getsize(temp_filename),
                                        None,
                                    )
                                    CustomFieldValue.file = img_file
                                except Exception:
                                    pass

                        else:
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=f"Update Items #{int(obj.item_id):04}: Invalid SVG-HTML Tag, Please check your SVG-HTML Tag.",
                                type="error",
                            )
                elif (
                    obj_name_custom_field.type == "date"
                    or obj_name_custom_field.type == "date_time"
                ):
                    try:
                        parsed_time = parse_date(data, workspace.timezone)
                        logger.debug(
                            "Processing datetime field: %s, value: %s",
                            obj_name_custom_field.name,
                            data,
                        )
                        if isinstance(parsed_time, datetime):
                            CustomFieldValue.value_time = parsed_time
                            CustomFieldValue.value = str(parsed_time)
                            logger.debug(
                                "Saved datetime value: %s, value_time: %s",
                                CustomFieldValue.value,
                                CustomFieldValue.value_time,
                            )
                        else:
                            CustomFieldValue.value = None
                            CustomFieldValue.value_time = None
                            logger.debug("Failed to parse datetime, setting to None")

                    except Exception as e:
                        logger.error("Error parsing datetime: %s", str(e))
                        CustomFieldValue.value = None
                        CustomFieldValue.value_time = None
                        logger.debug(f"... ERROR === utility.py -- 1038: {e}")
                elif obj_name_custom_field.type == "number":
                    data = data.replace(",", "")
                    CustomFieldValue.value = data
                    # Check if data can be converted to float before assigning to value_number
                    try:
                        CustomFieldValue.value_number = float(data) if data else None
                    except ValueError:
                        # If data cannot be converted to float, set value_number to None
                        CustomFieldValue.value_number = None
                elif obj_name_custom_field.type == "shipping_info":
                    data = request.POST.getlist(div_name, [])
                    keys = data[-1].split(",")
                    count = len(keys)
                    data = data[:-1]
                    res = {}
                    for i in range(count):
                        res[keys[i]] = data[i::count]

                    logger.debug(res)

                    CustomFieldValue.value = res
                elif obj_name_custom_field.type == "choice":
                    CustomFieldValue.value = data if data else None
                elif obj_name_custom_field.type in [
                    "property_sync",
                    "language",
                    "currency",
                ]:
                    pair_property = obj_name_custom_field.pair_property
                    if not pair_property:
                        continue
                    CustomFieldValue.value = data
                else:
                    CustomFieldValue.value = data

                try:
                    CustomFieldValue.save(log_data={"user": request.user})
                except Exception:
                    logger.debug("[DEBUG] CustomFieldValue.save Not support log data")
                    CustomFieldValue.save()

        # ===============================================================
        # Remove value from custom property
        # if not keys_with_pipes:
        custom_property_ids = request.POST.getlist("custom-property-id", None)
        for custom_property_id in custom_property_ids:
            if custom_property_id not in keys_with_pipes:
                custom_property_id = custom_property_id.split("|")
                if len(custom_property_id) < 4:
                    continue

                custom_field_name_id = custom_property_id[3]
                if is_valid_uuid(custom_field_name_id):
                    try:
                        obj_name_custom_field = CUSTOM_PROPERTY_NAME_OBJ.objects.get(
                            id=custom_field_name_id
                        )
                    except CUSTOM_PROPERTY_NAME_OBJ.DoesNotExist:
                        logger.debug(
                            f"[DEBUG] Custom field with id {custom_field_name_id} does not exist, skipping"
                        )
                        continue

                    if obj_name_custom_field.type != "hierarchy":
                        try:
                            CustomFieldValue, _ = (
                                CUSTOM_PROPERTY_VALUE_OBJ.objects.get_or_create(
                                    **{
                                        "field_name": obj_name_custom_field,
                                        CUSTOM_PROPERTY_DB_COLUMN: obj,
                                    }
                                )
                            )
                        except Exception:
                            CustomFieldValue = CUSTOM_PROPERTY_VALUE_OBJ.objects.filter(
                                **{
                                    "field_name": obj_name_custom_field,
                                    CUSTOM_PROPERTY_DB_COLUMN: obj,
                                }
                            ).first()

                        # Check if the property exists on the object
                        if hasattr(CustomFieldValue, "value"):
                            if obj_name_custom_field.type != "property_sync":
                                if CustomFieldValue.value:
                                    CustomFieldValue.value = None

                        # Check multiple properties
                        if hasattr(CustomFieldValue, "value_number"):
                            if CustomFieldValue.value_number:
                                CustomFieldValue.value_number = None

                        if hasattr(CustomFieldValue, "value_time"):
                            if CustomFieldValue.value_time:
                                CustomFieldValue.value_time = None

                        if hasattr(CustomFieldValue, "file"):
                            if CustomFieldValue.file:
                                CustomFieldValue.file = None

                        try:
                            CustomFieldValue.save(log_data={"user": request.user})
                        except Exception:
                            logger.debug(
                                "[DEBUG] CustomFieldValue.save Not support log data"
                            )
                            CustomFieldValue.save()

        logger.debug("process_master_label: ", process_master_label)
        logger.debug("process_master_value: ", process_master_value)
        logger.debug("process_master_cf_vals: ", process_master_cf_vals)
        if process_master_label and process_master_value and process_master_cf_vals:
            for cf_id in process_master_cf_vals:
                cf_data = []
                if cf_id in process_master_label:
                    for index_label, label in process_master_label[cf_id].items():
                        for index_value, value in process_master_value[cf_id].items():
                            if index_label == index_value:
                                cf_data.append(
                                    {
                                        "index": index_label,
                                        "label": label,
                                        "value": value,
                                    }
                                )
                                break

                    logger.debug("[DEBUG]cf_data: ", cf_data)
                    if cf_data and CustomFieldValue:
                        # cf_data sort by index
                        CustomFieldValue = CUSTOM_PROPERTY_VALUE_OBJ.objects.get(
                            id=cf_id
                        )
                        cf_data = sorted(cf_data, key=lambda x: x["index"])
                        CustomFieldValue.value = json.dumps(cf_data)

                        try:
                            CustomFieldValue.save(log_data={"user": request.user})
                        except Exception:
                            logger.debug(
                                "[DEBUG] CustomFieldValue.save Not support log data"
                            )
                            CustomFieldValue.save()

        logger.debug("production_line_label: ", production_line_label)
        logger.debug("production_line_value: ", production_line_value)
        logger.debug("production_line_cf_vals: ", production_line_cf_vals)
        if production_line_label and production_line_value and production_line_cf_vals:
            for cf_id in production_line_cf_vals:
                cf_data = []
                if cf_id in production_line_label:
                    for index_label, label in production_line_label[cf_id].items():
                        for index_value, value in production_line_value[cf_id].items():
                            if index_label == index_value:
                                cf_data.append(
                                    {
                                        "index": index_label,
                                        "label": label,
                                        "value": value,
                                    }
                                )
                                break

                    logger.debug("[DEBUG]production_line cf_data: ", cf_data)
                    if cf_data and CustomFieldValue:
                        # cf_data sort by index
                        CustomFieldValue = CUSTOM_PROPERTY_VALUE_OBJ.objects.get(
                            id=cf_id
                        )
                        cf_data = sorted(cf_data, key=lambda x: x["index"])
                        CustomFieldValue.value = json.dumps(cf_data)

                        try:
                            CustomFieldValue.save(log_data={"user": request.user})
                        except Exception:
                            logger.debug(
                                "[DEBUG] CustomFieldValue.save Not support log data"
                            )
                            CustomFieldValue.save()

    if CUSTOM_PROPERTY_NAME_OBJ and CUSTOM_PROPERTY_VALUE_OBJ:
        # Debug logging for custom properties
        custom_properties = {
            k: v for k, v in request.POST.items() if k.startswith("custom_property_")
        }
        logger.debug("Custom properties: %s", custom_properties)

        # Debug logging for datetime values
        datetime_properties = {
            k: v for k, v in request.POST.items() if "datetime" in k.lower()
        }
        logger.debug("Datetime properties: %s", datetime_properties)

        keys_with_pipes = [key for key in request.POST.keys() if "|" in key]
        for key in keys_with_pipes:
            key_pipe = key.split("|")
            if len(key_pipe) < 4:
                continue

            div_name = key
            data = request.POST.get(div_name, None)
            custom_field_name_id = key_pipe[3]

            if is_valid_uuid(custom_field_name_id):
                try:
                    obj_name_custom_field = CUSTOM_PROPERTY_NAME_OBJ.objects.get(
                        id=custom_field_name_id
                    )
                except CUSTOM_PROPERTY_NAME_OBJ.DoesNotExist:
                    logger.debug(
                        f"[DEBUG] Custom field with id {custom_field_name_id} does not exist, skipping"
                    )
                    continue
                try:
                    CustomFieldValue, _ = (
                        CUSTOM_PROPERTY_VALUE_OBJ.objects.get_or_create(
                            **{
                                "field_name": obj_name_custom_field,
                                CUSTOM_PROPERTY_DB_COLUMN: obj,
                            }
                        )
                    )
                except Exception:
                    CustomFieldValue = CUSTOM_PROPERTY_VALUE_OBJ.objects.filter(
                        **{
                            "field_name": obj_name_custom_field,
                            CUSTOM_PROPERTY_DB_COLUMN: obj,
                        }
                    ).first()

                # Debug logging for datetime field processing
                if (
                    obj_name_custom_field.type == "date"
                    or obj_name_custom_field.type == "date_time"
                ):
                    logger.debug(
                        "Processing datetime field: %s, value: %s",
                        obj_name_custom_field.name,
                        data,
                    )
                    try:
                        parsed_time = parse_date(data, workspace.timezone)
                        logger.debug("Parsed datetime: %s", parsed_time)
                        if isinstance(parsed_time, datetime):
                            CustomFieldValue.value_time = parsed_time
                            CustomFieldValue.value = str(parsed_time)
                            logger.debug(
                                "Saved datetime value: %s, value_time: %s",
                                CustomFieldValue.value,
                                CustomFieldValue.value_time,
                            )
                        else:
                            CustomFieldValue.value = None
                            CustomFieldValue.value_time = None
                            logger.debug("Failed to parse datetime, setting to None")

                    except Exception as e:
                        logger.error("Error parsing datetime: %s", str(e))
                        CustomFieldValue.value = None
                        CustomFieldValue.value_time = None
                        logger.debug(f"... ERROR === utility.py -- 1038: {e}")

                # ... rest of the existing code ...

        # Delete Image Values
        delete_file_ids = request.POST.getlist("delete_image_file_id")
        for _id in delete_file_ids:
            if _id == "":
                continue
            file_ = CUSTOM_PROPERTY_VALUE_FILE_OBJ.objects.filter(id=_id).first()
            if (
                file_.valuecustomfield.field_name.type == "image"
                or file_.valuecustomfield.field_name.type == "file"
            ):
                valuecustomfield = file_.valuecustomfield
                files_ = CUSTOM_PROPERTY_VALUE_FILE_OBJ.objects.filter(
                    valuecustomfield=valuecustomfield
                )
            else:
                files_ = [file_]

            if files_:
                for file_ in files_:
                    try:
                        if "nyc3.digitaloceanspaces.com" in file_.file.url:
                            folder = CUSTOM_PROPERTY_DB_FILE
                            file_name = file_.file.url.split("/")[-1]
                            file_name = f"{folder}/{file_name}"
                            sc_file = file_
                            S3_CLIENT.delete_object(
                                Bucket=AWS_STORAGE_BUCKET_NAME,
                                Key=f"{AWS_LOCATION}/{sc_file.file}",
                            )
                        file_.delete()
                    except Exception:
                        pass

        file_dict = request.FILES.keys()
        file_keys_with_pipes = [key for key in file_dict if "|" in key]
        for file_keys in file_keys_with_pipes:
            key_pipe = file_keys.split("|")
            # item_id = key_pipe[2]
            custom_field_name_id = key_pipe[3]

            files = request.FILES.getlist(file_keys, None)
            try:
                obj_name_custom_field = CUSTOM_PROPERTY_NAME_OBJ.objects.get(
                    id=custom_field_name_id
                )
            except CUSTOM_PROPERTY_NAME_OBJ.DoesNotExist:
                logger.debug(
                    f"[DEBUG] Custom field with id {custom_field_name_id} does not exist, skipping"
                )
                continue
            CustomFieldValue, _ = CUSTOM_PROPERTY_VALUE_OBJ.objects.get_or_create(
                **{"field_name": obj_name_custom_field, CUSTOM_PROPERTY_DB_COLUMN: obj}
            )
            if (
                obj_name_custom_field.type == "image"
                or obj_name_custom_field.type == "image_group"
                or obj_name_custom_field.type == "file"
            ):
                if (
                    obj_name_custom_field.type == "file"
                    or obj_name_custom_field.type == "image"
                ):
                    files_ = CUSTOM_PROPERTY_VALUE_FILE_OBJ.objects.filter(
                        valuecustomfield=CustomFieldValue
                    )
                    if files_:
                        for file_ in files_:
                            try:
                                if "nyc3.digitaloceanspaces.com" in file_.file.url:
                                    folder = CUSTOM_PROPERTY_DB_FILE
                                    file_name = file_.file.url.split("/")[-1]
                                    file_name = f"{folder}/{file_name}"
                                    sc_file = file_
                                    S3_CLIENT.delete_object(
                                        Bucket=AWS_STORAGE_BUCKET_NAME,
                                        Key=f"{AWS_LOCATION}/{sc_file.file}",
                                    )
                                file_.delete()
                            except Exception:
                                pass
                for file in files:
                    filename = file.name
                    CUSTOM_PROPERTY_VALUE_FILE_OBJ.objects.get_or_create(
                        valuecustomfield=CustomFieldValue, name=filename, file=file
                    )
            else:
                file = files[0]
                CustomFieldValue.file = file

                try:
                    CustomFieldValue.save(log_data={"user": request.user})
                except Exception:
                    logger.debug("[DEBUG] CustomFieldValue.save Not support log data")
                    CustomFieldValue.save()


def getEnglishTranslation(japaneseInput, jsonData):  # pragma: no cover
    for key in jsonData:
        if jsonData[key]["ja"] == japaneseInput:
            return jsonData[key]["en"]
    return None


def customer_converter(
    obj, lang=None, with_id=True, with_tag=False
):  # pragma: no cover
    if not lang:
        lang = "ja"
    # Object should have 'contact' and 'company' as customer field
    customer = ""
    type_customer = ""

    logger.debug(f"[CUSTOMER_CONVERTER] Input object type: {type(obj)}")
    logger.debug(f"[CUSTOMER_CONVERTER] Object ID: {getattr(obj, 'id', 'No ID')}")
    logger.debug(f"[CUSTOMER_CONVERTER] Object has contact: {hasattr(obj, 'contact')}")
    if hasattr(obj, "contact"):
        logger.debug(
            f"[CUSTOMER_CONVERTER] Contact is not None: {obj.contact is not None}"
        )
    logger.debug(f"[CUSTOMER_CONVERTER] Object has company: {hasattr(obj, 'company')}")
    if hasattr(obj, "company"):
        logger.debug(
            f"[CUSTOMER_CONVERTER] Company is not None: {obj.company is not None}"
        )

    try:
        # First check if the object has contact or company attributes
        if hasattr(obj, "contact") and obj.contact:
            logger.debug(f"[CUSTOMER_CONVERTER] Using contact attribute: {obj.contact}")
            contact_obj = obj.contact
            if with_id:
                customer = f"#{'%04d' % contact_obj.contact_id} "
            else:
                customer = ""
            if contact_obj.last_name:
                if lang == "ja":
                    customer += f"{contact_obj.last_name} {contact_obj.name}"
                    if with_tag:
                        customer += " (連絡先)"
                else:
                    customer += f"{contact_obj.name} {contact_obj.last_name}"
                    if with_tag:
                        customer += " (Contact)"
            else:
                customer += f"{contact_obj.name}"
                if with_tag:
                    customer += " (Contact)"
            logger.debug(f"[CUSTOMER_CONVERTER] Returning contact result: {customer}")
            return customer

        elif hasattr(obj, "company") and obj.company:
            logger.debug(f"[CUSTOMER_CONVERTER] Using company attribute: {obj.company}")
            company_obj = obj.company
            if with_id:
                customer = f"#{'%04d' % company_obj.company_id} {company_obj.name}"
            else:
                customer = f"{company_obj.name}"
            if with_tag:
                if lang == "ja":
                    customer += " (企業)"
                else:
                    customer += " (Company)"
            logger.debug(f"[CUSTOMER_CONVERTER] Returning company result: {customer}")
            return customer

        # If the object itself is a contact or company
        try:
            type_customer = obj._meta.object_name.lower()
            logger.debug(
                f"[CUSTOMER_CONVERTER] Object type from _meta: {type_customer}"
            )
        except Exception as e:
            logger.debug(f"[CUSTOMER_CONVERTER] Error getting _meta.object_name: {e}")
            return customer  # Return empty string if we can't determine the type

        if type_customer == "company":
            logger.debug("[CUSTOMER_CONVERTER] Object is a Company")
            if with_id:
                customer = f"#{'%04d' % obj.company_id} {obj.name}"
            else:
                customer = f"{obj.name}"
            if with_tag:
                if lang == "ja":
                    customer += " (企業)"
                else:
                    customer += " (Company)"

        elif type_customer == "contact":
            logger.debug("[CUSTOMER_CONVERTER] Object is a Contact")
            if with_id:
                customer = f"#{'%04d' % obj.contact_id} "
            else:
                customer = ""
            if obj.last_name:
                if lang == "ja":
                    customer += f"{obj.last_name} {obj.name}"
                    if with_tag:
                        customer += " (連絡先)"
                else:
                    customer += f"{obj.name} {obj.last_name}"
                    if with_tag:
                        customer += " (Contact)"
            else:
                customer += f"{obj.name}"
                if with_tag:
                    customer += " (Contact)"
        else:
            logger.debug(f"[CUSTOMER_CONVERTER] Unknown object type: {type_customer}")

        logger.debug(f"[CUSTOMER_CONVERTER] Final result: {customer}")
        return customer

    except Exception as e:
        traceback.logger.debug_exc()
        logger.debug("Error in customer_converter", e)
    return customer


def is_iso_format(input_string):  # pragma: no cover
    try:
        datetime.fromisoformat(input_string)
        return True
    except ValueError:
        return False


def is_valid_email(email: str) -> bool:
    # Return False for None or non-string inputs
    if not isinstance(email, str) or not email:
        return False
    regex = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
    return re.match(regex, email) is not None


def needs_normalization(text):  # pragma: no cover
    """
    Detect if the given text needs normalization.

    This function checks for:
    1. Presence of fullwidth characters
    2. Presence of various Unicode spaces
    3. Presence of characters that would be altered by NFKC normalization

    Args:
    text (str): The input text to check

    Returns:
    bool: True if normalization is needed, False otherwise
    """
    if not text:
        return False

    # Check for fullwidth characters and special Unicode spaces
    if any(ord(char) > 0xFF for char in text):
        return True

    # Check for various Unicode spaces
    if any(
        char
        in "\u3000\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f"
        for char in text
    ):
        return True

    # Check if NFKC normalization would change the string
    if unicodedata.normalize("NFKC", text) != text:
        return True

    return False


def normalize_japanese_text(text):  # pragma: no cover
    """
    Normalize Japanese text using unicodedata.

    This function performs the following normalizations:
    1. Applies NFKC normalization (Compatibility Decomposition, followed by Canonical Composition)
    2. Replaces any remaining spaces with a single ASCII space
    3. Removes any duplicate spaces
    4. Strips leading and trailing whitespace

    Args:
    text (str): The input Japanese text to normalize

    Returns:
    str: The normalized text
    """
    # Apply NFKC normalization
    text = unicodedata.normalize("NFKC", text)

    # Replace any remaining spaces with ASCII space and remove duplicates
    text = re.sub(r"\s+", " ", text)

    text = re.sub("　{2,}", "　", text)

    # Strip leading and trailing whitespace
    return text.strip()


def translate_language(BaseString, language_code):  # pragma: no cover
    # This translations language only one direction. which is mean English to Another language.
    # Because we write default strings using english

    string_output = BaseString
    if BaseString and language_code:
        BaseStringOriginal = BaseString
        BaseString = BaseString.lower()
        string_output = BaseStringOriginal

        # Default Lang is ENGLISH
        # Try and Except, Preventing Error when DB is down
        try:
            try:
                string, _ = String.objects.get_or_create(
                    base_string=BaseString, language=language_code
                )
            except String.MultipleObjectsReturned:
                string = String.objects.filter(
                    base_string=BaseString, language=language_code
                ).first()
                String.objects.filter(
                    base_string=BaseString, language=language_code
                ).exclude(id=string.id).delete()
                string = String.objects.get(
                    base_string=BaseString, language=language_code
                )
            except Exception as e:
                # Log the error for debugging sequence issues
                logger.debug(f"Error creating String object: {e}")
                # Try to get existing string first
                string = String.objects.filter(
                    base_string=BaseString, language=language_code
                ).first()
                if not string:
                    # If no existing string, re-raise the error
                    raise e

            if language_code == "en":
                if not (string.string) or string.string != BaseStringOriginal:
                    string.string = BaseStringOriginal
                    string.save()

            # if the data available on database, we can return
            if string.string:
                string_output = string.string
                return string_output

            # This will translating only onetime. If the translation didn't result quite well, we can edited on the ADMIN
            # this can translate to any language.!!!.
            try:
                # translate to target language only once
                ai = OpenaiAPI()
                string_output = ai.translate(language_code, string.base_string.lower())
                string.string = string_output
                string.save()
            except Exception:
                string_output = BaseString

            string_output = string_output.title()
            if BaseString in COLUMNS_CAPITALIZE_LAST_WORD:
                words = string_output.split()
                if words:
                    words[-1] = words[-1].upper()
                    string_output = " ".join(words)

        except Exception as e:
            logger.debug("Translation error [2]: ", e)
            pass

    return string_output


def read_csv(csv_data_string):  # Renamed param for clarity
    """Reads CSV data from a string."""
    # Attempt to detect encoding if it's bytes, otherwise assume utf-8 for string
    if isinstance(csv_data_string, bytes):
        detected_encoding = chardet.detect(csv_data_string)["encoding"]
        # Fallback to utf-8 if detection is uncertain or not one of the common ones
        encoding = (
            detected_encoding
            if detected_encoding in ["utf-8", "shift_jis", "euc-jp"]
            else "utf-8"
        )
        csv_string_data_decoded = csv_data_string.decode(encoding, errors="replace")
    else:
        csv_string_data_decoded = csv_data_string

    # Use StringIO to treat the string as a file
    csv_file = StringIO(csv_string_data_decoded)
    reader = csv.reader(csv_file)
    data_list = [row for row in reader]
    return data_list


def send_discord_notification(position_str, msg):  # pragma: no cover
    try:
        dn = DiscordNotification()
        dn.send_message(f"[Error in {position_str}] - {msg}")
    except Exception:
        pass
    return True


def get_redirect_workflow(
    submit_option, node, workspace, at, wat, status
):  # pragma: no cover
    if submit_option == "run":
        if status == "success":
            at.status = "success"
            at.completed_at = timezone.now()
            at.save()
            wat.status = "success"
            wat.save()
        elif status == "failed":
            if node:
                at.status = "failed"
                at.completed_at = timezone.now()
                at.save()
                wat.status = "failed"
                wat.save()
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
            return (
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )
        return reverse("main", host="app")
    else:
        return reverse("main", host="app")


def get_workspace_currency(workspace):  # pragma: no cover
    currencies = ast.literal_eval(workspace.currencies)
    return currencies[0]


def get_currency_symbol(currency):  # pragma: no cover
    currency_dict = {code: symbol for code, _, symbol in CURRENCY_MODEL}

    try:
        currency_symbol = currency_dict.get(currency)
    except Exception:
        return currency
    return currency_symbol


def bg_job_to_sanka_url(url):  # pragma: no cover
    try:
        if PROD:
            sanka_domain = "sanka.com"
        else:
            sanka_domain = None

        if sanka_domain:
            current_domain = url.lstrip("/").split("/")
            # Checking if have http or https inside of url
            if "http" in current_domain[0]:
                if "app." in current_domain[2]:
                    url = url.replace(current_domain[2], "app." + sanka_domain)
                else:
                    url = url.replace(current_domain[2], sanka_domain)
            else:
                if "app." in current_domain[2]:
                    url = url.replace(current_domain[0], "app." + sanka_domain)
                else:
                    url = url.replace(current_domain[0], sanka_domain)
    except Exception:
        pass

    return url


# Logic to ordering from parent,child to grandchild


def check_hierarchy(custom_value=None, obj=None):  # pragma: no cover
    try:
        if custom_value and obj:
            order = {}
            label = []
            custom_model = custom_value.field_name
            list_hierarchy = ast.literal_eval(custom_model.choice_value)
            companies_ = custom_value.property_parent.all()

            for index, order_hierarchy in enumerate(list_hierarchy):
                order[order_hierarchy["label"]] = len(list_hierarchy) - index
                label.append(order_hierarchy["label"])
            try:
                if len(order) - order[custom_value.value] == 0:  # Top Hierarchy
                    order_value = label[len(order) - order[custom_value.value] + 1]
                # Middle of Hierarchy
                elif len(order) - order[custom_value.value] > 0:
                    order_value = label[len(order) - order[custom_value.value] - 1]
                # Lowest Hierarchy
                elif order[custom_value.value] == 0 and len(label) > 3:
                    order_value = label[3]
            except Exception:
                order_value = None

            if order_value:
                for company in companies_:
                    # Create multi Custom Value model for Hierarchy
                    CustomFieldValue, _ = CompanyValueCustomField.objects.get_or_create(
                        **{
                            "field_name": custom_model,
                            "value": order_value,
                            "company": company,
                        }
                    )
                    if obj not in CustomFieldValue.property_parent.all():
                        CustomFieldValue.property_parent.add(obj)
                        CustomFieldValue.save()
            return "success"
    except Exception as e:
        logger.debug("Error Check Hierarchy: ", e)
        return "failed"


def apply_item_search_setting(appsetting: AppSetting, search_key):  # pragma: no cover
    filter_conditions = Q()
    if appsetting:
        filter_dictionary = appsetting.search_setting_item
        if filter_dictionary and filter_dictionary != "unset":
            for filter in filter_dictionary.split(","):
                try:
                    if is_valid_uuid(filter):
                        custom_field = ShopTurboItemsNameCustomField.objects.get(
                            id=filter
                        )
                        if custom_field.type == "contact":
                            contact_filter = Q()
                            contact_filter |= Q(name__icontains=search_key)
                            contact_filter |= Q(last_name__icontains=search_key)
                            contacts = Contact.objects.filter(contact_filter)
                            contacts_id = []
                            for contact in contacts:
                                contacts_id.append(str(contact.id))
                            filter_conditions |= Q(
                                shopturbo_item_custom_field_relations__field_name__id=filter,
                                shopturbo_item_custom_field_relations__value__in=contacts_id,
                            )
                        elif custom_field.type == "choice":
                            custom_field = ShopTurboItemsNameCustomField.objects.get(
                                id=filter
                            )

                            # Direct value match filter
                            filter_conditions |= Q(
                                shopturbo_item_custom_field_relations__field_name__id=filter,
                                shopturbo_item_custom_field_relations__value__icontains=search_key,
                            )

                            # Add label match filter
                            if custom_field.choice_value:
                                try:
                                    choices = ast.literal_eval(
                                        custom_field.choice_value
                                    )
                                    # Find values where the label contains search_key
                                    matching_values = [
                                        choice["value"]
                                        for choice in choices
                                        if isinstance(choice, dict)
                                        and "label" in choice
                                        and "value" in choice
                                        and search_key.lower()
                                        in choice["label"].lower()
                                    ]

                                    if matching_values:
                                        filter_conditions |= Q(
                                            shopturbo_item_custom_field_relations__field_name__id=filter,
                                            shopturbo_item_custom_field_relations__value__in=matching_values,
                                        )
                                except Exception as e:
                                    logger.debug("Error parsing choice values:", e)
                        else:
                            filter_conditions |= Q(
                                shopturbo_item_custom_field_relations__field_name__id=filter,
                                shopturbo_item_custom_field_relations__value__icontains=search_key,
                            )

                    elif "-SKU" in filter and filter.startswith("item_platform|"):
                        id_ = filter.replace("-SKU", "").replace("item_platform|", "")
                        filter_conditions |= Q(
                            item__channel__id=id_,
                            item__platform_sku__icontains=search_key,
                        )
                    elif "-Item ID" in filter and filter.startswith("item_platform|"):
                        id_ = filter.replace("-Item ID", "").replace(
                            "item_platform|", ""
                        )
                        filter_conditions |= Q(
                            item__channel__id_=id_,
                            item__platform_id__icontains=search_key,
                        )
                    elif "-Variant ID" in filter and filter.startswith(
                        "item_platform|"
                    ):
                        id_ = filter.replace("-Variant ID", "").replace(
                            "item_platform|", ""
                        )
                        filter_conditions |= Q(
                            item__channel__id=id_,
                            item__variant_id__icontains=search_key,
                        )
                    elif filter == "platform_ids":
                        # Handle platform_ids search through the related ShopTurboItemsPlatforms model
                        filter_conditions |= Q(item__platform_id__icontains=search_key)
                    else:
                        filter_conditions |= Q(**{filter + "__icontains": search_key})

                except Exception as e:
                    traceback.logger.debug_exc()
                    logger.debug(f"ERROR === shopturbo.py -- 9872: {e}")

    return filter_conditions


def get_item_id_search_filter(search_key):
    """
    Create a Q filter for item_id search that handles both numeric and zero-padded searches.

    Args:
        search_key: The search term (e.g., "0100", "100", "abc")

    Returns:
        Q: Django Q object for item_id filtering
    """
    item_id_filter = Q()

    # Always include the icontains search for partial matches
    item_id_filter |= Q(item_id__icontains=search_key)

    # If the search key is numeric and has leading zeros, also search for the integer value
    if search_key.isdigit() and search_key.startswith("0") and len(search_key) > 1:
        try:
            numeric_value = int(search_key)
            item_id_filter |= Q(item_id=numeric_value)
        except ValueError:
            pass  # If conversion fails, just use the icontains search

    return item_id_filter


def to_snake_case(name):  # pragma: no cover
    name = name.replace(" ", "")
    # Convert CamelCase to snake_case
    name = re.sub(r"(?<!^)(?=[A-Z])", "_", name)
    # Replace spaces with a single underscore
    name = re.sub(r"\s+", "_", name.strip())
    return name.lower()  # Convert to lowercase


def natural_sort_key(filename):
    """Extract numbers for proper sorting"""
    return [
        int(text) if text.isdigit() else text.lower()
        for text in re.split(r"(\d+)", filename)
    ]


def get_item_by_code_col_query(workspace, code, col_query):
    column_values = [str(v.name) for v in ShopTurboItems._meta.fields]

    filter = Q(workspace=workspace)
    if col_query in column_values:
        try:
            filter &= Q(**{col_query: int(code)})
        except ValueError:
            filter &= Q(**{col_query + "__icontains": code})
    else:
        if is_valid_uuid(col_query):
            try:
                filter &= Q(
                    shopturbo_item_custom_field_relations__field_name__id=col_query,
                    shopturbo_item_custom_field_relations__field_name__workspace=workspace,
                    shopturbo_item_custom_field_relations__value=int(code),
                )
            except ValueError:
                filter &= Q(
                    shopturbo_item_custom_field_relations__field_name__id=col_query,
                    shopturbo_item_custom_field_relations__field_name__workspace=workspace,
                    shopturbo_item_custom_field_relations__value__icontains=code,
                )
        else:
            if "-SKU" in col_query:
                name = col_query.replace("-SKU", "")
                filter &= Q(
                    item__channel__name__icontains=name,
                    item__platform_sku__icontains=code,
                )
            elif "-Inventory ID" in col_query:
                name = col_query.replace("-Inventory ID", "")
                filter &= Q(
                    item__channel__name__icontains=name,
                    item__platform_id__icontains=code,
                )

    try:
        item = ShopTurboItems.objects.filter(filter).first()
        if item.status == "archived":
            item = {"id": item.id, "item_id": item.item_id, "status": item.status}
            app_logs = []
        else:
            app_logs = AppLog.objects.filter(workspace=workspace, item=item).order_by(
                "-created_at"
            )

    except Exception:
        item = None
        app_logs = None

    return item, app_logs


def reorder_columns(default_fields, all_columns):
    # Start with columns that match default fields (in default order)
    result = [col for col in default_fields if col in all_columns]

    # Add remaining columns that aren't in default fields
    result += [col for col in all_columns if col not in default_fields]

    return result


def normalize_q_filter(q):
    """
    Normalize a Q filter that is an OR of identical base filters with separate currency exclusions.
    """
    if not isinstance(q, Q) or q.connector != "OR":
        return q  # Nothing to normalize

    base_filters = {}
    excluded_currencies = set()

    for sub_q in q.children:
        if not isinstance(sub_q, Q) or sub_q.connector != "AND":
            return q  # Unexpected format; skip normalization

        for cond in sub_q.children:
            if isinstance(cond, Q) and cond.negated:
                # Handle NOT Q(currency__in=...)
                inner = cond.children[0]
                if isinstance(inner, tuple) and inner[0] == "currency__in":
                    excluded_currencies.update(inner[1])
            elif isinstance(cond, tuple):
                key, value = cond
                if key in base_filters and base_filters[key] != value:
                    raise ValueError(f"Conflicting values for {key}")
                base_filters[key] = value
            else:
                return q  # Unexpected format

    final_q = Q(**base_filters)
    if excluded_currencies:
        final_q &= ~Q(currency__in=list(excluded_currencies))

    return final_q


def convert_date_format(date_str):
    try:
        # Parse the date using the expected input format (M/D/YYYY)
        return datetime.strptime(date_str, "%m/%d/%Y").strftime("%Y-%m-%d")
    except ValueError:
        # Return the original string if it does not match the expected format
        return date_str


def is_animated_gif(uploaded_file):
    try:
        uploaded_file.seek(0)
        img = Image.open(uploaded_file)
        return getattr(img, "is_animated", False)
    except ValueError:
        return False
    except IOError:
        return False


def get_permission_filter(permission, user, permission_type="read"):
    permission_list = permission.split("|")
    permission_unit = next(
        (permission for permission in permission_list if permission_type in permission),
        "",
    )

    if "user_read" == permission_unit or "user_edit" == permission_unit:
        return Q(owner__user=user)
    elif "team_read" == permission_unit or "team_edit" == permission_unit:
        groups = user.group_set.all()
        group_members = []
        for group in groups:
            users = group.user.all()
            group_members.extend(users)
        group_members = list(set(group_members))
        return Q(owner__user__in=group_members)
    else:  # all objects or any other value
        return Q()


def is_valid_float(value):
    try:
        float(value)
        return True
    except (ValueError, TypeError):
        return False


def is_permitted_for_user(scope, owner, user):
    if user:
        if scope == "user":
            return owner == user
        elif scope == "team":
            user = User.objects.get(id=user)
            owner = User.objects.get(id=owner)
            groups = user.group_set.all()
            for group in groups:
                users = group.user.all()
                if owner in users:
                    return True
            return False
        elif scope == "none":
            return False
        return True
    return False


def assign_object_owner(obj, owner_username, request, object_type):
    """Assign owner to object based on username, with proper error handling."""

    from utils.workspace import get_permission

    permission = get_permission(object_type=object_type, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    workspace = get_workspace(request.user)

    if not owner_username:
        obj.owner = None
        return

    try:
        user = User.objects.get(username=owner_username)
        # Add permission check to verify current user can assign this owner
        if "edit" in permission:
            permission_list = permission.split("|")
            edit_permission = permission_list[1] if len(permission_list) > 1 else ""
            edit_permission_list = edit_permission.split("_")
            scope = edit_permission_list[0] if len(edit_permission_list) > 1 else "all"

            if not is_permitted_for_user(scope, user.id, request.user.id):
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"{request.user.first_name} does not have permission to edit this {obj.__class__.__name__} object. Please update an object you have access to.",
                    type="error",
                )
                return
        user_management = UserManagement.objects.filter(
            workspace=workspace, user=user
        ).first()
        if user_management:
            obj.owner = user_management
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"User {owner_username} not found in workspace",
                type="error",
            )
    except User.DoesNotExist:
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message=f"User {owner_username} does not exist",
            type="error",
        )


def get_icon_by_name(module_name_en, module_name_ja):
    if module_name_en.lower() == "contact" or module_name_ja.lower() == "コンタクト":
        icon = """
        <svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M17 19C17 17.6044 17 16.9067 16.8278 16.3389C16.44 15.0605 15.4395 14.06 14.1611 13.6722C13.5933 13.5 12.8956 13.5 11.5 13.5H6.5C5.10444 13.5 4.40665 13.5 3.83886 13.6722C2.56045 14.06 1.56004 15.0605 1.17224 16.3389C1 16.9067 1 17.6044 1 19M13.5 5.5C13.5 7.98528 11.4853 10 9 10C6.51472 10 4.5 7.98528 4.5 5.5C4.5 3.01472 6.51472 1 9 1C11.4853 1 13.5 3.01472 13.5 5.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        """

    elif module_name_en.lower() == "items" or module_name_ja.lower() == "アイテム":
        icon = """
        <svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M19.5 6V14.2C19.5 15.8802 19.5 16.7202 19.173 17.362C18.8854 17.9265 18.4265 18.3854 17.862 18.673C17.2202 19 16.3802 19 14.7 19H7.3C5.61984 19 4.77976 19 4.13803 18.673C3.57354 18.3854 3.1146 17.9265 2.82698 17.362C2.5 16.7202 2.5 15.8802 2.5 14.2V6M2.6 1H19.4C19.9601 1 20.2401 1 20.454 1.10899C20.6422 1.20487 20.7951 1.35785 20.891 1.54601C21 1.75992 21 2.03995 21 2.6V4.4C21 4.96005 21 5.24008 20.891 5.45399C20.7951 5.64215 20.6422 5.79513 20.454 5.89101C20.2401 6 19.9601 6 19.4 6H2.6C2.03995 6 1.75992 6 1.54601 5.89101C1.35785 5.79513 1.20487 5.64215 1.10899 5.45399C1 5.24008 1 4.96005 1 4.4V2.6C1 2.03995 1 1.75992 1.10899 1.54601C1.20487 1.35785 1.35785 1.20487 1.54601 1.10899C1.75992 1 2.03995 1 2.6 1ZM8.6 9.5H13.4C13.9601 9.5 14.2401 9.5 14.454 9.60899C14.6422 9.70487 14.7951 9.85785 14.891 10.046C15 10.2599 15 10.5399 15 11.1V11.9C15 12.4601 15 12.7401 14.891 12.954C14.7951 13.1422 14.6422 13.2951 14.454 13.391C14.2401 13.5 13.9601 13.5 13.4 13.5H8.6C8.03995 13.5 7.75992 13.5 7.54601 13.391C7.35785 13.2951 7.20487 13.1422 7.10899 12.954C7 12.7401 7 12.4601 7 11.9V11.1C7 10.5399 7 10.2599 7.10899 10.046C7.20487 9.85785 7.35785 9.70487 7.54601 9.60899C7.75992 9.5 8.03995 9.5 8.6 9.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        """

    elif module_name_en.lower() == "commerce" or module_name_ja == "コマース":
        icon = """
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10.6657 5.33333C10.6657 6.04058 10.3848 6.71885 9.88469 7.21895C9.3846 7.71905 8.70632 8 7.99907 8C7.29183 8 6.61355 7.71905 6.11346 7.21895C5.61336 6.71885 5.33241 6.04058 5.33241 5.33333M2.42122 4.93426L1.95455 10.5343C1.8543 11.7373 1.80418 12.3388 2.00753 12.8028C2.1862 13.2105 2.49576 13.5469 2.8872 13.7588C3.33272 14 3.93631 14 5.1435 14H10.8546C12.0618 14 12.6654 14 13.1109 13.7588C13.5024 13.5469 13.812 13.2105 13.9906 12.8028C14.194 12.3388 14.1438 11.7373 14.0436 10.5343L13.5769 4.93426C13.4907 3.89917 13.4475 3.38162 13.2183 2.9899C13.0165 2.64496 12.7159 2.3684 12.3554 2.1959C11.946 2 11.4267 2 10.388 2L5.61017 2C4.57149 2 4.05215 2 3.64275 2.1959C3.28224 2.3684 2.98167 2.64496 2.77982 2.9899C2.55061 3.38162 2.50748 3.89917 2.42122 4.93426Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        """

    elif (
        module_name_en.lower() == "procurement"
        or module_name_ja.lower() == "プロキュアメント"
    ):
        icon = """
        <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1H2.30616C2.55218 1 2.67519 1 2.77418 1.04524C2.86142 1.08511 2.93535 1.14922 2.98715 1.22995C3.04593 1.32154 3.06333 1.44332 3.09812 1.68686L3.57143 5M3.57143 5L4.62332 12.7314C4.75681 13.7125 4.82355 14.2031 5.0581 14.5723C5.26478 14.8977 5.56108 15.1564 5.91135 15.3174C6.30886 15.5 6.80394 15.5 7.79411 15.5H16.352C17.2945 15.5 17.7658 15.5 18.151 15.3304C18.4905 15.1809 18.7818 14.9398 18.9923 14.6342C19.2309 14.2876 19.3191 13.8247 19.4955 12.8988L20.8191 5.94969C20.8812 5.62381 20.9122 5.46087 20.8672 5.3335C20.8278 5.22177 20.7499 5.12768 20.6475 5.06802C20.5308 5 20.365 5 20.0332 5H3.57143ZM9 20C9 20.5523 8.55228 21 8 21C7.44772 21 7 20.5523 7 20C7 19.4477 7.44772 19 8 19C8.55228 19 9 19.4477 9 20ZM17 20C17 20.5523 16.5523 21 16 21C15.4477 21 15 20.5523 15 20C15 19.4477 15.4477 19 16 19C16.5523 19 17 19.4477 17 20Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        """

    elif module_name_en.lower() == "process" or module_name_ja.lower() == "プロセス":
        icon = """
        <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10.5 4H10.9344C13.9816 4 15.5053 4 16.0836 4.54729C16.5836 5.02037 16.8051 5.71728 16.6702 6.39221C16.514 7.17302 15.2701 8.05285 12.7823 9.81253L8.71772 12.6875C6.2299 14.4471 4.98599 15.327 4.82984 16.1078C4.69486 16.7827 4.91642 17.4796 5.41636 17.9527C5.99474 18.5 7.51836 18.5 10.5656 18.5H11.5M7 4C7 5.65685 5.65685 7 4 7C2.34315 7 1 5.65685 1 4C1 2.34315 2.34315 1 4 1C5.65685 1 7 2.34315 7 4ZM21 18C21 19.6569 19.6569 21 18 21C16.3431 21 15 19.6569 15 18C15 16.3431 16.3431 15 18 15C19.6569 15 21 16.3431 21 18Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        """

    elif module_name_en.lower() == "talent" or module_name_ja.lower() == "タレント":
        icon = """
        <svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M21 19V17C21 15.1362 19.7252 13.5701 18 13.126M14.5 1.29076C15.9659 1.88415 17 3.32131 17 5C17 6.67869 15.9659 8.11585 14.5 8.70924M16 19C16 17.1362 16 16.2044 15.6955 15.4693C15.2895 14.4892 14.5108 13.7105 13.5307 13.3045C12.7956 13 11.8638 13 10 13H7C5.13623 13 4.20435 13 3.46927 13.3045C2.48915 13.7105 1.71046 14.4892 1.30448 15.4693C1 16.2044 1 17.1362 1 19M12.5 5C12.5 7.20914 10.7091 9 8.5 9C6.29086 9 4.5 7.20914 4.5 5C4.5 2.79086 6.29086 1 8.5 1C10.7091 1 12.5 2.79086 12.5 5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        """

    elif module_name_en.lower() == "report" or module_name_ja.lower() == "レポート":
        icon = """
        <svg width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11 1.26953V5.40007C11 5.96012 11 6.24015 11.109 6.45406C11.2049 6.64222 11.3578 6.7952 11.546 6.89108C11.7599 7.00007 12.0399 7.00007 12.6 7.00007H16.7305M5 14V17M13 12V17M9 9.5V17M17 8.98822V16.2C17 17.8802 17 18.7202 16.673 19.362C16.3854 19.9265 15.9265 20.3854 15.362 20.673C14.7202 21 13.8802 21 12.2 21H5.8C4.11984 21 3.27976 21 2.63803 20.673C2.07354 20.3854 1.6146 19.9265 1.32698 19.362C1 18.7202 1 17.8802 1 16.2V5.8C1 4.11984 1 3.27976 1.32698 2.63803C1.6146 2.07354 2.07354 1.6146 2.63803 1.32698C3.27976 1 4.11984 1 5.8 1H9.01178C9.74555 1 10.1124 1 10.4577 1.08289C10.7638 1.15638 11.0564 1.27759 11.3249 1.44208C11.6276 1.6276 11.887 1.88703 12.4059 2.40589L15.5941 5.59411C16.113 6.11297 16.3724 6.3724 16.5579 6.67515C16.7224 6.94356 16.8436 7.2362 16.9171 7.5423C17 7.88757 17 8.25445 17 8.98822Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        """

    else:
        random_icon = random.choice(LIST_ICONS)
        icon = random_icon["svg"]

    return icon
