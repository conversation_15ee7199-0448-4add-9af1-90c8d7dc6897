{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="">
    <div class="" id="main-content" style="padding: 0;">
        <div id="change-stamp-section" class="mb-10">
            <div class="">
                <form 
                    action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}"
                    method="POST"
                    >
                    {% csrf_token %}

                    <div class="task_wizard">
                        <div class="mb-5">
                            <label class="{% include 'data/utility/form-label.html' %}">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    連携サービス
                                    {% else %}
                                    Integrations
                                    {% endif %}
    
                                </span>
                            </label>
                            <select required class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="false" 
                                id="channel-select" onchange="toggleInputSendTo()" 
                                name="select_integration_ids" placeholder="{% if LANGUAGE_CODE == 'ja' %}担当者{% else %}Assignee{% endif %}">
                                {% for channel in channels %}
                                    <option value="{{ channel.id }}" data-checkbox="{{channel.ms_refresh_token}}" data-platform="{{ channel.integration.slug }}">
                                        {% if channel.integration.slug == 'hubspot' and channel.integration_app %}
                                            {% if LANGUAGE_CODE == 'ja' %}
                                                {{channel.integration.slug|title}} - {{channel.integration_app.title_ja}} {{ channel.name|split:channel.integration_app.title|last }}
                                            {% else %} 
                                                {{channel.integration.slug|title}} - {{channel.integration_app.title}} {{ channel.name|split:channel.integration_app.title|last }}
                                            {% endif %}
                                        {% else %}
                                            {{channel.integration.slug|title}} - {{channel.name}} 
                                        {% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div id="mapping-contact-field" class="d-none">
                        {% csrf_token %}
                        <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                            <input id="contact-checker" name="item-checker" class="form-check-input" type="checkbox"
                                
                                hx-post="{% host_url 'sync_case_header_extractor' host 'app' %}"
                                hx-target="#csvMappingContainer"
                                hx-include="#channel-select"
                                hx-trigger="load, channelChanged"
                                hx-vals='{"function_type":"{{import_export_type}}","mapping_type":"order"}'
                                hx-encoding="multipart/form-data"
                                onchange="contactMappingHandler(this)"

                                hx-on="htmx:beforeSend: 
                                    document.getElementById('csvMappingContainer').classList.add('d-none');
                                    document.getElementById('loading-spinner').classList.remove('d-none');
                                
                                    htmx:afterRequest:
                                    document.getElementById('loading-spinner').classList.add('d-none');
                                    document.getElementById('csvMappingContainer').classList.remove('d-none')
                                    "
                                
                            >
                        </div>

                        <div id="loading-spinner" class="text-center d-none">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>

                        <div id="csvMappingContainer" class="mb-3 mb-10 mt-8"></div>
                    </div>

                    <div id="set-contact-as-company-field" class="form-check mb-5 d-none">
                        <input class="form-check-input" type="checkbox" id="set-contact-as-company" name="set-contact-as-company" value=1>
                        <label class="form-check-label ms-1 fs-5 text-gray-700" for="set-contact-as-company" >
                            {% if LANGUAGE_CODE == 'ja' %}会社を優先的に顧客として登録{% else %}Prioritize Company for Customer{% endif %}
                        </label>
                    </div>
                    
                    <div class="mb-8">
                        {% for case_id in case_ids %}
                            <input type="hidden" name="case_ids" value="{{case_id}}">
                        {% endfor %}
                    </div>

                    <div class="d-flex ">
                        <button id="import-btn" type="submit" class="btn btn-dark {% if import_export_type != 'import' %}d-none{% endif %}" name="import_deals">
                            <span class="svg-icon svg-icon-2 svg-icon-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-download" viewBox="0 0 16 16">
                                    <path d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                                    <path d="M7.646 15.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 14.293V5.5a.5.5 0 0 0-1 0v8.793l-2.146-2.147a.5.5 0 0 0-.708.708z"/>
                                </svg>
                            </span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            案件をインポート {% if case_ids|length > 0 %}({{case_ids|length}}){% endif %}
                            {% else %}
                            Import Cases {% if case_ids|length > 0 %}({{case_ids|length}}){% endif %}
                            {% endif %}
                        </button>

                        <button id="export-btn" type="submit" class="btn btn-dark {% if import_export_type != 'export' %}d-none{% endif %}" name="export_deals">
                            <span class="svg-icon svg-icon-2 svg-icon-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cloud-upload" viewBox="0 0 16 16">
                                    <path fill-rule="evenodd" d="M4.406 1.342A5.53 5.53 0 0 1 8 0c2.69 0 4.923 2 5.166 4.579C14.758 4.804 16 6.137 16 7.773 16 9.569 14.502 11 12.687 11H10a.5.5 0 0 1 0-1h2.688C13.979 10 15 8.988 15 7.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 2.825 10.328 1 8 1a4.53 4.53 0 0 0-2.941 1.1c-.757.652-1.153 1.438-1.153 2.055v.448l-.445.049C2.064 4.805 1 5.952 1 7.318 1 8.785 2.23 10 3.781 10H6a.5.5 0 0 1 0 1H3.781C1.708 11 0 9.366 0 7.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383"/>
                                    <path fill-rule="evenodd" d="M7.646 4.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 5.707V14.5a.5.5 0 0 1-1 0V5.707L5.354 7.854a.5.5 0 1 1-.708-.708z"/>
                                </svg>
                            </span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            案件をエクスポート {% if case_ids|length > 0 %}({{case_ids|length}}){% endif %}
                            {% else %}
                            Export Cases {% if case_ids|length > 0 %}({{case_ids|length}}){% endif %}
                            {% endif %}
                        </button>
                    </div>

                    <style>
                        #task-field-indicator{
                            display:none;
                        }
                        .htmx-request#task-field-indicator{
                            display:inline-block;
                        }
                    </style>
                    <span class="spinner-border spinner-border-sm text-secondary task-field-indicator" id="task-field-indicator" style="position:relative; right:-6px" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </span>
                    
                </form>
            </div>

        </div>
    </div>
</div>

<script>
    $('.select2-this').select2();
    function showFilename(elm){
        path = elm.value.replace(/^.*[\\\/]/, '')
        elm.parentElement.parentElement.querySelector('.filename').innerHTML = path
    }
    $(document).ready(function() {
        toggleInputSendTo();
    });

    function toggleInputSendTo(){
        let platform = $('#channel-select option:selected').data('platform');
        var contact_mapping = document.getElementById("mapping-contact-field");
        const import_export_type = '{{import_export_type}}'

        if(platform === 'ecforce' | platform === 'rakuten' | platform === 'makeshop' | platform === 'yahoo-shopping' | platform === 'hubspot' | platform === 'wordpress'| platform === 'amazon'){
            contact_mapping.classList.remove('d-none')
            contact_mapping.querySelector('input[name="item-checker"]').setAttribute('hx-vals', `{"mapping_type":"order","function_type":"${import_export_type}"}`)
        } else if(platform === 'shopify' | platform === 'nextengine' | platform === 'freee'){
            contact_mapping.classList.remove('d-none')
            contact_mapping.querySelector('input[name="item-checker"]').setAttribute('hx-vals', `{"mapping_type":"contact","function_type":"${import_export_type}"}`)
        } else if(platform === 'salesforce') {
            contact_mapping.classList.remove('d-none')
            contact_mapping.querySelector('input[name="item-checker"]').setAttribute('hx-vals', `{"mapping_type":"case","function_type":"${import_export_type}"}`)
        } else {
            contact_mapping.classList.add('d-none')
        }
        
        Array.from(document.querySelectorAll("input[name='item-checker']")).forEach(element => {
            if (!Array.from(element.parentElement.parentElement.classList).includes('d-none')) {
                console.log(element)
                htmx.trigger(`#${element.id}`, "channelChanged")
            }
        });
    }
    
</script>