from typing import Any, Dict, List, Optional

from django.conf import settings
from django.db import transaction
from hubspot import HubSpot
from hubspot.crm.associations.models import BatchInputPublicObjectId
import requests

from data.models import (
    Channel,
    Company,
    CompanyNameCustomField,
    CompanyPlatforms,
    CompanyValueCustomField,
    Contact,
    ContactsNameCustomField,
    ContactsPlatforms,
    ContactsValueCustomField,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsOrders,
    ShopTurboItemsOrdersValueCustomField,
    ShopTurboItemsPlatforms,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    ShopTurboItemsOrdersNameCustomField,
    ShopTurboOrders,
    ShopTurboOrdersPlatforms,
)


def refresh_access_token(channel: Channel) -> str:
    """Refresh HubSpot access token using refresh token."""
    token_url = "https://api.hubspot.com/oauth/v1/token"
    data = {
        "grant_type": "refresh_token",
        "client_id": settings.HUBSPOT_CLIENT_ID,
        "client_secret": settings.HUBSPOT_CLIENT_SECRET,
        "refresh_token": channel.refresh_token,
    }
    try:
        response = requests.post(token_url, data=data)
        response.raise_for_status()
        token_info = response.json()
    except requests.exceptions.RequestException as e:
        raise Exception(f"Failed to refresh HubSpot token: {str(e)}")

    if "access_token" not in token_info:
        raise Exception(f"Invalid token response: {token_info}")

    channel.access_token = token_info.get("access_token")
    channel.save()
    return token_info.get("access_token")

def get_hubspot_client(channel: Channel) -> HubSpot:
    """Get authenticated HubSpot API client."""
    access_token = refresh_access_token(channel)
    return HubSpot(access_token=access_token)


def fetch_companies_in_deals(channel_id: str, deal_ids: List[str]) -> Dict[str, str]:
    """Fetch company associations for a list of deal IDs."""

    channel = Channel.objects.get(pk=channel_id)
    api_client = get_hubspot_client(channel)

    deal_to_company = {}

    try:
        # Fetch associations in batches to avoid hitting API limits
        batch_size = 100
        for i in range(0, len(deal_ids), batch_size):
            batch = deal_ids[i : i + batch_size]

            # Get associations for this batch
            response = api_client.crm.associations.batch_api.read(
                from_object_type="deal",
                to_object_type="company",
                batch_input_public_object_id={
                    "inputs": [{"id": deal_id} for deal_id in batch]
                },
            )

            # Process the response
            for result in response.results:
                deal_id = result._from.id
                companies = [to.id for to in result.to]
                if companies:
                    deal_to_company[deal_id] = companies[
                        0
                    ]  # Take first company if multiple

    except Exception as e:
        print(f"Error fetching company associations: {str(e)}")

    return deal_to_company


def get_associated_contact(
    api_client, deal, contact_properties: List[str]
) -> Optional[Dict]:
    """Get contact associated with a deal."""
    if deal.associations and "contacts" in deal.associations:
        contact_id = deal.associations["contacts"].results[0].id
        contact_response = api_client.crm.contacts.basic_api.get_by_id(
            contact_id,
            properties=contact_properties,
        )
        return contact_response.properties
    return None


def get_associated_company(
    api_client, deal, company_properties: List[str]
) -> Optional[Dict]:
    """Get company associated with a deal."""
    if deal.associations and "companies" in deal.associations:
        company_id = deal.associations["companies"].results[0].id
        company_response = api_client.crm.companies.basic_api.get_by_id(
            company_id,
            properties=company_properties,
        )
        return company_response.properties
    return None


def create_or_update_contact(workspace, channel, contact_data: Dict) -> Contact:
    """Create or update contact from HubSpot contact data."""
    print(f"DEBUG: create_or_update_contact called with contact_data type: {type(contact_data)}")
    print(f"DEBUG: contact_data: {contact_data}")

    # Handle case where contact_data might be a string instead of dict
    if isinstance(contact_data, str):
        print(f"ERROR: contact_data is a string, not a dict: {contact_data}")
        return None

    if not isinstance(contact_data, dict):
        print(f"ERROR: contact_data is not a dict: {type(contact_data)}")
        return None

    contact_id = contact_data.get("hs_object_id")
    print(f"DEBUG: contact_id from hs_object_id: {contact_id}")

    if not contact_id:
        print("ERROR: No hs_object_id found in contact_data")
        return None

    try:
        contact_platform, _ = ContactsPlatforms.objects.get_or_create(
            channel=channel,
            platform_id=contact_id,
        )
        print(f"DEBUG: ContactsPlatforms created/found: {contact_platform}")
    except Exception as e:
        print(f"ERROR: Failed to create ContactsPlatforms: {str(e)}")
        return None

    if contact_platform.contact:
        print(f"DEBUG: Existing contact found: {contact_platform.contact}")
        return contact_platform.contact

    try:
        contact, created = Contact.objects.get_or_create(
            workspace=workspace,
            email=contact_data.get("email"),
            name=contact_data.get("firstname", ""),
        )
        print(f"DEBUG: Contact {'created' if created else 'found'}: {contact}")
    except Exception as e:
        print(f"ERROR: Failed to create contact: {str(e)}")
        contact = Contact.objects.filter(
            workspace=workspace,
            email=contact_data.get("email"),
            name=contact_data.get("firstname", ""),
        ).first()
        print(f"DEBUG: Fallback contact found: {contact}")

    contact_platform.contact = contact
    contact_platform.save()

    if contact_data.get("phone"):
        contact.phone = contact_data.get("phone")
    if contact_data.get("lastname"):
        contact.last_name = contact_data.get("lastname")
    contact.save()

    return contact


def create_or_update_company(workspace, channel, company_data: Dict) -> Company:
    """Create or update company from HubSpot company data."""
    print(f"DEBUG: create_or_update_company called with company_data type: {type(company_data)}")
    print(f"DEBUG: company_data: {company_data}")

    # Handle case where company_data might be a string instead of dict
    if isinstance(company_data, str):
        print(f"ERROR: company_data is a string, not a dict: {company_data}")
        return None

    if not isinstance(company_data, dict):
        print(f"ERROR: company_data is not a dict: {type(company_data)}")
        return None

    company_id = company_data.get("hs_object_id")
    print(f"DEBUG: company_id from hs_object_id: {company_id}")

    if not company_id:
        print("ERROR: No hs_object_id found in company_data")
        return None

    try:
        company_platform, _ = CompanyPlatforms.objects.get_or_create(
            channel=channel,
            platform_id=company_id,
        )
        print(f"DEBUG: CompanyPlatforms created/found: {company_platform}")
    except Exception as e:
        print(f"ERROR: Failed to create CompanyPlatforms: {str(e)}")
        return None

    if company_platform.company:
        print(f"DEBUG: Existing company found: {company_platform.company}")
        return company_platform.company

    try:
        company, created = Company.objects.get_or_create(
            workspace=workspace,
            name=company_data.get("name", ""),
            email=company_data.get("email", ""),
        )
        print(f"DEBUG: Company {'created' if created else 'found'}: {company}")
    except Exception as e:
        print(f"ERROR: Failed to create company: {str(e)}")
        company = Company.objects.filter(
            workspace=workspace,
            name=company_data.get("name", ""),
            email=company_data.get("email", ""),
        ).first()
        print(f"DEBUG: Fallback company found: {company}")

    company_platform.company = company
    company_platform.save()

    if company_data.get("phone"):
        company.phone_number = company_data.get("phone")
    if company_data.get("address"):
        company.address = company_data.get("address")
    company.save()

    return company


def process_order_items(
    api_client,
    shopturbo_order,
    order_platform,
    deal_id: str,
    mapping_custom_fields: Optional[Dict] = None,
    channel=None,
    platform=None,
    how_to_import_items: str = "create",
    key_item_field: str = None,
):
    """
    Process line items associated with a deal.

    Args:
        api_client: HubSpot API client
        shopturbo_order: Parent order
        order_platform: Order platform
        deal_id: ID of the deal
        mapping_custom_fields: Custom field mappings
        channel: Channel instance
        platform: Platform instance
        how_to_import_items: Import strategy ('create' or 'match')
        key_item_field: Custom field to use for item matching
    """
    batch_input_public_object_id = BatchInputPublicObjectId(inputs=[{"id": deal_id}])

    try:
        associations_response = api_client.crm.associations.batch_api.read(
            from_object_type="deal",
            to_object_type="line_item",
            batch_input_public_object_id=batch_input_public_object_id,
        )

        line_item_ids = [
            to.id for assoc in associations_response.results for to in assoc.to
        ]

        if not line_item_ids:
            shopturbo_order.order_type = "manual_order"
            shopturbo_order.save()
            return

        # Clear existing items
        ShopTurboItemsOrdersValueCustomField.objects.filter(
            item_order__order=shopturbo_order
        ).delete()
        ShopTurboItemsOrders.objects.filter(order=shopturbo_order).delete()

        for line_item_id in line_item_ids:
            process_line_item(
                api_client=api_client,
                shopturbo_order=shopturbo_order,
                order_platform=order_platform,
                line_item_id=line_item_id,
                mapping_custom_fields=mapping_custom_fields,
                channel=channel,
                platform=platform,
                how_to_import_items=how_to_import_items,
                key_item_field=key_item_field,
            )

    except Exception as e:
        print(f"Error processing line items: {str(e)}")


def process_line_item(
    api_client,
    shopturbo_order,
    order_platform,
    line_item_id: str,
    mapping_custom_fields: Optional[Dict] = None,
    channel=None,
    platform=None,
    how_to_import_items: str = "create",
    key_item_field: str = None,
):
    """
    Process a single line item.

    Args:
        api_client: HubSpot API client
        shopturbo_order: Parent order
        order_platform: Order platform
        line_item_id: ID of the line item
        mapping_custom_fields: Custom field mappings
        channel: Channel instance
        platform: Platform instance
        how_to_import_items: Import strategy ('create' or 'match')
        key_item_field: Custom field to use for item matching
    """
    try:
        properties=[
            "name",
            "amount",
            "quantity",
            "hs_product_id",
            "product_id",
            "hs_total_discount",
            "hs_discount_percentage",
            "hs_sku",
        ]
        for field in mapping_custom_fields:
            if "|line_item" in field and field not in ["name|line_item", "discount|line_item", "product_id|line_item"]:
                properties.append(
                    field.replace("|line_item", "")
                )
        line_item = api_client.crm.line_items.basic_api.get_by_id(
            line_item_id,
            properties=properties,
        )

        item_data = line_item.properties
        shopturbo_item_order = create_or_update_item_order(
            order=shopturbo_order,
            order_platform=order_platform,
            item_data=item_data,
            line_item_id=line_item_id,
            api_client=api_client,
            channel=channel,
            workspace=shopturbo_order.workspace,
            platform=platform,
            how_to_import_items=how_to_import_items,
            key_item_field=key_item_field,
        )

        # Process line item custom fields if mapping exists
        if mapping_custom_fields:
            process_line_item_custom_fields(
                workspace=shopturbo_order.workspace,
                item_order=shopturbo_item_order,
                item_data=item_data,
                line_item_id=line_item_id,
                mapping_custom_fields=mapping_custom_fields,
            )

        # Update item order fields
        if item_data.get("amount"):
            item_price = float(item_data["amount"])
            shopturbo_item_order.price = item_price

            # Calculate price per item if quantity exists
            if item_data.get("quantity"):
                quantity = float(item_data["quantity"])
                shopturbo_item_order.number_item = quantity
                if quantity > 0:
                    shopturbo_item_order.item_price_order = item_price / quantity
                    shopturbo_item_order.total_price = item_price

        shopturbo_item_order.save()

    except Exception as e:
        print(f"Error processing line item {line_item_id}: {str(e)}")


def process_line_item_custom_fields(
    workspace,
    item_order,
    item_data: Dict,
    line_item_id: str,
    mapping_custom_fields: Dict,
) -> None:
    """Process custom fields for a line item."""
    order_item_name = item_data.get("name")
    discount = item_data.get("hs_discount_percentage")
    discount_amount = item_data.get("hs_total_discount")
    product_id = item_data.get("hs_product_id")
    custom_product_id = item_data.get("product_id")

    for field in mapping_custom_fields:
        if "|line_item" in field:
            sanka_field = mapping_custom_fields[field].replace("|line_item", "")
            value = None

            if field == "name|line_item":
                value = order_item_name or f"Item {line_item_id}"
            elif field == "discount|line_item":
                value = discount or discount_amount
            elif field == "product_id|line_item":
                value = custom_product_id or product_id
            else:
                value = item_data.get(field.replace("|line_item", ""), None)

            if value is not None:
                custom_field = ShopTurboItemsOrdersNameCustomField.objects.filter(
                    workspace=workspace, name=sanka_field
                ).first()

                if custom_field:
                    custom_value, _ = (
                        ShopTurboItemsOrdersValueCustomField.objects.get_or_create(
                            field_name=custom_field,
                            item_order=item_order,
                        )
                    )
                    custom_value.value = str(value)

                    # Set number format for discount fields
                    if field == "discount|line_item":
                        custom_value.value_number_format = "%" if discount else "number"

                    custom_value.save()


def create_or_update_item_order(
    order,
    order_platform,
    item_data: Dict,
    line_item_id: str,
    api_client=None,
    channel=None,
    workspace=None,
    platform=None,
    how_to_import_items: str = "create",
    key_item_field: str = None,
):
    """
    Create or update an item order with product handling.

    Args:
        order: The parent ShopTurboOrders instance
        order_platform: The order platform
        item_data: Dictionary containing item data from HubSpot
        line_item_id: The ID of the line item
        api_client: Optional HubSpot API client for product lookups
        channel: Optional Channel instance
        workspace: Optional Workspace instance
        platform: Optional Platform instance
        how_to_import_items: Import strategy ('create' or 'match')
        key_item_field: Custom field to use for item matching

    Returns:
        ShopTurboItemsOrders: The created or updated item order
    """
    product_id = item_data.get("hs_product_id")
    product_sku = item_data.get("hs_sku")
    product_name = item_data.get("name")
    # These will be used in the actual implementation
    _amount = float(item_data.get("amount", 0))
    _quantity = float(item_data.get("quantity", 1))
    _currency = order.currency

    # Try to find existing item order by SKU first
    existing_item_order = None
    if product_sku:
        existing_item_order = ShopTurboItemsOrders.objects.filter(
            order=order,
            sku=product_sku,
            order_platform=order_platform,
        ).first()

    # If not found by SKU, try by product ID
    if not existing_item_order and product_id:
        existing_item_orders = ShopTurboItemsOrders.objects.filter(
            order=order,
            platform_item_id=product_id,
            order_platform=order_platform,
        )
        if existing_item_orders.exists():
            existing_item_order = existing_item_orders[0]

    # Handle product ID to SKU migration if we have a product ID and channel
    if product_id and channel:
        try:
            # Look for any existing platform entries with this product ID
            existing_item_platforms = ShopTurboItemsPlatforms.objects.filter(
                channel=channel, platform_id=product_id, platform_type="default"
            )

            for platform_entry in existing_item_platforms:
                existing_item = platform_entry.item
                if existing_item:
                    # If we found an existing item through the platform entry
                    if existing_item_order and existing_item_order != existing_item:
                        # TODO: Handle merging items if needed
                        pass
                    elif not existing_item_order:
                        # Use the existing item
                        existing_item_order = existing_item

                        # Update the order with the found item
                        existing_item_order.order = order
                        existing_item_order.order_platform = order_platform
                        if product_sku and not existing_item_order.sku:
                            existing_item_order.sku = product_sku
                        existing_item_order.save()

                # Clean up the old platform entry
                platform_entry.delete()

        except Exception as e:
            print(f"Error migrating product platform entries: {str(e)}")

    # If we still don't have an item order, try to find by old naming convention
    if not existing_item_order:
        old_order_name = f"Item {line_item_id}"
        existing_item_order = ShopTurboItemsOrders.objects.filter(
            order=order, custom_item_name=old_order_name
        ).first()

        if existing_item_order:
            return existing_item_order

        # Create a new item order if none found
        existing_item_order = ShopTurboItemsOrders.objects.create(
            custom_item_name=product_name or f"Item {line_item_id}",
            order=order,
            order_platform=order_platform,
        )

    # Handle item platform and custom fields

    # Set default values
    update_value = how_to_import_items != "match"
    if not key_item_field:
        key_item_field = "None"

    # Get or create item platform
    item_platform, _ = ShopTurboItemsPlatforms.objects.get_or_create(
        platform_id=product_sku or product_id or f"item_{line_item_id}",
        platform_type="default",
        channel=channel,
        defaults={"item": None},
    )

    # Handle item updates and custom fields
    if item_platform.item:
        shopturbo_item = item_platform.item
        if key_item_field != "None" and product_sku:
            custom_field = ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, name=key_item_field
            ).first()

            if custom_field:
                custom_value = ShopTurboItemsValueCustomField.objects.filter(
                    field_name=custom_field, value=product_sku
                ).first()

                if custom_value and custom_value.items != shopturbo_item:
                    # If another item has this SKU, update references
                    shopturbo_item.product_id = None
                    item_platform.item = None
                    shopturbo_item.save()
                    item_platform.save()
                    shopturbo_item = custom_value.items
                    shopturbo_item.product_id = product_sku
                    shopturbo_item.save()
                elif not custom_value:
                    # Create new custom value if it doesn't exist
                    custom_value = ShopTurboItemsValueCustomField.objects.create(
                        field_name=custom_field, items=shopturbo_item, value=product_sku
                    )
    else:
        # Handle case where we need to create a new item
        if existing_item_order.item:
            shopturbo_item = existing_item_order.item
            if key_item_field != "None" and product_sku:
                custom_field = ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace, name=key_item_field
                ).first()
                if custom_field:
                    custom_value, _ = (
                        ShopTurboItemsValueCustomField.objects.get_or_create(
                            field_name=custom_field,
                            items=shopturbo_item,
                            defaults={"value": product_sku},
                        )
                    )
        else:
            if key_item_field != "None" and product_sku:
                # Try to find existing item by custom field value
                custom_field = ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace, name=key_item_field
                ).first()

                if custom_field:
                    custom_value = ShopTurboItemsValueCustomField.objects.filter(
                        field_name=custom_field,
                        value=product_sku,
                    ).first()

                    if custom_value:
                        shopturbo_item = custom_value.items
                        shopturbo_item.product_id = product_sku
                        shopturbo_item.save()
                    else:
                        update_value = True
                        shopturbo_item = ShopTurboItems.objects.create(
                            workspace=workspace,
                            platform=platform,
                            product_id=product_sku,
                        )
                        custom_value = ShopTurboItemsValueCustomField.objects.create(
                            field_name=custom_field,
                            items=shopturbo_item,
                            value=product_sku,
                        )
            else:
                update_value = True
                shopturbo_item = ShopTurboItems.objects.create(
                    workspace=workspace,
                    platform=platform,
                    product_id=product_sku or f"item_{line_item_id}",
                )

    # Update item properties if needed
    if update_value and hasattr(item_platform, "item") and item_platform.item:
        shopturbo_item = item_platform.item

        # Get product details if available
        if product_id and api_client:
            try:
                product_response = api_client.crm.products.basic_api.get_by_id(
                    product_id
                )
                if product_response and hasattr(product_response, "properties"):
                    item_name = product_response.properties.get("name")
                    item_price = product_response.properties.get("price")

                    if item_name:
                        shopturbo_item.name = item_name

                    if item_price:
                        try:
                            item_price = float(item_price)

                            # Create or update price record
                            price, _ = ShopTurboItemsPrice.objects.get_or_create(
                                item=shopturbo_item,
                                price=item_price,
                                currency=order.currency,
                            )

                            # Handle default price
                            default_prices = ShopTurboItemsPrice.objects.filter(
                                item=shopturbo_item, default=True
                            )

                            if not default_prices.exists():
                                price.default = True
                            elif update_value:
                                default_prices.update(default=False)
                                price.default = True

                            price.name = (
                                shopturbo_item.name or f"Price for {product_sku}"
                            )
                            price.save()

                        except (ValueError, TypeError):
                            pass
            except Exception as e:
                print(f"Error fetching product details: {str(e)}")

        shopturbo_item.save()

    # Update item platform references
    if product_sku and item_platform.platform_id != product_sku:
        item_platform.platform_id = product_sku
    if hasattr(item_platform, "item") and item_platform.item != shopturbo_item:
        item_platform.item = shopturbo_item
    item_platform.save()

    # Update item order with the item
    if existing_item_order.item != shopturbo_item:
        existing_item_order.item = shopturbo_item
        existing_item_order.save()

    return existing_item_order


def update_order_custom_fields(
    order,
    deal: Dict[str, Any],
    mapping_custom_fields: Optional[Dict] = None,
    deal_stages: Optional[List] = None,
    deal_pipelines: Optional[List] = None,
    associated_contact: Optional[Dict] = None,
    associated_company: Optional[Dict] = None,
    lang: str = "ja",
) -> None:
    """Update custom fields for an order based on the provided mapping.

    Args:
        order: The order to update
        deal: The HubSpot deal data
        mapping_custom_fields: Mapping of HubSpot fields to custom fields
        deal_stages: List of available deal stages
        deal_pipelines: List of available deal pipelines
        associated_contact: Associated contact data if any
        associated_company: Associated company data if any
        lang: Language for translations (default: "ja")
    """
    if not mapping_custom_fields:
        return

    stage_mapping = {
        "Appointment Scheduled": "appointmentscheduled",
        "Qualified To Buy": "qualifiedtobuy",
        "Presentation Scheduled": "presentationscheduled",
        "Decision Maker Bought-In": "decisionmakerboughtin",
        "Contract Sent": "contractsent",
        "Closed Won": "closedwon",
        "Closed Lost": "closedlost",
    }

    workspace = order.workspace
    updated_deal_stage = False
    updated_deal_pipeline = False

    # First handle special fields that might need to update the order directly
    for hubspot_field in [
        "deal_name",
        "deal_stage",
        "issue_date",
        "billing_date",
        "partner_display_name",
        "pipeline|deal",
    ]:
        if hubspot_field in mapping_custom_fields:
            sanka_field = mapping_custom_fields[hubspot_field]
            value = None
            
            # Handle special fields
            if hubspot_field == "deal_name":
                value = deal.properties.get("dealname")
            elif hubspot_field == "deal_stage":
                value = _get_deal_stage_value(deal, deal_stages, stage_mapping)
            elif hubspot_field == "issue_date":
                value = deal.properties.get("createdate")
            elif hubspot_field == "billing_date":
                value = deal.properties.get("closedate")
            elif hubspot_field == "pipeline|deal":
                value = deal.properties.get("pipeline")
            elif hubspot_field == "partner_display_name" and hasattr(order, "company"):
                value = order.company.name if order.company else None

            if value is not None:
                _update_order_custom_field(
                    workspace,
                    order,
                    sanka_field,
                    value,
                    deal_stages,
                    deal_pipelines,
                    lang,
                    updated_deal_stage,
                    updated_deal_pipeline,
                )

    # Then handle all other custom fields
    for hubspot_field, sanka_field in mapping_custom_fields.items():
        # Skip special fields that were already handled
        if hubspot_field in [
            "deal_name",
            "deal_stage",
            "issue_date",
            "billing_date",
            "partner_display_name",
            "pipeline|deal",
        ]:
            continue

        # Handle other custom fields
        value = None
        if "|deal" in hubspot_field:
            field = hubspot_field.replace("|deal", "")
            value = deal.properties.get(field)
        elif "|contact" in hubspot_field and associated_contact:
            field = hubspot_field.replace("|contact", "")
            # Handle both Contact model instances and raw contact dictionaries
            if hasattr(associated_contact, 'get'):
                # Raw contact dictionary from HubSpot
                value = associated_contact.get(field)
            else:
                # Contact model instance - map HubSpot fields to model attributes
                contact_field_mapping = {
                    'firstname': 'name',
                    'lastname': 'last_name',
                    'email': 'email',
                    'phone': 'phone_number',
                }
                model_field = contact_field_mapping.get(field, field)
                value = getattr(associated_contact, model_field, None)
        elif "|company" in hubspot_field and associated_company:
            field = hubspot_field.replace("|company", "")
            # Handle both Company model instances and raw company dictionaries
            if hasattr(associated_company, 'get'):
                # Raw company dictionary from HubSpot
                value = associated_company.get(field)
            else:
                # Company model instance - map HubSpot fields to model attributes
                company_field_mapping = {
                    'name': 'name',
                    'phone': 'phone_number',
                    'website': 'website',
                }
                model_field = company_field_mapping.get(field, field)
                value = getattr(associated_company, model_field, None)
        else:
            # Try to get the value directly from deal properties
            value = deal.properties.get(hubspot_field)

        if value is not None:
            _update_custom_field_value(
                workspace,
                order,
                sanka_field,
                value,
                deal_stages,
                deal_pipelines,
                lang,
                updated_deal_stage,
                updated_deal_pipeline,
                associated_contact,
                associated_company,
            )


def _get_deal_stage_value(deal, deal_stages, stage_mapping):
    deal_stage = deal.properties.get("dealstage")
    if deal_stage in stage_mapping:
        return stage_mapping[deal_stage]
    elif deal_stages and deal_stage in [stage["label"] for stage in deal_stages]:
        return deal_stage
    else:
        return None


def _update_order_custom_field(
    workspace,
    order,
    field_name,
    value,
    deal_stages,
    deal_pipelines,
    lang,
    updated_deal_stage,
    updated_deal_pipeline,
):
    if field_name == "deal_stage":
        if not updated_deal_stage:
            order.deal_stage = value
            order.save()
            updated_deal_stage = True
    elif field_name == "pipeline|deal":
        if not updated_deal_pipeline:
            order.pipeline = value
            order.save()
            updated_deal_pipeline = True
    else:
        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
            workspace=workspace, name=field_name
        ).first()

        if custom_field:
            custom_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                field_name=custom_field,
                orders=order,
            )
            custom_value.value = value
            custom_value.save()


def _update_custom_field_value(
    workspace,
    order,
    field_name,
    value,
    deal_stages,
    deal_pipelines,
    lang,
    updated_deal_stage,
    updated_deal_pipeline,
    associated_contact,
    associated_company,
):
    if "|contact" in field_name:
        field_name = field_name.replace("|contact", "")
        if not associated_contact:
            return

        if field_name in ["name", "last_name", "email", "phone_number"]:
            if field_name == "name":
                associated_contact.name = value
            elif field_name == "last_name":
                associated_contact.last_name = value
            elif field_name == "email":
                associated_contact.email = value
            elif field_name == "phone_number":
                associated_contact.phone = value
            associated_contact.save()
            return

        custom_field = ContactsNameCustomField.objects.filter(
            workspace=workspace, name=field_name
        ).first()

        if custom_field:
            custom_value, _ = ContactsValueCustomField.objects.get_or_create(
                field_name=custom_field,
                contact=associated_contact,
            )
            custom_value.value = value
            custom_value.save()

    elif "|company" in field_name:
        field_name = field_name.replace("|company", "")
        if not associated_company:
            return

        if field_name in ["name", "email", "phone_number"]:
            if field_name == "name":
                associated_company.name = value
            elif field_name == "email":
                associated_company.email = value
            elif field_name == "phone_number":
                associated_company.phone_number = value
            associated_company.save()
            return

        custom_field = CompanyNameCustomField.objects.filter(
            workspace=workspace, name=field_name
        ).first()

        if custom_field:
            custom_value, _ = CompanyValueCustomField.objects.get_or_create(
                field_name=custom_field,
                company=associated_company,
            )
            custom_value.value = value
            custom_value.save()
    else:
        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
            workspace=workspace, name=field_name
        ).first()

        if custom_field:
            custom_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                field_name=custom_field,
                orders=order,
            )
            custom_value.value = value
            custom_value.save()


def update_custom_field(
    order, field_name: str, value: Any, workspace, contact=None, company=None
):
    """Update a custom field value."""
    if "|contact" in field_name:
        field_name = field_name.replace("|contact", "")
        if not contact:
            return

        if field_name in ["name", "last_name", "email", "phone_number"]:
            if field_name == "name":
                contact.name = value
            elif field_name == "last_name":
                contact.last_name = value
            elif field_name == "email":
                contact.email = value
            elif field_name == "phone_number":
                contact.phone = value
            contact.save()
            return

        custom_field = ContactsNameCustomField.objects.filter(
            workspace=workspace, name=field_name
        ).first()

        if custom_field:
            custom_value, _ = ContactsValueCustomField.objects.get_or_create(
                field_name=custom_field,
                contact=contact,
            )
            custom_value.value = value
            custom_value.save()

    elif "|company" in field_name:
        field_name = field_name.replace("|company", "")
        if not company:
            return

        if field_name in ["name", "email", "phone_number"]:
            if field_name == "name":
                company.name = value
            elif field_name == "email":
                company.email = value
            elif field_name == "phone_number":
                company.phone_number = value
            company.save()
            return

        custom_field = CompanyNameCustomField.objects.filter(
            workspace=workspace, name=field_name
        ).first()

        if custom_field:
            custom_value, _ = CompanyValueCustomField.objects.get_or_create(
                field_name=custom_field,
                company=company,
            )
            custom_value.value = value
            custom_value.save()
    else:
        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
            workspace=workspace, name=field_name
        ).first()

        if custom_field:
            custom_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                field_name=custom_field,
                orders=order,
            )
            custom_value.value = value
            custom_value.save()

def bulk_preprocess_deals_data(
    deals,
    channel: Channel,
    workspace,
    contact_properties: List[str],
    company_properties: List[str],
    deal_to_company: Optional[Dict] = None,
    filter_deal_stage: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Preprocess all deals data for bulk operations.
    
    Args:
        deals: List of HubSpot deal objects
        channel: Channel instance
        workspace: Workspace instance
        contact_properties: List of contact properties to fetch
        company_properties: List of company properties to fetch
        deal_to_company: Optional mapping of deal IDs to company IDs
        filter_deal_stage: Optional filter for deal stage
    
    Returns:
        Dictionary containing preprocessed data for bulk operations
    """
    api_client = get_hubspot_client(channel)
    
    # Containers for bulk data
    bulk_data = {
        'deals_data': [],
        'contacts_data': {},
        'companies_data': {},
        'deal_to_contact': {},
        'deal_to_company': {},
        'existing_order_platforms': {},
        'existing_contacts': {},
        'existing_companies': {},
    }
    
    # Get existing order platforms to avoid duplicates
    existing_platforms = ShopTurboOrdersPlatforms.objects.filter(
        channel=channel
    ).values_list('platform_order_id', flat=True)
    bulk_data['existing_order_platforms'] = set(existing_platforms)
    
    # Get existing contacts and companies
    existing_contact_platforms = ContactsPlatforms.objects.filter(
        channel=channel
    ).select_related('contact')
    bulk_data['existing_contacts'] = {
        cp.platform_id: cp.contact for cp in existing_contact_platforms
        if cp.contact
    }
    
    existing_company_platforms = CompanyPlatforms.objects.filter(
        channel=channel
    ).select_related('company')
    bulk_data['existing_companies'] = {
        cp.platform_id: cp.company for cp in existing_company_platforms
        if cp.company
    }
    
    # Process each deal and extract data
    for deal in deals:
        try:
            # Extract basic deal data
            deal_data = {
                'deal_id': deal.id,
                'deal_name': deal.properties.get('dealname'),
                'amount': float(deal.properties.get('amount', 0)) or 0,
                'currency': deal.properties.get('deal_currency_code', 'JPY'),
                'deal_stage': deal.properties.get('dealstage'),
                'create_date': deal.properties.get('createdate'),
                'close_date': deal.properties.get('closedate'),
                'order_at': deal.properties.get('closedate') or deal.properties.get('createdate'),
                'deal_properties': deal.properties,
            }
            
            # Get associated contact
            contact_id = None
            if deal.associations and "contacts" in deal.associations:
                contact_id = deal.associations["contacts"].results[0].id
                deal_data['contact_id'] = contact_id
                bulk_data['deal_to_contact'][deal.id] = contact_id
                
                # Fetch contact data if not already fetched
                if contact_id not in bulk_data['contacts_data']:
                    try:
                        contact_response = api_client.crm.contacts.basic_api.get_by_id(
                            contact_id, properties=contact_properties
                        )
                        contact_data = contact_response.properties
                        contact_data['hs_object_id'] = contact_id  # Add the HubSpot object ID
                        bulk_data['contacts_data'][contact_id] = contact_data
                    except Exception as e:
                        print(f"Error fetching contact {contact_id}: {str(e)}")
            
            # Get associated company
            company_id = None
            if deal.associations and "companies" in deal.associations:
                company_id = deal.associations["companies"].results[0].id
                deal_data['company_id'] = company_id
                bulk_data['deal_to_company'][deal.id] = company_id
                
                # Fetch company data if not already fetched
                if company_id not in bulk_data['companies_data']:
                    try:
                        company_response = api_client.crm.companies.basic_api.get_by_id(
                            company_id, properties=company_properties
                        )
                        company_data = company_response.properties
                        company_data['hs_object_id'] = company_id  # Add the HubSpot object ID
                        bulk_data['companies_data'][company_id] = company_data
                    except Exception as e:
                        print(f"Error fetching company {company_id}: {str(e)}")
            
            # Handle additional company lookup for deals without filter_deal_stage
            if not filter_deal_stage and deal_to_company and str(deal.id) in deal_to_company:
                additional_company_id = deal_to_company[str(deal.id)]
                if additional_company_id not in bulk_data['companies_data']:
                    try:
                        company_response = api_client.crm.companies.basic_api.get_by_id(
                            additional_company_id, properties=company_properties
                        )
                        company_data = company_response.properties
                        company_data['hs_object_id'] = additional_company_id  # Add the HubSpot object ID
                        bulk_data['companies_data'][additional_company_id] = company_data
                        # Update the deal data with the additional company
                        deal_data['company_id'] = additional_company_id
                        bulk_data['deal_to_company'][deal.id] = additional_company_id
                    except Exception as e:
                        print(f"Error fetching additional company {additional_company_id}: {str(e)}")
            
            bulk_data['deals_data'].append(deal_data)
            
        except Exception as e:
            print(f"Error preprocessing deal {deal.id}: {str(e)}")
    
    print(f"DEBUG: Preprocessed {len(bulk_data['deals_data'])} deals")
    print(f"DEBUG: Found {len(bulk_data['contacts_data'])} unique contacts")
    print(f"DEBUG: Found {len(bulk_data['companies_data'])} unique companies")
    
    return bulk_data


def bulk_create_or_update_contacts(
    contacts_data: Dict[str, Dict],
    workspace,
    channel: Channel,
    existing_contacts: Dict[str, Contact],
) -> Dict[str, Contact]:
    """
    Bulk create or update contacts from preprocessed data.
    
    Returns:
        Dictionary mapping contact IDs to Contact instances
    """
    contacts_to_create = []
    contacts_to_update = []
    contact_platforms_to_create = []
    contact_id_to_instance = {}
    
    for contact_id, contact_data in contacts_data.items():
        if contact_id in existing_contacts:
            # Update existing contact
            contact = existing_contacts[contact_id]
            contact_id_to_instance[contact_id] = contact
            
            # Update contact fields
            needs_update = False
            if contact_data.get('phone') and contact.phone_number != contact_data['phone']:
                contact.phone_number = contact_data['phone']
                needs_update = True
            if contact_data.get('lastname') and contact.last_name != contact_data['lastname']:
                contact.last_name = contact_data['lastname']
                needs_update = True
            if needs_update and contact not in contacts_to_update:
                contacts_to_update.append(contact)
        else:
            # Create new contact - use same pattern as original create_or_update_contact
            try:
                contact, created = Contact.objects.get_or_create(
                    workspace=workspace,
                    email=contact_data.get('email'),
                    name=contact_data.get('firstname', ''),
                )
                if not created:
                    # If contact already exists, add it to existing_contacts for future use
                    existing_contacts[contact_id] = contact
                    contact_id_to_instance[contact_id] = contact
                else:
                    contacts_to_create.append(contact)
                    contact_id_to_instance[contact_id] = contact
                    
                    # Update phone and last_name after creation
                    needs_update = False
                    if contact_data.get('phone'):
                        contact.phone_number = contact_data.get('phone')
                        needs_update = True
                    if contact_data.get('lastname'):
                        contact.last_name = contact_data.get('lastname')
                        needs_update = True
                    if needs_update and contact not in contacts_to_update:
                        contacts_to_update.append(contact)
                
                # Prepare contact platform
                contact_platform = ContactsPlatforms(
                    channel=channel,
                    platform_id=contact_id,
                    contact=contact,
                )
                contact_platforms_to_create.append((contact_platform, contact))
            except Exception as e:
                print(f"ERROR: Failed to create contact for ID {contact_id}: {str(e)}")
                # Try to find existing contact
                contact = Contact.objects.filter(
                    workspace=workspace,
                    email=contact_data.get('email'),
                    name=contact_data.get('firstname', ''),
                ).first()
                if contact:
                    contact_id_to_instance[contact_id] = contact
                    # Prepare contact platform
                    contact_platform = ContactsPlatforms(
                        channel=channel,
                        platform_id=contact_id,
                        contact=contact,
                    )
                    contact_platforms_to_create.append((contact_platform, contact))
    
    # Create contact platforms (contacts were created individually above)
    if contact_platforms_to_create:
        with transaction.atomic():
            platforms_to_create = []
            for contact_platform, contact in contact_platforms_to_create:
                contact_platform.contact = contact
                platforms_to_create.append(contact_platform)
            
            ContactsPlatforms.objects.bulk_create(platforms_to_create, batch_size=100)
    
    # Bulk update contacts
    if contacts_to_update:
        Contact.objects.bulk_update(
            contacts_to_update, 
            ['phone_number', 'last_name'], 
            batch_size=100
        )
    
    print(f"DEBUG: Processed {len(contact_id_to_instance)} contacts")
    print(f"DEBUG: Updated {len(contacts_to_update)} contact fields")
    
    return contact_id_to_instance


def bulk_create_or_update_companies(
    companies_data: Dict[str, Dict],
    workspace,
    channel: Channel,
    existing_companies: Dict[str, Company],
) -> Dict[str, Company]:
    """
    Bulk create or update companies from preprocessed data.
    
    Returns:
        Dictionary mapping company IDs to Company instances
    """
    companies_to_create = []
    companies_to_update = []
    company_platforms_to_create = []
    company_id_to_instance = {}
    
    for company_id, company_data in companies_data.items():
        if company_id in existing_companies:
            # Update existing company
            company = existing_companies[company_id]
            company_id_to_instance[company_id] = company
            
            # Update company fields
            needs_update = False
            if company_data.get('phone') and company.phone_number != company_data['phone']:
                company.phone_number = company_data['phone']
                needs_update = True
            if company_data.get('address') and company.address != company_data['address']:
                company.address = company_data['address']
                needs_update = True
            if needs_update and company not in companies_to_update:
                companies_to_update.append(company)
        else:
            # Create new company - use same pattern as original create_or_update_company
            try:
                company, created = Company.objects.get_or_create(
                    workspace=workspace,
                    name=company_data.get('name', ''),
                    email=company_data.get('email', ''),
                )
                if not created:
                    # If company already exists, add it to existing_companies for future use
                    existing_companies[company_id] = company
                    company_id_to_instance[company_id] = company
                else:
                    companies_to_create.append(company)
                    company_id_to_instance[company_id] = company
                    
                    # Update phone and address after creation
                    needs_update = False
                    if company_data.get('phone'):
                        company.phone_number = company_data.get('phone')
                        needs_update = True
                    if company_data.get('address'):
                        company.address = company_data.get('address')
                        needs_update = True
                    if needs_update and company not in companies_to_update:
                        companies_to_update.append(company)
                
                # Prepare company platform
                company_platform = CompanyPlatforms(
                    channel=channel,
                    platform_id=company_id,
                    company=company,
                )
                company_platforms_to_create.append((company_platform, company))
            except Exception as e:
                print(f"ERROR: Failed to create company for ID {company_id}: {str(e)}")
                # Try to find existing company
                company = Company.objects.filter(
                    workspace=workspace,
                    name=company_data.get('name', ''),
                    email=company_data.get('email', ''),
                ).first()
                if company:
                    company_id_to_instance[company_id] = company
                    # Prepare company platform
                    company_platform = CompanyPlatforms(
                        channel=channel,
                        platform_id=company_id,
                        company=company,
                    )
                    company_platforms_to_create.append((company_platform, company))
    
    # Create company platforms (companies were created individually above)
    if company_platforms_to_create:
        with transaction.atomic():
            platforms_to_create = []
            for company_platform, company in company_platforms_to_create:
                company_platform.company = company
                platforms_to_create.append(company_platform)
            
            CompanyPlatforms.objects.bulk_create(platforms_to_create, batch_size=100)
    
    # Bulk update companies
    if companies_to_update:
        Company.objects.bulk_update(
            companies_to_update, 
            ['phone_number', 'address'], 
            batch_size=100
        )
    
    print(f"DEBUG: Processed {len(company_id_to_instance)} companies")
    print(f"DEBUG: Updated {len(companies_to_update)} company fields")
    
    return company_id_to_instance


def bulk_create_orders_and_platforms(
    bulk_data: Dict[str, Any],
    workspace,
    channel: Channel,
    contact_instances: Dict[str, Contact],
    company_instances: Dict[str, Company],
    how_to_import: str = "create",
    hubspot_import_platform_match: Optional[List] = None,
) -> Dict[str, ShopTurboOrders]:
    """
    Bulk create orders and order platforms from preprocessed data.
    
    Args:
        bulk_data: Preprocessed data from bulk_preprocess_deals_data
        workspace: Workspace instance
        channel: Channel instance
        contact_instances: Dictionary mapping contact IDs to Contact instances
        company_instances: Dictionary mapping company IDs to Company instances
        how_to_import: Import strategy ('create', 'update', 'skip')
        hubspot_import_platform_match: Optional list for platform matching
    
    Returns:
        Dictionary mapping deal IDs to ShopTurboOrders instances
    """
    from data.constants.constant import ORDER_USAGE_CATEGORY
    from utils.meter import has_quota
    
    orders_to_create = []
    platforms_to_create = []
    orders_to_update = []
    platforms_to_update = []
    deal_id_to_order = {}
    
    # Check quota once for all orders
    if not has_quota(workspace, ORDER_USAGE_CATEGORY):
        from data.models import Notification
        message_ja = "制限を超えたため、受注レコードを作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。"
        message_en = "Order could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space."
        
        for user in workspace.user.all():
            Notification.objects.create(
                workspace=workspace,
                user=user,
                message=message_ja if user.verification.language == "ja" else message_en,
                type="error",
            )
        return {}
    
    for deal_data in bulk_data['deals_data']:
        try:
            deal_id = deal_data['deal_id']
            platform_order_id = str(deal_id)
            
            # Skip if already exists and skip mode
            if platform_order_id in bulk_data['existing_order_platforms'] and how_to_import == "skip":
                print(f"DEBUG: Skipping existing order for deal {deal_id}")
                continue
            
            # Create order instance
            order = ShopTurboOrders(
                workspace=workspace,
                currency=deal_data['currency'],
                platform=channel.integration.slug,
                order_type="item_order",
                status="active",
                total_price=deal_data['amount'],
                total_price_without_tax=deal_data['amount'],
                item_price_order=deal_data['amount'],
                order_at=deal_data['order_at'],
            )
            
            # Handle contact/company association
            contact_id = deal_data.get('contact_id')
            company_id = deal_data.get('company_id')
            
            if channel.ms_refresh_token and (contact_id or company_id):
                # Priority to company for MS channels
                if company_id and company_id in company_instances:
                    order.company = company_instances[company_id]
                    order.contact = None
                elif contact_id and contact_id in contact_instances:
                    order.company = company_instances.get(contact_id)  # Check if contact data has company
                    order.contact = None
            elif contact_id and contact_id in contact_instances:
                order.contact = contact_instances[contact_id]
                order.company = None
            elif company_id and company_id in company_instances:
                order.company = company_instances[company_id]
                order.contact = None
            
            orders_to_create.append(order)
            deal_id_to_order[deal_id] = order
            
            # Create corresponding platform
            platform = ShopTurboOrdersPlatforms(
                channel=channel,
                platform_order_id=platform_order_id,
                order_display_name=deal_data['deal_name'] or f"Deal {deal_id}",
                order=order,
            )
            platforms_to_create.append(platform)
            
        except Exception as e:
            print(f"Error preparing order for deal {deal_data.get('deal_id', 'unknown')}: {str(e)}")
    
    # Bulk create orders and platforms
    if orders_to_create:
        with transaction.atomic():
            print(f"DEBUG: Bulk creating {len(orders_to_create)} orders")
            ShopTurboOrders.objects.bulk_create(orders_to_create, batch_size=100)
            
            print(f"DEBUG: Bulk creating {len(platforms_to_create)} order platforms")
            ShopTurboOrdersPlatforms.objects.bulk_create(platforms_to_create, batch_size=100)
    
    print(f"DEBUG: Created {len(orders_to_create)} orders and platforms")
    
    return deal_id_to_order


def bulk_process_custom_fields(
    bulk_data: Dict[str, Any],
    deal_id_to_order: Dict[str, ShopTurboOrders],
    contact_instances: Dict[str, Contact],
    company_instances: Dict[str, Company],
    mapping_custom_fields: Optional[Dict] = None,
    mapping_status_custom_fields: Optional[Dict] = None,
) -> None:
    """
    Bulk process custom fields for all orders.
    
    Args:
        bulk_data: Preprocessed data from bulk_preprocess_deals_data
        deal_id_to_order: Dictionary mapping deal IDs to ShopTurboOrders instances
        contact_instances: Dictionary mapping contact IDs to Contact instances
        company_instances: Dictionary mapping company IDs to Company instances
        mapping_custom_fields: Custom field mappings
        mapping_status_custom_fields: Status custom field mappings
    """
    if not mapping_custom_fields:
        return
    
    custom_values_to_create = []
    custom_values_to_update = []
    
    # Get deal stages and pipelines (simplified - you might want to cache these)
    deal_stages = []
    deal_pipelines = []
    
    stage_mapping = {
        "Appointment Scheduled": "appointmentscheduled",
        "Qualified To Buy": "qualifiedtobuy",
        "Presentation Scheduled": "presentationscheduled",
        "Decision Maker Bought-In": "decisionmakerboughtin",
        "Contract Sent": "contractsent",
        "Closed Won": "closedwon",
        "Closed Lost": "closedlost",
    }
    
    for deal_data in bulk_data['deals_data']:
        deal_id = deal_data['deal_id']
        if deal_id not in deal_id_to_order:
            continue
            
        order = deal_id_to_order[deal_id]
        deal_properties = deal_data['deal_properties']
        
        # Get associated contact and company instances
        contact_id = deal_data.get('contact_id')
        company_id = deal_data.get('company_id')
        associated_contact = contact_instances.get(contact_id)
        associated_company = company_instances.get(company_id)
        
        # Process custom fields for this order
        for hubspot_field, sanka_field in mapping_custom_fields.items():
            try:
                value = None
                
                # Handle special fields
                if hubspot_field == "deal_name":
                    value = deal_properties.get("dealname")
                elif hubspot_field == "deal_stage":
                    deal_stage = deal_properties.get("dealstage")
                    if deal_stage in stage_mapping:
                        value = stage_mapping[deal_stage]
                    elif deal_stages and deal_stage in [stage["label"] for stage in deal_stages]:
                        value = deal_stage
                elif hubspot_field == "issue_date":
                    value = deal_properties.get("createdate")
                elif hubspot_field == "billing_date":
                    value = deal_properties.get("closedate")
                elif hubspot_field == "pipeline|deal":
                    value = deal_properties.get("pipeline")
                elif hubspot_field == "partner_display_name" and order.company:
                    value = order.company.name
                elif "|deal" in hubspot_field:
                    field = hubspot_field.replace("|deal", "")
                    value = deal_properties.get(field)
                elif "|contact" in hubspot_field and associated_contact:
                    field = hubspot_field.replace("|contact", "")
                    contact_field_mapping = {
                        'firstname': 'name',
                        'lastname': 'last_name',
                        'email': 'email',
                        'phone': 'phone_number',
                    }
                    model_field = contact_field_mapping.get(field, field)
                    value = getattr(associated_contact, model_field, None)
                elif "|company" in hubspot_field and associated_company:
                    field = hubspot_field.replace("|company", "")
                    company_field_mapping = {
                        'name': 'name',
                        'phone': 'phone_number',
                        'website': 'website',
                    }
                    model_field = company_field_mapping.get(field, field)
                    value = getattr(associated_company, model_field, None)
                else:
                    value = deal_properties.get(hubspot_field)
                
                if value is not None:
                    # Handle direct order field updates
                    if sanka_field == "deal_stage":
                        order.deal_stage = value
                        order.save(update_fields=['deal_stage'])
                    elif sanka_field == "pipeline|deal":
                        order.pipeline = value
                        order.save(update_fields=['pipeline'])
                    else:
                        # Handle custom field creation (simplified)
                        # In a real implementation, you would batch these operations too
                        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
                            workspace=order.workspace, name=sanka_field
                        ).first()
                        
                        if custom_field:
                            custom_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                                field_name=custom_field,
                                orders=order,
                            )
                            custom_value.value = str(value)
                            custom_value.save()
                            
            except Exception as e:
                print(f"Error processing custom field {hubspot_field} for deal {deal_id}: {str(e)}")
    
    print(f"DEBUG: Processed custom fields for {len(deal_id_to_order)} orders")


def reset_auto_increment_ids(workspace, model_classes: List[tuple]) -> None:
    """
    Reset auto-increment ID fields for multiple models after bulk operations.
    
    Args:
        workspace: Workspace instance
        model_classes: List of tuples (Model, id_field_name) to reset
    
    Example:
        reset_auto_increment_ids(workspace, [
            (ShopTurboOrders, 'order_id'),
            (ShopTurboItems, 'item_id'),
            (Company, 'company_id'),
        ])
    """
    print(f"DEBUG: Resetting auto-increment IDs for {len(model_classes)} models")
    
    for model_class, id_field_name in model_classes:
        try:
            # Get all objects ordered by created_at, then by the id field
            objects_qs = model_class.objects.filter(workspace=workspace).order_by('created_at', id_field_name)
            
            updates_needed = []
            for idx, obj in enumerate(objects_qs, start=1):
                current_id = getattr(obj, id_field_name)
                if current_id != idx:
                    setattr(obj, id_field_name, idx)
                    updates_needed.append(obj)
            
            # Bulk update only objects that need updating
            if updates_needed:
                with transaction.atomic():
                    model_class.objects.bulk_update(updates_needed, [id_field_name], batch_size=100)
                    print(f"DEBUG: Reset {len(updates_needed)} {model_class.__name__}.{id_field_name} values")
            else:
                print(f"DEBUG: No {model_class.__name__}.{id_field_name} values needed resetting")
                
        except Exception as e:
            print(f"ERROR: Failed to reset {model_class.__name__}.{id_field_name}: {str(e)}")


def bulk_process_deals_optimized(
    deals,
    channel_id: str,
    filter_deal_stage: Optional[str] = None,
    mapping_custom_fields: Optional[Dict] = None,
    how_to_import: Optional[str] = None,
    lang: str = "ja",
    how_to_import_items: Optional[str] = None,
    key_item_field: Optional[str] = None,
    mapping_status_custom_fields: Optional[Dict] = None,
    last_index: Optional[int] = None,
    hubspot_filter_import: Optional[Dict] = None,
    history: Optional = None,
) -> bool:
    """
    Optimized bulk processing of HubSpot deals using bulk operations.
    
    This function replaces the individual deal processing loop with bulk operations
    for significantly better performance while preserving all business logic.
    """
    from data.models import TransferHistory
    from utils.logger import logger
    
    task = history
    
    # Check if deals is None or empty
    if deals is None:
        logger.info("ERROR: deals parameter is None - subprocess must fetch deals first")
        if task:
            task.status = "failed"
            task.error_message = "No deals provided to process"
            task.save()
        return False

    logger.info(f"DEBUG: Processing {len(deals)} deals with bulk operations")
    logger.info(f"DEBUG: bulk_process_deals_optimized called with channel_id={channel_id}")
    logger.info(f"DEBUG: filter_deal_stage={filter_deal_stage}")
    logger.info(f"DEBUG: how_to_import={how_to_import}")

    try:
        channel = Channel.objects.get(id=channel_id)
        workspace = channel.workspace
        logger.info(f"DEBUG: Found channel: {channel.name}, workspace: {workspace.name}")
    except Exception as e:
        logger.info(f"Error in bulk_process_deals_optimized: {str(e)}")
        if task:
            task.status = "failed"
            task.save()
        return False

    # Apply filters (same logic as original)
    original_deal_count = len(deals)
    logger.info(f"DEBUG: Starting with {original_deal_count} deals before filtering")

    if hubspot_filter_import and hubspot_filter_import.get("order_status"):
        status_filter = hubspot_filter_import["order_status"]
        logger.info(f"DEBUG: Applying order_status filter: {status_filter}")
        if status_filter["key"] == "includes":
            deals = [
                d
                for d in deals
                if d.properties.get("dealstage") in status_filter["value"]
            ]
            logger.info(f"DEBUG: After includes filter: {len(deals)} deals remain")
        elif status_filter["key"] == "excludes":
            deals = [
                d
                for d in deals
                if d.properties.get("dealstage") not in status_filter["value"]
            ]
            logger.info(f"DEBUG: After excludes filter: {len(deals)} deals remain")

    if filter_deal_stage:
        logger.info(f"DEBUG: Applying deal stage filter: {filter_deal_stage}")
        deals = [d for d in deals if d.properties.get("dealstage") == filter_deal_stage]
        logger.info(f"DEBUG: After deal stage filter: {len(deals)} deals remain")

    if last_index is not None:
        logger.info(f"DEBUG: Applying last_index filter: {last_index}")
        deals = deals[: int(last_index) + 1]
        logger.info(f"DEBUG: After last_index filter: {len(deals)} deals remain")

    logger.info(f"DEBUG: Final deal count after all filters: {len(deals)} (started with {original_deal_count})")

    # Update task with total count
    if task:
        task.total_number = len(deals)
        task.save()

        # update parent task with preventing race condition
        if task.parent:
            try:
                with transaction.atomic():
                    task.parent.refresh_from_db()
                    total_number = (
                        task.parent.total_number
                        if task.parent.total_number is not None
                        else 0
                    )
                    task.parent.total_number = total_number + len(deals)
                    task.parent.save()
            except Exception as e:
                err_msg = (
                    f"Process hubspot deals: Error in updating parent task: {str(e)}"
                )
                logger.info(err_msg)

    # Prepare properties to fetch (same as original)
    properties = [
        "dealname",
        "amount",
        "deal_currency_code",
        "dealstage",
        "createdate",
        "closedate",
    ]
    contact_properties = ["firstname", "lastname", "email", "phone"]
    company_properties = ["name", "email", "phone", "domain"]

    # Add custom fields to properties (same logic as original)
    if mapping_custom_fields:
        for field in mapping_custom_fields:
            if field not in [
                "deal_name",
                "deal_stage",
                "issue_date",
                "billing_date",
                "partner_display_name",
            ]:
                if "|deal" in field:
                    field = field.replace("|deal", "")
                    if field not in properties:
                        properties.append(field)
                elif "|contact" in field:
                    field = field.replace("|contact", "")
                    if field not in contact_properties:
                        contact_properties.append(field)
                elif "|company" in field:
                    field = field.replace("|company", "")
                    if field not in company_properties:
                        company_properties.append(field)

    # Get hubspot_import_platform_match if needed (same logic as original)
    hubspot_import_platform_match = None
    if how_to_import in ["update", "match", "skip"]:
        hubspot_import_platform_match = list(
            Channel.objects.filter(workspace=workspace)
            .exclude(integration__slug="hubspot")
            .values_list("id", flat=True)
        )

        if mapping_custom_fields and (
            "platform|deal" not in mapping_custom_fields
            or "platform_id|deal" not in mapping_custom_fields
        ):
            hubspot_import_platform_match = None

    # Get deal to company mapping if filter_deal_stage is not provided (same as original)
    deal_to_company = {}
    if not filter_deal_stage and deals:
        deal_ids = [deal.id for deal in deals]
        deal_to_company = fetch_companies_in_deals(channel_id, deal_ids)

    # BULK OPERATIONS START HERE
    try:
        logger.info("DEBUG: Starting bulk preprocessing of deals data")
        
        # Step 1: Preprocess all deal data
        bulk_data = bulk_preprocess_deals_data(
            deals=deals,
            channel=channel,
            workspace=workspace,
            contact_properties=contact_properties,
            company_properties=company_properties,
            deal_to_company=deal_to_company,
            filter_deal_stage=filter_deal_stage,
        )
        
        # Step 2: Bulk create/update contacts
        logger.info("DEBUG: Starting bulk contact operations")
        contact_instances = bulk_create_or_update_contacts(
            contacts_data=bulk_data['contacts_data'],
            workspace=workspace,
            channel=channel,
            existing_contacts=bulk_data['existing_contacts'],
        )
        
        # Step 3: Bulk create/update companies
        logger.info("DEBUG: Starting bulk company operations")
        company_instances = bulk_create_or_update_companies(
            companies_data=bulk_data['companies_data'],
            workspace=workspace,
            channel=channel,
            existing_companies=bulk_data['existing_companies'],
        )
        
        # Step 4: Bulk create orders and platforms
        logger.info("DEBUG: Starting bulk order and platform operations")
        deal_id_to_order = bulk_create_orders_and_platforms(
            bulk_data=bulk_data,
            workspace=workspace,
            channel=channel,
            contact_instances=contact_instances,
            company_instances=company_instances,
            how_to_import=how_to_import or "create",
            hubspot_import_platform_match=hubspot_import_platform_match,
        )
        
        # Step 5: Bulk process custom fields
        if mapping_custom_fields:
            logger.info("DEBUG: Starting bulk custom field processing")
            bulk_process_custom_fields(
                bulk_data=bulk_data,
                deal_id_to_order=deal_id_to_order,
                contact_instances=contact_instances,
                company_instances=company_instances,
                mapping_custom_fields=mapping_custom_fields,
                mapping_status_custom_fields=mapping_status_custom_fields,
            )
        
        # Step 6: Reset auto-increment IDs
        logger.info("DEBUG: Resetting auto-increment IDs")
        reset_auto_increment_ids(workspace, [
            (ShopTurboOrders, 'order_id'),
            (Company, 'company_id'),
            (Contact, 'contact_id'),
        ])
        
        # Step 7: Update task progress
        imported_count = len(deal_id_to_order)
        logger.info(f"DEBUG: Successfully processed {imported_count} deals using bulk operations")
        
        if task:
            task.success_number = imported_count
            task.progress = (imported_count / len(deals)) * 100 if deals else 100
            task.save()

            if task.parent:
                try:
                    with transaction.atomic():
                        parent = task.parent
                        parent.refresh_from_db()
                        success_number = parent.success_number or 0
                        parent.success_number = success_number + imported_count
                        parent.progress = (
                            (parent.success_number / parent.total_number) * 100
                            if parent.total_number
                            else 100
                        )
                        parent.save()
                except Exception as e:
                    err_msg = f"Process hubspot deals: Error in updating parent task: {str(e)}"
                    logger.info(err_msg)

        # Mark task as completed
        if task:
            task.status = "completed"
            task.save()
            
            # Update parent task if all children are done
            with transaction.atomic():
                parent_task = task.parent
                if parent_task:
                    parent_task.refresh_from_db()
                    children = TransferHistory.objects.filter(parent=parent_task)
                    if all(child.status != "running" for child in children):
                        parent_task.status = "completed"
                        parent_task.progress = 100
                        parent_task.save()

        return True
        
    except Exception as e:
        logger.info(f"Error in bulk operations: {str(e)}")
        if task:
            task.status = "failed"
            task.error_message = f"Bulk processing error: {str(e)}"
            task.save()
        return False