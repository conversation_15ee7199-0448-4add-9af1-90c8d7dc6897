import ast
import random
import re
import traceback
import uuid
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from typing import List
from types import SimpleNamespace
from zoneinfo import ZoneInfo
from pytz import timezone as pytz_timezone

from dateutil.relativedelta import relativedelta
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.paginator import Paginator
from django.db import connection
from django.db.models import (<PERSON>v<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Count, <PERSON><PERSON>ield,
                              DateTimeField, ExpressionWrapper, F, FloatField,
                              Func, IntegerField, Max, Min, OuterRef, Q,
                              Subquery, Sum, Value, When)
from django.db.models.functions import (Cast, Coalesce, Concat, TruncDate,
                                        TruncHour, TruncMonth, TruncWeek)
from django.utils import timezone

from data.constants.constant import (CHART_CHANNEL_STAT, CHART_POST_STAT,
                                     PANEL_METRIC_TITLE)
from data.constants.date_constant import *
from data.constants.properties_constant import *
from data.models import *
from utils.currency_symbols._constants import *
from utils.currency_symbols.currency_symbols import CurrencySymbols
from utils.filter import build_filters, build_raw_filters, q_has_field
from utils.forecast import Forecast
from utils.formula import calculate_math
from utils.properties.properties import (ALIAS_TABLE, get_list_view_columns,
                                         get_object_display_based_columns,
                                         get_page_object, get_prefix_rel)
from utils.utility import is_valid_uuid, to_snake_case
from utils.reports import get_field_safely

def fetch_panel_pivot_data(panel: ReportPanel, custom_metrics=None, start_date=None, end_date=None, group_by=None, lang='ja'):
    col_data = []
    row_data = []
    start_date_str = ''
    end_date_str = ''
    group_by = group_by if group_by else 'day'

    column_headers = set()
    row_headers = set()
    annotations = set()
    pivot_data = defaultdict(lambda: defaultdict(dict))
    is_error = False

    column_key = None
    row_key = None
    aggregate_field = None
    aggregate_function_name = None

    value_display_settings = []

    # Initialize filter variables with default values
    column_filter_order = 'asc'  # default value
    column_filter_sort_by = 'column'  # default value
    row_filter_order = 'asc'  # default value
    row_filter_sort_by = 'row'  # default value
    is_row_date = False
    value_to_label = {}

    panel_metrics = panel.metrics.all()
    if not panel_metrics:
        print('no panel metrics')
        return column_headers, row_headers, annotations, pivot_data, is_error

    for metric in panel_metrics:
        if metric.name == 'pivot-column':
            column_key = metric.metric
            if column_key == 'item_price_order':
                column_key = 'total_price_without_tax'
        elif metric.name == 'pivot-column-filter-order':
            column_filter_order = metric.metric
        elif metric.name == 'pivot-column-filter-sort-by':
            column_filter_sort_by = metric.metric
        elif metric.name == 'pivot-row':
            row_key = metric.metric
            if row_key == 'item_price_order':
                row_key = 'total_price_without_tax'
        elif metric.name == 'pivot-row-filter-order':
            row_filter_order = metric.metric
        elif metric.name == 'pivot-row-filter-sort-by':
            row_filter_sort_by = metric.metric
        elif metric.name == 'pivot-value':
            try:
                aggregate_field = ast.literal_eval(metric.metric)
            except:
                aggregate_field = [metric.metric]
            if aggregate_field == 'item_price_order':
                aggregate_field = 'total_price_without_tax'
        elif metric.name == 'pivot-aggregate':
            try:
                aggregate_function_name = ast.literal_eval(metric.metric)
            except:
                aggregate_function_name = [metric.metric]
        elif metric.name == 'pivot-value-display':
            try:
                pivot_value_display = ast.literal_eval(metric.metric)
            except:
                pivot_value_display = [metric.metric]

    workspace = panel.workspace
    conditions = Q(workspace=panel.workspace, status='active')
    print('=== Debug Panel Filters', panel.filter)

    if start_date and end_date:
        panel.start_time = start_date
        panel.end_time = end_date
        panel.save()

        start_date_str = start_date.strftime('%Y-%m-%dT%H:%M:%S')
        end_date_str = end_date.strftime('%Y-%m-%dT%H:%M:%S')

    rule_filter = Q()
    if panel.filter and panel.filter['filters']:
        rule_filter = build_filters(panel.filter['filters'])
        print(f'=== report.py - 770: Filter for {panel.name}: {rule_filter}')

    if panel.data_source == 'orders':
        try:
            orders = ShopTurboOrders.objects.filter(conditions, rule_filter)
            if aggregate_field and aggregate_function_name:
                aggregation_functions = {
                    'sum': Sum,
                    'average': Avg,
                    'count': Count,
                    'min': Min,
                    'max': Max,
                    # median not directly supported in Django ORM
                }

                group_by_fields = []
                queryset = orders

                if row_key == column_key:
                    column_key = ''

                if row_key:
                    group_by_fields.append(row_key)

                # Handle column_key
                if column_key:
                    group_by_fields.append(column_key)

                # total_count = queryset.count()

                # Dynamically build annotations
                annotations = {}
                for field in aggregate_field:
                    for func_name in aggregate_function_name:
                        func_name_lower = func_name.lower()
                        if func_name_lower == 'percentage_row':
                            row_total_subquery = orders.filter(
                                **{row_key: OuterRef(row_key)}
                            ).values(row_key).annotate(
                                row_sum=Count(field)
                            ).values('row_sum')
                            annotations[f'{field}_percentage_row'] = ExpressionWrapper(
                                Cast(
                                    (Count(field) * 100.0) /
                                    Subquery(row_total_subquery),
                                    output_field=FloatField(
                                        max_digits=5, decimal_places=2)
                                ),
                                output_field=FloatField()
                            )

                        elif func_name_lower == 'percentage_column':
                            column_total_subquery = orders.filter(
                                **{column_key: OuterRef(column_key)}
                            ).values(column_key).annotate(
                                col_sum=Count(field)
                            ).values('col_sum')
                            annotations[f'{field}_percentage_column'] = ExpressionWrapper(
                                Cast(
                                    (Count(field) * 100.0) /
                                    Subquery(column_total_subquery),
                                    output_field=FloatField(
                                        max_digits=5, decimal_places=2)
                                ),
                                output_field=FloatField()
                            )
                        else:
                            aggregation_function_cls = aggregation_functions.get(
                                func_name_lower, Sum)
                            annotations[f'{field}_{func_name_lower}'] = aggregation_function_cls(
                                field)
                # Build queryset with grouping
                queryset = queryset.values(
                    *group_by_fields).annotate(**annotations)
                pivot_data = defaultdict(lambda: defaultdict(dict))
                row_headers = set()
                column_headers = set()

                for item in queryset:
                    row_value = item.get(
                        row_key) if row_key else '合計' if lang == 'ja' else 'Total'
                    col_value = item.get(
                        column_key) if column_key else '合計' if lang == 'ja' else 'Total'

                    row_headers.add(row_value)
                    column_headers.add(col_value)

                    # Populate pivot data
                    for field, value in item.items():
                        if field in annotations:
                            pivot_data[row_value][col_value].setdefault(
                                field, 0)
                            pivot_data[row_value][col_value][field] = value if value is not None else 0
            else:
                if not aggregate_field:
                    aggregate_field = 'total_price_without_tax'

                if not aggregation_function_cls:
                    aggregation_function_cls = Sum

                total_agg_value = orders.aggregate(
                    agg_value=aggregation_function_cls(aggregate_field))['agg_value']
                col_data = ['Total']
                row_data = [{'row_key': 'Total', 'Total': total_agg_value}]

        except Exception as e:
            print('=== DEBUG (Pivot Table): Error in Query result Pivoting Table}')
            print(e)
            pivot_data = defaultdict(lambda: defaultdict(dict))
            row_headers = set()
            column_headers = set()
            annotations = set()
            is_error = True
    elif panel.data_source == 'inventory':
        try:
            inventory = ShopTurboInventory.objects.filter(
                conditions, rule_filter)
            if aggregate_field and aggregate_function_name:
                aggregation_functions = {
                    'sum': Sum,
                    'average': Avg,
                    'count': Count,
                    'min': Min,
                    'max': Max,
                    # median not directly supported in Django ORM
                }

                group_by_fields = []
                queryset = inventory

                if row_key == column_key:
                    column_key = ''

                if row_key:
                    group_by_fields.append(row_key)

                # Handle column_key
                if column_key:
                    group_by_fields.append(column_key)

                total_count = queryset.count()

                # Dynamically build annotations
                annotations = {}
                for field in aggregate_field:
                    for func_name in aggregate_function_name:
                        func_name_lower = func_name.lower()
                        if func_name_lower == 'percentage_row':
                            row_total_subquery = inventory.filter(
                                **{row_key: OuterRef(row_key)}
                            ).values(row_key).annotate(
                                row_sum=Count(field)
                            ).values('row_sum')
                            annotations[f'{field}_percentage_row'] = ExpressionWrapper(
                                Cast(
                                    (Count(field) * 100.0) /
                                    Subquery(row_total_subquery),
                                    output_field=FloatField(
                                        max_digits=5, decimal_places=2)
                                ),
                                output_field=FloatField()
                            )

                        elif func_name_lower == 'percentage_column':
                            column_total_subquery = inventory.filter(
                                **{column_key: OuterRef(column_key)}
                            ).values(column_key).annotate(
                                col_sum=Count(field)
                            ).values('col_sum')
                            annotations[f'{field}_percentage_column'] = ExpressionWrapper(
                                Cast(
                                    (Count(field) * 100.0) /
                                    Subquery(column_total_subquery),
                                    output_field=FloatField(
                                        max_digits=5, decimal_places=2)
                                ),
                                output_field=FloatField()
                            )
                        else:
                            aggregation_function_cls = aggregation_functions.get(
                                func_name_lower, Sum)
                            annotations[f'{field}_{func_name_lower}'] = aggregation_function_cls(
                                field)

                # Build queryset with grouping
                queryset = queryset.values(
                    *group_by_fields).annotate(**annotations)
                pivot_data = defaultdict(lambda: defaultdict(dict))
                row_headers = set()
                column_headers = set()

                for item in queryset:
                    row_value = item.get(
                        row_key) if row_key else '合計' if lang == 'ja' else 'Total'
                    col_value = item.get(
                        column_key) if column_key else '合計' if lang == 'ja' else 'Total'

                    row_headers.add(row_value)
                    column_headers.add(col_value)

                    # Populate pivot data
                    for field, value in item.items():
                        if field in annotations:
                            if field in annotations:
                                pivot_data[row_value][col_value].setdefault(
                                    field, 0)
                                pivot_data[row_value][col_value][field] = value if value is not None else 0
            else:
                if not aggregate_field:
                    aggregate_field = 'total_price_without_tax'

                if not aggregation_function_cls:
                    aggregation_function_cls = Sum

                total_agg_value = orders.aggregate(
                    agg_value=aggregation_function_cls(aggregate_field))['agg_value']
                col_data = ['Total']
                row_data = [{'row_key': 'Total', 'Total': total_agg_value}]

        except Exception as e:
            print('=== DEBUG (Pivot Table): Error in Query result Pivoting Table}')
            print(e)
            pivot_data = defaultdict(lambda: defaultdict(dict))
            row_headers = set()
            column_headers = set()
            annotations = set()
            is_error = True
    elif panel.data_source == 'application':
        conditions = Q(workspace=panel.workspace)
        try:
            application = JobApplication.objects.filter(
                conditions, rule_filter)
            if aggregate_field and aggregate_function_name:
                aggregation_functions = {
                    'sum': Sum,
                    'average': Avg,
                    'count': Count,
                    'min': Min,
                    'max': Max,
                    # median not directly supported in Django ORM
                }

                group_by_fields = []
                queryset = application

                if row_key == column_key:
                    column_key = ''

                if row_key:
                    group_by_fields.append(row_key)

                # Handle column_key
                if column_key:
                    group_by_fields.append(column_key)

                total_count = queryset.count()

                # Dynamically build annotations
                annotations = {}
                for field in aggregate_field:
                    for func_name in aggregate_function_name:
                        func_name_lower = func_name.lower()
                        if func_name_lower == 'percentage_row':
                            row_total_subquery = application.filter(
                                **{row_key: OuterRef(row_key)}
                            ).values(row_key).annotate(
                                row_sum=Count(field)
                            ).values('row_sum')
                            annotations[f'{field}_percentage_row'] = ExpressionWrapper(
                                Cast(
                                    (Count(field) * 100.0) /
                                    Subquery(row_total_subquery),
                                    output_field=FloatField(
                                        max_digits=5, decimal_places=2)
                                ),
                                output_field=FloatField()
                            )

                        elif func_name_lower == 'percentage_column':
                            column_total_subquery = application.filter(
                                **{column_key: OuterRef(column_key)}
                            ).values(column_key).annotate(
                                col_sum=Count(field)
                            ).values('col_sum')
                            annotations[f'{field}_percentage_column'] = ExpressionWrapper(
                                Cast(
                                    (Count(field) * 100.0) /
                                    Subquery(column_total_subquery),
                                    output_field=FloatField(
                                        max_digits=5, decimal_places=2)
                                ),
                                output_field=FloatField()
                            )
                        else:
                            aggregation_function_cls = aggregation_functions.get(
                                func_name_lower, Sum)
                            annotations[f'{field}_{func_name_lower}'] = aggregation_function_cls(
                                field)

                # Build queryset with grouping
                queryset = queryset.values(
                    *group_by_fields).annotate(**annotations)
                pivot_data = defaultdict(lambda: defaultdict(dict))
                row_headers = set()
                column_headers = set()

                for item in queryset:
                    row_value = item.get(
                        row_key) if row_key else '合計' if lang == 'ja' else 'Total'
                    col_value = item.get(
                        column_key) if column_key else '合計' if lang == 'ja' else 'Total'

                    row_headers.add(row_value)
                    column_headers.add(col_value)

                    # Populate pivot data
                    for field, value in item.items():
                        if field in annotations:
                            if field in annotations:
                                pivot_data[row_value][col_value].setdefault(
                                    field, 0)
                                pivot_data[row_value][col_value][field] = value if value is not None else 0

            else:
                if not aggregate_field:
                    aggregate_field = 'total_price_without_tax'

                if not aggregation_function_cls:
                    aggregation_function_cls = Sum

                total_agg_value = orders.aggregate(
                    agg_value=aggregation_function_cls(aggregate_field))['agg_value']
                col_data = ['Total']
                row_data = [{'row_key': 'Total', 'Total': total_agg_value}]

        except Exception as e:
            print('=== DEBUG (Pivot Table): Error in Query result Pivoting Table}')
            print(e)
            pivot_data = defaultdict(lambda: defaultdict(dict))
            row_headers = set()
            column_headers = set()
            annotations = set()
            is_error = True
    elif panel.data_source == 'invoices':
        try:
            invoices = Invoice.objects.filter(conditions, rule_filter)
            if aggregate_field and aggregate_function_name:
                aggregation_functions = {
                    'sum': Sum,
                    'average': Avg,
                    'count': Count,
                    'min': Min,
                    'max': Max,
                    # median not directly supported in Django ORM
                }

                group_by_fields = []
                queryset = invoices

                if row_key == column_key:
                    column_key = ''

                # Handle company custom properties for row dimensions
                if row_key and 'custom_field__' in row_key and row_key.startswith('company'):
                    # Extract the custom field ID from formats like:
                    # "company - custom_field__32a9cb93-bb57-43de-acab-7b3666c89be2"
                    company_custom_field_id = row_key.split('custom_field__')[-1]

                    if is_valid_uuid(company_custom_field_id):
                        # Create subquery to get company custom property value
                        company_custom_subquery = CompanyValueCustomField.objects.filter(
                            company=OuterRef('company'),
                            field_name__id=company_custom_field_id
                        ).values('value')[:1]

                        row_alias = f"company_custom_{company_custom_field_id.replace('-', '_')}"
                        queryset = queryset.annotate(**{row_alias: Subquery(company_custom_subquery)})
                        row_key = row_alias

                # Handle company custom properties for column dimensions
                if column_key and 'custom_field__' in column_key and column_key.startswith('company'):
                    # Extract the custom field ID from formats like:
                    # "company - custom_field__32a9cb93-bb57-43de-acab-7b3666c89be2"
                    company_custom_field_id = column_key.split('custom_field__')[-1]

                    if is_valid_uuid(company_custom_field_id):
                        # Create subquery to get company custom property value
                        company_custom_subquery = CompanyValueCustomField.objects.filter(
                            company=OuterRef('company'),
                            field_name__id=company_custom_field_id
                        ).values('value')[:1]

                        column_alias = f"company_custom_{company_custom_field_id.replace('-', '_')}"
                        queryset = queryset.annotate(**{column_alias: Subquery(company_custom_subquery)})
                        column_key = column_alias

                if row_key:
                    group_by_fields.append(row_key)

                # Handle column_key
                if column_key:
                    group_by_fields.append(column_key)

                total_count = queryset.count()

                # Dynamically build annotations
                annotations = {}
                for field in aggregate_field:
                    for func_name in aggregate_function_name:
                        func_name_lower = func_name.lower()
                        if func_name_lower == 'percentage_row':
                            row_total_subquery = invoices.filter(
                                **{row_key: OuterRef(row_key)}
                            ).values(row_key).annotate(
                                row_sum=Count(field)
                            ).values('row_sum')
                            annotations[f'{field}_percentage_row'] = ExpressionWrapper(
                                Cast(
                                    (Count(field) * 100.0) /
                                    Subquery(row_total_subquery),
                                    output_field=FloatField(
                                        max_digits=5, decimal_places=2)
                                ),
                                output_field=FloatField()
                            )
                        elif func_name_lower == 'percentage_column':
                            column_total_subquery = invoices.filter(
                                **{column_key: OuterRef(column_key)}
                            ).values(column_key).annotate(
                                col_sum=Count(field)
                            ).values('col_sum')
                            annotations[f'{field}_percentage_column'] = ExpressionWrapper(
                                Cast(
                                    (Count(field) * 100.0) /
                                    Subquery(column_total_subquery),
                                    output_field=FloatField(
                                        max_digits=5, decimal_places=2)
                                ),
                                output_field=FloatField()
                            )
                        else:
                            aggregation_function_cls = aggregation_functions.get(
                                func_name_lower, Sum)
                            annotations[f'{field}_{func_name_lower}'] = aggregation_function_cls(
                                field)

                # Build queryset with grouping
                if group_by_fields:
                    queryset = queryset.values(
                        *group_by_fields).annotate(**annotations)
                else:
                    queryset = [queryset.aggregate(**annotations)]

                pivot_data = defaultdict(lambda: defaultdict(dict))
                row_headers = set()
                column_headers = set()
                for item in queryset:
                    row_value = item.get(
                        row_key) if row_key else '合計' if lang == 'ja' else 'Total'
                    col_value = item.get(
                        column_key) if column_key else '合計' if lang == 'ja' else 'Total'

                    row_value = row_value if row_value else ""
                    col_value = col_value if col_value else ""

                    row_headers.add(row_value)
                    column_headers.add(col_value)

                    # Populate pivot data
                    for field, value in item.items():
                        if field in annotations:
                            pivot_data[row_value][col_value][field] = value if value is not None else 0

        except Exception as e:
            print('=== DEBUG (Pivot Table): Error in Query result Pivoting Table for invoices')
            print(e)
            pivot_data = defaultdict(lambda: defaultdict(dict))
            row_headers = set()
            column_headers = set()
            annotations = set()
            is_error = True
    elif panel.data_source == 'session_event':
        print('session event')
        end_time = timezone.now()
        start_time = end_time - timedelta(days=7)
        order_ids = ShopTurboOrdersPlatforms.objects.filter(order__workspace=workspace, is_active=True, platform_order_id__isnull=False, created_at__range=(
            start_time, end_time)).values_list('platform_order_id', flat=True).order_by('created_at')
        try:
            for order_id in order_ids[:20]:
                event_prop = PixelEventProperty.objects.filter(
                    pixel_event__workspace=workspace, name='order_id', value=order_id, created_at__range=(start_time, end_time)).first()
                if not event_prop:
                    continue
                session = event_prop.pixel_event.session
                print(session)
                pivot_data[order_id] = {}
                row_headers.add(order_id)
                utm_sources = PixelEventProperty.objects.filter(pixel_event__workspace=workspace, pixel_event__session=session, name='utm_source', created_at__range=(
                    start_time, end_time)).values_list('value', flat=True).distinct()
                for utm_source in utm_sources:
                    utm_mediums = PixelEventProperty.objects.filter(pixel_event__workspace=workspace, pixel_event__session=session, name='utm_medium', created_at__range=(
                        start_time, end_time)).values_list('value', flat=True).distinct()
                    for utm_medium in utm_mediums:
                        pivot_data[order_id][utm_source + '/' + utm_medium] = 1
                        column_headers.add(utm_source + '/' + utm_medium)

        except Exception as e:
            print('=== DEBUG (Pivot Table): Error in Query result Pivoting Table}')
            print(e)
            pivot_data = defaultdict(lambda: defaultdict(dict))
            row_headers = set()
            column_headers = set()
            annotations = set()
            is_error = True
    else:
        conditions = Q(workspace=panel.workspace)
        # panel.data_source is the old code, handling for avoid error
        data_sources = panel.type_objects if panel.type_objects else panel.data_source
        data_sources = data_sources.split(',')
        base_source = data_sources[0]
        relation_source = data_sources[1:]
        row_custom_field = None
        col_custom_field = None
        truncated_field = None
        truncated_col_field = None
        row_truncated_format = None
        col_truncated_format = None
        # clean up the row, column and value

        if ' - ' in row_key:
            if row_key:
                if base_source in row_key:
                    row_key = row_key.replace(f"{base_source} - ", "")
                else:
                    rel, row_key = row_key.split(' - ')
                    prefix = get_prefix_rel(base_source, rel)

                    row_key = row_key.replace(f"{rel} - ", "")
                    row_key = f"{prefix}{row_key}"

        if ' - ' in column_key:
            if column_key:
                if base_source in column_key:
                    column_key = column_key.replace(f"{base_source} - ", "")
                else:
                    rel, column_key = column_key.split(' - ')
                    prefix = get_prefix_rel(base_source, rel)

                    column_key = column_key.replace(f"{rel} - ", "")
                    column_key = f"{prefix}{column_key}"

        if aggregate_field:
            for i in range(len(aggregate_field)):
                if ' - ' in aggregate_field[i]:
                    if base_source in aggregate_field[i]:
                        aggregate_field[i] = aggregate_field[i].replace(
                            f"{base_source} - ", "")
                    else:
                        rel, aggregate_field[i] = aggregate_field[i].split(
                            ' - ')
                        prefix = get_prefix_rel(base_source, rel)

                        aggregate_field[i] = aggregate_field[i].replace(
                            f"{rel} - ", "")
                        aggregate_field[i] = f"{prefix}{aggregate_field[i]}"

        try:
            page_obj = get_page_object(base_source)
            if base_source == TYPE_OBJECT_CASE_LINE_ITEM:
                conditions = Q(deal__workspace=panel.workspace)

            if base_source in [TYPE_OBJECT_ORDER, TYPE_OBJECT_ITEM, TYPE_OBJECT_INVENTORY, TYPE_OBJECT_SUBSCRIPTION, TYPE_OBJECT_CONTACT,
                               TYPE_OBJECT_COMPANY, TYPE_OBJECT_CASE] and not q_has_field(rule_filter, 'status'):
                conditions &= ~Q(status='archived')
            elif base_source in [TYPE_OBJECT_INVENTORY_TRANSACTION, TYPE_OBJECT_PURCHASE_ORDER, TYPE_OBJECT_BILL, TYPE_OBJECT_EXPENSE,
                                 TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_INVOICE, TYPE_OBJECT_SLIP,
                                 ] and not q_has_field(rule_filter, 'usage_status'):
                conditions &= ~Q(usage_status='archived')
            base_model = page_obj['base_model']
            custom_value_model = page_obj['custom_value_model']
            related_name = ""
            for field in custom_value_model._meta.fields:
                if field.related_model == base_model:
                    related_name = field.name
                    break
            dynamic_annotations = {}
            dynamic_group_by = ['id']
            application = base_model.objects.filter(conditions, rule_filter)
            if aggregate_field and aggregate_function_name:
                aggregation_functions = {
                    'sum': Sum,
                    'average': Avg,
                    'count': Count,
                    'min': Min,
                    'max': Max,
                    # median not directly supported in Django ORM
                }

                group_by_fields = []
                order_by_fields = []
                queryset = application

                if row_key == column_key:
                    column_key = ''

                if 'custom_field__' in row_key:
                    row_custom_field_id = row_key.replace('custom_field__', '')

                    row_custom_field = page_obj['custom_model'].objects.filter(
                        id=row_custom_field_id).first()
                    if row_custom_field and row_custom_field.type == 'choice':
                        json_string = row_custom_field.choice_value
                        json_data = ast.literal_eval(json_string)
                        value_to_label = {
                            item['value']: item['label'] for item in json_data}

                    alias = f"row_value_group_{row_custom_field_id}"
                    row_key = alias
                    if row_custom_field and row_custom_field.type in ['date', 'datetime']:
                        dynamic_annotations[alias] = custom_value_model.objects.filter(**{
                            related_name: models.OuterRef('id'),
                            'field_name__id': row_custom_field_id
                        }).values('value_time')[:1]
                    else:
                        dynamic_annotations[alias] = custom_value_model.objects.filter(**{
                            related_name: models.OuterRef('id'),
                            'field_name__id': row_custom_field_id
                        }).values('value')[:1]

                    dynamic_group_by.append(row_key)

                row_type = None
                col_type = None
                tz = pytz_timezone(workspace.timezone)
                if row_key:
                    if '__value' not in row_key:
                        row_type = get_field_safely(base_model, row_key)
                        if row_custom_field:
                            if row_custom_field.type in ['date', 'datetime']:
                                if group_by == 'day':
                                    trunc_func = TruncDate
                                elif group_by == 'week':
                                    trunc_func = TruncWeek
                                elif group_by == 'month':
                                    trunc_func = TruncMonth
                                else:
                                    trunc_func = TruncDate

                                truncated_field = trunc_func(row_key, tzinfo=tz)
                                # queryset = queryset.annotate(**{f"{row_key}_": truncated_field})
                                row_key = f"{row_key}_"
                                is_row_date = True
                            row_type = row_custom_field.type
                        elif isinstance(row_type, (DateField, DateTimeField)):
                            if group_by == 'day':
                                trunc_func = TruncDate
                            elif group_by == 'week':
                                trunc_func = TruncWeek
                            elif group_by == 'month':
                                trunc_func = TruncMonth
                            else:
                                trunc_func = TruncDate

                            truncated_field = trunc_func(row_key, tzinfo=tz)
                            # queryset = queryset.annotate(**{f"{row_key}_": truncated_field})
                            row_key = f"{row_key}_"
                            is_row_date = True
                    group_by_fields.append(row_key)
                    order_by_field = row_key
                    if panel.row_sort_direction == "desc":
                        order_by_field = f"-{row_key}"
                    order_by_fields.append(order_by_field)

                if 'custom_field__' in column_key:
                    column_custom_field_id = column_key.replace(
                        'custom_field__', '')

                    col_custom_field = page_obj['custom_model'].objects.filter(
                        id=column_custom_field_id).first()
                    if col_custom_field and col_custom_field.type == 'choice':
                        json_string = col_custom_field.choice_value
                        json_data = ast.literal_eval(json_string)
                        value_to_label = {
                            item['value']: item['label'] for item in json_data}

                    alias = f"column_value_group_{column_custom_field_id}"
                    column_key = alias

                    if col_custom_field and col_custom_field.type in ['date', 'datetime']:

                        dynamic_annotations[alias] = custom_value_model.objects.filter(**{
                            related_name: models.OuterRef('id'),
                            'field_name__id': column_custom_field_id
                        }).values('value_time')[:1]
                    else:
                        dynamic_annotations[alias] = custom_value_model.objects.filter(**{
                            related_name: models.OuterRef('id'),
                            'field_name__id': column_custom_field_id
                        }).values('value')[:1]
                    dynamic_group_by.append(column_key)

                # Handle column_key
                if column_key:
                    if '__value' not in column_key:
                        col_type = get_field_safely(base_model, column_key)
                        if col_custom_field:
                            if col_custom_field.type in ['date', 'datetime']:
                                if group_by == 'day':
                                    trunc_func = TruncDate
                                elif group_by == 'week':
                                    trunc_func = TruncWeek
                                elif group_by == 'month':
                                    trunc_func = TruncMonth
                                else:
                                    trunc_func = TruncDate

                                truncated_col_field = trunc_func(column_key, tzinfo=tz)
                                # queryset = queryset.annotate(**{f"{column_key}_": truncated_field})
                                column_key = f"{column_key}_"
                                is_col_date = True
                        elif isinstance(col_type, (DateField, DateTimeField)):
                            if group_by == 'day':
                                trunc_func = TruncDate
                            elif group_by == 'week':
                                trunc_func = TruncWeek
                            elif group_by == 'month':
                                trunc_func = TruncMonth
                            else:
                                trunc_func = TruncDate

                            truncated_col_field = trunc_func(column_key, tzinfo=tz)
                            # queryset = queryset.annotate(**{f"{column_key}_": truncated_field})
                            column_key = f"{column_key}_"
                            is_col_date = True
                    group_by_fields.append(column_key)

                total_count = queryset.count()

                # Dynamically build annotations
                annotations = {}
                calculated_dict = {}
                is_annotate_formula = False
                is_values_formula = False
                extra_annotations = {}
                annotation_type = {}
                CURRENCY_DICT = {code: symbol for code,
                                 _, symbol in CURRENCY_MODEL}
                for idx, field in enumerate(aggregate_field):
                    filter_condition = {}
                    custom_field = None
                    annotate_type = "number"

                    if 'custom_field__' in field:
                        field_custom_field_id = field.replace(
                            "custom_field__", "")
                        custom_field = page_obj['custom_model'].objects.get(
                            id=field_custom_field_id)
                        alias = f"field_value_number_{field_custom_field_id}"

                        if custom_field.type in ['number']:
                            dynamic_annotations[alias] = custom_value_model.objects.filter(**{
                                related_name: models.OuterRef('id'),
                                'field_name__id': field_custom_field_id
                            }).values('value_number')[:1]
                        else:
                            dynamic_annotations[alias] = custom_value_model.objects.filter(**{
                                related_name: models.OuterRef('id'),
                                'field_name__id': field_custom_field_id
                            }).values('value')[:1]
                        field = alias
                        dynamic_group_by.append(field)
                        # Default to percent for ratio fields
                        annotate_type = custom_field.number_format if custom_field.number_format else 'percent'
                        if custom_field.type == 'formula':
                            annotate_type = custom_field.value_display

                    func_name = aggregate_function_name[idx]
                    func_name_lower = func_name.lower()
                    if func_name_lower == 'percentage_row':
                        row_total_subquery = application.filter(
                            **{row_key: OuterRef(row_key)}, **filter_condition
                        ).values(row_key).annotate(
                            row_sum=Count(field)
                        ).values('row_sum')
                        annotations[f'{field}_percentage_row'] = ExpressionWrapper(
                            (Count(field) * 100.0) /
                            Subquery(row_total_subquery),
                            output_field=FloatField()
                        )
                        annotate_type = '%'

                    elif func_name_lower == 'percentage_column':
                        column_total_subquery = application.filter(
                            **{column_key: OuterRef(column_key)}, **filter_condition
                        ).values(column_key).annotate(
                            col_sum=Count(field)
                        ).values('col_sum')
                        annotations[f'{field}_percentage_column'] = ExpressionWrapper(
                            (Count(field) * 100.0) /
                            Subquery(column_total_subquery),
                            output_field=FloatField()
                        )
                        annotate_type = '%'
                    else:
                        if custom_field:
                            field = alias
                            dynamic_group_by.append(field)
                            raw_field = field
                            field = to_snake_case(custom_field.name)
                            aggregation_function_cls = aggregation_functions.get(
                                func_name_lower, Sum)
                            annotations[f'{field}_{func_name_lower}'] = aggregation_function_cls(
                                raw_field)

                            if custom_field.type in ['formula']:
                                calculated_dict = {
                                    obj.id: calculate_math(page_obj, obj, custom_field) for obj in queryset
                                }
                                extra_annotations[f"{field}_{func_name_lower}"] = Case(
                                    *[
                                        When(id=obj_id, then=Value(value))
                                        for obj_id, value in calculated_dict.items()
                                    ],
                                    output_field=FloatField()
                                )
                                annotations[f"{field}_{func_name_lower}"] = aggregation_function_cls(
                                    f"{field}_{func_name_lower}")
                        else:
                            aggregation_function_cls = aggregation_functions.get(
                                func_name_lower, Sum)
                            annotations[f'{field}_{func_name_lower}'] = aggregation_function_cls(
                                field)

                    annotation_type[f'{field}_{func_name_lower}'] = annotate_type
                # Build queryset with grouping
                if calculated_dict:
                    queryset = queryset.annotate(**extra_annotations)

                if dynamic_annotations:
                    queryset = queryset.annotate(**dynamic_annotations)
                    queryset = queryset.distinct()

                if truncated_field:
                    # queryset = queryset.annotate(**{f"{row_key}": truncated_field})
                    queryset = queryset.annotate(
                        **{f"{row_key}": Cast(truncated_field, DateField())})

                if truncated_col_field:
                    # queryset = queryset.annotate(**{f"{row_key}": truncated_field})
                    queryset = queryset.annotate(
                        **{f"{column_key}": Cast(truncated_col_field, DateField())})

                if group_by_fields:
                    queryset = queryset.values(
                        *group_by_fields).annotate(**annotations).order_by(*order_by_fields)
                else:
                    queryset = [queryset.aggregate(**annotations)]

                pivot_data = defaultdict(lambda: defaultdict(dict))
                # row_headers = set()
                # column_headers = set()
                row_headers = []
                column_headers = []
                for item in queryset:
                    print(item)
                    if not item.get(row_key):
                        continue
                    row_value = item.get(
                        row_key) if row_key else '合計' if lang == 'ja' else 'Total'
                    col_value = item.get(
                        column_key) if column_key else '合計' if lang == 'ja' else 'Total'

                    row_value = row_value if row_value else ""
                    col_value = col_value if col_value else ""
                    if row_type == 'user':
                        user_obj = User.objects.get(id=row_value)
                        row_value = f"{user_obj.first_name} - {user_obj.email}"

                    if row_value not in row_headers:
                        row_headers.append(row_value)
                    if col_value not in column_headers:
                        column_headers.append(col_value)

                    # Populate pivot data
                    for field, value in item.items():
                        if field in annotations:
                            pivot_data[row_value][col_value].setdefault(
                                field, 0)
                            ann_type = annotation_type.get(field, "")
                            val_display = value if value is not None else 0
                            if ann_type in ['%', 'percent']:
                                # Handle both SQL ratio results and other percentage values
                                val_display = float(val_display)
                                # SQL ratio result (0 to 1)
                                if val_display <= 1:
                                    val_display = f"{(val_display * 100):.2f}%"
                                else:  # Already multiplied by 100
                                    val_display = f"{val_display:.2f}%"
                            elif ann_type == 'number':
                                val_display = '{:,.0f}'.format(val_display)
                            elif (currency_code := ann_type.upper()) in CURRENCY_DICT:
                                currency_symbol = CURRENCY_DICT[currency_code]
                                val_display = '{:,.2f}'.format(val_display) if currency_code.upper(
                                ) != 'JPY' else '{:,.0f}'.format(val_display)
                                val_display = f"{currency_symbol} {val_display}"

                            pivot_data[row_value][col_value][field] = val_display
                print(pivot_data)
            else:
                if not aggregate_field:
                    aggregate_field = 'total_price_without_tax'

                if not aggregation_function_cls:
                    aggregation_function_cls = Sum

                total_agg_value = orders.aggregate(
                    agg_value=aggregation_function_cls(aggregate_field))['agg_value']
                col_data = ['Total']
                row_data = [{'row_key': 'Total', 'Total': total_agg_value}]

            index_annotation = 0

            for field in annotations:
                try:
                    value_display = pivot_value_display[index_annotation]
                    value_display = value_display if value_display else field
                    index_annotation += 1
                except:
                    value_display = field

                value_display_settings.append(value_display)

        except Exception as e:
            traceback.print_exc()
            print('=== DEBUG (Pivot Table): Error in Query result Pivoting Table}')
            print(e)
            pivot_data = defaultdict(lambda: defaultdict(dict))
            row_headers = set()
            column_headers = set()
            annotations = set()
            is_error = True

    print(pivot_data, annotations)
    return column_headers, row_headers, annotations, pivot_data, is_error, value_display_settings, is_row_date, value_to_label
