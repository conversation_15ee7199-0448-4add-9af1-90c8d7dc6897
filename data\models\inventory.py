import uuid

from django.contrib.auth.models import User
from django.db import connection
from django.db import transaction
from django.db import models
from django.utils import timezone

from data.constants.constant import (
    INVENTORY_CF_TYPE,
    INVENTORY_STATUS_CHOICES,
    OBJECTS_CF_TYPE,
)
from data.models import ObjectManager
from data.models.base import save_tags_values, validate_value_custom_field
from data.models.constant import POST_MEDIA_TYPE, USAGE_STATUS, SHOPTURBO_NUMBER_FORMAT
from data.models.item import ITEM_INVENTORY_COMPONENT_PLATFORM_TYPES

SHOPTURBO_INVENTORIES_USAGE_STATUS = [
    ("draft", "Draft"),
    ("active", "Active"),
    ("paused", "Paused"),
    ("canceled", "Canceled"),
    ("archived", "Archived"),
]


class ShopTurboInventory(models.Model):
    class Meta:
        indexes = [
            models.Index(fields=["-date", "-inventory_id", "-created_at"]),
            models.Index(fields=["workspace", "status"]),
            models.Index(fields=["date"]),
        ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    inventory_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)

    name = models.CharField(max_length=500, null=True, blank=True)
    status = models.CharField(
        choices=SHOPTURBO_INVENTORIES_USAGE_STATUS,
        max_length=30,
        null=True,
        blank=True,
        default="active",
    )
    currency = models.CharField(max_length=200, null=True, blank=True)

    item = models.ManyToManyField("ShopTurboItems", blank=True)
    item_variant = models.ManyToManyField("ShopTurboItemsVariations", blank=True)
    available = models.IntegerField(null=False, default=0, blank=False)
    committed = models.IntegerField(null=False, default=0, blank=False)
    unavailable = models.IntegerField(null=False, default=0, blank=False)
    inventory_status = models.CharField(
        choices=INVENTORY_STATUS_CHOICES,
        max_length=30,
        null=True,
        blank=True,
        default="available",
    )
    initial_value = models.IntegerField(null=False, default=0, blank=False)
    total_inventory = models.IntegerField(null=False, default=0, blank=False)
    inventory_value = models.FloatField(null=True, default=0, blank=True)
    warehouse = models.ForeignKey(
        "InventoryWarehouse", on_delete=models.CASCADE, null=True, blank=True
    )

    unit_price = models.FloatField(null=True, blank=True)

    date = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    def get_display_name(self):
        platforms = self.inventory.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.platform_id
        return data

    def get_platform_sku(self):
        platforms = self.inventory.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.platform_sku
        return data

    def get_platform_variant(self):
        platforms = self.inventory.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.variant_id
        return data

    def save(self, *args, **kwargs):
        if "log_data" in kwargs:
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        with transaction.atomic():
            # First, handle the inventory_id generation with proper locking
            if not self.inventory_id:
                # Lock the entire table to prevent race conditions
                with connection.cursor() as cursor:
                    cursor.execute(
                        f'LOCK TABLE "{ShopTurboInventory._meta.db_table}" IN EXCLUSIVE MODE'
                    )

                # Get the latest inventory ID
                last_inventory = (
                    ShopTurboInventory.objects.filter(workspace=self.workspace)
                    .exclude(inventory_id__isnull=True)
                    .order_by("-inventory_id")
                    .first()
                )

                if last_inventory and last_inventory.inventory_id:
                    try:
                        next_inventory_id = int(last_inventory.inventory_id) + 1
                    except (ValueError, TypeError):
                        next_inventory_id = 1
                else:
                    next_inventory_id = 1

                if not self.date:
                    self.date = timezone.now()
                self.inventory_id = f"{next_inventory_id:03d}"

            # Calculate total price
            total_price = 0
            items = self.item.all()
            if items:
                for item in items:
                    if not item.variation_switcher and item.price:
                        total_price += item.price

            # Add item variations to total price
            item_variations = self.item_variant.all()
            if item_variations:
                total_price += sum(v.price for v in item_variations)

            # Calculate inventory value
            if (
                isinstance(self.total_inventory, (int, float))
                and isinstance(total_price, (int, float))
                and self.total_inventory
                and total_price
            ):
                self.inventory_value = total_price * self.total_inventory
            else:
                self.inventory_value = 0.0

            # Single save at the end
            super().save(*args, **kwargs)


SHOPTURBO_INVENTORY_TYPES = [
    ("available", "Available"),
    ("committed", "Committed"),
    ("unavailable", "Unavailable"),
]

SHOPTURBO_INVENTORY_TRANSACTION_TYPES = [
    ("in", "In"),
    ("out", "Out"),
    ("adjust", "Adjust"),
]


class ShopTurboInventoryPlatforms(models.Model):
    inventory = models.ForeignKey(
        ShopTurboInventory,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="inventory",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    platform_sku = models.TextField(null=True, blank=True)
    variant_id = models.CharField(max_length=200, null=True, blank=True)
    location_id = models.CharField(max_length=200, null=True, blank=True)
    inventory_warehouse_id = models.CharField(max_length=200, null=True, blank=True)
    component_type = models.CharField(
        choices=ITEM_INVENTORY_COMPONENT_PLATFORM_TYPES,
        max_length=100,
        default="default",
    )
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class InventoryTransaction(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    transaction_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    inventory = models.ForeignKey(
        ShopTurboInventory, on_delete=models.CASCADE, null=True, blank=True
    )
    inventory_type = models.CharField(
        choices=SHOPTURBO_INVENTORY_TYPES, max_length=30, null=True, blank=True
    )
    transaction_type = models.CharField(
        choices=SHOPTURBO_INVENTORY_TRANSACTION_TYPES,
        max_length=30,
        null=True,
        blank=True,
    )
    amount = models.IntegerField(null=False, default=0, blank=False)
    transaction_amount = models.IntegerField(
        null=True, blank=True
    )  # Inventory Quantity
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    use_unit_value = models.BooleanField(default=False)
    transaction_date = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    price = models.FloatField(null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    average_price = models.FloatField(null=True, blank=True)

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if not self.transaction_id:
            # Generate a new order_id if it doesn't exist
            last_inventory_transaction = (
                InventoryTransaction.objects.filter(workspace=self.workspace)
                .order_by("-transaction_id")
                .first()
            )
            if last_inventory_transaction:
                if last_inventory_transaction.transaction_id:
                    try:
                        last_transaction_id = int(
                            last_inventory_transaction.transaction_id
                        )
                    except:
                        last_transaction_id = 0

                    next_transaction_id = last_transaction_id + 1
                else:
                    next_transaction_id = 1
            else:
                # If it's the first inventory, start with 1
                next_transaction_id = 1

            # Format the transaction_id as a three-digit string
            if not self.transaction_date:
                self.transaction_date = timezone.now()
            self.transaction_id = f"{next_transaction_id:03d}"

        super().save(*args, **kwargs)


class InventoryWarehouse(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    id_iw = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    location = models.CharField(max_length=1000, null=True, blank=True)
    warehouse = models.CharField(max_length=256, null=True, blank=True)
    floor = models.CharField(max_length=256, null=True, blank=True)
    zone = models.CharField(max_length=256, null=True, blank=True)
    aisle = models.CharField(max_length=256, null=True, blank=True)
    rack = models.CharField(max_length=256, null=True, blank=True)
    shelf = models.CharField(max_length=256, null=True, blank=True)
    bin = models.CharField(max_length=256, null=True, blank=True)

    # Related with InventoryWarehousePlatforms Mapping
    map_location_id = models.TextField(null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if not self.id_iw:
            # Generate a new order_id if it doesn't exist
            last_inventory_warehouse = (
                InventoryWarehouse.objects.filter(workspace=self.workspace)
                .order_by("-id_iw")
                .first()
            )
            if last_inventory_warehouse:
                if last_inventory_warehouse.id_iw:
                    try:
                        last_transaction_id = int(last_inventory_warehouse.id_iw)
                    except:
                        last_transaction_id = 0

                    next_transaction_id = last_transaction_id + 1
                else:
                    next_transaction_id = 1
            else:
                # If it's the first inventory, start with 1
                next_transaction_id = 1

            self.id_iw = next_transaction_id

        location = ""
        if self.warehouse:
            location = "-" + self.warehouse
        if self.floor:
            location += "-" + self.floor
        if self.zone:
            location += "-" + self.zone
        if self.aisle:
            location += "-" + self.aisle
        if self.rack:
            location += "-" + self.rack
        if self.shelf:
            location += "-" + self.shelf
        if self.bin:
            location += "-" + self.bin
        if location:
            if location[0] == "-":
                location = location[1:]
        self.location = location

        super().save(*args, **kwargs)


class InventoryWarehousePlatforms(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    warehouse = models.ForeignKey(
        InventoryWarehouse,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="inventory_warehouse_platforms",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    location_id = models.CharField(max_length=200, null=True, blank=True)
    component_type = models.CharField(
        choices=ITEM_INVENTORY_COMPONENT_PLATFORM_TYPES,
        max_length=100,
        default="default",
    )
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class InventoryWarehouseNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class InventoryWarehouseValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        InventoryWarehouseNameCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    warehouse = models.ForeignKey(
        InventoryWarehouse,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="inventory_warehouse_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="inventory-warehouse-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            (self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class InventoryWarehouseValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Inventory Warehouse Custom Field file",
        upload_to="inventory-warehouse-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        InventoryWarehouseValueCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class InventoryTransactionNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class InventoryTransactionValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        InventoryTransactionNameCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    transaction = models.ForeignKey(
        InventoryTransaction,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="inventory_transaction_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="inventory-transaction-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class InventoryTransactionValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Inventory Transaction Custom Field file",
        upload_to="inventory-transaction-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        InventoryTransactionValueCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class ShopTurboInventoryNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=INVENTORY_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)

    # Only for Items-Inventories Platform Sync
    is_shopify_sync = models.BooleanField(default=False)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class ShopTurboInventoryValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ShopTurboInventoryNameCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    inventory = models.ForeignKey(
        ShopTurboInventory,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="shopturbo_inventory_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="shopturbo-inventory-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class ShopTurboInventoryValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Shopturbo Inventory Custom Field file",
        upload_to="shopturbo-inventory-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        ShopTurboInventoryValueCustomField,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


SHOPTURBO_DECIMAL_TYPE = [
    ("normal", "Normal"),
    ("cut_off", "Cut Off"),
    ("cut_over", "Cut Over"),
    ("line_item_cut_off", "Line Item Cut Off"),
    ("line_item_cut_over", "Line Item Cut Over"),
]


class ShopTurboDecimalPoint(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE, null=True)
    app_name = models.CharField(max_length=200, null=True, blank=True)
    type = models.CharField(
        choices=SHOPTURBO_DECIMAL_TYPE, max_length=20, null=True, blank=True
    )
    created_at = models.DateTimeField(default=timezone.now)
