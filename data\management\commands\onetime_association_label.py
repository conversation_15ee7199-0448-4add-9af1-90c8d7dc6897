from data.models import Workspace,TYPE_OBJECT_ORDER,TYPE_OBJECT_INVOICE,TYPE_OBJECT_RECEIPT,TYPE_OBJECT_ESTIMATE,TYPE_OBJECT_SUBSCRIPTION,TYPE_OBJECT_INVENTORY,TYPE_OBJECT_CASE,TYPE_OBJECT_CONTACT,TYPE_OBJECT_COMPANY,TYPE_OBJECT_INVENTORY_TRANSACTION,AssociationLabel
from django.core.management.base import BaseCommand
from data.accounts.association_labels import auto_create_necessary_association_label_setting

class Command(BaseCommand):
    help = 'Create association labels for specified object types. Default behavior: no delete, process all supported types.'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--delete',
            action='store_true',
            help='Delete existing association labels before recreating (default: False)',
        )
        parser.add_argument(
            '--object-types',
            nargs='+',
            choices=['order', 'case', 'contact','company','inventory_transaction','inventory','subscription','estimate','invoice','payment', 'all'],
            default=['all'],
            help='Object types to process (default: all). Options: order, case, contact,company,inventory_transaction,inventory,subscription,estimate,invoice,payment all',
        )
        parser.add_argument(
            '--workspace',
            type=str,
            help='Process only specific workspace by name (optional)',
        )

    def handle(self, *args, **options):
        delete_existing = options['delete']
        object_types = options['object_types']
        workspace_name = options['workspace']
        
        # Map object type strings to constants
        object_type_mapping = {
            'order': TYPE_OBJECT_ORDER,
            'case': TYPE_OBJECT_CASE,
            'contact': TYPE_OBJECT_CONTACT,
            'company': TYPE_OBJECT_COMPANY,
            'inventory_transaction': TYPE_OBJECT_INVENTORY_TRANSACTION,
            'inventory': TYPE_OBJECT_INVENTORY,
            'subscription': TYPE_OBJECT_SUBSCRIPTION,
            'estimate': TYPE_OBJECT_ESTIMATE,
            'invoice': TYPE_OBJECT_INVOICE,
            'payment': TYPE_OBJECT_RECEIPT,
        }
        
        # Determine which object types to process
        if 'all' in object_types:
            types_to_process = [TYPE_OBJECT_ORDER, TYPE_OBJECT_CASE, TYPE_OBJECT_CONTACT,TYPE_OBJECT_COMPANY,TYPE_OBJECT_INVENTORY_TRANSACTION,TYPE_OBJECT_INVENTORY,TYPE_OBJECT_SUBSCRIPTION,TYPE_OBJECT_ESTIMATE,TYPE_OBJECT_INVOICE,TYPE_OBJECT_RECEIPT]
            type_names = ['order', 'case', 'contact','company','inventory_transaction','inventory','subscription','estimate','invoice','payment']
        else:
            types_to_process = [object_type_mapping[obj_type] for obj_type in object_types]
            type_names = object_types
        
        print("Starting association label management script...")
        print(f"Delete existing: {delete_existing}")
        print(f"Object types: {', '.join(type_names)}")
        
        # Filter workspaces if specific workspace is requested
        workspaces = Workspace.objects.all()
        if workspace_name:
            workspaces = workspaces.filter(name__icontains=workspace_name)
            if not workspaces.exists():
                print(f"No workspace found matching '{workspace_name}'")
                return
        
        for workspace in workspaces:
            print(f"\nProcessing workspace: {workspace.name}")
            
            for obj_type, type_name in zip(types_to_process, type_names):
                print(f"  Processing {type_name} associations...")
                
                if delete_existing:
                    deleted_count = AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=obj_type
                    ).count()
                    AssociationLabel.objects.filter(
                        workspace=workspace, 
                        object_source=obj_type
                    ).delete()
                    print(f"    Deleted {deleted_count} existing {type_name} association labels")
                
                # Create/update association labels
                auto_create_necessary_association_label_setting(workspace, obj_type)
                
                # Count final association labels
                final_count = AssociationLabel.objects.filter(
                    workspace=workspace, 
                    object_source=obj_type
                ).count()
                print(f"    Created/updated {type_name} association labels (total: {final_count})")

            print("=" * 50)
            print("MIGRATION COMPLETED!")
            print(f"Workspace: {workspace.name}")
            print(f"Processed types: {', '.join(type_names)}")
            print("=" * 50)



            