{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div id="sync-contacts-table">
    <input type="hidden" name="previous_page" value="{{current_page}}">
    <input type="hidden" name="previous_search_q" value="{{search_q}}">
    <input type="hidden" name="source_object_type" value="{{object_type}}">
    <table class="{% include "data/utility/table.html" %} px-5">
        <thead class="{% include "data/utility/table-header.html" %}">
            <tr>
                <th class="min-w-50px">
                    <span>
                        {{platform}}{% if LANGUAGE_CODE == 'ja' %} ヘッダ {% else %} Header {% endif %}
                    </span>
                </th>
                
                <th class="min-w-50px">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        オブジェクトタイプ
                        {% else %}
                        Object Type
                        {% endif %}
                    </span>
                </th>
                
                <th class="min-w-50px">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        Sankaプロパティ
                        {% else %}
                        Sanka Property
                        {% endif %}
                    </span>
                <th class="min-w-50px text-center">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        除外
                        {% else %}
                        Skip
                        {% endif %}
                    </span>
                </th>
            </tr>
        </thead>
        <tbody class="fs-6" id="body-section">
            {% for header in header_list %}
                <tr>
                    <td>
                        <input name="file-column" type="hidden" value="{{header|parse_header_item}}"/>
                        
                        {% if LANGUAGE_CODE == 'ja' %}
                            {% if header|parse_header_item:'name_ja' %}
                                {{header|parse_header_item:'name_ja'}}
                                <input name="file-column-name" type="hidden" value="{{ header|parse_header_item:'name_ja' }}"/>
                            {% else %} {% comment %} Just in case for some platform not set yet for japanese header {% endcomment %}
                                {{header|parse_header_item:'name'}}
                                <input name="file-column-name" type="hidden" value="{{ header|parse_header_item:'name' }}"/>
                            {% endif %}
                        {% else %}
                            {{header|parse_header_item:'name'}}
                            <input name="file-column-name" type="hidden" value="{{ header|parse_header_item:'name' }}"/>
                        {% endif %}
                    </td>

                    <td>
                        <div>
                            {% if '|order' in header.value %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}受注{% else %}Order{% endif %}</span>
                            {% elif '|lead' in header.value %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}リード{% else %}Lead{% endif %}</span>
                            {% elif object_type == 'company' %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}企業{% else %}Company{% endif %}</span>
                            {% else %}
                                <span style="display: inline-block; max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{% if LANGUAGE_CODE == 'ja' %}連絡先{% else %}Contact{% endif %}</span>
                            {% endif %}
                        </div>
                    </td>

                    <td>
                        {% with header|parse_header_item:'name' as header_type %}
                        <div id="sanka-properties-{{header|parse_header_item}}" class="">
                            <select name="sanka-properties" class="bg-white form-select form-select-solid border h-40px select2-this"
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="ヘッダーの選択"
                                {% else %}
                                data-placeholder="Select Header"
                                {% endif %}
                                >   
                                    {% if '|order' not in header.value %}
                                        <option value="create_new">{% if LANGUAGE_CODE == 'ja' %}新しい Sanka プロパティの作成{% else %}Create New Sanka Text Property{% endif %}</option>

                                        {% if object_type == 'contact' or object_type == 'company' %}
                                            <option value="create_new_choice">
                                                {% if LANGUAGE_CODE == 'ja' %}
                                                    新しい Sanka 選択プロパティの作成
                                                {% else %}
                                                    Create New Sanka Choice Property
                                                {% endif %}
                                            </option>
                                        {% endif %}
                                        
                                        {% if object_type == 'company' %}
                                            {% for column in contacts_columns %}
                                                <option {% if column == header|parse_header_item:'default'%} selected {% endif %} value='{{column}}'>
                                                    {{column|display_column_company:request}}
                                                </option>  
                                            {% endfor %} 
                                        {% endif %}

                                        {% if object_type == 'contact' %}
                                            {% for column in contacts_columns %}
                                                <option {% if column == header|parse_header_item:'default'%} selected {% endif %} value='{{column}}'>
                                                    {{column|display_column_contacts:request}}
                                                </option>  
                                            {% endfor %} 
                                        {% endif %}
                                    {% else %}
                                        <option value="create_new|order">{% if LANGUAGE_CODE == 'ja' %}新しい Sanka プロパティの作成{% else %}Create New Sanka Property{% endif %}</option>
                                        {% for column in platform_columns %}
                                            <option {% if column == header|parse_header_item:'default'%} selected {%endif%} value='{{column}}'>
                                                {{column|display_column_orders:request}}
                                            </option>  
                                        {% endfor %} 
                                    {% endif %}
                            </select>
                        </div>
                        {% endwith %}
                        

                    </td>
                    <td class="text-center">
                        <input type="checkbox" {% if header|parse_header_item:'skip' == 'True'%} checked {%endif%}class="form-check-input">
                        <input type="hidden" name="ignore" value="{{header|parse_header_item:'skip'}}">
                    </td>
                </tr>
                {% comment %} #manage-contacts-view-settings-drawer {% endcomment %}
                <button class="d-none shopturbo-manage-wizard-button" id="create-property-elm-{{header|parse_header_item}}" 
                    hx-get="{% host_url 'create_custom_value' host 'app' %}"
                    hx-vals='{"target_id": "{{header|parse_header_item}}"}'
                    hx-trigger="click"
                    hx-target="#shopturbo-drawer-content">
                    <script>
                        // $(`#select-sanka-properties-{{header|parse_header_item|cut:'|order'|cut:'|lead'}}`).select2();
                        $('.select2-this').select2();

                        // Note: as Per 22 May 2025 - Haegwan told to disable Add Default Value option for Import
                        {% comment %} $(`#select-sanka-properties-{{header|parse_header_item|cut:'|order'|cut:'|lead'}}`).select2().on('select2:open', function() {
                            // Add footer after dropdown opens
                            if (!$(`.select2-footer`).length) {
                                console.log('Heres');
                                $(`.select2-dropdown`).append(`
                                    <div class="select2-footer">
                                        <div class="p-2 fw-bolder fs-5 border-t text-sm text-center">
                                            <!-- Your footer content here -->
                                            <a type='button' class="cursor-pointer fs-8 create-property-in-dropdown">
                                                {% if LANGUAGE_CODE == 'ja'%}デフォルト値を追加{% else %}Add Default Value{% endif %}
                                            </a>
                                        </div>
                                    </div>
                                `);
                                // Add click handler
                                $(`.select2-footer .create-property-in-dropdown`).on('click', function() {
                                    $(`#select-sanka-properties-{{header|parse_header_item|cut:'|order'|cut:'|lead'}}`).select2('close');
                                    var elements = document.getElementById(`create-property-elm-{{header|parse_header_item}}`);
                                    if (elements) {
                                        elements.click();
                                    }
                                });
                            }
                        }); {% endcomment %}
                    </script>
            {% endfor %} 
        </tbody> 
    </table>
    <script>
        $(document).ready(function() {
            document.getElementById('table-body').classList.remove('d-none');
            document.getElementById('loading-spinner-header').classList.add('d-none')
            setupCheckboxHandlers();
        })
    </script>
    </script>
</div>