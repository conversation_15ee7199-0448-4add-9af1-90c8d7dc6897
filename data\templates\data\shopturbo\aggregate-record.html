{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}


<div class="mb-6">
    <span class="py-0 mb-3 border-0 fs-5 fw-bold text-active-primary ms-0">
        {% if LANGUAGE_CODE == 'ja'%}
        オブジェクト
        {% else %}
        Object
        {% endif %}
    </span>
    <select class="{% include 'data/utility/select-form.html' %} select2-this" name="object_type{% if action_index %}-{{action_index}}{% endif %}" id="object_type{% if action_index %}-{{action_index}}{% endif %}" 
    hx-get="{% url 'aggregate_record_name' %}" 
    hx-swap="innerHTML"
    hx-trigger="load, htmx-change"
    hx-vals='{"object_type":"{{object_type}}", "object_name":"{{object_name}}", "action_index":"{{action_index}}", "action_node_id":"{{action_node_id}}"}'
    hx-target="#action-name{% if action_index %}-{{action_index}}{% endif %}"
    onchange="onchangeRecordType(this);">
        <option value="commerce_orders" {% if object_type == 'commerce_orders' %}selected{% endif %}>
            {% if LANGUAGE_CODE == 'ja'%}
            受注
            {% else %}
            Orders
            {% endif %}
        </option>
        <option value="estimates" {% if object_type == 'estimates' %}selected{% endif %}>
            {% if LANGUAGE_CODE == 'ja'%}
            見積
            {% else %}
            Estimates
            {% endif %}
        </option>
        <option value="invoices" {% if object_type == 'invoices' %}selected{% endif %}>
            {% if LANGUAGE_CODE == 'ja'%}
            売上請求
            {% else %}
            Invoices
            {% endif %}
        </option>
        <option value="delivery_slips" {% if object_type == 'delivery_slips' %}selected{% endif %}>
            {% if LANGUAGE_CODE == 'ja'%}
            納品
            {% else %}
            Delivery Notes
            {% endif %}
        </option>
    </select>

    <div id="action-name{% if action_index %}-{{action_index}}{% endif %}" class="mt-3"></div>
    <div id="action-rendered{% if action_index %}-{{action_index}}{% endif %}" class="mt-3"></div>
</div>

<script>
    $('.select2-this').select2();

    $(document).ready(function() {
        // Get the source element
        var sourceElement = document.getElementById('object_type{% if action_index %}-{{action_index}}{% endif %}');
        
        // Set default value if none selected
        if (!sourceElement.value) {
            sourceElement.value = 'commerce_orders';
        }
        
        // First, ensure the proper values are set in the hx-vals attribute
        var currentHxVals = sourceElement.getAttribute('hx-vals');
        var currentHxValsObject = JSON.parse(currentHxVals);
        
        currentHxValsObject.object_type = sourceElement.value;
        sourceElement.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
        
        // Trigger both select2 and htmx changes
        $(sourceElement).trigger('change');
        htmx.trigger(sourceElement, 'htmx-change');
    });

    function onchangeRecordType(element) {
        var currentHxVals = element.getAttribute('hx-vals');
        var currentHxValsObject = JSON.parse(currentHxVals);
        
        let selectedValue = element.value
        if (selectedValue === 'commerce_orders') {
            currentHxValsObject.object_type = 'commerce_orders'
        } else if (selectedValue === 'estimates') {
            currentHxValsObject.object_type = 'estimates'
        } else if (selectedValue === 'invoices') {
            currentHxValsObject.object_type = 'invoices'
        } else if (selectedValue === 'delivery_slips') {
            currentHxValsObject.object_type = 'delivery_slips'
        }

        element.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
        htmx.trigger(element, 'htmx-change');
    }
</script>