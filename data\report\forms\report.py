import json
import logging
import random
import traceback
from django.core.paginator import EmptyPage, Paginator
from django.http import HttpResponse, HttpResponseNotFound, JsonResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse

from data.constants.properties_constant import *
from data.constants.reports_constant import *
from data.models import *
from data.property import properties as forward_properties
from data.report.views import panel_export
from data.report.utility import get_panel_form_context
from utils.actions import log_redirect_failed
from utils.decorator import login_or_hubspot_required
from utils.utility import get_workspace, is_valid_uuid, save_custom_property, assign_object_owner
from utils.workflow import get_nodes
from utils.workspace import get_permission
from utils.properties.properties import get_page_object

logger = logging.getLogger(__name__)

@login_or_hubspot_required
def report_form(request):
    if request.method == 'POST':
        workspace = get_workspace(request.user)
        report_ids = request.POST.getlist('report_ids')
        no_redirect = request.POST.get('no-redirect', False)
        is_template = bool(request.POST.get('is_template', False))
        report_type = request.POST.get('report_type')
        object_type = request.POST.get('object_type')
        owner = request.POST.get('owner')

        if request.POST.get('delete-report'):
            report_ids = request.POST.getlist(
                'report_ids', request.POST.getlist('report_id'))
            if not report_ids:
                return HttpResponse(status=400)
            try:
                for report_id in report_ids:
                    report = Report.objects.get(
                        workspace=workspace, id=report_id)
                    report.delete()
                PanelReportPanel.objects.filter(report__in=report_ids).delete()

                if no_redirect:
                    reports_queryset = Report.objects.filter(is_template=False,
                                                             workspace=workspace, channel=None, is_deleted=False).order_by('-created_at')
                    paginator = Paginator(reports_queryset, MAX_PAGE_CONTENT)
                    page = request.POST.get('page', 1)
                    try:
                        reports_v2 = paginator.page(page)
                    except EmptyPage:
                        page = paginator.num_pages
                        reports_v2 = paginator.page(page)
                    paginator_item_begin = (
                        MAX_PAGE_CONTENT*int(page))-(MAX_PAGE_CONTENT-1)
                    paginator_item_end = MAX_PAGE_CONTENT * \
                        int(page) if MAX_PAGE_CONTENT * \
                        int(page) < paginator.count else paginator.count
                    context = {
                        'reports_v2': reports_v2,
                        'paginator': paginator,
                        'page_type': 'reports_v2',
                        'paginator_item_begin': paginator_item_begin,
                        'paginator_item_end': paginator_item_end,
                        'not_render_template': True
                    }
                    return render(request, 'data/reports/report-table-container.html', context)

                if is_template:
                    try:
                        DashboardTemplate.objects.filter(
                            report_id__in=report_ids).delete()
                    except:
                        pass
                    return redirect(reverse('manage_data', host='app'))

                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_DASHBOARD]
                module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_DASHBOARD).order_by(
                    'order', 'created_at').first()
                if module:
                    module_slug = module.slug
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f"?object_type={object_type}")
                return redirect(reverse('main', host='app'))

            except Exception as e:
                traceback.print_exc()
                print(f'Error: {e}')
                return HttpResponse(status=404)

        name = request.POST.get('name')
        panel_ids_str = request.POST.get('panel_ids')
        panel_ids = panel_ids_str.split(',') if panel_ids_str else []
        report_id = request.POST.get('report_id')
        name_ja = request.POST.get('name_ja', "") 
        description = request.POST.get('description', "") 
        description_ja = request.POST.get('description_ja', "") 
        if not name:
            return HttpResponse(status=400)

        if report_id:
            try:
                report = Report.objects.get(id=report_id)
                report.name = name
                report.name_ja = name_ja
                report.description = description
                report.description_ja = description_ja
                report.is_template = is_template
                report.type = report_type
                report.updated_by = request.user
                report.save()
                PanelReportPanel.objects.filter(report=report).exclude(
                    panel_id__in=panel_ids).delete()
                for order, panel_id in enumerate(panel_ids):
                    PanelReportPanel.objects.update_or_create(
                        report=report,
                        panel_id=panel_id,
                        defaults={'order': order}
                    )

                if report.type == 'summary_table':
                    PanelReportPanel.objects.filter(report=report).delete()
                    _panel = ReportPanel(workspace=workspace,
                                         name='Summaries',
                                         data_source_type='app',
                                         panel_type='summary_table',
                                         is_deleted=True,
                                         is_template=is_template,
                                         ratio=100)
                    panel_config = {}
                    panel_config_name = request.POST.get('config-name')
                    panel_config_value = request.POST.get('config-value')
                    if panel_config_name:
                        panel_config[panel_config_name] = panel_config_value

                    breakdown = request.POST.get('breakdown')
                    if breakdown:
                        panel_config['breakdown'] = breakdown
                    _panel.meta_data = panel_config
                    _panel.save()
                    PanelReportPanel.objects.create(
                        report=report, panel=_panel)

            except Exception as e:
                traceback.print_exc()
                print(f'Error: {e}')

        else:
            report = Report.objects.create(
                workspace=workspace, name=name, name_ja=name_ja, description=description, description_ja=description_ja,
                is_template=is_template, type=report_type, created_by=request.user)
            if len(panel_ids) > 0:
                order = 0
                for panel_id in panel_ids:
                    PanelReportPanel.objects.create(
                        report=report, panel_id=panel_id, order=order)
                    order += 1
            else:
                PanelReportPanel.objects.filter(report=report).delete()

            if report_type == 'summary_table':
                _panel = ReportPanel(workspace=workspace,
                                     name='Summaries',
                                     data_source_type='app',
                                     panel_type='summary_table',
                                     is_deleted=True,
                                     is_template=is_template,
                                     ratio=100)
                panel_config = {}
                panel_config_name = request.POST.get('config-name')
                panel_config_value = request.POST.get('config-value')
                if panel_config_name:
                    panel_config[panel_config_name] = panel_config_value

                breakdown = request.POST.get('breakdown')
                if breakdown:
                    panel_config['breakdown'] = breakdown
                _panel.meta_data = panel_config
                _panel.save()
                PanelReportPanel.objects.create(report=report, panel=_panel)

        assign_object_owner(report,owner,request,object_type)
        report.save()
        save_custom_property(request, report)
        if is_template and False: # Remove this line first, no idea what is this
            try:
                template = DashboardTemplate.objects.get(report=report)
            except:
                template = DashboardTemplate()

            template.name = request.POST.get('name', '')
            template.name_ja = request.POST.get('name_ja', '')
            template.category = request.POST.get('category', '')
            template.category_ja = request.POST.get('category_ja', '')
            template.overview = request.POST.get('overview', '')
            template.overview_ja = request.POST.get('overview_ja', '')
            template.description = request.POST.get('description', '')
            template.description_ja = request.POST.get('description_ja', '')
            template.hide = bool(request.POST.get('hide', False))
            template.workspace_id = request.POST.get('test_workspace_id')
            template.report = report
            template.save()
            return redirect(reverse('manage_dashboard', host='app'))

        return redirect(reverse('report_view', kwargs={'report_id': report.id}, host='app'))

    elif request.method == 'GET':
        object_type = request.GET.get('object_type')

        print('object_type', object_type)
        workspace = get_workspace(request.user)
        lang = request.LANGUAGE_CODE
        page = request.GET.get('page', 1)
        page_obj = get_page_object(object_type, lang)
        page_type = page_obj['page_type']
        page_title = page_obj['page_title']

        permission = get_permission(
            object_type=object_type, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == 'hide':
            context = {
                'permission': permission,

                'page_type': page_type,
                'page_title': page_title,
            }
            return render(request, 'data/reports/reports-list.html', context)

        is_template = bool(request.GET.get('is_template', False))
        panel_id_preselected = request.GET.get('panel_id', None)
        if panel_id_preselected:
            panel_id_preselected = ReportPanel.objects.get(
                id=panel_id_preselected)
        panels = ReportPanel.objects.filter(
            is_template=is_template, is_deleted=False, workspace=workspace).order_by('-panel_id')
        drawer_type = request.GET.get('type')
        context = {
            'panels': panels,
            'type': drawer_type,
            'is_template': is_template,
            'panel_id_preselected': panel_id_preselected,
            'workspaces': Workspace.objects.all(),
            'NameCustomField': ReportNameCustomField.objects.filter(workspace=get_workspace(request.user)).order_by("order"),
            'permission': permission,
            'object_type': object_type
        }
        if drawer_type == 'edit':
            report_id = request.GET.get('report_id')
            try:
                report = Report.objects.get(id=report_id)
                context['report'] = report
            except Exception as e:
                print(f'Error: {e}')

            template = None
            if report_id:
                try:
                    template = DashboardTemplate.objects.get(report=report)
                except Exception as e:
                    print(f'Error: {e}')

                context['template'] = template

            if report and report.owner and report.owner.user:
                permission += f'|{report.owner.user.id}#{request.user.id}'
                context['permission'] = permission

        return render(request, 'data/reports/report-form.html', context)