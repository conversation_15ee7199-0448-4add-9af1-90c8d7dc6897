import traceback

from django.core.paginator import EmptyPage, Paginator
from django.db.models import Q
from django.http import HttpResponse
from django.http.response import JsonResponse
from django.shortcuts import redirect, render
from django.views.decorators.http import require_GET, require_POST

from data.constants.constant import DEFAULT_PERMISSION, OBJECT_GROUP_TYPE
from data.constants.properties_constant import TYPE_OBJECT_COMMERCE_METER
from data.models import (
    AdvanceSearchFilter,
    AppSetting,
    CommerceMeter,
    CommerceMeterValueCustomField,
    Company,
    Contact,
    Notification,
    PropertySet,
    ShopTurboItems,
    ShopTurboSubscriptions,
    TransferHistory,
    View,
    ViewFilter,
)
from data.models.commerce_meter import CommerceMeterNameCustomField
from utils.decorator import login_or_hubspot_required
from utils.logger import logger
from utils.project import get_ordered_views
from utils.properties.properties import get_page_object
from utils.properties.properties import get_default_property_set
from utils.utility import get_permission_filter, is_valid_uuid, save_custom_property
from utils.view.commerce_meter_view_utils import (
    apply_ordering,
    build_base_queryset,
    get_column_labels,
    get_paginated_data,
    get_redirect_link,
    handle_search_and_filtering,
    process_view_columns,
)
from utils.view.view_utils import get_view_and_view_filter
from utils.workspace import get_permission, get_workspace


@login_or_hubspot_required
def commerce_meter(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    page_title = OBJECT_GROUP_TYPE[TYPE_OBJECT_COMMERCE_METER][lang]

    view_id = request.GET.get("view_id", None)
    object_id = request.GET.get("id", None)
    page = request.GET.get("page", 1)
    search_q = request.GET.get("q")

    permission = get_permission(
        object_type=TYPE_OBJECT_COMMERCE_METER, user=request.user
    )
    if not permission:
        permission = DEFAULT_PERMISSION

    if permission == "hide":
        context = {
            "permission": permission,
            "page_title": page_title,
            "object_type": TYPE_OBJECT_COMMERCE_METER,
        }
        return render(request, "data/commerce_meter/commerce-meter.html", context)

    if not view_id:
        view, _ = View.objects.get_or_create(
            workspace=workspace, title__isnull=True, target=TYPE_OBJECT_COMMERCE_METER
        )
        view_filter, is_new_view_filter = ViewFilter.objects.get_or_create(view=view)
        if is_new_view_filter:
            view_filter.view_type = "list"
            view_filter.save()
        view_id = view.id
    get_default_property_set(
        TYPE_OBJECT_COMMERCE_METER, workspace, lang
    )  # to make sure PropertySet are created properly if doesn't exist
    views = get_ordered_views(workspace, TYPE_OBJECT_COMMERCE_METER, user=request.user)

    context = {
        "page_title": page_title,
        "permission": permission,
        "view_id": view_id,
        "search_q": search_q,
        "object_id": object_id,
        "object_type": TYPE_OBJECT_COMMERCE_METER,
        "page": page,
        "views": views,
    }
    return render(request, "data/commerce_meter/commerce-meter.html", context)


@login_or_hubspot_required
@require_GET
def commerce_meter_content(request):
    """Main view function for displaying meter contents with filtering and pagination."""
    # Get request parameters
    workspace = get_workspace(request.user)
    view_id = request.GET.get("view_id", None)
    object_id = request.GET.get("id", None)
    search_q = request.GET.get("q")
    open_drawer = request.GET.get("open_drawer")
    transfer_history_id = request.GET.get("imported")
    module = request.GET.get("module")

    # Handle transfer history if present
    transfer_history = None
    if transfer_history_id:
        transfer_history = TransferHistory.objects.filter(
            id=transfer_history_id
        ).first()

    # Get or create view and filter
    view, view_filter = get_view_and_view_filter(
        workspace, view_id, TYPE_OBJECT_COMMERCE_METER
    )
    view_id = view.id  # In case a new view was created

    # Process columns and view settings
    columns = process_view_columns(view_filter)
    view_filter.column = columns
    view_filter.save()

    column_labels = get_column_labels(columns, workspace)

    # Get app settings
    AppSetting.objects.get_or_create(workspace=workspace, app_target="shopturbo")

    # Build base filter conditions
    filter_conditions = Q(workspace=workspace)

    # Apply permissions
    permission = get_permission(TYPE_OBJECT_COMMERCE_METER, request.user)
    filter_conditions &= get_permission_filter(permission, request.user)

    # Apply status filter
    status = request.GET.get("status")
    filter_conditions &= Q(
        usage_status="archived" if status == "archived" else "active"
    )

    # Handle search and filtering
    advance_search = None
    if transfer_history:
        filter_conditions &= Q(
            inventory_id__in=transfer_history.checkpoint_details.get("imported", [])
        )
    else:
        filter_conditions = handle_search_and_filtering(
            request, workspace, view_filter, filter_conditions, search_q
        )
        # Get advance search for context
        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_COMMERCE_METER, type="default"
        ).first()

    logger.info(filter_conditions)

    commerce_meters = build_base_queryset(filter_conditions)
    logger.info(commerce_meters)

    commerce_meters = apply_ordering(commerce_meters, view_filter)
    logger.info(commerce_meters)

    pagination_data = get_paginated_data(
        commerce_meters, request.GET.get("page"), view_filter.pagination or 25
    )
    logger.info(pagination_data)

    context = {
        "search_q": search_q,
        "commerce_meters": pagination_data["objects"],
        "columns": columns,
        "column_labels": column_labels,
        "page": pagination_data["page_number"],
        "paginator_item_begin": pagination_data["start"],
        "paginator_item_end": pagination_data["end"],
        "paginator": pagination_data["paginator"],
        "config_view": view_filter.view_type,
        "view_filter": view_filter,
        "view_id": view_id,
        "target": TYPE_OBJECT_COMMERCE_METER,
        "object_type": TYPE_OBJECT_COMMERCE_METER,
        "object_id": object_id,
        "permission": permission,
        "open_drawer": open_drawer,
        "advance_search": advance_search,
        "advance_search_filter": view_filter.filter_value
        if not transfer_history
        else None,
        "module": module,
    }

    return render(request, "data/commerce_meter/commerce-meter-content.html", context)


@login_or_hubspot_required
@require_GET
def commerce_meter_table_row(request, id):
    workspace = get_workspace(request.user)
    module = request.GET.get("module")

    view_id = request.GET.get("view_id")
    if not view_id or view_id == "None":
        view_id = (
            View.objects.filter(
                workspace=workspace,
                title__isnull=True,
                target=TYPE_OBJECT_COMMERCE_METER,
            )
            .first()
            .id
        )
    view_filter = ViewFilter.objects.filter(view_id=view_id).first()
    columns = process_view_columns(view_filter)
    if "checkbox" in columns:
        columns.remove("checkbox")

    basic_columns = ["id"]
    custom_columns = []
    select_related = []
    for col in columns:
        if is_valid_uuid(col):
            custom_columns.append(col)
        elif col == "customer":
            basic_columns.append("company")
            basic_columns.append("contact")
            select_related.append("company")
            select_related.append("contact")
        elif col == "item":
            basic_columns.append("item")
            select_related.append("item")
        else:
            basic_columns.append(col)
    try:
        commerce_meter = (
            CommerceMeter.objects.filter(id=id, workspace=workspace)
            .select_related(*select_related)
            .values(*basic_columns)
            .first()
        )
    except:
        logger.error(traceback.format_exc())
        return HttpResponse(status=404)

    if not commerce_meter:
        return HttpResponse(status=404)

    if "item" in basic_columns:
        commerce_meter["item"] = ShopTurboItems.objects.filter(
            id=commerce_meter["item"], workspace=workspace
        ).first()
    if "company" in basic_columns:
        commerce_meter["company"] = Company.objects.filter(
            id=commerce_meter["company"], workspace=workspace
        ).first()
    if "contact" in basic_columns:
        commerce_meter["contact"] = Contact.objects.filter(
            id=commerce_meter["contact"], workspace=workspace
        ).first()

    custom_column_values = {}
    for col in custom_columns:
        value_field = CommerceMeterValueCustomField.objects.filter(
            meter_id=commerce_meter["id"], field_name_id=col
        ).first()
        col = str(col)
        custom_column_values[col] = {
            "field_name": {
                "id": col,
            }
        }
        if value_field:
            custom_column_values[col]["field_name"]["type"] = (
                value_field.field_name.type
            )
            custom_column_values[col]["field_name"]["number_format"] = (
                value_field.field_name.number_format
            )
            if value_field.field_name.type == "image" and value_field.file:
                custom_column_values[col]["value"] = value_field.file.url
            elif value_field.field_name.type in ["date", "date_time"]:
                custom_column_values[col]["value"] = value_field.value_time
            else:
                custom_column_values[col]["value"] = value_field.value

    context = {
        "object_id": id,
        "object_type": TYPE_OBJECT_COMMERCE_METER,
        "commerce_meter": commerce_meter,
        "module": module,
        "columns": columns,
        "basic_columns": basic_columns,
        "custom_columns": custom_columns,
        "custom_column_values": custom_column_values,
    }
    return render(request, "data/commerce_meter/commerce-meter-row.html", context)


@login_or_hubspot_required
def create_commerce_meter(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "POST":
        logger.info(request.POST)
        contact_and_company = request.POST.get("contact_and_company")
        item_id = request.POST.get("item")
        usage = request.POST.get("usage")
        try:
            usage = float(usage)
        except:
            usage = 0
        subscription = request.POST.get("subscription")

        view_id = request.POST.get("view_id", "")
        module_slug = request.POST.get("module")
        redirect_url = get_redirect_link(workspace, module_slug)

        if not (contact_and_company and item_id and usage):
            err_msg = "Failed create Meter: Input was malformed."
            if lang == "ja":
                err_msg = "メーターの作成に失敗しました。"
            Notification.objects.create(
                workspace=workspace, user=request.user, message=err_msg, type="error"
            )
            return redirect(redirect_url)

        try:
            contact = Contact.objects.filter(
                workspace=workspace, id=contact_and_company, status="active"
            ).first()
            company = Company.objects.filter(
                workspace=workspace, id=contact_and_company, status="active"
            ).first()
        except:
            contact = False
            company = False
        if not (contact or company):
            err_msg = "Failed create Meter: Contact or Company not found"
            if lang == "ja":
                err_msg = "連絡先または企業が見つかりませんでした"
            Notification.objects.create(
                workspace=workspace, user=request.user, message=err_msg, type="error"
            )
            return redirect(redirect_url)

        try:
            item = ShopTurboItems.objects.filter(
                workspace=workspace, id=item_id, status="active"
            ).first()
        except:
            item = False

        if not item:
            err_msg = "Failed create Meter: Item not found"
            if lang == "ja":
                err_msg = "商品が見つかりませんでした"
            Notification.objects.create(
                workspace=workspace, user=request.user, message=err_msg, type="error"
            )
            return redirect(redirect_url)

        if subscription:
            try:
                subscription = ShopTurboSubscriptions.objects.filter(
                    workspace=workspace, id=subscription, status="active"
                ).first()
            except:
                subscription = False
            if not subscription:
                err_msg = "Failed create Meter: Subscription not found"
                if lang == "ja":
                    err_msg = "サブスクリプションが見つかりませんでした"
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=err_msg,
                    type="error",
                )
                return redirect(redirect_url)

        if usage <= 0:
            err_msg = "Failed create Meter: Usage must be greater than 0"
            if lang == "ja":
                err_msg = "使用量は0より大きい必要があります"
            Notification.objects.create(
                workspace=workspace, user=request.user, message=err_msg, type="error"
            )
            return redirect(redirect_url)

        cm = CommerceMeter.objects.create(
            workspace=workspace,
            item=item,
            usage=usage,
        )
        if contact:
            cm.contact = contact
        if company:
            cm.company = company
        if subscription:
            cm.subscription = subscription
        cm.save()

        page_obj = get_page_object(TYPE_OBJECT_COMMERCE_METER, lang)
        save_custom_property(request, cm, page_obj)
        if view_id:
            return redirect(redirect_url + f"?id={cm.id}&view_id={view_id}")
        return redirect(redirect_url + f"?id={cm.id}")

    # GET method
    view_id = request.GET.get("view_id")
    if view_id == "None":
        view_id = None
    view = None
    if view_id:
        try:
            view = View.objects.filter(id=view_id, workspace=workspace).first()
        except:
            pass
    if not view:
        view = View.objects.filter(
            workspace=workspace,
            target=TYPE_OBJECT_COMMERCE_METER,
            title__isnull=True,
        ).first()
    default_property_set = (
        PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_COMMERCE_METER
        )
        .filter(Q(as_default=True) | Q(name__isnull=True))
        .first()
    )
    form_properties = default_property_set.children
    custom_properties = []
    for prop in form_properties:
        if is_valid_uuid(prop):
            custom_prop = CommerceMeterNameCustomField.objects.filter(
                workspace=workspace,
                id=prop,
            ).first()
            custom_properties.append(custom_prop)
    context = {
        "form_properties": form_properties,
        "custom_properties": custom_properties,
    }
    return render(request, "data/commerce_meter/create-commerce-meter.html", context)


@login_or_hubspot_required
def manage_commerce_meter_record(request, id):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        view_id = request.GET.get("view_id")
        if view_id == "None":
            view_id = None
        try:
            cm = CommerceMeter.objects.filter(id=id, workspace=workspace).first()
        except:
            return HttpResponse(status=404)

        view = None
        if view_id:
            try:
                view = View.objects.filter(id=view_id, workspace=workspace).first()
            except:
                pass
        if not view:
            view = View.objects.filter(
                workspace=workspace,
                target=TYPE_OBJECT_COMMERCE_METER,
                title__isnull=True,
            ).first()
        default_property_set = (
            PropertySet.objects.filter(
                workspace=workspace, target=TYPE_OBJECT_COMMERCE_METER
            )
            .filter(Q(as_default=True) | Q(name__isnull=True))
            .first()
        )
        form_properties = default_property_set.children
        custom_properties = []
        for prop in form_properties:
            if is_valid_uuid(prop):
                custom_prop = CommerceMeterNameCustomField.objects.filter(
                    workspace=workspace,
                    id=prop,
                ).first()
                custom_properties.append(custom_prop)

        context = {
            "lang": lang,
            "workspace": workspace,
            "object_id": id,
            "cm": cm,
            "form_properties": form_properties,
            "custom_properties": custom_properties,
        }
        return render(
            request, "data/commerce_meter/manage-commerce-meter-record.html", context
        )

    # POST
    logger.info(request.POST)
    view_id = request.POST.get("view_id", "")
    module_slug = request.POST.get("module")
    redirect_url = get_redirect_link(workspace, module_slug)
    try:
        cm = CommerceMeter.objects.filter(id=id, workspace=workspace).first()
    except:
        err_msg = "Failed update Meter: Meter not found"
        if lang == "ja":
            err_msg = "メーターの更新に失敗しました。"
        Notification.objects.create(
            workspace=workspace, user=request.user, message=err_msg, type="error"
        )
        return redirect(redirect_url)

    contact_and_company = request.POST.get("contact_and_company")
    item_id = request.POST.get("item")
    usage = request.POST.get("usage")
    try:
        usage = float(usage)
    except:
        usage = 0
    subscription = request.POST.get("subscription")

    if not (contact_and_company and item_id and usage):
        err_msg = "Failed create Meter: Input was malformed."
        if lang == "ja":
            err_msg = "メーターの作成に失敗しました。"
        Notification.objects.create(
            workspace=workspace, user=request.user, message=err_msg, type="error"
        )
        return redirect(redirect_url)

    try:
        contact = Contact.objects.filter(
            workspace=workspace, id=contact_and_company, status="active"
        ).first()
        company = Company.objects.filter(
            workspace=workspace, id=contact_and_company, status="active"
        ).first()
    except:
        contact = False
        company = False
    if not (contact or company):
        err_msg = "Failed create Meter: Contact or Company not found"
        if lang == "ja":
            err_msg = "連絡先または企業が見つかりませんでした"
        Notification.objects.create(
            workspace=workspace, user=request.user, message=err_msg, type="error"
        )
        return redirect(redirect_url)

    try:
        item = ShopTurboItems.objects.filter(
            workspace=workspace, id=item_id, status="active"
        ).first()
    except:
        item = False

    if not item:
        err_msg = "Failed create Meter: Item not found"
        if lang == "ja":
            err_msg = "商品が見つかりませんでした"
        Notification.objects.create(
            workspace=workspace, user=request.user, message=err_msg, type="error"
        )
        return redirect(redirect_url)

    if subscription:
        try:
            subscription = ShopTurboSubscriptions.objects.filter(
                workspace=workspace, id=subscription, status="active"
            ).first()
        except:
            subscription = False
        if not subscription:
            err_msg = "Failed create Meter: Subscription not found"
            if lang == "ja":
                err_msg = "サブスクリプションが見つかりませんでした"
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=err_msg,
                type="error",
            )
            return redirect(redirect_url)

    if usage <= 0:
        err_msg = "Failed create Meter: Usage must be greater than 0"
        if lang == "ja":
            err_msg = "使用量は0より大きい必要があります"
        Notification.objects.create(
            workspace=workspace, user=request.user, message=err_msg, type="error"
        )
        return redirect(redirect_url)

    cm.item = item
    cm.usage = usage
    if contact:
        cm.contact = contact
    if company:
        cm.company = company
    if subscription:
        cm.subscription = subscription

    page_obj = get_page_object(TYPE_OBJECT_COMMERCE_METER, lang)
    save_custom_property(request, cm, page_obj)
    cm.save(log_data={"user": request.user, "workspace": workspace})
    if view_id:
        return redirect(redirect_url + f"?id={cm.id}&view_id={view_id}")
    return redirect(redirect_url + f"?id={cm.id}")


@login_or_hubspot_required
@require_POST
def toggle_commerce_meter_status(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    status = request.POST.get("status", "")
    object_ids = request.POST.get("object_ids", "")
    object_ids = object_ids.split(",")

    module_slug = request.POST.get("module_slug", "")
    redirect_url = get_redirect_link(workspace, module_slug)

    if not (object_ids and status in ["activate", "archive"]):
        err_msg = "Failed to activate / archive Meter: Input was malformed."
        if lang == "ja":
            err_msg = "メーターの有効化/無効化に失敗しました。"
        Notification.objects.create(
            workspace=workspace, user=request.user, message=err_msg, type="error"
        )
        return redirect(redirect_url)

    CommerceMeter.objects.filter(id__in=object_ids, workspace=workspace).update(
        usage_status=status
    )
    msg = (
        "Meter(s) "
        + ("activated" if status == "activate" else "archived")
        + " successfully."
    )
    if lang == "ja":
        msg = (
            "メーターを"
            + ("有効化" if status == "activate" else "無効化")
            + "しました。"
        )
    Notification.objects.create(
        workspace=workspace, user=request.user, message=msg, type="success"
    )
    return redirect(redirect_url)


@login_or_hubspot_required
@require_GET
def data_transfer_commerce_meter_drawer(request):
    workspace = get_workspace(request.user)
    view_id = request.GET.get("view_id", None)
    module_slug = request.GET.get("module_slug", "")
    section = request.GET.get("section")
    import_export_type = request.GET.get("import_export_type")
    if import_export_type not in ["import", "export"]:
        import_export_type = "import"

    if section not in [
        "history",
        "import_selector",
        "export_selector",
        "bulk-entry",
        "drawer",
    ]:
        return HttpResponse(status=400)

    if section == "history":
        return HttpResponse()
    elif section == "import_selector":
        context = {
            "page_group_type": TYPE_OBJECT_COMMERCE_METER,
            "module": module_slug,
        }
        return render(
            request,
            "data/commerce_meter/data-transfer/import-source-selector.html",
            context,
        )
    elif section == "export_selector":
        return HttpResponse()
    elif section == "bulk-entry":
        context = {
            "view_id": view_id,
            "transfer_history": TransferHistory.objects.filter(
                workspace=workspace, type="import_commerce_meter"
            )
            .order_by("-created_at")
            .first(),
            "module": module_slug,
            "object_type": TYPE_OBJECT_COMMERCE_METER,
        }
        return render(
            request,
            "data/commerce_meter/data-transfer/commerce-meter-bulk-entry.html",
            context,
        )
    elif section == "drawer":
        context = {
            "item_ids": request.GET.get("item_ids", None),
            "import_export_type": import_export_type,
            "page_group_type": TYPE_OBJECT_COMMERCE_METER,
            "view_id": view_id,
            "module": module_slug,
        }
        return render(
            request,
            "data/commerce_meter/data-transfer/data-transfer-commerce-meter-drawer.html",
            context,
        )


@login_or_hubspot_required
def get_meter_options(request):
    workspace = get_workspace(request.user)
    page = request.GET.get("page", 1)

    subscription_ids = request.GET.get("subscription_ids", "")
    subscriptions = []
    if subscription_ids:
        try:
            subscription_ids = subscription_ids.split(",")
            subscriptions = ShopTurboSubscriptions.objects.filter(
                workspace=workspace, id__in=subscription_ids, status="active"
            )
        except Exception as e:
            logger.warning(
                f"Could not retrieve subscriptions with ids {subscription_ids}: {e}"
            )

    meters = CommerceMeter.objects.filter(
        workspace=workspace, usage_status="active", item__isnull=False
    )

    if subscriptions:
        meters = meters.filter(
            Q(company__in=subscriptions.values("company"))
            | Q(contact__in=subscriptions.values("contact"))
        ).distinct()

    res = []
    ITEMS_PER_PAGE = 20
    meters_paginator = Paginator(meters, ITEMS_PER_PAGE)
    paginated_meters = []
    more_pagination = False
    if page:
        try:
            meter_page_content = meters_paginator.page(page if page else 1)
            paginated_meters = meter_page_content.object_list
            more_pagination = meter_page_content.has_next()
        except EmptyPage:
            pass

    for meter in paginated_meters:
        sub_dict = {
            "id": str(meter.id),
            "text": f"#{meter.meter_id:04d} {meter.item.name} {meter.usage}",
        }

        res.append(sub_dict)

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)
