{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<div>
    <label class="form-check-label cursor-pointer fw-semibold text-gray-800">
        <span class="required">
            {% if LANGUAGE_CODE == 'ja' %}
            前回のデータ
            {% else %}
            When Previous data
            {% endif %}
        </span>
    </label>
    <div class="mb-5">
        <select 
            class="h-40px bg-white border form-select form-select-solid condition_data-{% if action_index %}-{{action_index}}{% else %}{{action_counter}}{% endif %}"
            name="action_condition-object_type{% if action_index %}-{{action_index}}{% else %}{{action_counter}}{% endif %}"
            hx-get="{% host_url 'get_property_operator' host 'app' %}"
            hx-trigger="{% if not condition %}load,{% endif %} htmx-change from:this"
            hx-vals='{
                "action_index": "{% if action_index %}{{action_index}}{% else %}{{action_counter}}{% endif %}",
                "action_condition-object_type-{% if action_index %}{{action_index}}{% else %}{{action_counter}}{% endif %}": "commerce_orders"
                {% if condition %}
                "condition_id": "{{condition.id}}",
                {% endif %}
            }'
            hx-target="#condition_content-{% if action_index %}-{{action_index}}{% else %}{{action_counter}}{% endif %}"
        >
            <option value='commerce_orders' {% if condition.object_type == 'commerce_orders' %}selected{% endif %}>
                {% if LANGUAGE_CODE == 'ja' %}注文{% else %}Order{% endif %}
            </option>
        </select>
    </div>
    <script>
        $('.condition_data-{% if action_index %}-{{action_index}}{% else %}{{action_counter}}{% endif %}').select2().on('select2:select', function (e) {
            var selectElement = $(this).closest('select').get(0);
            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
        })
    </script>

    <div id="condition_content-{% if action_index %}-{{action_index}}{% else %}{{action_counter}}{% endif %}" class="mb-5">
    </div>
</div>