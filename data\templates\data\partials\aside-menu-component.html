{% load i18n %}
{% load hosts %}
{% load custom_tags %}

{% if menu_key == 'studio' or menu_key == 'account' %}

	<div class="ps-3 px-5">
		<div class="fs-3 fw-bolder menu-item">
			{% if menu_key == 'workspace' %}
				{% if LANGUAGE_CODE == 'ja'%}
				ワークスペース
				{% else %}
				Workspace
				{% endif %}
			{% elif menu_key == 'account' %}
				{% if LANGUAGE_CODE == 'ja'%}
				アカウント
				{% else %}
				Account
				{% endif %}
			{% elif menu_key == 'studio' %}
				{% if LANGUAGE_CODE == 'ja'%}
				パートナーセンター
				{% else %}
				Partner Center
				{% endif %}
			{% endif %}

		</div>
	</div>
	<div class="menu-item cursor-pointer ps-0">
		<a class="menu-link my-1 p-2 m-0" href="{% host_url 'main' host 'app' %}">
			<span class="menu-icon me-1" >
				<span class="svg-icon svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path opacity="0.5" d="M14.2657 11.4343L18.45 7.25C18.8642 6.83579 18.8642 6.16421 18.45 5.75C18.0358 5.33579 17.3642 5.33579 16.95 5.75L11.4071 11.2929C11.0166 11.6834 11.0166 12.3166 11.4071 12.7071L16.95 18.25C17.3642 18.6642 18.0358 18.6642 18.45 18.25C18.8642 17.8358 18.8642 17.1642 18.45 16.75L14.2657 12.5657C13.9533 12.2533 13.9533 11.7467 14.2657 11.4343Z" fill="currentColor"/>
					<path d="M8.2657 11.4343L12.45 7.25C12.8642 6.83579 12.8642 6.16421 12.45 5.75C12.0358 5.33579 11.3642 5.33579 10.95 5.75L5.40712 11.2929C5.01659 11.6834 5.01659 12.3166 5.40712 12.7071L10.95 18.25C11.3642 18.6642 12.0358 18.6642 12.45 18.25C12.8642 17.8358 12.8642 17.1642 12.45 16.75L8.2657 12.5657C7.95328 12.2533 7.95328 11.7467 8.2657 11.4343Z" fill="currentColor"/>
					</svg>
				</span>
			</span>
			<span class="fs-2 fw-bold fs-lg-5  ">
				{% if LANGUAGE_CODE == 'ja'%}
				ホームに戻る
				{% else %}
				Back to Home
				{% endif %}
			</span>
		</a>
	</div>



	{% if menu_key == 'studio' %}
		<div class="menu-item ps-3">
			<a class="menu-link my-1 p-2 py-1 m-0 ps-2 border-0 fw-bolder {% if page_group_type == 'api' %}active{% endif %}" href="{% host_url 'developers_api' host 'app' %}">
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path opacity="0.3" d="M6 22H4V3C4 2.4 4.4 2 5 2C5.6 2 6 2.4 6 3V22Z" fill="currentColor"/>
							<path d="M18 14H4V4H18C18.8 4 19.2 4.9 18.7 5.5L16 9L18.8 12.5C19.3 13.1 18.8 14 18 14Z" fill="currentColor"/>
						</svg>
					</span>
				</span>

				<span class="fs-2 fw-bold fs-lg-5 ">
					API
				</span>
			</a>
		</div>

	{% elif menu_key == 'account' %}

		<div class="menu-item cursor-pointer ps-1">
			<a href="{% url 'account' %}" class="menu-link p-2 m-0 ps-2 border-0 fw-bolder {% if page_group_type == 'account' %}active{% endif %}">
				<span class="fs-2 fw-bold fs-lg-5 ">
					{% if LANGUAGE_CODE == 'ja'%}
					アカウント
					{% else %}
					Account
					{% endif %}
				</span>
			</a>
		</div>
		<div class="menu-item cursor-pointer ps-1">
			<a href="{% url 'invitation' %}" class="menu-link p-2 m-0 ps-2 border-0 fw-bolder {% if page_group_type == 'invitation' %}active{% endif %}">
				<span class="fs-2 fw-bold fs-lg-5 ">
					{% if LANGUAGE_CODE == 'ja'%}
					招待
					{% else %}
					Invitations
					{% endif %}
				</span>
			</a>
		</div>
		<div class="menu-item cursor-pointer ps-1">
			<a href="{% url 'security' %}" class="menu-link p-2 m-0 ps-2 border-0 fw-bolder {% if page_group_type == 'security' %}active{% endif %}">
				<span class="fs-2 fw-bold fs-lg-5 ">
					{% if LANGUAGE_CODE == 'ja'%}
					セキュリティ
					{% else %}
					Security
					{% endif %}
				</span>
			</a>
		</div>



	{% endif %}

{% else %}
	<div class="dropend me-2" >
		<div class="menu-item cursor-pointer ps-2 py-1 w-100 me-2 input-lg-posthead"
			 data-bs-toggle="tooltip" data-bs-placement="right"
			 {% if LANGUAGE_CODE == 'ja' %}
				title="ショートカット: h"
			 {% else %}
				title="Shortcut: h"
			 {% endif %}
		>
			<a class="menu-link p-2 py-2 ms-0 tw-font-[600] {% if menu_key == 'home' %}active text-white svg-icon-white tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px] {% endif %}" href="{% host_url 'main' host 'app' %}">
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2 {% if menu_key == 'home' %}svg-icon-primary{% endif %}">
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M9 20.9997V13.5997C9 13.0397 9 12.7597 9.10899 12.5457C9.20487 12.3576 9.35785 12.2046 9.54601 12.1087C9.75992 11.9997 10.0399 11.9997 10.6 11.9997H13.4C13.9601 11.9997 14.2401 11.9997 14.454 12.1087C14.6422 12.2046 14.7951 12.3576 14.891 12.5457C15 12.7597 15 13.0397 15 13.5997V20.9997M11.0177 2.76375L4.23539 8.03888C3.78202 8.3915 3.55534 8.56781 3.39203 8.78861C3.24737 8.9842 3.1396 9.20454 3.07403 9.43881C3 9.70327 3 9.99045 3 10.5648V17.7997C3 18.9198 3 19.4799 3.21799 19.9077C3.40973 20.284 3.71569 20.59 4.09202 20.7818C4.51984 20.9997 5.07989 20.9997 6.2 20.9997H17.8C18.9201 20.9997 19.4802 20.9997 19.908 20.7818C20.2843 20.59 20.5903 20.284 20.782 19.9077C21 19.4799 21 18.9198 21 17.7997V10.5648C21 9.99045 21 9.70327 20.926 9.43881C20.8604 9.20454 20.7526 8.9842 20.608 8.78861C20.4447 8.56781 20.218 8.3915 19.7646 8.03888L12.9823 2.76376C12.631 2.4905 12.4553 2.35388 12.2613 2.30136C12.0902 2.25502 11.9098 2.25502 11.7387 2.30136C11.5447 2.35388 11.369 2.4905 11.0177 2.76375Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</span>
				</span>

				<span class="fs-2 fs-lg-5 " >
					{% if LANGUAGE_CODE == 'ja'%}
					ホーム
					{% else %}
					Home
					{% endif %}
				</span>
			</a>
		</div>
	</div>

	{% for module in modules %}
		<div x-data="{ open: {% if menu_key == module.slug %}true{% else %}false{% endif %} }"
     		id="module-{{ forloop.counter }}"
			class="dropend me-2 {% if menu_key == module.slug %} svg-icon-primary {% endif %}"
			>
			<!-- Top-level item -->
			<div class="menu-item cursor-pointer ps-2 input-lg-posthead me-2 w-100" @click="open = !open"
			data-bs-toggle="tooltip" data-bs-placement="right" {% if LANGUAGE_CODE == 'ja' %}
			title="ショートカット: {{ forloop.counter }}" {% else %} title="Shortcut: {{ forloop.counter }}" {% endif %}
			>
				<div
					class="menu-link p-2 py-1 ms-0 py-2">
					<span class="menu-icon me-1">
						<span class="svg-icon svg-icon-2">
							{% if module.icon %}{{module.icon|default:""|safe}}{% endif %}
						</span>
					</span>
					<span class="fs-2 tw-font-[600] fs-lg-5">
						{% if LANGUAGE_CODE == 'ja' %}
						{{ module.name_ja|truncatechars:17 }}
						{% else %}
						{{ module.name|truncatechars:17 }}
						{% endif %}
					</span>
				</div>
			</div>

			<!-- Submenu with Alpine toggle -->
			<div x-show="open" x-transition.duration.200ms class="menu-item-collapsible ps-3 mt-1" style="display: none;">
				{% for module_object in module.object_values|split:"," %}
				<a class="tw-block py-2 {% if menu_key == module.slug and object_type == module_object or menu_key == module.slug and module_object == custom_object_id %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
					href="{% host_url 'load_object_page' module.slug module_object|object_type_slug:request host 'app' %}"
					data-bs-toggle="tooltip" data-bs-placement="right" {% if LANGUAGE_CODE == 'ja' %}
					title="ショートカット: {{ forloop.parentloop.counter }} + {{ forloop.counter }}" {% else %}
					title="Shortcut: {{ forloop.parentloop.counter }} + {{ forloop.counter }}" {% endif %}
					>
					<span class="menu-icon me-1">
						<span class="svg-icon svg-icon-2">
							{% if menu_key == module.slug and object_type == module_object or menu_key == module.slug and module_object == custom_object_id %}
							<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
							</svg>
							{% else %}
							<svg
								xmlns="http://www.w3.org/2000/svg"
								class="svg-icon svg-icon-2"
								width="16" height="16" viewBox="0 0 16 16"
							>
								<!-- no shapes -->
							</svg>
							{% endif %}
						</span>
					</span>
					<span class="fs-2 fs-lg-5">
						{{ module_object|object_group_type:request }}
					</span>
				</a>
				{% endfor %}
			</div>
		</div>
	{% endfor %}

	{% comment %} Workspace {% endcomment %}
	<div x-data="{ open: {% if menu_key == 'workspace' or menu_key == 'logs' %}true{% else %}false{% endif %} }"
		class="dropend me-2"
		>
		<!-- Top-level item -->
		<div class="menu-item cursor-pointer ps-2 input-lg-posthead me-2 w-100" @click="open = !open">
			<div
				class="menu-link p-2 py-1 ms-0 py-2 {% if menu_key == 'workspace' or menu_key == 'logs' %} svg-icon-primary {% endif %}"
				data-bs-toggle="tooltip" data-bs-placement="right" {% if LANGUAGE_CODE == 'ja' %}
				title="ショートカット: w" {% else %} title="Shortcut: w" {% endif %}
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M8.4 3H4.6C4.03995 3 3.75992 3 3.54601 3.10899C3.35785 3.20487 3.20487 3.35785 3.10899 3.54601C3 3.75992 3 4.03995 3 4.6V8.4C3 8.96005 3 9.24008 3.10899 9.45399C3.20487 9.64215 3.35785 9.79513 3.54601 9.89101C3.75992 10 4.03995 10 4.6 10H8.4C8.96005 10 9.24008 10 9.45399 9.89101C9.64215 9.79513 9.79513 9.64215 9.89101 9.45399C10 9.24008 10 8.96005 10 8.4V4.6C10 4.03995 10 3.75992 9.89101 3.54601C9.79513 3.35785 9.64215 3.20487 9.45399 3.10899C9.24008 3 8.96005 3 8.4 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M19.4 3H15.6C15.0399 3 14.7599 3 14.546 3.10899C14.3578 3.20487 14.2049 3.35785 14.109 3.54601C14 3.75992 14 4.03995 14 4.6V8.4C14 8.96005 14 9.24008 14.109 9.45399C14.2049 9.64215 14.3578 9.79513 14.546 9.89101C14.7599 10 15.0399 10 15.6 10H19.4C19.9601 10 20.2401 10 20.454 9.89101C20.6422 9.79513 20.7951 9.64215 20.891 9.45399C21 9.24008 21 8.96005 21 8.4V4.6C21 4.03995 21 3.75992 20.891 3.54601C20.7951 3.35785 20.6422 3.20487 20.454 3.10899C20.2401 3 19.9601 3 19.4 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M19.4 14H15.6C15.0399 14 14.7599 14 14.546 14.109C14.3578 14.2049 14.2049 14.3578 14.109 14.546C14 14.7599 14 15.0399 14 15.6V19.4C14 19.9601 14 20.2401 14.109 20.454C14.2049 20.6422 14.3578 20.7951 14.546 20.891C14.7599 21 15.0399 21 15.6 21H19.4C19.9601 21 20.2401 21 20.454 20.891C20.6422 20.7951 20.7951 20.6422 20.891 20.454C21 20.2401 21 19.9601 21 19.4V15.6C21 15.0399 21 14.7599 20.891 14.546C20.7951 14.3578 20.6422 14.2049 20.454 14.109C20.2401 14 19.9601 14 19.4 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						<path d="M8.4 14H4.6C4.03995 14 3.75992 14 3.54601 14.109C3.35785 14.2049 3.20487 14.3578 3.10899 14.546C3 14.7599 3 15.0399 3 15.6V19.4C3 19.9601 3 20.2401 3.10899 20.454C3.20487 20.6422 3.35785 20.7951 3.54601 20.891C3.75992 21 4.03995 21 4.6 21H8.4C8.96005 21 9.24008 21 9.45399 20.891C9.64215 20.7951 9.79513 20.6422 9.89101 20.454C10 20.2401 10 19.9601 10 19.4V15.6C10 15.0399 10 14.7599 9.89101 14.546C9.79513 14.3578 9.64215 14.2049 9.45399 14.109C9.24008 14 8.96005 14 8.4 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</span>
				</span>
				<span class="fs-2 fs-lg-5 tw-font-[600]">
					{% if LANGUAGE_CODE == 'ja'%}
					ワークスペース
					{% else %}
					Workspace
					{% endif %}
				</span>
			</div>
		</div>

		<!-- Submenu with Alpine toggle -->
		<div x-show="open" x-transition.duration.200ms class="menu-item-collapsible ps-3 mt-1 {% if menu_key == 'workspace' or menu_key == 'logs' %} svg-icon-primary {% endif %}" style="display: none;">
			<a class="tw-block py-2 {% if page_type == 'object_manager' %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'workspace_modules' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if page_type == 'object_manager' %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					オブジェクト管理
					{% else %}
					Object Manager
					{% endif %}
				</span>
			</a>
			<a class="tw-block py-2 {% if page_type == 'data_transfer' %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'data_transfer' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if page_type == 'data_transfer' %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					データ転送
					{% else %}
					Data Transfer
					{% endif %}
				</span>
			</a>
			<a class="tw-block py-2 {% if page_type == 'integrations' %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'integrations' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if page_type == 'integrations' %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					連携サービス
					{% else %}
					Integrations
					{% endif %}
				</span>
			</a>
			<a class="tw-block py-2 {% if page_type == 'datasets' %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'datasets' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if page_type == 'datasets' %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					データセット
					{% else %}
					Datasets
					{% endif %}
				</span>
			</a>
			<a class="tw-block py-2 {% if page_content_key == 'store' %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'store_index' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if page_content_key == 'store' %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					ポータル
					{% else %}
					Portal
					{% endif %}
				</span>
			</a>
			<a class="tw-block py-2 {% if page_type == 'users' or object_type == constant.TYPE_OBJECT_USER_MANAGEMENT %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'workspace' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if page_type == 'users' or object_type == constant.TYPE_OBJECT_USER_MANAGEMENT %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					ユーザー管理
					{% else %}
					User Manager
					{% endif %}
				</span>
			</a>
			<a class="tw-block py-2 {% if page_type == 'usage' %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'workspace_billing' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if page_type == 'usage' %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					プラン & 請求管理
					{% else %}
					Plan & Billing
					{% endif %}
				</span>
			</a>
			<a class="tw-block py-2 {% if menu_key == 'logs' %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'workspace_logs' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if menu_key == 'logs' %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					システムログ
					{% else %}
					System Log
					{% endif %}
				</span>
			</a>
			<a class="tw-block py-2 {% if page_type == 'basic' %}tw-text-[#38383A] tw-font-[600] tw-rounded-lg tw-bg-[#F4F4FF] tw-pl-[5px]{% else %}tw-text-[#8F8E93] tw-font-[400]{% endif %} hover:tw-text-[#472CF5]"
				href="{% host_url 'workspace_basic_info' host 'app' %}"
				>
				<span class="menu-icon me-1">
					<span class="svg-icon svg-icon-2">
						{% if page_type == 'basic' %}
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M4 4V5.4C4 8.76031 4 10.4405 4.65396 11.7239C5.2292 12.8529 6.14708 13.7708 7.27606 14.346C8.55953 15 10.2397 15 13.6 15H20M20 15L15 10M20 15L15 20" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
						</svg>
						{% else %}
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="svg-icon svg-icon-2"
							width="16" height="16" viewBox="0 0 16 16"
						>
							<!-- no shapes -->
						</svg>
						{% endif %}
					</span>
				</span>
				<span class="fs-2 fs-lg-5">
					{% if LANGUAGE_CODE == 'ja'%}
					ワークスペース設定
					{% else %}
					Workspace Settings
					{% endif %}
				</span>
			</a>
		</div>
	</div>


{% endif %}

