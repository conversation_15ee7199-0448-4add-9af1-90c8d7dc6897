{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="card-header" id="kt_help_header">
    <h5 class="{% include "data/utility/card-header.html" %}">
        {% if LANGUAGE_CODE == 'ja' %}
            ダッシュボードテンプレート    
        {% else %}
            Dashboard Templates
        {% endif %}
        
    </h5>
    <div class="card-toolbar">
        <button type="button" class="btn btn-sm btn-icon me-n5" data-kt-drawer-dismiss="true">
            <span class="svg-icon svg-icon-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                    <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                </svg>
            </span>
        </button>
    </div>
</div>
<div class="card-body">
    {% for report in reports %}
        <div class="flex-grow-1 border border-1 rounded p-6 mb-5">
            <div class="mb-3">
                <div class="w-50">
                    <label class="fs-4 fw-bold form-label">
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                                {% if report.name_ja %}
                                    {{report.name_ja}}
                                {% else %}
                                    {{report.name}}
                                {% endif %}
                            {% else %}
                                {{report.name}}
                            {% endif %}
                        </span>
                    </label>
                </div>
            </div>
            <div class=" mb-5">
                <div>
                    {% if LANGUAGE_CODE == 'ja' %}
                        {{report.description_ja}}
                    {% else %}
                        {{report.description}}
                    {% endif %}
                </div>
            </div>
            
            <form method="POST" class="form w-100" action="{% host_url 'dashboards_templates' host 'app' %}">
                {% csrf_token %}
                <input type="hidden" name="report_id" value="{{report.id}}">
                <button type="submit" class="btn btn-sm btn-dark">
                    {% if LANGUAGE_CODE == 'ja'%}
                    テンプレートを使用する
                    {% else %}
                    Use Template
                    {% endif %}
                </button>
            </form>
        </div>
    {% endfor %}
</div>