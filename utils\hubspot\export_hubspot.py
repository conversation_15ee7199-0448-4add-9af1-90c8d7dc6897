from uuid import uuid4
from hubspot import HubSpot
from hubspot.crm.objects import (ApiException,
                                 SimplePublicObjectInputForCreate)
from hubspot.crm import products, deals as hs_deals, line_items, associations, companies
from hubspot.crm.deals import ApiException as DealsApiException
from hubspot.crm.line_items import ApiException as LineItemsApiException
from hubspot.crm.products import ApiException as ProductsApiException
from hubspot.crm.associations import ApiException as AssociationsApiException
from hubspot.crm.associations import BatchInputPublicObjectId
from hubspot.crm.properties import PropertyCreate, PropertyUpdate, ApiException

from hubspot.crm.deals.models import SimplePublicObjectInputForCreate as SimplePublicObjectInputForCreateDeals
from hubspot.crm.contacts import ApiException as ContactsApiException
from hubspot.crm.contacts.models import SimplePublicObjectInput as ContactInput, PublicObjectSearchRequest, Filter, FilterGroup
from hubspot.crm.companies import ApiException as CompaniesApiException
from hubspot.crm.companies.models import SimplePublicObjectInput as CompanyInput
# Import necessary models for V4 batch associations
from hubspot.crm.associations.v4.models import BatchInputPublicAssociationMultiPost, PublicDefaultAssociation

from data.models import *
from utils.logger import logger
from utils.meter import *
from utils.utility import chunks_dict, chunks_list, get_workspace
from collections import defaultdict

import json
import ast
import traceback
import requests
import csv
import uuid
from io import StringIO
from django.core.files.base import ContentFile as DjangoContentFile
from data.constants.constant import HUBSPOT_DEAL_STAGE_MAPPER
from sanka.settings import (HUBSPOT_INTERNAL_ACCESS_TOKEN)

import hmac
import hashlib
import base64
import time
from dateutil import parser
from .utils_hubspot import *
from .owner_hubspot import get_all_owners
from utils.properties.properties import get_page_object,property_display

# Document: https://developers.hubspot.com/docs/api/crm/products
# Flow:
## 1. Fetch batch products by item's sku
## 2. Update data for existed products and create new products
def push_hubspot_items(channel_id: str, sku_map_items: dict[str, ShopTurboItems]):
    skus = list(sku_map_items.keys())

    existed_skus = []
    try:
        results = fetch_batch_products_by_sku(channel_id, skus)
        for result in results:
            existed_skus.append(str(result.properties['hs_sku']))
    except Exception as e:
        print(f'... ERROR when FETCH list product from hubspot: {e}')
        return

    sku_map_existed_items = {}
    sku_map_new_items = {}
    for sku, item in sku_map_items.items():
        if sku in existed_skus:
            sku_map_existed_items[sku] = item
        else:
            sku_map_new_items[sku] = item

    if len(sku_map_existed_items.keys()) > 0:
        try:
            update_batch_products(channel_id, sku_map_existed_items)
        except Exception as e:
            print(f'... ERROR when UPDATE list product from hubspot: {e}')

    if len(sku_map_new_items.keys()) > 0:
        try:
            for chunk in chunks_dict(sku_map_new_items, 100):
                create_batch_products(channel_id, chunk)
        except Exception as e:
            print(f'... ERROR when CREATE list product from hubspot: {e}')

def fetch_batch_products_by_sku(channel_id: str, skus: list[str]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    api_client = HubSpot(access_token=access_token)

    inputs = [{"id": sku} for sku in skus]

    results = []
    for chunk in chunks_list(inputs, 100):
        try:
            batch_read_input_simple_public_object_id = products.BatchReadInputSimplePublicObjectId(
                id_property="hs_sku",
                inputs=chunk,
                properties=["name", "description", "hs_sku", "price"]
            )

            api_response = api_client.crm.products.batch_api.read(
                batch_read_input_simple_public_object_id=batch_read_input_simple_public_object_id,
                archived=False
            )
            if isinstance(api_response, products.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 450: {api_response.errors[0].message}')
            elif isinstance(api_response, products.BatchResponseSimplePublicObject):
                results.extend(api_response.results)

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 453: {e}')

    return results

def create_batch_products(channel_id: str, sku_map_product: dict[str, dict]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = []
    for sku, product in sku_map_product.items():
        inputs.append({
            "properties": {
                "hs_sku": sku,
                "name": product.get("name", ""),
                "price": product.get("price", ""),
                "platform": product.get("platform", ""),
                # "description": product.get("description", "")
            }
        })

    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_simple_public_object_input_for_create = products.BatchInputSimplePublicObjectInputForCreate(
                inputs=chunk
            )
            api_response = client.crm.products.batch_api.create(
                batch_input_simple_public_object_input_for_create=batch_input_simple_public_object_input_for_create
            )
            if isinstance(api_response, products.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 483: {api_response.errors[0].message}')

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 486: {e}')

    return True

def update_batch_products(channel_id: str, sku_map_product: dict[str, dict]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    api_client = HubSpot(access_token=access_token)

    inputs = []
    for sku, product in sku_map_product.items():
        inputs.append({
            "idProperty": "hs_sku",
            "id": sku,
            "properties": {
                "name": product.get("name", ""),
                "price": product.get("price", ""),
                "platform": product.get("platform", ""),
                # "description": product.get("description", ""),
            }
        })

    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_simple_public_object_batch_input = products.BatchInputSimplePublicObjectBatchInput(
                inputs=chunk
            )
            api_response = api_client.crm.products.batch_api.update(
                batch_input_simple_public_object_batch_input=batch_input_simple_public_object_batch_input
            )
            if isinstance(api_response, products.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 515: {api_response.errors[0].message}')

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 518: {e}')

    return True

# Document: https://developers.hubspot.com/docs/api/crm/deals
# Flow:
## 0. Get all owners
## 1. Fetch batch deals by Sanka's order_id
## 2. Fetch batch line items related to deals in step 1
## 3. Delete all line items in step 2
## 4. Create new deals
## 5. Fetch batch line_items by item's sku (--skip--)
## 6. Update data for existed line_items and create new line_items
## 7. Update data all deals
def push_hubspot_orders(channel_id: str, sanka_id_map_deals: dict[str, dict], mapping_association_custom_fields=None):
    ids = sanka_id_map_deals.keys()
    refresh_hubspot_token(channel_id)
    channel = Channel.objects.get(pk=channel_id)
    task = TransferHistory.objects.filter(workspace=channel.workspace, status='running', type="export_order").first()
    if task:
        task.progress = 50
        task.save()
    
    try:
        check_required_properties(channel.access_token)
    except:
        pass
    
    print("\n>>>>>>>>>>>>>>>> 0. Get all owners")
    headers = {
        "Authorization": f"Bearer {channel.access_token}",
        "Content-Type": "application/json"
    }
    owners = get_all_owners(headers)

    print("\n>>>>>>>>>>>>>>>> 1. Fetch batch deals by Sanka's order_id")
    deal_id_map_deals = {}
    existed_sanka_ids = []
    existed_deal_object_ids = []
    try:
        refresh_hubspot_token(channel_id)
        results = fetch_batch_deals_by_sanka_id(channel_id, ids)
        for result in results:
            existed_sanka_ids.append(result.properties['sanka_id'])
            existed_deal_object_ids.append(result.properties['hs_object_id'])
            deal_id_map_deals[result.properties['hs_object_id']] = sanka_id_map_deals[result.properties['sanka_id']]
    except Exception as e:
        traceback.print_exc()
        print(f'... ERROR when FETCH list deal from hubspot: {e}')
        if task:
            task.error_message = e
            task.save()
        return

    print('deals:', results, deal_id_map_deals)
    print("\n>>>>>>>>>>>>>>>> 2. Fetch batch line items related to deals in step 1")
    existed_line_item_ids = []
    try:
        refresh_hubspot_token(channel_id)
        results = fetch_line_items_in_deals(channel_id, existed_deal_object_ids)
        for result in results:
            existed_line_item_ids.append(result.to_object_id)
    except Exception as e:
        traceback.print_exc()
        print(f'... ERROR when FETCH list line_item from hubspot: {e}')

    print('line_items:', results)

    if len(existed_line_item_ids) > 0:
        print("\n>>>>>>>>>>>>>>>> 3. Delete all line items in step 2")
        try:
            refresh_hubspot_token(channel_id)
            delete_batch_line_items(channel_id, existed_line_item_ids)
        except Exception as e:
            traceback.print_exc()
            print(f'... ERROR when DELETE list line_item from hubspot: {e}')

    sanka_id_map_new_deals = {}
    for sanka_id, deal in sanka_id_map_deals.items():
        if sanka_id not in existed_sanka_ids:
            sanka_id_map_new_deals[sanka_id] = deal

    if len(sanka_id_map_new_deals.keys()) > 0:
        print("\n>>>>>>>>>>>>>>>> 4. Create new deals")
        try:
            refresh_hubspot_token(channel_id)
            results = create_batch_deals(channel_id, sanka_id_map_new_deals)
            for result in results:
                deal_id_map_deals[result.properties['hs_object_id']] = sanka_id_map_deals[result.properties['sanka_id']]
        except Exception as e:
            traceback.print_exc()
            print(f'... ERROR when CREATE list deal from hubspot: {e}')


    try:
        print("\n>>>>>>>>>>>>>>>> 5. Create new line_items")
        refresh_hubspot_token(channel_id)
        create_batch_line_items(channel_id, deal_id_map_deals)
    except Exception as e:
        traceback.print_exc()
        print(f'... ERROR when CREATE list line_items from hubspot: {e}')


    try:
        print("\n>>>>>>>>>>>>>>>> 6. Update data all deals")
        refresh_hubspot_token(channel_id)
        update_batch_deals(channel_id, sanka_id_map_deals, owners, mapping_association_custom_fields=mapping_association_custom_fields)
    except Exception as e:
        traceback.print_exc()
        if task:
            task.error_message = e
            task.save()
        print(f'... ERROR when UPDATE list deal from hubspot: {e}')


    sanka_id_map_companies = {}
    for _, deal in sanka_id_map_deals.items():
        if deal['company']:
            sanka_id_map_companies[deal['company']['sanka_id']] = deal['company']

    sanka_id_map_hs_id = {}
    try:
        print("\n>>>>>>>>>>>>>>>> 7. Update Companies")
        refresh_hubspot_token(channel_id)
        sanka_id_map_hs_id = push_hubspot_companies(channel_id, sanka_id_map_companies)
        if task:
            task.progress = 100
            task.status = 'completed'
            task.save()
    except Exception as e:
        traceback.print_exc()
        print(f'... ERROR when UPDATE list company from hubspot: {e}')
        if task:
            task.status = 'completed'
            task.error_message = e
            task.save()
        return

    if len(sanka_id_map_hs_id.keys()) == 0:
        print("\n<<<<<<<<<<<<<<<< No Companies to update")

    deal_id_map_hs_company_id = {}
    for deal_id, deal in deal_id_map_deals.items():
        if not deal['company']:
            continue

        sanka_id = f"{deal['company']['sanka_id']}"
        hs_company_id = sanka_id_map_hs_id.get(sanka_id)
        if hs_company_id:
            deal_id_map_hs_company_id[deal_id] = hs_company_id

    try:
        print("\n>>>>>>>>>>>>>>>> 8. Update Deal - Company associate")
        refresh_hubspot_token(channel_id)
        create_batch_deal_to_company_associations(channel_id, deal_id_map_hs_company_id)
    except Exception as e:
        traceback.print_exc()
        print(f'... ERROR when UPDATE list Deal - Company associations from hubspot: {e}')

    time.sleep(10)
    # Create contact
    try:
        channel = Channel.objects.get(pk=channel_id)
        access_token = channel.access_token
        api_client = HubSpot(access_token=access_token)
        print("\n>>>>>>>>>>>>>>>> 8b. Update Deal - Contact associate")

        for sanka_id, deal in sanka_id_map_deals.items():
            # Step 2a: Create the company/contact (or check if it exists first)
            if deal['contact'] is not None:
                search_filter = {
                    "filters": [
                        {
                            "propertyName": "sanka_id",
                            "operator": "EQ",
                            "value": sanka_id
                        }
                    ]
                }
                search_body = {
                    "filterGroups": [search_filter],
                    "properties": ["hs_object_id"]  # Retrieve the internal ID if it exists
                }

                search_response = api_client.crm.objects.search_api.do_search('deals', search_body)
                object_id = search_response.results[0].id

                company_contact = deal['contact']
                company_contact_properties = {
                    "sanka_id": company_contact['sanka_id'],
                    "firstname": company_contact['name'],
                    "email": company_contact['email'],
                    "phone": company_contact['phone']
                }
                contact_object = {"properties": company_contact_properties}

                try:
                    # Search or create the contact (or company)
                    contact_search_filter = {
                        "filters": [
                            {"propertyName": "sanka_id", "operator": "EQ", "value": company_contact['sanka_id']}
                        ]
                    }
                    search_body = {
                        "filterGroups": [contact_search_filter],
                        "properties": ["hs_object_id"]  # Retrieve internal ID if exists
                    }
                    contact_search_response = api_client.crm.objects.search_api.do_search('contact', search_body)

                    if contact_search_response.results:
                        contact_id = contact_search_response.results[0].id
                        print(f"Contact with sanka_id {company_contact['sanka_id']} exists with ID: {contact_id}")
                    else:
                        # Create new contact
                        contact_response = api_client.crm.objects.basic_api.create('contact', contact_object)
                        contact_id = contact_response.id
                        print(f"Contact created with ID: {contact_id}")
                        time.sleep(2)

                except ApiException as e:
                    print(f"Exception when creating/searching contact: {e}")

                # Step 3a: Create associations between the subscription and company/contact
                try:
                    # Associate company/contact with the subscription
                    batch_input_public_default_association_multi_post = associations.BatchInputPublicObjectId(
                        inputs=[{
                            "from":{
                                "id": object_id,
                            },
                            "to":{
                                "id": contact_id,
                            }
                        }]
                    )
                    api_client.crm.associations.v4.batch_api.create_default(
                        'deals', 'contact', batch_input_public_default_association_multi_post=batch_input_public_default_association_multi_post
                    )
                    print(f"Associated contact {contact_id} with order {object_id}")

                except ApiException as e:
                    print(f"Exception when associating objects: {e}")

    except Exception as e:
        print(f"Exception when creating HubSpot client: {e}")

def push_hubspot_subscriptions(channel_id: str, sanka_id_map_subs: dict[str, dict]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    api_client = HubSpot(access_token=access_token)

    object_type = f"p_{channel.service_account}" # custom object name
    object_group_name = f"{channel.service_account}_information"

    properties = [
        {'name': 'subscriptions_id', 'label': 'Subscriptions ID', 'type': 'string', 'field_type': 'text'},
        {'name': 'platform_display_name', 'label': 'Platform Display Name', 'type': 'string', 'field_type': 'text'},
        {'name': 'status', 'label': 'Status', 'type': 'string', 'field_type': 'text'},
        {'name': 'subscription_status', 'label': 'Subscription Status', 'type': 'string', 'field_type': 'text'},
        {'name': 'number_item', 'label': 'Number of Items', 'type': 'number', 'field_type': 'number'},
        {'name': 'start_date', 'label': 'Start Date', 'type': 'datetime', 'field_type': 'date'},
        {'name': 'end_date', 'label': 'End Date', 'type': 'datetime', 'field_type': 'date'},
        {'name': 'currency', 'label': 'Currency', 'type': 'string', 'field_type': 'text'},
        {'name': 'frequency', 'label': 'Frequency', 'type': 'string', 'field_type': 'text'},
        {'name': 'frequency_time', 'label': 'Frequency Time', 'type': 'string', 'field_type': 'text'},
        {'name': 'prior_to_next', 'label': 'Prior to Next', 'type': 'number', 'field_type': 'number'},
        {'name': 'prior_to_time', 'label': 'Prior to Time', 'type': 'string', 'field_type': 'text'},
        {'name': 'total_price', 'label': 'Total Price', 'type': 'number', 'field_type': 'number'},
        {'name': 'shipping_cost_tax_status', 'label': 'Shipping Cost Tax Status', 'type': 'string', 'field_type': 'text'},
        {'name': 'tax', 'label': 'Tax', 'type': 'number', 'field_type': 'number'},
        {'name': 'tax_applied_to', 'label': 'Tax Applied To', 'type': 'string', 'field_type': 'text'},
        {'name': 'created_at', 'label': 'Created At', 'type': 'datetime', 'field_type': 'date'}
    ]

    try:
        api_response = api_client.crm.objects.basic_api.get_page(object_type=object_type, limit=10, properties=['id'])
        for obj in api_response.results:
            print(obj)
        existing_properties_response = api_client.crm.properties.core_api.get_all(object_type)
        existing_properties = {prop.name for prop in existing_properties_response.results}

        for prop in properties:
            if prop['name'] in existing_properties:
                print(f"Property '{prop['name']}' already exists. Skipping.")
                continue
            property_create = PropertyCreate(
                name=prop['name'],
                label=prop['label'],
                type=prop['type'],
                field_type=prop['field_type'],
                group_name=object_group_name,  # Group where properties will be added
                options=[]
            )
            api_client.crm.properties.core_api.create(object_type, property_create)

        for sanka_id, sub in sanka_id_map_subs.items():
            properties = {
                'id': str(sub.get('id', '')),
                'subscriptions_id': sanka_id,
                'platform_display_name': sub.get('platform_display_name', ''),
                'status': sub.get('status', ''),
                'subscription_status': sub.get('subscription_status', ''),
                'number_item': sub.get('number_item', 0),
                'start_date': sub.get('start_date', ''),
                'end_date': sub.get('end_date', ''),
                'currency': sub.get('currency', ''),
                'frequency': sub.get('frequency', ''),
                'frequency_time': sub.get('frequency_time', ''),
                'prior_to_next': sub.get('prior_to_next', 0),
                'prior_to_time': sub.get('prior_to_time', ''),
                'total_price': sub.get('total_price', 0),
                'shipping_cost_tax_status': sub.get('shipping_cost_tax_status', ''),
                'tax': sub.get('tax', 0),
                'tax_applied_to': sub.get('tax_applied_to', ''),
                'created_at': sub.get('created_at', '')
            }
            properties_object = {
                "properties": properties
            }
            object_id = properties['id']

            subscriptions_id = properties['id']
            object_type_line_item = "line_items"
            object_type_company = "companies"
            object_type_contact = "contact"

            try:
                # Search for the object by custom property (subscriptions_id)
                search_filter = {
                    "filters": [
                        {
                            "propertyName": "id",
                            "operator": "EQ",
                            "value": subscriptions_id
                        }
                    ]
                }
                search_body = {
                    "filterGroups": [search_filter],
                    "properties": ["hs_object_id"]  # Retrieve the internal ID if it exists
                }

                search_response = api_client.crm.objects.search_api.do_search(object_type, search_body)

                if search_response.results:
                    # Object exists, proceed to update
                    object_id = search_response.results[0].id  # Get HubSpot internal ID

                    # Update the existing object
                    api_response = api_client.crm.objects.basic_api.update(object_type, object_id, properties_object)
                    print(f"Subscription object updated successfully with ID: {api_response.id}")
                else:
                    # Object doesn't exist, so create it
                    api_response = api_client.crm.objects.basic_api.create(object_type, properties_object)
                    time.sleep(7)

                    search_response2 = api_client.crm.objects.search_api.do_search(object_type, search_body)
                    object_id = search_response2.results[0].id  # Get HubSpot internal ID after creating

                    print(f"Subscription object created successfully with subscriptions_id: {subscriptions_id}")

                # Step 1: Create the line items (or retrieve existing ones)
                line_item_ids = []
                print(sub['items'])
                for item in sub['items']:
                    line_item_properties = {
                        "name": item['name'],
                        "description": item['description'],
                        "hs_sku": item['hs_sku'],
                        "price": item['price'],
                        "quantity": item['quantity']
                    }
                    line_item_object = {"properties": line_item_properties}

                    try:
                        # Create the line item (or check if it exists first)
                        line_item_response = api_client.crm.objects.basic_api.create(object_type_line_item, line_item_object)
                        line_item_id = line_item_response.id
                        line_item_ids.append(line_item_id)
                        print(f"Line item created with ID: {line_item_id}")

                    except ApiException as e:
                        print(f"Exception when creating line item: {e}")

                # Step 2b: Create the company/contact (or check if it exists first)
                if sub['company'] is not None:
                    company_contact = sub['company']
                    print(company_contact)
                    company_contact_properties = {
                        "sanka_id": company_contact['sanka_id'],
                        "name": company_contact['name'],
                        "email": company_contact['email'],
                        "phone": company_contact['phone']
                    }
                    contact_object = {"properties": company_contact_properties}

                    try:
                        # Search or create the contact (or company)
                        contact_search_filter = {
                            "filters": [
                                {"propertyName": "sanka_id", "operator": "EQ", "value": company_contact['sanka_id']}
                            ]
                        }
                        search_body = {
                            "filterGroups": [contact_search_filter],
                            "properties": ["hs_object_id"]  # Retrieve internal ID if exists
                        }

                        contact_search_response = api_client.crm.objects.search_api.do_search(object_type_company, search_body)

                        if contact_search_response.results:
                            contact_id = contact_search_response.results[0].id
                            print(f"Company with sanka_id {company_contact['sanka_id']} exists with ID: {contact_id}")
                        else:
                            # Create new contact
                            contact_response = api_client.crm.objects.basic_api.create(object_type_company, contact_object)
                            contact_id = contact_response.id
                            print(f"Company created with ID: {contact_id}")
                            time.sleep(2)

                    except ApiException as e:
                        print(f"Exception when creating/searching company: {e}")
                if sub['contact'] is not None:
                    company_contact = sub['contact']
                    print(company_contact)
                    company_contact_properties = {
                        "sanka_id": company_contact['sanka_id'],
                        "firstname": company_contact['name'],
                        "email": company_contact['email'],
                        "phone": company_contact['phone']
                    }
                    contact_object = {"properties": company_contact_properties}

                    try:
                        # Search or create the contact (or company)
                        contact_search_filter = {
                            "filters": [
                                {"propertyName": "sanka_id", "operator": "EQ", "value": company_contact['sanka_id']}
                            ]
                        }
                        search_body = {
                            "filterGroups": [contact_search_filter],
                            "properties": ["hs_object_id"]  # Retrieve internal ID if exists
                        }
                        contact_search_response = api_client.crm.objects.search_api.do_search(object_type_contact, search_body)

                        if contact_search_response.results:
                            contact_id = contact_search_response.results[0].id
                            print(f"Contact with sanka_id {company_contact['sanka_id']} exists with ID: {contact_id}")
                        else:
                            # Create new contact
                            contact_response = api_client.crm.objects.basic_api.create(object_type_contact, contact_object)
                            contact_id = contact_response.id
                            print(f"Contact created with ID: {contact_id}")
                            time.sleep(2)

                    except ApiException as e:
                        print(f"Exception when creating/searching contact: {e}")

                # Step 3b: Create associations between the subscription and company/contact
                try:
                    # Associate company/contact with the subscription
                    if sub['company'] is not None:
                        batch_input_public_default_association_multi_post = associations.BatchInputPublicObjectId(
                            inputs=[{
                                "from":{
                                    "id": object_id,
                                },
                                "to":{
                                    "id": contact_id,
                                }
                            }]
                        )
                        api_client.crm.associations.v4.batch_api.create_default(
                            object_type, object_type_company, batch_input_public_default_association_multi_post=batch_input_public_default_association_multi_post
                        )
                        print(f"Associated company {contact_id} with subscription {subscriptions_id}")
                    if sub['contact'] is not None:
                        batch_input_public_default_association_multi_post = associations.BatchInputPublicObjectId(
                            inputs=[{
                                "from":{
                                    "id": object_id,
                                },
                                "to":{
                                    "id": contact_id,
                                }
                            }]
                        )
                        api_client.crm.associations.v4.batch_api.create_default(
                            object_type, object_type_contact, batch_input_public_default_association_multi_post=batch_input_public_default_association_multi_post
                        )
                        print(f"Associated contact {contact_id} with subscription {subscriptions_id}")

                except ApiException as e:
                    print(f"Exception when associating objects: {e}")

            except ApiException as e:
                print(f"Exception when checking/creating subscription {subscriptions_id}: {e}")
    except ApiException as e:
        print(f"Exception when retrieving custom objects: {e}")

    return True

def fetch_batch_deals_by_sanka_id(channel_id: str, ids: list[str]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    api_client = HubSpot(access_token=access_token)

    inputs = [{"id": id} for id in ids]

    results = []
    for chunk in chunks_list(inputs, 100):
        try:
            batch_read_input_simple_public_object_id = hs_deals.BatchReadInputSimplePublicObjectId(
                id_property="sanka_id",
                inputs=chunk,
                properties=["sanka_id", "dealname", "hs_object_id"]
            )

            api_response = api_client.crm.deals.batch_api.read(
                batch_read_input_simple_public_object_id=batch_read_input_simple_public_object_id,
                archived=False
            )
            if isinstance(api_response, hs_deals.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 595: {api_response.errors[0].message}')
            elif isinstance(api_response, hs_deals.BatchResponseSimplePublicObject):
                results.extend(api_response.results)

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 600: {e}')

    return results

def create_batch_deals(channel_id: str, id_map_orders: dict[str, dict]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = []
    for id, order in id_map_orders.items():
        try:
            search_filter = {"filters": [{"propertyName": "sanka_id", "operator": "EQ", "value": id}]}
            search_request = hs_deals.PublicObjectSearchRequest(filter_groups=[search_filter], limit=1)
            search_response = client.crm.deals.search_api.do_search(search_request)

            # If there are results, skip adding this deal
            if search_response.results:
                print(f"Deal with sanka_id {id} already exists. Skipping creation.")
                continue
        except Exception as e:
            traceback.print_exc()
            print(f'... ERROR during search for sanka_id {id}: {e}')
            continue

        inputs.append({
            "properties": {
                "sanka_id": id,
                "dealname": order.get("platform_order_id", ""),
                "closedate": order.get("order_at", ""),
                # "platform": order.get("platform", ""),
                # "platform_id": order.get("platform_order_id", ""),
            }
        })

    results = []
    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_simple_public_object_input_for_create = hs_deals.BatchInputSimplePublicObjectInputForCreate(
                inputs=chunk
            )

            api_response = client.crm.deals.batch_api.create(
                batch_input_simple_public_object_input_for_create=batch_input_simple_public_object_input_for_create
            )
            if isinstance(api_response, hs_deals.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 632: {api_response.errors[0].message}')
            elif isinstance(api_response, hs_deals.BatchResponseSimplePublicObject):
                results.extend(api_response.results)

        except Exception as e:
            traceback.print_exc()
            print(f'... ERROR === hubspot.py -- 637: {e}')
            error_message = json.loads(e.body)["message"]
            workspace = channel.workspace
            Notification.objects.create(workspace=workspace, user=workspace.user.first(), message=error_message, type='error')

    return results

def update_batch_deals(channel_id: str, id_map_orders: dict[str, dict], owners: list[dict]=[], force_inputs=[], mapping_association_custom_fields=None):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = []

    # Fetch the dealstages
    dealstages = client.crm.pipelines.pipelines_api.get_all("deals")
    fetched_stages = []
    try:
        for stage in dealstages.results[0].stages:
            fetched_stages.append(stage.label)
    except Exception as e:
        traceback.print_exc()
        print(f'... ERROR during fetching deal stages: {e}')

    dealstage = "closedwon"
    # for fetched_stage in fetched_stages:
    #     if fetched_stage not in ['Appointment Scheduled', 'Qualified To Buy', 'Presentation Scheduled', 'Decision Maker Bought-In', 'Contract Sent', 'Closed Won', 'Closed Lost']:
    #         dealstage = fetched_stage

    if force_inputs:
        inputs = force_inputs
    else:
        for id, order in id_map_orders.items():
            try:
                dealstage_field = order.get('mapping_custom_fields')
                if dealstage_field:
                    dealstage_field = order.get('mapping_custom_fields').get('deal_stage')
                    shopturbo_order = ShopTurboOrders.objects.filter(id=id).first()
                    workspace = order.workspace

                    dealstage_field = ShopTurboOrdersNameCustomField.objects.filter(workspace=workspace, name=dealstage_field).first()
                    if dealstage_field:
                        dealstage_value = ShopTurboOrdersValueCustomField.objects.filter(field_name=dealstage_field, order=shopturbo_order).first()
                        if dealstage_value:
                            if dealstage_value.value and dealstage_value.value != '' and dealstage_value.value != 'None':
                                dealstage = dealstage.value

                    if not dealstage:
                        dealstage = 'closedwon'
            except:
                pass
            properties = {
                "sanka_id": id,
                "dealname": order.get("platform_order_id", ""),
                "closedate": order.get("order_at", ""),
                "amount": order.get("amount", ""),
                "platform": order.get("platform", ""),
                "platform_id": order.get("platform_order_id", ""),
                "dealstage": dealstage
            }
            if order.get('deal_mapping'):
                properties.update(order.get('deal_mapping'))
            inputs.append({
                "idProperty": "sanka_id",
                "id": id,
                "properties": properties
            })

    results = []
    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_simple_public_object_batch_input = hs_deals.BatchInputSimplePublicObjectBatchInput(
                inputs=chunk
            )
            api_response = client.crm.deals.batch_api.update(
                batch_input_simple_public_object_batch_input=batch_input_simple_public_object_batch_input
            )
            if isinstance(api_response, hs_deals.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 669: {api_response.errors[0].message}')
            elif isinstance(api_response, hs_deals.BatchResponseSimplePublicObject):
                results.extend(api_response.results)

        except Exception as e:
            traceback.print_exc()
            print(f'... ERROR === hubspot.py -- 674: {e}')
            error_message = json.loads(e.body)["message"]
            workspace = channel.workspace
            Notification.objects.create(workspace=workspace, user=workspace.user.first(), message=error_message, type='error')

    print('[DEBUG] mapping_association_custom_fields:', mapping_association_custom_fields)
    if mapping_association_custom_fields:
        for result in results:
            sanka_id = result.properties['sanka_id']
            order = ShopTurboOrders.objects.filter(id=sanka_id).first()
            if order:
                order_platform,_ = ShopTurboOrdersPlatforms.objects.get_or_create(
                    channel = channel,
                    order = order,
                    platform_order_id = result.id,
                )
                order_platform.order_display_name = result.properties['dealname']
                order_platform.save()
                workspace = channel.workspace

                for hubspot_association, sanka_association in mapping_association_custom_fields.items():
                    association_label = AssociationLabel.objects.filter(id=sanka_association).first()
                    if not association_label:
                        continue

                    hubspot_association_list = hubspot_association.split('|')
                    hubspot_association_name = hubspot_association_list[0]
                    hubspot_association_id = hubspot_association_list[1]
                    hubspot_association_from_object_type_id = hubspot_association_list[2]
                    hubspot_association_to_object_type_id = hubspot_association_list[3]

                    selected_associations = AssociationLabelObject.get_for_object(order, workspace, association_label).values_list('target_object_id', flat=True)
                    obj_id = order.id
                    page_group_type = association_label.object_source
                    object_targets = association_label.object_target.split(',')
                    if obj_id:
                        try:
                            page_obj = get_page_object(page_group_type, 'en')
                            object = page_obj['base_model'].objects.filter(id=obj_id).first()
                        except:
                            object = CustomObjectPropertyRow.objects.filter(id=obj_id).first()
                        if object:
                            selected_associations = AssociationLabelObject.get_for_object(object,workspace,association_label).values_list('target_object_id')
                            logger.info(f"Log credentials: {hubspot_association_name}, {hubspot_association_id}, {hubspot_association_from_object_type_id}, {hubspot_association_to_object_type_id}")
                            assoc_platform_ids = []
                            for selected_association in selected_associations:
                                if TYPE_OBJECT_COMPANY in object_targets and hubspot_association_to_object_type_id == '0-2':
                                    object_platform = CompanyPlatforms.objects.filter(
                                        channel=channel, company_id=selected_association).first()
                                    assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None

                                if TYPE_OBJECT_CONTACT in object_targets and hubspot_association_to_object_type_id == '0-1':
                                    object_platform = ContactsPlatforms.objects.filter(
                                        channel=channel, contact_id=selected_association).first()
                                    assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None

                                if TYPE_OBJECT_ORDER in object_targets and hubspot_association_to_object_type_id == '0-3':
                                    object_platform = ShopTurboOrdersPlatforms.objects.filter(
                                        channel=channel, order_id=selected_association).first()
                                    assoc_platform_ids.append(object_platform.platform_order_id) if object_platform and object_platform.platform_order_id else None

                                if not assoc_platform_ids:
                                    object_platform = CustomObjectPlatforms.objects.filter(
                                        channel=channel, object_id=selected_association).first()
                                    assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None
                            logger.info(f"Log association platform ids: {assoc_platform_ids}")

                            if len(assoc_platform_ids) > 0:
                                for assoc_platform_id in assoc_platform_ids:
                                    url = f"https://api.hubapi.com/crm/v3/associations/{hubspot_association_from_object_type_id}/{hubspot_association_to_object_type_id}/batch/create"
                                    data = {
                                        "inputs": [
                                            {
                                                "from": {"id": result.id},
                                                "to": {"id": assoc_platform_id},
                                                "type": hubspot_association_id
                                            }
                                        ]
                                    }
                                    try:
                                        headers = {
                                            'Authorization': 'Bearer ' + channel.access_token,
                                            'Content-Type': 'application/json'
                                        }
                                        response = requests.post(
                                            url,
                                            headers=headers,
                                            json=data
                                        )
                                        if response.status_code not in [200, 201, 204]:
                                            logger.info(f"Failed to associate {result.id} -> {assoc_platform_id}: {response.text}")
                                        else:
                                            logger.info(f"Successfully associated {result.id} -> {assoc_platform_id}")
                                    except Exception as e:
                                        logger.error(f"Exception occurred during association: {str(e)}")
    return results

# Relation between Deal and Product
# Document: https://developers.hubspot.com/docs/api/crm/line-items
def create_batch_line_items(channel_id: str, deal_id_map_deals: dict[str, dict]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = []
    for deal_id, deal in deal_id_map_deals.items():
        products = deal['items']
        for product in products:
            properties = {}
            for key, value in product.items():
                properties[key] = value
            inputs.append({
                "associations":[{
                    "types":[{
                        "associationCategory":"HUBSPOT_DEFINED",
                        "associationTypeId": 20 # https://developers.hubspot.com/docs/api/crm/associations
                    }],
                    "to":{
                        "id": deal_id,
                    }
                }],
                "properties": properties
            })

    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_simple_public_object_input_for_create = line_items.BatchInputSimplePublicObjectInputForCreate(
                inputs=chunk
            )

            api_response = client.crm.line_items.batch_api.create(
                batch_input_simple_public_object_input_for_create=batch_input_simple_public_object_input_for_create
            )
            if isinstance(api_response, line_items.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 719: {api_response.errors[0].message}')

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 722: {e}')
            error_message = json.loads(e.body)["message"]
            workspace = channel.workspace
            Notification.objects.create(workspace=workspace, user=workspace.user.first(), message=error_message, type='error')

    return True

def delete_batch_line_items(channel_id: str, ids: list):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = [{"id": id} for id in ids]
    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_simple_public_object_id = line_items.BatchInputSimplePublicObjectId(
                inputs=chunk
            )
            api_response = client.crm.line_items.batch_api.archive(
                batch_input_simple_public_object_id=batch_input_simple_public_object_id
            )
            if isinstance(api_response, line_items.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 760: {api_response.errors[0].message}')

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 763: {e}')
            error_message = json.loads(e.body)["message"]
            workspace = channel.workspace
            Notification.objects.create(workspace=workspace, user=workspace.user.first(), message=error_message, type='error')

    return True

# Document: https://developers.hubspot.com/docs/api/crm/associations#:~:text=Retrieve%20association%20types
def fetch_line_items_in_deals(channel_id: str, deal_object_ids: list):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = [{"id": id} for id in deal_object_ids]

    results = []
    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_public_fetch_associations_batch_request = associations.BatchInputPublicObjectId(
                inputs=chunk
            )
            api_response = client.crm.associations.v4.batch_api.get_page(
                from_object_type="deal",
                to_object_type="line_items",
                batch_input_public_fetch_associations_batch_request=batch_input_public_fetch_associations_batch_request
            )
            if (isinstance(api_response, associations.BatchResponsePublicAssociationWithErrors)
                    or isinstance(api_response, associations.BatchResponsePublicAssociationMultiWithErrors)):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 749: {api_response.errors[0].message}')
            elif len(api_response.results) > 0:
                for _result in api_response.results:
                    results.extend(_result.to)

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 754: {e}')

    return results

def create_batch_deal_to_company_associations(channel_id: str, deal_id_map_company_id: dict[str, str]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = []
    for deal_id, company_id in deal_id_map_company_id.items():
        inputs.append({
            "from":{
                "id": deal_id,
            },
            "to":{
                "id": company_id,
            }
        })

    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_public_default_association_multi_post = associations.BatchInputPublicObjectId(
                inputs=chunk
            )

            api_response = client.crm.associations.v4.batch_api.create_default(
                from_object_type="deal",
                to_object_type="company",
                batch_input_public_default_association_multi_post=batch_input_public_default_association_multi_post
            )
            if isinstance(api_response, associations.BatchResponsePublicAssociation):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 851: {api_response.errors[0].message}')

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 884: {e}')
            error_message = json.loads(e.body)["message"]
            workspace = channel.workspace
            Notification.objects.create(workspace=workspace, user=workspace.user.first(), message=error_message, type='error')

    return True

# Document: https://developers.hubspot.com/docs/api/crm/companies
def push_hubspot_companies(channel_id: str, id_map_companies: dict[str, dict]):
    ids = list(id_map_companies.keys())

    existed_ids = []
    try:
        results = fetch_batch_companies_by_sanka_id(channel_id, ids)
        for result in results:
            existed_ids.append(str(result.properties['sanka_id']))
    except Exception as e:
        print(f'... ERROR when FETCH list company from hubspot: {e}')
        return

    id_map_existed_companies = {}
    id_map_new_companies = {}
    for id, company in id_map_companies.items():
        if str(id) in existed_ids:
            id_map_existed_companies[id] = company
        else:
            id_map_new_companies[id] = company

    sanka_id_map_hs_id = {}
    if len(id_map_existed_companies.keys()) > 0:
        try:
            results = update_batch_companies(channel_id, id_map_existed_companies)
            for result in results:
                sanka_id_map_hs_id[result.properties['sanka_id']] = result.properties['hs_object_id']
        except Exception as e:
            print(f'... ERROR when UPDATE list company from hubspot: {e}')

    if len(id_map_new_companies.keys()) > 0:
        try:
            results = create_batch_companies(channel_id, id_map_new_companies)
            for result in results:
                sanka_id_map_hs_id[result.properties['sanka_id']] = result.properties['hs_object_id']
        except Exception as e:
            print(f'... ERROR when CREATE list company from hubspot: {e}')
            error_message = json.loads(e.body)["message"]
            try:
                channel = Channel.objects.get(id=channel_id)
                workspace = channel.workspace
                Notification.objects.create(workspace=workspace, user=workspace.user.first(
                ), message=error_message, type='error')
            except:
                pass

    return sanka_id_map_hs_id

def fetch_batch_companies_by_sanka_id(channel_id: str, ids: list[str]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    api_client = HubSpot(access_token=access_token)

    inputs = [{"id": id} for id in ids]

    results = []
    for chunk in chunks_list(inputs, 100):
        try:
            batch_read_input_simple_public_object_id = companies.BatchReadInputSimplePublicObjectId(
                id_property="sanka_id",
                inputs=chunk,
                properties=["sanka_id"]
            )

            api_response = api_client.crm.companies.batch_api.read(
                batch_read_input_simple_public_object_id=batch_read_input_simple_public_object_id,
                archived=False
            )
            if isinstance(api_response, companies.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 839: {api_response.errors[0].message}')
            elif isinstance(api_response, companies.BatchResponseSimplePublicObject):
                results.extend(api_response.results)

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 844: {e}')

    return results

def create_batch_companies(channel_id: str, id_map_companies: dict[str, dict]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = []
    for id, company in id_map_companies.items():
        try:
            search_filter = {"filters": [{"propertyName": "sanka_id", "operator": "EQ", "value": id}]}
            search_request = companies.PublicObjectSearchRequest(filter_groups=[search_filter], limit=1)
            search_response = client.crm.companies.search_api.do_search(search_request)

            # If there are results, skip adding this deal
            if search_response.results:
                print(f"Company with sanka_id {id} already exists. Skipping creation.")
                continue
        except Exception as e:
            traceback.print_exc()
            print(f'... ERROR during search for company sanka_id {id}: {e}')
            continue

        _input = { "properties": {} }
        for k, v in company.items():
            if v:
                _input["properties"][k] = v
        if company.get('company_mapping'):
            _input["properties"].update(company.get('company_mapping'))
        inputs.append(_input)

    results = []
    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_simple_public_object_input_for_create = companies.BatchInputSimplePublicObjectInputForCreate(
                inputs=chunk
            )

            api_response = client.crm.companies.batch_api.create(
                batch_input_simple_public_object_input_for_create=batch_input_simple_public_object_input_for_create
            )
            if isinstance(api_response, companies.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 878: {api_response.errors[0].message}')
            elif isinstance(api_response, companies.BatchResponseSimplePublicObject):
                results.extend(api_response.results)

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 883: {e}')
            error_message = json.loads(e.body)["message"]
            workspace = channel.workspace
            Notification.objects.create(workspace=workspace, user=workspace.user.first(), message=error_message, type='error')

    return results

def update_batch_companies(channel_id: str, id_map_companies: dict[str, dict]):
    channel = Channel.objects.get(pk=channel_id)
    access_token = channel.access_token
    client = HubSpot(access_token=access_token)

    inputs = []
    for id, company in id_map_companies.items():
        _input = {
            "idProperty": "sanka_id",
            "id": id,
            "properties": {}
        }
        for k, v in company.items():
            if v:
                _input["properties"][k] = v
        if company.get('company_mapping'):
            _input["properties"].update(company.get('company_mapping'))

        inputs.append(_input)

    results = []
    for chunk in chunks_list(inputs, 100):
        try:
            batch_input_simple_public_object_batch_input = companies.BatchInputSimplePublicObjectBatchInput(
                inputs=chunk
            )
            api_response = client.crm.companies.batch_api.update(
                batch_input_simple_public_object_batch_input=batch_input_simple_public_object_batch_input
            )
            if isinstance(api_response, companies.BatchResponseSimplePublicObjectWithErrors):
                if api_response.errors[0].errors:
                    print(f'... ERROR === hubspot.py -- 918: {api_response.errors[0].message}')
            elif isinstance(api_response, companies.BatchResponseSimplePublicObject):
                results.extend(api_response.results)

        except Exception as e:
            print(f'... ERROR === hubspot.py -- 923: {e}')

    return results

def sync_update_contacts(channel_id, contact_id, lang, user_id, mapping_custom_fields=None):
    user = User.objects.get(id=user_id)

    def notify_user(message_en, message_ja, type="error"):
        workspace = get_workspace(user)
        message = message_ja if lang == 'ja' else message_en
        Notification.objects.create(workspace=workspace, user=user, message=message, type=type)

    try:
        print('[DEBUG]: sync_update_contacts')
        print(contact_id)
        workspace = get_workspace(user)

        if isinstance(contact_id, str):
            contact_id = ast.literal_eval(contact_id)

        channel = Channel.objects.get(id=channel_id)
        contacts = Contact.objects.filter(id__in=contact_id)
        api = channel.access_token
        headers = {
            'Authorization': 'Bearer ' + api,
            'Content-Type': 'application/json'
        }

        task = TransferHistory.objects.filter(
            workspace=workspace,
            status='running',
            type='export_contact'
        ).order_by('-created_at').first()

        try:
            check_required_properties(channel.access_token)
        except:
            pass

        vid_list = []
        
        # Initialize error logging
        error_rows = []
        error_count = 0
        
        owners = get_all_owners(headers)

        counter = 0
        token_url = "https://api.hubapi.com/oauth/v1/token"
        token_data = {
            "grant_type": "refresh_token",
            "client_id": settings.HUBSPOT_CLIENT_ID,
            "client_secret": settings.HUBSPOT_CLIENT_SECRET,
            "refresh_token": channel.refresh_token,
        }
        contact_properties_url = (
            "https://api.hubapi.com/properties/v1/contacts/properties"
        )
        contact_properties_response = requests.get(
            contact_properties_url, headers=headers
        )
        contact_property_name_to_label_map = {}
        for contact_property in contact_properties_response.json():
            contact_property_name_to_label_map[contact_property.get('name')] = contact_property.get('label')

        if task:
            task.total_number = contacts.count()
            task.save()

        for contact in contacts:
            counter += 1
            if counter % 100 == 0 or counter == 1:
                response = requests.post(token_url, data=token_data)
                token_info = response.json()
                channel.access_token = token_info.get("access_token")
                channel.save()

                access_token = (
                    token_info.get("access_token")
                    if token_info.get("access_token")
                    else channel.access_token
                )
                api = access_token
                headers = {
                    'Authorization': 'Bearer ' + api,
                    'Content-Type': 'application/json'
                }
                logger.info(f'Processing contact {counter} of {contacts.count()} - Access token refreshed')
            
            task = TransferHistory.objects.filter(
                workspace=workspace,
                status='running',
                type='export_contact'
            ).order_by('-created_at').first()
            if task:
                if task.status == 'canceled':
                    return False
                progress = round((counter / contacts.count()) * 100, 2)
                task.progress = progress
                task.success_number = counter
                task.save()

            try:
                contactplatform, _ = ContactsPlatforms.objects.get_or_create(contact=contact, channel=channel)
                website = contact.links.first().platform_url if contact.links.exists() else ""

                properties = [
                    {"property": "firstname", "value": contact.name},
                    {"property": "lastname", "value": contact.last_name},
                    {"property": "phone", "value": contact.phone_number},
                    {"property": "website", "value": website}
                ]

                if contact.email:
                    properties.append({"property": "email", "value": contact.email})

                try:
                    if mapping_custom_fields:
                        for key, val in mapping_custom_fields.items():
                            if key in ['firstname', 'lastname', 'phone', 'website', 'email', 'hubspot_owner_id', 'total_revenue', 'lastmodifieddate']:
                                pass
                            else:
                                try:
                                    custom_field = ContactsNameCustomField.objects.filter(workspace=workspace, name=val).first()
                                    custom_value = None
                                    if custom_field:
                                        custom_value,_ = ContactsValueCustomField.objects.get_or_create(field_name=custom_field, contact=contact)
                                        custom_value = custom_value.value
                                        
                                        if not custom_value:
                                            custom_value = ''
                                        
                                        if key == 'hubspot_owner_id':
                                            if custom_value:
                                                for owner in owners:
                                                    if owner['email'] == custom_value:
                                                        custom_value = owner['id']
                                                        break
                                                
                                                if custom_value: 
                                                    custom_value = custom_value
                                                else:
                                                    custom_value = ''


                                        properties.append({"property": key, "value": custom_value})
                                    else:
                                        try:
                                            def_value = getattr(contact, val, None)
                                            if def_value:
                                                properties.append({"property": key, "value": def_value})
                                        except:
                                            pass
                                except:
                                    pass
                except:
                    pass
                print('logprop', properties)

                data = json.dumps({"properties": properties})

                url = f"https://api.hubapi.com/contacts/v1/contact/vid/{contactplatform.platform_id}/profile" if contactplatform.platform_id \
                            else "https://api.hubapi.com/contacts/v1/contact/"

                r = requests.post(url=url, headers=headers, data=data)

                if not r.text and not contactplatform.platform_id:
                    print('No Response from HUBAPI for Contact:', contact.id)
                    notify_user(
                        'There was an error while communicating with the Hubspot API',
                        'Hubspot APIとの通信中にエラーが発生しました'
                    )
                elif contactplatform.platform_id and r.status_code == 204:
                    vid_list.append(contactplatform.platform_id)
                else:
                    results = r.json()
                    logger.info(f'Contact ID: {contact.contact_id:04d}. Result: {results}')
                    if results.get('status') == 'error':
                        if results.get('category') == 'OBJECT_NOT_FOUND':
                            contactplatform.delete()
                            sync_update_contacts(channel.id, f"['{contact.id}']", lang, user_id)
                            continue
                        else:
                            try:
                                invalid_properties = []
                                invalid_properties_label = []
                                try:
                                    if results.get('validationResults'):
                                        for result in results.get('validationResults'):
                                            if result.get('name'):
                                                invalid_properties.append(result.get('name'))
                                                invalid_properties_label.append(contact_property_name_to_label_map.get(result.get('name'), result.get('name')))
                                except:
                                    pass

                                if len(invalid_properties) > 0:
                                    error_message = f"Custom property values were not valid [{', '.join(invalid_properties_label)}]"
                                    error_message_ja = f"カスタムプロパティの値が無効でした [{', '.join(invalid_properties_label)}]"
                                else:
                                    if results.get('message') == 'Property values were not valid':
                                        error_message = 'One of the custom property values were not valid. Exporting with default properties'
                                        error_message_ja = 'カスタムプロパティの値の1つが無効でした。デフォルトプロパティでエクスポートしています'
                                        properties = [
                                            {"property": "firstname", "value": contact.name},
                                            {"property": "lastname", "value": contact.last_name},
                                            {"property": "phone", "value": contact.phone_number},
                                            {"property": "website", "value": website}
                                        ]
                                        if contact.email:
                                            properties.append({"property": "email", "value": contact.email})
                                    else:
                                        error_message = results.get('message')
                                        error_message_ja = results.get('message')

                                # Log warning to error file
                                error_count += 1
                                warning_msg = error_message_ja if lang == 'ja' else error_message
                                error_rows.append([error_count, f"{contact.contact_id:04d}", f"Warning: {warning_msg}"])

                                notify_user(
                                    f'Contact ID: {contact.contact_id:04d}. Warning: {error_message}',
                                    f'連絡先ID: {contact.contact_id:04d}。警告: {error_message_ja}',
                                    type='warning'
                                )
                                continue

                                # properties = [p for p in properties if p["property"] not in invalid_properties]
                                # print('properties:', properties)

                                # data = json.dumps({"properties": properties})
                                # r = requests.post(url=url, headers=headers, data=data)
                            except:
                                continue

                            if r.text:
                                results = r.json()

                            if results.get('status') == 'error' and not r.status_code == 204:
                                # Log error to error file
                                error_count += 1
                                error_msg = results.get('message', 'Unknown error')
                                error_rows.append([error_count, f"{contact.contact_id:04d}", f"Error: {error_msg}"])
                                
                                notify_user(
                                    f'Contact ID: {contact.contact_id:04d}. There was an error while communicating with the HubSpot API. Log: {results.get("message")}',
                                    f'連絡先ID: {contact.contact_id:04d} - HubSpot APIとの通信中にエラーが発生しました。Log: {results.get("message")}'
                                )
                                if not contactplatform.platform_id:
                                    contactplatform.delete()
                                continue
                            else:
                                pass
                    
                    if r.status_code == 204:
                        vid_list.append(contactplatform.platform_id)
                    else:
                        contactplatform.platform_id = results.get('vid')
                        contactplatform.channel = channel
                        contactplatform.save()

                    vid_list.append(results.get('vid'))

                notify_user(
                    'Contact successfully exported to HubSpot.',
                    'HubSpotへの連絡先のエクスポートが成功しました。',
                    type="success"
                )

            except Exception as e:
                # Log general error to error file
                error_count += 1
                error_msg = str(e)
                error_rows.append([error_count, f"{contact.contact_id:04d}", f"Exception: {error_msg}"])
                
                print('[Error in sync_update_contacts]:', e)
                notify_user(
                    'A server error occurred. Please try again or contact support.',
                    'サーバーエラーが発生しました。再度お試しになるか、サポートまでお問い合わせください。'
                )

        # Create error file if there are errors
        if error_rows and task:
            error_csv_buffer = StringIO()
            error_writer = csv.writer(error_csv_buffer, quoting=csv.QUOTE_MINIMAL)
            
            # Write header
            if lang == 'ja':
                error_writer.writerow(['No', '連絡先ID', 'エラーメッセージ'])
            else:
                error_writer.writerow(['No', 'Contact ID', 'Error Message'])
            
            # Write error rows
            for row in error_rows:
                error_writer.writerow(row)
            
            # Save error file
            error_data = error_csv_buffer.getvalue().encode('utf-8-sig')
            content_file = DjangoContentFile(error_data)
            task.error_file.save(f'contact_export_errors_{uuid.uuid4()}.csv', content_file)
            task.save()

        if channel.hubspot_list and len(vid_list) > 0:
            try:
                # 1. Create a new static list
                if channel.hubspot_list == '0':
                    list_name = f"{channel.name} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    list_payload = {
                        "name": list_name,
                        "dynamic": False,
                        "processingType": "MANUAL",
                        "objectTypeId": "0-1"
                    }

                    list_create_url = "https://api.hubapi.com/crm/v3/lists"
                    list_response = requests.post(url=list_create_url, headers=headers, data=json.dumps(list_payload))

                    list_result = list_response.json()
                    new_list_id = list_result.get('list').get('listId')

                    if not new_list_id:
                        raise Exception("Failed to create contact list on HubSpot")

                    add_url = f"https://api.hubapi.com/crm/v3/lists/{new_list_id}/memberships/add"
                else:
                    new_list_id = int(channel.hubspot_list)
                    add_url = f"https://api.hubapi.com/crm/v3/lists/{new_list_id}/memberships/add"

                # 2. Add contacts to the created list
                add_payload = json.dumps(vid_list)
                add_response = requests.put(url=add_url, headers=headers, data=add_payload)
                add_result = add_response.json()
                print('log:', add_result)

                if add_result.get('recordsIdsAdded'):
                    notify_user(
                        'Contacts successfully added to a new HubSpot list.',
                        '連絡先が新しいHubSpotリストに正常に追加されました。',
                        type="success"
                    )
                else:
                    notify_user(
                        'Contacts were not added to the HubSpot list.',
                        'HubSpotリストに連絡先を追加できませんでした。',
                        type="warning"
                    )
            except Exception as e:
                print('[Error creating or updating HubSpot list]:', e)
                notify_user(
                    'Failed to create or update HubSpot list.',
                    'HubSpotリストの作成または更新に失敗しました。'
                )

        return True
    except Exception as e:
        print('[Error in sync_update_contacts]:', e)
        notify_user(
            'A server error occurred. Please try again or contact support.',
            'サーバーエラーが発生しました。再度お試しになるか、サポートまでお問い合わせください。'
        )
        traceback.print_exc()
        return False

def sync_update_companies(channel_id, company_id, lang, user_id, mapping_custom_fields=None):
    user = User.objects.get(id=user_id)
    
    def notify_user(message_en, message_ja, type="error"):
        workspace = get_workspace(user)
        message = message_ja if lang == 'ja' else message_en
        Notification.objects.create(workspace=workspace, user=user, message=message, type=type)

    try:
        print('[DEBUG]: sync_update_companies')
        print(company_id)
        print(mapping_custom_fields)
        workspace = get_workspace(user)

        if isinstance(company_id, str):
            company_id = ast.literal_eval(company_id)

        channel = Channel.objects.get(id=channel_id)
        companies = Company.objects.filter(id__in=company_id)
        api = channel.access_token
        headers = {
            'Authorization': 'Bearer ' + api,
            'Content-Type': 'application/json'
        }

        task = TransferHistory.objects.filter(
            workspace=workspace,
            status='running',
            type='export_company'
        ).order_by('-created_at').first()

        try:
            check_required_properties(channel.access_token)
        except:
            pass
        
        owners = get_all_owners(headers)

        vid_list = []

        # Initialize error logging
        error_rows = []
        error_count = 0

        counter = 0
        token_url = "https://api.hubapi.com/oauth/v1/token"
        token_data = {
            "grant_type": "refresh_token",
            "client_id": settings.HUBSPOT_CLIENT_ID,
            "client_secret": settings.HUBSPOT_CLIENT_SECRET,
            "refresh_token": channel.refresh_token,
        }
        company_properties_url = (
            "https://api.hubapi.com/properties/v1/companies/properties"
        )
        company_properties_response = requests.get(
            company_properties_url, headers=headers
        )
        company_property_name_to_label_map = {}
        for company_property in company_properties_response.json():
            company_property_name_to_label_map[company_property.get('name')] = company_property.get('label')

        if task:
            task.total_number = companies.count()
            task.save()

        for company in companies:
            counter += 1
            if counter % 100 == 0 or counter == 1:
                response = requests.post(token_url, data=token_data)
                token_info = response.json()
                channel.access_token = token_info.get("access_token")
                channel.save()

                access_token = (
                    token_info.get("access_token")
                    if token_info.get("access_token")
                    else channel.access_token
                )
                api = access_token
                headers = {
                    'Authorization': 'Bearer ' + api,
                    'Content-Type': 'application/json'
                }
                logger.info(f'Processing company {counter} of {companies.count()} - Access token refreshed')

            task = TransferHistory.objects.filter(
                workspace=workspace,
                status='running',
                type='export_company'
            ).order_by('-created_at').first()
            if task:
                if task.status == 'canceled':
                    return False
                progress = round((counter / companies.count()) * 100, 2)
                task.progress = progress
                task.success_number = counter
                task.save()
                
            try:
                companyplatform, _ = CompanyPlatforms.objects.get_or_create(company=company, channel=channel)

                properties = [
                    {"name": "name", "value": company.name},
                    {"name": "phone", "value": company.phone_number or ""},
                    {"name": "email", "value": company.email or ""},
                    {"name": "address", "value": company.address or ""},
                ]

                try:
                    if mapping_custom_fields:
                        for key, val in mapping_custom_fields.items():
                            if key in ['name', 'email', 'phone', 'address', 'hubspot_owner_id', 'total_revenue', 'lastmodifieddate', 'annualrevenue']:
                                pass
                            else:
                                try:
                                    custom_field = CompanyNameCustomField.objects.filter(workspace=workspace, name=val).first()
                                    custom_value = None
                                    if custom_field:
                                        custom_value,_ = CompanyValueCustomField.objects.get_or_create(field_name=custom_field, company=company)
                                        custom_value = custom_value.value

                                        if not custom_value:
                                            custom_value = ''
                                            
                                        if key == 'hubspot_owner_id':
                                            if custom_value:
                                                for owner in owners:
                                                    if owner['email'] == custom_value:
                                                        custom_value = owner['id']
                                                        break
                                                
                                                if custom_value: 
                                                    custom_value = custom_value
                                                else:
                                                    custom_value = ''
                                                
                                        properties.append({"name": key, "value": custom_value})
                                    else:
                                        try:
                                            def_value = getattr(company, val, None)
                                            if def_value:
                                                properties.append({"name": key, "value": def_value})
                                        except:
                                            pass
                                except:
                                    pass
                except:
                    pass

                data = json.dumps({"properties": properties})

                url = f"https://api.hubapi.com/companies/v2/companies/{companyplatform.platform_id}" \
                    if companyplatform.platform_id else \
                    "https://api.hubapi.com/companies/v2/companies/"

                if companyplatform.platform_id:
                    r = requests.put(url=url, headers=headers, data=data)
                else:
                    r = requests.post(url=url, headers=headers, data=data)

                if not r.text:
                    print('No Response from HubSpot API for Company:', company.id)
                    notify_user(
                        'There was an error while communicating with the HubSpot API',
                        'HubSpot APIとの通信中にエラーが発生しました'
                    )
                else:
                    results = r.json()
                    logger.info(f'Company ID: {company.company_id:04d}. Result: {results}')
                    if results.get('status') == 'error':
                        if results.get('category') == 'OBJECT_NOT_FOUND':
                            companyplatform.delete()
                            sync_update_companies(channel.id, f"['{company.id}']", lang, user_id, mapping_custom_fields)
                            continue
                        else:
                            try:
                                invalid_properties = []
                                invalid_properties_label = []
                                try:
                                    if results.get('validationResults'):
                                        for result in results.get('validationResults'):
                                            if result.get('name'):
                                                invalid_properties.append(result.get('name'))
                                                invalid_properties_label.append(company_property_name_to_label_map.get(result.get('name'), result.get('name')))
                                except:
                                    pass

                                if len(invalid_properties) > 0:
                                    error_message = f"Custom property values were not valid [{', '.join(invalid_properties_label)}]"
                                    error_message_ja = f"カスタムプロパティの値が無効でした [{', '.join(invalid_properties_label)}]"
                                else:
                                    if results.get('message') == 'Property values were not valid':
                                        error_message = 'One of the custom property values were not valid. Exporting with default properties'
                                        error_message_ja = 'カスタムプロパティの値の1つが無効でした。デフォルトプロパティでエクスポートしています'
                                        properties = [
                                            {"name": "name", "value": company.name},
                                            {"name": "phone", "value": company.phone_number or ""},
                                            {"name": "email", "value": company.email or ""},
                                            {"name": "address", "value": company.address or ""},
                                        ]
                                    else:
                                        error_message = results.get('message')
                                        error_message_ja = results.get('message')

                                # Log warning to error file
                                error_count += 1
                                warning_msg = error_message_ja if lang == 'ja' else error_message
                                error_rows.append([error_count, f"{company.company_id:04d}", f"Warning: {warning_msg}"])

                                notify_user(
                                    f'Company ID: {company.company_id:04d}. Warning: {error_message}',
                                    f'会社ID: {company.company_id:04d}。警告: {error_message_ja}',
                                    type='warning'
                                )
                                continue

                                # properties = [p for p in properties if p["name"] not in invalid_properties]
                                # print('properties:', properties)

                                # data = json.dumps({"properties": properties})
                                # if companyplatform.platform_id:
                                #     r = requests.put(url=url, headers=headers, data=data)
                                # else:
                                #     r = requests.post(url=url, headers=headers, data=data)
                            except:
                                continue

                            if r.text:
                                results = r.json()

                            if results.get('status') == 'error':
                                # Log error to error file
                                error_count += 1
                                error_msg = results.get('message', 'Unknown error')
                                error_rows.append([error_count, f"{company.company_id:04d}", f"Error: {error_msg}"])
                                
                                notify_user(
                                    f'Company ID: {company.company_id:04d}. There was an error while communicating with the HubSpot API. Log: {results.get("message")}',
                                    f'会社ID: {company.company_id:04d} - HubSpot APIとの通信中にエラーが発生しました。Log: {results.get("message")}'
                                )
                                if not companyplatform.platform_id:
                                    companyplatform.delete()
                                continue
                            else:
                                pass
                    companyplatform.platform_id = results.get('companyId')
                    companyplatform.channel = channel
                    companyplatform.save()

                    vid_list.append(results.get('companyId'))

                notify_user(
                    'Company successfully exported to HubSpot.',
                    'HubSpotへの会社情報のエクスポートが成功しました。',
                    type="success"
                )
            except Exception as e:
                # Log general error to error file
                error_count += 1
                error_msg = str(e)
                error_rows.append([error_count, f"{company.company_id:04d}", f"Exception: {error_msg}"])
                
                traceback.print_exc()
                print('[Error in sync_update_companies]:', e)
                notify_user(
                    'A server error occurred. Please try again or contact support.',
                    'サーバーエラーが発生しました。再度お試しになるか、サポートまでお問い合わせください。'
                )

        # Create error file if there are errors
        if error_rows and task:
            error_csv_buffer = StringIO()
            error_writer = csv.writer(error_csv_buffer, quoting=csv.QUOTE_MINIMAL)
            
            # Write header
            if lang == 'ja':
                error_writer.writerow(['No', '会社ID', 'エラーメッセージ'])
            else:
                error_writer.writerow(['No', 'Company ID', 'Error Message'])
            
            # Write error rows
            for row in error_rows:
                error_writer.writerow(row)
            
            # Save error file
            error_data = error_csv_buffer.getvalue().encode('utf-8-sig')
            content_file = DjangoContentFile(error_data)
            task.error_file.save(f'company_export_errors_{uuid.uuid4()}.csv', content_file)
            task.save()

        if channel.hubspot_list and len(vid_list) > 0:
            try:
                # 1. Create a new static list
                response = None
                headers = {
                    'Authorization': f'Bearer {channel.access_token}',
                    'Content-Type': 'application/json'
                }
                if channel.hubspot_list == '0':
                    list_name = f"{channel.name} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                    list_payload = {
                        "name": list_name,
                        "dynamic": False,
                        "processingType": "MANUAL",
                        "objectTypeId": "0-2"
                    }

                    list_create_url = "https://api.hubapi.com/crm/v3/lists"
                    response = requests.post(url=list_create_url, headers=headers, data=json.dumps(list_payload))

                if response:
                    if response.status_code == 200:
                        list_result = response.json()
                        new_list_id = list_result.get('list').get('listId')
                else:
                    new_list_id = channel.hubspot_list
                print(f"Successfully created company list with ID: {new_list_id}")

                # 2. Add companies to the created list
                add_url = f"https://api.hubapi.com/crm/v3/lists/{new_list_id}/memberships/add"
                add_payload = json.dumps(vid_list)
                add_response = requests.put(url=add_url, headers=headers, data=add_payload)
                add_result = add_response.json()

                if add_result.get('recordsIdsAdded'):
                    notify_user(
                        'Contacts successfully added to a new HubSpot list.',
                        '連絡先が新しいHubSpotリストに正常に追加されました。',
                        type="success"
                    )
                else:
                    notify_user(
                        'Contacts were not added to the HubSpot list.',
                        'HubSpotリストに連絡先を追加できませんでした。',
                        type="warning"
                    )
            except Exception as e:
                traceback.print_exc()
                print('[Error creating or updating HubSpot list]:', e)
                notify_user(
                    'Failed to create or update HubSpot list.',
                    'HubSpotリストの作成または更新に失敗しました。'
                )
        return True
    except Exception as e:
        print('[Error in sync_update_companies]:', e)
        notify_user(
            'A server error occurred. Please try again or contact support.',
            'サーバーエラーが発生しました。再度お試しになるか、サポートまでお問い合わせください。'
        )
        traceback.print_exc()
        return False


def sync_update_contacts_as_companies(channel_id, contact_id, lang, user_id):
    user = User.objects.get(id=user_id)

    def notify_user(message_en, message_ja, type="error"):
        workspace = get_workspace(user)
        message = message_ja if lang == 'ja' else message_en
        Notification.objects.create(workspace=workspace, user=user, message=message, type=type)

    try:
        if isinstance(contact_id, str):
            contact_id = ast.literal_eval(contact_id)

        channel = Channel.objects.get(id=channel_id)
        contacts = Contact.objects.filter(id__in=contact_id)
        api = channel.access_token
        headers =  {'Authorization' : 'Bearer ' + api}
        headers['Content-Type']= 'application/json'

        try:
            check_required_properties(channel.access_token)
        except:
            pass

        vid_list = []

        for contact in contacts:
            try:
                contactplatform,_ = ContactsPlatforms.objects.get_or_create(contact=contact,channel=channel)
                website = contact.links.all()
                if website:
                    website = contact.links.all().first().platform_url
                else:
                    website = ""

                properties = [
                    {
                        "name": "name",
                        "value": contact.name
                    },
                    {
                        "name": "phone",
                        "value": contact.phone_number
                    },
                    {
                        "name": "website",
                        "value": website
                    }
                ]

                if contact.email:
                    properties.append(
                        {
                            "name": "email",
                            "value": contact.email
                        }
                    )

                data=json.dumps({
                    "properties": properties
                })
                url = "https://api.hubapi.com/companies/v2/companies/"
                r = requests.post(data=data, url=url, headers=headers)
                if not r.text:
                    print('Companies Integration Updated:', contactplatform.platform_id)
                    print('######')
                else:
                    try:
                        results = r.json()
                        contactplatform.platform_id = results.get('companyId')
                        contactplatform.channel = channel
                        contactplatform.save()

                        vid_list.append(results.get('companyId'))
                        if lang == 'en':
                            Notification.objects.create(workspace=channel.workspace, user=user, message='New Company Exported Successfully', type="success")
                        else:
                            Notification.objects.create(workspace=channel.workspace, user=user, message='新しい連絡先が正常にエクスポートされました', type="success")
                    except:
                        if r.status_code == 404:
                            contactplatform.platform_id = None
                            contactplatform.channel = channel
                            contactplatform.save()
                            if lang == 'en':
                                Notification.objects.create(workspace=channel.workspace, user=user, message=f"Company with id: {contact.platform_id} doesn't exist Creating a new one", type="error")
                            else:
                                Notification.objects.create(workspace=channel.workspace, user=user, message=f"IDとの連絡先：{contact.platform_id}は存在しません新しいものを作成", type="error")
                            sync_update_contacts_as_companies(channel.id,f"['{contact.id}']",lang,user_id)
                        else:
                            print('Some Error happening ',r.status_code)
                            print(r.text)

            except Exception as e:
                Notification.objects.create(workspace=channel.workspace, user=user, message=str(e), type="error")

        if channel.hubspot_list and len(vid_list) > 0:
            try:
                # 1. Create a new static list
                response = None
                headers = {
                    'Authorization': f'Bearer {channel.access_token}',
                    'Content-Type': 'application/json'
                }
                if channel.hubspot_list == '0':
                    list_name = f"{channel.name} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                    list_payload = {
                        "name": list_name,
                        "dynamic": False,
                        "processingType": "MANUAL",
                        "objectTypeId": "0-2"
                    }

                    list_create_url = "https://api.hubapi.com/crm/v3/lists"
                    response = requests.post(url=list_create_url, headers=headers, data=json.dumps(list_payload))

                if response:
                    if response.status_code == 200:
                        list_result = response.json()
                        new_list_id = list_result.get('list').get('listId')
                else:
                    new_list_id = channel.hubspot_list
                print(f"Successfully created company list with ID: {new_list_id}")

                # 2. Add companies to the created list
                add_url = f"https://api.hubapi.com/crm/v3/lists/{new_list_id}/memberships/add"
                add_payload = json.dumps(vid_list)
                add_response = requests.put(url=add_url, headers=headers, data=add_payload)
                add_result = add_response.json()

                if add_result.get('recordsIdsAdded'):
                    notify_user(
                        'Contacts successfully added to a new HubSpot list.',
                        '連絡先が新しいHubSpotリストに正常に追加されました。',
                        type="success"
                    )
                else:
                    notify_user(
                        'Contacts were not added to the HubSpot list.',
                        'HubSpotリストに連絡先を追加できませんでした。',
                        type="warning"
                    )
            except Exception as e:
                traceback.print_exc()
                print('[Error creating or updating HubSpot list]:', e)
                notify_user(
                    'Failed to create or update HubSpot list.',
                    'HubSpotリストの作成または更新に失敗しました。'
                )
    except Exception as e:
        print('Error in sync_update_contacts_as_companies:', e)
        traceback.print_exc()
        return False

def export_hubspot_custom_object(channel_id: str, custom_object_id:str, platform_object_type_id:str,mapping_custom_fields={},hubspot_group_name='',lang='ja',user_id='', custom_object_ids=[], mapping_custom_fields_association={}):
    channel = Channel.objects.get(pk=channel_id)
    workspace = channel.workspace
    platform = channel.integration.slug
    user = User.objects.get(id=user_id)

    custom_object = CustomObject.objects.filter(id=custom_object_id).first()

    task = TransferHistory.objects.filter(workspace=workspace, type=f'export_{custom_object.slug}').order_by('-created_at').first()

    if lang == 'ja':
        Notification.objects.create(workspace=workspace, user=user, message=f"{custom_object.name}をエクスポートしております。少々お待ちください...", type="success")
    else:
        Notification.objects.create(workspace=workspace, user=user, message=f"{custom_object.name} being export. Please give it a few moment...", type="success")

    try:
        access_token = refresh_hubspot_token(channel.id)

        inputs = []
        hubspotFieldType = {
            'bool': 'booleancheckbox',
            'enumeration': 'select',
            'date': 'date',
            'datetime': 'date',
            'string': 'text',
            'number': 'number',
        }
        list_create_properties = [{
            'name': 'sanka_id',
            'label': 'Sanka Id',
            'type': 'string'
        }]
        for property in list_create_properties:
            options = []
            input = {
                "name": property['name'],
                "label": property['label'],
                "type": property['type'],
                "fieldType": 'html' if property['name'] == f'{platform_object_type_id}_link' else hubspotFieldType[property['type']],
                "groupName": hubspot_group_name,
                "description": f"property {property['name']} for {platform_object_type_id}",
                "options": options,
                "searchable": True,
                "displayOrder": -1,
                "hasUniqueValue": "true" if property['name'] == 'id' else "false",
                "hidden": "false",
                "formField": "true"
            }

            inputs.append(input)
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        owners = get_all_owners(headers)

        ok, response = create_bulk_properties(access_token, platform_object_type_id, inputs)

        if not ok:
            raise Exception(response)

        # fetch record from Hubspot's custom object
        hs_object_property_ids = []
        ok, response = get_records_object(access_token, platform_object_type_id, [])
        if ok:
            hs_object_property_ids.extend([data['id'] for data in response])

        inputs = []
        inputs_batch_update = []

        # fetch record from sanka
        custom_object_rows = []
        if len(custom_object_ids) > 0:
            custom_object_rows = CustomObjectPropertyRow.objects.filter(custom_object=custom_object,workspace=workspace,id__in=custom_object_ids,usage_status='active')
        else:
            custom_object_rows = CustomObjectPropertyRow.objects.filter(custom_object=custom_object,workspace=workspace,usage_status='active')

        for row in custom_object_rows:
            properties = {}
            row_platform = row.custom_object_platform_relations.filter(platform_object_type=platform_object_type_id).order_by('-created_at').first()
            for hubspot_property, sanka_property in mapping_custom_fields.items():
                if '|' not in hubspot_property:
                    continue

                hubspot_property_list = hubspot_property.split('|')
                hubspot_property_name = hubspot_property_list[0]
                hubspot_property_type = hubspot_property_list[1]
                hubspot_property_label = hubspot_property_list[2]

                CustomFieldName = CustomObjectPropertyName.objects.filter(workspace=workspace,custom_object=custom_object,name=sanka_property).first()
                if CustomFieldName:
                    custom_value,_ = CustomObjectPropertyValue.objects.get_or_create(field_name=CustomFieldName,object=row)

                    if custom_value:
                        if custom_value.value and CustomFieldName.type == 'tag':
                            tag_values = ast.literal_eval(custom_value.value)
                            properties[hubspot_property_name] = ','.join([
                                tag['value'] for tag in tag_values
                            ])
                        if custom_value.value_number:
                            properties[hubspot_property_name] = custom_value.value_number
                        if custom_value.value_time:
                            properties[hubspot_property_name] = custom_value.value_time.isoformat()
                        if custom_value.value:
                            properties[hubspot_property_name] = custom_value.value
                        else:
                            properties[hubspot_property_name] = ""
                            
                        if hubspot_property_name == 'hubspot_owner_id':
                            for owner in owners:
                                if owner['email'] == custom_value.value:
                                    properties[hubspot_property_name] = owner['id']
                                    break
                                
                elif sanka_property == 'id':
                    properties[hubspot_property_name] = str(row.id)

            platform_id = ''
            if row_platform and row_platform.platform_id:
                platform_id = row_platform.platform_id

            if platform_id in hs_object_property_ids:
                inputs_batch_update.append({'id': str(platform_id),'properties': properties})
            else:
                inputs.append({'properties': properties})
        
        successful_updated_records = []
        if inputs_batch_update:
            ok, response = update_batch_records(access_token, platform_object_type_id, inputs_batch_update, is_update=True)
            if ok:
                print('Log update:', response)
                successful_updated_records = response
            else:
                if lang == 'ja': # in japanese
                    Notification.objects.create(workspace=workspace, user=user, message=f"Hubspotカスタムオブジェクト {custom_object.name} アップデートに失敗しました: {response}", type='error')
                else:
                    Notification.objects.create(workspace=workspace, user=user, message=f"Hubspot custom object {custom_object.name} update failed: {response}", type='error')
                raise Exception(response)
        successful_records, failed_records = create_batch_records(access_token, platform_object_type_id, inputs, workspace=workspace, user=user)
        if failed_records:
            raise Exception(response)

        for record in successful_records:
            sanka_id = record['properties'].get('sanka_id')
            platform_id = record.get('id')

            row = CustomObjectPropertyRow.objects.get(id=sanka_id)

            for hubspot_association, sanka_association in mapping_custom_fields_association.items():
                association_label = AssociationLabel.objects.filter(id=sanka_association).first()
                if not association_label:
                    continue

                hubspot_association_list = hubspot_association.split('|')
                hubspot_association_name = hubspot_association_list[0]
                hubspot_association_id = hubspot_association_list[1]
                hubspot_association_from_object_type_id = hubspot_association_list[2]
                hubspot_association_to_object_type_id = hubspot_association_list[3]

                selected_associations = AssociationLabelObject.get_for_object(row, workspace, association_label).values_list('target_object_id', flat=True)
                obj_id = row.id
                page_group_type = association_label.object_source
                object_targets = association_label.object_target.split(',')
                if obj_id:
                    try:
                        page_obj = get_page_object(page_group_type, 'en')
                        object = page_obj['base_model'].objects.filter(id=obj_id).first()
                    except:
                        object = CustomObjectPropertyRow.objects.filter(id=obj_id).first()
                    if object:
                        selected_associations = AssociationLabelObject.get_for_object(object,workspace,association_label).values_list('target_object_id')
                        logger.info(f"Log credentials: {hubspot_association_name}, {hubspot_association_id}, {hubspot_association_from_object_type_id}, {hubspot_association_to_object_type_id}")
                        assoc_platform_ids = []
                        for selected_association in selected_associations:
                            if TYPE_OBJECT_COMPANY in object_targets and hubspot_association_to_object_type_id == '0-2':
                                object_platform = CompanyPlatforms.objects.filter(
                                    channel=channel, company_id=selected_association).first()
                                assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None

                            if TYPE_OBJECT_CONTACT in object_targets and hubspot_association_to_object_type_id == '0-1':
                                object_platform = ContactsPlatforms.objects.filter(
                                    channel=channel, contact_id=selected_association).first()
                                assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None

                            if TYPE_OBJECT_ORDER in object_targets and hubspot_association_to_object_type_id == '0-3':
                                object_platform = ShopTurboOrdersPlatforms.objects.filter(
                                    channel=channel, order_id=selected_association).first()
                                assoc_platform_ids.append(object_platform.platform_order_id) if object_platform and object_platform.platform_order_id else None

                            if not assoc_platform_ids:
                                object_platform = CustomObjectPlatforms.objects.filter(
                                    channel=channel, object_id=selected_association).first()
                                assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None
                        logger.info(f"Log association platform ids: {assoc_platform_ids}")

                        if len(assoc_platform_ids) > 0:
                            for assoc_platform_id in assoc_platform_ids:
                                url = f"https://api.hubapi.com/crm/v3/associations/{hubspot_association_from_object_type_id}/{hubspot_association_to_object_type_id}/batch/create"
                                data = {
                                    "inputs": [
                                        {
                                            "from": {"id": platform_id},
                                            "to": {"id": assoc_platform_id},
                                            "type": hubspot_association_id
                                        }
                                    ]
                                }
                                try:
                                    response = requests.post(
                                        url,
                                        headers=headers,
                                        json=data
                                    )
                                    if response.status_code not in [200, 201, 204]:
                                        logger.info(f"Failed to associate {platform_id} -> {assoc_platform_id}: {response.text}")
                                    else:
                                        logger.info(f"Successfully associated {platform_id} -> {assoc_platform_id}")
                                except Exception as e:
                                    logger.error(f"Exception occurred during association: {str(e)}")

            CustomObjectPlatforms.objects.update_or_create(
                custom_object=custom_object,
                object=row,
                channel=channel,
                platform_id=platform_id,
                platform='hubspot',
                platform_object_type=platform_object_type_id
            )

        for record in successful_updated_records:
            sanka_id = record['properties'].get('sanka_id')
            platform_id = record.get('id')

            row = CustomObjectPropertyRow.objects.get(id=sanka_id)

            for hubspot_association, sanka_association in mapping_custom_fields_association.items():
                association_label = AssociationLabel.objects.filter(id=sanka_association).first()
                if not association_label:
                    continue

                hubspot_association_list = hubspot_association.split('|')
                hubspot_association_name = hubspot_association_list[0]
                hubspot_association_id = hubspot_association_list[1]
                hubspot_association_from_object_type_id = hubspot_association_list[2]
                hubspot_association_to_object_type_id = hubspot_association_list[3]

                selected_associations = AssociationLabelObject.get_for_object(row, workspace, association_label).values_list('target_object_id', flat=True)
                obj_id = row.id
                page_group_type = association_label.object_source
                object_targets = association_label.object_target.split(',')
                if obj_id:
                    try:
                        page_obj = get_page_object(page_group_type, 'en')
                        object = page_obj['base_model'].objects.filter(id=obj_id).first()
                    except:
                        object = CustomObjectPropertyRow.objects.filter(id=obj_id).first()
                    if object:
                        selected_associations = AssociationLabelObject.get_for_object(object,workspace,association_label).values_list('target_object_id')
                        logger.info(f"Log credentials: {hubspot_association_name}, {hubspot_association_id}, {hubspot_association_from_object_type_id}, {hubspot_association_to_object_type_id}")
                        assoc_platform_ids = []
                        for selected_association in selected_associations:
                            if TYPE_OBJECT_COMPANY in object_targets and hubspot_association_to_object_type_id == '0-2':
                                object_platform = CompanyPlatforms.objects.filter(
                                    channel=channel, company_id=selected_association).first()
                                assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None

                            if TYPE_OBJECT_CONTACT in object_targets and hubspot_association_to_object_type_id == '0-1':
                                object_platform = ContactsPlatforms.objects.filter(
                                    channel=channel, contact_id=selected_association).first()
                                assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None

                            if TYPE_OBJECT_ORDER in object_targets and hubspot_association_to_object_type_id == '0-3':
                                object_platform = ShopTurboOrdersPlatforms.objects.filter(
                                    channel=channel, order_id=selected_association).first()
                                assoc_platform_ids.append(object_platform.platform_order_id) if object_platform and object_platform.platform_order_id else None

                            if not assoc_platform_ids:
                                object_platform = CustomObjectPlatforms.objects.filter(
                                    channel=channel, object_id=selected_association).first()
                                assoc_platform_ids.append(object_platform.platform_id) if object_platform and object_platform.platform_id else None
                        logger.info(f"Log association platform ids: {assoc_platform_ids}")

                        if len(assoc_platform_ids) > 0:
                            for assoc_platform_id in assoc_platform_ids:
                                url = f"https://api.hubapi.com/crm/v3/associations/{hubspot_association_from_object_type_id}/{hubspot_association_to_object_type_id}/batch/create"
                                data = {
                                    "inputs": [
                                        {
                                            "from": {"id": platform_id},
                                            "to": {"id": assoc_platform_id},
                                            "type": hubspot_association_id
                                        }
                                    ]
                                }
                                try:
                                    response = requests.post(
                                        url,
                                        headers=headers,
                                        json=data
                                    )
                                    if response.status_code not in [200, 201, 204]:
                                        logger.info(f"Failed to associate {platform_id} -> {assoc_platform_id}: {response.text}")
                                    else:
                                        logger.info(f"Successfully associated {platform_id} -> {assoc_platform_id}")
                                except Exception as e:
                                    logger.error(f"Exception occurred during association: {str(e)}")

        if lang == 'ja':
            Notification.objects.create(workspace=workspace, user=user, message=f"{custom_object.name}が Hubspot に正常にエクスポートされました", type="success")
        else:
            Notification.objects.create(workspace=workspace, user=user, message=f"{custom_object.name} successfully exported to Hubspot", type="success")

        task.status = 'completed'
        task.progress = 100
        task.save()

    except Exception as e:
        task.status = 'Failed'
        task.progress = 0
        task.save()
        logger.error(f'[ERROR] Error export hubspot custom object {custom_object.name}, {e}')

        if lang == 'ja':
            Notification.objects.create(workspace=workspace, user=user, message=f'Hubspot {custom_object.name} のエクスポートに失敗しました', type='error')
        else:
            Notification.objects.create(workspace=workspace, user=user, message=f'Hubspot {custom_object.name} export failed', type='error')

def export_invoice_hubspot_custom_object(channel_id: str, platform_object_type_id: str, mapping_custom_fields={}, hubspot_group_name='', lang='ja', user_id='', invoice_ids=[]):
    channel = Channel.objects.get(pk=channel_id)
    workspace = channel.workspace
    platform = channel.integration.slug
    user = User.objects.get(id=user_id)

    task = TransferHistory.objects.filter(workspace=workspace, type='export_invoice').order_by('-created_at').first()
    if task:
        task.total_number = len(invoice_ids)
        task.save(update_fields=['total_number'])

    try:
        access_token = refresh_hubspot_token(channel.id)

        inputs = []
        hubspotFieldType = {
            'bool': 'booleancheckbox',
            'enumeration': 'select',
            'date': 'date',
            'datetime': 'date',
            'string': 'text',
            'number': 'number',
        }
        list_create_properties = [{
            'name': 'sanka_id',
            'label': 'Sanka Id',
            'type': 'string'
        }, {
            'name': 'id',
            'label': 'ID',
            'type': 'string'
        }]
        for property in list_create_properties:
            options = []
            input = {
                "name": property['name'],
                "label": property['label'],
                "type": property['type'],
                "fieldType": 'html' if property['name'] == f'{platform_object_type_id}_link' else hubspotFieldType[property['type']],
                "groupName": hubspot_group_name,
                "description": f"property {property['name']} for {platform_object_type_id}",
                "options": options,
                "searchable": True,
                "displayOrder": -1,
                "hasUniqueValue": "true" if property['name'] in ['sanka_id', 'id'] else "false",
                "hidden": "false",
                "formField": "true"
            }

            inputs.append(input)

        ok, response = create_bulk_properties(access_token, platform_object_type_id, inputs)

        if not ok:
            raise Exception(response)

        # Fetch records from HubSpot's custom object
        hs_object_property_ids = []
        ok, response = get_records_object(access_token, platform_object_type_id, [])
        if ok:
            hs_object_property_ids.extend([data['id'] for data in response])

        inputs = []
        inputs_batch_update = []

        if len(invoice_ids) > 0:
            invoices = Invoice.objects.filter(id__in=invoice_ids)
        else:
            invoices = Invoice.objects.filter(workspace=workspace)

        for row in invoices:
            properties = {
                'sanka_id': str(row.id),  # Always set sanka_id as it's required by HubSpot
                'id': str(row.id)
            }
            row_platform = InvoicePlatforms.objects.filter(invoice=row, channel=channel).order_by('-created_at').first()

            # Map custom fields
            for hubspot_property, sanka_property in mapping_custom_fields.items():
                print('[DEBUG] - export_invoice_hubspot_custom_object - hubspot_property:', hubspot_property, 'sanka_property:', sanka_property)
                if '|' not in hubspot_property:
                    continue

                hubspot_property_list = hubspot_property.split('|')
                hubspot_property_name = hubspot_property_list[0]

                if sanka_property == 'id':
                    properties[hubspot_property_name] = str(row.id)
                elif sanka_property == 'platform_id':
                    if row_platform and row_platform.platform_id:
                        properties[hubspot_property_name] = str(row_platform.platform_id)
                    else:
                        # Don't set empty value for 'id' property as it's required by HubSpot
                        if hubspot_property_name != 'id':
                            properties[hubspot_property_name] = ''
                elif sanka_property == 'customers':
                    if row.contact:
                        properties[hubspot_property_name] = row.contact.name
                    elif row.company:
                        properties[hubspot_property_name] = row.company.name
                    else:
                        properties[hubspot_property_name] = ''
                else:
                    try:
                        property_value = getattr(row, sanka_property, None)
                        try:
                            if sanka_property == 'tax_rate':
                                property_value = float(property_value) / 100
                        except:
                            pass
                        try:
                            field = row._meta.get_field(sanka_property)
                            property_type = field.get_internal_type().lower()
                        except Exception:
                            property_type = type(property_value).__name__.lower()

                        if property_type == 'datetimefield':
                            properties[hubspot_property_name] = property_value.strftime('%Y-%m-%dT%H:%M:%S.%fZ') if property_value else ''
                        elif property_type == 'datefield':
                            properties[hubspot_property_name] = property_value.strftime('%Y-%m-%d') if property_value else ''
                        else:
                            properties[hubspot_property_name] = property_value
                    except:
                        pass

                    if not property_value:
                        field_name = InvoiceNameCustomField.objects.filter(
                            workspace=workspace,
                            name=sanka_property
                        ).first()
                        if field_name:
                            field_value,_ = InvoiceValueCustomField.objects.get_or_create(
                                invoice=row,
                                field_name=field_name,
                            )
                            if field_value.value:
                                properties[hubspot_property_name] = field_value.value

            platform_id = ''
            if row_platform and row_platform.platform_id:
                platform_id = row_platform.platform_id
            if platform_id in hs_object_property_ids:
                inputs_batch_update.append({'id': str(platform_id), 'properties': properties})
            else:
                inputs.append({'properties': properties})

            # Handle associations with contact or company
            try:
                if row.contact:
                    contact_id = ensure_hubspot_contact(row.contact, access_token)
                    create_hubspot_association(access_token, platform_object_type_id, platform_id or row.id, 'contact', contact_id)
                if row.company:
                    company_id = ensure_hubspot_company(row.company, access_token)
                    create_hubspot_association(access_token, platform_object_type_id, platform_id or row.id, 'company', company_id)
            except:
                pass

        if inputs_batch_update:
            ok, response = update_batch_records(access_token, platform_object_type_id, inputs_batch_update, is_update=True)
            if not ok:
                raise Exception(response)

        successful_records, failed_records = create_batch_records(access_token, platform_object_type_id, inputs)
        if successful_records or failed_records:
            if task:
                task.success_number = len(successful_records) if successful_records else 0
                task.failed_number = len(failed_records) if failed_records else 0
                task.save(update_fields=['success_number', 'failed_number'])
        
        if failed_records:
            raise Exception(response)

        for record in successful_records:
            sanka_id = record['properties'].get('sanka_id')
            platform_id = record.get('id')

            successful_invoice = Invoice.objects.get(id=sanka_id)
            InvoicePlatforms.objects.update_or_create(
                invoice=successful_invoice,
                channel=channel,
                platform_id=platform_id,
                platform='hubspot'
            )

        task.status = 'completed'
        task.progress = 100
        task.save()

    except Exception as e:
        print('Exception:', e)
        task.status = 'Failed'
        task.progress = 0
        task.save()


def export_hubspot_subscriptions_custom_object(channel_id: str, custom_object_id: str, subscription_ids=[], user_id='', workspace_id='', language='en', mapping_data=None):
    """
    Export subscriptions to HubSpot custom object
    """
    try:
        channel = Channel.objects.get(pk=channel_id)
        workspace = channel.workspace
        user = User.objects.get(id=user_id)
        
        # Get mapping fields for subscriptions
        mapping_custom_fields = None
        
        # Use mapping data from payload if available, otherwise fall back to database
        if mapping_data and mapping_data.get('subscription_mapping'):
            mapping_custom_fields = mapping_data['subscription_mapping']
            print(f"Using mapping data from payload with {len(mapping_custom_fields)} field mappings")
        else:
            # Fall back to database query
            mapping_obj = SubscriptionMappingFields.objects.filter(
                workspace=workspace, 
                platform=channel.integration.slug
            ).first()
            
            if not mapping_obj or not mapping_obj.input_data:
                print("No mapping configuration found for subscriptions. User needs to configure field mapping first.")
                print(f"Checked for workspace: {workspace.id}, platform: {channel.integration.slug}")
                # Return False to indicate the export failed due to missing configuration
                return False
            else:
                mapping_custom_fields = mapping_obj.input_data
                print(f"Found mapping configuration from database with {len(mapping_custom_fields)} field mappings")
        
        # Get HubSpot custom object details
        access_token = refresh_hubspot_token(channel.id)
        platform_object = get_schema_detail_by_object_type_id(channel.id, custom_object_id)
        
        if not platform_object:
            print(f"Custom object {custom_object_id} not found in HubSpot")
            return False
        
        platform_object_type_id = platform_object.object_type_id
        
        # Create required properties in HubSpot
        hubspotFieldType = {
            'bool': 'booleancheckbox',
            'enumeration': 'select',
            'date': 'date',
            'datetime': 'date',
            'string': 'text',
            'number': 'number',
        }
        
        list_create_properties = [{
            'name': 'sanka_id',
            'label': 'Sanka Id',
            'type': 'string'
        }, {
            'name': 'id',
            'label': 'ID',
            'type': 'string'
        }]
        
        # Get the hubspot_group_name from platform object
        hubspot_group_name = None
        if hasattr(platform_object, 'properties') and platform_object.properties:
            for prop in platform_object.properties:
                if hasattr(prop, 'group_name') and prop.group_name:
                    hubspot_group_name = prop.group_name
                    break
        
        if not hubspot_group_name:
            hubspot_group_name = f"{platform_object.labels.singular}_information"
        
        inputs = []
        for property in list_create_properties:
            input = {
                "name": property['name'],
                "label": property['label'],
                "type": property['type'],
                "fieldType": hubspotFieldType[property['type']],
                "groupName": hubspot_group_name,
                "description": f"property {property['name']} for {platform_object_type_id}",
                "options": [],
                "searchable": True,
                "displayOrder": -1,
                "hasUniqueValue": "true" if property['name'] in ['sanka_id', 'id'] else "false",
                "hidden": "false",
                "formField": "true"
            }
            inputs.append(input)
        
        # Create properties in HubSpot
        ok, response = create_bulk_properties(access_token, platform_object_type_id, inputs)
        if not ok:
            print(f"Failed to create properties: {response}")
            # Check if it's just because the property already exists
            if "already exists" in response.lower() or "property already exists" in response.lower():
                print("Property 'sanka_id' already exists, continuing with export...")
            else:
                return False
        
        # Fetch existing records from HubSpot's custom object
        hs_object_property_ids = []
        ok, response = get_records_object(access_token, platform_object_type_id, [])
        if ok:
            hs_object_property_ids.extend([data['id'] for data in response])
        
        inputs = []
        inputs_batch_update = []
        
        # Get subscriptions to export
        if len(subscription_ids) > 0:
            subscriptions = ShopTurboSubscriptions.objects.filter(id__in=subscription_ids)
        else:
            subscriptions = ShopTurboSubscriptions.objects.filter(workspace=workspace)
        
        if not subscriptions.exists():
            print("No subscriptions found to export")
            return False
        
        for row in subscriptions:
            properties = {}
            row_platform = ShopTurboSubscriptionPlatforms.objects.filter(
                source_subscription=row, 
                channel=channel
            ).order_by('-created_at').first()
            
            # Add required properties
            properties['sanka_id'] = str(row.id)
            properties['id'] = str(row.id)  # Required by HubSpot
            
            # Map custom fields
            for hubspot_property, sanka_property in mapping_custom_fields.items():
                if '|' not in hubspot_property:
                    continue
                
                hubspot_property_list = hubspot_property.split('|')
                hubspot_property_name = hubspot_property_list[0]
                
                # Special handling for HubSpot 'id' property - always use Sanka ID
                if hubspot_property_name == 'id':
                    properties[hubspot_property_name] = str(row.id)
                elif sanka_property == 'id':
                    properties[hubspot_property_name] = str(row.id)
                elif sanka_property == 'platform_id':
                    properties[hubspot_property_name] = ''
                    if row_platform and row_platform.platform_id:
                        properties[hubspot_property_name] = str(row_platform.platform_id)
                elif sanka_property == 'contact':
                    if row.contact:
                        properties[hubspot_property_name] = row.contact.name
                    else:
                        properties[hubspot_property_name] = ''
                elif sanka_property == 'company':
                    if row.company:
                        properties[hubspot_property_name] = row.company.name
                    else:
                        properties[hubspot_property_name] = ''
                elif sanka_property == 'item':
                    if row.item:
                        properties[hubspot_property_name] = row.item.name
                    else:
                        properties[hubspot_property_name] = ''
                elif sanka_property == 'item_variant':
                    if row.item_variant:
                        properties[hubspot_property_name] = row.item_variant.name
                    else:
                        properties[hubspot_property_name] = ''
                else:
                    try:
                        property_value = getattr(row, sanka_property, None)
                        
                        # Handle different field types
                        try:
                            field = row._meta.get_field(sanka_property)
                            property_type = field.get_internal_type().lower()
                        except Exception:
                            property_type = type(property_value).__name__.lower()
                        
                        if property_type == 'datetimefield':
                            properties[hubspot_property_name] = property_value.strftime('%Y-%m-%dT%H:%M:%S.%fZ') if property_value else ''
                        elif property_type == 'datefield':
                            properties[hubspot_property_name] = property_value.strftime('%Y-%m-%d') if property_value else ''
                        else:
                            # Ensure proper serialization of all values
                            properties[hubspot_property_name] = str(property_value) if property_value is not None else ''
                    except:
                        pass
                    
                    # Handle custom fields if standard field not found
                    if not properties.get(hubspot_property_name):
                        field_name = ShopTurboSubscriptionsNameCustomField.objects.filter(
                            workspace=workspace,
                            name=sanka_property
                        ).first()
                        if field_name:
                            field_value = ShopTurboSubscriptionsValueCustomField.objects.filter(
                                subscriptions=row,
                                field_name=field_name,
                            ).first()
                            if field_value and field_value.value:
                                properties[hubspot_property_name] = field_value.value
            
            # Check if record exists in HubSpot
            platform_id = ''
            if row_platform and row_platform.platform_id:
                platform_id = row_platform.platform_id
            
            if platform_id in hs_object_property_ids:
                inputs_batch_update.append({'id': str(platform_id), 'properties': properties})
            else:
                inputs.append({'properties': properties})
            
            # Ensure the 'id' property is always set (backup check)
            if 'id' not in properties:
                properties['id'] = str(row.id)
        
        # Update existing records
        if inputs_batch_update:
            ok, response = update_batch_records(access_token, platform_object_type_id, inputs_batch_update, is_update=True)
            if not ok:
                print(f"Failed to update records: {response}")
                return False
        
        # Create new records
        if inputs:
            successful_records, failed_records = create_batch_records(access_token, platform_object_type_id, inputs)
            if failed_records:
                print(f"Failed to create some records: {failed_records}")
                # Continue even if some records failed
            
            # Update platform mappings and create associations for successful records
            for record in successful_records:
                sanka_id = record['properties'].get('sanka_id')
                platform_id = record.get('id')
                
                if sanka_id and platform_id:
                    try:
                        successful_subscription = ShopTurboSubscriptions.objects.get(id=sanka_id)
                        ShopTurboSubscriptionPlatforms.objects.update_or_create(
                            source_subscription=successful_subscription,
                            channel=channel,
                            defaults={
                                'platform_id': platform_id,
                                'platform_display_name': successful_subscription.platform_display_name or '',
                            }
                        )
                        
                        # Create associations with contact/company now that we have the HubSpot ID
                        if successful_subscription.contact:
                            try:
                                contact_id = ensure_hubspot_contact(successful_subscription.contact, access_token)
                                create_hubspot_association(access_token, platform_object_type_id, platform_id, 'contact', contact_id)
                            except Exception as e:
                                print(f"Failed to create association with contact: {e}")
                        
                        if successful_subscription.company:
                            try:
                                company_id = ensure_hubspot_company(successful_subscription.company, access_token)
                                create_hubspot_association(access_token, platform_object_type_id, platform_id, 'company', company_id)
                            except Exception as e:
                                print(f"Failed to create association with company: {e}")
                        
                    except ShopTurboSubscriptions.DoesNotExist:
                        print(f"Subscription {sanka_id} not found")
                        pass
        
        print(f"Successfully exported {len(subscriptions)} subscriptions to HubSpot custom object {custom_object_id}")
        return True
        
    except Exception as e:
        print(f"Error in export_hubspot_subscriptions_custom_object: {e}")
        import traceback
        traceback.print_exc()
        return False
    
def export_hubspot_cases(channel_id: str, case_ids=[], mapping_custom_fields={}, history_id=None, lang='ja'):
    """
    Export cases to HubSpot tickets
    """
    try:
        channel = Channel.objects.get(id=channel_id)
        access_token = refresh_hubspot_token(channel.id)
        workspace = channel.workspace
        task = TransferHistory.objects.filter(id=history_id).first() if history_id else None
        check_required_properties(access_token)

        if not access_token:
            raise Exception("Invalid access token")

        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        inputs = []
        inputs_batch_update = []

        if case_ids:
            cases = Deals.objects.filter(id__in=case_ids)
        else:
            cases = Deals.objects.filter(workspace=workspace, status='active')

        # Get existing ticket IDs to determine update vs create
        ok, existing_tickets = get_records_object(access_token, '0-5', [])
        existing_ticket_ids = [ticket['id'] for ticket in existing_tickets] if ok else []

        for case in cases:
            properties = {}
            case_platform = DealsPlatforms.objects.filter(deal=case, channel=channel).first()

            # Map custom fields
            for hubspot_property, sanka_property in mapping_custom_fields.items():
                try:
                    if sanka_property == 'id':
                        properties[hubspot_property] = str(case.id)
                    elif sanka_property == 'platform_id':
                        properties[hubspot_property] = ''
                        if case_platform and case_platform.platform_id:
                            properties[hubspot_property] = str(case_platform.platform_id)
                    else:
                        property_value = getattr(case, sanka_property, None)
                        try:
                            field = case._meta.get_field(sanka_property)
                            property_type = field.get_internal_type().lower()
                        except Exception:
                            property_type = type(property_value).__name__.lower()

                        if property_type == 'datetimefield':
                            properties[hubspot_property] = property_value.strftime('%Y-%m-%dT%H:%M:%S.%fZ') if property_value else ''
                        elif property_type == 'datefield':
                            properties[hubspot_property] = property_value.strftime('%Y-%m-%d') if property_value else ''
                        else:
                            properties[hubspot_property] = str(property_value) if property_value is not None else ''
                except Exception as e:
                    print(f"Failed mapping {sanka_property} → {hubspot_property}: {e}")
                    continue

            # Use sanka_id for deduplication
            sanka_id = str(case.id)
            properties['sanka_id'] = sanka_id
            if 'subject' in properties:
                if properties['subject'] == '':
                    properties['subject'] = case.name if case.name else ''
            else:
                properties['subject'] = case.name if case.name else ''

            properties['hs_pipeline_stage'] = '1'

            # Check if we need to update or create
            platform_id = ''
            if case_platform and case_platform.platform_id:
                platform_id = case_platform.platform_id

            if platform_id and platform_id in existing_ticket_ids:
                inputs_batch_update.append({'id': platform_id, 'properties': properties})
            else:
                inputs.append({'properties': properties})

        # Update existing tickets
        if inputs_batch_update:
            ok, update_resp = update_batch_records(access_token, '0-5', inputs_batch_update, is_update=True)
            if not ok:
                print(f"Failed to update HubSpot tickets: {update_resp}")
                return False

        # Create new tickets
        if inputs:
            successful_records, failed_records = create_batch_records(access_token, '0-5', inputs)
            if failed_records:
                print(f"Some tickets failed to create: {failed_records}")

            for record in successful_records:
                sanka_id = record['properties'].get('sanka_id')
                hubspot_id = record.get('id')

                if sanka_id and hubspot_id:
                    try:
                        deal = Deals.objects.get(id=sanka_id)
                        DealsPlatforms.objects.update_or_create(
                            deal=deal,
                            channel=channel,
                            platform_id=hubspot_id,
                        )

                        # Create associations with contact/company now that we have the HubSpot ID
                        if deal.contact and deal.contact.count() > 0:
                            try:
                                contact_id = ensure_hubspot_contact(deal.contact.first(), access_token)
                                create_hubspot_association(access_token, '0-5', hubspot_id, 'contact', contact_id)
                            except Exception as e:
                                print(f"Failed to create association with contact: {e}")
                        
                        if deal.company and deal.company.count() > 0:
                            try:
                                company_id = ensure_hubspot_company(deal.company.first(), access_token)
                                create_hubspot_association(access_token, '0-5', hubspot_id, 'company', company_id)
                            except Exception as e:
                                print(f"Failed to create association with company: {e}")
                    except Deals.DoesNotExist:
                        print(f"Deal {sanka_id} not found")

        if task:
            task.status = 'success'
            task.save()

        print(f"Successfully exported {len(cases)} cases to HubSpot Tickets")
        return True

    except Exception as e:
        print(f"Error in export_hubspot_cases: {e}")
        import traceback
        traceback.print_exc()
        if history_id:
            TransferHistory.objects.filter(id=history_id).update(status='failed')
        return False


def ensure_hubspot_contact(contact, access_token):
    """
    Ensure the contact exists in HubSpot. If not, create it.
    """
    # Check if the contact exists in HubSpot
    contact_platform = ContactsPlatforms.objects.filter(contact=contact).first()
    if contact_platform and contact_platform.platform_id:
        return contact_platform.platform_id

    # Create the contact in HubSpot
    contact_data = {
        "properties": {
            "firstname": contact.name,
            "email": contact.email,
            "phone": contact.phone_number,
        }
    }
    response = requests.post(
        "https://api.hubapi.com/crm/v3/objects/contacts",
        headers={"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"},
        json=contact_data
    )
    response_data = response.json()
    if response.status_code == 201:
        contact_platform, _ = ContactsPlatforms.objects.get_or_create(contact=contact)
        contact_platform.platform_id = response_data["id"]
        contact_platform.save()
        return response_data["id"]
    else:
        print(f"Failed to create contact in HubSpot: {response_data}")


def ensure_hubspot_company(company, access_token):
    """
    Ensure the company exists in HubSpot. If not, create it.
    """
    # Check if the company exists in HubSpot
    company_platform = CompanyPlatforms.objects.filter(company=company).first()
    if company_platform and company_platform.platform_id:
        return company_platform.platform_id

    # Create the company in HubSpot
    company_data = {
        "properties": {
            "name": company.name,
            "email": company.email,
            "phone": company.phone_number,
        }
    }
    response = requests.post(
        "https://api.hubapi.com/crm/v3/objects/companies",
        headers={"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"},
        json=company_data
    )
    response_data = response.json()
    if response.status_code == 201:
        company_platform, _ = CompanyPlatforms.objects.get_or_create(company=company)
        company_platform.platform_id = response_data["id"]
        company_platform.save()
        return response_data["id"]
    else:
        print(f"Failed to create company in HubSpot: {response_data}")


def create_hubspot_association(access_token, from_object_type, from_object_id, to_object_type, to_object_id):
    """
    Create an association between two HubSpot objects.
    """
    label_url = f"https://api.hubapi.com/crm/v4/associations/{from_object_type}/{to_object_type}/labels"
    label_response = requests.get(
        label_url,
        headers={"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    )
    print(f"Label response: {label_response.json()}")

    assoc_type = '17'
    try:
        if label_response.status_code == 200:
            assoc_type = label_response.json().get('results')[0].get('typeId', 'association')
    except:
        pass
        
    url = f"https://api.hubapi.com/crm/v3/associations/{from_object_type}/{to_object_type}/batch/create"
    data = {
        "inputs": [
            {
                "from": {"id": from_object_id},
                "to": {"id": to_object_id},
                "type": assoc_type
            }
        ]
    }
    response = requests.post(
        url,
        headers={"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"},
        json=data
    )
    if response.status_code != 201:
        print(f"Failed to create association between {from_object_type} and {to_object_type}: {response.json()}")

def export_hubspot_items(items, channel_id, mapping_custom_fields):
    try:
        channel = Channel.objects.get(id=channel_id)
        access_token = refresh_hubspot_token(channel.id)
        workspace = channel.workspace
        task = TransferHistory.objects.filter(workspace=workspace, type='export_item').order_by('-created_at').first()

        if not access_token:
            raise Exception("Invalid access token")

        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        items_list = list(items)  # Convert items to a list

        for item in items:
            if task:
                task.progress = round(float(items_list.index(item) / len(items_list)) * 100, 2)
                task.save()
            try:
                # Prepare the payload for the item
                item_platform,_ = ShopTurboItemsPlatforms.objects.get_or_create(channel=channel, item=item)
                if item_platform:
                    hs_id = item_platform.platform_id
                else:
                    hs_id = None

                item_price = ShopTurboItemsPrice.objects.filter(item=item, default=True).first()
                if item_price:
                    item_price = item_price.price
                else:
                    item_price = item.price

                if not item_price:
                    item_price = 0

                properties = {
                    "name": item.name if item.name else "",
                    "description": item.description if item.description else "",
                    "price": item_price,
                }

                try:
                    if mapping_custom_fields:
                        for key, val in mapping_custom_fields.items():
                            if key in ['name', 'price']:
                                pass
                            else:
                                try:
                                    custom_field = ShopTurboItemsNameCustomField.objects.filter(workspace=workspace, name=val).first()
                                    custom_value = None
                                    if custom_field:
                                        custom_value,_ = ShopTurboItemsValueCustomField.objects.get_or_create(field_name=custom_field, items=item)
                                        custom_value = custom_value.value

                                        if not custom_value:
                                            custom_value = ''
                                        
                                        try:
                                            custom_value = int(custom_value)
                                        except:
                                            pass

                                        properties[key] = custom_value
                                except:
                                    traceback.print_exc()
                                    pass
                except:
                    traceback.print_exc()
                    pass

                payload = {
                    "properties": properties
                }
                print('logprop', properties)
            
                # Send the request to HubSpot API
                if hs_id == 'None':
                    hs_id = None

                if hs_id:
                    url = f"https://api.hubapi.com/crm/v3/objects/products/{hs_id}"
                    response = requests.patch(url, headers=headers, data=json.dumps(payload))
                else:
                    url = f"https://api.hubapi.com/crm/v3/objects/products"
                    response = requests.post(url, headers=headers, data=json.dumps(payload))

                if not response.text:
                    print(f"Empty response for product {item.id} in HubSpot")
                elif response.status_code == 201:
                    print(f"Successfully created product {item.id} in HubSpot.")

                    product_id = response.json().get("id")
                    if product_id and not hs_id:
                        item_platform.platform_id = product_id
                        item_platform.save()
                elif response.status_code == 200:
                    print(f"Successfully updated product {hs_id} in HubSpot.")
                else:
                    results = response.json()
                    if results.get('status') == 'error':
                        if results.get('category') == 'OBJECT_NOT_FOUND':
                            item_platform.delete()
                            continue
                        else:
                            try:
                                invalid_properties = []
                                try:
                                    for result in results.get('errors'):
                                        if result.get('context').get('propertyName'):
                                            invalid_properties.append(result.get('context').get('propertyName')[0])
                                except:
                                    pass
                                
                                for invalid_property in invalid_properties:
                                    if invalid_property in properties:
                                        properties.pop(invalid_property)
                                print('properties:', properties)

                                data = json.dumps({"properties": properties})
                                if hs_id:
                                    url = f"https://api.hubapi.com/crm/v3/objects/products/{hs_id}"
                                    r = requests.patch(url, headers=headers, data=data)
                                else:
                                    url = f"https://api.hubapi.com/crm/v3/objects/products"
                                    r = requests.post(url, headers=headers, data=data)
                            except:
                                pass

                            if r.text:
                                results = r.json()

                            if results.get('status') == 'error':
                                if not item_platform.platform_id:
                                    item_platform.delete()
                                continue
                            else:
                                product_id = results.get("id")
                                if product_id and not hs_id:
                                    item_platform.platform_id = product_id
                                    item_platform.save()
                                pass

            except Exception as e:
                traceback.print_exc()
                print(f"Error updating product {item.id}: {e}")
    except Exception as e:
        print(f"Error exporting items to HubSpot: {e}")

    return True