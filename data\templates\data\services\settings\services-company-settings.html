
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

{% if setting_type == 'company' %}
<div class="mb-10">
    <div id="change-stamp-section" class="mb-10">

        <form method="POST" action="{% host_url 'customerlink_settings' host 'app' %}" enctype="multipart/form-data" onkeydown="return event.key != 'Enter';">
            {% csrf_token %}
            <input type="hidden" class="" name="app_settings" value="manage_app"/>

            <div
                hx-get="{% host_url 'properties' host 'app' %}"
                hx-vals='{"page_group_type": "company"}'
                hx-trigger="load"
                hx-target="#properties-table"
            >
                <div id="properties-table"></div>
            </div>

            {% comment %} association label {% endcomment %}
            <div
                hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                hx-vals='{"page_group_type": "{{constant.TYPE_OBJECT_COMPANY}}"}'
                hx-trigger="load"
            ></div>

            <div class="mt-5 mb-5 d-none">
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span >
                        {% if LANGUAGE_CODE == 'ja' %}
                        検索設定
                        {% else %}
                        Search Settings
                        {% endif %}
                    </span>
                </label>

                <input
                {% if LANGUAGE_CODE == 'ja' %}
                placeholder="検索設定"
                {% else %}
                placeholder="Search Settings"
                {% endif %}
                id="search_setting_company"
                name="search_setting_company" class="form-control" 
                
                value=""></input>
            </div>
            
            <button id="submit-customfield-company-button" name="submit-customfield-company-button" type="submit" class="btn btn-primary mb-4">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button> 
        </form>
    </div>
</div>

<style>
    .stretch-select-custom span {
        height:100% !important;
    }
    .stretch-select-custom span span span span{
        {% comment %} display: flex !important; {% endcomment %}
        align-content: center;
        flex-wrap: wrap;
        margin-bottom: auto;
        margin-top: auto;
    }
</style>
<style>
    .loading-spinner{
        display:none;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>

<script>
    $(document).ready(function() {
        $('.select2-this').select2();
    });


    function delete_company_custom_field_name(elm){
        elm.parentElement.parentElement.parentElement.parentElement.remove()
    }

    var search_setting_company = document.getElementById("search_setting_company");
    var search_setting_contact_tagify = new Tagify(search_setting_company, {
        whitelist: [
        {% for column in column_values %}
            {% with args=column|stringify|add:'|'|add:'company' %} 
                {% with column_display=args|get_column_display:request %}
                    { "value": "{{column_display.name}}", "id": "{{column_display.id}}" },
                {% endwith %}
            {% endwith %}
        {% endfor %}
        ],
        dropdown: {
            maxItems: 20,           // <- mixumum allowed rendered suggestions
            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
            enabled: 0,             // <- show suggestions on focus
            closeOnSelect: true    // <- do not hide the suggestions dropdown once an item has been selected
        },
        enforceWhitelist: true,
        searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
        originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
        });

    {% if app_setting.search_setting_company %}
        {% with app_setting.search_setting_company|split:"," as search_setting_value %}
            {% for value in search_setting_value %} 
                {% with args=value|stringify|add:'|'|add:'company' %} 
                    {% with column_display=args|get_column_display:request %}
                        search_setting_contact_tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}" }]);
                    {% endwith %}
                {% endwith %}
            {% endfor %}
        {% endwith %}
    {% endif %}

</script>
{% endif %}