*.env
.env*
.envprod
.claude
*.log
*.pot
*.pyc
__pycache__/
/venv
staticfiles
node_modules
theme
tmp
log.txt
content-files
dump.rdb
*.session
*.session-journal
member_status.json
tweet.jpeg
tweet.jpg
app.localhost+2-key.pem
app.localhost+2.pem
data/migrations/__pycache__/
google_api_client_secret.json
help.json
flow_action.json
flow_action_input.json
.vscode/
.windsurf/
*.pem
page.json
page_tag.json

.DS_Store
sanka-desktop/node_modules/
.idea/
.history/

sanka-evm/lib/
sanka-evm/db/
sanka-desktop/.sh
sanka-desktop/public/.sqlite3
sanka-desktop/dist/
sanka-desktop/dist-electron/
sanka-desktop/release/

static/san-ai/*

z_notebook-renardi
*.sqlite3

exploration/
chromedriver/windows/*
chromedriver/mac_arm/*

service_account/*

.venv
.venv2
sanka.zip
stripe.exe

sanka_hubspot/powerSearchTab/SearchFeature-local/

# HubSpot config file
hubspot.config.yml

output_test/*
.zed/
pyproject.toml
cypress.env.json

#repromix
repomix-output.txt
.repomixignore

cypress/screenshots/
cypress/downloads/
.vercel

.coverage
htmlcov/

CLAUDE.md
GEMINI.md
*.mcp.json