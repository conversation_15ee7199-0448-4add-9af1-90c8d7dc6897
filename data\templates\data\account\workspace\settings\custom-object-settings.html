{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

{% if custom_object_slug %}
{% if custom_object %}

<div class="mt-5" style="width: 100%;">
    <div class="mt-5 container-fluid px-0 scroll-y scoll-x">
        <div class="d-flex align-items-center mb-10">
            <label class="d-flex align-items-center fs-3 fw-bolder me-2">
                <span class="">
                    {{custom_object.name}}
                    {% if LANGUAGE_CODE == 'ja' %}
                    オブジェクト
                    {% else %}
                    Object
                    {% endif %}
                </span>
            </label>
            <div class='cursor-pointer'>
                <a class="custom-object-btn fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'it' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}"
                    hx-get="{% host_url 'create_custom_object' host 'app' %}"
                    hx-target="#custom-object-content"
                    hx-vals='{"custom_object_id": "{{custom_object.id}}", "update_custom_object": "true"}'
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 20h9" />
                        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4Z" />
                        <path d="m15 5 3 3" />
                    </svg>                  
                </a>
            </div>
        </div>

        <div class="mb-0-10">
            <div id="change-stamp-section" class="mb-10">
                <div class="mb-10"
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "custom_object", "custom_object_id": "{{custom_object.id}}"}'
                    hx-trigger="load"
                    hx-target="#properties-table"
                >
                    <div id="properties-table"></div>
                </div>
            </div>
        </div>

        {% comment %} ASSOCIATION LABEL SETTINGS {% endcomment %}
        <div
            hx-get="{% host_url 'association_labels_settings' host 'app' %}"
            hx-vals='{"page_group_type": "{{custom_object.id}}"}'
            hx-trigger="load"
        ></div>
     
        <form class="mb-5 pb-5" method="POST" action="{% url 'custom_object_settings' custom_object.id %}" 
        enctype="multipart/form-data" onkeydown="return event.key != 'Enter';">
            {% csrf_token %}
            <input type="hidden" class="" name="app_settings" value="manage_app"/>
            <input type="hidden" class="" name="page_group_type" value="custom_object"/>
            <input type="hidden" class="" name="custom_object_id" value="{{custom_object.id}}"/>

            <div class="mb-10">
                {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

                <label class="{% include 'data/utility/form-label.html' %}">
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja' %}
                        検索設定
                        {% else %}
                        Search Settings
                        {% endif %}
                    </span>
                </label>

                <input
                {% if LANGUAGE_CODE == 'ja' %}
                placeholder="検索設定"
                {% else %}
                placeholder="Search Settings"
                {% endif %}
                id="search_setting_custom_object"
                name="search_setting_custom_object" class="form-control" 
                value=""></input>
            </div>

            <button id="submit-customproperty-custom-object-button" name="submit-customproperty-custom-object-button" type="submit" class="btn btn-dark my-5">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </form>         

        <div class="modal fade" tabindex="-1" id="records_delete_confirmation-{{custom_object.id}}">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header pb-0 border-0">
                        <div class="mb-5 text-center">
                            <h3 class="modal-title">
                                {% if LANGUAGE_CODE == 'ja' %}
                                    カスタムオブジェクト「{{ custom_object.name }}」を削除
                                {% else %}
                                    Delete Custom Object: {{ custom_object.name }}
                                {% endif %}
                            </h3>
                        </div>
                    </div>
                    <div class="modal-footer border-0">
                        <button class="btn btn-danger btn-sm" 
                        hx-get="{% url 'delete_custom_object' custom_object.id %}" 
                        hx-trigger="click"
                        >
                            {% if LANGUAGE_CODE == 'ja'%}
                            削除
                            {% else %}
                            Delete
                            {% endif %}
                        </button>
                        <a data-bs-dismiss="modal" class="btn btn-primary btn-sm">
                            {% if LANGUAGE_CODE == 'ja'%}
                            キャンセル
                            {% else %}
                            Cancel
                            {% endif %}
                        </a>
                    </div>
                    
                </div>
            </div>
        </div> 

        <button 
            class="btn btn-danger btn-sm" 
            data-bs-toggle="modal" 
            data-bs-target="#records_delete_confirmation-{{custom_object.id}}"
            >
            {% if LANGUAGE_CODE == 'ja' %}
            削除
            {% else %}
            Delete
            {% endif %}
        </button>
        
    </div>
</div>

{% endif %}
{% endif %}

{% block js %}

<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>

<script>
    var search_setting_custom_object = document.querySelector("#search_setting_custom_object");
    if (search_setting_custom_object) {
        var search_setting_custom_object_tagify = new Tagify(search_setting_custom_object, {
            whitelist: [
            {% for column in column_values %}
                {% search_custom_field_object_custom_object custom_object.id column request as channel_column %}
                {% with channel_column=channel_column %}
                    { "value": "{{channel_column.name}}", "id": "{{channel_column.id}}" },
                {% endwith %}
            {% endfor %}
            ],
            dropdown: {
                maxItems: 20,           // <- mixumum allowed rendered suggestions
                classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                enabled: 0,             // <- show suggestions on focus
                closeOnSelect: true    // <- do not hide the suggestions dropdown once an item has been selected
            },
            enforceWhitelist: true,
            searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
            originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
            });
        
        {% if app_setting.search_setting_custom_object %}
            {% with app_setting.search_setting_custom_object|split:"," as search_setting_value %}
                {% for value in search_setting_value %} 
                    {% search_custom_field_object_custom_object custom_object.id value request as channel_column %}
                    {% with channel_column=channel_column %}
                        search_setting_custom_object_tagify.addTags([{ value: "{{channel_column.name}}", id: "{{channel_column.id}}" }]);
                    {% endwith %} 
                {% endfor %}
            {% endwith %}
        {% endif %}
    }
    
</script>
{% endblock js %}

{% endblock %}