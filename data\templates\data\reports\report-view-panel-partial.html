{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% for panel in panels %}
    <div id="panel-view-{{panel.id}}" class="col col-md-6 col-sm-6 col-xs-6 border rounded-0 m-4 p-4 responsive-col mx-auto text-center panel-wrapper">                    
        <div hx-get="{% host_url 'report_panel_view' report_id=report.id panel_id=panel.id host 'app' %}" hx-trigger="revealed" hx-swap="outerHTML"
            hx-indicator=".panel-load-{{panel.id}}">
            <h3><a href="{% host_url 'panel_view' panel_id=panel.id host 'app' %}">{{panel.name}}</a>
            </h3>
            <span class="spinner-border spinner-border-sm text-secondary panel-load-{{panel.id}}" style="position:relative; right:-6px" role="status">
                <span class="visually-hidden">Loading...</span>
            </span>
        </div>
    </div>
{% endfor %}
{% if has_next %}
    <div 
        id="load-more-trigger-{{next_page}}"
        class="d-none"
        hx-get="{% host_url 'panels_list_view' report_id=report.id host 'app' %}?page={{ next_page }}"
        hx-swap="outerHTML"
        hx-target="#load-more-trigger-{{next_page}}"
    >
        <div class="text-center py-3 text-muted">{% if LANGUAGE_CODE == 'ja' %}さらに読み込み中{% else %}Loading more{% endif %}...</div>
    </div>
{% endif %}

<script>
    panelsExpected = document.querySelectorAll('.panel-wrapper').length;
    panelsLoaded = 0;

    document.body.addEventListener('htmx:afterSwap', function (e) {
        const el = e.detail.elt;
        if (el && el.id && el.id.startsWith('panel-')) {
            panelsLoaded += 1;
            if (panelsLoaded == panelsExpected) {
                time = 0

                if (panelsLoaded > 2) {
                    time = 1000
                }

                setTimeout(() => {
                    {% if has_next %}
                    document.getElementById('load-more-trigger-{{next_page}}').classList.remove('d-none')
                    {% endif %}
                }, time)
            }
        }
    })

    {% if page == 1 %}
    const observer = new IntersectionObserver((entries, obs) => {
        const delay = 3000;
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const el = entry.target;
                obs.unobserve(el);

                setTimeout(() => {
                    console.log("TRIGGER NEXT PAGE")
                    htmx.ajax('GET', el.getAttribute('hx-get'), {
                        target: el.getAttribute('hx-target') || el,
                        swap: el.getAttribute('hx-swap') || 'innerHTML',
                        source: el
                    });
                }, delay);
            }
        });
    });
    {% endif %}

    document.querySelectorAll('[id^="load-more-trigger-"]').forEach(el => {
        observer.observe(el);
    });
</script>