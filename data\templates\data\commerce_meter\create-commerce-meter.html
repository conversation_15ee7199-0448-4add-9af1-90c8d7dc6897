{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<div id="create-commerce-meter" class="{% include "data/utility/drawer-header.html" %}">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if LANGUAGE_CODE == 'ja'%}
            メーターレコードの作成
            {% else %}
            Create Meter Record
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <div class="d-flex align-items-center">
                <div></div>
                
                <!-- Close Button (X) -->
                <button class="btn btn-icon btn-sm ms-2" data-kt-drawer-dismiss="true" type="button">
                    <span class="svg-icon svg-icon-2x">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor"></rect>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor"></rect>
                        </svg>
                    </span>
                </button>
                
                <script>
                    KTMenu.createInstances()
                </script>
                
            </div>
        </div>
    </div>
    <div class="card-body">

        <form method="post" action="{% url 'create_commerce_meter' %}">
            {% csrf_token %}
            {% for prop in form_properties %}
                {% if prop == 'customer' %}
                    <div class="{% include "data/utility/form-div.html" %}">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required" >
                                {% if LANGUAGE_CODE == 'ja'%}
                                顧客情報
                                {% else %}
                                Customer
                                {% endif %}
                            </span>
                            {% comment %} {% if type_association != 'create-association' %}
                                {% render_contact_company_link "commerce_subscription" %}
                            {% endif %} {% endcomment %}
                        </label>
                        
                        <div class="mb-2">
                            <select id="select-contact_and_company" name="contact_and_company" class="bg-white form-select form-select-solid border h-40px" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="連絡先もしくは企業を選択"
                                {% else %}
                                data-placeholder="Select Contact or Company"
                                {% endif %}
                                required
                            >  
                            </select>
                            
                        </div>
                        <script>
                            $(document).ready(function () {
                                $('#select-contact_and_company').select2({
                                    ajax: {
                                        delay: 250, // wait 250 milliseconds before triggering the request
                                        dataType: 'json',
                                        url: '{% host_url "get_customer_options" host "app" %}',
                                        data: function (params) {
                                                var query = {
                                                    q: params.term,
                                                    page: params.page || 1,
                                                    json_response: true,
                                                }
                                                return query;
                                            },
                                        minimumInputLength: 2,
                                    },
                                    language: {
                                        searching: function(){
                                            return "{% if LANGUAGE_CODE == 'ja'%}検索中...{% else %}Searching...{% endif %}";
                                        },
                                        noResults: function(){
                                            return "{% if LANGUAGE_CODE == 'ja'%}結果が見つかりません{% else %}No results found{% endif %}";
                                        }
                                    }
                                }).on('select2:select', function (e){
                                    var innerTextList = [];
                                    var selectElement = $(this).closest('select').get(0);
                                    selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
                                    // Loop through each list item to get the innerText and add it to the array
                                    $(`#select2-${selector.replace('#', '').replace('.', '')}-container li`).each(function() {
                                        var innerText = $(this).text().trim(); // Use trim() to remove any extra spaces
                                        innerTextList.push(innerText);
                                    });
                        
                                    if(e.params.data.text.endsWith('(Company)')|| e.params.data.text.endsWith('(企業)')){
                                        var currentValues = $(this).val();
                                        var currentObj = $(this).text().trim();
                                        innerTextList.forEach(function(innerText, index) {
                                            if (innerText.includes('(Company)') || innerText.includes('(企業)')) {
                                                currentValues.splice(index, 1);
                                            } 
                                        });
                        
                                        try {
                                            currentValues.push(e.params.data.id);
                                        } catch (error) {
                                            currentValues = e.params.data.id;
                                        }
                                        newValues = currentValues 
                        
                                        
                                        $(this).val(newValues).trigger('change');

                                        var email_field = document.getElementById('email_customer')
                                        var selectedOption = e.params.data.id;
                                        if (email_field){
                                            {% with selectedOption|get_company_obj as company %}
                                                {% if company %}
                                                    email_field.value = company.email;
                                                {% endif %}
                                            {% endwith %}
                                        }

                                    } else if(e.params.data.text.endsWith('(Contact)')|| e.params.data.text.endsWith('(連絡先)')){
                                        
                                        {% with selectedOption|get_contact_obj as contact %}
                                        var email_field = document.getElementById('email_customer')
                                        var selectedOption = e.params.data.id;
                                        console.log({{contact.email}})
                                        if (email_field){
                                            {% if contact %}
                                                email_field.value = {{contact.email}};
                                            {% endif %}
                                        }
                                        {% endwith %}
                                        var currentValues = $(this).val();
                                        var currentObj = $(this).text().trim();
                                        innerTextList.forEach(function(innerText, index) {
                                            if (innerText.includes('(Contact)') || innerText.includes('(連絡先)')) {
                                                currentValues.splice(index, 1);
                                            } 
                                        });
                        
                                        currentValues.push(e.params.data.id);
                                        newValues = currentValues 
                        
                                        $(this).val(newValues).trigger('change');
                                        
                                    } 
                                    
                        
                                })
                            });
                        </script>
                    </div>

                {% elif prop == 'item' %}
                    <div class="{% include "data/utility/form-div.html" %}">          
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required" >
                            {% if LANGUAGE_CODE == 'ja'%}
                            商品を選択
                            {% else %}
                            Choose Item
                            {% endif %}
                            </span>
                            
                        </label>
                        <select required placeholder="Choose Item" class="bg-white border min-h-40px form-select select2-items-lazy" name="item" id="commerce_meter-item-selector"
                            {% if LANGUAGE_CODE == 'ja'%}
                            data-placeholder="商品"
                            {% else %}
                            data-placeholder="Item"
                            {% endif %}
                            >
                            {% if selected_items %}
                                <option selected value="{{selected_items.0.id}}">{% get_object_display selected_items.0 'commerce_items' %}
                                </option>
                            {% endif %}
                        </select>     

                        <script>
                            $('.select2-items-lazy').select2({
                                ajax: {
                                    delay: 250, // wait 250 milliseconds before triggering the request
                                    dataType: 'json',
                                    url: '{% host_url "item_options" host "app" %}',
                                    data: function (params) {
                                            var query = {
                                                q: params.term,
                                                page: params.page || 1,
                                                purpose: "inventory",
                                                json_response: true,
                                                {% if exclude_item %}
                                                    {% autoescape off %}
                                                    get_item_choosen: '{{exclude_item|join:"',' "}}'
                                                    {% endautoescape %}
                                                {% endif %}
                                            }
                                            return query;
                                        },
                                    minimumInputLength: 2,
                                },
                                language: {
                                    "noResults": function(){
                                        return "{% if LANGUAGE_CODE == 'ja'%}商品が見つかりません{% else %}No item found{% endif %}";
                                    },
                                    searching: function(){
                                        return "{% if LANGUAGE_CODE == 'ja'%}検索中...{% else %}Searching...{% endif %}";
                                    },
                                    "loadingMore": function(){
                                        return "{% if LANGUAGE_CODE == 'ja'%}読み込み中...{% else %}Loading more...{% endif %}";
                                    },
                                },
                            }).on('select2:select', function (e) {
                                var selectedValue = $(this).val();
                            });
                        </script>
                    </div>
                {% elif prop == 'usage' %}
                    <div class="{% include "data/utility/form-div.html" %}">          
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required" >
                            {% if LANGUAGE_CODE == 'ja'%}
                            利用量
                            {% else %}
                            Usage
                            {% endif %}
                            </span>
                            
                        </label>
                        <div>
                            <input placeholder="{% if LANGUAGE_CODE == 'ja'%}利用量{% else %}Usage{% endif %}" class="form-control" type="number" name="usage" min="0" required>
                        </div>
                    </div>
                {% elif prop == 'subscription' %}
                    <div class="{% include "data/utility/form-div.html" %}">          
                        <label class="mb-2 d-flex align-items-center fs-4 fw-bold">
                            <span class="" >
                            {% if LANGUAGE_CODE == 'ja'%}
                            サブスクリプション
                            {% else %}
                            Subscription
                            {% endif %}
                            </span>
                        </label>
                        
                        <select id="subscription-association-select" placeholder="Choose subscriptions" class="border min-h-40px form-select select2-subscriptions-lazy-association" name="subscription"
                            {% if LANGUAGE_CODE == 'ja'%}
                            data-placeholder="サブスクリプション"
                            {% else %}
                            data-placeholder="Subscription"
                            {% endif %}
                            data-allow-clear="true"
                            >
                        </select>     
                    
                        <script>
                            $('.select2-subscriptions-lazy-association').select2({
                                ajax: {
                                    delay: 250, // wait 250 milliseconds before triggering the request
                                    dataType: 'json',
                                    url: '{% host_url "get_subscription_object_options" host "app" %}',
                                    data: function (params) {
                                            var query = {
                                                q: params.term,
                                                page: params.page || 1,
                                                customer_id: document.getElementById("select-contact_and_company").value,
                                                json_response: true,
                                            }
                                            return query;
                                        },
                                    minimumInputLength: 2,
                                },
                                language: {
                                    "noResults": function(){
                                        return "{% if LANGUAGE_CODE == 'ja'%}サブスクリプションが見つかりません{% else %}No subscriptions found{% endif %}";
                                    },
                                    searching: function(){
                                        return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                                    },
                                    "loadingMore": function(){
                                        return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                                    },
                                },
                            })
                        </script>
                    </div>
                    {% elif prop|is_uuid %}
                    {% for custom_prop in custom_properties %}
                        {% if custom_prop.id|stringify == prop %}
                            <div class="fv-rowd-flex flex-column mb-8">
                                
                                <div class="mb-4">
                                    
                                    <span class="{% include 'data/utility/form-label.html' %} {% if custom_prop.required_field %} required {% endif%}">
                                        {{custom_prop.name }}
                                    </span>

                                    {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=custom_prop obj="" object_type=constant.TYPE_OBJECT_COMMERCE_METER %}

                                </div>
                                
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endif %}
            {% endfor %}

            <button type="submit" class="btn btn-dark w-100">
                {% if LANGUAGE_CODE == 'ja'%}
                メーターレコードの作成
                {% else %}
                Create Meter Record
                {% endif %}
            </button>
        </form>
    </div>
</div>
