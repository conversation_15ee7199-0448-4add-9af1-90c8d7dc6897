import ast
import io
import json
import os
import re
import traceback
import uuid
from datetime import date as date_class
from datetime import datetime
from io import BytesIO, String<PERSON>
from urllib.parse import quote
from dateutil.relativedelta import relativedelta
from urllib.parse import quote, unquote
from data.commerce.bulk_csv_commerce import bulk_csv_commerce
from utils.utility import send_discord_notification
from utils.workspace import get_permission

import chardet
import pandas as pd
from bs4 import BeautifulSoup
from django.conf import settings
from django.core.mail import EmailMessage
from django.core.paginator import EmptyPage, Paginator
from django.db.models import Q
from django.forms.models import model_to_dict
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.http import require_GET
from django.views.generic import View
from django_hosts.resolvers import reverse
from xhtml2pdf import pisa
from action.action import trigger_next_action
from data.commerce import commerce_pdf_download
from data.commerce.commerce_actions import aggregate_order
from data.constants.associate_constant import *
from data.constants.commerce_constant import (APP_SETTING_CHILD_LIST,
                                             COMMERCE_APP_TARGET, DIRECTORY,
                                             NEXT_DIRECTORY)
from data.constants.constant import COUNTRY_CODE
from data.constants.date_constant import DATE_FORMAT_PARSER
from data.constants.procurement_constant import PROCUREMENT_APP_TARGET
from data.constants.properties_constant import *
from data.constants.properties_constant import (TYPE_OBJECT_COMPANY,
                                                TYPE_OBJECT_CONTACT,
                                                TYPE_OBJECT_DELIVERY_NOTE,
                                                TYPE_OBJECT_ESTIMATE,
                                                TYPE_OBJECT_INVOICE,
                                                TYPE_OBJECT_SLIP)
from data.models import (Action, ActionHistory, ActionNode, ActionTaskHistory,
                         ActionTracker, AdvanceSearchFilter, Api, AppSetting, AppSettingChild, BackgroundJob, Bill,
                         Channel, Company, CompanyNameCustomField,
                         CompanyValueCustomField, Contact, ContactsNameCustomField,
                         ContactsPlatforms, ContactsValueCustomField,
                         CustomObject, CustomObjectPropertyRow,
                         CustomProperty, DateFormat, Deals,
                         DeliverySlip, DummyUser,
                         Estimate,
                         Invoice, InvoiceItem,
                         InvoiceNameCustomField,
                         Module, Notification,
                         ObjectManager, Page2, Projects,
                         PropertySet, Receipt,
                         ShopTurboInventoryValueCustomField, ShopTurboItems,
                         ShopTurboItemsNameCustomField, ShopTurboOrders,
                         ShopTurboOrdersNameCustomField, ShopTurboShippingCost,
                         ShopTurboSubscriptions, Slip,
                         User, UserManagement, Verification, View,
                         ViewFilter, Workflow, WorkflowActionTracker, Workspace)
from data.property import properties as forward_properties
from sanka.settings import (AWS_LOCATION, AWS_STORAGE_BUCKET_NAME,
                            S3_CLIENT)
from utils.actions import transfer_output_to_target_input
from utils.contact import display_contact_name
from utils.decorator import login_or_hubspot_required
from utils.freee_bg_jobs.freee import *
from utils.items_util import handling_items
from utils.meter import MODELS_TO_STORAGE_USAGE, has_quota, sync_usage
from utils.properties.properties import (get_default_prompt_set,
                                         get_field,
                                         get_list_object_columns,
                                         get_object_display_based_columns,
                                         get_page_object,
                                         get_properties_from_set,
                                         get_properties_with_details)
from utils.search.billing_search import apply_search_setting
from utils.serializer import *
from utils.smtp import *
from utils.utility import (bg_job_to_sanka_url, customer_converter,
                           get_workspace, is_valid_email, is_valid_uuid,
                           natural_sort_key, save_custom_property, update_query_params_url,
                           get_permission_filter)

type_http = settings.SITE_URL.split("//")[0]


# Commerce app setting // Need to be decomposed
@login_or_hubspot_required
def app_settings(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    list_template = []
    property_set = None
    app_setting = AppSetting.objects.get(
        workspace=workspace, app_target=COMMERCE_APP_TARGET)

    if request.method == 'POST':
        print(request.POST)
        setting_type = request.POST.get("setting_type", "")

        page_group_type = setting_type.split('_')[0] + 's'
        if len(setting_type.split('_')) > 2:
            page_group_type = setting_type[:13]

        # NOTE: @izatou -> Hey this is not standard, I made temporary fix
        if page_group_type == 'deliverys':
            page_group_type = TYPE_OBJECT_DELIVERY_NOTE

        display = request.POST.get('display')
        if display and page_group_type:
            om, _ = ObjectManager.objects.get_or_create(
                workspace=workspace, page_group_type=page_group_type)
            om.column_display = display
            om.save()

        page_obj = get_page_object(page_group_type, lang)
        id_field = page_obj['id_field']  # id_inv, id_est, id_rcp, id_ds
        default_columns = page_obj['default_columns']
        if id_field and id_field not in default_columns:
            default_columns.insert(0, id_field)
        base_model = page_obj['base_model']  # Invoice
        custom_model = page_obj['custom_model']  # InvoiceNameCustomField etc
        field_item_name = page_obj['field_item_name']  # invoice

        tax_default = None
        if request.POST.get("app_settings", None) == "manage_app":
            property_set = request.POST.get("property_set", None)
            if property_set:
                property_set = PropertySet.objects.filter(id=property_set)
                if property_set:
                    property_set = property_set.first()
                else:
                    property_set = None
            tax_default = request.POST.get("tax_default", 0)
            if not tax_default:
                tax_default = 0

            selected_columns = ""
            selected_columns_display = ""
            selected_header = ""
            selected_header_display = ""
            if request.POST.get("selected_columns[]", None):
                selected_columns = ','.join(
                    request.POST.getlist("selected_columns[]", []))

            if len(request.POST.getlist("selected_columns_display[]", [])) > 0:
                selected_columns_display = ','.join(
                    request.POST.getlist("selected_columns_display[]", []))

            if request.POST.get("header_block[]", None):
                selected_header = ','.join(
                    request.POST.getlist("header_block[]", []))

            if len(request.POST.getlist("header_block_display[]", [])) > 0:
                selected_header_display = ','.join(
                    request.POST.getlist("header_block_display[]", []))

            table_line_item_font_size = request.POST.get(
                'selected_font_line_item_size', '')
            if table_line_item_font_size and 'px' not in table_line_item_font_size:
                table_line_item_font_size = f"{table_line_item_font_size}px"

            # Catch from front end
            APP_SETTING_CHILD_LIST['_tax'] = tax_default
            APP_SETTING_CHILD_LIST['_send_from'] = request.POST.get(
                "send_from_default", "")
            APP_SETTING_CHILD_LIST['_note'] = request.POST.get(
                "default_note", None)
            APP_SETTING_CHILD_LIST['_email_body'] = request.POST.get(
                "default_email_content", None)
            APP_SETTING_CHILD_LIST['_pdf_title'] = request.POST.get(
                "pdf_title", "")
            APP_SETTING_CHILD_LIST['_is_stamp'] = request.POST.get(
                "sw_stamp", False)
            APP_SETTING_CHILD_LIST['_pdf_template'] = request.POST.get(
                "selected_template", None)
            APP_SETTING_CHILD_LIST['_pdf_line_item_table'] = selected_columns
            APP_SETTING_CHILD_LIST['_pdf_line_item_table_display'] = selected_columns_display
            APP_SETTING_CHILD_LIST['_pdf_font_type'] = request.POST.get(
                "selected_font_type", None)
            APP_SETTING_CHILD_LIST['_pdf_color'] = request.POST.get(
                "selected_color", None)
            APP_SETTING_CHILD_LIST['_slip_type'] = request.POST.get(
                "slip_type", None)
            APP_SETTING_CHILD_LIST['_search_setting'] = request.POST.get(
                "search_setting", "unset")
            APP_SETTING_CHILD_LIST['_pdf_header_block'] = selected_header
            APP_SETTING_CHILD_LIST['_pdf_header_block_display'] = selected_header_display
            APP_SETTING_CHILD_LIST['_pdf_payment_block'] = request.POST.get(
                'payment_block', '')
            APP_SETTING_CHILD_LIST['_pdf_payment_block_display'] = request.POST.get(
                'payment_block_display', '')
            APP_SETTING_CHILD_LIST['_pdf_send_from_block'] = request.POST.get(
                'send_from_block', '')
            APP_SETTING_CHILD_LIST['_pdf_send_from_block_display'] = request.POST.get(
                'send_from_block_display', '')
            APP_SETTING_CHILD_LIST['_pdf_send_to_block'] = request.POST.get(
                'send_to_block', '')
            APP_SETTING_CHILD_LIST['_pdf_send_to_block_display'] = request.POST.get(
                'send_to_block_display', '')
            APP_SETTING_CHILD_LIST['_pdf_notes_block'] = request.POST.get(
                'notes_block', '')
            APP_SETTING_CHILD_LIST['_pdf_notes_block_display'] = request.POST.get(
                'notes_block_display', '')
            APP_SETTING_CHILD_LIST['_pdf_footer_template'] = request.POST.get(
                'footer_template', '')
            APP_SETTING_CHILD_LIST['_pdf_line_item_table_bg_color'] = request.POST.get(
                'selected_bg_line_item_color', '')
            APP_SETTING_CHILD_LIST['_pdf_line_item_table_font_color'] = request.POST.get(
                'selected_font_line_item_color', '')
            APP_SETTING_CHILD_LIST['_pdf_line_item_table_font_size'] = table_line_item_font_size
            APP_SETTING_CHILD_LIST['_pdf_user_block'] = request.POST.get(
                'user_block', '')
            APP_SETTING_CHILD_LIST['_pdf_user_block_display'] = request.POST.get(
                'user_block_display', '')

            # Default of search variable will be deleted after update the setting
            APP_SETTING_CHILD_LIST['_search_setting'] = APP_SETTING_CHILD_LIST['_search_setting'].replace(
                'id_predefined,', '')

            # If it only Id , will remove it as blank string
            if APP_SETTING_CHILD_LIST['_search_setting'] == "id_predefined":
                APP_SETTING_CHILD_LIST['_search_setting'] = "unset"

            if "change-stamp-image" in request.POST or "delete-stamp-image" in request.POST or 'get-image-file' in request.POST:
                image_file = None
                app_setting_child = AppSettingChild.objects.filter(
                    app_setting=app_setting, target_name=setting_type, property_set=property_set).first()
                if not app_setting_child:
                    app_setting_child = AppSettingChild.objects.create(
                        app_setting=app_setting, target_name=setting_type, property_set=property_set)
                image_file = app_setting_child.file
                folder = "appsetting-customfield-files"

                if "delete-stamp-image" in request.POST:
                    try:
                        if "nyc3.digitaloceanspaces.com" in image_file.url:
                            file_name = image_file.url.split("/")[-1]
                            file_name = f"{folder}/{file_name}"
                            sc_file = image_file
                            S3_CLIENT.delete_object(
                                Bucket=AWS_STORAGE_BUCKET_NAME,  Key=f"{AWS_LOCATION}/{sc_file.file}")
                            setattr(app_setting_child, 'file', None)
                        app_setting_child.save()
                    except:
                        pass

                elif "change-stamp-image" in request.POST:
                    files = request.FILES.getlist('file-change-profile-pic')
                    if files:
                        for file in files:
                            file.name = str(uuid.uuid4()) + \
                                '.' + file.name.split('.')[-1]
                            app_setting_child = AppSettingChild.objects.filter(
                                app_setting=app_setting, target_name=setting_type, property_set=property_set).first()
                            if not app_setting_child:
                                app_setting_child = AppSettingChild.objects.create(
                                    app_setting=app_setting, target_name=setting_type, property_set=property_set)
                            app_setting_child.file = file
                            app_setting_child.save()
                            break

                try:
                    image_file = None
                    app_setting_child = AppSettingChild.objects.filter(
                        app_setting=app_setting, target_name=setting_type, property_set=property_set).first()
                    if not app_setting_child:
                        app_setting_child = AppSettingChild.objects.create(
                            app_setting=app_setting, target_name=setting_type, property_set=property_set)
                    image_file = app_setting_child.file
                except Exception as e:
                    print(e)
                    image_file = None

                context = {
                    'image_file': image_file,
                    'setting_type': setting_type,
                    'target_post_url': "commerce_settings",
                    'property_set': property_set,

                    'object_type': COMMERCE_APP_TARGET
                }
                response = render(
                    request, 'data/partials/partial-stamp.html', context)
                return response

            print("SAVING: ", APP_SETTING_CHILD_LIST)
            for setting_ in APP_SETTING_CHILD_LIST:
                # NOTE: Got Error when save `no attribute '_meta'`
                try:
                    app_setting_child = AppSettingChild.objects.filter(
                        app_setting=app_setting, target_name=setting_type + setting_, property_set=property_set).first()
                    if not app_setting_child:
                        app_setting_child = AppSettingChild.objects.create(
                            app_setting=app_setting, target_name=setting_type + setting_, property_set=property_set)
                    app_setting_child.value = APP_SETTING_CHILD_LIST[setting_]
                    app_setting_child.save()
                except Exception as e:
                    print("Error in saving AppSettingChild: ", e)

            # ======= Manage Shipping ======
            shipping_field_id = request.POST.getlist(
                "shipping_field_id", [])
            shipping_field_name = request.POST.getlist(
                "shipping_field_name", [])
            shipping_field_value = request.POST.getlist(
                "shipping_field_value", [])
            shipping_field_number_format = request.POST.getlist(
                "shipping_field_number_format", [])

            if shipping_field_name == '':
                shipping_field_name = []
            if len(shipping_field_name) > 0:
                if shipping_field_name[0] == '':
                    shipping_field_name = []

            shipping_active = []
            for idx, shipping_name in enumerate(shipping_field_name):
                if shipping_field_id[idx]:
                    shopturbo_shipping = ShopTurboShippingCost.objects.get(
                        id=shipping_field_id[idx])
                    shopturbo_shipping.name = shipping_name
                    shopturbo_shipping.value = shipping_field_value[idx]
                    shopturbo_shipping.number_format = shipping_field_number_format[idx]
                    shopturbo_shipping.save()
                else:
                    shopturbo_shipping, _ = ShopTurboShippingCost.objects.get_or_create(
                        workspace=workspace, name=shipping_name)
                    shopturbo_shipping.value = shipping_field_value[idx]
                    shopturbo_shipping.number_format = shipping_field_number_format[idx]
                    shopturbo_shipping.save()

                shipping_active.append(shopturbo_shipping.id)

            if shipping_active:
                ShopTurboShippingCost.objects.filter(
                    workspace=workspace).exclude(id__in=shipping_active).delete()
            else:
                ShopTurboShippingCost.objects.filter(
                    workspace=workspace).delete()

        elif request.POST.get("app_settings", None) == "manage_app_access":  # Not used??
            users = request.POST.getlist('users', [])
            groups = request.POST.getlist('groups', [])

            app_setting = AppSetting.objects.get(
                workspace=workspace, app_target=COMMERCE_APP_TARGET)
            app_setting.user.clear()
            app_setting.group.clear()

            selected_users = User.objects.filter(id__in=users)
            if selected_users:
                for selected_user in selected_users:
                    app_setting.user.add(selected_user)

            selected_groups = Group.objects.filter(id__in=groups)
            if selected_groups:
                for selected_group in selected_groups:
                    app_setting.group.add(selected_group)

            app_setting.save()

        return forward_properties(request)

    # GET
    role = UserManagement.objects.get(user=request.user, workspace=workspace)
    groups = Group.objects.filter(workspace=workspace)

    if 'page_group_type' in request.GET:
        page_group_type = request.GET.get('page_group_type')
    else:
        page_group_type = None

    if 'setting_type' in request.GET:
        setting_type = request.GET.get('setting_type')
        property_set = request.GET.get("property_set", None)
        if property_set:
            property_set = PropertySet.objects.filter(id=property_set)
            if property_set:
                property_set = property_set.first()
            else:
                property_set = None
        if not page_group_type:
            try:
                page_group_type = setting_type + 's'
            except:
                page_group_type = setting_type.split('-')[1] + 's'

        if 'get-image-file' in request.GET:
            image_file = None
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name=setting_type, property_set=property_set).first()
            if not app_setting_child:
                app_setting_child = AppSettingChild.objects.create(
                    app_setting=app_setting, target_name=setting_type, property_set=property_set)
            image_file = app_setting_child.file
            folder = "appsetting-customfield-files"

            try:
                image_file = None
                app_setting_child = AppSettingChild.objects.filter(
                    app_setting=app_setting, target_name=setting_type, property_set=property_set).first()
                if not app_setting_child:
                    app_setting_child = AppSettingChild.objects.create(
                        app_setting=app_setting, target_name=setting_type, property_set=property_set)
                image_file = app_setting_child.file
            except Exception as e:
                print(e)
                image_file = None

            context = {
                'image_file': image_file,
                'setting_type': setting_type,
                'target_post_url': "commerce_settings",
                'property_set': property_set,

                'object_type': COMMERCE_APP_TARGET
            }
            response = render(
                request, 'data/partials/partial-stamp.html', context)
            return response

        org_page_group_type = page_group_type
        page_obj = get_page_object(page_group_type, lang)
        id_field = page_obj['id_field']  # id_inv, id_est, id_rcp, id_ds
        default_columns = page_obj['default_columns']
        if id_field and id_field not in default_columns:
            default_columns.insert(0, id_field)
        base_model = page_obj['base_model']  # Invoice
        custom_model = page_obj['custom_model']  # InvoiceNameCustomField etc
        field_item_name = page_obj['field_item_name']  # invoice

        page_group_type = setting_type.split('_')[0]
        if len(setting_type.split('_')) > 1:
            page_group_type = setting_type[:13]

        column_values = []
        NameCustomField = None
        NameCustomField = custom_model.objects.filter(
            workspace=workspace).order_by("order")

        # Loop through all files in the directory
        for filename in os.listdir(DIRECTORY):
            # Check if the file is an HTML file and contains 'pdf_pattern' in its name
            if filename.endswith('.html') and field_item_name + 'PDF-pattern' == filename[:-7]:
                list_template.append(NEXT_DIRECTORY + filename)

        list_template = sorted(list_template, key=natural_sort_key)
        properties = get_properties_with_details(
            org_page_group_type, workspace, lang, searchable_only=True)
        column_values = [str(p['id']) for p in properties]

        contact_custom_fields = ContactsNameCustomField.objects.filter(
            workspace=workspace, type='text')
        for cf in contact_custom_fields:
            column_values.append(
                'contact_custom_field_relations__value__|'+str(cf.id))

        custom_fields = CompanyNameCustomField.objects.filter(
            workspace=workspace, type='text')
        for cf in custom_fields:
            column_values.append(
                'company_custom_field_relations__value__|'+str(cf.id))

        customer_company_custom_fields = InvoiceNameCustomField.objects.filter(
            workspace=workspace, type__in=['contact', 'company'])
        if customer_company_custom_fields:
            column_values.extend([str(cf.id)
                                 for cf in customer_company_custom_fields])

        if request.user not in app_setting.user.all():
            app_setting.user.add(request.user)
            app_setting.save()

        app_setting_child = AppSettingChild.objects.filter(
            app_setting=app_setting, target_name=setting_type + "_search_setting", property_set=property_set).first()
        if not app_setting_child:
            app_setting_child = AppSettingChild.objects.create(
                app_setting=app_setting, target_name=setting_type + "_search_setting", property_set=property_set)
        print("Search: ", app_setting_child.value)
        if not app_setting_child.value:
            search_predefined = "customers,start_date,status,total_price"
            if 'invoice' in setting_type.lower():
                search_predefined += 'due_date'
            app_setting_child.value = search_predefined
            app_setting_child.save()

        customize_template = CustomizePdfTemplate.objects.filter(
            setting_type=page_group_type, workspace=workspace)

    COMMERCE_STATUS = None
    SLIP_TYPE = None

    if page_group_type == TYPE_OBJECT_SLIP:
        SLIP_TYPE = base_model._meta.get_field('slip_type').choices
    else:
        COMMERCE_STATUS = base_model._meta.get_field('status').choices

    line_item_table_column = LINE_ITEM_DEFAULT
    custom_fields = ShopTurboItemsNameCustomField.objects.filter(
        workspace=workspace, type__in=['text', 'text-area', 'image', 'number', 'choice', 'svg'])

    for custom_field in custom_fields:
        line_item_table_column[str(custom_field.id)] = {
            'en': custom_field.name,
            'ja': custom_field.name,
        }

    preselected_col = ""
    preselected_col_display = ""
    preselected_header_block = ""
    preselected_header_block_display = ""
    preselected_payment_block = ""
    preselected_payment_block_display = ""
    preselected_send_from_block = ""
    preselected_send_from_block_display = ""
    preselected_send_to_block = ""
    preselected_send_to_block_display = ""
    preselected_ship_to_block = ""
    preselected_ship_to_block_display = ""
    preselected_notes_block = ""
    preselected_notes_block_display = ""
    preselected_user_block = ""
    preselected_user_block_display = ""
    preselected_footer_template = ""
    pdf_title = ""
    default_line_item_bg_color = ""
    default_line_item_font_color = ""
    default_line_item_font_size = ""
    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_line_item_table", property_set=property_set).first()
    if app_setting_child:
        preselected_col = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_line_item_table_display", property_set=property_set).first()
    if app_setting_child:
        preselected_col_display = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_header_block", property_set=property_set).first()
    if app_setting_child:
        preselected_header_block = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_header_block_display", property_set=property_set).first()
    if app_setting_child:
        preselected_header_block_display = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_payment_block", property_set=property_set).first()
    if app_setting_child:
        preselected_payment_block = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_payment_block_display", property_set=property_set).first()
    if app_setting_child:
        preselected_payment_block_display = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_send_from_block", property_set=property_set).first()
    if app_setting_child:
        preselected_send_from_block = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_send_from_block_display", property_set=property_set).first()
    if app_setting_child:
        preselected_send_from_block_display = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_send_to_block", property_set=property_set).first()
    if app_setting_child:
        preselected_send_to_block = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_send_to_block_display", property_set=property_set).first()
    if app_setting_child:
        preselected_send_to_block_display = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_ship_to_block", property_set=property_set).first()
    if app_setting_child:
        preselected_ship_to_block = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_ship_to_block_display", property_set=property_set).first()
    if app_setting_child:
        preselected_ship_to_block_display = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_notes_block", property_set=property_set).first()
    if app_setting_child:
        preselected_notes_block = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_notes_block_display", property_set=property_set).first()
    if app_setting_child:
        preselected_notes_block_display = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_notes_block", property_set=property_set).first()
    if app_setting_child:
        preselected_notes_block = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_notes_block_display", property_set=property_set).first()
    if app_setting_child:
        preselected_notes_block_display = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_footer_template", property_set=property_set).first()
    if app_setting_child:
        preselected_footer_template = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_title", property_set=property_set).first()
    if app_setting_child:
        pdf_title = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_line_item_table_bg_color", property_set=property_set).first()
    if app_setting_child:
        default_line_item_bg_color = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_line_item_table_font_color", property_set=property_set).first()
    if app_setting_child:
        default_line_item_font_color = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_line_item_table_font_size", property_set=property_set).first()
    if app_setting_child:
        default_line_item_font_size = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_user_block", property_set=property_set).first()
    if app_setting_child:
        preselected_user_block = app_setting_child.value

    app_setting_child = AppSettingChild.objects.filter(
        app_setting=app_setting, target_name=setting_type + "_pdf_user_block_display", property_set=property_set).first()
    if app_setting_child:
        preselected_user_block_display = app_setting_child.value

    temp = setting_type
    if temp in ['invoice', 'estimate', 'receipt', 'delivery_slip', 'slip']:
        temp = f"{temp}s"
    obj = get_page_object(temp)
    base_model = obj['base_model']
    custom_model = obj['custom_model']
    columns_display = obj['columns_display']
    # related_models = obj.get('related_data', None)
    related_models = []
    header_blocks = {}

    for field in base_model._meta.fields:
        if field.is_relation or field.name in ['id']:
            if field.is_relation or field.name in ['id']:
                if field.name in ['id', 'contact', 'company', 'workspace']:
                    continue

                related_model = field.related_model
                related_fields = [f.name for f in related_model._meta.get_fields(
                ) if not f.is_relation or f.one_to_one or f.many_to_one]

                for related_field in related_fields:
                    if related_field in ['id']:
                        continue

                    value = f"{field.name}__{related_field}"

                    trans = columns_display.get(related_field)
                    display_en = trans.get('en') if trans else value
                    display_ja = trans.get('ja') if trans else value

                    header_blocks[value] = {
                        'en': f"{field.name} - {display_en}",
                        'ja': f"{field.name} - {display_ja}"
                    }

        value = field.name

        trans = columns_display.get(field.name)

        display_en = trans.get('en') if trans else value
        display_ja = trans.get('ja') if trans else value

        header_blocks[value] = {
            'en': display_en,
            'ja': display_ja,
        }

    temp_header_block = ['contact__'+val for val in get_field(
        Contact, get_list_object_columns(Contact, excludes=['created_at', 'url']), ['CharField'])]
    temp_header_block.append("contact__first_name")
    if 'contact__company' in temp_header_block:
        temp_header_block.remove('contact__company')
    if 'contact__image_url' in temp_header_block:
        temp_header_block.remove('contact__image_url')

    temp_header_block = ['company__'+val for val in get_field(
        Company, get_list_object_columns(Company, excludes=['created_at']), ['CharField'])]
    trans_contact = SEARCH_COLUMNS_DISPLAY
    for hb in temp_header_block:
        trans = trans_contact.get(hb)
        display_en = trans.get('en') if trans else hb
        display_ja = trans.get('ja') if trans else hb

        header_blocks[hb] = {
            'en': display_en,
            'ja': display_ja,
        }

    contact_custom_fields = ContactsNameCustomField.objects.filter(
        workspace=workspace, type='text')
    for cf in contact_custom_fields:
        trans = trans_contact.get('contact')
        display_en = trans.get('en') if trans else ""
        display_ja = trans.get('ja') if trans else ""
        header_blocks[f"contact__custom_field|{cf.id}"] = {
            'en': f"{display_en} - {cf.name}",
            'ja': f"{display_ja} - {cf.name}",
        }

    custom_fields = CompanyNameCustomField.objects.filter(
        workspace=workspace, type='text')
    for cf in custom_fields:
        trans = trans_contact.get('company')
        display_en = trans.get('en') if trans else ""
        display_ja = trans.get('ja') if trans else ""
        header_blocks[f"company__custom_field|{cf.id}"] = {
            'en': f"{display_en} - {cf.name}",
            'ja': f"{display_ja} - {cf.name}",
        }

    custom_field_hierarchy = CompanyNameCustomField.objects.filter(
        workspace=workspace, type='hierarchy')
    for cf in custom_field_hierarchy:
        trans = trans_contact.get('company')
        display_en = trans.get('en') if trans else ""
        display_ja = trans.get('ja') if trans else ""
        fields_hierarchy = ast.literal_eval(cf.choice_value)
        for f_ in fields_hierarchy:
            for hb in temp_header_block:
                trans = trans_contact.get(hb)
                display_en_field = trans.get('en') if trans else hb
                display_ja_field = trans.get('ja') if trans else hb

                header_blocks[f"company__custom_field|{cf.id}__{f_['label']}__{hb}"] = {
                    'en': f"{cf.name}({f_['label']}) - {display_en_field}",
                    'ja': f"{cf.name}({f_['label']}) - {display_ja_field}",
                }

    if custom_model:
        for field in custom_model.objects.filter(workspace=workspace, type__in=['text', 'text-area', 'image', 'number', 'choice', 'svg', 'date', 'datetime', 'date_time', 'date_range', 'formula']):
            if field.name:
                header_blocks[f"custom_property__{field.id}"] = {
                    'en': field.name,
                    'ja': field.name,
                }

        custom_prop_model = custom_model.objects.filter(workspace=workspace, type__in=[
                                                        'bill_objects', 'invoice_objects', 'order_objects', 'contact', 'company', 'purchase_order', 'subscription'])

        for associate_obj in custom_prop_model:
            obj_ = associate_obj.name
            if associate_obj.type == 'bill_objects':
                associate_model = Bill
                assc_col_display = DISPLAY_COLUMNS_BILL.copy()
                obj_singular = 'billing'
            elif associate_obj.type == 'invoice_objects':
                associate_model = Invoice
                assc_col_display = INVOICE_COLUMNS_DISPLAY.copy()
                obj_singular = 'invoices'
            elif associate_obj.type == 'order_objects':
                associate_model = ShopTurboOrders
                assc_col_display = ORDERS_COLUMNS_DISPLAY.copy()
                obj_singular = 'commerce_orders'
            elif associate_obj.type == 'contact':
                associate_model = Contact
                assc_col_display = CONTACTS_COLUMNS_DISPLAY.copy()
                obj_singular = 'contacts'
            elif associate_obj.type == 'company':
                associate_model = Company
                assc_col_display = COMPANY_COLUMNS_DISPLAY.copy()
                obj_singular = 'company'
            elif associate_obj.type == 'purchase_order':
                associate_model = PurchaseOrders
                assc_col_display = PURCHASE_ORDER_COLUMNS_DISPLAY.copy()
                obj_singular = 'purchaseorder'
            elif associate_obj.type == 'subscription':
                associate_model = ShopTurboSubscriptions
                assc_col_display = SUBSCRIPTIONS_COLUMNS_DISPLAY.copy()
                obj_singular = 'commerce_subscription'
            else:
                continue
            for field in associate_model._meta.fields:
                if field.is_relation or field.name in ['id']:
                    continue
                else:
                    value = f"custom_property__{associate_obj.id}__{field.name}"
                    trans = assc_col_display.get(field.name)

                    display_en = trans.get('en') if trans else field.name
                    display_ja = trans.get('ja') if trans else field.name

                    header_blocks[value] = {
                        'en': f"{obj_} - {display_en}",
                        'ja': f"{obj_} - {display_ja}",
                    }
    header_blocks['customer__name'] = {
        'en': "Customer - Name",
        'ja': "顧客 - 名"
    }

    tmp_setting_type = setting_type
    if tmp_setting_type in ['invoice', 'estimate', 'receipt', 'delivery_slip', 'slip']:
        tmp_setting_type = f"{tmp_setting_type}s"

    list_custom_pdf = []
    if tmp_setting_type in [TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_INVOICE, TYPE_OBJECT_SLIP]:
        list_template = []
        list_custom_pdf = PdfTemplate.objects.filter(master_pdf__object_type=tmp_setting_type, workspace=workspace).order_by(
            'master_pdf__name_en', 'master_pdf__name_ja')
    context = {
        'column_values': column_values,
        'object_type': COMMERCE_APP_TARGET,
        'role': role,
        'groups': groups,
        'app_setting': app_setting,
        'setting_type': setting_type,
        'NameCustomField': NameCustomField,
        'number_formats': SHOPTURBO_NUMBER_FORMAT,
        'list_template': list_template,
        'list_customize_template': customize_template,
        'InvoiceProperties': ['total_price', 'total_price_without_tax'],
        'COMMERCE_STATUS': COMMERCE_STATUS,
        'SLIP_TYPE': SLIP_TYPE,
        'properties_types': OBJECTS_CF_TYPE,
        'line_item_table_column': line_item_table_column,
        'preselected_col': preselected_col,
        'preselected_col_display': preselected_col_display,
        'preselected_header_block': preselected_header_block,
        'preselected_header_block_display': preselected_header_block_display,
        'preselected_payment_block': preselected_payment_block,
        'preselected_payment_block_display': preselected_payment_block_display,
        'preselected_send_from_block': preselected_send_from_block,
        'preselected_send_from_block_display': preselected_send_from_block_display,
        'preselected_send_to_block': preselected_send_to_block,
        'preselected_send_to_block_display': preselected_send_to_block_display,
        'preselected_ship_to_block': preselected_ship_to_block,
        'preselected_ship_to_block_display': preselected_ship_to_block_display,
        'preselected_notes_block': preselected_notes_block,
        'preselected_notes_block_display': preselected_notes_block_display,
        'preselected_notes_block': preselected_notes_block,
        'preselected_notes_block_display': preselected_notes_block_display,
        'preselected_footer_template': preselected_footer_template,
        'preselected_user_block': preselected_user_block,
        'preselected_user_block_display': preselected_user_block_display,
        'default_line_item_bg_color': default_line_item_bg_color,
        'default_line_item_font_color': default_line_item_font_color,
        'default_line_item_font_size': default_line_item_font_size,
        'header_blocks': header_blocks,
        'pdf_title': pdf_title if pdf_title else "",
        'list_custom_pdf': list_custom_pdf,
    }

    try:
        if property_set:
            context["property_set"] = str(property_set.id)
        for setting_ in APP_SETTING_CHILD_LIST:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name=setting_type + setting_, property_set=property_set).first()
            if not app_setting_child:
                app_setting_child = AppSettingChild.objects.create(
                    app_setting=app_setting, target_name=setting_type + setting_, property_set=property_set)
            setting_ = 'default' + setting_
            if app_setting_child.value:
                context[setting_] = app_setting_child.value
            else:
                context[setting_] = ''
    except Exception as e:
        print("Context Error at: ", e)

    return render(request, 'data/commerce/manage-commerce-app.html', context)


def commerce_app_settings_child_apply_to_others(request):

    if request.method == 'GET':

        # temporary
        lang = request.LANGUAGE_CODE
        button_type = request.GET.get('button_type')
        page_group_type = request.GET.get('page_group_type')
        page_obj = get_page_object(page_group_type, lang)
        page_title = page_obj['page_title']  # Invoices etc

        context = {'page_group_type': page_group_type,
                   'page_title': page_title, 'button_type': button_type}
        return render(request, 'data/common/app_settings/checkbox-apply-to-others.html', context)

    else:

        lang = request.LANGUAGE_CODE
        workspace = get_workspace(request.user)

        page_group_type = request.POST.get('page_group_type')
        button_type = request.POST.get('button_type', None)
        page_obj = get_page_object(page_group_type, lang)

        setting_type_list = []
        if page_group_type.endswith('s'):
            setting_type = page_group_type[:-1]
        else:
            setting_type = page_obj['setting_type']
        setting_url = page_obj['setting_url']

        app_setting = AppSetting.objects.get(
            workspace=workspace, app_target=COMMERCE_APP_TARGET)
        if page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
            app_setting, _ = AppSetting.objects.get_or_create(
                workspace=workspace, app_target=PROCUREMENT_APP_TARGET)
            setting_type = 'purchase_order'
        setting_type_list.append(setting_type)

        if 'estimates' in request.POST and 'estimate' not in setting_type_list:
            setting_type_list.append('estimate')
        if 'delivery_slips' in request.POST and 'delivery_slip' not in setting_type_list:
            setting_type_list.append('delivery_slip')
        if 'invoices' in request.POST and 'invoice' not in setting_type_list:
            setting_type_list.append('invoice')
        if 'receipts' in request.POST and 'receipt' not in setting_type_list:
            setting_type_list.append('receipt')
        if 'slips' in request.POST and 'slip' not in setting_type_list:
            setting_type_list.append('slip')

        if 'purchaseorder' in request.POST and 'purchase_order' not in setting_type_list:
            app_setting, _ = AppSetting.objects.get_or_create(
                workspace=workspace, app_target=PROCUREMENT_APP_TARGET)
            setting_type = 'purchase_order'
            setting_type_list.append('purchase_order')
        if 'commerceorder' in request.POST and 'commerce_order' not in setting_type_list:
            app_setting, _ = AppSetting.objects.get_or_create(
                workspace=workspace, app_target='shopturbo')
            setting_type = 'order'
            setting_type_list.append('order')
        for setting_type in setting_type_list:
            if not setting_type == 'purchase_order':

                if button_type == 'default-tax-rate':
                    APP_SETTING_CHILD_LIST['_tax'] = request.POST.get(
                        "tax_default", 0) if request.POST.get("tax_default", 0) else 0
                elif button_type == 'send-from-info':
                    APP_SETTING_CHILD_LIST['_send_from'] = request.POST.get(
                        "send_from_default", "")
                elif button_type == 'default-notes':
                    APP_SETTING_CHILD_LIST['_note'] = request.POST.get(
                        "default_note", None)
                elif button_type == 'table-line-item-column':
                    APP_SETTING_CHILD_LIST['_pdf_line_item_table'] = request.POST.get(
                        'selected_col_default', '')
                    APP_SETTING_CHILD_LIST['_pdf_line_item_table_display'] = request.POST.get(
                        'selected_col_display_default', '')
                elif button_type == 'company-logo':
                    if setting_type_list[0] == 'purchase_order':
                        APP_SETTING_CHILD_LIST['_logo'] = getattr(
                            app_setting, setting_type_list[0] + setting_ + "_file")
                    else:
                        app_setting_child = AppSettingChild.objects.filter(
                            app_setting=app_setting, target_name=setting_type_list[0] + '_logo').first()
                        APP_SETTING_CHILD_LIST['_logo'] = app_setting_child.file

                elif button_type == 'company-signature':
                    if setting_type_list[0] == 'purchase_order':
                        APP_SETTING_CHILD_LIST['_stamp'] = getattr(
                            app_setting, setting_type_list[0] + setting_ + "_file")
                    else:
                        app_setting_child = AppSettingChild.objects.filter(
                            app_setting=app_setting, target_name=setting_type_list[0] + '_stamp').first()
                        APP_SETTING_CHILD_LIST['_stamp'] = app_setting_child.file

                # print("SAVING: ",APP_SETTING_CHILD_LIST)
                for setting_ in APP_SETTING_CHILD_LIST:
                    if setting_ in ['_pdf_template', '_search_setting']:
                        continue

                    if button_type:
                        if button_type == 'default-tax-rate':
                            if setting_ != '_tax':
                                continue
                        elif button_type == 'send-from-info':
                            if setting_ != '_send_from':
                                continue
                        elif button_type == 'default-notes':
                            if setting_ != '_note':
                                continue
                        elif button_type == 'table-line-item-column':
                            if setting_ not in ['_pdf_line_item_table', '_pdf_line_item_table_display']:
                                continue
                        elif button_type == 'company-logo':
                            if setting_ != '_logo':
                                continue
                        elif button_type == 'company-signature':
                            if setting_ != '_stamp':
                                continue
                        else:
                            continue
                    # NOTE: Got Error when save `no attribute '_meta'`
                    try:
                        app_setting_child = AppSettingChild.objects.filter(
                            app_setting=app_setting, target_name=setting_type + setting_).first()
                        if not app_setting_child:
                            app_setting_child = AppSettingChild.objects.create(
                                app_setting=app_setting, target_name=setting_type + setting_)

                        if setting_ == '_logo' or setting_ == '_stamp':
                            app_setting_child.file = APP_SETTING_CHILD_LIST[setting_]
                        else:
                            app_setting_child.value = APP_SETTING_CHILD_LIST[setting_]
                        app_setting_child.save()

                        print("app_setting_child: ", app_setting_child)
                    except Exception as e:
                        print("Error in saving AppSettingChild: ", e)

            elif setting_type == 'purchase_order':
                for setting_ in APP_SETTING_CHILD_LIST:
                    if 'tax' in setting_:
                        tax = request.POST.get("tax_default", 0) if request.POST.get(
                            "tax_default", 0) else 0
                        print(setting_, " 1. tax: ", tax)
                        if tax:
                            setattr(app_setting, setting_type + setting_, tax)
                    elif 'send_from' in setting_:
                        send_from = request.POST.get("send_from_default", "")
                        print(setting_, "1. send_from: ", send_from)
                        if send_from:
                            setattr(app_setting, setting_type +
                                    setting_, send_from)
                    elif 'note' in setting_:
                        default_note = request.POST.get("default_note", None)
                        print(setting_, "1. note: ", send_from)
                        if default_note:
                            setattr(app_setting, setting_type +
                                    setting_, default_note)
                    elif 'pdf_line_item_table_display' in setting_:
                        default_line_item_table_display = request.POST.get(
                            "selected_col_display_default", None)
                        if default_line_item_table_display:
                            setattr(app_setting, setting_type + setting_,
                                    default_line_item_table_display)
                    elif 'pdf_line_item_table' in setting_:
                        default_line_item_table = request.POST.get(
                            "selected_col_default", None)
                        if default_line_item_table:
                            setattr(app_setting, setting_type +
                                    setting_, default_line_item_table)

                    elif 'logo' in setting_:

                        app_setting_child = AppSettingChild.objects.filter(
                            app_setting=app_setting, target_name=setting_type_list[0] + '_logo').first()

                        if app_setting_child:
                            logo_file = app_setting_child.file
                            if logo_file:
                                setattr(app_setting, setting_type +
                                        setting_ + "_file", logo_file)

                    elif 'stamp' in setting_:

                        app_setting_child = AppSettingChild.objects.filter(
                            app_setting=app_setting, target_name=setting_type_list[0] + '_stamp').first()

                        if app_setting_child:
                            stamp_file = app_setting_child.file
                            print("stamp_file: ", stamp_file)
                            print(setting_type + setting_ + "_file")
                            if stamp_file:
                                setattr(app_setting, setting_type +
                                        setting_ + "_file", stamp_file)

                    app_setting.save()
            elif setting_type == 'order':
                for setting_ in APP_SETTING_CHILD_LIST:
                    if 'pdf_line_item_table_display' in setting_:
                        default_line_item_table_display = request.POST.get(
                            "selected_col_display_default", None)
                        if default_line_item_table_display:
                            setattr(app_setting, setting_type + setting_,
                                    default_line_item_table_display)
                    elif 'pdf_line_item_table' in setting_:
                        default_line_item_table = request.POST.get(
                            "selected_col_default", None)
                        if default_line_item_table:
                            setattr(app_setting, setting_type +
                                    setting_, default_line_item_table)
                    app_setting.save()

        setting_type = page_obj['setting_type']
        query_string = f'?setting_type={setting_type}'
        return redirect(reverse(f'{setting_url}', host='app') + query_string)


@login_or_hubspot_required
def commerce_send_mail(request, id, object_type=None):
    workspace = get_workspace(request.user)

    email = request.POST.get('email', '')
    cc_list = request.POST.get('cc_list', '')

    lang = request.LANGUAGE_CODE
    page_obj = get_page_object(object_type, lang)
    id_field = page_obj['id_field']  # id_inv, id_est, id_rcp, id_ds
    page_title = page_obj['page_title']  # Invoices etc
    columns_display = page_obj['columns_display']
    default_columns = page_obj['default_columns']
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj['base_model']  # Invoice
    custom_model = page_obj['custom_model']  # InvoiceNameCustomField etc
    # InvoiceNameCustomField etc
    custom_value_model = page_obj['custom_value_model']
    custom_relation = page_obj['custom_relation']  # invoice_object
    field_item_name = page_obj['field_item_name']  # invoice
    obj, html, desired_width = commerce_pdf_download(
        request, id=id, send_email=True, object_type=object_type)

    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by(
        'order', 'created_at').first()
    module_slug = None
    if module:
        module_slug = module.slug

    obj_id = getattr(obj, id_field, None)
    obj_id = f"{obj_id:04d}"

    customers = ''
    if obj.contact:
        customers = obj.contact.name
        if obj.contact.last_name:
            if lang == 'ja':
                customers = obj.contact.last_name + " " + customers
            else:
                customers = customers + " " + obj.contact.last_name
    elif obj.company:
        customers = obj.company.name

    # Pls check this translation
    if lang == 'ja':
        mail_subject = f'新しい{page_title}を受け取りました[{obj_id}]'
        message = f'{customers}さま\n{page_title}を添付致しました。ご査収いただきますようお願い致します。'
    else:
        mail_subject = f'You received a new {page_title} [{obj_id}]'
        message = f'Hi {customers}, \nThe attached is your {page_title}. Please take a look and should you have any questions contact us. Thank you.'

    if request.POST.get('email_content'):
        message = request.POST.get('email_content')

    from_email = 'Sanka <<EMAIL>>'

    try:
        to_email = [email]
        cc_emails = []
        cc_list = cc_list.replace("['", "").replace("']", '')
        if cc_list:
            lists_name = cc_list.split(",")
            for list_name in lists_name:
                cc_emails.append(list_name)
        result = io.BytesIO()
        pisa.CreatePDF(html.encode("UTF-8"), result, encoding='UTF-8')
        result.seek(0)
        result = result.read()

        email = EmailMessage(
            mail_subject,
            message,
            from_email,
            to_email,
            cc=cc_emails
        )

        name_pdf = obj_id + ".pdf"
        email.attach(name_pdf, result)
        try:
            email.send()
        except:
            if lang == 'ja':
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message='CCメールが送信先のメールと同じです.', type='error')
            else:
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message='Email CC same as Send To Email.', type='error')

        # Create Meter Object #
        data = {}
        data['sent_' + field_item_name] = str(obj.id)
        ActionTracker.objects.create(workspace=workspace, status='success',
                                     input_data=data,
                                     output_data=data,
                                     )

        if lang == 'ja':
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message='メールは正常に送信されました.', type='success')
        else:
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message='Email was sent successfully.', type='success')

        app_log = AppLog.objects.create(
            **{'workspace': workspace, object_type[:-1]: obj})
        app_log.user = request.user
        app_log.action = "send_email"
        app_log.field_name = to_email
        app_log.save()

    except Exception as e:
        print("Error at email send", e)

    if module_slug:
        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
    return redirect(reverse('main', host='app'))


@login_or_hubspot_required  # Decomposed to [object_type]_create_and_update
def billing_update(request, id=None, object_type=None):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    page_obj = get_page_object(object_type, lang)

    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    module = Module.objects.filter(
        workspace=workspace, object_values__contains=object_type).order_by('order', 'created_at')

    module_slug = request.POST.get('module', '')
    if module_slug:
        module = module.filter(slug=module_slug)

    if module:
        module = module.first()
        module_slug = module.slug

    id_field = page_obj['id_field']
    page_title = page_obj['page_title']
    default_columns = page_obj['default_columns']
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    # InvoiceNameCustomField etc
    custom_value_model = page_obj['custom_value_model']
    custom_value_file_model = page_obj['custom_value_file_model']
    custom_value_file_relation = page_obj['custom_value_file_relation']

    custom_item_model = page_obj['custom_item_model']
    custom_item_value_model = page_obj['custom_item_value_model']

    item_model = page_obj['item_model']
    try:
        purchase_item_model = page_obj['purchase_item_model']
    except:
        purchase_item_model = ''
    field_item_name = page_obj['field_item_name']
    additional_filter_fields = page_obj['additional_filter_fields']

    if request.method != 'POST':
        return HttpResponse(405)

    if 'csv_upload' in request.POST:
        try:
            result = bulk_csv_commerce(request, object_type)
            if result != "Done":
                Notification.objects.create(workspace=workspace, user=request.user,
                                            message="Wrong format uploaded, Please use this format",
                                            message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                                            cta_text="Template",
                                            cta_text_ja="テンプレート",
                                            cta_target=TEMPLATE_FILE[object_type][lang],
                                            type="error")

        except:
            traceback.print_exc()
            Notification.objects.create(workspace=workspace, user=request.user,
                                        message="Wrong format uploaded, Please use this format",
                                        message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                                        cta_text="Template",
                                        cta_text_ja="テンプレート",
                                        cta_target=TEMPLATE_FILE[object_type][lang],
                                        type="error")

        if module_slug:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
        return redirect(reverse('main', host='app'))

    if request.POST.get('submit_action') == 'archive':
        obj = base_model.objects.get(id=id)
        obj.usage_status = 'archived'
        obj.save(log_data={'user': request.user, 'workspace': workspace})

        if request.POST.get("view_id"):
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}')
        else:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))

    if request.POST.get('submit_action') == 'activate':
        obj = base_model.objects.get(id=id)
        if obj.usage_status == 'archived':
            if object_type == TYPE_OBJECT_SLIP or has_quota(workspace, object_type):
                obj.usage_status = 'active'
                obj.save(log_data={'user': request.user,
                         'workspace': workspace})
        if request.POST.get("view_id"):
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}')
        else:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))

    # Input Steps from form ==========================
    amount = []
    amounts_input = request.POST.getlist('amount')
    for am in amounts_input:
        am = am.replace(',', '')
        amount.append(am)

    section_position = request.POST.getlist('section_position')
    section_type = request.POST.getlist('section_type')
    currency = request.POST.get('currency')
    section = request.POST.getlist('section')
    amounts_input = request.POST.getlist('amount')
    amount_item = request.POST.getlist('item_amount')
    contact_and_company = request.POST.getlist("contact_and_company", None)
    start_date = request.POST.get('start-date', '')
    due_date = request.POST.get('freq-due', '')
    email = request.POST.get('email', '')
    cc_list = request.POST.get('cc_list', '')
    tax_rate = request.POST.get('tax_rate', 0)
    status = request.POST.get('status', '')
    slip_type = request.POST.get('slip_type', '')
    stamp_value = request.POST.get('sw_stamp', '')
    item_request = request.POST.getlist('item', '')
    shipping_checkbox = request.POST.get('shipping_checkbox', None)
    shipping = request.POST.get('shipping', 0)

    # Quick Entry Check
    billing_type = request.POST.get('billing_type', 'item')

    # Cost Table
    purchase_item_request = request.POST.getlist('purchase-item', '')
    purchase_amounts_input = request.POST.getlist('purchase-amount', '')
    purchase_amount_item = request.POST.getlist('purchase-item_amount', '')
    purchase_tax_item_rate = request.POST.getlist('purchase-tax_item_rate', '')

    notes = request.POST.get('notes', '')
    send_from = request.POST.get('send_from', '')
    tax_item_rate = request.POST.getlist('tax_item_rate', None)
    tax_option = request.POST.get('tax_option', None)
    tax_inclusive = request.POST.get('tax_inclusive', None)
    discount_toggle = request.POST.get('discount_toggle', None)
    cost_toggle = request.POST.get('cost_toggle', None)

    if cc_list:
        cc_list = cc_list.split(',')

    if tax_rate == '':
        tax_rate = 0

    item = []

    for it in item_request:
        item.append(it.split('|')[0])

    if discount_toggle:
        discount_option = request.POST.get('discount_option')
        discount_tax_option = request.POST.get('discount_tax_option')
        discount_value = request.POST.get('discount')
    else:
        discount_tax_option = ""
        discount_option = ""
        discount_value = ""

    if cost_toggle:
        cost_option = True
    else:
        cost_option = ""

    if not tax_inclusive:
        tax_inclusive = False
    else:
        if tax_option != "item_based_tax":
            tax_inclusive = "False"

    print("======= shipping: ", shipping)
    if shipping_checkbox and shipping:
        shipping = ShopTurboShippingCost.objects.filter(id=shipping).first()
    else:
        shipping = None

    if not stamp_value:
        stamp_value = False

    purchase_amount = []
    for pam in purchase_amounts_input:
        pam = pam.replace(',', '')
        purchase_amount.append(pam)

    purchase_item = []
    for pit in purchase_item_request:
        purchase_item.append(pit.split('|')[0])

    if id:
        try:
            obj = base_model.objects.get(id=id)
        except base_model.DoesNotExist:
            return HttpResponse(status=404)
    else:
        if object_type != TYPE_OBJECT_SLIP:
            sync_usage(workspace, MODELS_TO_STORAGE_USAGE[base_model])
            if object_type != TYPE_OBJECT_SLIP and not has_quota(workspace, object_type):
                msg = f"{page_title}, could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing {page_title} to free up space."
                if lang == 'ja':
                    msg = f"{page_title},制限を超えたため作成できませんでした。サブスクリプション プランをアップグレードしてクォータを増やすか、既存の {page_title} の一部をアーカイブしてスペースを解放します。"
                Notification.objects.create(
                    workspace=workspace, user=request.user, message=msg, type="error")
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
        obj = base_model.objects.create(
            workspace=workspace, updated_at=timezone.now)

    if type(item) != list:
        item = ast.literal_eval(item)
    if type(amount_item) != list:
        amount_item = ast.literal_eval(amount_item)
    if type(amount) != list:
        amount = ast.literal_eval(amount)
    if type(tax_item_rate) != list:
        tax_item_rate = ast.literal_eval(tax_item_rate)
    if type(section) != list:
        section = ast.literal_eval(section)
    if type(section_type) != list:
        section_type = ast.literal_eval(section_type)
    if type(section_position) != list:
        section_position = ast.literal_eval(section_position)

    if len(purchase_item) > 0:
        if type(purchase_item) != list:
            purchase_item = ast.literal_eval(purchase_item)
        if type(purchase_amount_item) != list:
            purchase_amount_item = ast.literal_eval(purchase_amount_item)
        if type(purchase_amount) != list:
            purchase_amount = ast.literal_eval(purchase_amount)
        if type(purchase_tax_item_rate) != list:
            purchase_tax_item_rate = ast.literal_eval(purchase_tax_item_rate)

    # Redirect after updating the associated object
    if 'update_orders' in request.POST:
        source = request.POST.get('source')
        if source == TYPE_OBJECT_INVOICE:
            order_ids = request.POST.getlist('orders', [])
            invoice = obj
            if invoice and order_ids != ['']:
                invoice.orders.clear()
                order_objs = ShopTurboOrders.objects.filter(id__in=order_ids)
                invoice.orders.add(*order_objs)
            else:
                invoice.orders.clear()
        if source == TYPE_OBJECT_ESTIMATE:
            order_ids = request.POST.getlist('orders', [])
            estimate = obj
            if estimate and order_ids != ['']:
                estimate.shopturboorders_set.clear()
                order_objs = ShopTurboOrders.objects.filter(id__in=order_ids)
                estimate.shopturboorders_set.add(*order_objs)
            else:
                estimate.orders.clear()

    if 'update_cases' in request.POST:
        source = request.POST.get('source')
        if source == TYPE_OBJECT_INVOICE:
            case_ids = request.POST.getlist('cases', [])
            invoice = obj
            if invoice and case_ids != ['']:
                invoice.deals.clear()
                case_objs = Deals.objects.filter(id__in=case_ids)
                invoice.deals.add(*case_objs)
            else:
                invoice.deals.clear()
        elif source == TYPE_OBJECT_ESTIMATE:
            case_ids = request.POST.getlist('cases', [])
            estimate = obj
            if estimate and case_ids != ['']:
                estimate.deals.clear()
                case_objs = Deals.objects.filter(id__in=case_ids)
                estimate.deals.add(*case_objs)
            else:
                estimate.deals.clear()

    if 'update_estimates' in request.POST:
        source = request.POST.get('source')
        if source == TYPE_OBJECT_INVOICE:
            estimate_ids = request.POST.getlist('estimates', [])
            invoice = obj
            if invoice and estimate_ids != ['']:
                invoice.estimate_set.clear()
                estimate_objs = Estimate.objects.filter(id__in=estimate_ids)
                invoice.estimate_set.add(*estimate_objs)
            else:
                invoice.estimate_set.clear()

    if 'update_subscriptions' in request.POST:
        source = request.POST.get('source')
        if source == TYPE_OBJECT_INVOICE:
            subscription_ids = request.POST.getlist('subscriptions', [])
            invoice = obj
            if invoice and subscription_ids != ['']:
                invoice.subscriptions.clear()
                subscription_objs = ShopTurboSubscriptions.objects.filter(
                    id__in=subscription_ids)
                invoice.subscriptions.add(*subscription_objs)
            else:
                invoice.subscriptions.clear()

    if 'update_invoices' in request.POST:
        source = request.POST.get('source')
        if source == TYPE_OBJECT_RECEIPT:
            invoice_ids = request.POST.getlist('invoices', [])
            receipt = obj
            if receipt and invoice_ids != ['']:
                receipt.invoices.clear()
                invoice_objs = Invoice.objects.filter(
                    id__in=invoice_ids)
                receipt.invoices.add(*invoice_objs)
            else:
                receipt.invoices.clear()
        elif source == TYPE_OBJECT_ESTIMATE:
            invoice_ids = request.POST.getlist('invoices', [])
            estimate = obj
            if estimate and invoice_ids != ['']:
                estimate.invoice.clear()
                invoice_objs = Invoice.objects.filter(
                    id__in=invoice_ids)
                estimate.invoice.add(*invoice_objs)
            else:
                estimate.invoice.clear()

    if 'update_receipts' in request.POST:
        source = request.POST.get('source')
        if source == TYPE_OBJECT_INVOICE:
            receipt_ids = request.POST.getlist('receipts', [])
            invoice = obj
            if invoice and receipt_ids != ['']:
                invoice.receipts.clear()
                receipt_objs = Receipt.objects.filter(
                    id__in=receipt_ids)
                invoice.receipts.add(*receipt_objs)
            else:
                invoice.receipts.clear()

    if 'update_orders' in request.POST or 'update_cases' in request.POST or 'update_estimates' in request.POST or 'update_subscriptions' in request.POST or 'update_invoices' in request.POST or 'update_receipts' in request.POST:
        if request.POST.get('source_url'):
            source_url = request.POST.get('source_url', None)
            if source_url:
                source_url = update_query_params_url(source_url, {
                    'target': object_type,
                    'id': [str(obj.id)]
                })
                return redirect(source_url)
        if request.POST.get("view_id"):
            view = View.objects.filter(id=request.POST.get("view_id"))
            if view:
                object_type = view[0].target
                page_obj = get_page_object(object_type, lang)

                if obj:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}&id={obj.id}')
                else:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}')

    try:
        print('logit!', request.POST)
        obj_item = item_model.objects.filter(
            **{field_item_name: obj}).order_by('created_at')
        # Clear all billing item data
        if obj_item:
            for obj__item in obj_item:
                obj__item.delete(
                    log_data={'user': request.user, 'workspace': workspace})
        for index, (item, amount_item, amount_price, tax_item_rate) in enumerate(zip(item, amount_item, amount, tax_item_rate)):
            if not tax_item_rate:
                tax_item_rate = '0'
            if amount_item == '':
                amount_item = 0.0
            if amount_price == '':
                amount_price = 0.0

            obj_item = item_model.objects.create(**{field_item_name: obj})

            if is_valid_uuid(item):
                item = ShopTurboItems.objects.filter(id=item)
                if item:
                    obj_item.item_link = item.last()
            else:
                obj_item.item_name = item
            obj_item.save(log_data={'user': request.user,
                          'status': 'create', 'workspace': workspace})

            obj_item.amount_price = amount_price
            obj_item.amount_item = amount_item

            if obj.tax_inclusive:
                obj_item.total_price_without_tax = (
                    (float(amount_price) * float(amount_item) / (1 + (float(tax_item_rate) / 100.0))))
                obj_item.total_price = float(amount_price) * float(amount_item)
            else:
                if tax_item_rate != '0':
                    obj_item.total_price_without_tax = float(
                        amount_price) * float(amount_item)
                    obj_item.total_price = (float(
                        amount_price) * float(tax_item_rate) + float(amount_price)) * float(amount_item)
                else:
                    obj_item.total_price_without_tax = float(
                        amount_price) * float(amount_item)
                    obj_item.total_price = float(
                        amount_price) * float(amount_item)
            obj_item.tax_rate = tax_item_rate
            obj_item.save(
                log_data={'user': request.user, 'workspace': workspace})

            line_item_properties = {
                key: request.POST.getlist(key)
                for key in request.POST
                if key.startswith('line_item_property')
            }

            for key, value in line_item_properties.items():
                custom_field_keys = key.split('|')
                custom_field_id = key.split('|')[-1]

                custom_field = custom_item_model.objects.get(
                    id=custom_field_id)
                custom_field_value, _ = custom_item_value_model.objects.get_or_create(
                    field_name=custom_field, item_order=obj_item)

                if len(custom_field_keys) == 3:  # discount
                    custom_field_value.value_number_format = value[index]
                else:
                    custom_field_value.value = value[index]
                custom_field_value.save()

        if object_type == TYPE_OBJECT_ESTIMATE and cost_toggle:  # Cost Table Items
            obj_item = purchase_item_model.objects.filter(
                **{field_item_name: obj}).order_by('created_at')
            # Clear all billing item data
            if obj_item:
                for obj__item in obj_item:
                    obj__item.delete(
                        log_data={'user': request.user, 'workspace': workspace})

            for item, amount_item, amount_price, tax_item_rate in zip(purchase_item, purchase_amount_item, purchase_amount, purchase_tax_item_rate):
                if not tax_item_rate:
                    tax_item_rate = '0'
                if amount_item == '':
                    amount_item = 0.0
                if amount_price == '':
                    amount_price = 0.0

                obj_item = purchase_item_model.objects.create(
                    **{field_item_name: obj})

                if is_valid_uuid(item):
                    item = ShopTurboItems.objects.filter(id=item)
                    if item:
                        obj_item.item_link = item.last()
                else:
                    obj_item.item_name = item
                obj_item.save(
                    log_data={'user': request.user, 'status': 'create', 'workspace': workspace})

                obj_item.amount_price = amount_price
                obj_item.amount_item = amount_item

                if obj.tax_inclusive:
                    obj_item.total_price_without_tax = (
                        (float(amount_price) * float(amount_item) / (1 + (float(tax_item_rate) / 100.0))))
                    obj_item.total_price = float(
                        amount_price) * float(amount_item)
                else:
                    if tax_item_rate != '0':
                        obj_item.total_price_without_tax = float(
                            amount_price) * float(amount_item)
                        obj_item.total_price = (float(
                            amount_price) * float(tax_item_rate) + float(amount_price)) * float(amount_item)
                    else:
                        obj_item.total_price_without_tax = float(
                            amount_price) * float(amount_item)
                        obj_item.total_price = float(
                            amount_price) * float(amount_item)
                obj_item.tax_rate = tax_item_rate
                obj_item.save(
                    log_data={'user': request.user, 'workspace': workspace})
    except Exception as e:
        print('[Error saving items]: ', e)

    try:
        if object_type == TYPE_OBJECT_ESTIMATE:
            SectionItemEstimate.objects.filter(
                workspace=workspace, estimate=obj).delete()
            for section, section_type, section_position in zip(section, section_type, section_position):
                section_object, _ = SectionItemEstimate.objects.get_or_create(
                    workspace=workspace,
                    section_type=section_type,
                    estimate=obj,
                    position=int(section_position),
                    value=section
                )

        elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
            SectionItemDeliverySlip.objects.filter(
                workspace=workspace, deliveryslip=obj).delete()
            for section, section_type, section_position in zip(section, section_type, section_position):
                section_object, _ = SectionItemDeliverySlip.objects.get_or_create(
                    workspace=workspace,
                    section_type=section_type,
                    deliveryslip=obj,
                    position=int(section_position),
                    value=section
                )

        elif object_type == TYPE_OBJECT_INVOICE:
            SectionItemInvoice.objects.filter(
                workspace=workspace, invoice=obj).delete()
            for section, section_type, section_position in zip(section, section_type, section_position):
                section_object, _ = SectionItemInvoice.objects.get_or_create(
                    workspace=workspace,
                    section_type=section_type,
                    invoice=obj,
                    position=int(section_position),
                    value=section
                )
        elif object_type == TYPE_OBJECT_RECEIPT:
            SectionItemReceipt.objects.filter(
                workspace=workspace, receipt=obj).delete()
            for section, section_type, section_position in zip(section, section_type, section_position):
                section_object, _ = SectionItemReceipt.objects.get_or_create(
                    workspace=workspace,
                    section_type=section_type,
                    receipt=obj,
                    position=int(section_position),
                    value=section
                )
    except Exception as e:
        print('[Error saving sections]: ', e)

    # IF NEED DELETE OBJECT
    if 'delete' in request.POST:
        obj = base_model.objects.get(id=id)
        obj.delete()
        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))

    obj.workspace = workspace
    obj.email = email
    obj.notes = notes
    obj.status = status
    obj.tax_rate = float(tax_rate)
    obj.send_from = send_from
    obj.is_stamp = stamp_value
    obj.tax_list = tax_item_rate
    obj.tax_inclusive = tax_inclusive
    obj.discount_option = discount_option
    obj.cost_option = cost_option
    obj.discount_tax_option = discount_tax_option
    obj.discount = discount_value
    obj.currency = currency
    obj.updated_at = timezone.now
    obj.shipping_cost = shipping

    if object_type == TYPE_OBJECT_RECEIPT and hasattr(obj, "billing_type"):
        obj.billing_type = billing_type
        tax_option = 'unified_tax'
        if billing_type == 'manual':
            obj.manual_price = float(request.POST.get(
                'manual_price', 0.0).replace(',', ''))
            item_model.objects.filter(**{field_item_name: obj}).delete()

            SectionItemReceipt.objects.filter(
                workspace=workspace, receipt=obj).delete()
    obj.tax_option = tax_option

    if slip_type:
        obj.slip_type = slip_type

    if start_date:
        if isinstance(start_date, tuple):
            start_date = start_date[0]
        try:
            if lang == 'ja':
                start_date = datetime.strptime(start_date, '%Y年%m月%d日').date()
            else:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            obj.start_date = start_date
        except:  # Format date is invalid , skip to save it
            pass
    if due_date:
        try:
            if lang == 'ja':
                due_date = datetime.strptime(due_date, '%Y年%m月%d日').date()
            else:
                due_date = datetime.strptime(due_date, '%Y-%m-%d').date()
            obj.due_date = due_date
        except:  # Format date is invalid , skip to save it
            pass

    for contact_and_company_id in contact_and_company:
        if Contact.objects.filter(id=contact_and_company_id):
            obj.contact = Contact.objects.get(id=contact_and_company_id)
            obj.company = None

        elif Company.objects.filter(id=contact_and_company_id):
            obj.company = Company.objects.get(id=contact_and_company_id)
            obj.contact = None

    if object_type in [TYPE_OBJECT_INVOICE, TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_RECEIPT, TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_SLIP]:
        obj.save(log_data={'user': request.user, 'workspace': workspace})
    else:
        obj.save()
    obj = handling_items(obj)
    print("Obj Handled 1: ")
    print(obj)

    # == Object Properties handler
    save_custom_property(request, obj, page_obj)

    if request.POST.get('submit_action') == 'duplicate' or 'duplicate' in request.POST:
        obj_item = item_model.objects.filter(
            **{field_item_name: obj}).order_by('created_at')
        customFields = custom_value_model.objects.filter(
            **{field_item_name: obj})

        if object_type == TYPE_OBJECT_SLIP or has_quota(workspace, object_type):
            obj = base_model.objects.get(id=id)
            obj.id = None
            obj.created_at = timezone.now()
            setattr(obj, id_field, None)
            obj.save()

            for field in customFields:
                field.id = None
                setattr(field, field_item_name, obj)
                field.save()
                # custom_value_model.objects.create(**{field_item_name:obj, 'value':field.value,'field_name':field.field_name})

            for item in obj_item:
                item.id = None
                setattr(item, field_item_name, obj)
                item.save()

    if request.POST.get('submit_action') == 'send_email':
        commerce_send_mail(request, obj.id, object_type=object_type)

    if request.POST.get('source_url'):
        source_url = request.POST.get('source_url', None)
        if source_url:
            source_url = update_query_params_url(source_url, {
                'target': object_type,
                'id': [str(obj.id)]
            })
            return redirect(source_url)
    if request.POST.get("view_id"):
        view = View.objects.filter(id=request.POST.get("view_id"))
        if view:
            object_type = view[0].target
            page_obj = get_page_object(object_type, lang)

            if obj:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}&id={obj.id}')
            else:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?view_id={str(request.POST.get("view_id"))}')

    # association related
    if request.POST.get('type', '') == 'create-association':
        if 'object_type' in request.POST:
            object_type = request.POST.get('object_type')
        source = request.POST.get('source')
        module_object_slug = OBJECT_TYPE_TO_SLUG[source]
        module = Module.objects.filter(workspace=workspace, object_values__contains=source).order_by(
            'order', 'created_at').first()
        module_slug = module.slug
        if source == TYPE_OBJECT_CASE:
            object_id = request.POST.get('source_object_id')
            case = Deals.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                case.invoices.add(obj)
            elif object_type == TYPE_OBJECT_ESTIMATE:
                case.estimates.add(obj)
            case.save()
        elif source == TYPE_OBJECT_INVOICE and object_type == TYPE_OBJECT_RECEIPT:
            object_id = request.POST.get('source_object_id')
            invoice = Invoice.objects.filter(id=object_id).first()
            if 'object_id' in request.POST:
                receipt_id = request.POST.get('object_id')
            else:
                receipt_id = obj.id
            receipt = Receipt.objects.filter(id=receipt_id).first()
            if invoice and receipt:
                invoice.receipts.add(receipt)
                invoice.save()
            if 'object_id' in request.POST:
                return HttpResponse(status=200)
            else:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={object_id}')

        elif source == TYPE_OBJECT_RECEIPT:
            object_id = request.POST.get('source_object_id')
            receipt = Receipt.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                if receipt:
                    receipt.invoices.add(obj)
                    receipt.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={object_id}')

        elif source == TYPE_OBJECT_DELIVERY_NOTE:
            object_id = request.POST.get('source_object_id')
            delivery_slip = DeliverySlip.objects.filter(id=object_id).first()
            if delivery_slip and object_type == TYPE_OBJECT_INVOICE:
                delivery_slip.invoices.add(obj)
                delivery_slip.save()

            if 'object_id' in request.POST:
                return HttpResponse(status=200)
            else:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={object_id}')
        elif source == TYPE_OBJECT_ORDER:
            object_id = request.POST.get('source_object_id')
            order = ShopTurboOrders.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                order.invoice.add(obj)
            if object_type == TYPE_OBJECT_ESTIMATE:
                order.estimate.add(obj)
            order.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={order.id}')

        elif source == TYPE_OBJECT_SUBSCRIPTION:
            object_id = request.POST.get('source_object_id')
            subs = ShopTurboSubscriptions.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                subs.invoices.add(obj)
                if not subs.last_billed_date:
                    subs.last_billed_date = obj.start_date
                if not subs.next_bill_date:
                    subs.next_bill_date = obj.due_date
            subs.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={subs.id}')

        elif source == TYPE_OBJECT_INVOICE and object_type == TYPE_OBJECT_ESTIMATE:
            object_id = request.POST.get('source_object_id')
            invoice = Invoice.objects.filter(id=object_id).first()
            invoice.estimate_set.add(obj)

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={str(invoice.id)}')

        elif source == TYPE_OBJECT_ESTIMATE:
            object_id = request.POST.get('source_object_id')
            estimate = Estimate.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                estimate.invoice.add(obj)

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={str(estimate.id)}')

        elif source == TYPE_OBJECT_COMPANY:
            object_id = request.POST.get('source_object_id')
            company = Company.objects.filter(id=object_id).first()
            obj.company = company
            obj.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={company.id}')

        elif source == TYPE_OBJECT_JOURNAL:
            object_id = request.POST.get('source_object_id')
            journal = JournalEntry.objects.filter(id=object_id).first()
            if object_type == TYPE_OBJECT_INVOICE:
                journal.invoice = obj
                journal.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={journal.id}')

        elif source == TYPE_OBJECT_CONTACT:
            object_id = request.POST.get('source_object_id')
            contact = Contact.objects.filter(id=object_id).first()
            obj.contact = contact
            obj.save()

            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={source}&id={contact.id}')

    else:
        if obj:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?id={obj.id}')
    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))


# Utility functions moved to utils.commerce.file_utils
from utils.commerce.file_utils import generate_zip


@login_or_hubspot_required  # ZIP download
def commerce_zip_download(request, list_object=None, object_type=None):
    pdf_tuple = []
    lang = request.LANGUAGE_CODE

    page_obj = get_page_object(object_type, lang)
    id_field = page_obj['id_field']
    page_title = page_obj['page_title']
    default_columns = page_obj['default_columns']
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    custom_relation = page_obj['custom_relation']
    field_item_name = page_obj['field_item_name']

    if list_object:
        obj_id = list_object
    else:
        obj_id = request.POST.getlist(object_type[:-1] + '_id')
    objects = base_model.objects.filter(id__in=obj_id)

    for obj in objects:
        obj_id = getattr(obj, id_field, None)
        obj_id = f"{obj_id:04d}"
        pdf_tuple.append(
            (obj_id + ".pdf", commerce_pdf_download(request, id=obj.id, buffer='buffer'))
        )

    full_zip_in_memory = generate_zip(pdf_tuple)
    response = HttpResponse(
        full_zip_in_memory, content_type='application/force-download')
    if lang == 'ja':
        japanese_filename = '全ての' + page_title
        # Use urllib.parse.quote to properly encode the filename.
        encoded_filename = quote(japanese_filename.encode('utf-8'))
        response['Content-Disposition'] = f'attachment; filename*=UTF-8\'\'{encoded_filename}.zip'
    else:
        response['Content-Disposition'] = 'attachment; filename="{}"'.format(
            f'All {page_title}.zip')
    return response


#  ================== Utilities ===================


def addon_create_company_contact(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    type_addon = request.GET.get('type_addon')
    create_type = request.GET.get('create_type')
    object_type = request.GET.get('object_type')
    slip_set_id = request.GET.get('slip_set_id', '')

    if object_type:
        drawer_url = object_type[:-1] + '_drawer'
        page_obj = get_page_object(object_type, lang)
        page_title = page_obj['page_title']
    else:
        drawer_url = None
        page_title = None

    if type_addon == 'create-contacts':
        companies = Company.objects.filter(workspace=workspace)
        contact_list = ContactList.objects.filter(workspace=workspace)
        set_id = request.GET.get('set_id')
        set_id, properties = get_properties_from_set(
            set_id, 'contacts', workspace)

        properties, CustomFieldMap = get_default_prompt_set(
            workspace, TYPE_OBJECT_CONTACT, ContactsNameCustomField)

        context = {
            'drawer_url': drawer_url,
            'drawer_type': object_type,
            'page_title': page_title,
            'companies': companies,
            'contact_list': contact_list,
            'country_code': COUNTRY_CODE,
            'properties': properties,
            'CustomFieldMap': CustomFieldMap,
            'slip_set_id': slip_set_id,
            'create_type': create_type
        }
        return render(request, 'data/contacts/account-explorer-contacts-drawer.html', context)

    elif type_addon == 'create-company':
        company_list = CompanyList.objects.filter(workspace=workspace)
        properties, CustomFieldMap = get_default_prompt_set(
            workspace, TYPE_OBJECT_COMPANY, CompanyNameCustomField)

        context = {'create_type': create_type,
                   'drawer_type': object_type,
                   'drawer_url': drawer_url,
                   'page_title': page_title,
                   'slip_set_id': slip_set_id,
                   'country_code': COUNTRY_CODE,
                   'company_list': company_list,
                   'properties': properties,
                   'CustomFieldMap': CustomFieldMap}
        return render(request, 'data/contacts/account-explorer-contacts-company-drawer.html', context)


# Utility functions moved to utils.commerce.data_utils
from utils.commerce.data_utils import safe_column_access


# Utility functions moved to utils.commerce.data_utils
from utils.commerce.data_utils import convert_date_format


@login_or_hubspot_required
def commerce_csv_header_extractor(request, object_type=None):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    page_obj = get_page_object(object_type, lang)

    id_field = page_obj['id_field']
    default_columns = page_obj['default_columns']
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    custom_model = page_obj['custom_model']

    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by(
        'order', 'created_at').first()
    module_slug = None
    if module:
        module_slug = module.slug

    if request.method == 'POST':
        csv = request.FILES.get('csv_upload', False)
        if not csv:
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message="Failed to retreive the CSV file.", type="error")
            if module_slug:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
            return redirect(reverse('main', host='app'))

        with csv.open() as f:
            read_data = f.read()
            result = chardet.detect(read_data)
            encoding = result['encoding']
            # #Reset Pointer
            f.seek(0)
            if encoding.lower() == 'shift_jis':
                content = f.read()
                decoded_content = content.decode('shift_jis')
                string_data = StringIO(decoded_content)
                df = pd.read_csv(string_data, sep=",",
                                 encoding='shift_jis', dtype=str)
            else:
                df = pd.read_csv(f, dtype=str)
            df = df.dropna(axis=1, how='all')
            df = df.dropna(axis=0, how='all')
            df = df.reset_index(drop=True)

        header_list = df.columns.tolist()
        sanka_columns = ['start_date', 'due_date', 'currency', 'item', 'item_price', 'item_amount', 'status',
                         'tax_rate', 'discount_rate', 'discount_type', 'discount_option', 'tax_option', 'tax_inclusive', 'contact_id', 'company_id']
        if object_type != 'invoices':
            sanka_columns.remove('due_date')

        contactsnamecustomfield = [str(uid) for uid in custom_model.objects.filter(
            workspace=workspace).exclude(type__in=["file", "image", "formula"]).values_list('id', flat=True)]
        sanka_columns.extend(contactsnamecustomfield)
        context = {
            "header_list": header_list,
            "sanka_columns": sanka_columns,
            "mapping_type": object_type,
        }
        return render(request, 'data/commerce/csv-commerce-import-mapping.html', context)

    return HttpResponse(200)


def commerce_table_mapping_url(request):
    workspace = get_workspace(request.user)
    header_list = request.POST.get("header_list")
    sanka_columns = request.POST.get("sanka_columns")
    mapping_type = request.POST.get("mapping_type", None)
    lang = request.LANGUAGE_CODE
    try:
        page_obj = get_page_object(mapping_type, lang)
        page_title = page_obj['page_title']
    except:
        page_title = None

    context = {
        "header_list": ast.literal_eval(header_list),
        "sanka_columns": ast.literal_eval(sanka_columns),
        "page_title": page_title,
        "mapping_type": mapping_type
    }

    mapping_storage = ImportMappingFields.objects.filter(
        workspace=workspace, object_type=mapping_type).first()
    if mapping_storage:
        context["mapping_storage"] = mapping_storage

    if mapping_type in ["invoices", "estimates", "delivery_slips", "receipts"]:
        return render(request, 'data/commerce/csv-commerce-table-mapping.html', context)
    return HttpResponse('Unknown Input')


@login_or_hubspot_required
def send_emails_with_object_pdf(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == 'GET':
        object_type = request.GET.get('object_type')

        selected_ids = request.GET.get('selected_ids', '[]')
        if selected_ids == '[]':
            selected_ids = request.GET.get('order_ids', '[]')
        if '[' in selected_ids and ']' in selected_ids:
            selected_ids = ast.literal_eval(selected_ids)
        else:
            selected_ids = [selected_ids]

        if object_type is None:
            object_type = request.GET.get('page_group_type')
        page_obj = get_page_object(object_type, lang)
        base_model = page_obj['base_model']
        id_field = page_obj['id_field']

        objects = base_model.objects.filter(
            workspace=workspace, id__in=selected_ids)
        smtp_channels = Channel.objects.filter(
            Q(workspace=workspace, integration__slug='smtp') |
            Q(
                workspace=workspace,
                integration__slug='rakuten',
                service_account__isnull=False,
                app_id__isnull=False,
                account_id__isnull=False,
                account_username__isnull=False,
            )
        ).exclude(
            Q(integration__slug='rakuten', service_account='') |
            Q(integration__slug='rakuten', app_id='') |
            Q(integration__slug='rakuten', account_id='') |
            Q(integration__slug='rakuten', account_username='')
        )

        const_properties = []
        properties = []
        custom_properties = []
        templates = []

        custom_prop_model = None
        if request.GET.get('page_group_type') == TYPE_OBJECT_ORDER:
            base_model = ShopTurboOrders
            custom_prop_model = ShopTurboOrdersNameCustomField
            const_properties.append('customer__name')

            templates = MessageBodyTemplate.objects.filter(workspace=workspace)

        if base_model:
            properties = [
                field.name for field in base_model._meta.get_fields() if not field.is_relation]

        if custom_prop_model:
            custom_props = custom_prop_model.objects.filter(
                workspace=workspace)
            custom_properties = [prop.name for prop in custom_props]

        if len(const_properties) > 0:
            for prop in const_properties:
                properties.append(prop)

        context = {
            'action_id': request.GET.get('action_id'),
            'object_type': object_type,
            'objects': objects,
            'workspace': workspace,
            'selected_ids': selected_ids,
            'id_field': id_field,
            'smtp_channels': smtp_channels,
            'properties': properties,
            'custom_properties': custom_properties,
            'templates': templates,
        }

        return render(request, 'data/commerce/action/send-emails-with-object-pdf.html', context)

    if request.method == 'POST':
        object_type = request.POST.get('object_type')
        module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
        module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by(
            'order', 'created_at').first()
        module_slug = None
        if module:
            module_slug = module.slug
        page_obj = get_page_object(object_type, lang)
        base_model = page_obj['base_model']
        id_field = page_obj['id_field']
        field_item_name = page_obj['field_item_name']
        page_title = page_obj['page_title']

        selected_ids = request.POST.getlist('selected_ids')
        print('=== DEBUG (Selected IDs):', selected_ids)
        objects = base_model.objects.filter(
            workspace=workspace, id__in=selected_ids)

        subject = request.POST.get('subject', '')
        body = request.POST.get('body', '')
        smtp_channel_id = request.POST.get('smtp_channel_id', None)

        checked_emails = []

        df_object = DateFormat.objects.filter(
            workspace=workspace, is_workspace_level=True).first()
        date_format = df_object.value if (
            df_object and df_object.value is not None) else "DD/MM/YYYY"
        date_format = DATE_FORMAT_PARSER[date_format]

        for obj in objects:
            customers = ''
            email = ''

            # Handle the placeholder
            placeholders = re.findall(r'\[\[([^\]]+)\]\]', body)
            for placeholder in placeholders:
                val = ""
                
                if "company__custom_property" in placeholder or "contact__custom_property" in placeholder:
                    customer_obj = None
                    if "company__" in placeholder:
                        customer_obj = obj.company
                        static_string = "company"
                    elif "contact__" in placeholder:
                        customer_obj = obj.contact
                        static_string = "contact"
                        
                    if customer_obj:
                        cleaned_field_name = placeholder.replace(
                            f"{static_string}__custom_property__", "", 1)
                        
                        if static_string == 'company':
                            custom_field = customer_obj.company_custom_field_relations.get(
                                field_name__name=cleaned_field_name)
                        elif static_string == 'contact':
                            custom_field = customer_obj.contact_custom_field_relations.get(
                                field_name__name=cleaned_field_name)
                            
                        if custom_field.field_name.type == 'choice':
                            json_string = custom_field.field_name.choice_value

                            try:
                                data = ast.literal_eval(json_string)
                                value_to_label = {
                                    item['value']: item['label'] for item in data}

                                arr = custom_field.value.split(";")
                                choiced_val = []
                                for val in arr:
                                    val = value_to_label.get(val, "")
                                    if val:
                                        choiced_val.append(val)

                                val = ", ".join(choiced_val)
                            except json.JSONDecodeError as e:
                                print(f"JSON Decode Error: {e}")
                                val = ""
                        elif custom_field.field_name.type in ['date', 'date_time']:
                            date_format = df_object.value if df_object else "DD/MM/YYYY"
                            date_format = DATE_FORMAT_PARSER[df_object.value]
                            if custom_field.field_name.type == 'date_time':
                                date_format = f"{date_format} %H:%M"

                            if custom_field.value_time:
                                date = custom_field.value_time.strftime(
                                    date_format)
                                if date_format == '%Yx%my%dz':
                                    date = date.replace('x', '年').replace(
                                        'y', '月').replace('z', '日')
                                val = date
                            else:
                                val = ""
                        else:
                            val = custom_field.value
                elif "custom_property__" in placeholder:
                    cleaned_field_name = placeholder.replace(
                        "custom_property__", "", 1)
                    try:
                        custom_field = obj.shopturbo_custom_field_relations.get(
                            field_name__name=cleaned_field_name)

                        if custom_field.field_name.type == 'choice':
                            json_string = custom_field.field_name.choice_value

                            try:
                                data = ast.literal_eval(json_string)
                                value_to_label = {
                                    item['value']: item['label'] for item in data}

                                arr = custom_field.value.split(";")
                                choiced_val = []
                                for val in arr:
                                    val = value_to_label.get(val, "")
                                    if val:
                                        choiced_val.append(val)

                                val = ", ".join(choiced_val)
                            except json.JSONDecodeError as e:
                                print(f"JSON Decode Error: {e}")
                                val = ""
                        elif custom_field.field_name.type in ['date', 'date_time']:
                            date_format = df_object.value if df_object else "DD/MM/YYYY"
                            date_format = DATE_FORMAT_PARSER[df_object.value]
                            if custom_field.field_name.type == 'date_time':
                                date_format = f"{date_format} %H:%M"

                            if custom_field.value_time:
                                date = custom_field.value_time.strftime(
                                    date_format)
                                if date_format == '%Yx%my%dz':
                                    date = date.replace('x', '年').replace(
                                        'y', '月').replace('z', '日')
                                val = date
                            else:
                                val = ""
                        else:
                            val = custom_field.value
                    except:
                        val = ""
                else:
                    if placeholder == 'customer__name':
                        if obj.contact:
                            val = obj.contact.name
                        elif obj.company:
                            val = obj.company.name
                    else:
                        new_placeholder = ""
                        if "__" in placeholder:
                            new_obj_field, new_placeholder = placeholder.split("__")
                            new_obj = getattr(obj, new_obj_field, None)
                            if new_obj:
                                val = getattr(new_obj, new_placeholder, "")
                        else:
                            val = getattr(obj, placeholder, "")

                        if placeholder.endswith("_id") or placeholder.startswith("id_") or new_placeholder.endswith("_id") or new_placeholder.startswith("id_"):
                            val = f"{int(val):04d}"
                        elif isinstance(val, (int, float)):
                            # Format as currency
                            val = '{:,.2f}'.format(val)
                        elif isinstance(val, (date_class, datetime)):
                            val = val.strftime(date_format)
                            if date_format == '%Yx%my%dz':
                                val = val.replace('x', '年').replace(
                                    'y', '月').replace('z', '日')

                if val:
                    val = str(val)
                    body = body.replace(f"[[{placeholder}]]", val)
                else:
                    body = body.replace(f"[[{placeholder}]]", "") # if no val available just remove the placeholders

            if obj.contact:
                if obj.contact.email:
                    email = obj.contact.email

                customers = obj.contact.name
                if obj.contact.last_name:
                    if lang == 'ja':
                        customers = obj.contact.last_name + " " + customers
                    else:
                        customers = customers + " " + obj.contact.last_name
            elif obj.company:
                if obj.company.email:
                    email = obj.company.email
                customers = obj.company.name

            if object_type == TYPE_OBJECT_ORDER:
                if email in checked_emails:
                    continue
                checked_emails.append(email)

            if smtp_channel_id:
                if smtp_channel_id != 'sanka':
                    smtp_channel = Channel.objects.filter(
                        id=smtp_channel_id).first()
                    if smtp_channel.integration.slug == 'rakuten':
                        if push_rakuten_email(smtp_channel_id, email, subject, body):
                            message_thread = MessageThread.objects.create(
                                channel=smtp_channel, workspace=workspace, message_type='email')
                            if obj.contact:
                                message_thread.contacts.add(obj.contact)
                            message = Message.objects.create(
                                thread=message_thread, body=body)
                            message.sent_at = timezone.now()
                            message.save()
                    elif push_email_smtp(smtp_channel_id, email, subject, body):
                        message_thread = MessageThread.objects.create(
                            channel=smtp_channel, workspace=workspace, message_type='email')
                        if obj.contact:
                            message_thread.contacts.add(obj.contact)
                        message = Message.objects.create(
                            thread=message_thread, body=body)
                        message.sent_at = timezone.now()
                        message.save()

            obj_id = getattr(obj, id_field, None)

            if not is_valid_email(email):
                if lang == 'ja':
                    Notification.objects.create(workspace=workspace, user=request.user,
                                                message=f'電子メールアドレスが正しくありません。{page_title} - #{obj_id:04d}', type="error")
                else:
                    Notification.objects.create(workspace=workspace, user=request.user,
                                                message=f'Email address is invalid. {page_title} - #{obj_id:04d}', type="error")
                continue

            _, html, _ = commerce_pdf_download(
                request,
                id=obj.id,
                send_email=True,
                object_type=object_type
            )
            # print('html:', html)

            if lang == 'ja':
                mail_subject = f'新しい{page_title}を受け取りました[#{obj_id:04d}]'
                message = f'{customers}さま\n{page_title}を添付致しました。ご査収いただきますようお願い致します。'
            else:
                mail_subject = f'You received a new {page_title} [#{obj_id:04d}]'
                message = f'Hi {customers}, \nThe attached is your {page_title}. Please take a look and should you have any questions contact us. Thank you.'

            if subject:
                mail_subject = subject
            if body:
                message = body

            from_email = 'Sanka <<EMAIL>>'
            to_email = [email]

            try:
                result = io.BytesIO()
                pisa.CreatePDF(html.encode("UTF-8"), result, encoding='UTF-8')
                result.seek(0)
                result = result.read()
            except:
                if lang == 'ja':
                    Notification.objects.create(workspace=workspace, user=request.user,
                                                message=f'PDFダウンロードに失敗しました。{page_title} - #{obj_id:04d}', type="error")
                else:
                    Notification.objects.create(workspace=workspace, user=request.user,
                                                message=f'Failed to send email to {email} for {page_title} - #{obj_id:04d}, There was an error in generating the PDF.', type="error")
                continue

            email = EmailMessage(
                mail_subject,
                message,
                from_email,
                to_email,
            )

            if object_type != TYPE_OBJECT_ORDER:
                name_pdf = f'{obj_id:04d}' + ".pdf"
                email.attach(name_pdf, result)

                name_pdf = f'{obj_id:04d}' + ".pdf"
                email.attach(name_pdf, result)

            try:
                email.send()
            except:
                if lang == 'ja':
                    Notification.objects.create(workspace=workspace, user=request.user,
                                                message=f'電子メールの送信に失敗しました。{page_title} - #{obj_id:04d}', type="error")
                else:
                    Notification.objects.create(workspace=workspace, user=request.user,
                                                message=f'Failed to send email to {email} for {page_title} - #{obj_id:04d}', type="error")
                continue

            # Create Meter Object #
            data = {}
            data['sent_' + field_item_name] = str(obj.id)
            ActionTracker.objects.create(workspace=workspace, status='success',
                                         input_data=data,
                                         output_data=data,
                                         )

            if lang == 'ja':
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message=f'メールは正常に送信されました. {page_title} - #{obj_id:04d}', type='success')
            else:
                Notification.objects.create(workspace=get_workspace(
                    request.user), user=request.user, message=f'Email sent successfully. {page_title} - #{obj_id:04d}', type='success')

            if object_type[:-1] == 'commerce_order':
                obj_type = 'order'
            else:
                obj_type = object_type[:-1].replace('_', '')
            app_log = AppLog.objects.create(
                **{'workspace': workspace, obj_type: obj})
            app_log.user = request.user
            app_log.action = "send_email"
            app_log.field_name = to_email
            app_log.save()

        if module_slug:
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
        return redirect(reverse('main', host='app'))


def email_customer_handler(request):
    data = {}
    if request.POST.get('contact_and_company'):
        customer = None
        customer_email = None
        for contact_and_company_id in request.POST.getlist('contact_and_company'):
            if Contact.objects.filter(id=contact_and_company_id):
                customer = Contact.objects.get(id=contact_and_company_id)
                customer_email = customer.email

            elif Company.objects.filter(id=contact_and_company_id):
                customer = Company.objects.get(id=contact_and_company_id)
                customer_email = customer.email
        data['email_customer'] = customer_email
        print(customer_email)

        return JsonResponse(data)
    return HttpResponse(404)


@login_or_hubspot_required
@require_GET
def autocomplete_receipt(request):
    search = request.GET.get('search', '')
    page = int(request.GET.get('page', 1))
    workspace = get_workspace(request.user)

    filter_conditions = Q(workspace=workspace) & Q(usage_status='active')

    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_RECEIPT, user=request.user)
    filter_conditions &= get_permission_filter(permission, request.user,permission_type='edit')

    if search:
        filter_conditions &= (
            Q(id_rcp__icontains=search) |
            Q(contact__name__icontains=search) |
            Q(company__name__icontains=search)
        )

    receipts = Receipt.objects.filter(filter_conditions).distinct().order_by('-created_at')

    # Pagination
    paginator = Paginator(receipts, 10)
    try:
        receipts_page = paginator.page(page)
    except EmptyPage:
        receipts_page = paginator.page(paginator.num_pages)

    results = []
    for receipt in receipts_page:
        customer_name = ''
        if receipt.contact:
            customer_name = receipt.contact.name
        elif receipt.company:
            customer_name = receipt.company.name

        results.append({
            'id': receipt.id,
            'text': f"#{receipt.id_rcp} - {customer_name}"
        })

    return JsonResponse({
        'results': results,
        'pagination': {
            'more': receipts_page.has_next()
        }
    })


@login_or_hubspot_required
def commerce_row_detail(request, id, object_type=None):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    module_slug = request.GET.get('module', '')
    isopen = request.GET.get('isopen', '')

    page_obj = get_page_object(object_type, lang)
    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    manage_obj_link = page_obj['manage_obj_link']
    id_field = page_obj['id_field']  # id_inv, id_est, id_rcp, id_ds etc

    commerce = get_object_or_404(base_model, id=id)

    view_id = request.GET.get('view_id', None)
    if view_id:
        view = View.objects.filter(id=view_id).first()
    else:
        # Create Main View
        view, _ = View.objects.get_or_create(
            workspace=workspace, title__isnull=True, target=object_type)
        view_id = view.id

    view_filter, _ = ViewFilter.objects.get_or_create(view=view)

    # Handle case where view_filter.column is None to prevent ValueError
    try:
        columns = ast.literal_eval(view_filter.column) if view_filter and view_filter.column else page_obj['default_columns']
    except (ValueError, SyntaxError):
        # If literal_eval fails, try to parse as comma-separated string or use default
        if isinstance(view_filter.column, list):
            columns = view_filter.column
        else:
            columns = [col.strip() for col in view_filter.column.split(',')] if view_filter and view_filter.column else page_obj['default_columns']

    # Ensure columns is a list and add checkbox if not present
    if not isinstance(columns, list):
        columns = page_obj['default_columns']

    if 'checkbox' not in columns:
        columns.insert(0, 'checkbox')

    COMMERCE_STATUS = None
    SLIP_TYPE = None

    decimal_point = ShopTurboDecimalPoint.objects.filter(
        workspace=workspace, app_name='general').first()
    
    # # NOTE: Update this for invoice
    if object_type == TYPE_OBJECT_INVOICE:
        from data.invoice.utils.invoice_total_amount_fix import invoice_total_fix
        commerce = invoice_total_fix(commerce, workspace)
    elif object_type == TYPE_OBJECT_ESTIMATE:
        from data.estimate.utils.estimate_total_amount_fix import estimate_total_fix
        commerce = estimate_total_fix(commerce, workspace)

    if object_type == TYPE_OBJECT_SLIP:
        SLIP_TYPE = base_model._meta.get_field('slip_type').choices
    else:
        COMMERCE_STATUS = base_model._meta.get_field('status').choices
    context = {
        'manage_obj_link': manage_obj_link,
        'commerce': commerce,
        'obj_id': getattr(commerce, id_field, None),
        'columns': columns,
        'view_id': view_id,
        'selected_id': request.GET.get('selected_id'),
        'page': request.GET.get('page', 1),
        'NameCustomField': custom_model.objects.filter(workspace=workspace).order_by("order"),
        'module': module_slug,
        'object_type': object_type,
        'SLIP_TYPE': SLIP_TYPE,
        'COMMERCE_STATUS': COMMERCE_STATUS,
        'orders': ShopTurboOrders.objects.filter(workspace=workspace),
        'decimal_point': decimal_point,
        'isopen': isopen
    }

    if object_type == TYPE_OBJECT_SLIP:
        if view.form:
            context['set_id'] = str(view.form.id)

    return render(request, 'data/commerce/commerce-row-detail.html', context)


def commerce_get_form(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    set_id = request.GET.get('set_id', None)
    view_id = request.GET.get('view_id', None)
    object_type = request.GET.get('object_type', None)
    form_id = None
    if view_id and view_id != "None":
        try:
            # Validate that view_id is a valid UUID
            uuid.UUID(view_id)
            view = View.objects.get(id=view_id)
            if view.form:
                form_id = str(view.form.id)
        except (ValueError, TypeError, View.DoesNotExist):
            # If view_id is not a valid UUID or view doesn't exist, skip
            pass
    input_type = None
    set = None

    page_obj = get_page_object(object_type, lang)

    if set_id:
        try:
            set = PropertySet.objects.get(id=set_id)
        except:
            pass
    context = {
        'object_type': object_type,
        'form_id': form_id
    }
    if set:
        context['set'] = set

    properties = get_properties_with_details(object_type, workspace, lang)
    if properties:
        context['properties'] = properties
    required_properties = page_obj['required_properties']
    if required_properties:
        context['required_properties'] = required_properties

    return render(request, 'data/commerce/commerce-form-partial-add.html', context)


def commerce_form_update(request, view_id):
    if request.method == 'POST':
        as_default = request.POST.get('property_set_default')
        if as_default:
            try:
                if view_id and view_id != "None":
                    # Validate that view_id is a valid UUID
                    uuid.UUID(view_id)
                    view = View.objects.get(id=view_id)
                    prop_ = PropertySet.objects.get(id=as_default)
                    view.form = prop_
                    view.save()
            except Exception as e:
                print("Error_commerce_form_update: ", e)
    return True


def customer_property_check(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if request.method == 'GET':
        action_index = request.GET.get('action_index', '')
        action_index = '-' + action_index
        selected_contact_property_due_date = request.GET.get(
            'selected_contact_property_due_date', '')
        selected_company_property_due_date = request.GET.get(
            'selected_company_property_due_date', '')
        selected_specific_customer = request.GET.get(
            'selected_specific_customer', '')
        selected_property_due_date = request.GET.get(
            'selected_property_due_date', '')
        selected_date = request.GET.get('selected_date', '')
        selected_date_range_select_month = request.GET.get(
            'selected_date_range_select_month', '')
        contact_property_due_date = request.GET.get(
            'contact_property_due_date' + action_index, None)
        company_property_due_date = request.GET.get(
            'company_property_due_date' + action_index, None)
        if contact_property_due_date and contact_property_due_date != "''" or company_property_due_date and company_property_due_date != "''":
            context = {
                'action_index': action_index[1:],
                'selected_property_due_date': selected_property_due_date,
            }
            return render(request, 'data/commerce/action/action-choice-customer-property.html', context)

        customer = request.GET.get('customer' + action_index, '')
        customer_type = ''
        data = {
            'slug': '',
            'display': ''
        }
        specific_customers = [data]
        contacts_property = [data]
        companies_property = [data]

        company_custom_field_name = CompanyNameCustomField.objects.filter(
            workspace=workspace, type="billing_cycle").order_by('name')
        contact_custom_field_name = ContactsNameCustomField.objects.filter(
            workspace=workspace, type="billing_cycle").order_by('name')

        if 'contact' in customer:
            customer_type = 'contact'
        elif 'company' in customer:
            customer_type = 'company'
        elif 'customer' in customer:
            customer_type = 'customer'

        for name in contact_custom_field_name:
            if lang == 'ja':
                data = {
                    'slug': 'customer_contact_' + str(name.id),
                    'display': f'連絡先プロパティ - {name.name}'
                }
            else:
                data = {
                    'slug': 'customer_contact_' + str(name.id),
                    'display': f'Contact Property - {name.name}'
                }
            contacts_property.append(data)

        for name in company_custom_field_name:
            if lang == 'ja':
                data = {
                    'slug': 'customer_company_' + str(name.id),
                    'display': f'企業プロパティ - {name.name}'
                }
            else:
                data = {
                    'slug': 'customer_company_' + str(name.id),
                    'display': f'Company Property - {name.name}'
                }
            companies_property.append(data)

        contacts_list = Contact.objects.filter(
            workspace=workspace, status="active").order_by('-contact_id')
        company_list = Company.objects.filter(
            workspace=workspace, status="active").order_by('-company_id')

        if customer_type == 'company':
            for company in company_list:
                data_company = {
                    'slug': str(company.id)
                }
                data_company['display'] = customer_converter(
                    company, lang, with_tag=True)
                specific_customers.append(data_company)

        elif customer_type == 'contact':
            for contact in contacts_list:
                data_contact = {
                    'slug': str(contact.id)
                }
                data_contact['display'] = customer_converter(
                    contact, lang, with_tag=True)
                specific_customers.append(data_contact)

        context = {
            'action_index': action_index[1:],
            'customer_type': customer_type,
            'customer': customer,

            'selected_date_range_select_month': selected_date_range_select_month,
            'selected_property_due_date': selected_property_due_date,
            'selected_date': selected_date,
            'selected_specific_customer': selected_specific_customer,
            'selected_contact_property_due_date': selected_contact_property_due_date,
            'selected_company_property_due_date': selected_company_property_due_date,
            'contacts_property': contacts_property,
            'companies_property': companies_property,
            'specific_customers': specific_customers,
        }
        return render(request, 'data/commerce/action/action-customer-property.html', context)


@login_or_hubspot_required  # NOTE: Applied on Workflow aswel, Handled by Faris
def aggregate_commerce_action(request, object_type=None, convert_to=None):
    """
    Aggregates multiple commerce objects (such as invoices, orders, or estimates) for one or more customers, supporting both direct user actions and workflow automation.
    
    On GET requests, renders a form for selecting customers, date ranges, and aggregation options, including support for workflow-driven actions. On POST requests, aggregates selected or filtered objects by customer and date, creates combined objects (e.g., a single invoice from multiple orders), manages workflow or action history, and handles notifications and error reporting. Supports conversion between object types, custom property-based customer selection, and integration with workflow automation.
    
    Returns rendered forms, redirects to result pages, or HTTP error responses depending on the operation and outcome.
    """
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    # Only override object_type from POST if it's explicitly provided and we're in a POST request
    # This prevents unintended parameter overrides that could cause incorrect processing
    if request.method == 'POST' and 'object_type' in request.POST and request.POST.get('object_type'):
        object_type = request.POST.get('object_type')
        convert_to = object_type
    page_obj = get_page_object(object_type, lang)
    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    field_item_name = page_obj['field_item_name']
    id_field = page_obj['id_field']
    display_name = page_obj['pdf_title']
    custom_value_model = page_obj['custom_value_model']
    object_combined = None
    selected_ids = None

    module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
    module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by(
        'order', 'created_at').first()
    module_slug = None

    if module:
        module_slug = module.slug

    if request.method == 'GET':
        customer = None
        selected_date = None
        selected_company_property_due_date = None
        selected_specific_customer = None
        selected_contact_property_due_date = None
        selected_property_due_date = None
        selected_specific_due_date = None
        selected_date_select_option_1 = None
        selected_date_range_select_option_1 = None
        selected_date_range_select_month = None
        selected_status = None
        selected_due_date = None
        selected_issue_date = None
        use_date_range = None
        tax_option = None
        use_account_rec = None
        selected_ids = None
        action_index = request.GET.get('action_index')
        action_node_id = request.GET.get('action_node_id')

        postfix = ''
        if action_index:
            postfix = '-' + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print('Action node does not exist')
                return HttpResponse(status=404)
        is_object_action = True
        if postfix:
            is_object_action = False
            if node and node.input_data:
                if 'customer' in node.input_data:
                    customer = node.input_data['customer']
                if 'date_select' in node.input_data:
                    selected_date = node.input_data['date_select']
                if 'specific_customer' in node.input_data:
                    selected_specific_customer = node.input_data['specific_customer']
                if 'contact_property_due_date' in node.input_data:
                    selected_contact_property_due_date = node.input_data['contact_property_due_date']
                if 'company_property_due_date' in node.input_data:
                    selected_company_property_due_date = node.input_data['company_property_due_date']
                if 'property_due_date' in node.input_data:
                    selected_property_due_date = node.input_data['property_due_date']
                if 'specific_due_date' in node.input_data:
                    selected_specific_due_date = node.input_data['specific_due_date']
                if 'date_range_select_month' in node.input_data:
                    selected_date_range_select_month = node.input_data['date_range_select_month']
                if 'use_date_range' in node.input_data:
                    use_date_range = node.input_data['use_date_range']
                if 'use_account_rec' in node.input_data:
                    use_account_rec = node.input_data['use_account_rec']
                if 'tax_option' in node.input_data:
                    tax_option = node.input_data['tax_option']
                if 'status' in node.input_data:
                    selected_status = node.input_data['status']
                if 'issue_date' in node.input_data:
                    selected_issue_date = node.input_data['issue_date']
        else:
            selected_ids = request.GET.get('selected_ids', '[]')
            if selected_ids == '[]':
                selected_ids = request.GET.get('order_ids', '[]')
            if '[' in selected_ids and ']' in selected_ids:
                selected_ids = ast.literal_eval(selected_ids)
            else:
                selected_ids = [selected_ids]

            if object_type is None:
                object_type = request.GET.get('page_group_type')
            page_obj = get_page_object(object_type, lang)
            base_model = page_obj['base_model']
            id_field = page_obj['id_field']
        data = {
            'slug': '',
            'display': ''
        }

        customers_data = [
            {
                'slug': 'contact',
                'display': '顧客 (連絡先)' if lang == 'ja' else 'Customer (Contact)'
            },
            {
                'slug': 'company',
                'display': '顧客 (企業)' if lang == 'ja' else 'Customer (Company)'
            }
        ]

        custom_field_name = custom_model.objects.filter(
            workspace=workspace).order_by('name')
        for name in custom_field_name:
            if name.type in ["contact", "company"]:
                if lang == 'ja':
                    if name.type == "contact":
                        type_ = "連絡先"
                    data = {
                        'slug': object_type.lower() + '_' + name.type + '_' + str(name.id),
                        'display': f'{display_name} プロパティ ({type_}) - {name.name}'
                    }
                else:
                    data = {
                        'slug': object_type.lower() + '_' + name.type + '_' + str(name.id),
                        'display': f'{display_name} Property ({name.type.capitalize()}) - {name.name}'
                    }
                customers_data.append(data)

        if selected_date:
            try:
                if lang == 'ja':
                    # Parse date range and convert to Japanese format
                    date_start, date_end = selected_date.split(' - ')
                    less_date = datetime.strptime(date_start, '%Y-%m-%d')
                    greater_date = datetime.strptime(date_end, '%Y-%m-%d')

                    # Format both dates in Japanese style
                    less_date_jp = f"{less_date.year}年{less_date.month:02d}月{less_date.day:02d}日"
                    greater_date_jp = f"{greater_date.year}年{greater_date.month:02d}月{greater_date.day:02d}日"

                    # Combine with Japanese range separator
                    selected_date = f"{less_date_jp} 〜 {greater_date_jp}"
            except Exception as e:
                print("Error with error: ", e)
                pass

        context = {
            'is_object_action': is_object_action,
            'action_index': action_index,
            'customer': customer,

            'selected_ids': selected_ids,
            'selected_date': selected_date,
            'selected_contact_property_due_date': selected_contact_property_due_date,
            'selected_company_property_due_date': selected_company_property_due_date,
            'selected_property_due_date': selected_property_due_date,
            'selected_specific_customer': selected_specific_customer,
            'selected_date_range_select_month': selected_date_range_select_month,

            'selected_date_select_option_1': selected_date_select_option_1,
            'selected_date_range_select_option_1': selected_date_range_select_option_1,
            'use_date_range': use_date_range,
            'use_account_rec': use_account_rec,
            'tax_option': tax_option,
            'tax_option': tax_option,
            'object_type': object_type,
            'convert_to': convert_to,
            'selected_due_date': selected_due_date,
            'selected_issue_date': selected_issue_date,
            'selected_status': selected_status,
            'obj': Invoice(workspace=workspace) if object_type == TYPE_OBJECT_INVOICE else None,

            'customers_data': customers_data,
            'is_record_action': request.GET.get('is_record_action'),
            'action_id': request.GET.get('action_id'),
        }
        return render(request, 'data/commerce/action/aggregate-object-action.html', context)

    if request.method == 'POST':
        submit_option = request.POST.get('submit-option')
        action_index = request.POST.get('action_index')
        action_node_id = request.POST.get('action_node_id')
        at_id = request.POST.get('action_tracker_id')
        wat_id = request.POST.get('workflow_action_tracker_id')
        selected_ids = request.POST.get('selected_ids', None)
        if selected_ids:
            selected_ids = ast.literal_eval(selected_ids)
            submit_option = 'run'
        node = None

        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                print('ActionNode does not exist')
                return HttpResponse(status=404)
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                print('ActionTracker does not exist')
                return HttpResponse(status=404)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                print('WorkflowActionTracker does not exist')
                return HttpResponse(status=404)

        postfix = ''
        if action_index:
            postfix = '-' + action_index

        action_slug = request.POST.get('action'+postfix)
        is_object_action = request.POST.get('is_object_action', None)
        is_record_action = request.POST.get('is_record_action', None)
        # print(request.POST)

        customer = request.POST.get('customer' + postfix, '')
        date_select = request.POST.get('date_select' + postfix, '')
        try:
            specific_customer = request.POST.getlist(
                'specific_customer' + postfix, '')
        except:
            pass
        contact_property_due_date = request.POST.get(
            'contact_property_due_date' + postfix, '')
        company_property_due_date = request.POST.get(
            'company_property_due_date' + postfix, '')
        property_due_date = request.POST.get('property_due_date' + postfix, '')
        specific_due_date = request.POST.get('specific_due_date' + postfix, '')
        use_date_range = request.POST.get('use_date_range' + postfix, '')
        tax_option = request.POST.get('tax_option' + postfix, '')
        status = request.POST.get('selected_status' + postfix, '')
        due_date = request.POST.get('selected_due_date' + postfix, '')
        issue_date = request.POST.get('selected_issue_date' + postfix, '')
        use_account_rec = request.POST.get('use_account_rec' + postfix, '')
        date_range_select_month = request.POST.get(
            'date_range_select_month' + postfix, '')

        if request.POST.get('object_type'+postfix):
            object_type = request.POST.get('object_type'+postfix)
        object_name = request.POST.get('object_name'+postfix)

    try:
        if submit_option == 'save':
            input_data = {}
            node.valid_to_run = True

            if customer:
                input_data['customer'] = customer
            if date_select:
                # 2024-6-22 - 2024-6-24
                if ' - ' in date_select:
                    date_start = date_select.split(' - ')[0]
                    date_end = date_select.split(' - ')[1]
                    less_date = date_start
                    greater_date = date_end
                else:
                    date_start = date_select.split(' 〜 ')[0]
                    date_end = date_select.split(' 〜 ')[1]
                    less_date = datetime.strptime(
                        date_start, '%Y年%m月%d日').date().strftime('%Y-%m-%d')
                    greater_date = datetime.strptime(
                        date_end, '%Y年%m月%d日').date().strftime('%Y-%m-%d')

                try:
                    date_select = less_date + ' - ' + greater_date
                except Exception as e:
                    print("error: ", e)
                input_data['date_select'] = date_select

            if specific_customer:
                input_data['specific_customer'] = specific_customer
            if contact_property_due_date:
                input_data['contact_property_due_date'] = contact_property_due_date
            if company_property_due_date:
                input_data['company_property_due_date'] = company_property_due_date
            if specific_due_date:
                input_data['specific_due_date'] = specific_due_date
            if property_due_date:
                input_data['property_due_date'] = property_due_date
            if use_date_range:
                input_data['use_date_range'] = use_date_range
            if use_account_rec:
                input_data['use_account_rec'] = use_account_rec
            if status:
                input_data['status'] = status
            if due_date:
                input_data['due_date'] = due_date
            if issue_date:
                input_data['issue_date'] = issue_date
            if tax_option:
                print("saving Tax: ", tax_option)
                input_data['tax_option'] = tax_option
            if date_range_select_month:
                input_data['date_range_select_month'] = date_range_select_month
            if not customer and not date_select:
                print('All Informations need to be defined.')
                node.valid_to_run = False

            if object_type:
                input_data['object_type'] = object_type
            if object_name:
                input_data['object_name'] = object_name

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            print(node.valid_to_run)
            if module_slug:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
            return redirect(reverse('main', host='app'))
        else:
            specific_due_date = None
            property_due_date = None
            specific_customer = None
            tax_option = None
            use_date_range = None
            use_account_rec = False
            exclude_status = None

            customer = None
            customer_objects = []
            objects_dict_list = {}

            customer_object_value_list = []
            specific_cust = []
            type_ = None

            if submit_option == 'run':
                if not selected_ids:
                    if 'customer' in node.input_data:
                        customer = node.input_data['customer']
                    if 'date_select' in node.input_data:
                        date_select = node.input_data['date_select']
                    if 'specific_customer' in node.input_data:
                        specific_customer = node.input_data['specific_customer']
                    if 'contact_property_due_date' in node.input_data:
                        contact_property_due_date = node.input_data['contact_property_due_date']
                    if 'company_property_due_date' in node.input_data:
                        company_property_due_date = node.input_data['company_property_due_date']
                    if 'property_due_date' in node.input_data:
                        property_due_date = node.input_data['property_due_date']
                    if 'specific_due_date' in node.input_data:
                        specific_due_date = node.input_data['specific_due_date']
                    if 'date_range_select_month' in node.input_data:
                        date_range_select_month = node.input_data['date_range_select_month']
                    if 'use_date_range' in node.input_data:
                        use_date_range = node.input_data['use_date_range']
                    if 'use_account_rec' in node.input_data:
                        use_account_rec = node.input_data['use_account_rec']
                    if 'status' in node.input_data:
                        status = node.input_data['status']
                    if 'due_date' in node.input_data:
                        due_date = node.input_data['due_date']
                    if 'issue_date' in node.input_data:
                        issue_date = node.input_data['issue_date']
                    if 'tax_option' in node.input_data:
                        tax_option = node.input_data['tax_option']

            if is_object_action or is_record_action:
                action = Action.objects.filter(slug=action_slug).first()
                history = ActionHistory.objects.create(
                    workspace=workspace,
                    action=action,
                    status='initialized',
                    created_by=request.user
                )
            if is_record_action:
                history.object_id = selected_ids[0]
                history.object_type = object_type
                history.save()

            if not selected_ids:
                ## AGGREGATE MAIN ACTION ##
                customer_id = None
                if specific_customer:
                    customer_id = specific_customer
                customer_data = None
                customer_object = None
                object_combined = None
                customer_name = None

                if use_account_rec:
                    use_account_rec = True

                workflow_history = node.workflow_history

                if object_type == TYPE_OBJECT_INVOICE and not has_quota(workspace, INVOICE_USAGE_CATEGORY):
                    msg = "Invoice could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing invoices to free up space."
                    if lang == 'ja':
                        msg = "制限を超えたため、請求書を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存の請求書の一部をアーカイブしてスペースを解放してください。"
                    Notification.objects.create(
                        workspace=workspace, user=request.user, message=msg, type="error")
                    return HttpResponse(status=500)

            try: 
                if not selected_ids:
                    if customer_id:  # First is used for aggregate and get list customer_objects
                        if type(customer_id) == str:
                            customer_id = customer_id.split(',')

                        for customers_ in customer_id:
                            customer_object = Contact.objects.filter(
                                id=customers_)
                            if customer_object:
                                customer_object = customer_object.first()
                            else:
                                customer_object = Company.objects.filter(
                                    id=customers_)
                                if customer_object:
                                    customer_object = customer_object.first()
                            customer_objects.append(customer_object)

                    if use_date_range:
                        # 2024-6-22 - 2024-6-24
                        if ' - ' in date_select:
                            date_start, date_end = date_select.split(' - ')
                            less_date = date_start
                            greater_date = date_end
                        else:
                            date_start, date_end = date_select.split(' 〜 ')
                            less_date = datetime.strptime(
                                date_start, '%Y年%m月%d日').date().strftime('%Y-%m-%d')
                            greater_date = datetime.strptime(
                                date_end, '%Y年%m月%d日').date().strftime('%Y-%m-%d')

                    else:  # Use Billing Cycle
                        try:
                            month, year = date_range_select_month.split('-')
                            date_select = f"{year}-{month}-{property_due_date}"
                        except Exception as e:
                            traceback.print_exc()
                            print("Error In Date Range: ", e)
                            send_discord_notification(
                                'aggregate_commerce_action', traceback.format_exc())

                            if lang == 'ja':
                                at.input_data['error_messages'] = '日付範囲を入力する必要があります'
                                Notification.objects.create(
                                    workspace=workspace, user=request.user, message=f"アクションの実行に失敗しました: {workflow_history.title}", type="error")
                            else:
                                at.input_data['error_messages'] = 'Date Range need to be filled'
                                Notification.objects.create(
                                    workspace=workspace, user=request.user, message=f"Failed Running Action: {workflow_history.title}", type="error")
                            at.status = "failed"
                            at.error_message = str(e)
                            at.save()
                            print(at.error_message)
                            return HttpResponse(status=500)

                        try:
                            if property_due_date == 'end':
                                date_select = f"{year}-{month}-1"
                                less_date = datetime.strptime(
                                    date_select, '%Y-%m-%d') - relativedelta(months=1)
                                greater_date = datetime.strptime(
                                    date_select, '%Y-%m-%d') + relativedelta(months=1) - timezone.timedelta(days=1)
                            else:
                                less_date = datetime.strptime(
                                    date_select, '%Y-%m-%d') - relativedelta(months=1) + timezone.timedelta(days=1)
                                greater_date = datetime.strptime(
                                    date_select, '%Y-%m-%d')

                        except Exception as e:
                            traceback.print_exc()
                            print("Error: ", e)
                            send_discord_notification(
                                'aggregate_commerce_action', traceback.format_exc())

                            if lang == 'ja':
                                at.input_data['error_messages'] = '日付形式は無効です'
                                Notification.objects.create(
                                    workspace=workspace, user=request.user, message=f"アクションの実行に失敗しました: {workflow_history.title}", type="error")
                            else:
                                at.input_data['error_messages'] = 'Date Format is invalid'
                                Notification.objects.create(
                                    workspace=workspace, user=request.user, message=f"Failed Running Action: {workflow_history.title}", type="error")
                            at.status = "failed"
                            at.error_message = str(e)
                            at.save()
                            print(at.error_message)
                            return HttpResponse(status=500)

                        less_date = less_date.strftime('%Y-%m-%d')
                        greater_date = greater_date.strftime('%Y-%m-%d')

                        date_select = less_date + ' - ' + greater_date

                        print("save updated date select: ", date_select)
                        if 'date_select' in at.input_data:
                            at.input_data['date_select'] = date_select
                        at.save()

                    if specific_due_date:
                        try:
                            specific_due_date = datetime.strptime(
                                specific_due_date, '%Y-%m-%d')
                        except:
                            pass
                    if due_date:
                        try:
                            due_date = datetime.strptime(
                                due_date, '%Y-%m-%d')
                        except:
                            pass

                    if issue_date:
                        try:
                            issue_date = datetime.strptime(
                                issue_date, '%Y-%m-%d')
                        except:
                            pass

                    less_date = datetime.strptime(less_date, '%Y-%m-%d')
                    greater_date = datetime.strptime(greater_date, '%Y-%m-%d')

                    # Filtering objects Based on Start And End Date
                    if object_type == TYPE_OBJECT_ORDER:
                        query = Q(
                            workspace=workspace, order_at__gte=less_date, order_at__lte=greater_date)
                        if selected_ids:
                            query &= Q(id__in=selected_ids)
                        objects_ = base_model.objects.filter(query).exclude(
                            status="archived").order_by(id_field)
                    else:
                        query = Q(
                            workspace=workspace, start_date__gte=less_date, start_date__lte=greater_date)
                        if selected_ids:
                            query &= Q(id__in=selected_ids)
                        objects_ = base_model.objects.filter(query).exclude(
                            usage_status="archived").order_by(id_field)

                else:
                    # When no date range is specified, only filter by selected_ids
                    query = Q(workspace=workspace)
                    if selected_ids:
                        query &= Q(id__in=selected_ids)
                    objects_ = base_model.objects.filter(query)

                if exclude_status:
                    objects_ = objects_.exclude(
                        status__in=ast.literal_eval(exclude_status))

                print("Getting Objects: ", len(objects_))

                # Start Filtering customer related obj -> return objects_dict_list and customer_object_value_list
                for obj_ in objects_:
                    customer_obj = None
                    contact_customers_ = None
                    company_customers_ = None

                    # Common action for Due date set
                    if contact_property_due_date:
                        _, n, customer_property_data = contact_property_due_date.split(
                            '_')
                        if is_valid_uuid(customer_property_data):
                            contact_property_name = ContactsNameCustomField.objects.get(
                                id=customer_property_data)

                    if company_property_due_date:
                        _, n, customer_property_data = company_property_due_date.split(
                            '_')
                        if is_valid_uuid(customer_property_data):
                            company_property_name = CompanyNameCustomField.objects.get(
                                id=customer_property_data)

                    try:
                        if customer in ['contact', 'company']:
                            type_ = customer
                            if customer == 'contact':
                                if obj_.contact:
                                    customer_obj = obj_.contact
                            if customer == 'company':
                                if obj_.company:
                                    customer_obj = obj_.company
                        else:
                            # Getting obj from custom property value, also zengin action use this , example data ("invoice_contact_#uuid")
                            if customer:
                                _, type_, customer_data = customer.split('_')
                                if is_valid_uuid(customer_data):
                                    customer_name_ = custom_model.objects.get(
                                        id=customer_data)
                                    at.input_data['customer'] = customer_name_.name
                                    at.save()
                                customer_ = custom_value_model.objects.filter(
                                    **{'field_name': customer_name_, field_item_name: obj_}).first()

                                if customer_.value:
                                    if type_ == 'contact':
                                        customer_obj = Contact.objects.filter(
                                            id=customer_.value).first()
                                    if type_ == 'company':
                                        customer_obj = Company.objects.filter(
                                            id=customer_.value).first()
                            else:
                                customer_obj = obj_.contact if obj_.contact else obj_.company

                        if customer_obj:  # Start checking each of obj with related customer data
                            if selected_ids:
                                if customer_obj not in customer_object_value_list:
                                    customer_object_value_list.append(
                                        customer_obj)
                                if str(customer_obj.id) in objects_dict_list:
                                    objects_dict_list[str(customer_obj.id)].append(
                                        str(obj_.id))
                                else:
                                    objects_dict_list[str(customer_obj.id)] = [
                                        str(obj_.id)]
                            elif customer_obj._meta.model_name == 'contact' and contact_property_due_date:
                                contact_customers_ = ContactsValueCustomField.objects.filter(
                                    field_name=contact_property_name, contact=customer_obj).first()
                                if contact_customers_:
                                    value_due_date = contact_customers_.value
                                    if value_due_date == property_due_date or not property_due_date:
                                        if customer_obj not in customer_object_value_list:
                                            customer_object_value_list.append(
                                                customer_obj)
                                        if str(customer_obj.id) in objects_dict_list:
                                            objects_dict_list[str(customer_obj.id)].append(
                                                str(obj_.id))
                                        else:
                                            objects_dict_list[str(customer_obj.id)] = [
                                                str(obj_.id)]
                            elif customer_obj._meta.model_name == 'company' and company_property_due_date:
                                company_customers_ = CompanyValueCustomField.objects.filter(
                                    field_name=company_property_name, company=customer_obj).first()
                                if company_customers_:
                                    value_due_date = company_customers_.value
                                    if value_due_date == property_due_date or not property_due_date:
                                        if customer_obj not in customer_object_value_list:
                                            customer_object_value_list.append(
                                                customer_obj)
                                        if str(customer_obj.id) in objects_dict_list:
                                            objects_dict_list[str(customer_obj.id)].append(
                                                str(obj_.id))
                                        else:
                                            objects_dict_list[str(customer_obj.id)] = [
                                                str(obj_.id)]

                            # this Also used in Zengin Action
                            elif specific_customer and not company_property_due_date and not contact_property_due_date or not specific_customer:
                                if customer_obj:
                                    if customer_obj not in customer_object_value_list:
                                        customer_object_value_list.append(
                                            customer_obj)
                                    if str(customer_obj.id) in objects_dict_list:
                                        objects_dict_list[str(customer_obj.id)].append(
                                            str(obj_.id))
                                    else:
                                        objects_dict_list[str(customer_obj.id)] = [
                                            str(obj_.id)]
                            else:
                                if lang == 'ja':
                                    at.input_data['error_messages'] = '顧客情報を設定する必要があります'
                                    Notification.objects.create(
                                        workspace=workspace, user=request.user, message=f"アクションの実行に失敗しました: {workflow_history.title}", type="error")
                                else:
                                    at.input_data['error_messages'] = 'You need to set the customer information'
                                    Notification.objects.create(
                                        workspace=workspace, user=request.user, message=f"Failed Running Action: {workflow_history.title}", type="error")
                                at.status = "failed"
                                at.error_message = 'You need to set the customer information'
                                at.save()
                                print("Error 1")
                                return HttpResponse(status=500)

                    except Exception as e:
                        traceback.print_exc()
                        send_discord_notification(
                            'aggregate_commerce_action - 4794', traceback.format_exc())
                        print("Error: ", e)

                if is_object_action or is_record_action:
                    transfer_history = ActionTaskHistory.objects.create(
                        workspace=history.workspace,
                        action_history=history,
                        status='initialized',
                    )
                if customer_object_value_list:  # Start Aggregating based on customer data
                    print('Start Aggregating based on customer data: ',
                          customer_object_value_list, customer_objects)
                    object_combined = None
                    for i, customers_ in enumerate(customer_object_value_list):
                        try:
                            if customer_objects:
                                if customers_ not in customer_objects:
                                    continue
                            customer_name = customer_converter(
                                customers_, lang, with_tag=True)
                            specific_cust.append(customer_name)

                            convert_to_invoice = False
                            if object_type == TYPE_OBJECT_ORDER and convert_to == TYPE_OBJECT_INVOICE:
                                id_field = 'id_inv'
                                msg, object_combined, amount_aggregated = aggregate_order(
                                    listed_dict=objects_dict_list, object_type=object_type, customers_=customers_, due_date=specific_due_date, lang=lang, convert_to_invoice=True, use_account_rec=use_account_rec, tax_option=tax_option)
                            elif object_type == TYPE_OBJECT_ORDER and convert_to == TYPE_OBJECT_PURCHASE_ORDER:
                                id_field = 'id_po'
                                msg, object_combined, amount_aggregated = aggregate_commerce_object(listed_dict=objects_dict_list, object_type=object_type, customers_=customers_, due_date=specific_due_date,
                                                                                                    lang=lang, convert_to_invoice=convert_to_invoice, use_account_rec=use_account_rec, tax_option=tax_option, convert_to_purchase_order=True)
                            else:
                                if object_type == TYPE_OBJECT_ESTIMATE:
                                    convert_to_invoice = False
                                else:
                                    convert_to_invoice = True
                                msg, object_combined, amount_aggregated = aggregate_commerce_object(
                                    listed_dict=objects_dict_list, object_type=object_type, customers_=customers_, due_date=specific_due_date, lang=lang, convert_to_invoice=convert_to_invoice, use_account_rec=use_account_rec, tax_option=tax_option)
                            print('MSG: ', msg, object_combined,
                                  amount_aggregated)
                            if msg == 'Success':
                                if object_combined:
                                    if convert_to:
                                        module_object_slug = OBJECT_TYPE_TO_SLUG[convert_to]
                                        module = Module.objects.filter(workspace=workspace, object_values__contains=convert_to).order_by(
                                            'order', 'created_at').first()
                                        module_slug = None
                                        if module:
                                            module_slug = module.slug
                                        object_type = convert_to

                                    if convert_to_invoice:
                                        id_field = 'id_inv'
                                        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVOICE]
                                        module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_INVOICE).order_by(
                                            'order', 'created_at').first()
                                        module_slug = None
                                        if module:
                                            module_slug = module.slug
                                        object_type = TYPE_OBJECT_INVOICE

                                        if selected_ids:
                                            url_ = bg_job_to_sanka_url(type_http + reverse('load_object_page', host='app', kwargs={
                                                                       'module_slug': module_slug, 'object_slug': module_object_slug}) + '?id=' + str(object_combined.id))
                                            if lang == 'en':
                                                Notification.objects.create(
                                                    workspace=workspace, user=request.user, message=f"Completed Action:  <a target='_blank' class='badge badge-info' href='{url_}'>Result</a>", type="success")
                                            else:
                                                Notification.objects.create(
                                                    workspace=workspace, user=request.user, message=f"アクションの完了: <a target='_blank' class='badge badge-info' href='{url_}'>結果</a>", type="success")

                                            if is_object_action or is_record_action:
                                                transfer_history.completed_at = timezone.now()
                                                transfer_history.status = 'success'
                                                transfer_history.result_link = url_
                                                transfer_history.save()
                                    if selected_ids:
                                        # Ensure we have valid module_slug and module_object_slug for redirect
                                        current_module_slug = module_slug
                                        current_module_object_slug = module_object_slug
                                        if not current_module_slug or not current_module_object_slug:
                                            # Fall back to original object_type if conversion variables are None
                                            current_module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
                                            current_module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by('order', 'created_at').first()
                                            if current_module:
                                                current_module_slug = current_module.slug

                                        url_ = bg_job_to_sanka_url(type_http + reverse('load_object_page', host='app', kwargs={
                                            'module_slug': current_module_slug, 'object_slug': current_module_object_slug}) + '?id=' + str(object_combined.id))

                                        if is_object_action or is_record_action:
                                            transfer_history.completed_at = timezone.now()
                                            transfer_history.status = 'success'
                                            transfer_history.result_link = url_
                                            transfer_history.save()

                                        if is_object_action or is_record_action:
                                            history.status = 'success'
                                            history.completed_at = timezone.now()
                                            history.save()
                                        if is_object_action:
                                            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': current_module_slug, 'object_slug': current_module_object_slug})+f"?id={str(object_combined.id)}&sidedrawer=action-history")
                                        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': current_module_slug, 'object_slug': current_module_object_slug}))
                                    if lang == 'en':
                                        Notification.objects.create(
                                            workspace=workspace, user=request.user, message=f"Success Run Action: {workflow_history.title}", type="success")
                                    else:
                                        Notification.objects.create(
                                            workspace=workspace, user=request.user, message=f"成功するアクション: {workflow_history.title}", type="success")

                                    at.input_data[str(
                                        i+1) + '_amount_aggregated (' + customer_name + ')'] = str(amount_aggregated)
                                    # Ensure we have valid module_slug and module_object_slug for URL generation
                                    current_module_slug = module_slug
                                    current_module_object_slug = module_object_slug
                                    if not current_module_slug or not current_module_object_slug:
                                        # Fall back to original object_type if variables are None
                                        current_module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
                                        current_module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by('order', 'created_at').first()
                                        if current_module:
                                            current_module_slug = current_module.slug

                                    if current_module_slug:
                                        url_ = bg_job_to_sanka_url(type_http + reverse('load_object_page', host='app', kwargs={
                                                                   'module_slug': current_module_slug, 'object_slug': current_module_object_slug}) + '?id=' + str(object_combined.id))
                                        at.input_data[str(i+1) + '_' + object_type.lower() + '_url (' + customer_name + ') - #' + str(
                                            getattr(object_combined, id_field, ''))] = url_
                                    else:
                                        url_ = bg_job_to_sanka_url(
                                            type_http + reverse('main', host='app'))
                                        at.input_data[str(i+1) + '_' + object_type.lower() + '_url (' + customer_name + ') - #' + str(
                                            getattr(object_combined, id_field, ''))] = url_
                                    if object_type in LIST_COMMERCE_OBJECT_TYPES:
                                        url_ = bg_job_to_sanka_url(
                                            type_http + reverse(object_type[:-1] + '_pdf_view', host='app', kwargs={'id': str(object_combined.id)}))
                                        at.input_data[str(i+1) + '_' + object_type.lower() + '_pdf_url (' + customer_name + ') - #' + str(
                                            getattr(object_combined, id_field, ''))] = url_

                                    at.status = 'success'
                                    at.completed_at = timezone.now()
                                    at.save()

                                    if is_object_action or is_record_action:
                                        transfer_history.completed_at = timezone.now()
                                        transfer_history.status = 'success'
                                        transfer_history.result_link = url_
                                        transfer_history.save()
                            else:
                                send_discord_notification(
                                    'aggregate_commerce_action - 4890', msg)
                                raise Exception(f"{msg}")

                        except Exception as e:
                            print('[Error saving items]: ', e)
                            send_discord_notification(
                                'aggregate_commerce_action - Traceback Error', traceback.format_exc())
                            traceback.print_exc()
                            if object_combined:
                                print('Existed2', object_combined.id)
                                object_combined.delete()
                            if not selected_ids:
                                if lang == 'ja':
                                    at.input_data['error_messages'] = str(e)
                                    Notification.objects.create(
                                        workspace=workspace, user=request.user, message=f"アクションの実行に失敗しました: {workflow_history.title}", type="error")
                                else:
                                    at.input_data['error_messages'] = str(e)
                                    Notification.objects.create(
                                        workspace=workspace, user=request.user, message=f"Failed Running Action: {workflow_history.title}", type="error")
                                at.status = "failed"
                                at.error_message = str(e)
                                at.save()
                                if 'specific_customer' in at.input_data:
                                    at.input_data['specific_customer'] = ", ".join(
                                        specific_cust)
                                    at.save()
                            print("Error 2")
                            return HttpResponse(status=500)

                    if is_object_action or is_record_action:
                        history.status = 'success'
                        history.completed_at = timezone.now()
                        history.save()

                else:
                    if object_combined:
                        print('Existed 3', object_combined.id)
                        object_combined.delete()
                    if lang == 'ja':
                        if at:
                            at.input_data['error_messages'] = f'関連する連絡先 /指定日に{display_name}はありません'
                            Notification.objects.create(
                                workspace=workspace, user=request.user, message=f"アクションの実行に失敗しました: {workflow_history.title}", type="error")
                    else:
                        if at:
                            at.input_data['error_messages'] = f'There is no {display_name} to the related contact / specified date'
                            Notification.objects.create(
                                workspace=workspace, user=request.user, message=f"Failed Running Action: {workflow_history.title}", type="error")
                    if at:
                        at.status = "failed"
                        at.error_message = f'There is no {display_name} to the related contact / specified date'
                        at.save()

                    if is_object_action or is_record_action:
                        # Ensure we have valid module_slug and module_object_slug for redirect
                        current_module_slug = module_slug
                        current_module_object_slug = module_object_slug
                        if module:
                            current_module_slug = module.slug
                        if not current_module_slug or not current_module_object_slug:
                            # Fall back to original object_type if variables are None
                            current_module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
                            current_module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by('order', 'created_at').first()
                            if current_module:
                                current_module_slug = current_module.slug

                        if is_object_action:
                            transfer_history.completed_at = timezone.now()
                            transfer_history.status = 'failed'
                            transfer_history.save()
                            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': current_module_slug, 'object_slug': current_module_object_slug}))
                        else:
                            history.status = 'failed'
                            history.completed_at = timezone.now()
                            history.save()
                            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': current_module_slug, 'object_slug': current_module_object_slug})+f"?id={selected_ids[0]}&sidedrawer=action-history")
                        return redirect(reverse('main', host='app'))
                    print("Error 3")
                    return HttpResponse(status=500)

                if at:
                    if 'specific_customer' in at.input_data:
                        at.input_data['specific_customer'] = ", ".join(
                            specific_cust)
                        at.save()

            except Exception as e:
                send_discord_notification(
                    'aggregate_commerce_action - 4945', traceback.format_exc())
                print(f"Aggregate error 2: {e}")
                traceback.print_exc()
                if submit_option == 'run':
                    if node and at:
                        at.input_data = {
                            f"{'エラーログ' if lang == 'ja' else 'Error Log'}": str(e),
                        }
                        at.output_data = {}
                        at.save()
                try:
                    if is_object_action or is_record_action:
                        history.status = 'failed'
                        history.completed_at = timezone.now()
                        history.save()
                        # Ensure we have valid module_slug and module_object_slug for redirect
                        current_module_slug = module_slug
                        current_module_object_slug = module_object_slug
                        if module:
                            current_module_slug = module.slug
                        if not current_module_slug or not current_module_object_slug:
                            # Fall back to original object_type if variables are None
                            current_module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
                            current_module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by('order', 'created_at').first()
                            if current_module:
                                current_module_slug = current_module.slug

                        if current_module_slug:
                            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': current_module_slug, 'object_slug': current_module_object_slug})+f'?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history')
                        return redirect(reverse('main', host='app'))
                except:
                    pass

                print("Error 4")
                return HttpResponse(status=500)

    except Exception as e:
        send_discord_notification(
            'aggregate_commerce_action - 4958', traceback.format_exc())
        print(f"Aggregate error 3: {e}")
        if submit_option == 'run':
            if node and at:
                at.input_data = {
                    f"{'エラーログ' if lang == 'ja' else 'Error Log'}": str(e),
                }
                at.output_data = {}
                at.save()

        if is_object_action or is_record_action:
            history.status = 'failed'
            history.completed_at = timezone.now()
            history.save()
            # Ensure we have valid module_slug and module_object_slug for redirect
            current_module_slug = module_slug
            current_module_object_slug = module_object_slug
            if module:
                current_module_slug = module.slug
            if not current_module_slug or not current_module_object_slug:
                # Fall back to original object_type if variables are None
                current_module_object_slug = OBJECT_TYPE_TO_SLUG[object_type]
                current_module = Module.objects.filter(workspace=workspace, object_values__contains=object_type).order_by('order', 'created_at').first()
                if current_module:
                    current_module_slug = current_module.slug

            if current_module_slug:
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': current_module_slug, 'object_slug': current_module_object_slug})+f'?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history')
            return redirect(reverse('main', host='app'))
        print("Error 5")
        return HttpResponse(status=500)

    if submit_option == 'run':
        if at:
            at.status = 'success'
            at.completed_at = timezone.now()
            at.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat)
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(current_action_tracker=at, workflow_action_tracker=wat,
                                current_node=node, user_id=str(request.user.id), lang=lang)
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW).order_by(
            'order', 'created_at').first()
        if module:
            module_slug = module.slug
            return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history')
        return redirect(reverse('main', host='app'))

    print("Error 6")
    return HttpResponse(status=500)


@login_or_hubspot_required
def create_invoice_from_line_item_property(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == 'GET':
        is_record_action = request.GET.get('is_record_action')
        object_type = request.GET.get('object_type', None)

        if object_type == TYPE_OBJECT_CUSTOM_OBJECT:
            object_id = request.GET.get('row_id', None)
            line_item_properties = CustomObjectPropertyName.objects.filter(
                workspace=workspace, type='price-information').order_by('-created_at')
        else:
            object_id = request.GET.get('case_id', None)
            # get multiple line item property
            line_item_properties = DealsNameCustomField.objects.filter(
                workspace=workspace, type='price-information').order_by('-created_at')

        context = {
            'line_item_properties': line_item_properties,
            'action_type': request.GET.get('action_type', None),
            'action_id': request.GET.get('action_id', None),
            'action_index': request.GET.get('action_index', False),
            'bulk_action': request.GET.get('bulk_action', False),
            'object_type': object_type,
            'is_record_action': is_record_action,
            'custom_object_id': request.GET.get('custom_object_id', None)
        }

        if object_type == TYPE_OBJECT_CUSTOM_OBJECT:
            obj = CustomObjectPropertyRow.objects.filter(id=object_id).first()
        else:
            obj = Deals.objects.filter(id=object_id).first()

        if obj:
            context['object_id'] = obj.id

        return render(request, 'data/commerce/action/generate-invoice-from-line-item-property.html', context)

    elif request.method == 'POST':
        object_id = request.POST.get('object_id', "")
        object_type = request.POST.get('object_type', TYPE_OBJECT_CASE)

        custom_object_id = request.POST.get('custom_object_id', "")

        start_date = request.POST.get('start-date', '')
        due_date = request.POST.get('freq-due', '')
        customer = request.POST.get('contact_and_company', '')
        is_record_action = request.POST.get('is_record_action')
        selected_line_item_property_id = request.POST.get(
            'select-line-item-property')

        action_id = request.POST.get('action_id')
        action = Action.objects.filter(slug=action_id).first()
        history = ActionHistory.objects.create(
            workspace=workspace,
            action=action,
            status='initialized',
            created_by=request.user
        )

        invoice_module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVOICE]
        invoice_module = Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_INVOICE).order_by('order', 'created_at').first()

        if invoice_module:
            invoice_module_slug = invoice_module.slug

        page_obj = get_page_object(TYPE_OBJECT_INVOICE, lang)
        id_field = page_obj['id_field']
        default_columns = page_obj['default_columns']
        if id_field and id_field not in default_columns:
            default_columns.insert(0, id_field)

        base_model = page_obj['base_model']
        item_model = page_obj['item_model']
        field_item_name = page_obj['field_item_name']

        history.status = 'running'
        history.save()

        input_data = dict(request.POST)
        input_data = {key: value[0] if len(
            value) == 1 else value for key, value in input_data.items()}

        transfer_history = ActionTaskHistory.objects.create(
            workspace=history.workspace,
            action_history=history,
            status='initialized',
            input_data=input_data
        )
        transfer_history.status = 'running'
        transfer_history.save()

        invoice = base_model.objects.create(workspace=workspace)

        # Process required field for invoice
        customer_contact = Contact.objects.filter(id=customer).first()
        if customer_contact:
            invoice.contact = customer_contact

        customer_company = Company.objects.filter(id=customer).first()
        if customer_company:
            invoice.company = customer_company

        if start_date:
            if isinstance(start_date, tuple):
                start_date = start_date[0]
            try:
                if lang == 'ja':
                    start_date = datetime.strptime(
                        start_date, '%Y年%m月%d日').date()
                else:
                    start_date = datetime.strptime(
                        start_date, '%Y-%m-%d').date()
                invoice.start_date = start_date
            except:  # Format date is invalid , skip to save it
                pass
        if due_date:
            try:
                if lang == 'ja':
                    due_date = datetime.strptime(due_date, '%Y年%m月%d日').date()
                else:
                    due_date = datetime.strptime(due_date, '%Y-%m-%d').date()
                invoice.due_date = due_date
            except:  # Format date is invalid , skip to save it
                pass

        invoice.status = 'draft'

        # Process line item for invoice
        if object_type == TYPE_OBJECT_CUSTOM_OBJECT:
            obj = CustomObjectPropertyRow.objects.filter(id=object_id).first()
            line_item_property = CustomObjectPropertyName.objects.filter(
                id=selected_line_item_property_id).first()
            line_item_value = CustomObjectPropertyValue.objects.filter(
                object=obj, field_name=line_item_property).first()
        else:
            obj = Deals.objects.filter(id=object_id).first()
            line_item_property = DealsNameCustomField.objects.filter(
                id=selected_line_item_property_id).first()
            line_item_value = DealsValueCustomField.objects.filter(
                deals=obj, field_name=line_item_property).first()

        line_item_values = []
        if line_item_value:
            line_item_values = ast.literal_eval(line_item_value.value)
        sum_item_prices = 0
        for idx, item in enumerate(line_item_values):
            obj_item = item_model.objects.create(**{field_item_name: invoice})
            item_name = item.get('item', '')
            if is_valid_uuid(item_name):
                item_obj = ShopTurboItems.objects.filter(id=item_name).first()
                obj_item.item_link = item_obj
                obj_item.item_name = item_obj.name
            else:
                obj_item.item_link = None
                obj_item.item_name = item_name

            item_price = item.get('item_price', 0.0)
            if is_valid_uuid(item_price):
                try:
                    item_price_obj = ShopTurboItemsPrice.objects.get(
                        id=item_price)
                    item_price = item_price_obj.price
                    obj_item.amount_price = item_price
                except:
                    item_price = 0.0
                    obj_item.amount_price = item_price
            else:
                obj_item.amount_price = float(item_price)

            obj_item.amount_item = item.get('number_of_items', 0)
            total_amount = float(item_price) * int(obj_item.amount_item)

            obj_item.tax_rate = '0'
            obj_item.total_price_without_tax = float(total_amount)
            obj_item.total_price = float(total_amount)
            obj_item.order_view = idx
            obj_item.save(
                log_data={'user': request.user, 'workspace': workspace})

            sum_item_prices += float(total_amount)

        currency = None
        if line_item_values:
            currency = line_item_values[0].get('currency', 'USD')[0]

        invoice.currency = currency

        invoice.total_price = float(sum_item_prices)
        invoice.total_price_without_tax = float(sum_item_prices)

        invoice.save()

        obj_dict = model_to_dict(invoice)
        serialized_data = json.dumps(obj_dict, default=custom_serializer)
        transfer_history.output_data = json.loads(serialized_data)
        if invoice_module_object_slug:
            transfer_history.result_link = reverse('load_object_page', host='app', kwargs={
                'module_slug': invoice_module.slug, 'object_slug': invoice_module_object_slug})+f"?id={invoice.id}"
        transfer_history.completed_at = timezone.now()
        transfer_history.status = 'success'
        transfer_history.save()

        if is_record_action:
            if object_type == TYPE_OBJECT_CUSTOM_OBJECT:
                history.object_type = TYPE_OBJECT_CUSTOM_OBJECT
                history.object_id = obj.id
            else:
                history.object_type = TYPE_OBJECT_CASE
                history.object_id = obj.id
            history.save()

        history.completed_at = timezone.now()
        history.status = 'success'
        history.save()

        if object_type == TYPE_OBJECT_CUSTOM_OBJECT:
            try:
                custom_object = CustomObject.objects.get(id=custom_object_id)
                module_object_slug = custom_object.slug
            except:
                return redirect(reverse('main', host='app'))

            module = Module.objects.filter(workspace=workspace, object_values__contains=str(
                custom_object_id)).order_by('order', 'created_at').first()
        else:
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
            module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_CASE).order_by(
                'order', 'created_at').first()

        if module:
            module_slug = module.slug
            if history:
                if object_type == TYPE_OBJECT_CUSTOM_OBJECT:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}))
                else:
                    if is_record_action:
                        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={TYPE_OBJECT_CASE}&id={obj.id}&sidedrawer=action-history')
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + "?open_drawer=action_drawer_history")
        return redirect(reverse('main', host='app'))


@login_or_hubspot_required
def convert_line_item_property_to_slips(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == 'GET':
        is_record_action = request.GET.get('is_record_action')
        object_id = request.GET.get('case_id', None)
        # get multiple line item property
        line_item_properties = DealsNameCustomField.objects.filter(
            workspace=workspace, type='price-information').order_by('-created_at')

        SLIP_TYPE = Slip._meta.get_field('slip_type').choices
        context = {
            'SLIP_TYPE': SLIP_TYPE,
            'line_item_properties': line_item_properties,
            'action_type': request.GET.get('action_type', None),
            'action_id': request.GET.get('action_id', None),
            'action_index': request.GET.get('action_index', False),
            'bulk_action': request.GET.get('bulk_action', False),
            'is_record_action': is_record_action
        }

        obj = Deals.objects.filter(id=object_id).first()

        if obj:
            context['object_id'] = obj.id

        return render(request, 'data/commerce/action/convert-line-item-property-to-slip.html', context)

    elif request.method == 'POST':
        object_id = request.POST.get('object_id', "")
        start_date = request.POST.get('start-date', '')
        is_record_action = request.POST.get('is_record_action')
        selected_line_item_property_id = request.POST.get(
            'select-line-item-property')
        slip_type = request.POST.get('slip_type')

        action_id = request.POST.get('action_id')
        action = Action.objects.filter(slug=action_id).first()
        history = ActionHistory.objects.create(
            workspace=workspace,
            action=action,
            status='initialized',
            created_by=request.user
        )

        slip_module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_SLIP]
        slip_module = Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_SLIP).order_by('order', 'created_at').first()

        if slip_module:
            slip_module_slug = slip_module.slug

        page_obj = get_page_object(TYPE_OBJECT_SLIP, lang)
        id_field = page_obj['id_field']
        default_columns = page_obj['default_columns']
        if id_field and id_field not in default_columns:
            default_columns.insert(0, id_field)
        base_model = page_obj['base_model']
        custom_model = page_obj['custom_model']
        custom_value_model = page_obj['custom_value_model']
        item_model = page_obj['item_model']
        field_item_name = page_obj['field_item_name']

        history.status = 'running'
        history.save()

        input_data = dict(request.POST)
        input_data = {key: value[0] if len(
            value) == 1 else value for key, value in input_data.items()}

        transfer_history = ActionTaskHistory.objects.create(
            workspace=history.workspace,
            action_history=history,
            status='initialized',
            input_data=input_data
        )
        transfer_history.status = 'running'
        transfer_history.save()

        slip = base_model.objects.create(workspace=workspace)

        # Process required field for slips

        if start_date:
            if isinstance(start_date, tuple):
                start_date = start_date[0]
            try:
                if lang == 'ja':
                    start_date = datetime.strptime(
                        start_date, '%Y年%m月%d日').date()
                else:
                    start_date = datetime.strptime(
                        start_date, '%Y-%m-%d').date()
                slip.start_date = start_date
            except:  # Format date is invalid , skip to save it
                pass

        slip.slip_type = slip_type
        # Process line item for slip
        deal = Deals.objects.filter(id=object_id).first()
        # line_item_property = DealsNameCustomField.objects.filter(
        #     id=selected_line_item_property_id).first()
        # line_item_value = DealsValueCustomField.objects.filter(
        #     deals=deal, field_name=line_item_property).first()

        # line_item_values = []
        # if line_item_value:
        #     line_item_values = ast.literal_eval(line_item_value.value)
        sum_item_prices = 0

        # for idx, item in enumerate(line_item_values):
        case_items = DealsItems.objects.filter(deal=deal)
        for idx, item in enumerate(case_items):

            obj_item = item_model.objects.create(**{field_item_name: slip})
            # item_name = item.get('item', '')
            if item.item:
                item_name = item.item.id
                item_price = item.item_price.id
            else:
                item_name = item.custom_item_name
                item_price = item.item_price_deal

            if is_valid_uuid(item_name):
                item_obj = ShopTurboItems.objects.filter(id=item_name).first()
                obj_item.item_link = item_obj
                obj_item.item_name = item_obj.name
            else:
                obj_item.item_link = None
                obj_item.item_name = item_name

            # item_price = item.get('item_price', 0.0)

            if is_valid_uuid(item_price):
                try:
                    item_price_obj = ShopTurboItemsPrice.objects.get(
                        id=item_price)
                    item_price = item_price_obj.price
                    obj_item.amount_price = item_price
                except:
                    item_price = 0.0
                    obj_item.amount_price = item_price
            else:
                obj_item.amount_price = float(item_price)

            obj_item.amount_item = item.number_item
            total_amount = float(item_price) * int(obj_item.amount_item)

            obj_item.tax_rate = '0'
            obj_item.total_price_without_tax = float(total_amount)
            obj_item.total_price = float(total_amount)
            obj_item.order_view = idx
            obj_item.save(
                log_data={'user': request.user, 'workspace': workspace})

            sum_item_prices += float(total_amount)

        currency = "USD"
        # if line_item_values:
        #     currency_list = line_item_values[0].get('currency')
        #     if len(currency_list) > 0:
        #         currency = currency_list[0]
        if deal.currency:
            currency = deal.currency

        slip.currency = currency

        slip.total_price = float(sum_item_prices)
        slip.total_price_without_tax = float(sum_item_prices)

        slip.tax_rate = 0.0
        slip.save()

        obj_dict = model_to_dict(slip)
        serialized_data = json.dumps(obj_dict, default=custom_serializer)
        transfer_history.output_data = json.loads(serialized_data)
        if slip_module_object_slug:
            transfer_history.result_link = reverse('load_object_page', host='app', kwargs={
                'module_slug': slip_module.slug, 'object_slug': slip_module_object_slug})+f"?id={slip.id}"
        transfer_history.completed_at = timezone.now()
        transfer_history.status = 'success'
        transfer_history.save()

        if is_record_action:
            history.object_type = TYPE_OBJECT_CASE
            history.object_id = deal.id
            history.save()

        history.completed_at = timezone.now()
        history.status = 'success'
        history.save()

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
        module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_CASE).order_by(
            'order', 'created_at').first()
        if module:
            module_slug = module.slug
            if history:
                if is_record_action:
                    return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?target={TYPE_OBJECT_CASE}&id={obj.id}&sidedrawer=action-history')
                return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug}) + "?open_drawer=action_drawer_history")
        return redirect(reverse('main', host='app'))


def aggregate_commerce_object(objects=None, listed_dict=None, customers_=None, object_type='invoices', lang='ja', greater_date=None, due_date=None, convert_to_invoice=False, use_account_rec=False, tax_option='item_based_tax', convert_to_purchase_order=False):
    page_obj = get_page_object(object_type, lang)
    base_model = page_obj['base_model']
    item_model = page_obj['item_model']
    custom_value_relation = page_obj['custom_value_relation']
    custom_value_model = page_obj['custom_value_model']
    if not objects:
        try:
            objects = base_model.objects.filter(
                id__in=listed_dict[str(customers_.id)]).exclude(total_price=0.0)
        except:
            objects = base_model.objects.filter(
                id__in=listed_dict[str(customers_.id)])

    if objects:
        workspace = objects.first().workspace
        contact = objects.first().contact
        company = objects.first().company
        if customers_ and customers_._meta.model_name == 'contact':
            if convert_to_invoice:
                object_combined = Invoice.objects.create(
                    workspace=workspace, contact=customers_)
                item_combined = InvoiceItem
            elif convert_to_purchase_order:
                object_combined = PurchaseOrders.objects.create(
                    workspace=workspace, contact=customers_)
                item_combined = PurchaseOrdersItem
            else:
                object_combined = base_model.objects.create(
                    workspace=workspace, contact=customers_)
                item_combined = None
        elif customers_ and customers_._meta.model_name == 'company':
            if convert_to_invoice:
                object_combined = Invoice.objects.create(
                    workspace=workspace, company=customers_)
                item_combined = InvoiceItem
            elif convert_to_purchase_order:
                object_combined = PurchaseOrders.objects.create(
                    workspace=workspace, company=customers_)
                item_combined = PurchaseOrdersItem
            else:
                object_combined = base_model.objects.create(
                    workspace=workspace, company=customers_)
                item_combined = None
        else:
            if object_type == TYPE_OBJECT_INVOICE:
                object_combined = base_model.objects.create(
                    workspace=workspace, contact=contact)
                item_combined = InvoiceItem

        object_combined.currency = objects[0].currency
        index = 0
        is_inclusive = False
        count = 0
        for object_ in objects:
            if convert_to_purchase_order:
                pass
            else:
                if object_.tax_inclusive:
                    count += 1

        if count == len(objects):
            is_inclusive = True
            object_combined.tax_inclusive = True

        total_price_combined = 0
        for object_ in objects:
            index += 1
            if object_combined.currency != object_.currency:
                object_combined.delete()
                print('Deleted Here 2')
                return 'Cannot proccess Multicurrencies', '', ''

            if custom_value_relation == 'orders':
                custom_value_relation = 'order'
            item_objects = item_model.objects.filter(
                **{custom_value_relation: object_}).order_by('created_at')
            if item_objects:
                for item_object in item_objects:
                    if convert_to_invoice:
                        item_combined_ = item_combined.objects.create(
                            invoice=object_combined)
                        item_combined_.item_name = item_object.item_name
                        item_combined_.item_link = item_object.item_link
                        item_combined_.amount_price = item_object.amount_price
                        item_combined_.amount_item = item_object.amount_item
                        item_combined_.tax_rate = item_object.tax_rate
                        item_combined_.total_price = item_object.total_price
                        item_combined_.total_price_without_tax = item_object.total_price_without_tax
                        item_combined_.save()
                        item_object = item_combined_
                    elif convert_to_purchase_order:
                        item_combined_ = item_combined.objects.create(
                            purchase_order=object_combined)
                        if item_object.item:
                            item_combined_.item_name = item_object.item.name
                            item_combined_.item = item_object.item
                        else:
                            item_combined_.item_name = item_object.custom_item_name
                        item_object_total_price = item_object.total_price if item_object.total_price else item_object.item_price_order * item_object.number_item
                        item_combined_.amount_price = item_object.item_price_order
                        item_combined_.amount_item = item_object.number_item
                        item_combined_.total_price = item_object_total_price
                        item_combined_.total_price_without_tax = item_object_total_price
                        item_combined_.save()
                        total_price_combined += item_combined_.total_price
                        item_object = item_combined_
                    else:
                        item_object.id = None
                        setattr(item_object, custom_value_relation,
                                object_combined)
                    if object_type == 'commerce_orders':
                        pass
                    else:
                        if object_.tax_inclusive:
                            if not is_inclusive:
                                # Override tax to zero
                                amount_of_tax_inclusive = (
                                    float(item_object.amount_price) / (1 + (float(item_object.tax_rate) / 100.0)))
                                item_object.amount_price = amount_of_tax_inclusive
                        else:
                            if object_.tax_option == 'unified_tax' and object_.tax_rate:
                                item_object.tax_rate = object_.tax_rate
                    item_object.save()
        if index == 0:
            object_combined.delete()
            print('Deleted Here 3')
            return 'There is no Aggregated record are listed', '', ''

        if object_combined:
            app_target = "shopturbo"
            app_setting, _ = AppSetting.objects.get_or_create(
                workspace=workspace, app_target=app_target)

            app_setting_child, _ = AppSettingChild.objects.get_or_create(
                **{'app_setting': app_setting, 'target_name': object_type[:-1] + "_note"})
            default_note = app_setting_child.value

            app_setting_child, _ = AppSettingChild.objects.get_or_create(
                **{'app_setting': app_setting, 'target_name': object_type[:-1] + "_send_from"})
            default_send_from = app_setting_child.value
            object_combined.contact = contact
            object_combined.company = company
            object_combined.notes = default_note
            object_combined.send_from = default_send_from
            object_combined.usage_status = "active"
            object_combined.tax_option = tax_option
            if convert_to_purchase_order:
                object_combined.total_price = total_price_combined
            object_combined.save()

            if custom_value_relation == 'order':
                custom_value_relation = 'orders'
            customFields = custom_value_model.objects.filter(
                **{custom_value_relation: objects.first()})

            for field in customFields:
                if convert_to_invoice or convert_to_purchase_order:
                    pass
                else:
                    custom_value_model.objects.create(
                        **{custom_value_relation: object_combined, 'value': field.value, 'field_name': field.field_name})

            if use_account_rec:
                obj = object_combined
                print("Add User Accounting 1:")
                unsettle_accounts = None
                unsettle_accounts_amount = 0
                if obj.contact:
                    check_formula_field = ContactsNameCustomField.objects.filter(
                        workspace=workspace, type='formula').first()
                    if check_formula_field:
                        if "amount_after_settlement" in check_formula_field.choice_value:
                            unsettle_accounts = JournalEntry.objects.filter(
                                contact=obj.contact)
                elif obj.company:
                    check_formula_field = CompanyNameCustomField.objects.filter(
                        workspace=workspace, type='formula').first()
                    if check_formula_field:
                        if "amount_after_settlement" in check_formula_field.choice_value:
                            unsettle_accounts = JournalEntry.objects.filter(
                                company=obj.company)
                if unsettle_accounts:
                    for account in unsettle_accounts:
                        if account.amount_after_settlement:
                            unsettle_accounts_amount += account.amount_after_settlement
                if unsettle_accounts_amount:
                    obj_item = item_object
                    obj_item.id = None
                    obj_item.item_link = None
                    if lang == 'ja':
                        obj_item.item_name = "売掛金"
                    else:
                        obj_item.item_name = "Accounts Receivable"
                    obj_item.amount_price = unsettle_accounts_amount
                    obj_item.amount_item = 1
                    obj_item.total_price_without_tax = unsettle_accounts_amount
                    obj_item.total_price = unsettle_accounts_amount
                    obj_item.tax_rate = 0
                    obj_item.save()

            if convert_to_purchase_order:
                pass
            else:
                object_combined = handling_items(object_combined)

            return 'Success', object_combined, len(objects)


def autocomplete_invoice(request):
    workspace = get_workspace(request.user)
    search = request.GET.get('search', '')
    ids = request.GET.getlist('ids[]', '')   

    filter_condition = Q(workspace=workspace) & Q(usage_status='active')
    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_INVOICE, user=request.user)
    filter_condition &= get_permission_filter(permission, request.user, permission_type='edit')
    
    page = int(request.GET.get('page', 1))

    results_per_page = 10 if len(ids) <= 0 else len(ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target=COMMERCE_APP_TARGET).first()

        if app_setting:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name='invoice_search_setting').first()
            if app_setting_child:
                filter_condition &= (apply_search_setting(
                    app_setting, search) | Q(id_inv__icontains=search))

    if ids:
        filter_condition &= Q(id__in=ids)

    obj, created = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=TYPE_OBJECT_INVOICE)
    if created:
        try:
            col_display = ','.join(DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_INVOICE])
        except:
            col_display = 'id_inv'
        obj.column_display = col_display
        obj.save()
    else:
        col_display = obj.column_display if obj.column_display else "id_inv"

    columns = col_display.split(',')
    results = Invoice.objects.filter(
        filter_condition).distinct().order_by('-created_at')[start:end]
    data = [
        {"id": item.id, "text": get_object_display_based_columns(TYPE_OBJECT_INVOICE, item, columns, workspace.timezone, request.LANGUAGE_CODE)} for item in results
    ]

    return JsonResponse({
        "results": data,
        "pagination": {"more": results.exists()}
    })


@login_or_hubspot_required
def get_invoice_object_options(request):
    workspace = get_workspace(request)
    lang = request.LANGUAGE_CODE
    page_obj = get_page_object(TYPE_OBJECT_INVOICE, lang)
    base_model = page_obj['base_model']
    id_field = page_obj['id_field']
    q = request.GET.get('q', '')
    page = int(request.GET.get('page', 1))
    limit = 30
    offset = (page - 1) * limit

    kwargs = {
        'workspace': workspace,
        'usage_status': 'active',  # Only show active invoices
    }

    if q:
        kwargs['id_inv__icontains'] = q

    objects = base_model.objects.filter(**kwargs).order_by('-created_at')
    total_count = objects.count()
    objects = objects[offset:offset + limit]

    items = []
    for obj in objects:
        customer_name = ''
        if hasattr(obj, 'customer_name') and obj.customer_name:
            customer_name = obj.customer_name
        elif hasattr(obj, 'contact') and obj.contact:
            customer_name = obj.contact.name
        elif hasattr(obj, 'company') and obj.company:
            customer_name = obj.company.name

        items.append({
            'id': obj.id,
            'text': f"{obj.id_inv} - {customer_name}"
        })

    return JsonResponse({
        'items': items,
        'total_count': total_count,
    })


def autocomplete_estimate(request):
    workspace = get_workspace(request.user)
    search = request.GET.get('search', '')
    ids = request.GET.getlist('ids[]', '')
    filter_condition = Q(workspace=workspace) & Q(usage_status='active')
    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_ESTIMATE, user=request.user)
    filter_condition &= get_permission_filter(permission, request.user, permission_type='edit')
    
    page = int(request.GET.get('page', 1))

    results_per_page = 10 if len(ids) <= 0 else len(ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target=COMMERCE_APP_TARGET).first()

        if app_setting:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name='estimate_search_setting').first()
            if app_setting_child:
                filter_condition &= (apply_search_setting(
                    app_setting, search) | Q(id_inv__icontains=search))

    if ids:
        filter_condition &= Q(id__in=ids)

    obj, created = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=TYPE_OBJECT_ESTIMATE)
    if created:
        try:
            col_display = ','.join(
                DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_ESTIMATE])
        except:
            col_display = "id_est"
        obj.column_display = col_display
        obj.save()
    else:
        col_display = obj.column_display if obj.column_display else "id_est"

    columns = col_display.split(',')
    results = Estimate.objects.filter(
        filter_condition).distinct().order_by('-created_at')[start:end]
    data = [
        {"id": item.id, "text": get_object_display_based_columns(TYPE_OBJECT_ESTIMATE, item, columns, workspace.timezone, request.LANGUAGE_CODE)} for item in results
    ]

    return JsonResponse({
        "results": data,
        "pagination": {"more": results.exists()}
    })


def autocomplete_delivery_slip(request):
    workspace = get_workspace(request.user)
    search = request.GET.get('search', '')
    ids = request.GET.getlist('ids[]', '')
    filter_condition = Q(workspace=workspace) & Q(usage_status='active')
    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_DELIVERY_NOTE, user=request.user)
    filter_condition &= get_permission_filter(permission, request.user, permission_type='edit')
    
    page = int(request.GET.get('page', 1))

    results_per_page = 10 if len(ids) <= 0 else len(ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target=COMMERCE_APP_TARGET).first()

        if app_setting:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name='delivery_slip_search_setting').first()
            if app_setting_child:
                filter_condition &= (apply_search_setting(
                    app_setting, search) | Q(id_ds__icontains=search))

    if ids:
        filter_condition &= Q(id__in=ids)

    obj, created = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=TYPE_OBJECT_DELIVERY_NOTE)
    if created:
        try:
            col_display = ','.join(
                DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_DELIVERY_NOTE])
        except:
            col_display = "id_ds"
        obj.column_display = col_display
        obj.save()
    else:
        col_display = obj.column_display if obj.column_display else "id_ds"

    columns = col_display.split(',')
    results = DeliverySlip.objects.filter(
        filter_condition).distinct().order_by('-created_at')[start:end]
    data = [
        {"id": item.id, "text": get_object_display_based_columns(TYPE_OBJECT_DELIVERY_NOTE, item, columns, workspace.timezone, request.LANGUAGE_CODE)} for item in results
    ]

    return JsonResponse({
        "results": data,
        "pagination": {"more": results.exists()}
    })


def autocomplete_slip(request):
    workspace = get_workspace(request.user)
    search = request.GET.get('search', '')
    ids = request.GET.getlist('ids[]', '')

    filter_condition = Q(workspace=workspace) & Q(usage_status='active')

    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_SLIP, user=request.user)
    filter_condition &= get_permission_filter(permission, request.user, permission_type='edit')
    
    page = int(request.GET.get('page', 1))

    results_per_page = 10 if len(ids) <= 0 else len(ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target=COMMERCE_APP_TARGET).first()

        if app_setting:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name='slip_search_setting').first()
            if app_setting_child:
                filter_condition &= (apply_search_setting(
                    app_setting, search) | Q(id_slip__icontains=search))

    if ids:
        filter_condition &= Q(id__in=ids)

    obj, created = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=TYPE_OBJECT_SLIP)
    if created:
        try:
            col_display = ','.join(DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_SLIP])
        except:
            col_display = "id_slip"
        obj.column_display = col_display
        obj.save()
    else:
        col_display = obj.column_display if obj.column_display else "id_slip"

    columns = col_display.split(',')
    results = Slip.objects.filter(
        filter_condition).distinct().order_by('-created_at')[start:end]
    data = [
        {"id": item.id, "text": get_object_display_based_columns(TYPE_OBJECT_SLIP, item, columns, workspace.timezone, request.LANGUAGE_CODE)} for item in results
    ]

    return JsonResponse({
        "results": data,
        "pagination": {"more": results.exists()}
    })


def get_estimate_object_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    q = request.GET.get('q')
    page = request.GET.get('page', 1)

    filter_conditions = Q(workspace=workspace, usage_status='active')
    if q:
        q_id = q
        try:
            if q[0] == '#' or q[0] == '0':
                q_id = int(q[1:].replace(' ', ''))
        except Exception as e:
            print(e)
        filter_conditions &= (Q(id_est__icontains=q_id) | Q(
            company__name__icontains=q) | Q(contact__name__icontains=q))
    estimate_objs = Estimate.objects.filter(
        filter_conditions).order_by('-id_est')

    res = []
    ITEMS_PER_PAGE = 30
    estimates_paginator = Paginator(estimate_objs, ITEMS_PER_PAGE)
    paginated_estimates = []
    more_pagination = False
    if page:
        try:
            estimate_page_content = estimates_paginator.page(
                page if page else 1)
            paginated_estimates = estimate_page_content.object_list
            more_pagination = estimate_page_content.has_next()
        except EmptyPage:
            pass

    for estimate in paginated_estimates:
        est_dict = {
            'id': str(estimate.id),
            'text': f'#{estimate.id_est:04d} | '
        }
        if estimate.contact:
            est_dict['text'] += f'{display_contact_name(estimate.contact, lang)}'
        elif estimate.company:
            est_dict['text'] += f'{estimate.company.name}'

        res.append(est_dict)

    context = {
        'results': res,
        'pagination': {
            "more": more_pagination
        }
    }

    return JsonResponse(context)


@login_or_hubspot_required
def get_delivery_notes_object_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    q = request.GET.get('q')
    page = request.GET.get('page', 1)

    filter_conditions = Q(workspace=workspace, usage_status='active')
    if q:
        q_id = q
        try:
            if q[0] == '#' or q[0] == '0':
                q_id = int(q[1:].replace(' ', ''))
        except Exception as e:
            print(e)
        filter_conditions &= (Q(id_ds__icontains=q_id) | Q(
            company__name__icontains=q) | Q(contact__name__icontains=q))
    delivery_note_objs = DeliverySlip.objects.filter(
        filter_conditions).order_by('-id_ds')

    res = []
    ITEMS_PER_PAGE = 30
    delivery_notes_paginator = Paginator(delivery_note_objs, ITEMS_PER_PAGE)
    paginated_delivery_notes = []
    more_pagination = False
    if page:
        try:
            delivery_note_page_content = delivery_notes_paginator.page(
                page if page else 1)
            paginated_delivery_notes = delivery_note_page_content.object_list
            more_pagination = delivery_note_page_content.has_next()
        except EmptyPage:
            pass

    for delivery_note in paginated_delivery_notes:
        ds_dict = {
            'id': str(delivery_note.id),
            'text': f'#{delivery_note.id_ds:04d} | '
        }
        if delivery_note.contact:
            ds_dict['text'] += f'{display_contact_name(delivery_note.contact, lang)}'
        elif delivery_note.company:
            ds_dict['text'] += f'{delivery_note.company.name}'

        res.append(ds_dict)

    context = {
        'results': res,
        'pagination': {
            "more": more_pagination
        }
    }

    return JsonResponse(context)


@login_or_hubspot_required
def get_invoice_object_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    q = request.GET.get('q')
    page = request.GET.get('page', 1)

    filter_conditions = Q(workspace=workspace, usage_status='active')
    if q:
        q_id = q
        try:
            if q[0] == '#' or q[0] == '0':
                q_id = int(q[1:].replace(' ', ''))
        except Exception as e:
            print(e)
        filter_conditions &= (Q(id_inv__icontains=q_id) | Q(
            company__name__icontains=q) | Q(contact__name__icontains=q))
    invoice_objs = Invoice.objects.filter(
        filter_conditions).order_by('-id_inv')

    res = []
    ITEMS_PER_PAGE = 30
    invoices_paginator = Paginator(invoice_objs, ITEMS_PER_PAGE)
    paginated_invoices = []
    more_pagination = False
    if page:
        try:
            invoice_page_content = invoices_paginator.page(page if page else 1)
            paginated_invoices = invoice_page_content.object_list
            more_pagination = invoice_page_content.has_next()
        except EmptyPage:
            pass

    for invoice in paginated_invoices:
        inv_dict = {
            'id': str(invoice.id),
            'text': f'#{invoice.id_inv:04d} | '
        }
        if invoice.contact:
            inv_dict['text'] += f'{display_contact_name(invoice.contact, lang)}'
        elif invoice.company:
            inv_dict['text'] += f'{invoice.company.name}'

        res.append(inv_dict)

    context = {
        'results': res,
        'pagination': {
            "more": more_pagination
        }
    }

    return JsonResponse(context)


@login_or_hubspot_required
def send_emails_from_workflow(request):

    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == 'GET':
        action_index = request.GET.get('action_index')
        action_node_id = request.GET.get('action_node_id')
        postfix = ''
        if action_index:
            postfix = '-' + str(action_index)
        action_slug = request.GET.get('action_id' + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print('Action node does not exist')
                return HttpResponse(status=404)

        active_recipient = None
        active_subject = None
        active_body = None
        active_channel = None
        if node and node.input_data:
            if 'recipient' in node.input_data:
                active_recipient = node.input_data['recipient']
            if 'subject' in node.input_data:
                active_subject = node.input_data['subject']
            if 'body' in node.input_data:
                active_body = node.input_data['body']
            if 'channel' in node.input_data:
                active_channel = node.input_data['channel']

        smtp_channels = Channel.objects.filter(
            Q(workspace=workspace, integration__slug='smtp') |
            Q(
                workspace=workspace,
                integration__slug='rakuten',
                service_account__isnull=False,
                app_id__isnull=False,
                account_id__isnull=False,
                account_username__isnull=False,
            )
        ).exclude(
            Q(integration__slug='rakuten', service_account='') |
            Q(integration__slug='rakuten', app_id='') |
            Q(integration__slug='rakuten', account_id='') |
            Q(integration__slug='rakuten', account_username='')
        )

        templates = MessageBodyTemplate.objects.filter(workspace=workspace)

        context = {
            "action_index": action_index,
            "action_slug": action_slug,

            'active_recipient': active_recipient,
            'active_subject': active_subject,
            'active_body': active_body,
            'active_channel': active_channel,
            'workspace': workspace,
            'smtp_channels': smtp_channels,
            'templates': templates,
        }

        return render(request, 'data/commerce/send-emails-workflow.html', context)

    if request.method == 'POST':
        print(request.POST)
        submit_option = request.POST.get('submit-option')
        action_index = request.POST.get('action_index')
        action_node_id = request.POST.get('action_node_id')
        action_slug = request.POST.get('action_slug')
        at_id = request.POST.get('action_tracker_id')
        wat_id = request.POST.get('workflow_action_tracker_id')
        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                print('ActionNode does not exist')
                return HttpResponse(status=404)
        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                print('ActionTracker does not exist')
                return HttpResponse(status=404)
        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                print('WorkflowActionTracker does not exist')
                return HttpResponse(status=404)

        postfix = ''
        if action_index:
            postfix = '-' + action_index

        action_name = request.POST.get('action_name'+postfix)

        recipient = request.POST.get('recipient'+postfix)
        subject = request.POST.get('subject'+postfix)
        body = request.POST.get('body'+postfix)
        smtp_channel_id = request.POST.get('smtp_channel_id'+postfix)
        cc_email = request.POST.get('template-cc'+postfix)

        if submit_option == 'save':
            node.valid_to_run = True
            input_data = {}

            if recipient:
                input_data['recipient'] = recipient
            else:
                node.valid_to_run = False

            if subject:
                input_data['subject'] = subject
            else:
                node.valid_to_run = False

            if body:
                input_data['body'] = body
            else:
                node.valid_to_run = False

            if smtp_channel_id:
                input_data['channel'] = smtp_channel_id
            else:
                node.valid_to_run = False
                
            if cc_email:
                input_data['cc'] = cc_email

            if action_name:
                input_data['action_name'] = action_name
            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            return HttpResponse(status=200)

        df_object = DateFormat.objects.filter(
            workspace=workspace, is_workspace_level=True).first()
        date_format = df_object.value if (
            df_object and df_object.value is not None) else "DD/MM/YYYY"
        date_format = DATE_FORMAT_PARSER[date_format]

        email = node.input_data.get('recipient')
        subject = node.input_data.get('subject')
        body = node.input_data.get('body')
        smtp_channel_id = node.input_data.get('channel')

        if smtp_channel_id:
            if smtp_channel_id != 'sanka':
                smtp_channel = Channel.objects.filter(
                    id=smtp_channel_id).first()
                if smtp_channel.integration.slug == 'rakuten':
                    if push_rakuten_email(smtp_channel_id, email, subject, body):
                        message_thread = MessageThread.objects.create(
                            channel=smtp_channel, workspace=workspace, message_type='email')

                        message = Message.objects.create(
                            thread=message_thread, body=body)
                        message.sent_at = timezone.now()
                        message.save()
                elif push_email_smtp(smtp_channel_id, email, subject, body, is_html=True):
                    message_thread = MessageThread.objects.create(
                        channel=smtp_channel, workspace=workspace, message_type='email')

                    message = Message.objects.create(
                        thread=message_thread, body=body)
                    message.sent_at = timezone.now()
                    message.save()

        if not is_valid_email(email):
            if lang == 'ja':
                Notification.objects.create(workspace=workspace, user=request.user,
                                            message=f'電子メールアドレスが正しくありません', type="error")
            else:
                Notification.objects.create(workspace=workspace, user=request.user,
                                            message=f'Email address is invalid.', type="error")

        if subject:
            mail_subject = subject
        if body:
            message = body

        soup = BeautifulSoup(message, "html.parser")
        message = soup.get_text()

        from_email = 'Sanka <<EMAIL>>'
        to_email = [email]

        email = EmailMessage(
            mail_subject,
            message,
            from_email,
            to_email,
        )

        try:
            email.send()
        except:
            if lang == 'ja':
                Notification.objects.create(
                    workspace=workspace, user=request.user, message='電子メールの送信に失敗しました。', type="error")
            else:
                Notification.objects.create(
                    workspace=workspace, user=request.user, message=f'Failed to send email to {email}', type="error")

        at.status = 'success'
        at.completed_at = timezone.now()
        at.save()

        wat.status = 'success'
        wat.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat)
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(current_action_tracker=at, workflow_action_tracker=wat,
                                current_node=node, user_id=str(request.user.id), lang=lang)

        module_slug = request.POST.get('module')
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        if not module_slug:
            module = Module.objects.filter(workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW).order_by(
                'order', 'created_at').first()
            module_slug = module.slug
        return redirect(reverse('load_object_page', host='app', kwargs={'module_slug': module_slug, 'object_slug': module_object_slug})+f'?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history')
