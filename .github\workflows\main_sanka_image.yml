name: Build Sanka Base Image

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Base image version (e.g., 0.5.0)'
        required: true
        default: '0.6.0'
        type: string
      registry:
        description: 'Container registry to use'
        required: true
        default: 'porter'
        type: choice
        options:
          - porter
          - sanka
      platforms:
        description: 'Target platforms to build for'
        required: true
        default: 'multi'
        type: choice
        options:
          - multi
          - amd64

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout Github Repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Azure Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ github.event.inputs.registry == 'porter' && 'p16475locjapaneastsubeff9663d33.azurecr.io' || 'sanka.azurecr.io' }}
          username: ${{ github.event.inputs.registry == 'porter' && secrets.ACR_PORTER_USERNAME || secrets.AzureAppService_ContainerUsername_961fef46395b4ee4b51d8e91e961bcc6 }}
          password: ${{ github.event.inputs.registry == 'porter' && secrets.ACR_PORTER_PASSWORD || secrets.AzureAppService_ContainerPassword_8127305ba43944eaaf1f368fde89ea7b }}

      - name: Build and push base image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/Dockerfile.builder
          push: true
          platforms: ${{ github.event.inputs.platforms == 'multi' && 'linux/amd64,linux/arm64' || 'linux/amd64' }}
          tags: |
            ${{ github.event.inputs.registry == 'porter' && 'p16475locjapaneastsubeff9663d33.azurecr.io' || 'sanka.azurecr.io' }}/sanka/main/sanka-python:${{ github.event.inputs.version }}
            ${{ github.event.inputs.registry == 'porter' && 'p16475locjapaneastsubeff9663d33.azurecr.io' || 'sanka.azurecr.io' }}/sanka/main/sanka-python:latest
          build-args: |
            RELEASE=${{ github.event.inputs.version }}
            CACHEBUST=${{ github.run_number }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Image build summary
        run: |
          echo "✅ Base image built successfully!"
          echo "📦 Image: ${{ github.event.inputs.registry == 'porter' && 'p16475locjapaneastsubeff9663d33.azurecr.io' || 'sanka.azurecr.io' }}/sanka/main/sanka-python:${{ github.event.inputs.version }}"
          echo "🏷️  Tags: ${{ github.event.inputs.version }}, latest"
          echo "🖥️  Platforms: ${{ github.event.inputs.platforms == 'multi' && 'linux/amd64, linux/arm64' || 'linux/amd64' }}"