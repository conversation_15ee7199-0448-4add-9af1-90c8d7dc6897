{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

<script>
    // Basic search functionality
    function searchAction(e) {
        const sidebarItems = document.querySelectorAll(".module-management");
        const query = e.target.value.toLowerCase();

        sidebarItems.forEach((item) => {
            const textContent = item.textContent.trim().toLowerCase();
            if (textContent.includes(query)) {
                item.style.display = "";
            } else {
                item.style.setProperty("display", "none", "important");
            }
        });
    }

    // Initialize keyboard navigation
    document.addEventListener('DOMContentLoaded', initializeNavigation);
    window.onload = initializeNavigation;

    function initializeNavigation() {
        const searchInput = document.getElementById('object-search-input');
        if (!searchInput) return;

        const menuItems = document.querySelectorAll('.tw-rounded-md a');
        let currentIndex = -1;

        // Handle keyboard events
        searchInput.addEventListener('keydown', function(e) {
            const visibleItems = Array.from(menuItems).filter(item => {
                const parent = item.closest('.tw-rounded-md');
                return parent && window.getComputedStyle(parent).display !== 'none';
            });

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    if (currentIndex < visibleItems.length - 1) {
                        currentIndex++;
                        updateSelection(visibleItems);
                    }
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    if (currentIndex > 0) {
                        currentIndex--;
                        updateSelection(visibleItems);
                    } else if (currentIndex === -1 && visibleItems.length > 0) {
                        currentIndex = 0;
                        updateSelection(visibleItems);
                    }
                    break;
                    
                case 'Enter':
                    e.preventDefault();
                    if (currentIndex >= 0 && currentIndex < visibleItems.length) {
                        visibleItems[currentIndex].click();
                    }
                    break;
            }
        });

        function updateSelection(visibleItems) {
            // Remove selection from all items
            menuItems.forEach(item => {
                item.classList.remove('selected');
                item.style.backgroundColor = '';
            });

            // Add selection to current item
            if (currentIndex >= 0 && currentIndex < visibleItems.length) {
                const selectedItem = visibleItems[currentIndex];
                selectedItem.classList.add('selected');
                selectedItem.style.backgroundColor = '#f0f0f0';
                selectedItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        }

        // Add styles for selected item
        const style = document.createElement('style');
        style.textContent = `
            .selected {
                background-color: #f0f0f0 !important;
                border-radius: 4px;
            }
            .selected * {
                color: #472CF5 !important;
            }
        `;
        document.head.appendChild(style);
    }
</script>

<style>
    .navigation-workspace-settings {
        display:block !important;
        width: 20% !important;
        border-right: 1px solid #EAEAEA;
        height: calc(100vh - 75px) !important;
        overflow-y: scroll;
    }
    .content-workspace-settings {
        width: 80% !important;
        height: calc(100vh - 75px) !important;
        overflow-y: scroll;
    }
    @media only screen and (max-width: 991.98px) {
        .navigation-workspace-settings {
            width: 100% !important;
            height: 330px !important;
            border-right: 0;
            border-bottom: 1px solid #EAEAEA;
            padding-left: 25px !important;
        }
        .content-workspace-settings {
            width: 100% !important;
        }
    }
</style>

<div class="aside-menu d-block d-lg-flex" style="width: 100%;">
    <div class="menu pt-5 px-5 navigation-workspace-settings" style="">
        <h1 class="fs-4 fs-lg-3 mt-0 mb-0">
            {% if LANGUAGE_CODE == 'ja'%}
            オブジェクト管理
            {% else %}
            Object Management
            {% endif %}
        </h1>
        <div class="py-2 px-0">
            <input class="form-control bg-white rounded-1"
            type="text"
            id="object-search-input"
            name="search"
            placeholder="{% if LANGUAGE_CODE == 'ja' %} オブジェクトを検索 {% else %}Search Object{% endif %}..."
            value=""
            autofocus
            autocomplete="off"
            style="height: 26px;"
            onkeyup="searchAction(event)">
        </div>
        <div class="mb-15">
            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'module' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_modules' host 'app' %}">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        モジュール
                        {% else %}
                        Modules
                        {% endif %}
                    </span>
                </a>
            </div>
            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'module' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'module' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_modules' host 'app' %}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    モジュール
                    {% else %}
                    Modules
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'company' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=company">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        企業
                        {% else %}
                        company
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'company' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'company' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=company">
                    {% if LANGUAGE_CODE == 'ja'%}
                    企業
                    {% else %}
                    Companies
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'contacts' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=contacts">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        連絡先
                        {% else %}
                        Contacts
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'contacts' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'contacts' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=contacts">
                    {% if LANGUAGE_CODE == 'ja'%}
                    連絡先
                    {% else %}
                    Contacts
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'customer_case' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=customer_case">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        案件
                        {% else %}
                        Cases
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'customer_case' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'customer_case' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=customer_case">
                    {% if LANGUAGE_CODE == 'ja'%}
                    案件
                    {% else %}
                    Cases
                    {% endif %}
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'conversation' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'conversation' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=conversation">
                    {% if LANGUAGE_CODE == 'ja'%}
                    メッセージ
                    {% else %}
                    Messages
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == constant.TYPE_OBJECT_CAMPAIGN %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=campaigns">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        キャンペーン
                        {% else %}
                        Campaigns
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == constant.TYPE_OBJECT_CAMPAIGN %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == constant.TYPE_OBJECT_CAMPAIGN %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=campaigns">
                    {% if LANGUAGE_CODE == 'ja'%}
                    キャンペーン
                    {% else %}
                    Campaigns
                    {% endif %}
                </a>
            </div>
        

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'commerce_items' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_items">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        商品
                        {% else %}
                        Items
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'commerce_items' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'commerce_items' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_items">
                    {% if LANGUAGE_CODE == 'ja'%}
                    商品
                    {% else %}
                    Items
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'commerce_inventory_warehouse' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_inventory_warehouse">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        ロケーション
                        {% else %}
                        Locations
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'commerce_inventory_warehouse' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'commerce_inventory_warehouse' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_inventory_warehouse">
                    {% if LANGUAGE_CODE == 'ja'%}
                    ロケーション
                    {% else %}
                    Locations
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'commerce_inventory' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_inventory">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        在庫
                        {% else %}
                        Inventory
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'commerce_inventory' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'commerce_inventory' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_inventory">
                    {% if LANGUAGE_CODE == 'ja'%}
                    在庫
                    {% else %}
                    Inventory
                    {% endif %}
                </a>
            </div>
            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'commerce_inventory_transaction' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_inventory_transaction">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        入出庫
                        {% else %}
                        Inventory Transactions
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'commerce_inventory_transaction' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'commerce_inventory_transaction' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_inventory_transaction">
                    {% if LANGUAGE_CODE == 'ja'%}
                    入出庫
                    {% else %}
                    Inventory Transactions
                    {% endif %}
                </a>
            </div>

                        <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'purchaseorder' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=purchaseorder">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        発注
                        {% else %}
                        Purchase Orders
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'purchaseorder' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'purchaseorder' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=purchaseorder">
                    {% if LANGUAGE_CODE == 'ja'%}
                    発注
                    {% else %}
                    Purchase Orders
                    {% endif %}
                </a>
            </div>
            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == constant.TYPE_OBJECT_BILL %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type={{constant.TYPE_OBJECT_BILL}}">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        支払請求
                        {% else %}
                        Bills
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == constant.TYPE_OBJECT_BILL %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == constant.TYPE_OBJECT_BILL %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type={{constant.TYPE_OBJECT_BILL}}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    支払請求
                    {% else %}
                    Bills
                    {% endif %}
                </a>
            </div>



            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'estimates' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=estimates">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        見積
                        {% else %}
                        Estimates
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'estimates' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'estimates' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=estimates">
                    {% if LANGUAGE_CODE == 'ja'%}
                    見積
                    {% else %}
                    Estimates
                    {% endif %}
                </a>
            </div>


            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'commerce_orders' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_orders">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        受注
                        {% else %}
                        Orders
                        {% endif %}
                    </span>
                </a>
            </div>
            

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'commerce_orders' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'commerce_orders' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_orders">
                    {% if LANGUAGE_CODE == 'ja'%}
                    受注
                    {% else %}
                    Orders
                    {% endif %}
                </a>
            </div>
            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'commerce_subscription' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_subscription">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        サブスクリプション
                        {% else %}
                        Subscriptions
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'commerce_subscription' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'commerce_subscription' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=commerce_subscription">
                    {% if LANGUAGE_CODE == 'ja'%}
                    サブスクリプション
                    {% else %}
                    Subscriptions
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'delivery_slips' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=delivery_slips">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        納品
                        {% else %}
                        Delivery Notes
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'delivery_slips' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'delivery_slips' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=delivery_slips">
                    {% if LANGUAGE_CODE == 'ja'%}
                    納品
                    {% else %}
                    Delivery Notes
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'invoices' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=invoices">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoices{% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'invoices' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'invoices' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=invoices">
                {% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoices{% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'receipts' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=receipts">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        入金
                        {% else %}
                        Payments
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'receipts' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'receipts' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=receipts">
                    {% if LANGUAGE_CODE == 'ja'%}
                        入金
                    {% else %}
                    Payments
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == constant.TYPE_OBJECT_EXPENSE %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type={{constant.TYPE_OBJECT_EXPENSE}}">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        経費
                        {% else %}
                        Expenses
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == constant.TYPE_OBJECT_EXPENSE %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == constant.TYPE_OBJECT_EXPENSE %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type={{constant.TYPE_OBJECT_EXPENSE}}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    経費
                    {% else %}
                    Expenses
                    {% endif %}
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'task' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'task' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=task">
                    {% if LANGUAGE_CODE == 'ja'%}
                    タスク
                    {% else %}
                    Tasks
                    {% endif %}
                </a>
            </div>


            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'contract' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'contract' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=contract">
                    {% if LANGUAGE_CODE == 'ja'%}
                    契約
                    {% else %}
                    Contract
                    {% endif %}
                </a>
            </div>
            


            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'slips' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=slips">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        伝票
                        {% else %}
                        Slips
                        {% endif %}
                    </span>
                </a>
            </div>
            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'slips' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'slips' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=slips">
                    {% if LANGUAGE_CODE == 'ja'%}
                    伝票
                    {% else %}
                    Slips
                    {% endif %}
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'journal' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'journal' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=journal">
                    {% if LANGUAGE_CODE == 'ja'%}
                    仕訳
                    {% else %}
                    Journal Entries
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'panels' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=panels">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        レポート
                        {% else %}
                        Reports
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'panels' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'panels' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=panels">
                    {% if LANGUAGE_CODE == 'ja'%}
                    レポート
                    {% else %}
                    Reports
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == 'dashboards' %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=dashboards">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        ダッシュボード
                        {% else %}
                        Dashboard
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'dashboards' %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'dashboards' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=dashboards">
                    {% if LANGUAGE_CODE == 'ja'%}
                    ダッシュボード
                    {% else %}
                    Dashboard
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == constant.TYPE_OBJECT_USER_MANAGEMENT %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=user_management">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        従業員
                        {% else %}
                        Employees
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == constant.TYPE_OBJECT_USER_MANAGEMENT %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == constant.TYPE_OBJECT_USER_MANAGEMENT %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type=user_management">
                        {% if LANGUAGE_CODE == 'ja'%}
                        従業員
                        {% else %}
                        Employees
                        {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == constant.TYPE_OBJECT_JOBS %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type={{constant.TYPE_OBJECT_JOBS}}">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        求人
                        {% else %}
                        Jobs
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == constant.TYPE_OBJECT_JOBS %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == constant.TYPE_OBJECT_JOBS %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type={{constant.TYPE_OBJECT_JOBS}}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    求人
                    {% else %}
                    Jobs
                    {% endif %}
                </a>
            </div>

            <div class="max-lg:tw-block tw-hidden px-0 cursor-pointer menu-item my-2 module-management">
                <a class="menu-link py-1 m-0 ps-2 border-0 fw-bolder me-3{% if setting_type == constant.TYPE_OBJECT_COMMERCE_METER %} active text-white svg-icon-white{% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type={{constant.TYPE_OBJECT_COMMERCE_METER}}">
                    <span class="fs-2 fw-bold fs-lg-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        メーター
                        {% else %}
                        Meter
                        {% endif %}
                    </span>
                </a>
            </div>

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == constant.TYPE_OBJECT_COMMERCE_METER %} bg-dark {% endif %} tw-rounded-md">
                <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == constant.TYPE_OBJECT_COMMERCE_METER %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}" href="{% host_url 'workspace_setting' host 'app' %}?setting_type={{constant.TYPE_OBJECT_COMMERCE_METER}}">
                    {% if LANGUAGE_CODE == 'ja'%}
                    メーター
                    {% else %}
                    Meter
                    {% endif %}
                </a>
            </div>

            <div class="module-management my-2" style="border-bottom: 2px solid rgb(221, 221, 221);"></div>

            {% for object in custom_objects %}
                <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if custom_object_slug == object.slug %} bg-dark {% endif %} tw-rounded-md">
                    <a class="fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if custom_object_slug == object.slug %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}"
                        href="{% host_url 'workspace_setting' host 'app' %}?custom_object={{object.slug}}"
                    >
                        {% if LANGUAGE_CODE == 'ja'%}
                        {{object.name}}
                        {% else %}
                        {{object.name}}
                        {% endif %}
                    </a>
                </div>
            {% endfor %}

            <div class="{% include "data/utility/workspace-settings-menu.html" %} {% if setting_type == 'it' %} bg-dark {% endif %} tw-rounded-md">
                <a class="custom-object-btn fs-2 fs-lg-5 tw-block fw-bold text-primary tw-w-full tw-h-full {% if setting_type == 'it' %} tw-text-white {% else %} tw-text-[#494949] hover:tw-text-[#472CF5] {% endif %}"
                    hx-get="{% host_url 'create_custom_object' host 'app' %}"
                    hx-target="#custom-object-content"
                >
                    {% if LANGUAGE_CODE == 'ja'%}
                    カスタムオブジェクトの作成
                    {% else %}
                    Create Custom Object
                    {% endif %}
                </a>
            </div>
        </div>
    </div>


    <div class="px-10 content-workspace-settings" style="">
        {% include 'data/shopturbo/settings/shopturbo-main-settings.html' %}
        {% include 'data/services/settings/services-main-settings.html' %}
        {% include 'data/account/workspace/settings/workspace_main_setting.html' %}
        {% include 'data/account/workspace/settings/custom-object-settings.html'%}
    </div>

    <div id="view-settings-wizard" class="bg-white h-100 w-100" data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".view-settings-button"
        data-kt-drawer-width="{'default':'90%','lg':'70%'}"
        >
        <div id="view-settings-drawer" class="w-100"></div>

    </div>

    <div id="apply-to-others-wizard" class="bg-white h-100 w-100"  data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".apply-to-others-button"
        data-kt-drawer-width="{'default':'70%','lg':'40%'}"
        >
        <div id="apply-to-others-content" class="w-100" style="z-index: 9999;" ></div>

    </div>

    <div id="custom-pdf-wizard" class="bg-white h-100 w-100"  data-kt-drawer="true" data-kt-drawer-activate="true"
        data-kt-drawer-toggle=".pdf-setting-button"
        data-kt-drawer-width="{'default':'70%','lg':'40%'}"
        >
        <div id="pdf-setting-content" class="w-100" style="z-index: 9999;" ></div>

    </div>
</div>

<script>
    $(document).ready(function() {
        var wizards = [
            { id: "view-settings-wizard", contentId: "view-settings-drawer" },
            { id: "apply-to-others-wizard", contentId: "apply-to-others-content" },
            { id: "custom-pdf-wizard", contentId: "pdf-setting-content" }
        ];

        wizards.forEach(function(wizard) {
            var wizardEl = document.getElementById(wizard.id);
            var wizardInstance = KTDrawer.getInstance(wizardEl);
            wizardInstance.on("kt.drawer.hide", function() {
                if (wizard.contentId) {
                    document.getElementById(wizard.contentId).innerHTML = '';
                    console.log('clear', wizard.contentId);
                }
            });
        });

        {% comment %} Set drawer overlay z-index for multiple drawer showing in the same time {% endcomment %}
        document.querySelectorAll('div[data-kt-drawer=true]').forEach(function(drawerElement){
            var drawerInstance = KTDrawer.getInstance(drawerElement);
            drawerInstance.on("kt.drawer.shown", function() {
                sortOverlayDrawer()
            })

            {% comment %} RESET {% endcomment %}
            drawerInstance.on("kt.drawer.after.hidden", function() {
                sortOverlayDrawer()
            })
        })

        function sortOverlayDrawer(){
            KTDrawer.createInstances();
            // Get all showing drawer
            const drawerElements = Array.from(document.querySelectorAll('.drawer-on'));

            // Sort elements front to back
            const drawersByDOMOrder = drawerElements.sort((a, b) => {
                if (a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING) {
                    // b is in front
                    return 1;
                } else {
                    // a is in front
                    return -1;
                }
            });
            var originalZIndex = 110
            drawersByDOMOrder.forEach(function(drawer_){
                drawer_.style.zIndex = originalZIndex
                originalZIndex -= 1;
            })
        }
    });

</script>

{% endblock %}