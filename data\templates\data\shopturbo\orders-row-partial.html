{% load tz %}
{% load custom_tags %}
{% load commerce_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% load formatting_tags %}
{% get_current_language as LANGUAGE_CODE %}

    {% if "checkbox" == row_type and view != "kanban" %}
    <td class="w-10px">

        <input id="order-selection-{{order.id}}" class="form-check-input cursor-pointer order-selection check_input" type="checkbox" name="checkbox" value="{{order.id}}" data-owner="{{order.owner.user.id}}" onclick="checking_checkbox(this, event)"/>
    </td>

    {% elif "order_id" == row_type and view != "kanban" %}
    <td class="fw-bold text-nowrap special-col min-w-50px" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
        <span class="d-none manage-full-wizard-button order_{{order.id}}_activity"
            hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
            hx-vals = '{"tab":"activity", "drawer_type":"manage-orders", "order_id":"{{order.id}}" ,"view_id":"{{view_id}}", "page": "{{page}}", "module": "{{module}}"}'
            hx-target="#manage-full-drawer-content"
            hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
            hx-trigger="click">
        </span>
        <div class="d-flex align-items-center">
            <a id="profile_wizard_button" class="text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage-full-wizard-button view_form_trigger{{order.id}} order_{{order.id}}"
                hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
                hx-vals = '{"drawer_type":"manage-orders", "order_id":"{{order.id}}" ,"view_id":"{{view_id}}", "page": "{{page}}", "module": "{{module}}"}'
                hx-target="#manage-full-drawer-content"
                hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                hx-trigger="click"

                {% comment %}
                Making create drawer = None. it is preventing js crash between manage and create drawer.
                The add item on create drawer will cannot be used after opening manage drawer.
                {% endcomment %}
                hx-on::before-request="
                    var create_drawer = document.getElementById('shopturbo-create-drawer-content');
                    console.log('create_drawer: ',create_drawer);
                    if (create_drawer){
                        create_drawer.innerHTML='';
                    }

                    var manage_drawer = document.getElementById('manage-full-drawer-content');
                    console.log('manage_drawer: ',manage_drawer);
                    if (manage_drawer){
                        manage_drawer.innerHTML='';
                    }"
            >


            {{order.order_id|stringformat:"04d"}}
            </a>
            {% if property_sets|length > 1 %}
            <div class="dropdown">
                <button type="button" class="btn btn-primary-outline text-dark text-hover-primary px-1 py-0 dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="d-inline-flex align-items-center justify-content-center" style="width: 20px; height: 20px;">
                        <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu">
                    {% for set in property_sets %}
                    <li>
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden manage-full-wizard-button" type="button"
                            hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
                            hx-vals = '{"drawer_type":"manage-orders", "order_id":"{{order.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "set_id": "{{set.id}}", "module": "{{module}}"}'
                            hx-target="#manage-full-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"

                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if set.name %}
                                {{ set.name}}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %}
                            {% endif %}
                        </button>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </td>
    <td class="" style="width: 20px;">
    </td>

    {% elif "contact" == row_type %}
    <td class="fw-bold">
            <a id="profile_wizard_button" class="{% include "data/utility/table-link-shopturbo.html" %}"
                hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
                hx-vals = '{"drawer_type":"manage-orders", "order_id":"{{order.id}}", "module": "{{module}}"}'
                hx-target="#shopturbo-drawer-content"
                hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                hx-trigger="click"
            >
            {% if order.contact%}
                {{order.contact.name}}
            {% endif %}
            </a>
    </td>

    {% elif "company" == row_type %}
    <td class="fw-bold">
            {% if order.company%}
                {{order.company.name}}
            {% endif %}
    </td>

    {% elif "customer" == row_type %}
    <td class="fw-bolder">
            {% if order.company %}
                {% include 'data/partials/partial-load-contact-company-drawer.html' with company=order.company %}
            {% elif order.contact %}
                {% include 'data/partials/partial-load-contact-company-drawer.html' with contact=order.contact %}
            {% endif %}
    </td>

    {% comment %} customer|contact|"field" {% endcomment %}
    {% elif "customer|" in row_type and "contact" in row_type %}
    <td class="fw-bold">
        {% with parts=row_type|split:'|' %}
            {% if order.contact %}
                {% with contact_value=row_type|get_value_customer_field:order.contact %}
                    {% if parts.2 == 'image_url' %}
                        {% if order.contact.image_url or order.contact.image_file %}
                            <div class="symbol symbol-30px">
                                <img alt="Pic" src="{{ contact_value }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                            </div>
                        {% else %}
                            <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                <svg class="h-30px w-30px" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor"/>
                                <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor"/>
                                <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor"/>
                                </svg>
                            </span>
                        {% endif %}

                    {% elif contact_value %}
                        {{contact_value}}

                    {% endif %}
                {% endwith %}
            {% endif %}
        {% endwith %}
    </td>

    {% elif "customer|" in row_type and "company" in row_type %}
    <td class="fw-bold">
        {% with parts=row_type|split:'|' %}
            {% if order.company %}
                {% with company_value=row_type|get_value_customer_field:order.company %}
                    {% if parts.2 == 'image_file' %}
                        {% if order.company.image_file %}
                            <div class="symbol symbol-30px">
                                <img alt="Pic" src="{{ company_value }}" style="object-fit:cover !important; aspect-ratio: 9 / 9;"/>
                            </div>
                        {% else %}
                            <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                <svg class="h-30px w-30px" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.3" d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z" fill="currentColor"/>
                                <path d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z" fill="currentColor"/>
                                <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor"/>
                                </svg>
                            </span>
                        {% endif %}
                    {% elif company_value %}
                        {% if company_value|is_json_string %}
                            {% for tag in company_value|get_json %}
                            <div class="fw-bold mb-1">
                                <span class="badge bg-gray-500">{{tag.value}}</span>
                            </div>
                            {% endfor %}
                        {% else %}
                            {{company_value}}
                        {% endif %}
                    {% endif %}
                {% endwith %}
            {% endif %}
        {% endwith %}
    </td>

    {% elif "contact|" in row_type %}
    <td class="fw-bold">
                {% with contact_value=row_type|get_value_customer_field:order %}
                    {% if contact_value %}
                        {{contact_value}}
                    {% endif %}
                {% endwith %}
    </td>

    {% elif "company|" in row_type %}
    <td class="fw-bold">
            {% with company_value=row_type|get_value_customer_field:order %}
                {% if company_value %}
                    {{company_value}}
                {% endif %}
            {% endwith %}
    </td>

    {% elif "owner" == row_type %}
    <td class="fw-bold">
        {% if order.owner and order.owner.user %}
            {% with order.owner.user as user %}
            {% if user.verification.profile_photo %}
            <img class="w-20px rounded-circle me-2" src="{{user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
            {% else %}
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                    <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                </svg>    
            {% endif %}
            <span >
                {{user.first_name}}
            </span>
            {% endwith %}
        {% endif %}
    </td>
    {% elif "order_type" == row_type %}
    <td class="fw-bold">
            {% translate_lang order.get_order_type_display LANGUAGE_CODE %}
    </td>

    {% elif "currency" == row_type %}
    <td class="fw-bold">
            {% if order.currency %}
                {{order.currency}}
            {% endif %}
    </td>

    {% elif "line_item_name" == row_type %}
    <td class="fw-bold">
            {% if order.order_type == 'item_order' %}
                {% for item_order in line_items %}
                    {% if not item_id or item_id == item_order.id|to_str %}
                        <div>
                            {{item_order.custom_item_name|truncate_long_text}}
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
    </td>

    {% elif "line_item" == row_type %} {% comment %} artificial column {% endcomment %}
    <td class="fw-bold">
            {% if order.order_type == 'item_order' %}
                {% for item_order in line_items %}
                    <div>
                        {{item_order.custom_item_name|truncate_long_text}}
                    </div>
                {% endfor %}
            {% endif %}
    </td>

    {% elif "subscription" == row_type or "commerce_subscription" == row_type %}
    <td class="fw-bold">
            {% if order.subscription %}
                <div class="d-flex">
                    <a id="profile_wizard_button" class="{% include "data/utility/table-link.html" %} manage-full-wizard-button"
                        hx-get="{% host_url 'load_manage_subscriptions_drawer' host 'app' %}"
                        hx-target="#manage-full-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true", "drawer_type":"subscriptions-manage", "subscription_id":"{{order.subscription.id}}"}'>
                        <div class="mb-1">
                            #{{order.subscription.subscriptions_id|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}サブスクリプション{% else %}Subscription{% endif %}
                        </div>
                    </a>
                </div>
            {% endif %}
    </td>

    {% elif "invoice" == row_type or "invoices" == row_type %}
    <td class="fw-bold">
        {% for invoice in order.invoice.all %}
            <a class="{% include "data/utility/table-link.html" %} create-association-wizard-button"
                hx-get="{% host_url 'invoice_edit' invoice.id host 'app' %}"
                hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true"}'
                hx-trigger="click"
                hx-target="#create-association-wizard-content"
                hx-indicator=".loading-drawer-spinner,#create-association-wizard-content"
                hx-on::before-request="document.getElementById('create-association-wizard-content').innerHTML = ''"
                >
                <div class="mb-1">
                    #{{invoice.id_inv|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}請求書{% else %}Invoice{% endif %}
                </div>
            </a>
        {% endfor %}
    </td>

    {% elif "estimate" == row_type or "estimates" == row_type %}
    <td class="fw-bold">
        {% for estimate in order.estimate.all %}
        <div class="d-flex">
            <a class="{% include "data/utility/table-link.html" %} create-association-wizard-button"
                hx-get="{% host_url 'estimate_edit' estimate.id host 'app' %}"
                hx-target="#create-association-wizard-content"
                hx-trigger="click"
                hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true"}'
                hx-indicator=".loading-drawer-spinner"
                hx-on::before-request="document.getElementById('create-association-wizard-content').innerHTML = ''"
                >
                <div class="mb-1">
                    #{{estimate.id_est|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}見積{% else %}Estimate{% endif %}
                </div>
            </a>
        </div>
        {% endfor %}
    </td>

    {% elif "line_item_name_quantity" == row_type %}
    <td class="fw-bold">
            {% if order.order_type == 'item_order' %}
                {% for item_order in line_items %}
                    {% if not item_id or item_id == item_order.id|to_str %}
                        <div>
                            {{item_order.custom_item_name|truncate_long_text}} x {{item_order.number_item|float_to_int}}
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
    </td>



    {% elif "line_item_status" == row_type %}
        <td class="fw-bold">
            {% if item_status_choices and order.order_type == 'item_order' %}
                {% for item_order in line_items %}
                    {% if not item_id or item_id == item_order.id|to_str %}
                        <div class="text-start">
                            {% if item_order.item_status %}
                                {% with options=item_status_choices|string_list_to_list %}
                                    {% for option in options %}
                                        {% if item_order.item_status == option.value %}
                                            {{option.label}}
                                        {% endif %}
                                    {% endfor %}
                                {% endwith %}
                            {% else %}
                                -
                            {% endif %}
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        </td>

    {% elif "line_item_inventory_location" == row_type %}
        <td class="fw-bold">
            {% if order.order_type == 'item_order' %}
                {% for item_order in line_items %}
                    {% if not item_id or item_id == item_order.id|to_str %}
                        <div class="text-start">
                            {% if item_order.item %}
                                {{item_order.custom_item_name|truncate_long_text}} - {% with inventory=item_order.item.shopturboinventory_set.all.first %}
                                    {% if inventory %}
                                        {% if inventory.warehouse %}
                                            {{inventory.warehouse.id|get_inventory_warehouse_name:request}}
                                        {% endif %} - {{inventory|get_and_update_inventory_amount_display|use_thousand_separator}}
                                    {% endif %}
                                {% endwith %}
                            {% else %}
                            -
                            {% endif %}
                        </div>
                    {% endif %}
                {% endfor %}
            {% endif %}
        </td>

    {% elif "platform_order_id" == row_type %}
    <td class="fw-bold">
            {% if order.order_platforms.all %}
                {% for item_platform in order.order_platforms.all %}
                    <div class="fw-bold mb-2">
                        {{item_platform.platform_order_id}}
                    </div>
                {% endfor %}
            {% endif %}
    </td>

    {% elif "order_at" == row_type %}
    <td class="fw-bold">
            {% if order.order_at %}
                {% date_format order.order_at 1 %}
            {% endif %}
    </td>

    {% elif "number_item" == row_type %}
    <td class="fw-bold">
            {% if order.number_item %}
                {{order.number_item}}
            {% endif %}
    </td>

    {% elif "line_item_tax" == row_type %}
    <td class="fw-bold">
            {% if order.order_type == 'item_order' %}
                {% for item_order in line_items %}
                    {% if not item_id or item_id == item_order.id|to_str %}
                        <div>

                            {{item_order.custom_item_name|truncate_long_text}}
                            -

                            {% if item_order.item_price_order_tax %}
                                {{ item_order.item_price_order_tax }} %
                            {% elif item_order.item and item_order.item_price %}
                                {{item_order.item_price.tax}} %
                            {% else %}
                                0 %
                            {% endif %}

                        </div>
                    {% endif %}
                {% endfor %}
            {% elif order.order_type == 'manual_order' %}
                -
            {% endif %}
    </td>
    {% elif "line_item_price" == row_type %}
    <td class="fw-bold">
            {% if order.order_type == 'item_order' %}
                {% for item_order in line_items %}
                    {% if not item_id or item_id == item_order.id|to_str %}
                        <div>
                            {% if item_order.item %}
                                {{item_order.custom_item_name|truncate_long_text}} - {{item_order.currency|get_currency_symbol}}
                                {% if item_order.item_price %}

                                    {% if item_order.item_price.volume_price %}

                                        {% for volume_price in item_order.item_price.shopturbo_item_volume_price.all %}

                                            <div>
                                                - {% if LANGUAGE_CODE == 'ja' %}最小数量 {% else %}Min:{% endif %} {{volume_price.minimum}}
                                                - {% if LANGUAGE_CODE == 'ja' %}最大数量 {% else %}Max:{% endif %} {{volume_price.maximum}}
                                                - {% if LANGUAGE_CODE == 'ja' %}価格 {% else %}Price:{% endif %} {{volume_price.price|use_thousand_separator_string_with_currency:item_order.currency}}
                                            </div>

                                        {% endfor %}
                                    {% else %}
                                        {{item_order.item_price.price|use_thousand_separator_string_with_currency:item_order.currency}}
                                    {% endif %}






                                {% else %}
                                    {{item_order.item|get_default_price_from_item}}
                                {% endif %}
                            {% else %}
                                {{item_order.custom_item_name|truncate_long_text}} - {{item_order.currency|get_currency_symbol}}
                                {% if item_order.item_price_order %}
                                    {{item_order.item_price_order|use_thousand_separator_string_with_currency:item_order.currency}}
                                {% else %}
                                    0
                                {% endif %}
                            {% endif %}
                        </div>
                    {% endif %}
                {% endfor %}
            {% elif order.order_type == 'manual_order' %}
                -
            {% endif %}
    </td>

    {% elif "order-tax" == row_type %}
    <td class="fw-bold">
                {% if order.tax_applied_to == "all" %}
                    {% if LANGUAGE_CODE == 'ja'%}
                        商品＋送料
                    {% else %}
                        Items + Shipping Fee
                    {% endif %}
                    :
                {% elif order.tax_applied_to == "only_items" %}
                    {% if LANGUAGE_CODE == 'ja'%}
                        商品のみ
                    {% else %}
                        Only Items
                    {% endif %}
                    :
                {% elif order.tax_applied_to == "only_shipping" %}
                    {% if LANGUAGE_CODE == 'ja'%}
                        送料のみ
                    {% else %}
                        Only Shipping
                    {% endif %}
                    :
                {% endif %}

                {% if order.tax %}
                    {{order.tax}} %
                {% else %}
                    0 %
                {% endif %}
    </td>


    {% elif "total_price_without_tax" == row_type %}
    <td class="fw-bold">
            {% if order.total_price_without_tax %}
                {{order.currency|convert_currency_to_symbol}}

                {% with decimal_var=decimal_point.type|addstr:","|addstr:order.currency %}
                    {% if 'line_item' in decimal_var %}
                        {% with total_price_wt_tax=order.id|calculate_total_price_without_tax_decimal_point:decimal_var %}
                            {{total_price_wt_tax|use_thousand_separator_string_with_currency:order.currency}}
                        {% endwith %}
                    {% else %}
                        {% with total_price_wt_tax=order.total_price_without_tax|decimal_point:decimal_var %}
                            {{total_price_wt_tax|use_thousand_separator_string_with_currency:order.currency}}
                        {% endwith %}
                    {% endif %}
                {% endwith %}
            {% endif %}
    </td>

    {% elif "total_price" == row_type %}
    <td class="fw-bold">
            {{order.currency|convert_currency_to_symbol}}

            {% with decimal_var=decimal_point.type|addstr:","|addstr:order.currency %}
                {% if 'line_item' in decimal_var %}
                    {% with total_price=order.id|calculate_total_price_decimal_point:decimal_var %}
                        {{total_price|use_thousand_separator_string_with_currency:order.currency}}
                    {% endwith %}
                {% else %}
                    {% with total_price=order.total_price|decimal_point:decimal_var %}
                        {{total_price|use_thousand_separator_string_with_currency:order.currency}}
                    {% endwith %}
                {% endif %}
            {% endwith %}
    </td>


    {% elif "company" == row_type or "platform" == row_type %}
    <td class="fw-bold">
            {{ order|get_value_of_attribute:row_type }}
    </td>

    {% elif "notes" == row_type %}
    <td class="fw-bold">
            {% if order.notes %}
                {{order.notes}}
            {% endif %}
    </td>

    {% elif "discount" == row_type %}
    <td class="fw-bold">
            {% if order.discounts %}
                {{order.discounts.name}} -
                {% if order.discounts.number_format == "%" %}
                    {{order.discounts.value}} %
                {% else %}
                    {{order.discounts.number_format|upper}} {{order.discounts.value}}
                {% endif %}
            {% endif %}
    </td>

    {% elif "status" == row_type %}
    <td class="fw-bold">

        {% translate_lang order.get_status_display LANGUAGE_CODE %}

    </td>

    {% elif "delivery_status" == row_type %}
    <td class="fw-bold">
            {% if order.delivery_status %}
                {% with delivery_status_property_object='delivery_status'|get_custom_property_object:order %}
                    {% if delivery_status_property_object %}
                        {% with value_map_label=delivery_status_property_object|get_attr:'value'|string_list_to_list %}
                            {{value_map_label|get_line_item_dict_value:order.delivery_status}}
                        {% endwith %}
                    {% else %}
                        {{order.delivery_status|display_status_orders:request}}
                    {% endif %}
                {% endwith %}
            {% endif %}
    </td>

    {% elif "created_at" == row_type %}
    <td class="fw-bold">
            {% date_format order.created_at 1 %}
    </td>

    {% elif "updated_at" == row_type %}
    <td class="fw-bold">
            {% date_format order.updated_at 1 %}

    {% elif "-item" in row_type|lower %}
    <td class="fw-bold">
            {% for item in order.shopturboitemsorders_set.all|filter_order:"order_view" %}
            <div class="fw-bold">
                <a id="profile_wizard_button" class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item.item.id}}"
                    hx-get="{% url 'shopturbo_load_drawer' %}"
                    hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item.item.id}}" }'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                    hx-trigger="click"
                >
                {{item.item.name}}
                </a>
            </div>
            {% endfor %}
    </td>

    {% elif "item property" in row_type|lower or "商品のプロパティ" in row_type|lower %}
    <td class="fw-bold">
            {% for item in order.shopturboitemsorders_set.all|filter_item_property_in_order:row_type %}
            <div class="fw-bold">
                <a id="profile_wizard_button" class="{% include "data/utility/table-link-shopturbo.html" %} item_{{item.item.id}}"
                    hx-get="{% url 'shopturbo_load_drawer' %}"
                    hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item.items.id}}" }'
                    hx-target="#shopturbo-drawer-content"
                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                    hx-trigger="click"
                >
                #{{item.items.item_id|stringformat:"04d"}} {{item.value}}
                </a>
            </div>
            {% endfor %}
    </td>

    {% elif "-payment status" in row_type|lower %}
    <td class="fw-bold">
            {{order.id|order_platform_payment_status:row_type}}
    </td>
    
    {% elif "-paid_at" in row_type|lower %}
    <td class="fw-bold">
        {% with paid_at=order|order_platform_paid_at:row_type %}
            {% if paid_at %}
                {% date_format paid_at 1 %}
            {% endif %}
        {% endwith %}
    </td>

    {% elif "-fulfillment status" in row_type|lower %}
    <td class="fw-bold">
            {{order.id|order_platform_fulfillment_status:row_type}}
    </td>

    {% elif "-order status" in row_type|lower %}
    <td class="fw-bold">
            {{order.id|order_platform_order_status:row_type}}
    </td>


    {% elif "-order id" in row_type|lower or "-deal id" in row_type|lower %}
    <td class="fw-bold">
        {% comment %} #NOTE (Faris) : Change from old code getting the ordr platform id to the new one 3/9/24 {% endcomment %}
            {% with row_type_lower=row_type|lower %}
                {% with value_order_id=order.id|order_platform_order_id:row_type_lower %}
                    {% if value_order_id %}#{{value_order_id}}{% endif %}
                {% endwith %}
            {% endwith %}
    </td>

    {% elif "platform_id" in row_type|lower %}
    <td class="fw-bolder">
        {% if order.order_platforms.all %}
            {% for item_platform in order.order_platforms.all %}
                {{item_platform.platform_order_id}}
            {% endfor %}
        {% endif %}
    </td>

    {% elif "shipping_info|" in row_type|lower %}
    <td class="fw-bold">
            {% with parts=row_type|split:'|' %}
                {% with CustomFieldName=customfields_map_id|get_attr:parts.1 %}
                    {% with customfield_value=CustomFieldName.id|get_value_custom_field_orders:order.id %}
                        {% if parts.3 == 'id' %}
                            {% for key in customfield_value|string_list_to_list %}
                                {% if forloop.counter == 1 %}
                                    {% if customfield_value|string_list_to_list|get_attr:key|length == 1 %}
                                        {{ order.order_id|stringformat:"04d" }}-{{ forloop.counter }}
                                    {% else %}
                                        {% for value in customfield_value|string_list_to_list|get_attr:key %}
                                            #{{ order.order_id|stringformat:"04d" }}-{{ forloop.counter }}<br>
                                        {% endfor %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                        {% for value in customfield_value|string_list_to_list|get_attr:parts.3 %}
                            #{{ order.order_id|stringformat:"04d" }}-{{ forloop.counter }}&nbsp;&nbsp;{{ value }}<br>
                        {% endfor %}
                    {% endwith %}
                {% endwith %}
            {% endwith %}
    </td>

    {% elif row_type == 'line_item_quantity' %}
    <td class="fw-bold">
        {% if order.order_type == 'item_order' %}
            {% for item_order in line_items %}
                {% if not item_id or item_id == item_order.id|to_str %}
                    <div>
                        {{item_order.number_item|float_to_int}}
                    </div>
                {% endif %}
            {% endfor %}
        {% endif %}
    </td>

    {% elif "line_item|" in row_type|lower %}
    <td class="fw-bold">
        {% with parts=row_type|split:'|' %}
            {% for item_order in line_items %}
                {% if not item_id or item_id == item_order.id|to_str %}
                    <div>

                        #{{item_order.item.item_id|stringformat:"04d"}}
                        {% get_value_custom_line_item item_order.id parts.1 "commerce_orders" as custom_line_item_field_value %}
                        {{custom_line_item_field_value.value}}

                    </div>
                {% endif %}
            {% endfor %}
        {% endwith %}
    </td>

    {% elif row_type|startswith:'item__' %}
    <td class="fw-bold">
            {% if order.order_type == 'item_order' %}
                {% for item_order in line_items %}
                    {% if not item_id or item_id == item_order.id|to_str %}
                        <div>
                            {% if item_order.item %}
                                {% if item_order.item.purchase_price != None %}
                                    {% if item_order.item.currency %}{{ item_order.item.currency|convert_currency_to_symbol }}{% endif %} {{item_order.item.purchase_price|use_thousand_separator_string_with_currency:item_order.item.currency}}
                                {% endif %}
                            {% else %}
                                -
                            {% endif %}
                        </div>
                    {% endif %}
                {% endfor %}
            {% elif order.order_type == 'manual_order' %}
                -
            {% endif %}
    </td>

    {% elif row_type == 'source_item' %}
    <td class="fw-bold">
        {% if order.order_type == 'item_order' %}
            {% for item_order in line_items %}
                {% if not item_id or item_id == item_order.id|to_str %}
                    <div>
                        {% if item_order.item %}
                            <a class="{% include "data/utility/table-link.html" %} manage-full-wizard-button item_{{item_order.item.id}}"
                                hx-get="{% url 'shopturbo_load_drawer' %}"
                                hx-vals = '{"drawer_type":"item-manage", "item_id":"{{item_order.item.id}}", "view_id":"" }'
                                hx-target="#manage-full-drawer-content"
                                hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
                                hx-trigger="click"
                            >
                                {% get_object_display item_order.item 'commerce_items' %}
                            </a>
                        {% else %}
                            {{item_order.custom_item_name|truncate_long_text}}
                        {% endif %}
                    </div>
                {% endif %}
            {% endfor %}
        {% endif %}
    </td>

    {% elif row_type == 'inventory_transactions' %}
    <td class="fw-bold">
        {%if order.usage_status != 'archived'%}
            {% for transaction in order.inventory_transactions.all %}
                <a id="profile_wizard_button" class="mb-0 text-center cursor-pointer text-dark text-hover-primary fw-bolder manage-full-wizard-button transaction_{{transaction.id}}"
                    hx-get="{% url 'shopturbo_load_drawer' %}"
                    hx-vals = '{"drawer_type":"inventory-transaction-manage", "transaction_id":"{{transaction.id}}", "module":"{{menu_key}}" }'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner"
                    hx-trigger="click"
                >
                    #{{ transaction.transaction_id|stringformat:"04d" }}
                </a>
            {% endfor %}
        {%endif%}
    </td>

    {% elif row_type == 'payment_link' %}
    <td class="fw-bold">
        {%if order.usage_status != 'archived' and order.platform_payment_link %}
            <div>
                {{order.platform_payment_link}}
            </div>
        {%endif%}
    </td>

    {% elif row_type == 'warehouse_inventory_amount' %}
        <td class="fw-bold">
            {%if order.usage_status != 'archived' %}
                <div hx-get="{% url 'warehouse_inventory_amount' %}?object_id={{order.id}}&page_group_type=commerce_orders"
                hx-trigger="load">
                </div>
            {%endif%}
        </td>

    {% elif row_type|startswith:'source_item__' %}
    <td>
        {% if order.order_type == 'item_order' %}
            {% for item_order in line_items %}
                {% if not item_id or item_id == item_order.id|to_str %}
                    {% if item_order.item %}
                        {% include 'data/shopturbo/items-row-partial.html' with as_sub_cell=True shopturbo_items_columns=row_type|cut:'source_item__'|object_to_list item=item_order.item %}
                    {% else %}
                        <div>
                        -
                        </div>
                    {% endif %}
                {% endif %}
            {% endfor %}
        {% endif %}
    </td>
    {% elif 'order_platform|' in row_type %}
        <td class="fw-bold">
            <div>
            {% with channel_id=row_type|cut:'order_platform|'|slice:':36' field=row_type|cut:'order_platform|'|slice:'37:' %}
                {% for platform in order.order_platforms.all %}
                    {% if platform.channel.id|stringify == channel_id %}
                        {% if field == 'payment_status' %}
                            {% get_stripe_payment_status_label platform|get_attr:field %}
                        {% else %}
                            {{platform|get_attr:field}}
                        {% endif %}
                    {% endif %}
                {% endfor %}
            {% endwith %}
            </div>
        </td>

    {% elif row_type == 'associate#purchase_orders' %}
        <td class="fw-bold">
            <div class="text-start">
                {% if order.purchase_orders.all %}
                    {% for purchase_order in order.purchase_orders.all %}
                        <a
                            hx-get="{% host_url 'purchase_manage' purchase_order.id host 'app' %}"
                            hx-target="#manage-full-drawer-content"
                            hx-trigger="click"
                            hx-vals = '{"set_id": "{{set_id}}", "source": "{{constant.TYPE_OBJECT_ORDER}}" }'
                            hx-indicator=".loading-drawer-spinner"
                            class="text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage_full_wizard_button">

                            {{purchase_order.id_po|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}発注{% else %}Purchase Order{% endif %}
                        </a>
                        <br>
                    {% endfor %}
                {% endif %}
            </div>
        </td>
    {% else %}
    <td class="fw-bold">
        {% if row_type in customfields_map_id %}
            {% with CustomFieldName=customfields_map_id|get_attr:row_type %}
                {% if CustomFieldName.type == 'formula' %}
                    <div class="d-none"
                        hx-post="{% url 'get_formula_result' %}"
                        hx-vals='{"obj_id":"{{order.id}}","custom_field_id":"{{CustomFieldName.id}}","object_type":"{{constant.TYPE_OBJECT_ORDER}}"}'
                        hx-target="#shopturbo-formula-value-{{order.id}}-{{CustomFieldName.id}}"
                        hx-indicator=".loading-spinner"
                        hx-trigger="load"
                        hx-swap="innerHTML"
                    ></div>

                    <div class="loading-spinner ms-2 mb-2">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <div id="shopturbo-formula-value-{{order.id}}-{{CustomFieldName.id}}" ></div>
                {% endif %}
            {% endwith %}
        {% endif %}
        {% for custom_field_value in order.shopturbo_custom_field_relations.all %}
            {% with CustomFieldName=custom_field_value.field_name %}
                {% if CustomFieldName.id|stringify == row_type %}
                    {% if CustomFieldName.type == 'number' %}
                        <div class="fw-bold">
                        {% with customfield_value=custom_field_value.value %}
                            {% if CustomFieldName.number_format == '%' %}
                                {{customfield_value|floatformat:2}} {{CustomFieldName.number_format|upper}}
                            {% elif CustomFieldName.number_format == 'number' %}
                                {{customfield_value|floatformat:2}}
                            {% else %}
                                {{CustomFieldName.number_format|upper|get_currency_symbol}} {{customfield_value|use_thousand_separator_string_with_currency:CustomFieldName.number_format}}
                            {% endif %}
                        {% endwith %}
                        </div>

                    {% elif CustomFieldName.type == 'image' %}
                        <div class="ms-2">
                            {% if custom_field_value.shopturboordersvaluefile_set.last %}
                                {% with value_custom_field=custom_field_value.shopturboordersvaluefile_set.last %}
                                    <a target="_blank" href="{{value_custom_field.file.url}}">
                                        <div class="symbol symbol-lg-50px symbol-50px">
                                            <img style="object-fit: cover;" alt="Pic" src="{{value_custom_field.file.url}}" />
                                        </div>
                                    </a>
                                {% endwith %}
                            {% endif %}
                        </div>

                    {% elif CustomFieldName.type == 'image_group' %}
                        <div class="ms-2 d-flex">
                            {% if custom_field_value.shopturboordersvaluefile_set.all %}
                                {% with value_custom_field=custom_field_value.shopturboordersvaluefile_set.all %}
                                    {% for value_image in value_custom_field %}
                                        <a target="_blank" href="{{value_image.file.url}}" class="me-2">
                                            <div class="symbol symbol-lg-50px symbol-50px">
                                                <img style="object-fit: cover;" alt="Pic" src="{{value_image.file.url}}" />
                                            </div>
                                        </a>
                                    {% endfor %}
                                {% endwith %}
                            {% endif %}
                        </div>

                    {% elif CustomFieldName.type == 'user' %}
                        <div class="fw-bold">
                            {% with user_id=custom_field_value.value %}
                                {% if user_id %}
                                    {% with user=user_id|convert_user_id_to_user %}
                                        {% if user %}
                                            {{user.first_name}}
                                        {% endif %}
                                    {% endwith %}
                                {% endif %}
                            {% endwith %}
                        </div>

                    {% elif CustomFieldName.type == 'tag' %}
                        <div class="">
                            {% if custom_field_value.value %}
                                {% for tag in custom_field_value.value|get_json %}
                                    <div class="fw-bold mb-1">
                                        <span class="badge bg-gray-500">{{tag.value}}</span>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        </div>

                    {% elif CustomFieldName.type == 'date' %}
                        <div class="fw-bold">
                            {% if custom_field_value.value_time %}
                                {% date_format custom_field_value.value_time %}
                            {% endif %}
                        </div>
                    {% elif CustomFieldName.type == 'date_time' %}
                        <div class="fw-bold">
                            {% if custom_field_value.value_time %}
                                {% date_format custom_field_value.value_time 1 %}
                            {% endif %}
                        </div>
                    {% elif CustomFieldName.type == 'date_range' %}
                        <div class="fw-bold">
                            {% if custom_field_value.value %}
                                {% for date in custom_field_value.value|split:' - ' %}
                                    {{ date|date_display:request }} {% if not forloop.last %} - {% endif %}
                                {% endfor %}
                            {% endif %}
                        </div>
                    {% elif CustomFieldName.type == 'contact' %}
                        {% with contact=custom_field_value.value|get_contact_obj %}
                            {% if contact %}
                                <div class="fw-bold">
                                    <a id="profile_wizard_button" class="{% include "data/utility/table-link-shopturbo.html" %} contact_{{contact.id}}"
                                        hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                                        hx-target="#shopturbo-drawer-content"
                                        hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                                        hx-trigger="click"
                                    >
                                        {{contact.id|display_contact_name:LANGUAGE_CODE}}
                                    </a>
                                </div>
                            {% endif %}
                        {% endwith %}
                    {% elif CustomFieldName.type == 'company' %}
                        {% with company=custom_field_value.value|get_company_obj %}
                            {% if company %}
                                <div class="fw-bold">
                                    <a id="profile_wizard_button" class="{% include "data/utility/table-link-shopturbo.html" %} company_{{company.id}}"
                                        hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                                        hx-target="#shopturbo-drawer-content"
                                        hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                                        hx-trigger="click"
                                    >
                                        {{company.company_id|stringformat:"04d"}}-{{company.name}}
                                    </a>
                                </div>
                            {% endif %}
                        {% endwith %}
                    {% elif CustomFieldName.type == 'invoice_objects' %}
                        {% with invoice=custom_field_value.value|get_invoice_obj %}
                            {% if invoice %}
                                {% include "data/common/custom_field/invoice_object.html" with invoice=invoice drawer_id="profile_wizard_button" drawer_class="shopturbo-manage-wizard-button" htmx_target="#shopturbo-drawer-content" %}
                            {% endif %}
                        {% endwith %}
                    {% elif CustomFieldName.type == 'bill_objects' %}
                        {% with bill=custom_field_value.value|get_bill_obj %}
                            {% if bill.id %}
                                <div class="fw-bold">
                                    <a id="profile_wizard_button" class="{% include "data/utility/table-link-shopturbo.html" %} bill_{{bill.id}}"
                                        hx-get="{% host_url 'bill_manage' bill.id host 'app' %}"
                                        hx-target="#shopturbo-drawer-content"
                                        hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
                                        hx-trigger="click"
                                    >
                                        #{{bill|stringformat:"04d"}} - {{bill.title}}
                                    </a>
                                </div>
                            {% endif %}
                        {% endwith %}
                    {% elif CustomFieldName.type == 'choice' %}
                        {% if custom_field_value.value %}

                            {% for choice in CustomFieldName.choice_value|string_list_to_list %}
                                {% if ';' in custom_field_value.value and CustomFieldName.multiple_select %}
                                    {% for val in custom_field_value.value|split:";" %}
                                        {% if choice.value == val %}
                                            {% if CustomFieldName.show_badge %}
                                            <div class="fw-bold">
                                                <span class="d-inline-flex align-items-center">
                                                    <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                                                    {{ choice.label }}
                                                </span>
                                            </div>
                                            {% else %}
                                            <div class="fw-bold">
                                                {{ choice.label }}
                                            </div>
                                            {% endif %}
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    {% if choice.value == custom_field_value.value %}
                                        <div class="fw-bold">
                                            {% if CustomFieldName.show_badge %}
                                            <span class="d-inline-flex align-items-center">
                                                <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                                                {{ choice.label }}
                                            </span>
                                            {% else %}
                                            {{ choice.label }}
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}

                        {% endif %}
                    {% elif CustomFieldName.type == 'purchase_order'%}
                        {% with purchase_order=custom_field_value.value|get_purchase_order_obj %}
                            {% if purchase_order %}
                                <div class="fw-bold">
                                    <a  class="manage-full-wizard-button text-center mb-0 text-dark text-hover-primary cursor-pointer"
                                        hx-indicator=".loading-drawer-spinner,.procurement-full-form"
                                        hx-get="{% host_url 'purchase_manage' purchase_order.id host 'app' %}"
                                        hx-target="#manage-full-drawer-content"
                                        hx-trigger="click"
                                    >
                                        #{{ purchase_order.id_po|stringformat:"04d" }} | {{purchase_order.currency|upper}} {{purchase_order.total_price|use_thousand_separator_string_with_currency:purchase_order.currency}}
                                    </a>
                                </div>
                            {% endif %}
                        {% endwith %}
                    {% elif CustomFieldName.type != 'formula' %}
                        <div class="fw-bold">
                            {{ custom_field_value.value }}
                        </div>
                    {% endif %}
                {% endif %}
            {% endwith %}
        {% endfor %}
    </td>
    {% endif %}



