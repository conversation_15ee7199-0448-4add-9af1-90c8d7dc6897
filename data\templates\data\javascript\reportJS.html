<script>
    var updateUrlParameter = (uri, key, value) => {
        // remove the hash part before operating on the uri
        const i = uri.indexOf('#');
        const hash = i === -1 ? '' : uri.substr(i);
        uri = i === -1 ? uri : uri.substr(0, i);

        const re = new RegExp(`([?&])${key}=.*?(&|$)`, 'i');
        const separator = uri.indexOf('?') !== -1 ? '&' : '?';

        if (value === null) {
            // remove key-value pair if value is specifically null
            uri = uri.replace(new RegExp(`([?&]?)${key}=[^&]*`, 'i'), '');
            if (uri.slice(-1) === '?') {
                uri = uri.slice(0, -1);
            }
            // replace first occurrence of & by ? if no ? is present
            if (uri.indexOf('?') === -1) uri = uri.replace(/&/, '?');
        } else if (uri.match(re)) {
            uri = uri.replace(re, `$1${key}=${value}$2`);
        } else {
            uri = `${uri + separator + key}=${value}`;
        }
        return uri + hash;
    };

    // Defer Tagify initialization to improve initial load performance
    function initializeTagify() {
        var input1 = document.querySelector("#kt_tagify_1");
        if (input1){
            var tagify = new Tagify(input1, {
                callbacks: {
                    add: function (e) {
                        var val = e.detail.data.value
                        if (val == '@') {
                            tagify.removeTag(e.detail.tag)
                        } else if (!val.startsWith('@')) {
                            tagify.removeTag(e.detail.tag)
                            tagify.addTags("@" + val)
                        }
                        if( e.detail.index > 3 ) {
                            tagify.removeTag(e.detail.tag);
                        };
                    },
                }
            });
        }

        var input2 = document.querySelector("#kt_tagify_2");
        if (input2){
            var tagify2 = new Tagify(input2, {
                callbacks: {
                    add: function (e) {
                        var val = e.detail.data.value
                        if (val == '@') {
                            tagify2.removeTag(e.detail.tag)
                        } else if (!val.startsWith('@')) {
                            tagify2.removeTag(e.detail.tag)
                            tagify2.addTags("@" + val)
                        }
                        if( e.detail.index > 3 ) {
                            tagify2.removeTag(e.detail.tag);
                        };
                    },
                }
            });
            {% if selected_report %}
            $("#kt_tagify_2").ready(function() {
                console.log("loading...");
                const competitors = {{selected_report.get_competitors|safe}};
                console.log(competitors);
                competitors.forEach((value, _) => {
                    console.log(value);
                    tagify2.addTags("@"+value);
                });
            });
            {% endif %}
        }
    }

    // Initialize Tagify only when needed (defer for performance)
    if (document.querySelector("#kt_tagify_1") || document.querySelector("#kt_tagify_2")) {
        // Use requestIdleCallback for better performance, fallback to setTimeout
        if (window.requestIdleCallback) {
            requestIdleCallback(initializeTagify);
        } else {
            setTimeout(initializeTagify, 100);
        }
    }
    

</script>