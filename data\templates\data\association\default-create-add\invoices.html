{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}


<div class="card shadow-none rounded-0 w-100 vh-100 overflow-hidden border-0 bg-white">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if LANGUAGE_CODE == 'ja' %}
                売上請求レコードを追加
            {% else %}
                Add Invoice Record
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon me-5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    <div class="row px-10 mt-5">
        <div class="col-6">
            <div class="btn-group w-100" role="group">
                <input checked type="radio" class="w-100 text-center btn-check single-entry-section-switcher" name="switch" id="kt_switch_option_1"
                /> 
                <label class="w-100 btn btn-outline btn-outline-dashed btn-outline-default p-4" for="kt_switch_option_1">
                    <span class="text-dark fw-bolder d-block fs-6">
                        {% if LANGUAGE_CODE == 'ja'%}
                        売上請求レコードの作成
                        {% else %}
                        Create Invoice Record
                        {% endif %}
                    </span>
                </label>
            </div>
        </div>
        <div class="col-6">
            <div class="btn-group w-100" role="group">
                <input type="radio" class="w-100 text-center btn-check upload-entry-section-switcher" name="switch" id="kt_switch_option_3"
                />
                <label class="w-100 btn btn-outline btn-outline-dashed btn-outline-default p-4" for="kt_switch_option_3">
                    <span class="text-dark fw-bolder d-block fs-6">
                        {% if LANGUAGE_CODE == 'ja'%}
                        売上請求レコードを選択
                        {% else %}
                        Select Invoice Record
                        {% endif %}
                    </span>
                </label>
            </div>
        </div>
    </div>
    
    <div class='d-none'
        hx-get="{% url object_type|to_drawer_type|add:'_drawer' %}" 
        hx-vals='{"drawer_type":"{{object_type|to_drawer_type}}", {% if view_id %}"view_id":"{{view_id}}", {% endif %} "object_id":"{{object_id}}", "source":"{{source}}", "module":"{{module}}", "type":"create-association"}'
        hx-swap="beforeend"
        hx-target="#create-drawer-content" 
        hx-trigger="load"
    >
    </div>
    
    <div id="create-drawer-content" class="scroll-y px-0">
        
    </div>

    <div id="select-drawer-content" class="d-none scroll-y">
        <form id="select-invoices-form" class="px-10 mt-5" method="POST" name="" 
            {% if source == constant.TYPE_OBJECT_CASE %}
                action="{% host_url 'service_manage_deals' object_id host 'app' %}"
            {% elif source == constant.TYPE_OBJECT_ORDER %}
                action="{% host_url 'order_update_association_object' object_id host 'app' %}"
            {% elif source == 'commerce_subscription' %}
                action="{% host_url 'shopturbo_manage_subscriptions' object_id host 'app' %}"
            {% elif source == 'company' or source == 'contacts' %}
                action="{% host_url 'customer_update_association_object' object_id host 'app' %}"
            {% elif source == constant.TYPE_OBJECT_RECEIPT %}
                action="{% host_url 'update_receipt' id=object_id host 'app' %}"
            {% elif source == 'journal' %}
                action="{% host_url 'journal_update_association_object' host 'app' %}"
            {% elif source == constant.TYPE_OBJECT_ESTIMATE %}
                action="{% host_url 'update_estimate' id=object_id host 'app' %}"
            {% else %}
                action="{% host_url 'save_object_association' host 'app' %}"
            {% endif %}
        >
            {% csrf_token %}

            {% if view_id %}
                <input type="hidden" name="view_id" value="{{view_id}}">
            {% endif %}
            <input type="hidden" name="object_id" value="{{object_id}}">
            <input type="hidden" name="page" value="{{page}}">
            <input type="hidden" name="module" value="{{module}}">
            <input type="hidden" name="update_invoices" value="true">
            <input type="hidden" name="target_object_type" value="{{constant.TYPE_OBJECT_INVOICE}}">
            <input type="hidden" name="source" value="{{source}}">
    
            <div class="{% include "data/utility/form-div.html" %}">          
                <label class="mb-2 d-flex align-items-center fs-4 fw-bold">
                    <span class="" >
                    {% if LANGUAGE_CODE == 'ja'%}
                    売上請求
                    {% else %}
                    Invoices
                    {% endif %}
                    </span>
                </label>
                
                <select id="invoice-association-select" placeholder="Choose Invoices" class="border min-h-40px form-select select2-invoices-lazy-association" name="invoices"
                    {% if LANGUAGE_CODE == 'ja'%}
                    data-placeholder="売上請求"
                    {% else %}
                    data-placeholder="Invoice"
                    {% endif %}
                    data-allow-clear="true"
                    {% if source != 'journal' %}
                    multiple="multiple"
                    {% endif %}
                    >
                    {% if obj.invoices.all %}
                        {% for invoice in obj.invoices.all %}
                            <option value="{{invoice.id}}" selected>
                                {% if invoice.contact %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.contact|display_contact_name:LANGUAGE_CODE}}
                                {% elif invoice.company %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.company.name}}
                                {% else %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | (Unnamed)
                                {% endif %}
                            </option>
                        {% endfor %}
                    {% elif source == 'company' %}
                        {% for invoice in commerce_objs %}
                            <option value="{{invoice.id}}" selected>
                                {% if invoice.contact %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.contact|display_contact_name:LANGUAGE_CODE}}
                                {% elif invoice.company %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.company.name}}
                                {% else %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | (Unnamed)
                                {% endif %}
                            </option>
                        {% endfor %}
                    {% elif source|in_list:'commerce_subscription,estimates,commerce_orders' %}
                        {% for invoice in obj.invoice.all %}
                            <option value="{{invoice.id}}" selected>
                                {% if invoice.contact %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.contact|display_contact_name:LANGUAGE_CODE}}
                                {% elif invoice.company %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.company.name}}
                                {% else %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | (Unnamed)
                                {% endif %}
                            </option>
                        {% endfor %}
                    {% elif source == 'contacts' %}
                        {% for invoice in obj.invoice_set.all %}
                            <option value="{{invoice.id}}" selected>
                                {% if invoice.contact %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.contact|display_contact_name:LANGUAGE_CODE}}
                                {% elif invoice.company %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.company.name}}
                                {% else %}
                                    #{{invoice.id_inv|stringformat:'04d'}} | (Unnamed)
                                {% endif %}
                            </option>
                        {% endfor %}
                    {% elif obj.invoice %}
                        {% with obj.invoice as invoice %}
                        
                        <option value="{{invoice.id}}" selected>
                            {% if invoice.contact %}
                                #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.contact|display_contact_name:LANGUAGE_CODE}}
                            {% elif invoice.company %}
                                #{{invoice.id_inv|stringformat:'04d'}} | {{invoice.company.name}}
                            {% else %}
                                #{{invoice.id_inv|stringformat:'04d'}} | (Unnamed)
                            {% endif %}
                        </option>
                        {% endwith %}
                    {% endif %}
                </select>     
            
                <script>
                    $('.select2-invoices-lazy-association').select2({
                        ajax: {
                            delay: 250, // wait 250 milliseconds before triggering the request
                            dataType: 'json',
                            url: '{% host_url "get_invoice_object_options" host "app" %}',
                            data: function (params) {
                                    var query = {
                                        q: params.term,
                                        page: params.page || 1,
                                        json_response: true,
                                    }
                                    return query;
                                },
                            minimumInputLength: 2,
                        },
                        language: {
                            "noResults": function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}レコードが見つかりません{% else %}No record found{% endif %}";
                            },
                            searching: function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}検索中{% else %}Searching{% endif %}...";
                            },
                            "loadingMore": function(){
                                return "{% if LANGUAGE_CODE == 'ja'%}読み込み中{% else %}Loading more{% endif %}...";
                            },
                        },
                    })
                </script>
            </div>
    
            <button type="submit" class="btn btn-dark mt-5">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </form>
    </div>
</div>

{% block js %}
    <script>
        $(document).ready(function(){
            $('.single-entry-section-switcher').click(function(){
                $('#create-drawer-content').toggleClass('d-none');
                $('#select-drawer-content').toggleClass('d-none');
            });
            $('.upload-entry-section-switcher').click(function(){
                $('#create-drawer-content').toggleClass('d-none');
                $('#select-drawer-content').toggleClass('d-none');
            });
        });
    </script>
{% endblock %}