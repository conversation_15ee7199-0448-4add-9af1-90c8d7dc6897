{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<div id='text-unique-check' {% if object_type == 'commerce_orders' %} class="w-100"  {% endif %} hx-target="this" hx-swap="outerHTML">
    <input type="text" 
        {% if CustomFieldName.required_field %} required {% endif %}
        {% if form %} 
            form="{{form}}" 
        {% endif %}

        {% if object_type|in_list:'production,purchaseitem,purchaseorder,bill,expense,estimates,delivery_slips,invoices,receipts,slips,contacts,company,customer_case,commerce_orders,commerce_subscription,commerce_items,commerce_inventory,commerce_inventory_transaction,jobs,panels,dashboards,custom_object,commerce_inventory_warehouse,jobs_applicant,commerce_meter' %}
            {% if object_id %}
                name='text|value|{{object_id}}|{{property_id}}'
            {% else %}
                name='text|value||{{property_id}}'
            {% endif %}
        {% else %}
            {% if object_id %}
                name='text|value|{{object_id}}|{{property_id}}'
            {% else %}
                name='text|value|{{property_id}}'
            {% endif %}
        {% endif %}

        
        {% if is_unique%}
            {% if object_type|in_list:'estimates,delivery_slips,invoices,receipts,slips' %}
                {% if object_id %}
                hx-post="{% host_url 'property_text_form_unique_check' form object_type property_id object_id host 'app' %}"
                id="text-unique-check-{{object_id}}-{{property_id}}"
                {% else %}
                hx-post="{% host_url 'property_text_form_unique_check' form object_type property_id host 'app' %}"
                id="text-unique-check-{{property_id}}"
                {% endif %}
            {% else %}
                {% if object_id %}
                hx-post="{% host_url 'property_text_unique_check' object_type property_id object_id host 'app' %}"
                id="text-unique-check-{{object_id}}-{{property_id}}"
                {% else %}
                hx-post="{% host_url 'property_text_unique_check' object_type property_id host 'app' %}"
                id="text-unique-check-{{property_id}}"
                {% endif %}
            {% endif %}
            
            hx-trigger="keyup changed delay:500ms,eventUniqueText"
        {% endif %}

        {% if LANGUAGE_CODE == 'ja'%}
        placeholder="テキストを入力"
        {% else %}
        placeholder="text"
        {% endif %}

        {% if value %}
        value="{{ value }}" 
        {% endif %}
        
        class="form-control {% if is_unique %}unique-text-input{% endif %}" ></input>

        {% if is_unique %}
            {% if unique_exists %}
                <div id="unique-error" class="mt-3 tw-text-red-500 tw-text-left" style="color:#ef4444 !important;">
                    {% if LANGUAGE_CODE == 'ja'%}
                    この値は既に存在します。
                    {% else %}
                    This value already exists.
                    {% endif %}
                </div>
            {% endif %}
        {% endif %}
</div>

{% if is_unique %}
<script>
    {% if object_id %}
    document.getElementById("text-unique-check-{{object_id}}-{{property_id}}").addEventListener("keydown", function(event) {
        if (event.key === "Enter") {
            event.preventDefault(); // Prevent form submission
        }
    });
        {% if CustomFieldName.required_field %}
        document.getElementById("text-unique-check-{{object_id}}-{{property_id}}").addEventListener("htmx:beforeRequest", function(event) {
        if (this.hasAttribute('required') && !this.value.trim()) {
            event.preventDefault(); // Stop the HTMX request
            this.setCustomValidity('This field is required');
            this.reportValidity();
            return false;
        }
        this.setCustomValidity(''); // Clear validation message
        });
        {% endif %}
    {% else %}
    document.getElementById("text-unique-check-{{property_id}}").addEventListener("keydown", function(event) {
        if (event.key === "Enter") {
            event.preventDefault(); // Prevent form submission
        }
    });
        {% if CustomFieldName.required_field %}
        document.getElementById("text-unique-check-{{property_id}}").addEventListener("htmx:beforeRequest", function(event) {
        if (this.hasAttribute('required') && !this.value.trim()) {
            event.preventDefault(); // Stop the HTMX request
            this.setCustomValidity('This field is required');
            this.reportValidity();
            return false;
        }
        this.setCustomValidity(''); // Clear validation message
        });
        {% endif %}
    {% endif %}

    window.onload = function() {
        {% if object_id %}
        var input = document.getElementById('text-unique-check-{{object_id}}-{{property_id}}');
        {% else %}
        var input = document.getElementById('text-unique-check-{{property_id}}');
        {% endif %}
        // Focus the input element
        input.focus();
        // Move the cursor to the end of the text
        input.setSelectionRange(input.value.length, input.value.length);
    };
</script>
{% endif %}