import ast
import os
import uuid
from urllib.parse import unquote

from django.conf import settings
from django.http import HttpResponse
from django.shortcuts import render
from django.views.generic import View
from data.constants.associate_constant import *
from data.constants.commerce_constant import (APP_SETTING_CHILD_LIST,
                                             COMMERCE_APP_TARGET, DIRECTORY,
                                             NEXT_DIRECTORY)
from data.constants.properties_constant import *
from data.constants.properties_constant import TYPE_OBJECT_INVOICE
from data.models import (Module, PropertySet, View, ShopTurboItems, AppSetting, Invoice, InvoiceNameCustomField,
                         InvoiceValueCustomField, InvoiceItem, User, Log, ActionHistory, ActionNode, WorkflowActionTracker, Notification, AssociationLabel)
from utils.decorator import login_or_hubspot_required
from utils.freee_bg_jobs.freee import *
from utils.properties.properties import (get_default_property_set, get_page_object,
                                         get_properties_from_set)
from utils.serializer import *
from utils.smtp import *
from utils.utility import (get_workspace, is_valid_uuid,
                           natural_sort_key)
from utils.workspace import get_permission

type_http = settings.SITE_URL.split("//")[0]


@login_or_hubspot_required
def invoice_drawer(request, id=None):
    property_set = None
    set_id = None
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    module_slug = request.GET.get('module')
    print("Invoice Drawer")

    module = Module.objects.filter(
        workspace=workspace, object_values__contains=TYPE_OBJECT_INVOICE).order_by('order', 'created_at')

    if module_slug:
        module = module.filter(slug=module_slug)

    if module:
        module = module.first()
        module_slug = module.slug

    page_obj = get_page_object(TYPE_OBJECT_INVOICE, lang)

    id_field = page_obj['id_field']
    page_title = page_obj['page_title']
    default_columns = page_obj['default_columns']
    if id_field and id_field not in default_columns:
        default_columns.insert(0, id_field)
    base_model = page_obj['base_model']
    custom_model = page_obj['custom_model']
    item_model = page_obj['item_model']
    field_item_name = page_obj['field_item_name']
    invoice_id = None
    items_show = []
    filter = {}

    filter['workspace'] = get_workspace(request.user)

    properties = None
    drawer_type = request.GET.get('drawer_type')

    permission = get_permission(
        object_type=TYPE_OBJECT_INVOICE, user=request.user)
    if not permission:
        permission = DEFAULT_PERMISSION

    section = request.GET.get('section', None)
    if section == 'bulk-action':
        selected_id = request.GET.get('selected_ids', None)
        if selected_id and selected_id != '[]' and len(selected_id) > 0:
            selected_ids = ast.literal_eval(selected_id)
            invoices = base_model.objects.filter(id__in=selected_ids)
            selected_ids = [str(invoice.id) for invoice in invoices]
            selected_invoice_ids = [str(invoice.id) for invoice in invoices]
        else:
            selected_invoice_ids = []

        context = {
            'object_action': {
                'additional_params': {
                    'selected_ids': selected_invoice_ids,
                    'object_type': TYPE_OBJECT_INVOICE
                },
            },
            'object_type': TYPE_OBJECT_INVOICE,
        }
        return render(request, 'data/shopturbo/shopturbo-manage-action.html', context)

    if section == 'action_history':
        section = request.GET.get('section', None)
        action_tab = request.GET.get('action_tab', None)
        open_drawer = request.GET.get('open_drawer')

        context = {"selected_ids": request.GET.getlist("selected_ids", []),
                   "action_tab": action_tab, 'object_type': TYPE_OBJECT_INVOICE, 'drawer_url': page_obj['drawer_url'], "open_drawer": open_drawer}
        return render(request, 'data/shopturbo/manage-sync-actions-settings-commerce.html', context)

    property_set_form = None
    set_id = request.GET.get('set_id')

    if set_id and set_id != 'None':
        property_set_form = PropertySet.objects.filter(id=set_id).first()
        _, properties = get_properties_from_set(
            set_id, TYPE_OBJECT_INVOICE, workspace)

    else:
        view_id = request.GET.get('view_id', None)
        if not view_id or view_id == 'None':
            view = View.objects.filter(
                workspace=workspace,
                title='main',
                target=TYPE_OBJECT_INVOICE
            ).first()
        else:
            view = View.objects.filter(id=view_id).first()

        if view:
            form_set = PropertySet.objects.filter(
                target=TYPE_OBJECT_INVOICE, as_default=True, workspace=workspace).first()

            if not form_set:
                # default property set
                form_set = get_default_property_set(
                    TYPE_OBJECT_INVOICE, workspace, lang)

            if view.target:
                if view.target == TYPE_OBJECT_INVOICE:
                    view.form = form_set
                    view.save()
                else:
                    condition = Q(workspace=workspace,
                                  target=TYPE_OBJECT_INVOICE,
                                  title__in=['main', None])
                    view = View.objects.filter(condition).first()
            else:
                view.form = form_set
                view.save()

            if view:
                if view.form:
                    property_set_form = view.form
                    set_id = view.form.id

            set_id, properties = get_properties_from_set(
                set_id, TYPE_OBJECT_INVOICE, workspace)

    line_item_properties = []
    if property_set_form:
        if property_set_form.name == None and not property_set_form.children:
            property_set_form.children = DEFAULT_FORM_FIELD_COMMERCE
        properties['list_all'] = property_set_form.children
        line_item_properties = property_set_form.invoice_line_items.all()

    # Fields to remove
    fields_to_remove = ['line_item_name', 'line_item_status', 'line_item_price', 'line_item_tax',
                        'line_item_name', 'line_item_quantity', 'line_item_name_quantity', 'line_item_price_without_tax']
    fields_to_remove.extend(['customers__contact__first_name', 'customers__company__name',
                            'customers__contact__name', 'customers__contact__last_name'])
    fields_to_remove.extend(['created_at', 'updated_at', 'tax_rate', 'custom_price', 'discount',
                             'discount_option', 'cost_option', 'total_price', 'total_price_without_tax'])
    if properties:
        properties['list_all'] = [
            field for field in properties['list_all'] if field not in fields_to_remove]

    currency = request.GET.get("currency", None)
    if not currency:
        items = ShopTurboItems.objects.filter(
            workspace=get_workspace(request.user),
            status='active',
        )
    else:
        items = ShopTurboItems.objects.filter(
            workspace=get_workspace(request.user),
            status='active',
        ).filter(
            Q(shopturbo_item_price__currency=currency.upper()) |
            Q(shopturbo_item_price__isnull=True)
        )

    try:
        app_setting = AppSetting.objects.get(
            workspace=workspace, app_target=COMMERCE_APP_TARGET)
    except AppSetting.DoesNotExist:
        app_setting = None

    app_logs = None

    exclude_item_list = []
    item_prices_list = []
    exclude_item = []

    if 'available-items' in request.GET:  # item row
        if 'add_type' in request.GET:
            exclude_item_list = request.GET.getlist('selected-item-id', [])

            i = 0
            for item in exclude_item_list:
                if item:
                    exclude_item.append(item)
                else:
                    i += 1
            if i == len(exclude_item_list):  # If everything is empty string, make it empty list
                exclude_item_list = ['']

        context = {'index': (str(uuid.uuid4()))}

        context['currency'] = request.GET.get('currency')

        # line item property
        context['obj_item_id'] = request.GET.get('obj_item_id')
        context['object_type'] = TYPE_OBJECT_INVOICE

        if exclude_item:
            context['exclude_item'] = exclude_item
        if 'item' in request.GET or 'purchase-item' in request.GET:
            if 'item' in request.GET:
                if '|' in request.GET.get('item'):
                    item_id = request.GET.get('item').split('|')[0]
                else:
                    item_id = request.GET.get('item')
            else:
                if '|' in request.GET.get('purchase-item'):
                    item_id = request.GET.get('purchase-item').split('|')[0]
                else:
                    item_id = request.GET.get('purchase-item')

            context['item'] = item_id

            if item_id:
                item_object = None
                if is_valid_uuid(item_id):
                    item_object = ShopTurboItems.objects.filter(
                        id=item_id).first()

                if item_object:
                    if 'contact_and_company' in request.GET:
                        customer_item_price = None
                        customer = request.GET.get('contact_and_company', None)
                        if customer:
                            try:
                                customer = Contact.objects.get(id=customer)
                            except:
                                customer = Company.objects.get(id=customer)
                            if customer._meta.object_name == 'Company':
                                customer_item_price = ShopTurboCustomerItemsPrice.objects.filter(
                                    item=item_object, company=customer).first()
                            else:
                                customer_item_price = ShopTurboCustomerItemsPrice.objects.filter(
                                    item=item_object, contact=customer).first()
                            if customer_item_price:
                                item_prices_list.append(
                                    customer_item_price.price)

                    if request.GET.get('items_cost_section'):
                        item_prices = ItemPurchasePrice.objects.filter(item=item_object, currency=context['currency']).order_by(
                            # price filter by currency (new): item filter by currency (old)
                            '-default')
                    else:
                        item_prices = ShopTurboItemsPrice.objects.filter(item=item_object, currency=context['currency']).order_by(
                            # price filter by currency (new): item filter by currency (old)
                            '-default')

                    # Check if item_prices queryset is empty -> add price zero if empety
                    if not item_prices.exists():
                        # Create a temporary fake object
                        if request.GET.get('items_cost_section'):
                            # Create fake ItemPurchasePrice object
                            fake_price = ItemPurchasePrice(
                                item=item_object,
                                price=0,
                                default=True,
                                currency=context['currency']
                            )
                            # If you need it as a queryset
                            item_prices = [fake_price]
                        else:
                            # Create fake ShopTurboItemsPrice object
                            fake_price = ShopTurboItemsPrice(
                                item=item_object,
                                price=0,
                                default=True,
                                currency=context['currency'],
                                tax=0
                            )
                            # If you need it as a queryset
                            item_prices = [fake_price]

                    for price in item_prices:
                        item_prices_list.append(price.price)

        context['item_price_list'] = item_prices_list

        if line_item_properties:
            context["line_item_properties"] = line_item_properties

        if 'price' in request.GET:
            context['price'] = request.GET.get('price')
        if 'amount' in request.GET:
            context['amount'] = request.GET.get('amount')
        if 'tax' in request.GET:
            context['tax'] = request.GET.get('tax')
        if 'add_type' in request.GET:
            context['add_type'] = request.GET.get('add_type')

        if request.GET.get('extras_section'):
            context['extras_section'] = ast.literal_eval(
                request.GET.get('extras_section'))
            return render(request, 'data/invoice/invoice-add-sections.html', context)

        return render(request, 'data/invoice/invoice-add-items.html', context)

    if id:  # manage drawer
        try:
            invoice = base_model.objects.get(id=id)
        except:
            if lang == 'ja':
                response_msg = '無効なリクエスト、URLを確認してください'
            else:
                response_msg = 'Invalid request, Please check your url'
            return HttpResponse(response_msg)
        invoice_id = getattr(invoice, id_field, None)
        invoice_id = f"{invoice_id:04d}"
        if invoice.currency:
            items = ShopTurboItems.objects.filter(workspace=get_workspace(
                request.user), status='active', currency=invoice.currency.upper())

        # mapping
        log_object = 'invoice'
        setting_type = 'invoice'

        app_logs = AppLog.objects.filter(
            **{'workspace': workspace, log_object: invoice}).order_by('-created_at')

    else:  # create drawer
        invoice = None
        try:
            contact_preselected = Contact.objects.get(
                id=request.GET.get('contact_id', None))
        except:
            contact_preselected = None

        try:
            company_preselected = Company.objects.get(
                id=request.GET.get('company_id', None))
        except:
            company_preselected = None

        for item in items:
            print(item.item_id, " ", item.name)

        # Association label setup for create drawer  
        association_labels = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_INVOICE).order_by('created_at')
        association_labels_sanka_true = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_INVOICE, created_by_sanka=True)
        association_label_list_sanka_true = [association_label.label for association_label in association_labels_sanka_true]
        association_labels_sanka_false = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_INVOICE, created_by_sanka=False)
        association_label_list_sanka_false = [str(association_label.id) for association_label in association_labels_sanka_false]
        association_label_list = association_label_list_sanka_true + association_label_list_sanka_false

        context = {
            'module': module_slug,
            'app_logs': app_logs,
            'invoice': invoice,
            'pdf_download_link': page_obj['pdf_download_link'],
            'post_obj_link': page_obj['new_obj_link'],
            'items': items,
            'items_show': items_show,
            'drawer_type': drawer_type,
            'page_title': page_title,

            'object_type': TYPE_OBJECT_INVOICE,

            'app_setting': app_setting,
            'contact_preselected': contact_preselected,
            'company_preselected': company_preselected,
            'NameCustomField': custom_model.objects.filter(workspace=workspace).order_by("order"),
            'properties': properties,
            'permission': permission,
            'hide_associated_data': True,
            'set_id': set_id,
            'ShippingCosts': ShopTurboShippingCost.objects.filter(workspace=workspace).order_by('created_at'),
            'association_labels':association_labels,
            'association_label_list': association_label_list,
        }

        if set_id:
            context["set_id"] = str(set_id)

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_INVOICE).order_by('created_at')
        print("== property_sets atas : ", property_sets)
        context['property_sets'] = property_sets

        NameCustomFieldMap = {}
        NameCustomFieldMaps = custom_model.objects.filter(workspace=workspace)
        for ctf in NameCustomFieldMaps:
            NameCustomFieldMap[str(ctf.id)] = ctf
        context['NameCustomFieldMap'] = NameCustomFieldMap

        if line_item_properties:
            context["line_item_properties"] = line_item_properties

        # gak di gawe
        source_url = request.GET.get('source_url', None)
        if source_url:
            source_url = unquote(source_url)
            context['source_url'] = source_url

        #
        for setting_ in APP_SETTING_CHILD_LIST:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name='invoice' + setting_, property_set=property_set).first()
            if not app_setting_child:
                app_setting_child = AppSettingChild.objects.create(
                    app_setting=app_setting, target_name='invoice' + setting_, property_set=property_set)
            setting_ = 'value_app_setting' + setting_
            context[setting_] = app_setting_child.value

        if request.GET.get('type', '') == 'create-association':
            context['type'] = 'create-association'
            context['source'] = request.GET.get('source', "")
            context['source_object_id'] = request.GET.get('object_id', '')

        if 'currency' in request.GET:
            if 'id' in request.GET:
                try:
                    invoice = base_model.objects.get(id=request.GET.get('id'))
                    context['invoice'] = invoice
                except:
                    pass
            if 'form' in request.GET:
                form = request.GET.get('form')
                context['form'] = form
            return render(request, 'data/invoice/invoice-partial-add-items.html', context)
        else:
            view_id = request.GET.get('view_id', None)
            if not view_id:
                view = View.objects.filter(
                    workspace=workspace,
                    title='main',
                    target=TYPE_OBJECT_INVOICE
                ).first()
            else:
                view = View.objects.filter(id=view_id).first()

            # Add fallback if view doesn't exist
            if not view:
                # Create default property set if needed
                default_property_set = PropertySet.objects.filter(
                    workspace=workspace,
                    target=TYPE_OBJECT_INVOICE,
                    as_default=True
                ).first()

                if not default_property_set:
                    default_property_set = get_default_property_set(
                        TYPE_OBJECT_INVOICE, workspace, lang)

                # Create default main view
                view = View.objects.create(
                    workspace=workspace,
                    title='main',
                    target=TYPE_OBJECT_INVOICE,
                    form=default_property_set
                )

            context['form'] = view.form
            context['module'] = module_slug

            print("== base_model: ", base_model)
            context['invoice'] = {'model': base_model,
                                  'workspace': workspace}

            if request.GET.get('import_export_type'):
                context['upload_section'] = request.GET.get(
                    'import_export_type')
                return render(request, 'data/invoice/invoice-edit-form-import-export.html', context)

            context['manage_obj_link'] = page_obj['manage_obj_link']
            if 'entry_type' in request.GET:
                return render(request, 'data/invoice/invoice-partial-add-items.html', context)

            # Construct the template name
            template_name_base = 'invoice'
            template_path = 'data/invoice/invoice-edit-form.html'

            return render(request, template_path, context)

    if request.method == 'GET':
        if lang == 'en':
            try:
                if invoice.start_date:
                    invoice.start_date = invoice.start_date.strftime(
                        '%Y-%m-%d')
            except:
                pass

            try:
                if invoice.due_date:
                    invoice.due_date = invoice.due_date.strftime('%Y-%m-%d')
            except:
                pass

        try:
            invoice_item = item_model.objects.filter(
                **{field_item_name.replace('_', ''): invoice}).order_by('created_at')
            invoice.items = []
            invoice.amounts = []
            invoice.amount_items = []
            invoice.tax_lists = []
            invoice.invoice_item_ids = []

            for item_ in invoice_item:
                if item_.item_link:
                    invoice.items.append(str(item_.item_link.id))
                else:
                    if item_.item_name:
                        invoice.items.append(item_.item_name)
                    else:
                        invoice.items.append("")  # For none name item

                invoice.amounts.append(item_.amount_price)
                invoice.amount_items.append(item_.amount_item)
                invoice.tax_lists.append(item_.tax_rate)
                invoice.invoice_item_ids.append(str(item_.id))

            item_ids = [''] * len(invoice.items)
            extras_section = [''] * len(invoice.items)
            items_show = list(zip(item_ids, invoice.items, invoice.amount_items,
                              invoice.amounts, invoice.tax_lists, extras_section, invoice.invoice_item_ids))

            section_objects = SectionItemInvoice.objects.filter(
                workspace=workspace, invoice=invoice)

            if section_objects:
                for section in section_objects:
                    extras_section = {"section_position": section.position,
                                      "section_type": section.section_type, "section_value": section.value}
                    row_value = ('', '', '', '', '', extras_section, '')
                    items_show.insert(int(section.position), row_value)

        except Exception as e:
            print('[Error at Invoice Drawer]: ', e)

        # customize_template = CustomizePdfTemplate.objects.filter(
        #     setting_type=setting_type, workspace=workspace)
        customize_template = []

        list_template = []
        for filename in os.listdir(DIRECTORY):
            # Check if the file is an HTML file and contains 'pdf_pattern' in its name
            if filename.endswith('.html') and page_obj['pdf_pattern'] in filename:
                list_template.append(NEXT_DIRECTORY + filename)

        list_template = sorted(list_template, key=natural_sort_key)

        tmp_setting_type = setting_type
        if tmp_setting_type in ['invoice', 'estimate', 'receipt', 'delivery_slip', 'slip']:
            tmp_setting_type = f"{tmp_setting_type}s"

        if tmp_setting_type in [TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_INVOICE, TYPE_OBJECT_SLIP]:
            list_template = []
            customize_template = PdfTemplate.objects.filter(
                master_pdf__object_type=tmp_setting_type, workspace=workspace).order_by('master_pdf__name_en', 'master_pdf__name_ja')

        if invoice and invoice.owner and invoice.owner.user:
            permission += f'|{invoice.owner.user.id}#{request.user.id}'

        # Association label setup for manage drawer  
        association_labels = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_INVOICE).order_by('created_at')
        association_labels_sanka_true = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_INVOICE, created_by_sanka=True)
        association_label_list_sanka_true = [association_label.label for association_label in association_labels_sanka_true]
        association_labels_sanka_false = AssociationLabel.objects.filter(workspace=workspace, object_source=TYPE_OBJECT_INVOICE, created_by_sanka=False)
        association_label_list_sanka_false = [str(association_label.id) for association_label in association_labels_sanka_false]
        association_label_list = association_label_list_sanka_true + association_label_list_sanka_false

        context = {
            'app_logs': app_logs,
            'module': module_slug,
            'object_type': TYPE_OBJECT_INVOICE,
            'invoice_id': invoice_id,
            'app_setting': app_setting,
            'pdf_download_link': page_obj['pdf_download_link'],
            'post_obj_link': page_obj['update_obj_link'],
            'items': items,
            'drawer_type': drawer_type,
            'items_show': items_show,
            'type': drawer_type,
            'invoice': invoice,
            'page_title': page_title,
            'type_http': type_http,
            'view_id': request.GET.get("view_id"),
            'NameCustomField': custom_model.objects.filter(workspace=workspace).order_by("order"),
            field_item_name: invoice,
            'properties': properties,
            'permission': permission,
            'list_customize_template': customize_template,
            'list_template': list_template,
            'customer_selector_id': uuid.uuid4(),
            'side_drawer': request.GET.get('side_drawer'),
            'source': TYPE_OBJECT_INVOICE,
            'ShippingCosts': ShopTurboShippingCost.objects.filter(workspace=workspace).order_by('created_at'),
            'association_label_list': association_label_list,
            'association_labels':association_labels,
        }

        if set_id:
            context["set_id"] = str(set_id)

        print("=========================== set_id: ", set_id)

        if line_item_properties:
            context["line_item_properties"] = line_item_properties

        NameCustomFieldMap = {}
        NameCustomFieldMaps = custom_model.objects.filter(workspace=workspace)
        for ctf in NameCustomFieldMaps:
            NameCustomFieldMap[str(ctf.id)] = ctf
        context['NameCustomFieldMap'] = NameCustomFieldMap

        hide_associated_data = request.GET.get('hide_associated_data', None)
        if hide_associated_data:
            context['hide_associated_data'] = True

        source_url = request.GET.get('source_url', None)
        if source_url:
            source_url = unquote(source_url)
            context['source_url'] = source_url

        for setting_ in APP_SETTING_CHILD_LIST:
            app_setting_child = AppSettingChild.objects.filter(
                app_setting=app_setting, target_name='invoice' + setting_, property_set=property_set).first()
            if not app_setting_child:
                app_setting_child = AppSettingChild.objects.create(
                    app_setting=app_setting, target_name='invoice' + setting_, property_set=property_set)
            setting_ = 'value_app_setting' + setting_
            context[setting_] = app_setting_child.value

        if 'currency' in request.GET:
            if 'id' in request.GET:
                invoice = base_model.objects.get(id=request.GET.get('id'))
                context['invoice'] = invoice
            return render(request, 'data/invoice/invoice-partial-add-items.html', context)
        else:
            context['object_type'] = TYPE_OBJECT_INVOICE
            context['estimate'] = Estimate.objects.filter(
                workspace=workspace, invoice=invoice).first()
            context['deals'] = Deals.objects.filter(
                workspace=workspace, invoices__id=invoice.id)
            context['order'] = ShopTurboOrders.objects.filter(
                workspace=workspace, invoice=invoice).first()
            context['object_action'] = {
                'additional_params': {
                    'selected_ids': str(invoice.id),
                    'object_type': TYPE_OBJECT_INVOICE
                }
            }
            context['module'] = module_slug
            context['side_drawer'] = request.GET.get('side_drawer')

            property_sets = PropertySet.objects.filter(
                workspace=workspace, target=TYPE_OBJECT_INVOICE).order_by('created_at')
            print("== property_sets: ", property_sets)
            context['property_sets'] = property_sets
            context['manage_obj_link'] = page_obj['manage_obj_link']

            if 'entry_type' in request.GET:
                return render(request, 'data/invoice/invoice-partial-add-items.html', context)

            # Construct the template name
            template_name_base = 'invoice'
            template_path = 'data/invoice/invoice-edit-form.html'
            return render(request, template_path, context)
