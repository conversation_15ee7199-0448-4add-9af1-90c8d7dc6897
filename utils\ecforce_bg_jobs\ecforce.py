import json
import traceback
from datetime import datetime, timedelta
import pytz

import requests

from data.models import *
from utils.contact import display_contact_name
from utils.hubspot import push_hubspot_items, push_hubspot_orders
from utils.meter import *
from utils.logger import logger


def get_authentication_token(url, email, password):
    source_url = f'{url}/api/v2/admins/sign_in'
    data = {
        "admin[email]": email,
        "admin[password]": password
    }
    x = requests.post(source_url, data=data)
    content = json.loads(x.content.decode('utf-8'))
    return content['authentication_token']


def get_ecforce_items(url, authentication_token):
    source_url = f'{url}/api/v2/admin/products?page=1&per=100'
    data = []
    header = {
        'Authorization': f'Token token="{authentication_token}"'
    }
    result = requests.get(source_url, headers=header)
    parsed_result = json.loads(result.content.decode('utf-8'))
    data = data + parsed_result['data']
    meta = parsed_result['meta']
    links = parsed_result['links']

    no_page = int(meta['total_pages'])
    total = int(meta['total_count'])
    per = 100
    i = 2

    while i <= no_page:
        tmp = f'{url}/api/v2/admin/products?page={i}&per=100'
        page_result = requests.get(tmp, headers=header)
        payload = json.loads(page_result.content.decode('utf-8'))
        data = data + payload['data']
        i += 1
    return data


def get_ecforce_orders(url, authentication_token, import_order_date=None):
    try:
        # config:
        per = 1

        now = datetime.utcnow().date()
        if import_order_date:
            try:
                start_date = datetime.strptime(import_order_date, '%Y-%m-%d')
            except:
                start_date = now - timedelta(days=720)
        else:
            start_date = now - timedelta(days=720)

        start_date = start_date.isoformat()

        source_url = f'{url}/api/v2/admin/orders?page=1&per={per}&q[created_at_gt]={start_date}&include=customer,order_items,order_items.variant,customer.billing_address,customer.shipping_addresses'

        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }

        logger.info('source_url: '+source_url)
        logger.info('headers: '+str(header))
        logger.info('Sending Request Now')

        result = requests.get(source_url, headers=header)
        logger.info('Finish getting result')
        parsed_result = json.loads(result.content.decode('utf-8'))
        meta = parsed_result['meta']

        total_orders = int(meta['total_count'])

        total_pages = round(total_orders / 100) if total_orders > per else 1

        return total_orders, total_pages, start_date

    except Exception as e:
        logger.error(f"Error in get_ecforce_orders: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return empty lists to prevent unpacking errors
        return 0,0, None


def get_ecforce_orders_buffer_page(url, authentication_token, page=1, start_date=None):
    try:
        per = 10
        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }
        data = []
        included = []

        logger.info(f'Fetching page: {page}')
        source_url = f'{url}/api/v2/admin/orders?page={page}&per={per}&q[created_at_gt]={start_date}&include=customer,order_items,order_items.variant,customer.billing_address,customer.shipping_addresses'
        logger.info(f'source_url: {source_url}')
        page_result = requests.get(source_url, headers=header)
        if page_result.status_code == 200:
            payload = json.loads(page_result.content.decode('utf-8'))
            data = data + payload['data']
            included = included + payload['included']
        else:
            logger.info(f'Failed to fetch page {page_result.content}')

        logger.info(f'Finish getting page {page} - {page_result.status_code}')

        customers = [i for i in included if i['type'] == 'customer']
        order_items = [i for i in included if i['type'] == 'order_item']
        variants = [i for i in included if i['type'] == 'variant']
        addresses = [i for i in included if i['type'] == 'address']

        return data, customers, order_items, variants, addresses

    except Exception as e:
        logger.error(f"Error in get_ecforce_orders_buffer_page: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Return empty lists to prevent unpacking errors
        return [], [], [], [], []


def get_ecforce_customer(customer_id, url, authentication_token):
    try:
        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }

        source_url = f'{url}/api/v2/admin/customers/{customer_id}.json?include=billing_address,notes'
        response = requests.get(source_url, headers=header)
        if response.status_code == 200:
            customer_data = response.json().get('included', [])  
            for data in customer_data:
                if data.get('type') == 'address':
                    return data.get('attributes', {})  
                
            return {}
        else:
            logger.error(
                f"Failed to fetch customer {customer_id}: {response.status_code}")
            return {}
    except Exception as e:
        logger.error(f"Error fetching customer {customer_id}: {str(e)}")
        return {}


def get_ecforce_order_by_id(order_id, url, authentication_token):
    try:
        header = {
            'Authorization': f'Token token="{authentication_token}"'
        }

        source_url = f'{url}/api/v2/admin/orders/{order_id}.json'
        response = requests.get(source_url, headers=header)
        if response.status_code == 200:
            return response.json().get('data', {}).get('attributes', {})  
        else:
            logger.error(
                f"Failed to fetch order {order_id}: {response.status_code}")
            return {}
    except Exception as e:
        logger.error(f"Error fetching order {order_id}: {str(e)}")
        return {}


def pull_ecforce_items(channel_id, hubspot_channel_id=None):
    channel = Channel.objects.get(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace

    # HubSpot
    sku_map_items = {}

    logger.info("====== pull_ecforce_items")
    # TODO: Update logic later
    items = get_ecforce_items(url, token)
    for item in items:
        try:
            payload = item['attributes']
            item_id = payload['id']
            item_name = payload['name']
            item_description = payload['description']
            item_status = payload['state']
            if item_status == 'inactive':
                item_status = 'draft'
            else:
                item_status = 'active'
            item_price = payload['master_list_price']
            currency = 'JPY'

            item_platform, _ = ShopTurboItemsPlatforms.objects.get_or_create(
                channel=channel,
                platform_id=item_id,
                platform_type='default'
            )

            if item_platform.item:
                shopturbo_item = item_platform.item
            else:
                shopturbo_item, _ = ShopTurboItems.objects.get_or_create(
                    workspace=workspace,
                    product_id=item_id,
                    platform=platform,
                )
                item_platform.item = shopturbo_item
                item_platform.save()

            price, _ = ShopTurboItemsPrice.objects.get_or_create(
                item=shopturbo_item,
                price=float(item_price),
                currency=currency)

            check_default = ShopTurboItemsPrice.objects.filter(
                item=shopturbo_item,
                default=True
            )
            if len(check_default) == 0:
                price.default = True
            shopturbo_item.price = float(item_price)
            if currency:
                shopturbo_item.currency = currency
            if item_name:
                shopturbo_item.name = item_name
                price.name = item_name
            if item_description:
                shopturbo_item.description = item_description
            if item_status:
                shopturbo_item.status = item_status

            price.save()
            shopturbo_item.save()

            sku_map_items[f'{item_id}'] = {
                'platform': channel.integration.title_ja if channel.integration.title_ja else channel.integration.title,
                'name': shopturbo_item.name,
                'price': shopturbo_item.price,
            }
        except:
            continue

    # HubSpot
    if hubspot_channel_id:
        skus = sku_map_items.keys()
        if len(skus) == 0:
            return True

        channel = Channel.objects.get(id=hubspot_channel_id)
        logger.info(
            f'>>>>>>>>>>>>>>>> Start PUSH items to HubSpot channel {channel.name}')
        push_hubspot_items(str(channel.id), sku_map_items)
        logger.info(
            f'<<<<<<<<<<<<<<<< Finish PUSH items to HubSpot channel {channel.name}')

    return True


def push_ecforce_items(items, channel_id):
    channel = Channel.objects.get(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    logger.info("====== push_ecforce_items")
    # TODO: Update logic later

    return True


def import_ecforce_orders(channel_id, mapping_custom_fields=None, hubspot_channel_id=None, import_order_date=None):
    failed_number = 0
    success_number = 0
    channel = Channel.objects.get(id=channel_id)
    url = channel.app_id
    token = channel.access_token
    platform = channel.integration.slug
    workspace = channel.workspace
    task = TransferHistory.objects.filter(
        workspace=workspace, type='import_order').order_by('-created_at').first()
    logger.info("====== import_ecforce_orders")

    # HubSpot
    order_id_map_orders = {}

    # TODO: Update logic later
    total_orders, total_pages, start_date = get_ecforce_orders(
        url, token, import_order_date)
    
    # ========= Override data Only For testing
    # total_orders = 10
    # total_pages = 1
    
    
    task.total_number = total_orders
    task.save()
    for page in range(1, total_pages + 1):
        logger.info(f'Processing page {page} of {total_pages}')
        orders, customers, order_items, variants, addresses = get_ecforce_orders_buffer_page(
            url, token, page, start_date)

        if not orders:
            logger.info("No orders found to import")
            continue

        logger.info(f"Found {len(orders)} orders to import")
        # for customer in customers:
        for order in orders:
            task = TransferHistory.objects.filter(
                workspace=workspace, type='import_order').order_by('-created_at').first()
            if task:
                if task.status == 'canceled':
                    return False
            progress = 100 * (success_number) / total_orders
            task.progress = progress
            task.save()
            try:
                payload = order['attributes']
                order_id = payload['id']
                order_display_name = payload['number']
                currency = 'JPY'
                item_price = payload['subtotal']
                total_price = payload['total']
                tax = payload['tax']
                platform_order_id = order_id

                logger.info(f'Processing order: {order_id}')
                order_platform, _ = ShopTurboOrdersPlatforms.objects.get_or_create(
                    channel=channel,
                    platform_order_id=platform_order_id
                )

                if order_platform.order:
                    shopturbo_order = order_platform.order
                else:
                    if not has_quota(workspace, ORDER_USAGE_CATEGORY):
                        for user in workspace.user.all():
                            if user.verification.language == 'ja':
                                Notification.objects.create(
                                    workspace=workspace, user=user, message='制限を超えたため、受注レコードを作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。', type="error")
                            else:
                                Notification.objects.create(
                                    workspace=workspace, user=user, message='Order could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.', type="error")
                        return False

                    timezone = pytz.timezone('UTC')
                    dt_naive = datetime.strptime(
                        payload['created_at'], '%Y/%m/%d %H:%M:%S')
                    dt_aware = timezone.localize(dt_naive)
                    shopturbo_order = ShopTurboOrders.objects.create(
                        workspace=workspace,
                        currency=currency,
                        platform=platform,
                        status='active',
                        order_type='item_order',
                        order_at=dt_aware
                    )

                order_platform.order = shopturbo_order
                order_platform.order_display_name = order_display_name
                # order_platform.order_at = payload['created_at']
                order_platform.save()
                if item_price:
                    shopturbo_order.item_price_order = float(item_price)

                if total_price:
                    shopturbo_order.total_price = float(total_price)
                    shopturbo_order.total_price_without_tax = float(
                        total_price)

                if tax and total_price and float(total_price) > 0:
                    shopturbo_order.tax = round(
                        100 * float(tax) / float(total_price), 2)
                    shopturbo_order.total_price_without_tax = float(
                        total_price - tax)

                if currency:
                    shopturbo_order.currency = currency

                shopturbo_order.save()
                shopturbo_order.shopturboitemsorders_set.all().delete()
                item_relationships = order['relationships']['order_items']['data']

                if item_relationships and len(item_relationships) > 0:
                    for relationship in item_relationships:
                        order_item_id = relationship['id']
                        products = [
                            item for item in order_items if order_item_id == item['id']]
                        if len(products) > 0:
                            logger.info(f'Found products: {products[0]["id"]}')
                            variant_id = products[0]['attributes']['variant_id']
                            variant_ = False
                            if variants:
                                product_variants = [
                                    item for item in variants if variant_id == item['attributes']['id']]
                                variant_ = True
                            else:
                                product_variants = [
                                    item for item in products]
                            if len(product_variants) > 0:
                                product = product_variants[0]
                                logger.info(
                                    f'Processing product variant: {product.get("id")}')
                                if variant_:
                                    item_id = product['attributes']['product_id']
                                    item_name = product['attributes']['name']
                                    item_price = product['attributes']['sales_price']
                                else:
                                    item_id = product['attributes']['id']
                                    item_name = product['attributes']['variant_id']
                                    item_price = product['attributes']['price']

                                number_of_item = products[0]['attributes']['quantity']

                                item_platform, _ = ShopTurboItemsPlatforms.objects.get_or_create(
                                    channel=channel,
                                    platform_id=item_id,
                                    platform_type='default'
                                )
                                try:
                                    sku = product['attributes']['sku']
                                    if sku:
                                        item_id = sku
                                        item_platform.platform_id = sku
                                        item_platform.save()
                                except:
                                    pass
                                shopturbo_item_order, _ = ShopTurboItemsOrders.objects.get_or_create(
                                    platform_item_id=item_id,
                                    order=shopturbo_order,
                                    order_platform=order_platform
                                )
                                if item_platform.item:
                                    shopturbo_item = item_platform.item
                                else:
                                    if shopturbo_item_order.item:
                                        shopturbo_item = shopturbo_item_order.item
                                    else:
                                        shopturbo_item = ShopTurboItems.objects.create(
                                            workspace=workspace,
                                            platform=platform,)
                                item_platform.item = shopturbo_item
                                item_platform.save()

                                if number_of_item:
                                    shopturbo_item_order.number_item = float(
                                        number_of_item)
                                if item_price:
                                    shopturbo_item_order.item_price_order = float(
                                        item_price)
                                if currency:
                                    shopturbo_item_order.currency = currency
                                shopturbo_item_order.save()

                                if item_name:
                                    shopturbo_item.name = item_name
                                if item_price:
                                    shopturbo_item.price = float(
                                        item_price)
                                if currency:
                                    shopturbo_item.currency = currency

                                shopturbo_item.status = 'active'
                                shopturbo_item.save()

                                price, _ = ShopTurboItemsPrice.objects.get_or_create(
                                    item=shopturbo_item,
                                    price=float(item_price),
                                    currency=currency
                                )
                                check_default = ShopTurboItemsPrice.objects.filter(
                                    item=shopturbo_item,
                                    default=True
                                )
                                if len(check_default) == 0:
                                    price.default = True
                                price.name = item_name
                                price.save()

                                shopturbo_item_order.item = shopturbo_item
                                shopturbo_item_order.item_price = price
                                shopturbo_item_order.save()

                                for field in mapping_custom_fields:
                                    if field in ['product_number|item', 'variant_sku|item']:
                                        sanka_field = mapping_custom_fields[field].replace(
                                            '|item', '')
                                        value = None
                                        if field == 'product_number|item':
                                            value = products[0]['attributes'][
                                                'product_number'] if 'product_number' in products[0]['attributes'] else None
                                        if field == 'variant_sku|item':
                                            value = product['attributes']['sku'] if 'sku' in product['attributes'] else None

                                        custom_field = ShopTurboItemsNameCustomField.objects.filter(
                                            workspace=workspace, name=sanka_field).first()
                                        custom_value, _ = ShopTurboItemsValueCustomField.objects.get_or_create(
                                            field_name=custom_field, items=shopturbo_item)
                                        custom_value.value = value
                                        custom_value.save()

                    shopturbo_order.number_item = len(order_items)
                    shopturbo_order.save()

                order_customer = order['relationships']['customer']['data']
                address_payload = None
                if order_customer:
                    customer_payloads = [
                        customer for customer in customers if customer['id'] == order_customer['id']]
                    if not customer_payloads:
                        logger.warning(
                            f"Customer with ID {order_customer['id']} not found in customers data")
                        continue
                    customer_payload = customer_payloads[0]
                    logger.info(f'Processing customer: {customer_payload["id"]}')

                    contact_platform, _ = ContactsPlatforms.objects.get_or_create(
                        channel=channel,
                        platform_id=customer_payload['id']
                    )
                    if contact_platform.contact:
                        contact = contact_platform.contact

                    # Getting Customer attribute
                    customer_attr = get_ecforce_customer(
                        order_customer['id'], url, token)
                    if not contact_platform.contact:
                        contact, _ = Contact.objects.get_or_create(
                            workspace=workspace,
                            email=customer_payload.get('attributes','').get('email',''),
                            name=customer_attr.get('name01',''),
                            phone_number=customer_attr.get('full_tel',''),
                            last_name=customer_attr.get('name02','')
                        )
                        contact_platform.contact = contact
                        contact_platform.save()

                    # Getting Case
                    logger.info(f"Getting case for {order_id}")
                    order_data = get_ecforce_order_by_id(order_id, url, token)
                    if 'customer_contacts' in order_data and order_data.get('customer_contacts'):
                        logger.info(f"Case found in {order_id}")
                        case, _ = Deals.objects.get_or_create(
                            workspace=workspace,
                            contact=contact,
                            status="active",
                            name=order_data.get('customer_contacts')
                        )
                        case_platform = DealsPlatforms.objects.filter(
                            channel=channel,
                            platform_id=order_data.get('id')
                        ).first()
                        if case_platform:
                            case_platform.deal = case
                            case_platform.save()
                        else:
                            case_platform = DealsPlatforms.objects.create(
                                channel=channel,
                                deal=case,
                                platform_id=order_data.get('id')
                            )
                    else:
                        logger.info(f"No case found in {order_id}")

                    address_relationship = customer_payload['relationships']['billing_address']['data']
                    customer_addresses = [
                        address for address in addresses if address['id'] == address_relationship['id']]
                    shipping_address_relationships = customer_payload[
                        'relationships']['shipping_addresses']['data']
                    shipping_addresses = []
                    for shipping_address_relationship in shipping_address_relationships:
                        for address in addresses:
                            if address['id'] == shipping_address_relationship['id']:
                                shipping_addresses.append(address)
                    shipping_address_payload = shipping_addresses[0] if len(
                        shipping_addresses) > 0 else None
                    logger.info(
                        f'Processing shipping address: {shipping_address_payload}')

                    if len(customer_addresses) > 0:
                        address_payload = customer_addresses[0]
                        logger.info(
                            f'Processing customer address: {address_payload}')
                        contact.name = address_payload['attributes']['name01']
                        contact.last_name = address_payload['attributes']['name02']
                        phone_number = address_payload['attributes']['tel01']
                        if len(phone_number) > 0:
                            if phone_number[0] == '0':
                                tmp = list(phone_number)
                                tmp[0] = '+81-'
                                phone_number = ''.join(tmp)
                            else:
                                phone_number = '+81-' + phone_number
                        contact.phone_number = phone_number

                    contact.status = 'active'
                    contact.save()
                    shopturbo_order.contact = contact
                    shopturbo_order.save()

                if mapping_custom_fields:
                    for field in mapping_custom_fields:
                        sanka_field = mapping_custom_fields[field]
                        try:
                            order_attributes = order['attributes'] if order else {
                            }
                            customer_attributes = customer_payload['attributes'] if customer_payload else {
                            }
                            address_attributes = address_payload['attributes'] if address_payload else {
                            }
                            shipping_address_attributes = shipping_address_payload['attributes'] if shipping_address_payload else {
                            }

                            value = None
                            if '|item' not in field:
                                if field == 'customer_number':
                                    value = order_attributes.get(
                                        'customer_number', None)
                                elif field == 'customer_status':
                                    value = order_attributes.get(
                                        'customer_status', None)
                                elif field == 'membership_rank_name':
                                    value = customer_attributes.get(
                                        'membership_rank_name', None)
                                elif field == 'customer_mobile_email_address':
                                    value = customer_attributes.get(
                                        'mobile_email', None)
                                elif field == 'customer_gender':
                                    value = customer_attributes.get(
                                        'sex', None)
                                elif field == 'purchase_count':
                                    value = customer_attributes.get(
                                        'buy_times', None)
                                elif field == 'total_purchase_amount':
                                    value = customer_attributes.get(
                                        'buy_total', None)
                                elif field == 'membership_date':
                                    value = customer_attributes.get(
                                        'created_at', None)
                                elif field == 'billing_name_full':
                                    value = address_attributes.get(
                                        'full_name', None)
                                elif field == 'billing_name_kana_full':
                                    value = address_attributes.get(
                                        'full_kana', None)
                                elif field == 'billing_postal_code_full':
                                    value = address_attributes.get(
                                        'full_zip', None)
                                elif field == 'billing_address_full':
                                    value = address_attributes.get(
                                        'full_address', None)
                                elif field == 'billing_prefecture':
                                    value = address_attributes.get(
                                        'prefecture_name', None)
                                elif field == 'billing_phone_number_full':
                                    value = address_attributes.get(
                                        'full_tel', None)
                                elif field == 'billing_fax_full':
                                    value = address_attributes.get(
                                        'full_fax', None)
                                elif field == 'billing_name_1':
                                    value = address_attributes.get(
                                        'name01', None)
                                elif field == 'billing_name_2':
                                    value = address_attributes.get(
                                        'name02', None)
                                elif field == 'billing_kana_1':
                                    value = address_attributes.get(
                                        'kana01', None)
                                elif field == 'billing_kana_2':
                                    value = address_attributes.get(
                                        'kana02', None)
                                elif field == 'billing_postal_code_1':
                                    value = address_attributes.get(
                                        'zip01', None)
                                elif field == 'billing_postal_code_2':
                                    value = address_attributes.get(
                                        'zip02', None)
                                elif field == 'billing_address_1':
                                    value = address_attributes.get(
                                        'addr01', None)
                                elif field == 'billing_address_2':
                                    value = address_attributes.get(
                                        'addr02', None)
                                elif field == 'billing_address_3':
                                    value = address_attributes.get(
                                        'addr03', None)
                                elif field == 'billing_phone_number_1':
                                    value = address_attributes.get(
                                        'tel01', None)
                                elif field == 'billing_phone_number_2':
                                    value = address_attributes.get(
                                        'tel02', None)
                                elif field == 'billing_phone_number_3':
                                    value = address_attributes.get(
                                        'tel03', None)
                                elif field == 'billing_fax_1':
                                    value = address_attributes.get(
                                        'fax01', None)
                                elif field == 'billing_fax_2':
                                    value = address_attributes.get(
                                        'fax02', None)
                                elif field == 'billing_fax_3':
                                    value = address_attributes.get(
                                        'fax03', None)
                                elif field == 'total_points':
                                    value = customer_attributes.get(
                                        'point', None)
                                elif field == 'point_expiry_date':
                                    value = customer_attributes.get(
                                        'point_expired_at', None)
                                elif field == 'customer_type_name':
                                    value = customer_attributes.get(
                                        'customer_type_name', None)
                                elif field == 'email_newsletter_subscription':
                                    value = customer_attributes.get(
                                        'optin', None)
                                elif field == 'memo':
                                    value = order_attributes.get(
                                        'memo01', None)
                                elif field == 'blacklist':
                                    value = customer_attributes.get(
                                        'blacklist', None)
                                elif field == 'blacklist_reason':
                                    value = customer_attributes.get(
                                        'blacklist_reasons', None)
                                elif field == 'billing_address_full_with_spaces':
                                    value = address_attributes.get(
                                        'full_address_with_space', None)
                                elif field == 'line_id':
                                    value = customer_attributes.get(
                                        'line_id', None)
                                elif field == 'coupon':
                                    value = order_attributes.get(
                                        'coupons', None)
                                elif field == 'customer_label':
                                    value = order_attributes.get(
                                        'customer_labels', None)
                                elif field == 'do_not_send_email':
                                    value = customer_attributes.get(
                                        'mail_delivery_stop', None)
                                elif field == 'np_postpaid_real_time_royal_customer':
                                    value = customer_attributes.get(
                                        'np_royal_customer', None)
                                elif field == 'subtotal':
                                    value = order_attributes.get(
                                        'subtotal', None)
                                elif field == 'subtotal8':
                                    value = order_attributes.get(
                                        'subtotal8', None)
                                elif field == 'subtotal10':
                                    value = order_attributes.get(
                                        'subtotal10', None)
                                elif field == 'deliv_fee':
                                    value = order_attributes.get(
                                        'deliv_fee', None)
                                elif field == 'charge':
                                    value = order_attributes.get(
                                        'charge', None)
                                elif field == 'tax':
                                    value = order_attributes.get(
                                        'tax', None)
                                elif field == 'tax8':
                                    value = order_attributes.get(
                                        'tax8', None)
                                elif field == 'tax10':
                                    value = order_attributes.get(
                                        'tax10', None)
                                elif field == 'total':
                                    value = order_attributes.get(
                                        'total', None)
                                elif field == 'total8':
                                    value = order_attributes.get(
                                        'total8', None)
                                elif field == 'total10':
                                    value = order_attributes.get(
                                        'total10', None)
                                elif field == 'payment_total':
                                    value = order_attributes.get(
                                        'payment_total', None)
                                elif field == 'shipping_carrier_name':
                                    value = order_attributes.get(
                                        'shipping_carrier_name', None)
                                elif field == 'shipping_slip':
                                    value = order_attributes.get(
                                        'shipping_slip', None)
                                elif field == 'payment_method_name':
                                    value = order_attributes.get(
                                        'payment_method_name', None)
                                elif field == 'kind':
                                    value = order_attributes.get(
                                        'kind', None)
                                elif field == 'nth':
                                    value = order_attributes.get(
                                        'nth', None)
                                elif field == 'scheduled_delivery_time_code':
                                    value = order_attributes.get(
                                        'scheduled_delivery_time_code', None)
                                elif field == 'shipping_name_full':
                                    value = shipping_address_attributes.get(
                                        'full_name', None)
                                elif field == 'shipping_name_kana_full':
                                    value = shipping_address_attributes.get(
                                        'full_kana', None)
                                elif field == 'shipping_postal_code_full':
                                    value = shipping_address_attributes.get(
                                        'full_zip', None)
                                elif field == 'shipping_address_full':
                                    value = shipping_address_attributes.get(
                                        'full_address', None)
                                elif field == 'shipping_prefecture':
                                    value = shipping_address_attributes.get(
                                        'prefecture_name', None)
                                elif field == 'shipping_phone_number_full':
                                    value = shipping_address_attributes.get(
                                        'full_tel', None)
                                elif field == 'shipping_fax_full':
                                    value = shipping_address_attributes.get(
                                        'full_fax', None)
                                elif field == 'shipping_name_1':
                                    value = shipping_address_attributes.get(
                                        'name01', None)
                                elif field == 'shipping_name_2':
                                    value = shipping_address_attributes.get(
                                        'name02', None)
                                elif field == 'shipping_kana_1':
                                    value = shipping_address_attributes.get(
                                        'kana01', None)
                                elif field == 'shipping_kana_2':
                                    value = shipping_address_attributes.get(
                                        'kana02', None)
                                elif field == 'shipping_postal_code_1':
                                    value = shipping_address_attributes.get(
                                        'zip01', None)
                                elif field == 'shipping_postal_code_2':
                                    value = shipping_address_attributes.get(
                                        'zip02', None)
                                elif field == 'shipping_address_1':
                                    value = shipping_address_attributes.get(
                                        'addr01', None)
                                elif field == 'shipping_address_2':
                                    value = shipping_address_attributes.get(
                                        'addr02', None)
                                elif field == 'shipping_address_3':
                                    value = shipping_address_attributes.get(
                                        'addr03', None)
                                elif field == 'shipping_phone_number_1':
                                    value = shipping_address_attributes.get(
                                        'tel01', None)
                                elif field == 'shipping_phone_number_2':
                                    value = shipping_address_attributes.get(
                                        'tel02', None)
                                elif field == 'shipping_phone_number_3':
                                    value = shipping_address_attributes.get(
                                        'tel03', None)
                                elif field == 'shipping_fax_1':
                                    value = shipping_address_attributes.get(
                                        'fax01', None)
                                elif field == 'shipping_fax_2':
                                    value = shipping_address_attributes.get(
                                        'fax02', None)
                                elif field == 'shipping_fax_3':
                                    value = shipping_address_attributes.get(
                                        'fax03', None)
                                elif field == 'order_memo':
                                    if order_attributes.get('memo01', None) and order_attributes.get('memo02', None):
                                        value = order_attributes.get(
                                            'memo01', '') + ' ' + order_attributes.get('memo02', '')
                                    elif order_attributes.get('memo01', None):
                                        value = order_attributes.get(
                                            'memo01', None)
                                    elif order_attributes.get('memo02', None):
                                        value = order_attributes.get(
                                            'memo02', None)
                                    else:
                                        value = None
                                elif field == 'product_name_with_tax':
                                    value = order_attributes.get(
                                        'product_name_with_tax', None)
                                elif field == 'product_labels':
                                    value = order_attributes.get(
                                        'product_labels', None)
                                elif field == 'wrapping':
                                    value = order_attributes.get(
                                        'wrapping', None)
                                elif field == 'remark':
                                    value = order_attributes.get(
                                        'remark', None)
                                elif field == 'payment_state':
                                    value = order_attributes.get(
                                        'payment_state', None)
                                elif field == 'first_reminded_at':
                                    value = order_attributes.get(
                                        'first_reminded_at', None)
                                elif field == 'remindered_at':
                                    value = order_attributes.get(
                                        'remindered_at', None)
                                elif field == 'reminder_count':
                                    value = order_attributes.get(
                                        'reminder_count', None)
                                elif field == 'due_at':
                                    value = order_attributes.get(
                                        'due_at', None)
                                elif field == 'payment_authed_at':
                                    value = order_attributes.get(
                                        'payment_authed_at', None)
                                elif field == 'payment_completed_at':
                                    value = order_attributes.get(
                                        'payment_completed_at', None)
                                elif field == 'payment_paid_at':
                                    value = order_attributes.get(
                                        'payment_paid_at', None)
                                elif field == 'payment_voided_at':
                                    value = order_attributes.get(
                                        'payment_voided_at', None)
                                elif field == 'payment_access_id':
                                    value = order_attributes.get(
                                        'payment_access_id', None)
                                elif field == 'payment_access_pass':
                                    value = order_attributes.get(
                                        'payment_access_pass', None)
                                elif field == 'url_group':
                                    value = order_attributes.get(
                                        'url_group', None)
                                elif field == 'advertiser':
                                    value = order_attributes.get(
                                        'advertiser', None)
                                elif field == 'scheduled_to_be_delivered_at':
                                    value = order_attributes.get(
                                        'scheduled_to_be_delivered_at', None)
                                elif field == 'discount_with_point':
                                    value = order_attributes.get(
                                        'discount_with_point', None)
                                elif field == 'discount_with_point8':
                                    value = order_attributes.get(
                                        'discount_with_point8', None)
                                elif field == 'discount_with_point10':
                                    value = order_attributes.get(
                                        'discount_with_point10', None)
                                elif field == 'discount':
                                    value = order_attributes.get(
                                        'discount', None)
                                elif field == 'discount8':
                                    value = order_attributes.get(
                                        'discount8', None)
                                elif field == 'discount10':
                                    value = order_attributes.get(
                                        'discount10', None)
                                elif field == 'point':
                                    value = order_attributes.get(
                                        'point', None)
                                elif field == 'point8':
                                    value = order_attributes.get(
                                        'point8', None)
                                elif field == 'point10':
                                    value = order_attributes.get(
                                        'point10', None)
                                elif field == 'reward_point':
                                    value = order_attributes.get(
                                        'reward_point', None)
                                elif field == 'point_expired_at':
                                    value = customer_attributes.get(
                                        'point_expired_at', None)
                                elif field == 'scheduled_to_be_shipped_at':
                                    value = order_attributes.get(
                                        'scheduled_to_be_shipped_at', None)
                                elif field == 'previous_scheduled_to_be_shipped_at':
                                    value = order_attributes.get(
                                        'previous_scheduled_to_be_shipped_at', None)
                                elif field == 'previous_scheduled_to_be_delivered_at':
                                    value = order_attributes.get(
                                        'previous_scheduled_to_be_delivered_at', None)
                                elif field == 'store':
                                    value = order_attributes.get(
                                        'store', None)
                                elif field == 'bundled_items':
                                    value = order_attributes.get(
                                        'bundled_items', None)
                                elif field == 'misc_fee':
                                    value = order_attributes.get(
                                        'misc_fee', None)
                                elif field == 'adjustment':
                                    value = order_attributes.get(
                                        'adjustment', None)
                                elif field == 'tbc':
                                    value = order_attributes.get(
                                        'tbc', None)
                                elif field == 'tbc_order_reasons':
                                    value = order_attributes.get(
                                        'tbc_order_reasons', None)
                                elif field == 'payment_number':
                                    value = order_attributes.get(
                                        'payment_number', None)
                                elif field == 'times':
                                    value = order_attributes.get(
                                        'times', None)
                                elif field == 'shipping_histories_count':
                                    value = order_attributes.get(
                                        'shipping_histories_count', None)
                                elif field == 'picked_list':
                                    value = order_attributes.get(
                                        'picked_list', None)
                                elif field == 'picked_at':
                                    value = order_attributes.get(
                                        'picked_at', None)
                                elif field == 'payment_last_error_message':
                                    value = order_attributes.get(
                                        'payment_last_error_message', None)
                                elif field == 'customer_contacts':
                                    value = order_attributes.get(
                                        'customer_contacts', None)
                                elif field == 'ip':
                                    value = order_attributes.get(
                                        'ip', None)
                                elif field == 'device_variant':
                                    value = order_attributes.get(
                                        'device_variant', None)
                                elif field == 'shipping_carrier_code':
                                    value = order_attributes.get(
                                        'shipping_carrier_code', None)
                                elif field == 'last_order':
                                    value = order_attributes.get(
                                        'last_order', None)
                                elif field == 'memo01':
                                    value = order_attributes.get(
                                        'memo01', None)
                                elif field == 'memo02':
                                    value = order_attributes.get(
                                        'memo02', None)
                                elif field == 'subs_order_memo01':
                                    value = order_attributes.get(
                                        'subs_order_memo01', None)
                                elif field == 'subs_order_memo02':
                                    value = order_attributes.get(
                                        'subs_order_memo02', None)
                                elif field == 'invite_code':
                                    value = order_attributes.get(
                                        'invite_code', None)
                                elif field == 'total_recurring_sales_price_discount':
                                    value = order_attributes.get(
                                        'total_recurring_sales_price_discount', None)
                                elif field == 'pay_with_epos':
                                    value = order_attributes.get(
                                        'pay_with_epos', None)
                                elif field == 'grant_plan_point':
                                    value = order_attributes.get(
                                        'grant_plan_point', None)
                                elif field == 'o_plux_result':
                                    value = order_attributes.get(
                                        'o_plux_result', None)

                        except:
                            value = None
                            traceback.print_exc()

                        custom_field = ShopTurboOrdersNameCustomField.objects.filter(
                            workspace=workspace, name=sanka_field).first()
                        if custom_field:
                            custom_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                                field_name=custom_field, orders=shopturbo_order)
                            custom_value.value = value
                            custom_value.save()

                # HubSpot
                if hubspot_channel_id:
                    _items = []
                    _amount = 0
                    for item_order in shopturbo_order.shopturboitemsorders_set.all():
                        _item_platform = item_order.item.item.filter(
                            channel=channel, platform_type='default').first()
                        if _item_platform and _item_platform.platform_id:
                            _items.append({
                                'name': item_order.item.name,
                                'description': item_order.item.description,
                                'hs_sku': _item_platform.platform_id,
                                'price': item_order.item.price,
                                'quantity': item_order.number_item,
                                # 'currency': None                            # TODO: update later
                            })
                            _amount += item_order.number_item * item_order.item.price

                    _contact = None
                    if shopturbo_order.contact:
                        _contact = {
                            "sanka_id": f'{shopturbo_order.contact.contact_id}',
                            "name": display_contact_name(shopturbo_order.contact),
                            "email": shopturbo_order.contact.email if shopturbo_order.contact.email else '',
                            "phone": shopturbo_order.contact.phone_number if shopturbo_order.contact.phone_number else '',
                            "address": shopturbo_order.contact.location if shopturbo_order.contact.location else ''
                        }

                    order_id_map_orders[f'{shopturbo_order.order_id}'] = {
                        'order_at': shopturbo_order.order_at,
                        'platform_order_id': platform_order_id,
                        'platform': channel.integration.title_ja if channel.integration.title_ja else channel.integration.title,
                        'amount': _amount,
                        'items': _items,
                        'company': _contact
                    }
                success_number += 1
            except Exception as e:
                logger.info(
                    "=== import_ecforce_orders -- Error import order " + str(e))
                logger.info(traceback.format_exc())
                failed_number += 1
                continue
    # Update task progress
    task.success_number = success_number
    task.failed_number = failed_number
    task.progress = 100 * (success_number) / total_orders
    task.save()
    
    # HubSpot
    if hubspot_channel_id:
        order_ids = order_id_map_orders.keys()
        if len(order_ids) == 0:
            return True

        channel = Channel.objects.get(id=hubspot_channel_id)
        logger.info(
            f'>>>>>>>>>>>>>>>> Start PUSH orders to HubSpot channel {channel.name}')
        push_hubspot_orders(str(channel.id), order_id_map_orders)
        logger.info(
            f'<<<<<<<<<<<<<<<< Finish PUSH orders to HubSpot channel {channel.name}')

    return True


def push_ecforce_orders(user, order_ids, channel_id):
    channel = Channel.objects.get(id=channel_id)
    api_key = channel.api_key
    url = channel.account_id
    platform = channel.integration.slug
    workspace = channel.workspace
    logger.info("====== push_ecforce_orders")
    orders = ShopTurboOrders.objects.filter(id__in=order_ids)

    # TODO: Update logic later

    return True
