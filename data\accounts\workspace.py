import ast
import uuid

from django.core.paginator import Paginator
from django.db.models import Q
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django_hosts.resolvers import reverse

from data.constants.date_constant import DATE_FORMAT
from data.constants.properties_constant import *
from data.models import *
from data.accounts.background.fix_total_price import fix_total_price, FixTotalPricePayload
from utils.decorator import login_or_hubspot_required
from utils.discord import DiscordNotification
from utils.bgjobs.runner import trigger_bg_job
from utils.logger import logger
from utils.meter import sync_usage
from utils.project import get_ordered_projects
from utils.utility import check_if_int_castable, getEnglishTranslation, get_workspace
from utils.workspace import create_workspace, get_permission

from data.accounts.background.export_csv_system_log import ExportCSVSystemLogPayload, export_csv_system_log
from data.accounts.background.delete_records import DeleteRecordsPayload, delete_records_task
from data.accounts.background.delete_workspace import DeleteWorkspacePayload, delete_workspace_task
from utils.bgjobs.handler import add_hatchet_run_id, create_bg_job

POSTS_PER_PAGE = 30
POSTS_PER_PAGE_BEGIN = 29


@login_or_hubspot_required
def group_teammate(request, id=None):
    workspace = get_workspace(request.user)
    group = Group.objects.get(id=id)
    if request.method == "POST":
        if request.POST.get("delete-group"):
            group.delete()

        elif request.POST.get("update-users-group"):
            users = request.POST.getlist("users_group")
            child_groups = request.POST.getlist("child_groups")
            title = request.POST.get("title")
            description = request.POST.get("description")
            users = User.objects.filter(username__in=users)
            group.user.set(users)
            groups = Group.objects.filter(id__in=child_groups)
            group.child_groups.set(groups)
            group.title = title
            group.description = description
            group.save()

        return redirect(reverse("workspace_groups", host="app"))
    users = User.objects.filter(workspace=workspace)
    context = {
        "group": group,
        "users": users,
        "groups": Group.objects.filter(workspace=workspace).exclude(pk=group.pk),
    }
    return render(request, "data/partials/group-add-teammate.html", context)


@login_or_hubspot_required
def workspace_groups(request, id=None):
    lang = request.LANGUAGE_CODE

    page_group_type = "workspace_groups"
    menu_key = "workspace"
    if lang == "ja":
        page_title = "グループ管理"
    else:
        page_title = "Group Management"

    permission = get_permission(
        object_type=TYPE_OBJECT_USER_MANAGEMENT, user=request.user
    )
    if not permission:
        permission = "hide"

    workspace = get_workspace(request.user)
    groups = Group.objects.filter(workspace=workspace)
    projects = get_ordered_projects(workspace)

    you = User.objects.get(workspace=workspace, id=request.user.id)
    teammates = User.objects.filter(workspace=workspace).exclude(id=you.id)

    context = {
        "page_title": page_title,
        "menu_key": menu_key,
        "page_group_type": page_group_type,
        "workspace": workspace,
        "groups": groups,
        "teammates": teammates,
        "projects": projects,
        "page_type": "users",
        "permission": permission,
    }

    return render(request, "data/account/workspace/workspace_template.html", context)


@login_or_hubspot_required
def search_workspaces(request):
    if request.method == "POST":
        user = User.objects.get(id=request.user.id)
        active_workspace = get_workspace(request.user)

        search_query = str(request.POST.get("search", ""))

        query_filter = Q(user=user)
        if search_query:
            query_filter &= Q(name__icontains=search_query.lower())

        if active_workspace:
            query_filter &= ~Q(id=active_workspace.id)

        user_workspaces = Workspace.objects.filter(query_filter)
        if active_workspace:
            user_workspaces.exclude(id=active_workspace.id)

        context = {
            "workspaces": user_workspaces,
        }
        return render(request, "data/account/workspace/workspace_search.html", context)

    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def workspace_basic_info(request):
    lang = request.LANGUAGE_CODE
    if lang == "ja":
        page_title = "基本情報設定"
    else:
        page_title = "Basic Information"
    menu_key = "workspace"

    workspace = get_workspace(request.user)
    if not workspace:
        return redirect(reverse("start", host="app"))

    permission = get_permission(object_type="basic", user=request.user)
    if not permission:
        permission = "hide"

    if request.method == "POST":
        if "edit_workspace" not in request.POST:
            return redirect(reverse("workspace_basic_info", host="app"))

        input_name = request.POST.get("input_name")
        allowed_ip = request.POST.get("allowed_ip", None)
        if allowed_ip:
            allowed_ip = ast.literal_eval(allowed_ip)
            allowed_ip = ",".join([ip["value"] for ip in allowed_ip])
        else:
            allowed_ip = None

        workspace.name = input_name
        workspace.allowed_ip = allowed_ip
        workspace.save()

        # Decimal Point - unified setting for all objects
        decimal_format = request.POST.get("decimal_format", None)

        # Get or create the general decimal point setting
        decimal_point, _ = ShopTurboDecimalPoint.objects.get_or_create(
            workspace=workspace, app_name='general'
        )

        if decimal_point:
            prev_decimal_format = decimal_point.type
            if decimal_format in [
                "cut_off",
                "cut_over",
                "line_item_cut_off",
                "line_item_cut_over",
            ]:
                decimal_point.type = decimal_format
                decimal_point.save()
            else:
                decimal_point.type = None
                decimal_point.save()

            if prev_decimal_format != decimal_format:
                # NOTE: Run bulk update for all orders and invoices in hatchet background
                payload = FixTotalPricePayload(
                    user_id=str(request.user.id),
                    workspace_id=str(workspace.id),
                )
                
                job_id = create_bg_job(
                    workspace=workspace,
                    user=request.user,
                    function_name="fix_total_price",
                    payload=payload.model_dump(mode="json"),
                )
                payload.background_job_id = job_id
                
                # Log job parameters for debugging
                logger.info(f"TOTAL_PRICE_FIX_JOB: Starting total price fix for user {request.user.email} in workspace {workspace.id}")
                logger.info(f"TOTAL_PRICE_FIX_JOB: Decimal format changed from {prev_decimal_format} to {decimal_format}")
                
                ref = None
                try:
                    ref = fix_total_price.run_no_wait(input=payload)
                except Exception as e:
                    logger.error(f"TOTAL_PRICE_FIX_JOB: Exception occurred during fix_total_price: {str(e)}", exc_info=True)
                    ref = None
                
                is_running = None
                if ref:
                    logger.info(f"TOTAL_PRICE_FIX_JOB: Background job submitted successfully for user {request.user.email}")
                    add_hatchet_run_id(job_id, ref)
                    is_running = True
                else:
                    logger.error(f"TOTAL_PRICE_FIX_JOB: Failed to submit background job for user {request.user.email}")
                    is_running = False
                
                if is_running:
                    logger.info(f"TOTAL_PRICE_FIX_JOB: Successfully submitted total price fix job for user {request.user.email}")
                    # Note: No user notification needed for this internal operation
                else:
                    logger.error(f"TOTAL_PRICE_FIX_JOB: Failed to submit total price fix job for user {request.user.email} in workspace {workspace.id}")
                    logger.error(f"TOTAL_PRICE_FIX_JOB: Parameters used: {payload}")
                    # This is an internal operation, so we don't need to show error to user
                    # The decimal format change will still succeed, just the background update may fail

        # Update all existing app-specific decimal points to match the general setting
        ShopTurboDecimalPoint.objects.filter(
            workspace=workspace
        ).exclude(app_name='general').update(type=decimal_point.type)

        date_format = request.POST.get("date-format")
        if date_format in DATE_FORMAT:
            df_object, _ = DateFormat.objects.get_or_create(
                workspace=workspace, is_workspace_level=True
            )
            df_object.value = date_format
            df_object.save()

        selected_timezone = request.POST.get("timezone", None)
        workspace.timezone = selected_timezone

        currencies = request.POST.getlist("currency", None)
        if currencies:
            workspace.currencies = str(currencies)
        else:
            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="ワークスペースの通貨は空にできません。",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Workspace currency can not be empty.",
                    type="error",
                )
            return redirect(reverse("workspace_basic_info", host="app"))

        notif_task_assigned = bool(request.POST.get("notif-task-assigned"))
        workspace.notification_task_assigned = notif_task_assigned

        workspace.save()

        return redirect(reverse("workspace_basic_info", host="app"))

    allowed_ips = workspace.allowed_ip.split(",") if workspace.allowed_ip else None

    date_format_obj = DateFormat.objects.filter(
        workspace=workspace, is_workspace_level=True
    ).first()

    context = {
        "menu_key": menu_key,
        "page_title": page_title,
        "workspace": workspace,
        "allowed_ips": allowed_ips,
        "COUNTRY_CODE": HOLIDAY_COUNTRY_CODE,
        "page_type": "basic",
        "page_group_type": "basic",
        "DATE_FORMAT": DATE_FORMAT,
        "date_format_object": date_format_obj,
        "available_timezone": dict(Workspace.timezone.field.choices),
        "permission": permission,
    }
    return render(request, "data/account/workspace/workspace_template.html", context)


def workspace_logs(request):
    if request.method == "POST":
        lang = request.LANGUAGE_CODE
        workspace = get_workspace(request.user)

        if "download" in request.POST:
            if lang == "ja":
                curr_datetime = timezone.now().strftime("%Y年%m月%d日_%H:%M:%S")
                filename = f"サンカシステムログ_{curr_datetime}.csv"
            else:
                curr_datetime = timezone.now().strftime("%Y-%m-%d_%H:%M:%S")
                filename = f"sanka_system_log_{curr_datetime}.csv"

            history_name = filename
            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="export_system_log",
                name=history_name,
            )

            encoded_format = request.POST.get("encoded_format", "utf-8")
            
            payload = ExportCSVSystemLogPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                history_id=str(history.id),
                language=lang,
                encoded_format=encoded_format,
            )
            
            job_id = create_bg_job(
                workspace=workspace,
                user=request.user,
                function_name="export_csv_system_log",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id
            
            # Log export job parameters for debugging
            logger.info(f"EXPORT_JOB: Starting system log export for user {request.user.email} in workspace {workspace.id}")
            logger.info(f"EXPORT_JOB: Export parameters - function: export_csv_system_log, workspace: {workspace.id}, user: {request.user.id}")
            logger.info(f"EXPORT_JOB: History ID: {history.id}, Language: {lang}")
            logger.info(f"EXPORT_JOB: Encoded format: {encoded_format}")
            
            ref = None
            try:
                ref = export_csv_system_log.run_no_wait(input=payload)
            except Exception as e:
                logger.error(f"EXPORT_JOB: Exception occurred during export_csv_system_log: {str(e)}", exc_info=True)
                ref = None
            
            is_running = None  
            if ref:
                logger.info(f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}")
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                logger.error(f"EXPORT_JOB: Failed to submit background job for user {request.user.email}")
                is_running = False
            
            if is_running:
                logger.info(f"EXPORT_JOB: Successfully submitted system log export job for user {request.user.email}")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, csv will be sent to your email.",
                        type="success",
                    )
            else:
                logger.error(f"EXPORT_JOB: Failed to submit system log export job for user {request.user.email} in workspace {workspace.id}")
                logger.error(f"EXPORT_JOB: trigger_bg_job returned falsy value: {is_running}")
                logger.error(f"EXPORT_JOB: Parameters used: {payload}")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブの送信中にエラーが発生しました。サポートに連絡してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="There is an error while submitting the export job. Please contact support.",
                        type="error",
                    )
                history.delete()

            return redirect("workspace_logs")

    # GET method
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    page_title = "Workspace Logs"
    if lang == "ja":
        page_title = "ワークスペースのログ"
    menu_key = "logs"

    permission = get_permission(object_type="logs", user=request.user)
    if not permission:
        permission = "hide"

    filter_conditions = Q(workspace=workspace)
    search_q = request.GET.get("q")
    if search_q:
        search_q_number = None
        if check_if_int_castable(search_q):
            search_q_number = int(search_q)
            filter_conditions &= (
                Q(item__item_id=search_q_number)
                | Q(order__order_id=search_q_number)
                | Q(subscriptions__subscriptions_id=search_q_number)
                | Q(inventory__inventory_id=search_q_number)
                | Q(contact__contact_id=search_q_number)
                | Q(company__company_id=search_q_number)
            )
        else:
            if lang == "ja":
                query = search_q.rstrip().lower()

                for jsonData in [
                    ITEMS_COLUMNS_DISPLAY,
                    ORDERS_COLUMNS_DISPLAY,
                    SUBSCRIPTIONS_COLUMNS_DISPLAY,
                    CONTACTS_COLUMNS_DISPLAY,
                    COMPANY_COLUMNS_DISPLAY,
                    DEALS_COLUMNS_DISPLAY,
                    INVENTORY_COLUMNS_DISPLAY,
                    INVENTORY_TRANSACTION_COLUMNS_DISPLAY,
                ]:
                    output_query = getEnglishTranslation(query, jsonData)
                    if output_query:
                        query = output_query
                        break

                if query:
                    query = query.rstrip().lower().split()
            else:
                query = search_q.rstrip().lower().split()

            if query:
                for word in query:
                    filter_conditions &= Q(field_name__icontains=word)
    app_log = AppLog.objects.filter(filter_conditions).order_by("-created_at")

    # Pagination
    paginator = Paginator(app_log, 30)
    page = request.GET.get("page", 1)

    if page:
        page_content = paginator.page(page)
        app_log = page_content.object_list
        paginator_item_begin = (POSTS_PER_PAGE * int(page)) - POSTS_PER_PAGE_BEGIN
        paginator_item_end = POSTS_PER_PAGE * int(page)

    else:
        page_content = paginator.page(1)
        paginator_item_begin = POSTS_PER_PAGE * 1 - POSTS_PER_PAGE_BEGIN
        paginator_item_end = POSTS_PER_PAGE * 1

    context = {
        "menu_key": menu_key,
        "page_title": page_title,
        "workspace": workspace,
        "COUNTRY_CODE": HOLIDAY_COUNTRY_CODE,
        "app_logs": app_log,
        "search_q": search_q,
        "paginator_item_begin": paginator_item_begin,
        "paginator_item_end": paginator_item_end,
        "page_content": page_content,
        "paginator": paginator,
        "permission": permission,
    }
    return render(
        request, "data/account/workspace/settings/workspace_log_setting.html", context
    )


@login_or_hubspot_required
def delete_records(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if request.method == "POST":
        # Create a transfer history record for tracking
        history = TransferHistory.objects.create(
            workspace=workspace,
            user=request.user,
            name="Delete Records",
            status="pending",
            progress=0
        )

        # Create payload for background job
        payload = DeleteRecordsPayload(
            user_id=str(request.user.id),
            workspace_id=str(workspace.id),
            language=lang,
        )

        # Create background job
        job_id = create_bg_job(
            workspace=workspace,
            user=request.user,
            function_name="delete_records_task",
            transfer_history=history,
            payload=payload.model_dump(mode="json"),
        )
        payload.background_job_id = job_id

        # Log delete job parameters for debugging
        logger.info(f"DELETE_JOB: Starting records deletion for user {request.user.email} in workspace {workspace.id}")
        logger.info(f"DELETE_JOB: Delete parameters - function: delete_records_task, job_id: {job_id}")
        logger.info(f"DELETE_JOB: History ID: {history.id}, Language: {lang}")

        try:
            ref = delete_records_task.run_no_wait(input=payload)
            if ref:
                add_hatchet_run_id(job_id, ref.workflow_run_id)
                logger.info(f"DELETE_JOB: Successfully triggered background job with run_id: {ref.workflow_run_id}")
            else:
                logger.warning("DELETE_JOB: Background job reference is None")
        except Exception as e:
            logger.error(f"DELETE_JOB: Exception occurred during delete_records_task: {str(e)}", exc_info=True)
            # Update history status on failure
            history.status = 'failed'
            history.save()

            # Create error notification
            if lang == "ja":
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"レコードの削除の開始に失敗しました。サポートにお問い合わせください。 | {e}",
                    type="error",
                )
            else:
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"Failed to start records deletion. Please contact support. | {e}",
                    type="error",
                )

            return redirect(reverse("workspace_basic_info", host="app"))

        # Create initial notification that the job has started
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="レコードの削除を開始しました。完了時に通知されます。",
                type="info",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Records deletion has started. You will be notified when it's complete.",
                type="info",
            )

        return redirect(reverse("workspace_basic_info", host="app"))


def currency(request):
    # workspace = get_workspace(request.user)
    if request.method == "GET":
        currency_options = request.GET.get("currency_options")
        if currency_options:
            selected_currencies = request.GET.get("currency", "")
            q = request.GET.get("q", "")
            print(request.GET)
            if q:
                res = [
                    {"id": val[0], "text": val[1]}
                    for val in CURRENCY_MODEL
                    if val[0] not in selected_currencies.split(",")
                    and q.lower() in val[0].lower()
                ]
            else:
                res = [
                    {"id": val[0], "text": val[1]}
                    for val in CURRENCY_MODEL
                    if val[0] not in selected_currencies.split(",")
                ]
            context = {"results": res}
            return JsonResponse(context)

        else:
            predefined_currency = request.GET.get("predefined_currency", None)
            selected_currency = {}
            if predefined_currency:
                for val in CURRENCY_MODEL:
                    if val[0] == predefined_currency:
                        selected_currency = val[0]
                        break
            context = {
                "selected_currency": selected_currency,
            }
            return render(
                request,
                "data/account/workspace/settings/workspace_currency.html",
                context,
            )
    return HttpResponse(200)


@login_or_hubspot_required
def delete_workspace(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == "GET":
        page_title = "Delete Workspace"
        if lang == "ja":
            page_title = "ワークスペースを削除"

        context = {
            "menu_key": "workspace",
            "page_title": page_title,
            "workspace": workspace,
            "page_type": "delete",
        }
        return render(
            request,
            "data/account/workspace/settings/workspace_delete_setting.html",
            context,
        )

    # POST method - trigger background job for workspace deletion
    # Create a transfer history record for tracking
    history = TransferHistory.objects.create(
        workspace=workspace,
        user=request.user,
        name="Delete Workspace",
        status="pending",
        progress=0
    )

    # Create payload for background job
    payload = DeleteWorkspacePayload(
        user_id=str(request.user.id),
        workspace_id=str(workspace.id),
        language=lang,
    )

    # Create background job
    job_id = create_bg_job(
        workspace=workspace,
        user=request.user,
        function_name="delete_workspace_task",
        transfer_history=history,
        payload=payload.model_dump(mode="json"),
    )
    payload.background_job_id = job_id

    # Log delete job parameters for debugging
    logger.info(f"DELETE_WORKSPACE_JOB: Starting workspace deletion for user {request.user.email} in workspace {workspace.id}")
    logger.info(f"DELETE_WORKSPACE_JOB: Delete parameters - function: delete_workspace_task, job_id: {job_id}")
    logger.info(f"DELETE_WORKSPACE_JOB: History ID: {history.id}, Language: {lang}")

    try:
        ref = delete_workspace_task.run_no_wait(input=payload)
        if ref:
            add_hatchet_run_id(job_id, ref.workflow_run_id)
            logger.info(f"DELETE_WORKSPACE_JOB: Successfully triggered background job with run_id: {ref.workflow_run_id}")
        else:
            logger.warning("DELETE_WORKSPACE_JOB: Background job reference is None")
    except Exception as e:
        logger.error(f"DELETE_WORKSPACE_JOB: Exception occurred during delete_workspace_task: {str(e)}", exc_info=True)
        # Update history status on failure
        history.status = 'failed'
        history.save()

        # Create error notification
        if lang == "ja":
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"ワークスペースの削除の開始に失敗しました。サポートにお問い合わせください。 | {e}",
                type="error",
            )
        else:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Failed to start workspace deletion. Please contact support. | {e}",
                type="error",
            )

        return redirect(reverse("workspace_basic_info", host="app"))

    # Create initial notification that the job has started
    if lang == "ja":
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message="ワークスペースの削除を開始しました。完了時に通知されます。",
            type="info",
        )
    else:
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message="Workspace deletion has started. You will be notified when it's complete.",
            type="info",
        )

    # Immediately redirect to appropriate location since workspace will be deleted
    # Check if user has other workspaces
    remaining_workspaces = Workspace.objects.filter(user=request.user).exclude(id=workspace.id)

    if remaining_workspaces.exists():
        # Switch to the latest remaining workspace
        latest_workspace = remaining_workspaces.latest("id")
        try:
            verification = Verification.objects.get(user=request.user)
            verification.workspace = latest_workspace
            verification.save()

            # Add notification to the new workspace about the deletion
            if lang == "ja":
                Notification.objects.create(
                    workspace=latest_workspace,
                    user=request.user,
                    message=f"ワークスペース '{workspace.name}' の削除処理中です。別のワークスペースに切り替えました。",
                    type="info",
                )
            else:
                Notification.objects.create(
                    workspace=latest_workspace,
                    user=request.user,
                    message=f"Workspace '{workspace.name}' is being deleted. Switched to another workspace.",
                    type="info",
                )

            return redirect(reverse("workspace_basic_info", host="app"))
        except Verification.DoesNotExist:
            logger.warning(f"No verification found for user {request.user.email}")
            return redirect(reverse("start", host="app"))
    else:
        # No other workspaces, redirect to start page
        return redirect(reverse("start", host="app"))


@login_or_hubspot_required
def new_workspace(request):
    lang = request.LANGUAGE_CODE

    if request.method == "POST":
        workspace_title = request.POST.get("workspace_title")
        workspace_description = request.POST.get("workspace_description")
        workspace_currency = request.POST.get("currency", "jpy")

        workspace = create_workspace(
            request, workspace_title, workspace_description, workspace_currency
        )

        dn = DiscordNotification()
        message = (
            f"Congrats, new workspace 🎊🎉! name: {workspace.name} - <@U04KDB6UH2P>\n"
        )
        dn.send_message(message)

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="新しいワークスペースが作られました。",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="A new Workspace was created.",
                type="success",
            )

    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def switch_workspace(request):
    lang = request.LANGUAGE_CODE

    if request.method == "POST":
        workspace_id = request.POST.get("workspace_id")
        workspace = Workspace.objects.get(id=workspace_id)
        verification = Verification.objects.get(user=request.user)
        verification.workspace = workspace
        verification.save()
        # # Cache the workspace if possible
        # user = request.user
        # if hasattr(user, 'request'):
        #     user.request._cached_workspace = workspace

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message=str(workspace.name) + "にワークスペースを変更しました。",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="You have switched your Workspace to " + str(workspace.name),
                type="success",
            )

        return redirect(reverse("main", host="app"))


def load_drawer(request):
    workspace = get_workspace(request.user)
    drawer_type = request.GET.get("drawer_type", None)
    if drawer_type == "decimal_point":
        # Load a general decimal point setting (we'll use 'general' as app_name)
        decimal_point = ShopTurboDecimalPoint.objects.filter(
            workspace=workspace, app_name='general'
        ).first()

        context = {"decimal_point": decimal_point}
        return render(
            request,
            "data/shopturbo/manage-shopturbo-settings-decimal-points.html",
            context,
        )

    return HttpResponse(200)
