{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<style>
    .task-item:hover .task-grip svg{
        color: #211F1C !important;
    }
</style>

<div class="card border-0 shadow-none rounded-0 w-100 bg-white">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if download_view %}
                {% if LANGUAGE_CODE == 'ja' %}
                ダウンロードを管理
                {% else %}
                Manage Download
                {% endif %}
            {% elif panel %}
                {% if LANGUAGE_CODE == 'ja' %}
                レポートを管理
                {% else %}
                Manage Report
                {% endif %}
            {% else %}
                {% if LANGUAGE_CODE == 'ja' %}
                レポートを作成
                {% else %}
                Create Report
                {% endif %}
            {% endif %}
        </h5>

        <div class="card-toolbar">
            {% if create_type == 'addon-dashboard' %}
            <button 
                hx-get="{% host_url 'report_form' host 'app'  %}" 
                {% if report_id %}
                    hx-vals='{"type": "edit", "report_id": "{{report_id}}"}'
                {% else %}
                    hx-vals='{"type": "create"}'
                {% endif %}
                hx-target="#report-wizard"
                hx-trigger="click"
                hx-swap="innerHTML"
                id="htmx-trigger-button-panel"
                class="btn btn-icon me-n5 w-25" >
                <span class="svg-icon svg-icon-2">
                    {% if LANGUAGE_CODE == 'ja'%}
                    ダッシュボードに戻る
                    {% else %}
                    Go Back To Dashboards
                    {% endif %}  
                </span>
            </button>
            {% else %}
                {% if panel_id %}

                <div class="menu-item">
                    <button class="d-flex btn bg-white opacity-75-hover text-start py-3 px-1" type="button" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                        <span class="svg-icon svg-icon-2x">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <rect x="0" y="0" width="24" height="24"/>
                                    <circle fill="#000000" cx="12" cy="5" r="2"/>
                                    <circle fill="#000000" cx="12" cy="12" r="2"/>
                                    <circle fill="#000000" cx="12" cy="19" r="2"/>
                                </g>
                            </svg>
                        </span>
                    </button>

                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px py-3" data-kt-menu="true">
                        <div class="menu-item px-3">
                            <button type="submit" 
                                    form="panel_form_container" 
                                    class="menu-link px-3 text-danger border-0 bg-transparent w-100 text-start"
                                    onclick="document.getElementById('delete-panel-input').value = 'true';">
                                {% if LANGUAGE_CODE == 'ja' %}削除{% else %}Delete{% endif %}
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}
                <script>
                    KTMenu.createInstances();
                </script>

                <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss ms-2"
                    data-kt-drawer-dismiss="true">
                    <span class="svg-icon svg-icon-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
                                transform="rotate(-45 6 17.3137)" fill="black"></rect>
                            <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)"
                                fill="black"></rect>
                        </svg>
                    </span>
                </button>
            {% endif %}
        </div>
    </div>

    <div class="card-body">    
        <div class="mb-10">
            {% if create_type %} 
                {% if create_type == 'addon-dashboard' %}
                <!-- Trigger the second htmx request on success of the first -->
                <button class="d-none"
                    hx-get="{% host_url 'report_form' host 'app'  %}" 
                    hx-vals='{"type": "create"}'
                    hx-target="#report-wizard"
                    id="htmx-trigger-button-panel"
                    >
                </button>
                {%endif%}
                
            <script>
                document.body.addEventListener("showDashboardDrawer", function(evt){

                    if (evt.detail.event_panel_id !== null && evt.detail.event_panel_id !== undefined) {
                        // Show loading indicator immediately
                        const formOverlay = document.getElementById('form-overlay');
                        if (formOverlay) {
                            formOverlay.classList.remove('d-none');
                        }

                        // Trigger HTMX
                        // Trigger HTMX by simulating a click on the button
                        var htmxTriggerButton = document.getElementById("htmx-trigger-button-panel");
                        if (htmxTriggerButton) {

                            {% if create_type == 'addon-dashboard' %}
                                {% if report_id %}
                                    htmxTriggerButton.setAttribute('hx-vals', '{"type": "edit", "report_id": "{{report_id}}", "panel_id": "' + evt.detail.event_panel_id + '"}');
                                {% else %}
                                    htmxTriggerButton.setAttribute('hx-vals', '{"type": "create", "panel_id": "' + evt.detail.event_panel_id + '"}');
                                {% endif %}
                            {% endif %}
                            htmxTriggerButton.click();
                        }
                    }
                })
            </script>
            <div class="d-none" id="tempSinglePanel" ></div>
            <form id="create_panel_form"  hx-post="{% host_url 'panel_form' host 'app' %}" hx-encoding="multipart/form-data" hx-trigger="submit" hx-target="#tempSinglePanel">
            <input type="hidden" name="addon-order" value="0"/>    
            {%else%}
            <form method="post" action="{% host_url 'panel_form' host 'app' %}" hx-target="#tempSingleEntry" id="panel_form_container">
            {%endif%}
                <div id="form-overlay" class="overlay d-flex justify-content-center align-items-center d-none" style="position: absolute;
                    top: 0; left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255,255,255,0.7);
                    z-index: 10;">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading…</span>
                    </div>
                </div>
                {% csrf_token %}
                {% if panel %}<input type="hidden" name="panel_id" value="{{panel.id}}">{% endif %}
                <input type="hidden" name="module" value="{{module}}" >
                {% if is_template %}
                    {% include 'data/reports/panel-template-partial.html' %}
                {% endif %}
                {% if create_type == 'addon-dashboard' %}
                    <input type="hidden" name="addon-dashboard" value="0"/>
                {%endif%}  
                <div class="mb-10">
                    {% comment %} Name {% endcomment %}
                    {% if not is_template and not download_view %}
                    <div class="fv-rowd-flex flex-column mb-6">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja' %}
                                名前
                                {% else %}
                                Name
                                {% endif %}
                            </span>
                        </label>
                        <input required type="text" class="form-control"
                        {% if LANGUAGE_CODE == 'ja' %}
                        placeholder="名前"
                        {% else %}
                        placeholder="Name"
                        {% endif %}

                         name="name" {% if panel %}value="{{panel.name}}"{% endif %}>
                    </div>
                    {% endif %}
                    
                    {% if user.verification.admin %}
                    <div class="fv-rowd-flex flex-column mb-6">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja' %}
                                名前
                                {% else %}
                                Name
                                {% endif %}
                                - JA
                            </span>
                        </label>
                        <input required type="text" class="form-control"
                        {% if LANGUAGE_CODE == 'ja' %}
                        placeholder="名前 - JA"
                        {% else %}
                        placeholder="Name - JA"
                        {% endif %}

                        name="name_ja" {% if panel %}value="{{panel.name_ja|default:""}}"{% endif %}>
                    </div>
                    <div class="fv-rowd-flex flex-column mb-6">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja' %}
                                説明
                                {% else %}
                                Description
                                {% endif %}
                            </span>
                        </label>
                        <input required type="text" class="form-control"
                        {% if LANGUAGE_CODE == 'ja' %}
                        placeholder="説明"
                        {% else %}
                        placeholder="Description"
                        {% endif %}

                        name="description" {% if panel %}value="{{panel.description|default:""}}"{% endif %}>
                    </div>
                    <div class="fv-rowd-flex flex-column mb-6">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja' %}
                                説明
                                {% else %}
                                Description
                                {% endif %}
                                - JA
                            </span>
                        </label>
                        <input required type="text" class="form-control"
                        {% if LANGUAGE_CODE == 'ja' %}
                        placeholder="説明 - JA"
                        {% else %}
                        placeholder="Descrition - JA"
                        {% endif %}

                        name="description_ja" {% if panel %}value="{{panel.description_ja|default:""}}"{% endif %}>
                    </div>
                    <div class="fv-rowd-flex flex-column mb-6">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="required">
                                {% if LANGUAGE_CODE == 'ja' %}
                                テンプレート
                                {% else %}
                                Template
                                {% endif %}
                            </span>
                        </label>
                        <div class="flex items-center space-x-2">
                            <input id="is_template" name="is_template" type="checkbox"
                                class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                {% if panel.is_template %}checked{% endif %} />
                            <span class="text-sm text-gray-600">
                                {% if LANGUAGE_CODE == 'ja' %}
                                テンプレートとして保存
                                {% else %}
                                Mark as template
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% comment %} Panel Type {% endcomment %}
                    <div class="mb-6 w-100 {% if download_view %}d-none{% endif %}">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span {% if not panel %} class="required" {% endif %}>
                                {% if LANGUAGE_CODE == 'ja' %}
                                レポートタイプ
                                {% else %}
                                Report Type
                                {% endif %}
                            </span>
                        </label>
                        <select required class="form-select h-40px select2-this" id="panel-type" name="panel_type" data-control="select2"
                            hx-get="{% host_url 'panel_metric'  host 'app' %}?panel_id={{panel.id}}{% if download_view %}&download_view={{download_view}}{% endif %}" 
                            hx-target="#metric-container"
                            hx-include="#panel-type"
                            hx-swap="innerHTML" 
                            hx-trigger="load, panel-type-changed">
                        </select>

                        <script>
                            updatePanelTypeOptions()

                            $('#panel-type').on('change select2:select', function (e) {
                                const dataSource = document.getElementById('kt_accordion_settings')
                                for (let i = dataSource.children.length - 1; i > 0; i--) {
                                    dataSource.removeChild(dataSource.children[i]);
                                }
                                onChangeObjectSourceData()
                                updateDataSourceOptions()

                                panelType = e.currentTarget.value
                                console.log(panelType)
                                document.querySelector('#breakdown-box').classList.toggle('d-none', !['chart', 'stacked_chart', 'forecast'].includes(panelType));

                                // Show loading indicator
                                const panelTypeSelect = $('#panel-type');
                                panelTypeSelect.prop('disabled', true);
                                const loader = document.getElementById('form-overlay')
                                loader.classList.remove('d-none')

                                clearTimeout(window.panelTypeChangeTimer);
                                window.panelTypeChangeTimer = setTimeout(() => {
                                    htmx.trigger('#panel-type', 'panel-type-changed');
                                    // Remove loading indicator
                                    panelTypeSelect.prop('disabled', false);
                                }, 100);

                                setTimeout(() => {
                                    loader.classList.add('d-none')
                                }, 5000);
                            });

                            function getPanelTypeOptions() {
                                return [
                                    {% for type, title in PANEL_TYPE_TITLE.items %}
                                        {
                                            "value": "{{type}}", 
                                            "display": "{% if LANGUAGE_CODE == 'ja' %}{{title.ja}}{% else %}{{title.en}}{% endif %}"
                                        },
                                    {% endfor %}
                                ];
                            }

                            function updatePanelTypeOptions(){
                                const nextSelect = document.getElementById('panel-type');
                                nextSelect.innerHTML = '';

                                const select_options = getPanelTypeOptions();
                                select_options.forEach(function (option_val) {
                                    const optionElement = document.createElement('option');
                                    optionElement.value = option_val.value;
                                    optionElement.textContent = option_val.display;
                                    nextSelect.appendChild(optionElement);
                                });

                                {% if panel %}
                                nextSelect.value = "{{panel.panel_type}}"
                                {% endif %}
                            }

                            document.addEventListener("htmx:beforeRequest", function (event) {
                                if (event.target.id === "panel-type") {
                                    document.getElementById("form-overlay")?.classList.remove("d-none");
                                    setTimeout(() => {
                                        document.getElementById("form-overlay")?.classList.add("d-none");
                                    }, 5000)
                                }
                            });
                        </script>
                    </div>

                    <div id="metric-container">
                        {% comment %} Metrics {% endcomment %}
                        <div class="mb-6 w-100">
                            <div class="mb-3 d-flex align-items-center">
                                <label class="fs-6 fw-bold d-flex align-items-center">
                                    <span {% if not panel %} class="required" {% endif %}>
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        オブジェクト
                                        {% else %}
                                        Object
                                        {% endif %}
                                    </span>
                                </label>

                                <button id="add-data-source-btn" type="button" class="ms-4 pt-1 pb-2 px-4 rounded-1 btn btn-primary d-none" onclick="addDataSource(this)">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4"/>
                                    </svg>
                                </button>
                            </div>
                            
                            <div class="accordion mb-5" id="kt_accordion_settings">
                                {% if panel and panel.get_metrics|length > 0 %}
                                    {% for metric in panel.get_metrics %}
                                        {% include 'data/reports/panel-data-source-form.html' with metric=metric %}
                                    {% endfor %}
                                {% else %}
                                    {% include 'data/reports/panel-data-source-form.html' %}
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-6 w-100" id="filter-container">
                            <div class="mb-3 d-flex align-items-center">
                                <label class="fs-6 fw-bold d-flex align-items-center">
                                    <span {% if not panel %} {% endif %}>
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        フィルター
                                        {% else %}
                                        Filter
                                        {% endif %}
                                    </span>
                                </label>

                                <button id="add-filter-btn" type="button" 
                                    class="ms-4 pt-1 pb-2 px-4 rounded-1 btn btn-primary " 
                                    onclick="addPanelFilterChart(this.parentElement)">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4"/>
                                    </svg>
                                </button>

                            </div>

                            {% for filter in panel.filter.filters %}

                            <div class="d-flex py-1 d-flex align-items-center task-item">
                                <div class="w-100">
                                    <div class="d-flex filter-picker align-items-center">
                                        <select class="form-select h-40px select2-this panel-filter-select" 
                                        name="panel-filter-select" data-control="select2" onchange="onChangePanelFilterPropertySelect(this.parentElement)"
                                        style="width: 50%;">
                                            {% for data_source, prop_gr in properties.items %}
                                                <optgroup label={{data_source|capfirst}} >
                                                    {% for prop in prop_gr.base %}
                                                        <option value="{{prop.name}}" data-source="{{data_source}}" data-type="{{prop.type}}" data-val-option="{{prop.values}}" {% if filter.filter_select == prop.name and filter.filter_source == data_source %}selected{% endif %} >
                                                            {% if LANGUAGE_CODE == 'ja' %}
                                                                {{ PANEL_FILTER|get_item:data_source|get_item:prop.name|get_item:'ja'|default:prop.name }}
                                                            {% else %}
                                                                {{ PANEL_FILTER|get_item:data_source|get_item:prop.name|get_item:'en'|default:prop.name }}
                                                            {% endif %}
                                                        </option>
                                                    {% endfor %}
                                                    {% for prop in prop_gr.custom %}
                                                        <option value="{{prop.name}}" data-source="{{data_source}}" data-type="{{prop.type}}" {% if filter.filter_select == prop.name and filter.filter_source == data_source %}selected{% endif %} >{{ prop.name }}</option>
                                                    {% endfor %}
                                                </optgroup>
                                            {% endfor %}
                                            
                                        </select>

                                        <select class="form-select h-40px select2-this panel-filter-operator-select ms-2 me-2" name="panel-filter-operator" data-control="select2" style="width: 50%" onchange="onChangePanelFilterOperatorSelect(this.parentElement)">
                                            {% for prop_type, operators in filter_operators.items %}
                                                {% if prop_type == filter.filter_type %}
                                                    {% for opr in operators %}
                                                        <option value="{{opr.value}}" {% if filter.filter_operator == opr.value %}selected{% endif %}>
                                                            {% if LANGUAGE_CODE == 'ja' %}
                                                                {{ opr.display.ja }}
                                                            {% else %}
                                                                {{ opr.display.en }}
                                                            {% endif %}
                                                        </option>
                                                    {% endfor %}
                                                {% endif %}
                                            {% endfor %}
                                        </select>

                                        <input type="text" class="form-control ms-4" id="panel-filter-input-{{forloop.counter}}" name="panel-filter-input" placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルター値{% else %}Filter value{% endif %}" style="width: 75%;" 
                                            {% if filter.filter_type == 'choice' %}data-filter-input{% else %}value{% endif %}="{{filter.filter_input|filter_input_parser}}">
                                        
                                        <input hidden class="panel-filter-type-input" name="panel-filter-type" value="{{filter.filter_type}}">    
                                        <input hidden class="panel-filter-source-input" name="panel-filter-source" value="{{filter.filter_source}}">    
                                        
                                        <button type="button" class="ms-4 px-3 py-1 rounded-1 btn btn-danger" onclick="$(this).parent().remove()">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x" viewBox="0 0 16 16">
                                                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"/>
                                            </svg>
                                        </button>
                                    </div>
                                    
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="mb-8 mt-8 w-100 {% if panel and panel.panel_type != 'chart' and panel.panel_type != 'stacked_chart' and panel.panel_type != 'forecast' and panel.panel_type != 'summary_table' %}d-none{% endif %}" id="breakdown-box">
                            <div class="mb-6 d-flex align-items-center">
                                <label class="fs-6 fw-bold d-flex align-items-center">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        ブレイクダウン
                                        {% else %}
                                        Breakdown
                                        {% endif %}
                                    </span>
                                </label>

                                <button id="add-breakdown-btn" type="button" 
                                    class="ms-4 pt-1 pb-2 px-4 rounded-1 btn btn-primary {% if panel.breakdown%} d-none {% endif %}" 
                                    onclick="add_break_down()">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4"/>
                                    </svg>
                                </button>

                            </div>
                            


                            <div id="panel-breakdown" class="d-flex {% if not panel.breakdown %} d-none {% endif %}">
                                <select class="h-40px form-select" id="breakdown_id" name="breakdown_id">
                                    <option value="" >
                                        {% if LANGUAGE_CODE == 'ja' %}
                                        なし
                                        {% else %}
                                        None
                                        {% endif %}
                                    </option>
                                    {% for property in event_properties %}
                                        <option value="{{property.id}}" {% if panel.breakdown.id == property.id %}selected{% endif %}>{{property.name}}
                                        </option>
                                    {% endfor %}
                                </select>
                                <script>
                                    $(document).ready(function() {
                                        $('#breakdown_id').select2()
                                    })
                                </script>

                                <button type="button" class="ms-4 px-3 py-1 rounded-1 btn btn-danger" 
                                    onclick="remove_breakdown(this)">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x" viewBox="0 0 16 16">
                                        <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    {% comment %} Reports {% endcomment %}
                    {% if not is_template and not download_view %}
                    <div class="mb-6">
                        <label class="{% include 'data/utility/form-label.html' %} me-2">
                            <span>
                                {% if LANGUAGE_CODE == 'ja' %}ダッシュボードへ追加{% else %}Add Panel to Dashboard{% endif %}
                            </span>
                        </label>
                        <select class="form-select" id="report_ids" name="report_ids" multiple>
                            {% for report in reports_v2 %}
                                <option value="{{report.id}}" {% if report in panel.get_reports %}selected{% endif %}>{{report.name}}
                                </option>
                            {% endfor %}
                        </select>
                        <script>
                            $(document).ready(function() {
                                $('#report_ids').select2()
                            })
                        </script>
                    </div>
                    {% endif %}

                    {% comment %} Owner {% endcomment %}
                    <div class="owner-form-{{object_type}} mb-5">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span class="min-w-100px">
                                {% if LANGUAGE_CODE == 'ja'%}
                                所有者
                                {% else %}
                                Owner
                                {% endif %}
                            </span>
                        </label>
                        <select data-allow-clear='true' id="owner-input-{{object_type}}" class="h-40px form-select form-select-solid border bg-white select2-this" data-control="select2" name="owner" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}所有者{% else %}Owner{% endif %}">
                            <option></option>
                            {% if panel.id and panel.owner and panel.owner.user %}
                                <option value="{{panel.owner.user.username}}" selected>
                                    {{panel.owner.user.first_name}} - {{panel.owner.user.email}}
                                </option>
                            {% endif %}
                            {% for member in permission|get_scope_from_permission:'edit'|get_users_by_scope:user %}
                                {% if member != panel.owner.user %}
                                <option value="{{member.username}}">
                                    {{member.first_name}} - {{member.email}}
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>

                        <script>
                            $(parentDiv_{{object_type}}).ready(function () {    
                                $(parentDiv_{{object_type}}.querySelector('#owner-input-{{object_type}}')).select2();
                            });
                        </script>
                    </div>
                    
                    {% for CustomFieldName in NameCustomField %} 
                        {% if CustomFieldName %}
                            <div class="fv-rowd-flex flex-column mb-8">
                                <div class="mb-4">
                                    
                                    <span class="{% include 'data/utility/form-label.html' %}">
                                        {{CustomFieldName.name }}
                                    </span>
                                    
                                    {% include 'data/common/custom_field/custom-property.html' with CustomFieldName=CustomFieldName obj=panel object_type='panels' %}

                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                    <input id="delete-panel-input" type="hidden" name="delete-panel" value="true">
                </div>

                {% if download_view %}
                    <button name="download" type="submit" class="btn btn-dark" onclick="document.getElementById('delete-panel-input').remove();">
                        {% if panel %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            ダウンロード
                            {% else %}
                            Download
                            {% endif %}
                        {% endif %}
                    </button>
                {% else %}

                    <div class="separator separator-dashed mt-2 mb-5"></div>
                    {% if permission|check_permission:'edit' %}
                    <button type="submit" class="btn btn-dark w-100" onclick="document.getElementById('delete-panel-input').remove();">
                        {% if panel %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            レポートを更新
                            {% else %}
                            Update Report
                            {% endif %}
                        {% else %}
                            {% if LANGUAGE_CODE == 'ja' %}
                            レポートを作成
                            {% else %}
                            Create Report
                            {% endif %}
                        {% endif %}
                    </button>
                    {% endif %}
                {% endif %}
            </form>
        </div>
    </div>
</div>

<style>
    .modal-backdrop {
        z-index: 109 !important; /* Ensure it stays behind */
    }
</style>
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmationModalLabel">
                    {% if LANGUAGE_CODE == 'ja' %}
                    報告フォームの確認
                    {% else %}
                    Report Form Confirmation
                    {% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if LANGUAGE_CODE == 'ja' %}
                この操作を行うと、手動で入力した内容がすべて削除され、元に戻すことはできません。<br/>
                本当にデータを変更してもよろしいですか？
                {% else %}
                This action will remove all your manual input and cannot be undone.<br/>
                Are you sure you want to change the data?
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% if LANGUAGE_CODE == 'ja'%}
                    キャンセル
                    {% else %}
                    Cancel
                    {% endif %}
                </button>
                <button type="button" class="btn btn-dark" id="confirmBtn">
                    {% if LANGUAGE_CODE == 'ja'%}
                    送信
                    {% else %}
                    Submit
                    {% endif %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    var form = document.getElementById("panel_form_container");
    var modal = $('#confirmationModal');
    var confirmBtn = document.getElementById("confirmBtn");
    var cancelBtn = document.getElementById("cancelBtn");

    let shouldSubmit = false;

    form.addEventListener("submit", function(event) {
        var panelType = document.getElementById("panel-type")
        var needConfirmation = document.getElementById("need_confirmation")
        
        if (needConfirmation.value == 'yes' && panelType.value == "sheet") {
            if (!shouldSubmit) {
                modalElement = modal[0]
                var bootstrapModal = new bootstrap.Modal(modalElement);
                bootstrapModal.show();
                event.preventDefault(); // Stop the form
            }
        }
    });

    confirmBtn.addEventListener("click", function() {
        shouldSubmit = true;
        modalElement = modal[0]
        var bootstrapModal = new bootstrap.Modal(modalElement);
        bootstrapModal.hide();

        form.submit(); // Manually submit the form
    });

    form.addEventListener('keydown', function(event) {
        if (event.key === 'Enter' || event.keyCode === 13) {
            event.preventDefault();
        }
    });
</script>

<script>
    $(document).ready(function() {

        //Add Listener on Action Node select2
        $('.select2-this').select2();
        $('.select2-this').on('select2:select', function (e) {
            var selectElement = $(this).closest('select').get(0);
            selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
        });
    })

    var filter_operators = {{ filter_operators|safe }}
    var properties = {{ properties|safe }}
    var PANEL_SOURCE_TITLE_TYPE = {{ PANEL_SOURCE_TITLE_TYPE|safe }}
    var PANEL_FILTER = {{ PANEL_FILTER|safe }}

    function addDataSource(button) {
        let nextIndex = ($('#kt_accordion_settings .metric-item').length + 1)
        const panelType = document.getElementById('panel-type').value

        do {
            nextIndex++;
        } while (document.getElementById(`data_source_picker-${nextIndex}`));

        var metricSelectStr = `
            <select required="" class="form-select h-40px select2-this data_source_picker" name="data_source - ${nextIndex}" onchange="onChangeDataSource(this)" id="data_source_picker-${nextIndex}">
        `
        const selectOptions = getDataSourceOptions(panelType);
        selectOptions.forEach(function (grOption) {
            const metrics = getYAxisOptions(grOption.value, panelType)

            var grOptionIndex = metricSelectStr.indexOf(`<optgroup label="${grOption.display}">`)
            if (grOptionIndex !== -1) {
                var insertIndex = metricSelectStr.indexOf(`</optgroup>`, grOptionIndex)
                metrics.forEach((metric) => {
                    const optionElmStr = `<option value="${grOption.value}  -  ${metric.value} - ${metric.display}">${LANGUAGE_CODE === 'ja' ? metric.display_ja : metric.display}</option>`
                    metricSelectStr = metricSelectStr.split('')
                    metricSelectStr.splice(insertIndex, 0, optionElmStr)
                    metricSelectStr = metricSelectStr.join('')
                    insertIndex += optionElmStr.length
                })
            } else {
                metricSelectStr += `<optgroup label="${grOption.display}">`
                metrics.forEach((metric) => {
                    metricSelectStr += `
                        <option value="${grOption.value}  -  ${metric.value} - ${metric.display}">${LANGUAGE_CODE === 'ja' ? metric.display_ja : metric.display}</option>
                    `
                })
                metricSelectStr += `</optgroup>`
            }
        })
        metricSelectStr += `</select>`
        
        var metricTypeStr = `
            <select class="form-select h-40px select2-this metric_type_picker ms-4 d-none" name="metric_type - ${nextIndex}" 
            id="metric_type_picker-${nextIndex}"></select>
        `

        var metricCatStr = `
            <select class="form-select h-40px select2-this metric_category ms-4 d-none"
                id="metric_category-${nextIndex}"
                name="metric_category - ${nextIndex}">
            </select>
        `

        var metricContainerStr = `
            <div class="mb-3 pb-3 dt-source-container d-flex justify-content-between align-items-center metric-item">
                <div class="flex-grow-1" id="accordion_item_">
                    <div class="accordion-body p-0">
                        <div class="d-flex">
                            ${metricSelectStr}
                            ${metricTypeStr}
                            ${metricCatStr}
                            <button type="button" id="chart-filter-btn" class="filter-btn ms-4 px-3 py-1 rounded-1 btn btn-light-primary" onclick="filterToggle($(this)[0].parentElement)">
                                <svg xmlns="http://www.w3.org/2000/svg" height="16" width="16" fill="currentColor" class="bi bi-x" viewBox="0 0 512 512">
                                    <path d="M3.9 54.9C10.5 40.9 24.5 32 40 32H472c15.5 0 29.5 8.9 36.1 22.9s4.6 30.5-5.2 42.5L320 320.9V448c0 12.1-6.8 23.2-17.7 28.6s-23.8 4.3-33.5-3l-64-48c-8.1-6-12.8-15.5-12.8-25.6V320.9L9 97.3C-.7 85.4-2.8 68.8 3.9 54.9z"/>
                                </svg>
                            </button>
                        </div>
                    </div>  
                </div>  
                <button type="button" class="ms-4 px-3 py-1 rounded-1 btn btn-danger" onclick="removeDataSource(this)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x" viewBox="0 0 16 16">
                        <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"/>
                    </svg>
                </button>
            </div>  
        `;

        document.querySelector('#kt_accordion_settings').lastElementChild.insertAdjacentHTML('afterend', metricContainerStr);
        $('.data_source_picker').select2();
        // force select to first option on the list to avoid bug while the value from the select is still undefined
        $(`#data_source_picker-${nextIndex}`).each(function() {
            $(this).val($(this).find("option:first").val()).trigger('change');
        });
    }

    function add_break_down(){
        $('#panel-breakdown').removeClass('d-none')
        $('#add-breakdown-btn').addClass('d-none')
    }

    function remove_breakdown(element){
        $(element).parent().addClass('d-none')
        $('#add-breakdown-btn').removeClass('d-none')
        $('select[name="breakdown_id"]').val(null);
        
    }

    function onChangePanelFilterPropertySelect(parentElement) {
        const selector = parentElement.querySelector('.panel-filter-select')
        let inputField = parentElement.querySelector('[name="panel-filter-input"]')
        const dataSourceValue = selector.selectedOptions[0].getAttribute('data-source')
        let propType = selector.selectedOptions[0].getAttribute('data-type')
        let valOpt = selector.selectedOptions[0].getAttribute('data-val-option')


        parentElement.querySelector('.panel-filter-type-input').value = propType
        parentElement.querySelector('.panel-filter-source-input').value = dataSourceValue

        const operatorSelector = parentElement.querySelector('.panel-filter-operator-select')
        operatorSelector.innerHTML = ''

        operators_by_type = operators[propType]
        console.log(propType)
        console.log(operators_by_type)
        for (const operator of operators_by_type) {
            console.log(operator)
            const op_val = operator['value']
            const op_display = LANGUAGE_CODE == 'ja' ? operator['display']['ja'] : operator['display']['en']
            const option = document.createElement('option')
            option.value = op_val
            option.textContent = op_display
            operatorSelector.appendChild(option)
        }

        if (inputField.tagify) {
            inputField.tagify.destroy()
            inputField.tagify = undefined
        }
        
        inputField.setAttribute('type', 'text');
        if (propType == 'numeric') {
            inputField.setAttribute('type', 'number');
        } else if (propType == 'datetime') {
            inputField.setAttribute('type', 'date');
        } else if (valOpt) {
            valOpt = JSON.parse(valOpt);
            let tagifyWhitelist = valOpt.map(([value, label]) => ({
                id: value,
                value: label,
            }));

            let tagify = new Tagify(inputField, {
                whitelist: tagifyWhitelist,
                enforceWhitelist: true,
                maxTags: tagifyWhitelist.length, // Limit max selections based on available options
                searchKeys: ['value'], // Allow searching by label
                dropdown: {
                    maxItems: tagifyWhitelist.length,
                    classname: "tagify__inline__suggestions",
                    enabled: 0,
                    closeOnSelect: false
                },
                originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(',') // Submit IDs to backend
            });

            const preselected_value = inputField.getAttribute('value')
            if (preselected_value) {
                let selectedValues = preselected_value.split(",");
                let selectedTags = selectedValues.map(value => ({
                    id: value,
                    value: tagifyWhitelist.find(tag => tag.id === value)?.value || value
                }));
                
                tagify.addTags(selectedTags);
            }

            tagify.on('add', function (e) {
                console.log("Tag added:", tagify.value.map(tag => tag.value));
            });
        
            // Handle tag removal
            tagify.on('remove', function (e) {
                console.log("Tag removed:", tagify.value.map(tag => tag.value));
            });
            inputField.tagify = tagify
        } else if (propType == 'object') {
            inputField.classList.add('d-none')
        } else {
            inputField.setAttribute('type', 'text');
        }

    }

    function onChangePanelFilterOperatorSelect(parentElement) {
        const selector = parentElement.querySelector('.panel-filter-select')
        let inputField = parentElement.querySelector('[name="panel-filter-input"]')
        const dataSourceValue = selector.selectedOptions[0].getAttribute('data-source')
        let propType = selector.selectedOptions[0].getAttribute('data-type')
        let valOpt = selector.selectedOptions[0].getAttribute('data-val-option')

        const operatorSelector = parentElement.querySelector('.panel-filter-operator-select')

        if (inputField.tagify) {
            inputField.tagify.destroy()
            inputField.tagify = undefined
        }
        
        inputField.setAttribute('type', 'text');
        if (propType == 'numeric') {
            inputField.setAttribute('type', 'number');
        } else if (propType == 'datetime') {
            console.log(operatorSelector.value)
            if (operatorSelector.value == 'between') {
                val = inputField.value.split(' - ')
                if (val.length > 1) {
                    startTime = val[0]
                    endTime = val[1]
                }
                inputField.setAttribute('autocomplete', 'off');
                $(inputField).daterangepicker({
                    singleDatePicker: false,
                    startDate: startTime,
                    endDate: endTime,
                    timePicker: false,
                    autoUpdateInput: false,

                    showDropdowns: true,
                    drops: "auto",
                    locale: {
                        {% if LANGUAGE_CODE == 'ja' %}
                            cancelLabel: 'クリア',
                            format: 'YYYY年MM月DD日',
                            separator: ' 〜 ',
                            applyLabel: '選択',
                            cancelLabel: 'キャンセル',
                            fromLabel: 'From',
                            toLabel: 'To',
                            customRangeLabel: 'カスタム範囲',
                            daysOfWeek: ['日', '月', '火', '水', '木', '金', '土'],
                            monthNames: [
                                '1月', '2月', '3月', '4月', '5月', '6月',
                                '7月', '8月', '9月', '10月', '11月', '12月'
                            ],
                        {% else %}
                            format: "YYYY-MM-DD",
                        {% endif %}
                    }
                }, function(start, end) {
                    // Update input field value when a date is selected
                    let dateFormat = {% if LANGUAGE_CODE == 'ja' %} 'YYYY年MM月DD日' {% else %} 'YYYY-MM-DD' {% endif %};
                    let separator = {% if LANGUAGE_CODE == 'ja' %} ' 〜 ' {% else %} ' - ' {% endif %};
                    $(inputField).val(start.format(dateFormat) + ` ${separator} ` + end.format(dateFormat));
                });
            } else {
                inputField.setAttribute('type', 'date');
            }
        } else if (valOpt) {
            valOpt = JSON.parse(valOpt);
            let tagifyWhitelist = valOpt.map(([value, label]) => ({
                id: value,
                value: label,
            }));

            let tagify = new Tagify(inputField, {
                whitelist: tagifyWhitelist,
                enforceWhitelist: true,
                maxTags: tagifyWhitelist.length, // Limit max selections based on available options
                searchKeys: ['value'], // Allow searching by label
                dropdown: {
                    maxItems: tagifyWhitelist.length,
                    classname: "tagify__inline__suggestions",
                    enabled: 0,
                    closeOnSelect: false
                },
                originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(',') // Submit IDs to backend
            });

            const preselected_value = inputField.getAttribute('value')
            if (preselected_value) {
                let selectedValues = preselected_value.split(",");
                let selectedTags = selectedValues.map(value => ({
                    id: value,
                    value: tagifyWhitelist.find(tag => tag.id === value)?.value || value
                }));
                
                tagify.addTags(selectedTags);
            }

            tagify.on('add', function (e) {
                console.log("Tag added:", tagify.value.map(tag => tag.value));
            });
        
            // Handle tag removal
            tagify.on('remove', function (e) {
                console.log("Tag removed:", tagify.value.map(tag => tag.value));
            });
            inputField.tagify = tagify
        } else if (propType == 'object') {
            inputField.classList.add('d-none')
        } else {
            inputField.setAttribute('type', 'text');
        }
    }

    function addPanelFilterChart(parentElement) {
        var panelSourceTitle = {{ PANEL_SOURCE_TITLE|safe }};
        var panel_type = document.getElementById('panel-type').value
        var panelTitles = panelSourceTitle[panel_type]
        const selectsMetrics = document.querySelectorAll('select[id^="data_source_picker-"]');
        const selectedMetricsValue = [];
        const selectObjects = document.getElementById('object_source_picker').value
        // Loop through each select element and store its value into the array
        selectsMetrics.forEach(select => {
            const parsedVariable = select.value.split('  -  ')
            selectedMetricsValue.push(parsedVariable[0]);
        });

        let filterPickerString = `
            <div class="d-flex py-1 d-flex align-items-center task-item">
                <div class="w-100">
                    <div class="d-flex filter-picker align-items-center">
                        <select class="form-select h-40px select2-this panel-filter-select" 
                        name="panel-filter-select" data-control="select2" onchange="onChangePanelFilterPropertySelect(this.parentElement)"
                        style="width: 50%;">`;

        for (let [data_source, prop_gr] of Object.entries(properties)) {
            if (!selectedMetricsValue.includes(data_source) && (data_source != 'deals' && selectedMetricsValue.includes('contacts'))) {
                continue
            }

            if (!selectObjects.includes(data_source)) {
                continue
            }

            if (data_source == 'orders' && panel_type != 'summary_table' ) {
                data_source = 'commerce_orders'
            }

            if (LANGUAGE_CODE == 'ja') {
                labelPanel = panelTitles[data_source].ja
            } else {
                labelPanel = panelTitles[data_source].en
            }
            filterPickerString += `
                <optgroup label=${labelPanel} >`;

            for (let prop of [...prop_gr.base, ...prop_gr.custom]) {
                filterPickerString += `
                    <option value="${prop.name}" data-source="${data_source}" data-type="${prop.type}" data-val-option='${prop.values}'>
                        ${PANEL_FILTER?.[data_source]?.[prop.name] 
                            ? (LANGUAGE_CODE == 'ja' 
                                ? PANEL_FILTER[data_source][prop.name]?.['ja'] ?? prop.name 
                                : PANEL_FILTER[data_source][prop.name]?.['en'] ?? prop.name) 
                            : prop.name
                        }
                    </option>`;
            }

            filterPickerString += `</optgroup>`;
        }

        filterPickerString += `</select>
                        <select class="form-select h-40px select2-this panel-filter-operator-select me-2 ms-2" name="panel-filter-operator" data-control="select2" style="width: 50%" onchange="onChangePanelFilterOperatorSelect(this.parentElement)">`;

        for (let [prop_type, operators] of Object.entries(filter_operators)) {
            if (prop_type == 'string') {
                for (let opr of operators) {
                    filterPickerString += `
                        <option value="${opr.value}">${
                            LANGUAGE_CODE == 'ja' ? opr.display['ja'] : opr.display['en']
                        }</option>`;
                }
            }
        }

        filterPickerString += `</select>
                        <input type="text" class="form-control ms-4" name="panel-filter-input"  style="width: 75%;" 
                            value="">
                        
                        <input hidden class="panel-filter-type-input" name="panel-filter-type" value="">    
                        <input hidden class="panel-filter-source-input" name="panel-filter-source" value="">    
                        
                        <button type="button" class="ms-4 px-3 py-1 rounded-1 btn btn-danger" onclick="$(this).parent().parent().parent().remove()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-x" viewBox="0 0 16 16">
                                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>`;

        parentElement.insertAdjacentHTML('afterend', filterPickerString);

        panel_type = document.getElementById('panel-type').value
        if (panel_type != 'pivot_table') {
            $('.panel-filter-select').select2();
            $('.panel-filter-operator-select').select2();
            $('.select2-this').on('select2:select', function (e) {
                var selectElement = $(this).closest('select').get(0);
                selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
            });
            $(`#metric_category-${id}`).select2();
        } else {
            //name=panel-filter-select select2
            $('select[name="panel-filter-select"]').select2();
            $('select[name="panel-filter-select"]').on('select2:select', function (e) {
                var selectElement = $(this).closest('select').get(0);
                selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
            });

            $('select[name="panel-filter-operator"]').select2();
            $('select[name="panel-filter-operator"]').on('select2:select', function (e) {
                var selectElement = $(this).closest('select').get(0);
                selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
            });

        }

    }
</script>
