import ast
import json
import os
import traceback
import uuid

from asgiref.sync import async_to_sync
from django.core.files.base import ContentFile as DjangoContentFile
from django.core.mail import send_mail
from django.http import JsonResponse
from django.http.response import HttpResponse
from django.shortcuts import redirect, render
from django.template.loader import render_to_string
from django.utils import timezone
from django_hosts.resolvers import reverse

from action.action import trigger_next_action
from data.commerce.commerce_actions import import_stripe_invoices
from data.commerce.commerce_functions import (
    aggregate_commerce_action,
    send_emails_from_workflow,
)
from data.constants.constant import (
    INVOICE_USAGE_CATEGORY,
    ORDERS_COLUMNS_DISPLAY,
    USAGE_CATEGORIES_TITLE,
)
from data.constants.date_constant import DATE_FORMAT_PARSER
from data.constants.properties_constant import (
    OBJECT_TYPE_TO_SLUG,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_WORKFLOW,
)
from data.contacts import upload_wholesale_price
from data.contractwise import when_contract_completed
from data.custom_object.co_utility import import_custom_objects
from data.custom_pdf import generate_multiline_objs, get_custom_pdf_context
from data.estimate.create_dg_payment import create_dg_payment
from data.inventory.actions import when_inventory_transaction_created
from data.invoice.create_invoice_zengin_format import create_invoice_zengin_format
from data.items_inventory import (
    auto_inventory_email,
    create_inventory_transaction,
    sync_item_and_inventory,
    sync_item_shopify_webhook,
)
from data.contact.background.import_shopify_contacts import (
    ImportShopifyContactsPayload,
    import_shopify_contacts_task,
)
from data.messages.messages import import_messages
from data.models import (
    Action,
    ActionHistory,
    ActionNode,
    ActionTaskHistory,
    ActionTracker,
    AppSetting,
    Bill,
    Channel,
    Company,
    Contact,
    ContactsMappingFields,
    ContactsNameCustomField,
    DateFormat,
    GeneralStorage,
    Group,
    Invoice,
    InvoiceItem,
    Module,
    Notification,
    ORDERS_STATUS_DISPLAY,
    PdfTemplate,
    Projects,
    SHOPTURBO_ORDER_DELIVERY_STATUS,
    ShopTurboInventory,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsValueCustomField,
    ShopTurboOrders,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    ShopTurboSubscriptions,
    ShopTurboSubscriptionsNameCustomField,
    ShopTurboSubscriptionsValueCustomField,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_SUBSCRIPTION,
    Task,
    TaskCustomFieldName,
    TaskCustomFieldValue,
    TransferHistory,
    User,
    WorkflowActionTracker,
)
from data.orders.actions import convert_order_to_po, import_orders
from data.orders.orders import add_shipping_cost_to_order
from data.procurement import (
    create_new_purchase_order,
    export_purchase_order,
    purchase_order_to_inventory_form,
    when_purchase_item_updated,
)
from data.shopturbo import (
    change_hubspot_deal_property,
    change_hubspot_deal_stage,
    convert_order_to_estimate,
    export_order_to_next_engine,
    export_orders,
    generate_barcode_form,
    generate_invoice_from_subscription,
    generate_order_from_subscription,
    hubspot_upload_properties,
    import_items,
    import_multiple_items,
)
from data.workflow.workflow import export_invoice_to_hubspot_custom_object
from data.items_inventory import export_items
from data.subscriptions.actions import create_stripe_subscription_link
from utils.actions import transfer_output_to_target_input
from utils.bgjobs.runner import trigger_bg_job
from utils.bgjobs.handler import create_bg_job, add_hatchet_run_id
from data.workflow.background.run_workflow_action import (
    run_workflow_action_task,
    RunWorkflowActionPayload,
)
from utils.decorator import login_or_hubspot_required
from utils.discord import DiscordNotification
from utils.logger import logger
from utils.meter import has_quota
from utils.project import notif_assigned_assignee
from utils.seal_subscription import cancel_seal_supscription, import_seal_subscriptions
from utils.shopify_bg_job.shopify_orders import (
    parse_mapping,
    push_shopify_customer,
)
from utils.stripe.stripe import create_stripe_payment_link_for_order
from utils.stripe.stripe_subscription import expire_subscription_checkout_session
from utils.utility import get_redirect_workflow, get_workspace, is_valid_uuid
from utils.stripe.import_subscriptions import import_stripe_subscriptions

SHOPTURBO_APP_TARGET = "shopturbo"


def record_updated(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        if not action_slug:
            return HttpResponse(status=400)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        actions = [
            ("item_updated", "Item Record Updated", "商品レコードの更新"),
            ("order_updated", "Order Record Updated", "受注レコードの更新"),
            (
                "when_contract_completed",
                "Contract Record Updated",
                "契約レコードの更新",
            ),
            (
                "draft_to_active_subscriptions",
                "Subscription Record Updated",
                "サブスクリプションレコードの更新",
            ),
            ("invoice_updated", "Invoice Record Updated", "請求書レコードの更新"),
            ("inventory_less_than_x", "Inventory Record Updated", "在庫レコードの更新"),
            (
                "when_purchase_item_updated",
                "Purchase Order Record Updated",
                "発注レコードの更新",
            ),
            (
                "shopify_webhook",
                "Shopify Webhook Triggered",
                "Shopify Webhook Triggered",
            ),
        ]

        action_name = None
        if node and node.input_data:
            if "action_name" in node.input_data:
                action_name = node.input_data["action_name"]

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "action_node_id": action_node_id,
            "action_names": actions,
            "action_name": action_name,
        }

        if node.input_data:
            context["active_action"] = node.input_data.get("action_name", None)
        return render(request, "data/shopturbo/record-updated.html", context)

    # POST
    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    if submit_option != "save":
        return HttpResponse(status=400)

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index
    action_name = request.POST.get("action_name" + postfix)

    node.input_data = {"action_name": action_name}
    node.predefined_input = {}
    node.valid_to_run = True
    node.save()

    if action_name == "item_updated":
        return item_updated(request)
    elif action_name == "order_updated":
        return order_updated(request)
    elif action_name == "when_purchase_item_updated":
        return when_purchase_item_updated(request)
    elif action_name == "inventory_less_than_x":
        return inventory_less_than_x(request)
    else:
        return when_contract_completed(request)

    return HttpResponse()


def record_updated_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    action_name = request.GET.get("action_name" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if action_name == "item_updated":
        return item_updated(request)
    elif action_name == "order_updated":
        return order_updated(request)
    elif action_name == "when_purchase_item_updated":
        return when_purchase_item_updated(request)
    elif action_name == "inventory_less_than_x":
        return inventory_less_than_x(request)
    else:
        return when_contract_completed(request)

    return HttpResponse(status=200)


def item_updated(request):
    workspace = get_workspace(request.user)

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Performance optimization: Only load first 50 properties for initial render
        # The rest will be loaded via AJAX when user searches
        properties = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace
        ).order_by("name")[:50]

        context = {
            "action_slug": action_slug,
            "action_node_id": action_node_id,
            "properties": properties,
        }

        if node.input_data:
            context["active_key"] = node.input_data.get("property-key", None)
            context["active_value"] = node.input_data.get("property-value", None)

            # If there's an active key that's not in the first 50, load it specifically
            active_key = context["active_key"]
            if active_key and active_key != "name":
                try:
                    active_property = ShopTurboItemsNameCustomField.objects.get(
                        id=active_key, workspace=workspace
                    )
                    # Add the active property to the list if it's not already there
                    property_ids = [str(p.id) for p in properties]
                    if str(active_property.id) not in property_ids:
                        properties = list(properties) + [active_property]
                        context["properties"] = properties
                except ShopTurboItemsNameCustomField.DoesNotExist:
                    pass

        return render(request, "data/shopturbo/item-updated.html", context)

    # POST
    # Workflow related input
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index
    action_name = request.POST.get("action_name" + postfix)

    node.input_data = {}
    node.input_data["property-key"] = request.POST.get("property-key")
    node.input_data["property-value"] = request.POST.get("property-value")
    node.input_data["action_name"] = action_name
    node.predefined_input = {}
    node.valid_to_run = True
    node.save()
    return HttpResponse()


@login_or_hubspot_required
def search_item_properties(request):
    """AJAX endpoint for searching item properties for Select2"""
    if request.method != "GET":
        return JsonResponse({"error": "Method not allowed"}, status=405)

    workspace = get_workspace(request.user)
    search_term = request.GET.get("q", "").strip()
    page = int(request.GET.get("page", 1))
    page_size = 30

    # Build query
    properties_query = ShopTurboItemsNameCustomField.objects.filter(
        workspace=workspace
    ).order_by("name")

    if search_term:
        properties_query = properties_query.filter(name__icontains=search_term)

    # Paginate results
    start = (page - 1) * page_size
    end = start + page_size
    properties = properties_query[
        start : end + 1
    ]  # Get one extra to check if there are more

    # Check if there are more results
    has_more = len(properties) > page_size
    if has_more:
        properties = properties[:page_size]  # Remove the extra item

    # Format results for Select2
    results = []
    for prop in properties:
        results.append({"id": str(prop.id), "text": prop.name})

    return JsonResponse({"results": results, "pagination": {"more": has_more}})


def order_updated(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        statuses = [
            {
                "value": "all",
                "display": "すべてのステータス" if lang == "ja" else "All Statuses",
            }
        ] + [
            {
                "value": val,
                "display": ORDERS_STATUS_DISPLAY[val]["ja"]
                if lang == "ja"
                else ORDERS_STATUS_DISPLAY[val]["en"],
            }
            for val in ORDERS_STATUS_DISPLAY
        ]
        context = {
            "action_slug": action_slug,
            "action_node_id": action_node_id,
            "statuses": statuses,
            "dummy_order": ShopTurboOrders(workspace=workspace),
        }

        if node.input_data:
            context["active_status"] = node.input_data.get("order_status", None)
        return render(request, "data/shopturbo/order-updated.html", context)

    # POST
    # Workflow related input
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index
    action_name = request.POST.get("action_name" + postfix)

    node.input_data = {}
    node.input_data["order_status"] = request.POST.get("order_status")
    node.input_data["action_name"] = action_name
    node.predefined_input = {}
    node.valid_to_run = True
    node.save()
    return HttpResponse()


def inventory_less_than_x(request):
    workspace = get_workspace(request.user)

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        inventories = ShopTurboInventory.objects.filter(workspace=workspace)
        context = {
            "action_slug": action_slug,
            "action_node_id": action_node_id,
            "inventories": inventories,
        }

        if node.input_data:
            context["active_inventory"] = node.input_data.get("inventory", None)
            context["active_total"] = node.input_data.get("total", None)
        return render(request, "data/shopturbo/inventory-less-than-x.html", context)

    # POST
    # Workflow related input
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index
    action_name = request.POST.get("action_name" + postfix)

    node.input_data = {}
    node.input_data["inventory"] = request.POST.get("inventory")
    node.input_data["total"] = request.POST.get("total")
    node.input_data["action_name"] = action_name
    node.predefined_input = {}
    node.valid_to_run = True
    node.save()
    return HttpResponse()


def record_created(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        if not action_slug:
            return HttpResponse(status=400)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        actions = [
            (TYPE_OBJECT_SUBSCRIPTION, "Subscriptions", "サブスクリプション"),
            (TYPE_OBJECT_ORDER, "Orders", "受注"),
            (TYPE_OBJECT_INVENTORY_TRANSACTION, "Inventory Transaction", "入出庫"),
        ]

        action_name = None
        if node and node.input_data:
            if "action_name" in node.input_data:
                action_name = node.input_data["action_name"]

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "action_node_id": action_node_id,
            "action_names": actions,
            "action_name": action_name,
        }

        if node.input_data:
            context["active_action"] = node.input_data.get("action_name", None)
        return render(request, "data/shopturbo/record-created.html", context)

    # POST
    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    if submit_option != "save":
        return HttpResponse(status=400)

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index
    action_name = request.POST.get("action_name" + postfix)

    node.input_data = {"action_name": action_name}
    node.predefined_input = {}
    node.valid_to_run = True
    node.save()

    if action_name == TYPE_OBJECT_SUBSCRIPTION:
        return when_contract_completed(request)
    elif action_name == TYPE_OBJECT_INVENTORY_TRANSACTION:
        return when_inventory_transaction_created(request)
    else:
        return when_contract_completed(request)

    return HttpResponse()


def record_created_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    action_name = request.GET.get("action_name" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if action_name == "commerce_subscription":
        return when_contract_completed(request)
    elif action_name == TYPE_OBJECT_INVENTORY_TRANSACTION:
        return when_inventory_transaction_created(request)
    else:
        return when_contract_completed(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def create_record(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        object_type = None
        is_convert_record_action = False
        object_source = None
        object_target = None
        if postfix:
            if node and node.input_data:
                if "object_type" in node.input_data:
                    object_type = node.input_data["object_type"]
                if "is_convert_record_action" in node.input_data:
                    is_convert_record_action = node.input_data[
                        "is_convert_record_action"
                    ]
                if "object_source" in node.input_data:
                    object_source = node.input_data["object_source"]
                if "object_target" in node.input_data:
                    object_target = node.input_data["object_target"]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "object_type": object_type,
            "is_convert_record_action": is_convert_record_action,
            "object_source": object_source,
            "object_target": object_target,
            "node": node,
        }
        return render(request, "data/shopturbo/create-record.html", context)

    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    if at_id:
        try:
            ActionTracker.objects.get(id=at_id)
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    if wat_id:
        try:
            WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    object_type = request.POST.get("object_type" + postfix)
    object_name = request.POST.get("object_name" + postfix)
    is_convert_record_action = request.POST.get("is_convert_record_action" + postfix)

    if submit_option == "save":
        input_data = {}
        if object_type:
            input_data["object_type"] = object_type
        if object_name:
            input_data["object_name"] = object_name

        node.input_data = input_data
        node.save()
    else:
        try:
            if "is_convert_record_action" in node.input_data:
                is_convert_record_action = node.input_data["is_convert_record_action"]
            if "object_name" in node.input_data:
                object_name = node.input_data["object_name"]
        except:
            pass

    if is_convert_record_action:
        return convert_record(request)
    if object_name == "create_inventory_transaction":
        return create_inventory_transaction(request)
    elif object_name == "create_task":
        return create_task(request)
    elif object_name == "create_new_purchase_order":
        return create_new_purchase_order(request)
    elif object_name == "create_invoice":
        return create_invoice(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def create_record_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")
    object_type = request.GET.get("object_type")

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    action_names = []
    if object_type == "transaction":
        action_names = [
            (
                "create_inventory_transaction",
                "Create Inventory Transaction Record",
                "入出庫レコードの作成",
            )
        ]
    if object_type == "task":
        action_names = [("create_task", "Create Task Record", "タスクレコードの作成")]
    if object_type == "purchaseorder":
        action_names = [
            (
                "create_new_purchase_order",
                "Create Purchase Order Record",
                "発注レコードの作成",
            )
        ]
    if object_type == "invoice":
        action_names = [
            ("create_invoice", "Create Invoice Record", "売上請求レコードの作成")
        ]

    context = {
        "action_index": action_index,
        "action_node_id": action_node_id,
        "object_type": object_type,
        "action_names": action_names,
    }
    return render(request, "data/shopturbo/create-record-name.html", context)


@login_or_hubspot_required
def create_action_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    object_name = request.GET.get("object_name" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if object_name == "create_inventory_transaction":
        return create_inventory_transaction(request)
    elif object_name == "create_task":
        return create_task(request)
    elif object_name == "create_new_purchase_order":
        return create_new_purchase_order(request)
    elif object_name == "create_invoice":
        return create_invoice(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def create_task(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        active_title = None
        active_users = []
        active_start_date = None
        active_due_date = None
        active_description = None
        active_project = None
        active_status = None
        if node and node.input_data:
            if "title" in node.input_data:
                active_title = node.input_data["title"]
            if "users" in node.input_data:
                active_users = node.input_data["users"]
            if "start_date" in node.input_data:
                active_start_date = node.input_data["start_date"]
            if "due_date" in node.input_data:
                active_due_date = node.input_data["due_date"]
            if "description" in node.input_data:
                active_description = node.input_data["description"]
            if "project_target" in node.input_data:
                active_project = node.input_data["project_target"]
            if "status" in node.input_data:
                active_status = node.input_data["status"]

        members = workspace.user.all()
        projects = Group.objects.filter(workspace=workspace)
        project_targets = Projects.objects.filter(workspace=workspace)
        if lang == "ja":
            status_list = [("todo", "対応予定"), ("doing", "対応中"), ("done", "完了")]
        else:
            status_list = [
                ("todo", "To Do"),
                ("doing", "In Progress"),
                ("done", "Done"),
            ]

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "active_title": active_title,
            "active_users": active_users,
            "active_start_date": active_start_date,
            "active_due_date": active_due_date,
            "active_description": active_description,
            "active_project": active_project,
            "active_status": active_status,
            "members": members,
            "projects": projects,
            "project_targets": project_targets,
            "status_list": status_list,
        }
        print("context", node.input_data)

        return render(request, "data/shopturbo/create-task-action-form.html", context)

    # POST
    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    title = request.POST.get("title" + postfix)
    try:
        users = request.POST.getlist("users" + postfix)
    except:
        users = request.POST.get("users" + postfix)
    start_date = request.POST.get("start_date" + postfix)
    due_date = request.POST.get("due_date" + postfix)
    description = request.POST.get("task_desc" + postfix)
    project_target = request.POST.get("project_target" + postfix)
    status = request.POST.get("status" + postfix)

    object_type = request.POST.get("object_type" + postfix)
    object_name = request.POST.get("object_name" + postfix)

    if submit_option == "save":
        node.valid_to_run = True
        input_data = {}

        if title:
            input_data["title"] = title
        if users:
            input_data["users"] = []
            for user in users:
                if "user|" in user:
                    user = user.replace("user|", "")
                    input_data["users"].append(user)
                else:
                    user = user.replace("group|", "")
                    input_data["users"].append(user)
        if start_date:
            input_data["start_date"] = start_date
        if due_date:
            input_data["due_date"] = due_date
        if description:
            input_data["description"] = description
        if project_target:
            input_data["project_target"] = project_target
        if status:
            input_data["status"] = status

        if (
            not title
            or not users
            or not start_date
            or not due_date
            or not project_target
            or not status
        ):
            node.valid_to_run = False

        if object_type:
            input_data["object_type"] = object_type
        if object_name:
            input_data["object_name"] = object_name

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()
    else:
        if not node:
            return HttpResponse(status=400)

        if "title" in node.input_data:
            title = node.input_data["title"]
        if "users" in node.input_data:
            users = node.input_data["users"]
        if "start_date" in node.input_data:
            start_date = node.input_data["start_date"]
        if "due_date" in node.input_data:
            due_date = node.input_data["due_date"]
        if "description" in node.input_data:
            description = node.input_data["description"]
        if "project_target" in node.input_data:
            project_target = node.input_data["project_target"]
            project_target = Projects.objects.get(id=project_target)
        if "status" in node.input_data:
            status = node.input_data["status"]

        task = Task.objects.create(
            workspace=workspace, project_target=project_target, usage_status="active"
        )
        try:
            if title:
                task.title = title

            if start_date:
                task.start_date = start_date

            if due_date:
                task.due_date = due_date

            if description:
                task.description = description

            if status:
                task.status = status

                task_cf, _ = TaskCustomFieldName.objects.get_or_create(
                    workspace=task.workspace, name="Status"
                )
                task_cf_value, _ = TaskCustomFieldValue.objects.get_or_create(
                    field_name=task_cf, task=task
                )
                if status == "todo":
                    task_cf_value.value = 1
                elif status == "doing":
                    task_cf_value.value = 2
                elif status == "done":
                    task_cf_value.value = 3
                task_cf_value.save()

            for user in users:
                if is_valid_uuid(user):
                    try:
                        user = Group.objects.get(id=user)
                        task.project.add(user)
                        task.save()
                    except:
                        user = User.objects.get(username=user)
                        task.assignee.add(user)
                        task.save()
                        notif_assigned_assignee(task=task, assignee=user, lang=lang)
                else:
                    user = User.objects.get(username=user)
                    task.assignee.add(user)
                    task.save()
                    notif_assigned_assignee(task=task, assignee=user, lang=lang)
        except Exception:
            task.save()

        output_data = {"tasks": []}

        output_data["tasks"].append({"task_id": task.task_id})

        at.status = "success"
        at.output_data = output_data
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

        module_slug = request.POST.get("module")
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        if not module_slug:
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
        )


@login_or_hubspot_required
def update_record(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        object_type = None
        if postfix:
            if node and node.input_data:
                if "object_type" in node.input_data:
                    object_type = node.input_data["object_type"]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "object_type": object_type,
        }
        return render(request, "data/shopturbo/update-record.html", context)

    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    if at_id:
        try:
            ActionTracker.objects.get(id=at_id)
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    if wat_id:
        try:
            WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    object_type = request.POST.get("object_type" + postfix)

    if submit_option == "save":
        input_data = {}
        if object_type:
            input_data["object_type"] = object_type

        node.input_data = input_data
        node.save()
    else:
        try:
            object_type = node.input_data["object_type"]
        except:
            pass

    if object_type == "commerce_items":
        return update_item(request)
    elif object_type == "item_components":
        return update_item_component(request)
    elif object_type == "task":
        return update_task(request)
    elif object_type == "add_shipping_cost_to_order":
        return add_shipping_cost_to_order(request)
    elif object_type == "order_status":
        return update_order_status(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def update_record_name(request):
    action_node_id = request.GET.get("action_node_id")
    object_type = request.GET.get("object_type")

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if object_type == "commerce_items":
        return update_item(request)
    if object_type == "item_components":
        return update_item_component(request)
    if object_type == "task":
        return update_task(request)
    if object_type == "add_shipping_cost_to_order":
        return add_shipping_cost_to_order(request)
    if object_type == "order_status":
        return update_order_status(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def update_action_name(request):  # unused
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")
    object_type = request.GET.get("object_type")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    object_name = request.GET.get("object_name" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if object_name == "update_item":
        return update_item(request)
    elif object_type == "item_components":
        return update_item_component(request)
    elif object_name == "update_task":
        return update_task(request)
    elif object_name == "add_shipping_cost_to_order":
        return add_shipping_cost_to_order(request)
    elif object_name == "update_order_status":
        return update_order_status(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def update_item(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        object_type = None
        active_key = None
        active_value = None
        selected_item = None
        use_prev_data = None
        if postfix:
            if node and node.input_data:
                if "property-key" in node.input_data:
                    active_key = node.input_data["property-key"]
                if "property-value" in node.input_data:
                    active_value = node.input_data["property-value"]
                if "selected_item" in node.input_data:
                    selected_item = node.input_data["selected_item"]
                if "use_prev_data" in node.input_data:
                    use_prev_data = node.input_data["use_prev_data"]

        properties = ShopTurboItemsNameCustomField.objects.filter(workspace=workspace)
        items = ShopTurboItems.objects.filter(workspace=workspace)
        context = {
            "action_index": action_index,
            "active_key": active_key,
            "active_value": active_value,
            "properties": properties,
            "items": items,
            "selected_item": selected_item,
            "use_prev_data": use_prev_data,
        }
        return render(request, "data/shopturbo/update-item-component.html", context)

    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None

    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    property_key = request.POST.get("property-key" + postfix, "")
    property_value = request.POST.get("property-value" + postfix, "")
    use_prev_data = request.POST.get("use_prev_data" + postfix, "")
    try:
        selected_item = request.POST.getlist("item_id" + postfix, "")
    except:
        selected_item = request.POST.get("item_id" + postfix, "")

    object_type = request.POST.get("object_type" + postfix, "")
    object_name = request.POST.get("object_name" + postfix, "")

    try:
        item_id = at.input_data["item_id"]
    except:
        item_id = None

    # POST
    print(request.POST)
    print("Running Update Item")
    try:
        if submit_option == "save":
            input_data = {}
            node.valid_to_run = True

            if property_key:
                input_data["property-key"] = property_key
            if not property_key:
                node.valid_to_run = False
            if property_value:
                input_data["property-value"] = property_value
            if not property_value:
                node.valid_to_run = False

            if use_prev_data:
                input_data["use_prev_data"] = use_prev_data

            input_data["selected_item"] = selected_item

            if object_type:
                input_data["object_type"] = object_type
            if object_name:
                input_data["object_name"] = object_name

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            print(node.valid_to_run)
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module_slug:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        else:
            property_key = node.input_data.get("property-key")
            property_value = node.input_data.get("property-value")
            use_prev_data = node.input_data.get("use_prev_data")
            item_ids = node.input_data.get("selected_item")

            if not use_prev_data:
                for item_id in item_ids:
                    item = ShopTurboItems.objects.get(id=item_id)
                    property_to_update = ShopTurboItemsNameCustomField.objects.filter(
                        id=property_key
                    ).first()

                    item_property_to_update, _ = (
                        ShopTurboItemsValueCustomField.objects.get_or_create(
                            field_name=property_to_update, items=item
                        )
                    )
                    item_property_to_update.value = property_value
                    item_property_to_update.save()
            else:
                # item_ids coming from order
                if "order" in at.input_data:
                    order_id = at.input_data["order"]["order_id"]
                    order = ShopTurboOrders.objects.get(
                        order_id=order_id, workspace=workspace
                    )
                    for item in order.shopturboitemsorders_set.all():
                        if item.item:
                            property_to_update = (
                                ShopTurboItemsNameCustomField.objects.filter(
                                    id=property_key
                                ).first()
                            )

                            item_property_to_update, _ = (
                                ShopTurboItemsValueCustomField.objects.get_or_create(
                                    field_name=property_to_update, items=item.item
                                )
                            )
                            item_property_to_update.value = property_value
                            item_property_to_update.save()

                if item_id:
                    item = ShopTurboItems.objects.get(id=item_id)
                    property_to_update = ShopTurboItemsNameCustomField.objects.filter(
                        id=property_key
                    ).first()

                    item_property_to_update, _ = (
                        ShopTurboItemsValueCustomField.objects.get_or_create(
                            field_name=property_to_update, items=item
                        )
                    )
                    item_property_to_update.value = property_value
                    item_property_to_update.save()

    except Exception:
        traceback.print_exc()
        return HttpResponse(status=500)

    if submit_option == "run":
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug
        at.status = "success"
        at.completed_at = timezone.now()
        at.save()

        next_node = None
        if node:
            next_node = node.next_node
            at = ActionTracker.objects.filter(id=at.id).first()
        if next_node:
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )
        return redirect(reverse("main", host="app"))
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def update_item_component(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        object_type = None
        active_key = None
        active_value = None
        selected_item = None
        use_prev_data = None
        if postfix:
            if node and node.input_data:
                if "property-key" in node.input_data:
                    active_key = node.input_data["property-key"]
                if "property-value" in node.input_data:
                    active_value = node.input_data["property-value"]
                if "selected_item" in node.input_data:
                    selected_item = node.input_data["selected_item"]
                if "use_prev_data" in node.input_data:
                    use_prev_data = node.input_data["use_prev_data"]

        properties = ShopTurboItemsNameCustomField.objects.filter(workspace=workspace)
        items = ShopTurboItems.objects.filter(workspace=workspace)
        context = {
            "action_index": action_index,
            "active_key": active_key,
            "active_value": active_value,
            "properties": properties,
            "items": items,
            "selected_item": selected_item,
            "use_prev_data": use_prev_data,
        }
        return render(request, "data/shopturbo/update-item-component.html", context)

    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None

    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    property_key = request.POST.get("property-key" + postfix, "")
    property_value = request.POST.get("property-value" + postfix, "")
    use_prev_data = request.POST.get("use_prev_data" + postfix, "")
    try:
        selected_item = request.POST.getlist("item_id" + postfix, "")
    except:
        selected_item = request.POST.get("item_id" + postfix, "")

    object_type = request.POST.get("object_type" + postfix, "")
    object_name = request.POST.get("object_name" + postfix, "")

    try:
        item_id = at.input_data["item_id"]
    except:
        item_id = None

    # POST
    print(request.POST)
    print("Running Update Item Component")
    try:
        if submit_option == "save":
            input_data = {}
            node.valid_to_run = True

            if property_key:
                input_data["property-key"] = property_key
            if not property_key:
                node.valid_to_run = False
            if property_value:
                input_data["property-value"] = property_value
            if not property_value:
                node.valid_to_run = False

            if use_prev_data:
                input_data["use_prev_data"] = use_prev_data

            input_data["selected_item"] = selected_item

            if object_type:
                input_data["object_type"] = object_type
            if object_name:
                input_data["object_name"] = object_name

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            print(node.valid_to_run)
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module_slug:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        else:
            property_key = node.input_data.get("property-key")
            property_value = node.input_data.get("property-value")
            use_prev_data = node.input_data.get("use_prev_data")
            item_ids = node.input_data.get("selected_item")

            if not use_prev_data:
                for item_id in item_ids:
                    item = ShopTurboItems.objects.get(id=item_id)
                    property_to_update = ShopTurboItemsNameCustomField.objects.filter(
                        id=property_key
                    ).first()

                    component_property = ShopTurboItemsNameCustomField.objects.filter(
                        workspace=workspace, type="components"
                    ).first()

                    item_component_ids = ShopTurboItemsValueCustomField.objects.filter(
                        items=item, field_name=component_property
                    ).values_list("components__item_component__id", flat=True)

                    print(
                        "logit",
                        property_to_update,
                        property_value,
                        item_id,
                        item_component_ids,
                    )
                    for item_component_id in item_component_ids:
                        item_component = ShopTurboItems.objects.get(
                            id=item_component_id
                        )
                        item_property_to_update, _ = (
                            ShopTurboItemsValueCustomField.objects.get_or_create(
                                field_name=property_to_update, items=item_component
                            )
                        )
                        item_property_to_update.value = property_value
                        item_property_to_update.save()
            else:
                # item_ids coming from order
                if "order" in at.input_data:
                    order_id = at.input_data["order"]["order_id"]
                    order = ShopTurboOrders.objects.get(
                        order_id=order_id, workspace=workspace
                    )
                    for item in order.shopturboitemsorders_set.all():
                        if item.item:
                            property_to_update = (
                                ShopTurboItemsNameCustomField.objects.filter(
                                    id=property_key
                                ).first()
                            )

                            component_property = (
                                ShopTurboItemsNameCustomField.objects.filter(
                                    workspace=workspace, type="components"
                                ).first()
                            )

                            item_component_ids = (
                                ShopTurboItemsValueCustomField.objects.filter(
                                    items=item.item, field_name=component_property
                                ).values_list(
                                    "components__item_component__id", flat=True
                                )
                            )

                            print(
                                "logit",
                                property_to_update,
                                property_value,
                                item_id,
                                item_component_ids,
                            )
                            for item_component_id in item_component_ids:
                                item_component = ShopTurboItems.objects.get(
                                    id=item_component_id
                                )
                                item_property_to_update, _ = (
                                    ShopTurboItemsValueCustomField.objects.get_or_create(
                                        field_name=property_to_update,
                                        items=item_component,
                                    )
                                )
                                item_property_to_update.value = property_value
                                item_property_to_update.save()

                if item_id:
                    item = ShopTurboItems.objects.get(id=item_id)
                    property_to_update = ShopTurboItemsNameCustomField.objects.filter(
                        id=property_key
                    ).first()

                    component_property = ShopTurboItemsNameCustomField.objects.filter(
                        workspace=workspace, type="components"
                    ).first()

                    item_component_ids = ShopTurboItemsValueCustomField.objects.filter(
                        items=item, field_name=component_property
                    ).values_list("components__item_component__id", flat=True)

                    print(
                        "logit",
                        property_to_update,
                        property_value,
                        item_id,
                        item_component_ids,
                    )
                    for item_component_id in item_component_ids:
                        item_component = ShopTurboItems.objects.get(
                            id=item_component_id
                        )
                        item_property_to_update, _ = (
                            ShopTurboItemsValueCustomField.objects.get_or_create(
                                field_name=property_to_update, items=item_component
                            )
                        )
                        item_property_to_update.value = property_value
                        item_property_to_update.save()

    except Exception:
        traceback.print_exc()
        return HttpResponse(status=500)

    if submit_option == "run":
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug
        at.status = "success"
        at.completed_at = timezone.now()
        at.save()

        next_node = None
        if node:
            next_node = node.next_node
            at = ActionTracker.objects.filter(id=at.id).first()
        if next_node:
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )
        return redirect(reverse("main", host="app"))
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def update_task(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        object_type = None
        active_status = None
        selected_task = None
        if postfix:
            if node and node.input_data:
                if "status" in node.input_data:
                    active_status = node.input_data["status"]
                if "selected_task" in node.input_data:
                    selected_task = node.input_data["selected_task"]

        properties = ShopTurboItemsNameCustomField.objects.filter(workspace=workspace)
        if lang == "ja":
            status_list = [("todo", "対応予定"), ("doing", "対応中"), ("done", "完了")]
        else:
            status_list = [
                ("todo", "To Do"),
                ("doing", "In Progress"),
                ("done", "Done"),
            ]

        tasks = Task.objects.filter(workspace=workspace)
        context = {
            "action_index": action_index,
            "active_status": active_status,
            "status_list": status_list,
            "properties": properties,
            "tasks": tasks,
            "selected_task": selected_task,
        }

        return render(request, "data/shopturbo/update-task-status.html", context)

    # Workflow related input
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None

    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    status = request.POST.get("status" + postfix, "")

    object_type = request.POST.get("object_type" + postfix, "")
    object_name = request.POST.get("object_name" + postfix, "")
    try:
        selected_task = request.POST.getlist("task_id" + postfix, "")
    except:
        selected_task = request.POST.get("task_id" + postfix, "")

    # POST
    print(request.POST)
    print("Running Change Task Status")
    try:
        if submit_option == "save":
            input_data = {}
            node.valid_to_run = True

            if status:
                input_data["status"] = status
            if not status:
                node.valid_to_run = False

            if object_type:
                input_data["object_type"] = object_type
            if object_name:
                input_data["object_name"] = object_name
            if selected_task:
                input_data["selected_task"] = selected_task

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            print(node.valid_to_run)
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module_slug:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        else:
            status = node.input_data.get("status")
            selected_tasks = node.input_data.get("selected_task")

            for task in selected_tasks:
                task = Task.objects.get(id=task)
                task.status = status
                task.save()

                task_cf, _ = TaskCustomFieldName.objects.get_or_create(
                    workspace=task.workspace, name="Status"
                )
                task_cf_value, _ = TaskCustomFieldValue.objects.get_or_create(
                    field_name=task_cf, task=task
                )
                if status == "todo":
                    task_cf_value.value = 1
                elif status == "doing":
                    task_cf_value.value = 2
                elif status == "done":
                    task_cf_value.value = 3
                task_cf_value.save()

            wat.workflow_history.status = status
            wat.workflow_history.save()

            at.status = "success"
            at.completed_at = timezone.now()
            at.save()

    except Exception:
        traceback.print_exc()
        return HttpResponse(status=500)

    if submit_option == "run":
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug
        at.status = "success"
        at.completed_at = timezone.now()
        at.save()

        next_node = None
        if node:
            next_node = node.next_node
            at = ActionTracker.objects.filter(id=at.id).first()
        if next_node:
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )
        return redirect(reverse("main", host="app"))
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def convert_record(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        object_source = None
        object_target = None
        if postfix:
            if node and node.input_data:
                if "object_source" in node.input_data:
                    object_source = node.input_data["object_source"]
                if "object_target" in node.input_data:
                    object_target = node.input_data["object_target"]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "object_source": object_source,
            "object_target": object_target,
        }
        return render(request, "data/shopturbo/convert-record.html", context)

    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    if at_id:
        try:
            ActionTracker.objects.get(id=at_id)
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    if wat_id:
        try:
            WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    object_source = request.POST.get("object_source" + postfix)
    object_target = request.POST.get("object_target" + postfix)

    if submit_option == "save":
        input_data = {}
        if object_source:
            input_data["object_source"] = object_source
        if object_target:
            input_data["object_target"] = object_target

        node.input_data = input_data
        node.save()
    else:
        try:
            object_source = node.input_data["object_source"]
            object_target = node.input_data["object_target"]
        except:
            pass

    if object_source == "commerce_orders" and object_target == "estimates":
        return convert_order_to_estimate(request)
    if object_source == "commerce_orders" and object_target == "purchaseorder":
        return convert_order_to_po(request)
    elif object_source == "subscriptions" and object_target == "invoices":
        return generate_invoice_from_subscription(request)
    elif object_source == "subscriptions" and object_target == "commerce_orders":
        return generate_order_from_subscription(request)
    elif object_source == "purchaseorder" and object_target == "commerce_inventory":
        return purchase_order_to_inventory_form(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def convert_record_target(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")
    object_source = request.GET.get("object_source")
    object_target = request.GET.get("object_target")

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    object_targets = []
    if object_source == "commerce_orders":
        object_targets = [
            ("estimates", "Estimates", "見積書"),
            ("purchaseorder", "Purchase Orders", "発注"),
        ]
    elif object_source == "subscriptions":
        object_targets = [
            ("invoices", "Invoices", "売上請求"),
            ("commerce_orders", "Orders", "受注"),
        ]
    elif object_source == "purchaseorder":
        object_targets = [("commerce_inventory", "Inventory", "在庫")]

    context = {
        "action_index": action_index,
        "action_node_id": action_node_id,
        "object_source": object_source,
        "object_target": object_target,
        "object_targets": object_targets,
    }
    return render(request, "data/shopturbo/convert-record-target.html", context)


@login_or_hubspot_required
def convert_action_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")
    object_source = request.GET.get("object_source")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    object_target = request.GET.get("object_target" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if object_source == "commerce_orders" and object_target == "estimates":
        return convert_order_to_estimate(request)
    if object_source == "commerce_orders" and object_target == "purchaseorder":
        return convert_order_to_po(request)
    elif object_source == "subscriptions" and object_target == "invoices":
        return generate_invoice_from_subscription(request)
    elif object_source == "subscriptions" and object_target == "commerce_orders":
        return generate_order_from_subscription(request)
    elif object_source == "purchaseorder" and object_target == "commerce_inventory":
        return purchase_order_to_inventory_form(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def aggregate_record(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        object_type = None
        object_name = None
        if postfix:
            if node and node.input_data:
                if "object_type" in node.input_data:
                    object_type = node.input_data["object_type"]
                if "object_name" in node.input_data:
                    object_name = node.input_data["object_name"]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "object_type": object_type,
            "object_name": object_name,
        }
        return render(request, "data/shopturbo/aggregate-record.html", context)

    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    if at_id:
        try:
            ActionTracker.objects.get(id=at_id)
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    if wat_id:
        try:
            WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    object_type = request.POST.get("object_type" + postfix)
    object_name = request.POST.get("object_name" + postfix)

    if submit_option == "save":
        input_data = {}
        if object_type:
            input_data["object_type"] = object_type
        if object_name:
            input_data["object_name"] = object_name

        node.input_data = input_data
        node.save()
    else:
        try:
            object_name = node.input_data["object_name"]
        except:
            pass

    if object_name == "aggregate_order_to_invoice":
        return aggregate_commerce_action(
            request, TYPE_OBJECT_ORDER, convert_to=TYPE_OBJECT_INVOICE
        )
    elif object_name == "aggregate_order_to_purchase_order":
        return aggregate_commerce_action(
            request, TYPE_OBJECT_ORDER, convert_to=TYPE_OBJECT_PURCHASE_ORDER
        )
    elif object_name == "aggregate_estimate":
        return aggregate_commerce_action(request, TYPE_OBJECT_ESTIMATE)
    elif object_name == "aggregate_invoice":
        return aggregate_commerce_action(request, TYPE_OBJECT_INVOICE)
    elif object_name == "aggregate_delivery_note_to_invoice":
        return aggregate_commerce_action(request, TYPE_OBJECT_DELIVERY_NOTE)

    return HttpResponse(status=200)


@login_or_hubspot_required
def aggregate_record_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")
    object_type = request.GET.get("object_type")
    object_name = request.GET.get("object_name")

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    action_names = []
    if object_type == "commerce_orders":
        action_names = [
            (
                "aggregate_order_to_invoice",
                "Aggregate Order Records to Invoice Records",
                "受注レコードを売上請求レコードにまとめる",
            ),
            (
                "aggregate_order_to_purchase_order",
                "Aggregate Order Records to Purchase Order Records",
                "受注レコードを発注レコードにまとめる",
            ),
        ]
    elif object_type == "estimates":
        action_names = [
            (
                "aggregate_estimate",
                "Aggregate Estimates for a Customer",
                "顧客の見積書をまとめる",
            )
        ]
    elif object_type == "invoices":
        action_names = [
            (
                "aggregate_invoice",
                "Aggregate Invoices for a Customer",
                "顧客の請求書をまとめる",
            )
        ]
    elif object_type == "delivery_slips":
        action_names = [
            (
                "aggregate_delivery_note_to_invoice",
                "Aggregate Delivery Note Records to Invoice Records",
                "納品レコードを売上請求レコードにまとめる",
            )
        ]

    context = {
        "action_index": action_index,
        "action_node_id": action_node_id,
        "object_type": object_type,
        "action_names": action_names,
        "object_name": object_name,
    }
    return render(request, "data/shopturbo/aggregate-record-name.html", context)


@login_or_hubspot_required
def aggregate_action_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    object_name = request.GET.get("object_name" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if object_name == "aggregate_order_to_invoice":
        return aggregate_commerce_action(
            request, TYPE_OBJECT_ORDER, convert_to=TYPE_OBJECT_INVOICE
        )
    elif object_name == "aggregate_order_to_purchase_order":
        return aggregate_commerce_action(
            request, TYPE_OBJECT_ORDER, convert_to=TYPE_OBJECT_PURCHASE_ORDER
        )
    elif object_name == "aggregate_estimate":
        return aggregate_commerce_action(request, TYPE_OBJECT_ESTIMATE)
    elif object_name == "aggregate_invoice":
        return aggregate_commerce_action(request, TYPE_OBJECT_INVOICE)
    elif object_name == "aggregate_delivery_note_to_invoice":
        return aggregate_commerce_action(request, TYPE_OBJECT_DELIVERY_NOTE)

    return HttpResponse(status=200)


@login_or_hubspot_required
def create_invoice(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Status options
        if lang == "ja":
            status_list = [
                ("draft", "下書き"),
                ("sent", "送信済み"),
                ("scheduled", "スケジュール済み"),
                ("paid", "支払済み"),
            ]
        else:
            status_list = [
                ("draft", "Draft"),
                ("sent", "Sent"),
                ("scheduled", "Scheduled"),
                ("paid", "Paid"),
            ]

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "input_data": node.input_data,
            "status_list": status_list,
        }

        return render(
            request, "data/shopturbo/create-invoice-action-form.html", context
        )

    # POST
    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    status = request.POST.get("status" + postfix, None)
    issuing_date = request.POST.get("issuing_date" + postfix, None)
    payment_due_date = request.POST.get("payment_due_date" + postfix, None)
    contact_company = request.POST.get("contact_and_company" + postfix, None)
    currency = request.POST.get("currency" + postfix, None)
    tax_rate = request.POST.get("tax_rate" + postfix, None)

    send_from = request.POST.get("send_from" + postfix, None)
    notes = request.POST.get("notes" + postfix, None)

    object_type = request.POST.get("object_type" + postfix)
    object_name = request.POST.get("object_name" + postfix)

    if submit_option == "save":
        try:
            # Items Information
            items = request.POST.getlist("item" + postfix, [])
            tax_item_rate = request.POST.getlist("tax_item_rate" + postfix, [])
            item_amount = request.POST.getlist("item_amount" + postfix, [])
            amount_price = request.POST.getlist("amount" + postfix, [])
            items_status = request.POST.getlist("item_status" + postfix, [])

            poi_id = []
            item = []
            item_status = []
            amount = []
            item_id = []
            price = []
            tax = []
            items_show = None

            if items:
                try:
                    for idx, item_data in enumerate(items):
                        if "|" in item_data:
                            item.append("")
                            item_id.append(str(item_data.split("|")[2]))
                        else:
                            item.append(item_data)
                            item_id.append("")

                        if item_amount:
                            amount.append(float(item_amount[idx]))
                        else:
                            amount.append(float(0))
                        if amount_price:
                            price.append(float(amount_price[idx]))
                        else:
                            price.append(float(0))
                        if tax_item_rate:
                            tax.append(float(tax_item_rate[idx]))
                        else:
                            tax.append(None)

                        if items_status:
                            item_status.append(items_status[idx])
                        else:
                            item_status.append("")
                        poi_id.append("")
                    items_show = list(
                        zip(poi_id, item_id, item, amount, price, tax, status)
                    )
                except Exception as e:
                    print(e)
        except Exception:
            print(
                "[DEBUG] Skip this input as it is read from html, Workflow do not have this input"
            )
            pass

        if submit_option == "save":
            node.valid_to_run = True

            input_data = {
                "status": status,
                "issuing_date": issuing_date,
                "payment_due_date": payment_due_date,
                "contact_company": contact_company,
                "tax_rate": tax_rate,
                "currency": currency,
                "send_from": send_from,
                "notes": notes,
                "items_show": items_show,
            }

            if object_type:
                input_data["object_type"] = object_type
            if object_name:
                input_data["object_name"] = object_name

            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            return HttpResponse(status=200)
    else:
        if not node:
            return HttpResponse(status=400)

        status = node.input_data.get("status", None)
        issuing_date = node.input_data.get("issuing_date", None)
        payment_due_date = node.input_data.get("payment_due_date", None)
        contact_company = node.input_data.get("contact_company", None)
        currency = node.input_data.get("currency", None)
        tax_rate = node.input_data.get("tax_rate", None)
        send_from = node.input_data.get("send_from", None)
        notes = node.input_data.get("notes", None)
        items_show = node.input_data.get("items_show", None)

        if not has_quota(workspace, INVOICE_USAGE_CATEGORY):
            entries_name = USAGE_CATEGORIES_TITLE[INVOICE_USAGE_CATEGORY]["en"]
            msg = f"{entries_name}, could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing {entries_name} to free up space."
            if lang == "ja":
                entries_name = USAGE_CATEGORIES_TITLE[INVOICE_USAGE_CATEGORY]["ja"]
                msg = f"{entries_name},制限を超えたため作成できませんでした。サブスクリプション プランをアップグレードしてクォータを増やすか、既存の {entries_name} の一部をアーカイブしてスペースを解放します。"
            Notification.objects.create(
                workspace=workspace, user=request.user, message=msg, type="error"
            )
            return_fn = get_redirect_workflow(
                submit_option, node, workspace, at, wat, status="failed"
            )
            next_node = None
            if node:
                next_node = node.next_node
                at = ActionTracker.objects.filter(id=at.id).first()
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )
            return redirect(return_fn)

        invoice_obj = Invoice.objects.create(
            workspace=workspace,
            currency=currency,
            status=status,
            usage_status="active",
            total_price_without_tax=0,
            total_price=0,
        )

        for item_data in items_show:
            (
                dummy_poi_id,
                item_id,
                item_name,
                item_amount,
                item_price,
                tax_item_rate,
                item_status,
            ) = item_data

            if item_id:
                item = ShopTurboItems.objects.filter(
                    workspace=workspace, id=item_id
                ).first()
                if not item:
                    item = None
            else:
                item = None

            item_price = float(item_price or 0)
            item_amount = float(item_amount or 0)
            tax_item_rate = float(tax_item_rate or 0)

            InvoiceItem.objects.create(
                invoice=invoice_obj,
                item_name=item_name,
                item_link=item,
                amount_price=item_price,
                amount_item=item_amount,
                tax_rate=tax_item_rate,
            )

            invoice_obj.total_price_without_tax = (
                invoice_obj.total_price_without_tax + item_price * item_amount
            )
            if tax_item_rate:
                invoice_obj.total_price = invoice_obj.total_price_without_tax + (
                    invoice_obj.total_price_without_tax * tax_item_rate / 100
                )
                invoice_obj.save()
            else:
                invoice_obj.total_price = invoice_obj.total_price_without_tax
                invoice_obj.save()

        if contact_company:
            contact = Contact.objects.filter(
                workspace=workspace, id=contact_company
            ).first()
            if contact:
                invoice_obj.contact = contact
                invoice_obj.save()

            company = Company.objects.filter(
                workspace=workspace, id=contact_company
            ).first()
            if company:
                invoice_obj.company = company
                invoice_obj.save()

        invoice_obj.start_date = issuing_date
        invoice_obj.due_date = payment_due_date
        invoice_obj.tax_rate = tax_rate
        invoice_obj.send_from = send_from
        invoice_obj.notes = notes
        invoice_obj.save(
            log_data={"user": request.user, "workspace": workspace, "status": "create"}
        )

        # Create output data for next node
        output_data = {"invoices": []}
        output_data["invoices"].append({"invoice_id": str(invoice_obj.id)})

        at.status = "success"
        at.output_data = output_data
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

        module_slug = request.POST.get("module")
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        if not module_slug:
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
        )


@login_or_hubspot_required  # NOTE: Applied on Workflow as well
def create_pickup_list_from_order_form(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        module_slug = request.GET.get("module")
        view_id = request.GET.get("view_id")

        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)
        if postfix:
            order_ids = request.GET.getlist("order_ids" + postfix, None)
        else:
            order_ids = request.GET.get("order_ids")

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        if postfix:
            if node and node.input_data:
                if "order_ids" in node.input_data:
                    order_ids = node.input_data["order_ids"]

        list_template = []
        DIRECTORY = "data/templates/data/shopturbo/orders/pdf"
        NEXT_DIRECTORY = "data/shopturbo/orders/pdf/"
        # Loop through all files in the directory
        for filename in os.listdir(DIRECTORY):
            # Check if the file is an HTML file and contains 'pdf_pattern' in its name
            if filename.startswith("orderPDF-pattern") and filename.endswith(".html"):
                list_template.append(NEXT_DIRECTORY + filename)

        pdf_templates = PdfTemplate.objects.filter(
            workspace=workspace, master_pdf__object_type=TYPE_OBJECT_ORDER
        ).order_by("master_pdf__name_en", "master_pdf__name_ja")

        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=workspace, app_target="shopturbo"
        )
        context = {
            "bulk_action": request.GET.get("bulk_action"),
            "action_slug": action_slug,
            "action_index": action_index,
            "order_ids": order_ids,
            "is_record_action": request.GET.get("is_record_action"),
            "module_slug": module_slug,
            "view_id": view_id,
            "list_template": list_template,
            "pdf_templates": pdf_templates,
            "app_setting": app_setting,
        }
        return render(
            request, "data/purchase_order/create-picking-list-from-order.html", context
        )

    # Workflow related input
    module_slug = request.POST.get("module_slug")

    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    action_slug = request.POST.get("action" + postfix)
    is_record_action = request.POST.get("is_record_action")
    bulk_action = request.POST.get("bulk_action")

    # create pickup list related input
    template_id = request.POST.get("template_select")
    order_ids = request.POST.getlist("order_ids" + postfix)

    if submit_option == "save":
        node.valid_to_run = True
        if not order_ids:
            node.valid_to_run = False
        input_data = {
            "order_ids": order_ids,
        }

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()

        return HttpResponse()
    else:
        if submit_option == "run":
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
            redirect_url = (
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

            order_ids = node.input_data["order_ids"]
        else:
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_ORDER]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_ORDER
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
            if bulk_action:
                redirect_url = (
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + "?open_drawer=action_drawer_history"
                )
            else:
                redirect_url = (
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={order_ids[0]}&target={TYPE_OBJECT_ORDER}&sidedrawer=action-history"
                )
            if not order_ids:
                print("Order ID is required.")
                return redirect(redirect_url)

        history = None
        orders = ShopTurboOrders.objects.filter(workspace=workspace, id__in=order_ids)
        if not orders:
            print("Order does not exist")
            return redirect(redirect_url)

        if bulk_action or is_record_action:
            action = Action.objects.filter(slug=action_slug).first()
            history = ActionHistory.objects.create(
                workspace=workspace,
                action=action,
                status="initialized",
                created_by=request.user,
            )

        if is_record_action:
            history.object_id = order_ids[0]
            history.object_type = TYPE_OBJECT_ORDER
            history.save()

        try:
            app_setting = AppSetting.objects.get(
                workspace=workspace, app_target=SHOPTURBO_APP_TARGET
            )
        except AppSetting.DoesNotExist:
            if is_record_action or bulk_action:
                history.completed_at = timezone.now()
                history.status = "failed"
                history.save()

                task_action_history = ActionTaskHistory.objects.create(
                    workspace=history.workspace,
                    action_history=history,
                    status="failed",
                    error_message="Order's slip template is not set.",
                    error_message_ja="受注の帳票テンプレートが設定されていません。",
                )

            if submit_option == "run":
                at.status = "failed"
                at.completed_at = timezone.now()
                at.save()
                wat.status = "failed"
                wat.save()
            return redirect(redirect_url)
        if not is_valid_uuid(template_id) and template_id is None:
            template_id = app_setting.order_pdf_template

        raw_html_content = ""
        landscape = False
        if not is_valid_uuid(template_id):
            order_pdf_line_item_table = (
                app_setting.order_pdf_line_item_table.split(",")
                if app_setting.order_pdf_line_item_table
                else []
            )
            order_pdf_header_block = (
                app_setting.order_pdf_header_block.split(",")
                if app_setting.order_pdf_header_block
                else []
            )
            order_pdf_payment_block = app_setting.order_pdf_payment_block
            order_pdf_send_from_block = app_setting.order_pdf_send_from_block
            order_pdf_send_to_block = app_setting.order_pdf_send_to_block
            order_pdf_notes_block = app_setting.order_pdf_notes_block
            order_pdf_title = app_setting.order_pdf_title
            order_pdf_font_type = app_setting.order_pdf_font_type
            order_pdf_bg_color = app_setting.order_pdf_color

            order_pdf_line_item_table_display = (
                app_setting.order_pdf_line_item_table_display
            )
            order_pdf_header_block_display = app_setting.order_pdf_header_block_display
            order_pdf_payment_block_display = (
                app_setting.order_pdf_payment_block_display
            )
            order_pdf_send_from_block_display = (
                app_setting.order_pdf_send_from_block_display
            )
            order_pdf_send_to_block_display = (
                app_setting.order_pdf_send_to_block_display
            )
            order_pdf_notes_block_display = app_setting.order_pdf_notes_block_display
            order_pdf_line_item_table_bg_color = (
                app_setting.order_pdf_line_item_table_bg_color
            )
            order_pdf_line_item_table_font_color = (
                app_setting.order_pdf_line_item_table_font_color
            )

            if order_pdf_line_item_table_display:
                base_val = order_pdf_line_item_table
                custom_trans = order_pdf_line_item_table_display.split(",")
                order_pdf_line_item_table_display = {}

                for i in range(len(base_val)):
                    if len(custom_trans) <= i:
                        continue

                    if not custom_trans[i]:
                        continue

                    key = base_val[i]
                    if not key:
                        continue

                    order_pdf_line_item_table_display[key] = custom_trans[i]

            if order_pdf_header_block_display:
                base_val = order_pdf_header_block
                custom_trans = order_pdf_header_block_display.split(",")
                order_pdf_header_block_display = {}

                for i in range(len(base_val)):
                    if len(custom_trans) <= i:
                        continue

                    if not custom_trans[i]:
                        continue

                    key = base_val[i]
                    if not key:
                        continue

                    order_pdf_header_block_display[key] = custom_trans[i]

            if order_pdf_payment_block_display:
                custom_trans = order_pdf_payment_block_display
                order_pdf_payment_block_display = {}
                order_pdf_payment_block_display[order_pdf_payment_block] = custom_trans

            if order_pdf_send_from_block_display:
                custom_trans = order_pdf_send_from_block_display
                order_pdf_send_from_block_display = {}
                order_pdf_send_from_block_display[order_pdf_send_from_block] = (
                    custom_trans
                )

            if order_pdf_send_to_block_display:
                custom_trans = order_pdf_send_to_block_display
                order_pdf_send_to_block_display = {}
                order_pdf_send_to_block_display[order_pdf_send_to_block] = custom_trans

            if order_pdf_notes_block_display:
                custom_trans = order_pdf_notes_block_display
                order_pdf_notes_block_display = {}
                order_pdf_notes_block_display[order_pdf_notes_block] = custom_trans

            context = {
                "workspace": workspace,
                "is_preview": False,
                "lang": request.LANGUAGE_CODE,
                "pdf_line_item_table": order_pdf_line_item_table,
                "pdf_header_block": order_pdf_header_block,
                "pdf_payment_block": order_pdf_payment_block,
                "pdf_send_from_block": order_pdf_send_from_block,
                "pdf_send_to_block": order_pdf_send_to_block,
                "pdf_notes_block": order_pdf_notes_block,
                "obj_trans": ORDERS_COLUMNS_DISPLAY,
                "custom_model": "ShopTurboOrdersNameCustomField",
                "main_model": "ShopTurboOrders",
                "logo_url": app_setting.order_logo_file.url
                if app_setting.order_logo_file
                else "",
                "stamp_url": app_setting.order_stamp_file.url
                if app_setting.order_stamp_file
                else "",
                "pdf_title": order_pdf_title,
                "pdf_font_type": order_pdf_font_type,
                "pdf_bg_color": order_pdf_bg_color,
                "pdf_line_item_table_display": order_pdf_line_item_table_display,
                "pdf_header_block_display": order_pdf_header_block_display,
                "pdf_payment_block_display": order_pdf_payment_block_display,
                "pdf_send_from_block_display": order_pdf_send_from_block_display,
                "pdf_send_to_block_display": order_pdf_send_to_block_display,
                "pdf_notes_block_display": order_pdf_notes_block_display,
                "pdf_line_item_table_bg_color": order_pdf_line_item_table_bg_color,
                "pdf_line_item_table_font_color": order_pdf_line_item_table_font_color,
            }
            try:
                raw_html_content = render_to_string(template_id, context)
                if "orderPDF-pattern-5" in template_id:
                    landscape = True
            except:
                if is_record_action or bulk_action:
                    history.completed_at = timezone.now()
                    history.status = "failed"
                    history.save()

                    task_action_history = ActionTaskHistory.objects.create(
                        workspace=history.workspace,
                        action_history=history,
                        status="failed",
                        error_message="Order's statement template is not set.",
                        error_message_ja="受注の帳票テンプレートが設定されていません。",
                    )

                if submit_option == "run":
                    at.status = "failed"
                    at.completed_at = timezone.now()
                    at.save()
                    wat.status = "failed"
                    wat.save()
                return redirect(redirect_url)
        else:
            template = PdfTemplate.objects.filter(
                workspace=workspace, id=template_id
            ).first()
            if not template:
                template = PdfTemplate.objects.filter(
                    workspace=workspace,
                    master_pdf__object_type=TYPE_OBJECT_ORDER,
                    master_pdf__path__contains="pattern-1",
                ).first()
            template_id = template.master_pdf.path

            context = get_custom_pdf_context(template.id, TYPE_OBJECT_ORDER)
            context["logo_url"] = (
                app_setting.order_logo_file.url if app_setting.order_logo_file else ""
            )
            context["stamp_url"] = (
                app_setting.order_stamp_file.url if app_setting.order_stamp_file else ""
            )
            context["workspace"] = workspace
            context["lang"] = request.LANGUAGE_CODE
            raw_html_content = render_to_string(template_id, context)

        if is_record_action or bulk_action:
            task_action_history = ActionTaskHistory.objects.create(
                workspace=history.workspace,
                action_history=history,
                status="running",
                input_data={
                    "order_ids": order_ids,
                },
            )
        custom_field_obj = {}
        df_object = DateFormat.objects.filter(
            workspace=workspace, is_workspace_level=True
        ).first()

        # item need to be filled with
        # item.id, item.name, item.description, quantity, unit_price, total
        items = {}
        # Prefetch related items and custom fields in a single query
        orders = orders.prefetch_related(
            "shopturboitemsorders_set",
            "shopturboitemsorders_set__item",
            "shopturbo_custom_field_relations",
            "shopturbo_custom_field_relations__field_name",
        )

        # Pre-fetch related objects that will be needed for custom fields
        custom_field_values = {
            "user": {
                str(u.id): u.get_full_name()
                for u in User.objects.filter(
                    id__in=[
                        cf.value
                        for order in orders
                        for cf in order.shopturbo_custom_field_relations.all()
                        if cf.field_name and cf.field_name.type == "user" and cf.value
                    ]
                )
            },
            "bill_objects": {
                str(b.id): f"{int(b.id_bill):04d}"
                for b in Bill.objects.filter(
                    id__in=[
                        cf.value
                        for order in orders
                        for cf in order.shopturbo_custom_field_relations.all()
                        if cf.field_name
                        and cf.field_name.type == "bill_objects"
                        and cf.value
                    ]
                )
            },
            "invoice_objects": {
                str(i.id): f"{int(i.id_inv):04d}"
                for i in Invoice.objects.filter(
                    id__in=[
                        cf.value
                        for order in orders
                        for cf in order.shopturbo_custom_field_relations.all()
                        if cf.field_name
                        and cf.field_name.type == "invoice_objects"
                        and cf.value
                    ]
                )
            },
            "order_objects": {
                str(o.id): f"{int(o.order_id):04d}"
                for o in ShopTurboOrders.objects.filter(
                    id__in=[
                        cf.value
                        for order in orders
                        for cf in order.shopturbo_custom_field_relations.all()
                        if cf.field_name
                        and cf.field_name.type == "order_objects"
                        and cf.value
                    ]
                )
            },
            "contact": {
                str(c.id): f"{c.last_name} {c.name}"
                for c in Contact.objects.filter(
                    id__in=[
                        cf.value
                        for order in orders
                        for cf in order.shopturbo_custom_field_relations.all()
                        if cf.field_name
                        and cf.field_name.type == "contact"
                        and cf.value
                    ]
                )
            },
            "company": {
                str(c.id): c.name
                for c in Company.objects.filter(
                    id__in=[
                        cf.value
                        for order in orders
                        for cf in order.shopturbo_custom_field_relations.all()
                        if cf.field_name
                        and cf.field_name.type == "company"
                        and cf.value
                    ]
                )
            },
        }

        # Process orders and their items
        for order in orders:
            # Process items
            order_items = []
            for item in order.shopturboitemsorders_set.all():
                qty = (
                    int(item.number_item)
                    if item.number_item.is_integer()
                    else item.number_item
                )
                unit_price = item.item_price_order if item.item_price_order else 0
                total_price = float(unit_price) * float(item.number_item)

                # Format prices based on currency
                price_format = (
                    "{:,.0f}"
                    if order.currency and order.currency.upper() == "JPY"
                    else "{:,.2f}"
                )
                order_items.append(
                    {
                        "id": item.item.item_id if item.item else "",
                        "name": item.item.name if item.item else item.custom_item_name,
                        "description": item.item.description if item.item else "",
                        "quantity": qty,
                        "unit_price": price_format.format(unit_price),
                        "tax": 0,  # TODO: Cannot use tax in order
                        "total": price_format.format(total_price),
                    }
                )
            items[order.id] = order_items

            # Process custom fields
            custom_field_obj[order.id] = {}
            for custom_field in order.shopturbo_custom_field_relations.all():
                if not custom_field.field_name:
                    continue

                field_id = str(custom_field.field_name.id)
                field_type = custom_field.field_name.type
                value = custom_field.value

                if field_type == "choice":
                    json_string = custom_field.field_name.choice_value.replace("'", '"')
                    data = json.loads(json_string)
                    value_to_label = {item["value"]: item["label"] for item in data}
                    custom_field_obj[order.id][field_id] = value_to_label.get(value, "")

                elif field_type in ["date", "date_time"]:
                    if custom_field.value_time:
                        date_format = (
                            df_object.value
                            if (df_object and df_object.value is not None)
                            else "DD/MM/YYYY"
                        )
                        date_format = DATE_FORMAT_PARSER[df_object.value]
                        if field_type == "date_time":
                            date_format = f"{date_format} %H:%M"
                        date = custom_field.value_time.strftime(date_format)
                        custom_field_obj[order.id][field_id] = (
                            date.replace("x", "年")
                            .replace("y", "月")
                            .replace("z", "日")
                        )
                    else:
                        custom_field_obj[order.id][field_id] = ""

                elif field_type == "image":
                    custom_field_obj[order.id][field_id] = f"<div>{value}</div>"

                elif field_type in custom_field_values:
                    custom_field_obj[order.id][field_id] = custom_field_values[
                        field_type
                    ].get(value, "")

                else:
                    custom_field_obj[order.id][field_id] = value
        if is_record_action or bulk_action:
            pdf_bytes = async_to_sync(generate_multiline_objs)(
                request,
                template_id,
                orders,
                items,
                custom_field_obj,
                workspace=workspace,
                as_http_response=False,
                raw_html_content=raw_html_content,
                setting_type=TYPE_OBJECT_ORDER,
                footer_template=app_setting.order_pdf_footer_template,
                landscape=landscape,
            )
            res_file = DjangoContentFile(pdf_bytes)
            if lang == "ja":
                res_file.name = "ピッキングリスト" + "." + str(uuid.uuid4()) + ".pdf"
            else:
                res_file.name = "Picking List" + "." + str(uuid.uuid4()) + ".pdf"
            task_action_history.result_file = res_file
            task_action_history.status = "success"
            task_action_history.completed_at = timezone.now()
            task_action_history.save()

            history.completed_at = timezone.now()
            history.status = "success"
            history.save()

        if submit_option == "run" or not (is_record_action or bulk_action):
            return async_to_sync(generate_multiline_objs)(
                request,
                template_id,
                orders,
                items,
                custom_field_obj,
                workspace=workspace,
                raw_html_content=raw_html_content,
                setting_type=TYPE_OBJECT_ORDER,
                footer_template=app_setting.order_pdf_footer_template,
                landscape=landscape,
            )

        return redirect(redirect_url)


@login_or_hubspot_required
def export_data_to_integration(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        action_name = None
        if postfix:
            if node and node.input_data:
                if "action_name" in node.input_data:
                    action_name = node.input_data["action_name"]
        action_names = [
            (
                "create_stripe_payment_link",
                "Create Stripe Payment Link",
                "Stripe 決済リンクの作成",
            ),
            (
                "create_stripe_subscription_link",
                "Create Stripe Subscription Link",
                "Stripe サブスクリプションリンクの作成",
            ),
            ("create_dg_payment", "Create DGFT Payment Link", "DGFT決済リンク作成"),
            (
                "create_invoice_zengin_format",
                "Export Invoice Records in Zengin Format",
                "売上請求レコードを全銀形式でエクスポート",
            ),
            ("export_purchase_order", "Export Purchase Order", "発注をエクスポート"),
            (
                "change_hubspot_deal_stage",
                "Change Hubspot Deal Stage",
                "Hubspot取引ステージを変更",
            ),
            (
                "change_hubspot_deal_property",
                "Change Hubspot Deal Property",
                "Hubspot取引プロパティを変更する",
            ),
            (
                "hubspot_upload_properties",
                "Bulk Upload Hubspot Properties",
                "Hubspotのプロパティを一括でアップロード",
            ),
            (
                "upload_file_to_google_drive",
                "Upload File to Google Drive",
                "Google Driveにファイルをアップロード",
            ),
            (
                "cancel_stripe_subscription",
                "Cancel Stripe Subscription Based on Sanka Subscription",
                "Sankaサブスクリプションに基づいてStripeサブスクリプションをキャンセル",
            ),
            (
                "mark_subscription_order_paid",
                "Mark Subscription Order Payment as Paid",
                "サブスクリプションを支払済みとして更新",
            ),
            ("mapping_customers", "Mapping Customers", "顧客のマッピング"),
            ("export_customers", "Export Customers", "顧客をエクスポート"),
            (
                "export_order_to_next_engine",
                "Export Order to Next Engine",
                "Next Engineに受注レコードをエクスポート",
            ),
            ("export_orders", "Export Orders", "受注レコードをエクスポート"),
            ("export_items", "Export Items", "商品をエクスポート"),
            (
                "export_invoice_to_hubspot_custom_object",
                "Export Invoice To Hubspot Custom Object",
                "Hubspotカスタムオブジェクトに請求書をエクスポートします",
            ),
        ]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_name": action_name,
            "action_names": action_names,
        }
        return render(
            request, "data/shopturbo/export-data-to-integration.html", context
        )

    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    if at_id:
        try:
            ActionTracker.objects.get(id=at_id)
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    if wat_id:
        try:
            WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {}
        if action_name:
            input_data["action_name"] = action_name

        node.input_data = input_data
        node.save()
    else:
        try:
            action_name = node.input_data["action_name"]
        except:
            pass

    if action_name == "create_stripe_payment_link":
        return create_stripe_payment_link(request)
    elif action_name == "create_stripe_subscription_link":
        return create_stripe_subscription_link(request)
    elif action_name == "create_dg_payment":
        return create_dg_payment(request)
    elif action_name == "create_invoice_zengin_format":
        return create_invoice_zengin_format(request)
    elif action_name == "export_purchase_order":
        return export_purchase_order(request)
    elif action_name == "change_hubspot_deal_stage":
        return change_hubspot_deal_stage(request)
    elif action_name == "change_hubspot_deal_property":
        return change_hubspot_deal_property(request)
    elif action_name == "hubspot_upload_properties":
        return hubspot_upload_properties(request)
    elif action_name == "upload_file_to_google_drive":
        return upload_file_to_google_drive(request)
    elif action_name == "cancel_stripe_subscription":
        return cancel_stripe_subscription(request)
    elif action_name == "mark_subscription_order_paid":
        return mark_subscription_order_paid(request)
    elif action_name == "mapping_customers":
        return mapping_customers(request)
    elif action_name == "export_customers":
        return export_customers(request)
    elif action_name == "export_order_to_next_engine":
        return export_order_to_next_engine(request)
    elif action_name == "export_orders":
        return export_orders(request)
    elif action_name == "export_items":
        return export_items(request)
    elif action_name == "export_invoice_to_hubspot_custom_object":
        return export_invoice_to_hubspot_custom_object(request)
    return HttpResponse(status=200)


@login_or_hubspot_required
def export_integration_action_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    action_name = request.GET.get("action_name" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if action_name == "create_stripe_payment_link":
        return create_stripe_payment_link(request)
    elif action_name == "create_stripe_subscription_link":
        return create_stripe_subscription_link(request)
    elif action_name == "create_dg_payment":
        return create_dg_payment(request)
    elif action_name == "create_invoice_zengin_format":
        return create_invoice_zengin_format(request)
    elif action_name == "export_purchase_order":
        return export_purchase_order(request)
    elif action_name == "change_hubspot_deal_stage":
        return change_hubspot_deal_stage(request)
    elif action_name == "change_hubspot_deal_property":
        return change_hubspot_deal_property(request)
    elif action_name == "hubspot_upload_properties":
        return hubspot_upload_properties(request)
    elif action_name == "upload_file_to_google_drive":
        return upload_file_to_google_drive(request)
    elif action_name == "cancel_stripe_subscription":
        return cancel_stripe_subscription(request)
    elif action_name == "mark_subscription_order_paid":
        return mark_subscription_order_paid(request)
    elif action_name == "mapping_customers":
        return mapping_customers(request)
    elif action_name == "export_customers":
        return export_customers(request)
    elif action_name == "export_order_to_next_engine":
        return export_order_to_next_engine(request)
    elif action_name == "export_orders":
        return export_orders(request)
    elif action_name == "export_items":
        return export_items(request)
    elif action_name == "export_invoice_to_hubspot_custom_object":
        return export_invoice_to_hubspot_custom_object(request)
    return HttpResponse(status=200)


@login_or_hubspot_required
def create_stripe_payment_link(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_order_id = None
        active_stripe_payment_integration = None
        active_stripe_link_field = None
        active_postal_code_field = None
        active_add_shipping_fee = None

        if node and node.input_data:
            if "order_id" in node.input_data:
                active_order_id = node.input_data["order_id"]
            if "stripe_payment_integration" in node.input_data:
                active_stripe_payment_integration = node.input_data[
                    "stripe_payment_integration"
                ]
            if "stripe_link_field" in node.input_data:
                active_stripe_link_field = node.input_data["stripe_link_field"]
            if "postal_code_field" in node.input_data:
                active_postal_code_field = node.input_data["postal_code_field"]
            if "add_shipping_fee" in node.input_data:
                active_add_shipping_fee = node.input_data["add_shipping_fee"]

        # Get data for form dropdowns
        orders = ShopTurboOrders.objects.filter(workspace=workspace)

        # Get Stripe integrations
        stripe_integrations = Channel.objects.filter(
            workspace=workspace, integration__slug="stripe"
        )

        # Get text fields available for storing links
        order_text_fields = ShopTurboOrdersNameCustomField.objects.filter(
            workspace=workspace, type__in=["text", "url", "url_redirect"]
        )

        # Get text fields for postal code
        contact_text_fields = ContactsNameCustomField.objects.filter(
            workspace=workspace, type__in=["text", "postal_code"]
        )

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_order_id": active_order_id,
            "active_stripe_payment_integration": active_stripe_payment_integration,
            "active_stripe_link_field": active_stripe_link_field,
            "active_postal_code_field": active_postal_code_field,
            "active_add_shipping_fee": active_add_shipping_fee,
            "orders": orders,
            "stripe_integrations": stripe_integrations,
            "order_text_fields": order_text_fields,
            "contact_text_fields": contact_text_fields,
        }

        return render(
            request, "data/shopturbo/create-stripe-payment-link-form.html", context
        )

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    order_id = request.POST.get("order_id" + postfix)
    stripe_payment_integration = request.POST.get(
        "stripe_payment_integration" + postfix
    )
    stripe_link_field = request.POST.get("stripe_link_field" + postfix)
    postal_code_field = request.POST.get("postal_code_field" + postfix)
    add_shipping_fee = request.POST.get("add_shipping_fee" + postfix)

    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        node.valid_to_run = True
        input_data = {}

        if order_id:
            input_data["order_id"] = order_id
        else:
            node.valid_to_run = False

        if stripe_payment_integration:
            input_data["stripe_payment_integration"] = stripe_payment_integration
        else:
            node.valid_to_run = False

        if stripe_link_field:
            input_data["stripe_link_field"] = stripe_link_field
        else:
            node.valid_to_run = False

        if postal_code_field:
            input_data["postal_code_field"] = postal_code_field

        if add_shipping_fee:
            input_data["add_shipping_fee"] = add_shipping_fee

        if action_name:
            input_data["action_name"] = action_name

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()
    else:
        if not node:
            return HttpResponse(status=400)

        # Extract values from node input data if running the action
        order_id = node.input_data.get("order_id", "")
        stripe_payment_integration = node.input_data.get(
            "stripe_payment_integration", ""
        )
        stripe_link_field = node.input_data.get("stripe_link_field", "")
        postal_code_field = node.input_data.get("postal_code_field", "")
        add_shipping_fee = node.input_data.get("add_shipping_fee", "")

        try:
            # Get the order object
            order = ShopTurboOrders.objects.get(id=order_id, workspace=workspace)

            allow_shipping_fee = True
            if add_shipping_fee and str(add_shipping_fee).lower() == "false":
                allow_shipping_fee = False

            # Create Stripe payment link
            stripe_payment_link_result = create_stripe_payment_link_for_order(
                stripe_payment_integration,
                order.id,
                postal_code_field,
                allow_shipping_fee,
                lang=lang,
            )

            if (
                not stripe_payment_link_result
                or "url" not in stripe_payment_link_result
            ):
                at.status = "failed"
                at.completed_at = timezone.now()
                at.save()

                wat.status = "failed"
                wat.save()

                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message="Failed to create Stripe payment link"
                    if lang == "en"
                    else "Stripe決済リンクの作成に失敗しました",
                    type="error",
                )

                return HttpResponse(status=500)

            stripe_payment_link = stripe_payment_link_result.get("url", "")

            # Save the link to the specified field
            if stripe_link_field:
                field = ShopTurboOrdersNameCustomField.objects.get(id=stripe_link_field)
                field_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                    field_name=field, orders=order
                )
                field_value.value = stripe_payment_link
                field_value.save()

            output_data = {
                "order_id": str(order.order_id),
                "stripe_payment_link": stripe_payment_link,
            }

            # Update action tracker
            at.status = "success"
            at.output_data = output_data
            at.completed_at = timezone.now()
            at.save()

            wat.status = "success"
            wat.save()

            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

            # Redirect to workflow page
            module_slug = request.POST.get("module")
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_WORKFLOW,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

        except Exception as e:
            print(f"Error creating Stripe payment link: {str(e)}")
            at.status = "failed"
            at.completed_at = timezone.now()
            at.save()

            wat.status = "failed"
            wat.save()

            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Failed to create Stripe payment link: {str(e)}"
                if lang == "en"
                else f"Stripe決済リンクの作成に失敗しました: {str(e)}",
                type="error",
            )

            return HttpResponse(status=500)


@login_or_hubspot_required
def upload_file_to_google_drive(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_pdf_file_id = None
        active_category = None

        if node and node.input_data:
            if "pdf_file_id" in node.input_data:
                active_pdf_file_id = node.input_data["pdf_file_id"]
            if "category" in node.input_data:
                active_category = node.input_data["category"]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_pdf_file_id": active_pdf_file_id,
            "active_category": active_category,
        }

        return render(
            request, "data/shopturbo/upload-file-to-google-drive-form.html", context
        )

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    pdf_file_id = request.POST.get("pdf_file_id" + postfix)
    category = request.POST.get("category" + postfix)
    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        node.valid_to_run = True
        input_data = {}

        if pdf_file_id:
            input_data["pdf_file_id"] = pdf_file_id
        else:
            node.valid_to_run = False

        if category:
            input_data["category"] = category
        else:
            node.valid_to_run = False

        if action_name:
            input_data["action_name"] = action_name

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()
    else:
        if not node:
            return HttpResponse(status=400)

        # Extract values from node input data if running the action
        pdf_file_id = node.input_data.get("pdf_file_id", "")
        category = node.input_data.get("category", "")

        try:
            # Get the PDF file
            pdf_file = GeneralStorage.objects.get(id=pdf_file_id)

            # Simulate upload to Google Drive (actual implementation would use the Google API)
            # For now, we'll just create a successful output

            output_data = {
                "file_url": f"https://drive.google.com/simulated-file-{pdf_file.id}",
                "category": category,
                "file_name": pdf_file.file.name.split("/")[-1],
            }

            # Update action tracker
            at.status = "success"
            at.output_data = output_data
            at.completed_at = timezone.now()
            at.save()

            wat.status = "success"
            wat.save()

            # Create notification
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="File successfully uploaded to Google Drive"
                if lang == "en"
                else "ファイルがGoogle Driveにアップロードされました",
                type="success",
            )

            # Trigger next action if available
            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

            # Redirect to workflow page
            module_slug = request.POST.get("module")
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_WORKFLOW,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

        except Exception as e:
            print(f"Error uploading file to Google Drive: {str(e)}")
            at.status = "failed"
            at.completed_at = timezone.now()
            at.save()

            wat.status = "failed"
            wat.save()

            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Failed to upload file to Google Drive: {str(e)}"
                if lang == "en"
                else f"Google Driveへのファイルのアップロードに失敗しました: {str(e)}",
                type="error",
            )

            return HttpResponse(status=500)


@login_or_hubspot_required
def cancel_stripe_subscription(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_stripe_link_id_field = None

        if node and node.input_data:
            active_stripe_link_id_field = node.input_data.get(
                "stripe_link_id_field", None
            )

        # Get text fields available for storing Stripe subscription IDs
        subscription_text_fields = ShopTurboSubscriptionsNameCustomField.objects.filter(
            workspace=workspace, type__in=["text", "url", "url_redirect"]
        )

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_stripe_link_id_field": active_stripe_link_id_field,
            "subscription_text_fields": subscription_text_fields,
        }

        return render(
            request, "data/shopturbo/cancel-stripe-subscription-form.html", context
        )

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    stripe_link_id_field = request.POST.get("stripe_link_id_field" + postfix)
    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {
            "stripe_link_id_field": stripe_link_id_field,
            "action_name": action_name,
        }
        if stripe_link_id_field:
            node.valid_to_run = True
        node.input_data = input_data
        node.save()
        return HttpResponse(status=200)
    else:
        stripe_link_id_field = node.input_data.get("stripe_link_id_field", None)

    # Execute the action
    if not at or not node:
        return HttpResponse(status=400)

    try:
        if not stripe_link_id_field:
            at.status = "failed"
            at.completed_at = timezone.now()
            at.save()

            if wat:
                wat.status = "failed"
                wat.save()

            return HttpResponse(status=400)

        subscriptions = ShopTurboSubscriptions.objects.filter(workspace=workspace)
        custom_field = ShopTurboSubscriptionsNameCustomField.objects.get(
            id=stripe_link_id_field
        )
        for subscription in subscriptions:
            stripe_link_id_value, _ = (
                ShopTurboSubscriptionsValueCustomField.objects.get_or_create(
                    field_name=custom_field, subscriptions=subscription
                )
            )
            if subscription.status != "active" or (
                subscription.subscription_status
                and subscription.subscription_status != "active"
            ):
                if stripe_link_id_value.value:
                    expire_subscription_checkout_session(
                        workspace.id, stripe_link_id_value.value, subscription.id
                    )
                if subscription.status != "active":
                    status = "cancel"
                else:
                    status = subscription.subscription_status
                cancel_seal_supscription(subscription.id, status)

        at.status = "success"
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        # If this is part of a workflow, process the next node
        if wat:
            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

        return HttpResponse(status=200)

    except Exception as e:
        print(f"Error canceling Seal subscription: {e}")
        at.status = "failed"
        at.completed_at = timezone.now()
        at.save()

        if wat:
            wat.status = "failed"
            wat.save()

        return HttpResponse(status=500)


@login_or_hubspot_required
def mark_subscription_order_paid(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_channel_integration = None
        active_order_tag = None

        if node and node.input_data:
            active_channel_integration = node.input_data.get(
                "channel_integration", None
            )
            active_order_tag = node.input_data.get("order_tag", None)

        # Get available Shopify channels
        channels = Channel.objects.filter(
            workspace=workspace, integration__slug="shopify"
        )

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_channel_integration": active_channel_integration,
            "active_order_tag": active_order_tag,
            "channels": channels,
        }

        return render(
            request, "data/shopturbo/mark-subscription-order-paid-form.html", context
        )

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    channel_integration = request.POST.get("channel_integration" + postfix)
    order_tag = request.POST.get("order_tag" + postfix)
    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {
            "channel_integration": channel_integration,
            "order_tag": order_tag,
            "action_name": action_name,
        }
        node.valid_to_run = True
        if not channel_integration or not order_tag:
            node.valid_to_run = False
        node.input_data = input_data
        node.save()
        return HttpResponse(status=200)

    # Execute the action
    if not at or not node:
        return HttpResponse(status=400)

    try:
        channel_integration = node.input_data.get("channel_integration", None)
        order_tag = node.input_data.get("order_tag", None)

        if not channel_integration or not order_tag:
            at.status = "failed"
            at.completed_at = timezone.now()
            at.save()

            if wat:
                wat.status = "failed"
                wat.save()

            return HttpResponse(status=400)

        channel = Channel.objects.get(id=channel_integration)

        # Only process if it's a Shopify channel
        if channel.integration.slug == "shopify":
            api_key = channel.api_key
            access_token = channel.access_token
            shop_name = channel.account_id

            # Get orders from Shopify with the specified tag
            from utils.shopify_bg_job.shopify_orders import (
                get_shopify_orders,
                mark_order_as_paid,
            )

            all_orders = get_shopify_orders(api_key, access_token, shop_name)
            filtered_orders = [order for order in all_orders if order.tags == order_tag]

            count = 0
            for order in filtered_orders:
                # Skip orders that are already paid or completed
                if (
                    hasattr(order, "financial_status")
                    and order.financial_status == "paid"
                ):
                    continue
                if hasattr(order, "status") and order.status == "completed":
                    continue

                # Mark the order as paid
                mark_order_as_paid(api_key, access_token, shop_name, order.id)
                count += 1

            # Update the output data with the results
            output_data = {
                "subscriptions": {
                    "subscription_id": order_tag,
                    "marked_as_paid_count": count,
                }
            }

            at.status = "success"
            at.output_data = output_data
            at.completed_at = timezone.now()
            at.save()

            wat.status = "success"
            wat.save()
        else:
            at.status = "failed"
            if lang == "ja":
                at.input_data["error_messages"] = (
                    "このアクションは現在Shopifyのみサポートしています"
                )
            else:
                at.input_data["error_messages"] = (
                    "This action currently only supports Shopify"
                )
            at.completed_at = timezone.now()
            at.save()

            if wat:
                wat.status = "failed"
                wat.save()
            return HttpResponse(status=400)

        # If this is part of a workflow, process the next node
        if wat:
            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

        return HttpResponse(status=200)

    except Exception as e:
        print(f"Error marking subscription orders as paid: {e}")
        at.status = "failed"
        at.completed_at = timezone.now()
        at.save()

        if wat:
            wat.status = "failed"
            wat.save()

        return HttpResponse(status=500)


@login_or_hubspot_required
def import_subscriptions(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_channel_integration = None

        if node and node.input_data:
            active_channel_integration = node.input_data.get(
                "channel_integration", None
            )

        channels = Channel.objects.filter(
            workspace=workspace, integration__slug__in=["seal-subscription", "stripe"]
        )

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_channel_integration": active_channel_integration,
            "channels": channels,
        }

        return render(request, "data/shopturbo/import-subscriptions-form.html", context)

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    channel_integration = request.POST.get("channel_integration" + postfix)
    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {
            "channel_integration": channel_integration,
            "action_name": action_name,
        }
        node.valid_to_run = True
        if not channel_integration:
            node.valid_to_run = False
        node.input_data = input_data
        node.save()
        return HttpResponse(status=200)

    # Execute the action
    if not at or not node:
        return HttpResponse(status=400)

    try:
        channel_integration = node.input_data.get("channel_integration", None)

        if not channel_integration:
            at.status = "failed"
            at.completed_at = timezone.now()
            at.save()

            if wat:
                wat.status = "failed"
                wat.save()

            return HttpResponse(status=400)

        channel = Channel.objects.get(id=channel_integration)

        # Import the subscriptions using the utility function
        if channel.integration.slug == "seal-subscription":
            import_seal_subscriptions(channel.id)
        elif channel.integration.slug == "stripe":
            import_stripe_subscriptions(
                channel, request.user, lang=request.LANGUAGE_CODE
            )

        # Update output data with results
        output_data = {"channel_name": channel.name}

        at.status = "success"
        at.output_data = output_data
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        # If this is part of a workflow, process the next node
        if wat:
            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

        # Redirect to workflow page if appropriate
        if "run" in submit_option and node and node.workflow_history:
            module_slug = request.POST.get("module")
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_WORKFLOW,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

        return HttpResponse(status=200)

    except Exception as e:
        print(f"Error importing subscriptions: {e}")
        at.status = "failed"
        at.completed_at = timezone.now()
        at.save()

        if wat:
            wat.status = "failed"
            wat.save()

        # Create error notification
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message=f"Failed to import subscriptions: {str(e)}"
            if lang == "en"
            else f"サブスクリプションのインポートに失敗しました: {str(e)}",
            type="error",
        )

        return HttpResponse(status=500)


@login_or_hubspot_required
def mapping_customers(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_first_name = None
        active_last_name = None
        active_email = None
        active_phone = None
        active_address1 = None
        active_address2 = None
        active_country = None
        active_province = None
        active_city = None
        active_zip = None

        if node and node.input_data:
            active_first_name = node.input_data.get("first_name", None)
            active_last_name = node.input_data.get("last_name", None)
            active_email = node.input_data.get("email", None)
            active_phone = node.input_data.get("phone", None)
            active_address1 = node.input_data.get("address1", None)
            active_address2 = node.input_data.get("address2", None)
            active_country = node.input_data.get("country", None)
            active_province = node.input_data.get("province", None)
            active_city = node.input_data.get("city", None)
            active_zip = node.input_data.get("zip", None)

        # Create field options for dropdowns
        field_options = [
            {"slug": "", "display": ""},
            {"slug": "name", "display": "Name" if lang == "en" else "名"},
            {"slug": "last_name", "display": "Last Name" if lang == "en" else "姓"},
            {"slug": "email", "display": "Email" if lang == "en" else "メール"},
            {
                "slug": "phone_number",
                "display": "Phone Number" if lang == "en" else "電話番号",
            },
            {
                "slug": "address1",
                "display": "Address Line 1" if lang == "en" else "住所1",
            },
            {
                "slug": "address2",
                "display": "Address Line 2" if lang == "en" else "住所2",
            },
            {"slug": "country", "display": "Country" if lang == "en" else "国"},
            {
                "slug": "province",
                "display": "Province/State" if lang == "en" else "都道府県",
            },
            {"slug": "city", "display": "City" if lang == "en" else "市区町村"},
            {
                "slug": "postal_code",
                "display": "Postal Code" if lang == "en" else "郵便番号",
            },
        ]

        # Add custom fields from ContactsNameCustomField
        custom_fields = (
            ContactsNameCustomField.objects.filter(workspace=workspace)
            .exclude(type__in=["image", "user", "formula"])
            .values_list("id", "name")
        )

        for field_id, field_name in custom_fields:
            field_options.append({"slug": f"custom_{field_id}", "display": field_name})

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_email": active_email,
            "active_first_name": active_first_name,
            "active_last_name": active_last_name,
            "active_phone": active_phone,
            "active_address1": active_address1,
            "active_address2": active_address2,
            "active_country": active_country,
            "active_province": active_province,
            "active_city": active_city,
            "active_zip": active_zip,
            "field_options": field_options,
        }

        return render(request, "data/shopturbo/mapping-customers-form.html", context)

    # POST handling stays mostly the same
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    first_name = request.POST.get("first_name" + postfix)
    last_name = request.POST.get("last_name" + postfix)
    email = request.POST.get("email" + postfix)
    phone = request.POST.get("phone" + postfix)
    address1 = request.POST.get("address1" + postfix)
    address2 = request.POST.get("address2" + postfix)
    country = request.POST.get("country" + postfix)
    province = request.POST.get("province" + postfix)
    city = request.POST.get("city" + postfix)
    zip_code = request.POST.get("zip" + postfix)

    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {
            "first_name": first_name,
            "last_name": last_name,
            "email": email,
            "phone": phone,
            "address1": address1,
            "address2": address2,
            "country": country,
            "province": province,
            "city": city,
            "zip": zip_code,
            "action_name": action_name,
        }

        node.valid_to_run = True
        # Make required fields validation
        if not email:
            node.valid_to_run = False

        node.input_data = input_data
        node.save()
        return HttpResponse(status=200)

    # Execute the action
    if not at or not node:
        return HttpResponse(status=400)

    try:
        mapping = {
            "first_name": node.input_data.get("first_name"),
            "last_name": node.input_data.get("last_name"),
            "email": node.input_data.get("email"),
            "phone": node.input_data.get("phone"),
            "address1": node.input_data.get("address1"),
            "address2": node.input_data.get("address2"),
            "country": node.input_data.get("country"),
            "province": node.input_data.get("province"),
            "city": node.input_data.get("city"),
            "zip": node.input_data.get("zip"),
        }

        # Save the mapping
        save_mapping, _ = ContactsMappingFields.objects.get_or_create(
            workspace=workspace,
            platform="shopify",  # Default to shopify, can be expanded later
        )
        save_mapping.input_data = mapping
        save_mapping.save()

        output_data = {"mapping_fields": mapping}

        at.status = "success"
        at.output_data = output_data
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        # Create notification
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message="Customer fields mapping has been saved successfully"
            if lang == "en"
            else "顧客フィールドのマッピングが正常に保存されました",
            type="success",
        )

        # If this is part of a workflow, process the next node
        if wat:
            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

        # Redirect to workflow page if appropriate
        if "run" in submit_option and node and node.workflow_history:
            module_slug = request.POST.get("module")
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_WORKFLOW,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

        return HttpResponse(status=200)

    except Exception as e:
        print(f"Error mapping customer fields: {e}")
        at.status = "failed"
        at.completed_at = timezone.now()
        at.save()

        if wat:
            wat.status = "failed"
            wat.save()

        # Create error notification
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message=f"Failed to save customer field mapping: {str(e)}"
            if lang == "en"
            else f"顧客フィールドのマッピングの保存に失敗しました: {str(e)}",
            type="error",
        )

        return HttpResponse(status=500)


@login_or_hubspot_required
def export_customers(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_channel_integration = None

        if node and node.input_data:
            active_channel_integration = node.input_data.get(
                "channel_integration", None
            )

        # Get available integrations
        channels = Channel.objects.filter(
            workspace=workspace, integration__slug__in=["shopify"]
        )

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_channel_integration": active_channel_integration,
            "channels": channels,
        }

        return render(request, "data/shopturbo/export-customers-form.html", context)

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    channel_integration = request.POST.get("channel_integration" + postfix)
    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {
            "channel_integration": channel_integration,
            "action_name": action_name,
        }

        node.valid_to_run = True
        if not channel_integration:
            node.valid_to_run = False

        node.input_data = input_data
        node.save()
        return HttpResponse(status=200)
    else:
        channel_integration = node.input_data.get("channel_integration", None)

    # Execute the action
    if not at or not node:
        return HttpResponse(status=400)

    try:
        # Get the channel integration
        channel_integration = node.input_data.get("channel_integration")
        if not channel_integration:
            at.status = "failed"
            at.input_data["error_messages"] = (
                "Channel integration is required"
                if lang == "en"
                else "チャネル連携が必要です"
            )
            at.completed_at = timezone.now()
            at.save()

            wat.status = "failed"
            wat.save()
            return False

        channel = Channel.objects.get(id=channel_integration)
        platform = channel.integration.slug

        # Get mapping configuration for this platform
        try:
            mapping = ContactsMappingFields.objects.get(
                workspace=workspace, platform=platform
            )
            mapped_fields = mapping.input_data
        except ContactsMappingFields.DoesNotExist:
            try:
                # Try to get default mapping
                mapping = ContactsMappingFields.objects.get(
                    workspace=workspace, platform__isnull=True
                )
                mapped_fields = mapping.input_data
                # Update platform for reuse
                mapping.platform = platform
                mapping.save()
            except ContactsMappingFields.DoesNotExist:
                mapped_fields = {}

        # Get active contacts to export
        contacts = Contact.objects.filter(workspace=workspace, status="active")

        if platform == "shopify":
            # Parse mapping and push to Shopify
            if mapped_fields:
                parsed_mapping = parse_mapping(mapped_fields)
            else:
                parsed_mapping = None
            push_shopify_customer(request.user, contacts, channel.id, parsed_mapping)
        else:
            at.status = "failed"
            at.input_data["error_messages"] = (
                f"Unsupported integration: {platform}"
                if lang == "en"
                else f"サポートされていない連携: {platform}"
            )
            at.completed_at = timezone.now()
            at.save()

            wat.status = "failed"
            wat.save()
            return False

        # Update output data with results
        output_data = {"integration_name": channel.name}

        at.status = "success"
        at.output_data = output_data
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        # Trigger next action if available
        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

        # Redirect to workflow page if appropriate
        if "run" in submit_option and node and node.workflow_history:
            module_slug = request.POST.get("module")
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_WORKFLOW,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

        return HttpResponse(status=200)

    except Exception as e:
        print(f"Error exporting customers: {e}")
        at.status = "failed"
        at.input_data["error_messages"] = str(e)
        at.completed_at = timezone.now()
        at.save()

        wat.status = "failed"
        wat.save()

        # Create error notification
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message=f"Failed to export customers: {str(e)}"
            if lang == "en"
            else f"顧客のエクスポートに失敗しました: {str(e)}",
            type="error",
        )
        return HttpResponse(status=500)


@login_or_hubspot_required
def import_data_from_integration(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        action_name = None
        if postfix:
            if node and node.input_data:
                if "action_name" in node.input_data:
                    action_name = node.input_data["action_name"]
        action_names = [
            ("import_customers", "Import Customers", "顧客をインポート"),
            ("import_messages", "Import Messages", "メッセージをインポート"),
            (
                "import_subscriptions",
                "Import Subscriptions",
                "サブスクリプションレコードをインポート",
            ),
            ("import_orders", "Import Orders", "受注レコードをインポート"),
            (
                "import_stripe_invoices",
                "Import Invoices",
                "売上請求レコードをインポート",
            ),
            ("import_items", "Import Items", "商品レコードをインポート"),
            (
                "import_multiple_items",
                "Import Items from Multiple Integrations",
                "複数の連携サービスから商品レコードをインポート",
            ),
            (
                "import_custom_objects",
                "Import Custom Object",
                "カスタムオブジェクトレコードをインポート",
            ),
        ]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_name": action_name,
            "action_names": action_names,
        }
        return render(
            request, "data/shopturbo/import-data-from-integration.html", context
        )

    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    if at_id:
        try:
            ActionTracker.objects.get(id=at_id)
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    if wat_id:
        try:
            WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    action_name = request.POST.get("action_name" + postfix)
    print(action_name)

    if submit_option == "save":
        input_data = {}
        if action_name in [
            "import_customers",
            "import_messages",
            "import_subscriptions",
            "import_stripe_invoices",
            "import_orders",
            "import_items",
            "import_multiple_items",
            "import_custom_objects",
        ]:
            input_data["action_name"] = action_name
        else:
            node.valid_to_run = False

        node.input_data = input_data
        node.save()
    else:
        try:
            action_name = node.input_data["action_name"]
        except:
            pass

    if action_name == "import_customers":
        return import_customers(request)
    elif action_name == "import_messages":
        return import_messages(request)
    elif action_name == "import_subscriptions":
        return import_subscriptions(request)
    elif action_name == "import_orders":
        return import_orders(request)
    elif action_name == "import_items":
        return import_items(request)
    elif action_name == "import_multiple_items":
        return import_multiple_items(request)
    elif action_name == "import_custom_objects":
        return import_custom_objects(request)
    elif action_name == "import_stripe_invoices":
        return import_stripe_invoices(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def import_integration_action_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    action_name = request.GET.get("action_name" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    print("ACTION NAME", action_name)

    if action_name == "import_customers":
        return import_customers(request)
    elif action_name == "import_messages":
        return import_messages(request)
    elif action_name == "import_subscriptions":
        return import_subscriptions(request)
    elif action_name == "import_stripe_invoices":
        return import_stripe_invoices(request)
    elif action_name == "import_orders":
        return import_orders(request)
    elif action_name == "import_items":
        return import_items(request)
    elif action_name == "import_multiple_items":
        return import_multiple_items(request)
    elif action_name == "import_custom_objects":
        return import_custom_objects(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def import_customers(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_channel_integration = None

        if node and node.input_data:
            if "channel_integration" in node.input_data:
                active_channel_integration = node.input_data["channel_integration"]

        # Get available integrations
        integrations = Channel.objects.filter(
            workspace=workspace, integration__slug__in=["shopify"]
        )

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_channel_integration": active_channel_integration,
            "integrations": integrations,
        }

        return render(request, "data/shopturbo/import-customers-form.html", context)

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    channel_integration = request.POST.get("channel_integration" + postfix)
    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        node.valid_to_run = True
        input_data = {}

        if channel_integration:
            input_data["channel_integration"] = channel_integration
        else:
            node.valid_to_run = False

        if action_name:
            input_data["action_name"] = action_name

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()
    else:
        if not node:
            return HttpResponse(status=400)

        # Extract values from node input data if running the action
        channel_integration = node.input_data.get("channel_integration", "")

        try:
            # Get the integration channel
            integration_channel = Channel.objects.get(
                id=channel_integration, workspace=workspace
            )

            # Determine which integration to use based on integration_channel.integration.slug
            integration_type = integration_channel.integration.slug

            imported_customers = 0

            # Import customers based on integration type
            if integration_channel.integration.slug == "shopify":
                mapping = {}
                next_node = None
                is_contain_mapping = False
                if not is_contain_mapping:
                    try:
                        mapping = ContactsMappingFields.objects.get(
                            workspace=workspace, platform__isnull=True
                        )
                        mapping.platform = "shopify"
                        mapping.save()
                    except:
                        pass
                    save_mapping, _ = ContactsMappingFields.objects.get_or_create(
                        workspace=workspace,
                        platform=integration_channel.integration.slug,
                    )
                    if save_mapping.input_data:
                        mapping = parse_mapping(save_mapping.input_data)

                # Create transfer history for background task tracking
                if lang == "ja":
                    task_name = "Shopify顧客のインポート"
                else:
                    task_name = "Import Shopify customers"

                history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="import_contact",
                    name=task_name,
                    channel=integration_channel or None,
                )

                # Create background job and run Shopify import task
                background_job = create_bg_job(
                    workspace=workspace,
                    user=request.user,
                    name=task_name,
                    transfer_history=history,
                )

                try:
                    payload = ImportShopifyContactsPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(integration_channel.id),
                        mapping_custom_fields=json.dumps(mapping),
                        history_id=str(history.id),
                        lang=lang,
                        background_job_id=str(background_job.id),
                    )
                    ref = import_shopify_contacts_task.run_no_wait(input=payload)
                    add_hatchet_run_id(background_job.id, ref.run_id)

                    # Update action tracker status
                    at.status = "completed"
                    at.completed_at = timezone.now()
                    at.save()

                    if wat:
                        wat.status = "completed"
                        wat.save()

                    if lang == "ja":
                        message = "Shopify顧客のインポートが開始されました。少々お待ちください..."
                    else:
                        message = "Shopify customers import started. Please give it a few moments..."

                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=message,
                        type="success",
                    )

                except Exception as e:
                    logger.error(f"Error starting Shopify import task: {str(e)}")

                    # Update action tracker status to failed
                    at.status = "failed"
                    at.completed_at = timezone.now()
                    at.save()

                    if wat:
                        wat.status = "failed"
                        wat.save()

                    if lang == "ja":
                        message = "Shopify顧客のインポートの開始に失敗しました。サポートにお問い合わせください。"
                    else:
                        message = "Failed to start Shopify customers import. Please contact support."

                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message=message,
                        type="error",
                    )

                    # Update transfer history to failed
                    history.status = "failed"
                    history.save()
            else:
                # Default or unsupported integration
                at.status = "failed"
                at.completed_at = timezone.now()
                at.save()

                wat.status = "failed"
                wat.save()

                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"Unsupported integration type: {integration_type}"
                    if lang == "en"
                    else f"サポートされていない連携タイプ: {integration_type}",
                    type="error",
                )

                return HttpResponse(status=400)

            output_data = {
                "imported_customers_count": imported_customers,
                "integration_type": integration_type,
                "integration_name": integration_channel.name,
            }

            # Update action tracker
            at.status = "success"
            at.output_data = output_data
            at.completed_at = timezone.now()
            at.save()

            wat.status = "success"
            wat.save()

            # Create notification
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Successfully imported {imported_customers} customers from {integration_channel.name}"
                if lang == "en"
                else f"{integration_channel.name}から{imported_customers}人の顧客を正常にインポートしました",
                type="success",
            )

            # Trigger next action if available
            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

            # Redirect to workflow page
            module_slug = request.POST.get("module")
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_WORKFLOW,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

        except Exception as e:
            print(f"Error importing customers: {str(e)}")
            at.status = "failed"
            at.completed_at = timezone.now()
            at.save()

            wat.status = "failed"
            wat.save()

            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Failed to import customers: {str(e)}"
                if lang == "en"
                else f"顧客のインポートに失敗しました: {str(e)}",
                type="error",
            )

            return HttpResponse(status=500)


@login_or_hubspot_required
def ai_actions(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        action_name = None
        if postfix:
            if node and node.input_data:
                if "action_name" in node.input_data:
                    action_name = node.input_data["action_name"]
        action_names = [
            ("generate_barcode_form", "Generate Barcode", "バーコードを生成"),
            ("convert_pdf_to_text", "Convert PDF to Text", "PDFをテキストに変換"),
            ("upload_wholesale_price", "Upload price table", "価格表をアップロード"),
            ("send_emails_from_workflow", "Send Emails", "メールを送信"),
            (
                "auto_inventory_email",
                "Inventory Shortage Notification Email",
                "在庫不足お知らせメール",
            ),
        ]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_name": action_name,
            "action_names": action_names,
        }
        return render(request, "data/shopturbo/ai-actions.html", context)

    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("Action node does not exist")
            return HttpResponse(status=404)
    if at_id:
        try:
            ActionTracker.objects.get(id=at_id)
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    if wat_id:
        try:
            WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {}
        if action_name:
            input_data["action_name"] = action_name

        node.input_data = input_data
        node.save()
    else:
        try:
            action_name = node.input_data["action_name"]
        except:
            pass

    if action_name == "generate_barcode_form":
        return generate_barcode_form(request)
    elif action_name == "convert_pdf_to_text":
        return convert_pdf_to_text(request)
    elif action_name == "upload_wholesale_price":
        return upload_wholesale_price(request)
    elif action_name == "send_emails_from_workflow":
        return send_emails_from_workflow(request)
    elif action_name == "auto_inventory_email":
        return auto_inventory_email(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def ai_action_name(request):
    action_index = request.GET.get("action_index")
    action_node_id = request.GET.get("action_node_id")

    postfix = ""
    if action_index:
        postfix = "-" + str(action_index)
    action_name = request.GET.get("action_name" + postfix)

    if action_node_id:
        try:
            ActionNode.objects.get(id=action_node_id)
        except:
            print("Action node does not exist")
            return HttpResponse(status=404)

    if action_name == "generate_barcode_form":
        return generate_barcode_form(request)
    elif action_name == "convert_pdf_to_text":
        return convert_pdf_to_text(request)
    elif action_name == "upload_wholesale_price":
        return upload_wholesale_price(request)
    elif action_name == "send_emails_from_workflow":
        return send_emails_from_workflow(request)
    elif action_name == "auto_inventory_email":
        return auto_inventory_email(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def convert_pdf_to_text(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        # Get active values from node input_data if they exist
        active_language = None
        active_pdf_file_id = None

        if node and node.input_data:
            if "language" in node.input_data:
                active_language = node.input_data["language"]
            if "pdf_file_id" in node.input_data:
                active_pdf_file_id = node.input_data["pdf_file_id"]

        # Language selection options
        if lang == "ja":
            selector_language = [
                {"value": "ja", "display": "日本語"},
                {"value": "en", "display": "英語"},
            ]
        else:
            selector_language = [
                {"value": "en", "display": "English"},
                {"value": "ja", "display": "Japanese"},
            ]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_slug": action_slug,
            "active_language": active_language,
            "active_pdf_file_id": active_pdf_file_id,
            "language_select": selector_language,
        }

        return render(request, "data/shopturbo/convert-pdf-to-text-form.html", context)

    # POST
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    language = request.POST.get("language_select" + postfix)
    pdf_file = request.FILES.get("pdf_file" + postfix)
    pdf_file_id = request.POST.get("pdf_file_id" + postfix)
    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        node.valid_to_run = True
        input_data = {}

        # If we have a file upload, save it first
        if pdf_file:
            try:
                # Save the uploaded PDF file
                pdf_storage = GeneralStorage.objects.create(
                    file=pdf_file, user=request.user, workspace=workspace
                )
                pdf_file_id = str(pdf_storage.id)
            except Exception as e:
                print(f"Error saving PDF file: {str(e)}")
                node.valid_to_run = False

        if pdf_file_id:
            input_data["pdf_file_id"] = pdf_file_id
        else:
            node.valid_to_run = False

        if language:
            input_data["language"] = language
        else:
            node.valid_to_run = False

        if action_name:
            input_data["action_name"] = action_name

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()
    else:
        if not node:
            return HttpResponse(status=400)

        # Extract values from node input data if running the action
        pdf_file_id = node.input_data.get("pdf_file_id", "")
        language = node.input_data.get("language", "auto")

        try:
            # Get the PDF file
            pdf_storage = GeneralStorage.objects.get(id=pdf_file_id)

            # Attempt to convert PDF to text using OCR or PDF extraction
            # This is a placeholder for the actual implementation
            try:
                from pdf2image import convert_from_path

                # Path to the uploaded PDF file
                pdf_path = pdf_storage.file.path

                # Extract text using different methods based on PDF type
                extracted_text = ""

                try:
                    # First try simple PDF text extraction
                    from PyPDF2 import PdfReader

                    reader = PdfReader(pdf_path)
                    for page_num in range(len(reader.pages)):
                        page = reader.pages[page_num]
                        extracted_text += page.extract_text() + "\n\n"
                except Exception as e:
                    print(f"Simple extraction failed, falling back to OCR: {e}")
                    # If that fails, use OCR
                    convert_from_path(pdf_path)
                    # for i, page in enumerate(pages):
                    # text = pytesseract.image_to_string(
                    #     page, lang=language if language != 'auto' else None)
                    # extracted_text += text + "\n\n"

            except ImportError:
                # If OCR libraries aren't available, provide a fallback message
                extracted_text = "PDF text extraction requires additional libraries. Please contact support."

            output_data = {"pdf_text": extracted_text, "pdf_file_id": pdf_file_id}

            # Update action tracker
            at.status = "success"
            at.output_data = output_data
            at.completed_at = timezone.now()
            at.save()

            wat.status = "success"
            wat.save()

            # Create notification
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Successfully converted PDF to text"
                if lang == "en"
                else "PDFをテキストに正常に変換しました",
                type="success",
            )

            # Trigger next action if available
            next_node = None
            if node:
                next_node = node.next_node
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

            # Redirect to workflow page
            module_slug = request.POST.get("module")
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            if not module_slug:
                module = (
                    Module.objects.filter(
                        workspace=workspace,
                        object_values__contains=TYPE_OBJECT_WORKFLOW,
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
            )

        except Exception as e:
            print(f"Error converting PDF to text: {str(e)}")
            at.status = "failed"
            at.completed_at = timezone.now()
            at.save()

            wat.status = "failed"
            wat.save()

            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Failed to convert PDF to text: {str(e)}"
                if lang == "en"
                else f"PDFをテキストに変換できませんでした: {str(e)}",
                type="error",
            )

            return HttpResponse(status=500)


def approval(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        approvers = []
        fields = []
        multiple_approval = False
        if node and node.input_data:
            if "approvers" in node.input_data:
                approvers = node.input_data["approvers"]
            if "fields" in node.input_data:
                fields = node.input_data["fields"]
            if "multiple_approval" in node.input_data:
                multiple_approval = node.input_data["multiple_approval"]

        context = {
            "node": node,
            "action_slug": action_slug,
            "action_index": action_index,
            "approvers": approvers,
            "fields": fields,
            "multiple_approval": multiple_approval,
        }
        return render(request, "data/action/approval/approval.html", context)

    # POST
    submit_option = request.POST.get("submit-option")
    if submit_option not in ["save", "run", "approve", "decline", "rollback"]:
        print("Invalid submit option:", submit_option)
        return HttpResponse(status=404)

    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")

    node = None
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
            if at.node:
                node = at.node
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)

    if not node:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except:
            print("ActionNode does not exist")
            return HttpResponse(status=404)

    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    print(submit_option)
    if submit_option == "save":
        node.valid_to_run = False
        action_index = request.POST.get("action_index")
        try:
            action_index = int(action_index)
            postfix = f"-{action_index}"
        except:
            print("Invalid action index")
            return HttpResponse(status=400)
        approvers = request.POST.getlist("approver" + postfix)
        if approvers:
            workspace = node.workflow.workspace
            approvers = list(
                workspace.user.filter(username__in=approvers).values_list(
                    "username", flat=True
                )
            )

        is_multiple_approval = request.POST.get("multiple_approval" + postfix)
        if is_multiple_approval:
            approvers = []
            approver_targets = request.POST.getlist("multi-approver" + postfix)
            for approver_target in approver_targets:
                try:
                    field_id = approver_target.split("-")[1]
                    approver_username = request.POST.get(approver_target)
                    approver_data = {
                        "id": field_id,
                        "username": approver_username,
                    }
                    approvers.append(approver_data)
                except Exception as e:
                    logger.error(f"Error processing approval field: {e}", exc_info=True)

        custom_fields = request.POST.getlist("approval-custom-field" + postfix)
        fields = []
        for field in custom_fields:
            try:
                field_id = field.split("-")[1]
                field_type = field.split("-")[0]
                field_label = request.POST.get(
                    field_type + "-" + field_id + "-label" + postfix
                )
                field_value = request.POST.get(field)
                field_data = {
                    "id": field_id,
                    "label": field_label,
                    "value": field_value,
                    "type": field_type,
                }
                if field_type == "choice":
                    options = request.POST.getlist(
                        "choice-options-" + field_id + postfix
                    )
                    field_data["options"] = options
                fields.append(field_data)
            except (IndexError, ValueError) as e:
                logger.error(
                    f"Error processing approval field '{field}': {e}", exc_info=True
                )

        if approvers:
            node.valid_to_run = True

        node.input_data = {
            "approvers": approvers,
            "fields": fields,
            "multiple_approval": is_multiple_approval,
        }
        node.predefined_input = node.input_data
        node.save()
        return HttpResponse()

    elif submit_option in ["approve", "rollback", "decline"]:
        if not request.user.is_authenticated:
            return HttpResponse(status=401)
        workspace = get_workspace(request.user)
        lang = request.LANGUAGE_CODE
        workflow_action_tracker_id = request.POST.get("workflow_action_tracker_id")
        action_tracker_id = request.POST.get("action_tracker_id")
        rollback_to = request.POST.get("rollback_to")
        text = request.POST.get("text")

        module_slug = request.POST.get("module")
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        if not module_slug:
            module = (
                Module.objects.filter(
                    workspace=workspace,
                    object_values__contains=TYPE_OBJECT_WORKFLOW,
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug

        if (
            (not submit_option and not text)
            or (
                submit_option
                and submit_option not in ["approve", "rollback", "decline"]
            )
            or (submit_option == "rollback" and not rollback_to)
        ):
            if lang == "ja":
                err_message = "無効または不正な入力です。"
            else:
                err_message = "Invalid input or input was malformed."
            print("400", err_message)
            response = HttpResponse(err_message, status=400)
            return response

        try:
            wat = WorkflowActionTracker.objects.get(id=workflow_action_tracker_id)
            at = ActionTracker.objects.get(id=action_tracker_id)
        except Exception as e:
            if lang == "ja":
                err_message = "何か問題が発生しました。"
            else:
                err_message = "Something went wrong."
            print("404", e)
            response = HttpResponse(err_message, status=404)
            return response

        params = {
            "workflow-action-tracker-id": str(wat.id),
            "user": request.user.id,
            "lang": lang,
        }
        response = HttpResponse(status=200)

        custom_fields = request.POST.getlist("approval-custom-field")
        stored_fields = at.input_data.get("fields", [])
        for stored_field in stored_fields:
            for field in custom_fields:
                try:
                    field_id = field.split("-")[1]
                    if stored_field["id"] == field_id:
                        field_type = field.split("-")[0]
                        field_value = request.POST.get(field)
                        if field_value != stored_field["value"]:
                            stored_field["value"] = field_value
                            stored_field["edited_by"] = request.user.username
                except (IndexError, ValueError) as e:
                    logger.error(
                        f"Error processing approval field '{field}': {e}", exc_info=True
                    )
        if stored_fields:
            at.input_data["fields"] = stored_fields
            at.save()
        if submit_option == "approve":
            try:
                prev_node = ActionNode.objects.get(
                    workflow_history=wat.workflow_history, next_node=at.node
                )

                # completed_at__isnull: This is the reference node. Should be ignore
                prev_at = (
                    prev_node.actiontracker_set.filter(completed_at__isnull=False)
                    .order_by("-completed_at")
                    .first()
                )
                output_data = prev_at.output_data
                # for k in flatten_output_data(prev_at.output_data):
                #     # Get payload
                #     val = request.POST.get(k.replace('.', '-'))
                #     update_nested_dict_value(output_data, k.split('.'), val)

                # Update output data as reviewed and updated by reviewer
                at.output_data = output_data
            except ActionNode.DoesNotExist:
                prev_node = None

            if at.input_data.get("multiple_approval"):
                input_data = at.input_data.copy()
                approvers = input_data.get("approvers")
                unique_approvers = list(
                    dict.fromkeys([val["username"] for val in approvers])
                )
                current_approver_index = unique_approvers.index(
                    next(
                        item
                        for item in unique_approvers
                        if item == request.user.username
                    )
                )
                if current_approver_index + 1 < len(unique_approvers):
                    input_data["current_approver"] = unique_approvers[
                        current_approver_index + 1
                    ]
                at.input_data = input_data
                if current_approver_index + 1 == len(unique_approvers):
                    at.status = "success"
                else:
                    at.status = "review"
            else:
                at.status = "success"
            at.completed_at = timezone.now()
            at.save()

            wat.status = "running"
            wat.save()

        elif submit_option == "rollback":
            # Archive all the rollback node action trackers, and create new one.
            rollback_at = ActionTracker.objects.get(id=rollback_to)
            archive_node = rollback_at.node
            while archive_node != at.node.next_node:
                # Archiving action tracker on each nodes
                for archive_at in ActionTracker.objects.filter(
                    node=archive_node
                ).order_by("created_at"):
                    # Create new action tracker
                    new_at = ActionTracker.objects.create(
                        workspace=workspace,
                        workflow_action_tracker=wat,
                        node=archive_node,
                        status="initialized",
                    )
                    new_at.input_data = archive_at.initial_input_data
                    new_at.initial_input_data = archive_at.initial_input_data
                    new_at.previous_output_data = archive_at.previous_output_data
                    new_at.save()

                    archive_at.delete()

                archive_node = archive_node.next_node

            wat.status = "running"
            wat.save()

            # Set which action tracker to start again
            at = ActionTracker.objects.filter(node=rollback_at.node).order_by(
                "created_at"
            )[0]
            params["action-tracker-id"] = str(at.id)
            params["action-node-id"] = str(rollback_at.node.id)
            params["user_id"] = str(request.user.id)
            # Create payload for background job
            payload = RunWorkflowActionPayload(
                action_node_id=params.get("action-node-id"),
                action_tracker_id=params.get("action-tracker-id"),
                workflow_action_tracker_id=params.get("workflow-action-tracker-id"),
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                language=lang,
            )

            # Create background job
            job_id = create_bg_job(
                workspace=workspace,
                user=request.user,
                function_name="run_workflow_action",
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id

            print(
                f"[Generate Workflow Action] - Adding run action queue message to bg-job: {payload.model_dump()}"
            )

            try:
                ref = run_workflow_action_task.run_no_wait(input=payload)
            except Exception as e:
                logger.error(
                    f"Generate Workflow Action: Exception occurred during run_workflow_action_task: {str(e)}",
                    exc_info=True,
                )
                ref = None

            is_running = None
            if ref:
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                is_running = False

            if not is_running:
                dn = DiscordNotification()
                dn.send_message(
                    "[Trigger next action] - Send queue to run-action failed."
                )
                at.status = "failed"
                at.save()

                print("[Generate Workflow Action] - Action running successfully")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="アクションは正常に実行されます",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=get_workspace(request.user),
                        user=request.user,
                        message="Action running successfully",
                        type="success",
                    )

                return response

            title = wat.workflow_history.workflow.title
            requesters = wat.workflow_history.workflow.assignee.all()

            for requester in requesters:
                status = submit_option
                if status == "rollback":
                    status = "rolled back"
                else:
                    status = status + "d"
                lang = requester.verification.language
                if lang == "ja":
                    if status == "approve":
                        status = "承認された"
                    elif status == "rolled back":
                        status = "ロールバック"
                    else:
                        status = "拒否"
                link = (
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=review"
                )
                Notification.objects.create(
                    workspace=workspace,
                    user=requester,
                    cta_target=link,
                    message=f"{request.user.first_name} が {wat.workflow_history.workflow.title} に返信しました: {status}"
                    if lang == "ja"
                    else f"{request.user.first_name} responded to {wat.workflow_history.workflow.title}: {status.title()}",
                    type="success" if status == "approval" else "warning",
                )

                context = {
                    "fullname": requester.first_name,
                    "requesters": requesters,
                    "link": link,
                }
                if lang == "ja":
                    mail_subject = f"ワークフローレビューリクエスト - {title}"
                    message = render_to_string(
                        "data/email/email-changed-ja.html", context
                    )
                else:
                    mail_subject = f"Workflow Review Request - {title}"
                    message = render_to_string("data/email/email-changed.html", context)

                from_email = "Sanka <<EMAIL>>"
                to_email = [requester.email]
                send_mail(
                    mail_subject,
                    message,
                    from_email,
                    to_email,
                    fail_silently=False,
                )

            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module:
                module_slug = module.slug
            if module:
                # +f'?h_task={tat.task.id}&show_task_action={tat.id}'
                response["HX-Redirect"] = reverse("taskflow_in_app", host="app")
            else:
                response["HX-Redirect"] = reverse("main", host="app")
            return response

        else:  # decline
            if text:
                at.output_data = {"text": text}
            at.status = "declined"
            at.completed_at = timezone.now()
            at.save()

            wat.status = "declined"
            wat.save()

            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            if module:
                module_slug = module.slug
                response["HX-Redirect"] = (
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?h_workflow={wat.workflow_history.workflow.id}"
                )
            else:
                response["HX-Redirect"] = reverse("main", host="app")
            return response

        if at.node.next_node:
            try:
                next_at = ActionTracker.objects.get(
                    node=at.node.next_node, workflow_action_tracker=wat
                )
            except ActionTracker.DoesNotExist:
                next_at = ActionTracker.objects.create(
                    workspace=workspace,
                    workflow_action_tracker=wat,
                    node=at.node.next_node,
                    status="initialized",
                )
                if node.input_data:
                    at.input_data = node.input_data
                    at.initial_input_data = node.input_data
                if node.previous_output_data:
                    at.previous_output_data = node.previous_output_data
                next_at.save()
            print(
                "[Generate Workflow Action] - Run action in background",
                at.node.next_node.action,
            )
            params["user_id"] = str(request.user.id)
            params["action_tracker_id"] = str(next_at.id)
            params["action_node_id"] = str(at.node.next_node.id)

            # Create payload for background job
            payload = RunWorkflowActionPayload(
                action_node_id=params.get("action_node_id"),
                action_tracker_id=params.get("action_tracker_id"),
                workflow_action_tracker_id=params.get("workflow_action_tracker_id"),
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                workflow_history_id=str(wat.workflow_history.id)
                if wat.workflow_history
                else None,
                language=lang,
            )

            # Create background job
            job_id = create_bg_job(
                workspace=workspace,
                user=request.user,
                function_name="run_workflow_action",
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id

            print(
                f"[Generate Workflow Action] - Adding run action queue message to bg-job: {payload.model_dump()}"
            )

            try:
                ref = run_workflow_action_task.run_no_wait(input=payload)
            except Exception as e:
                logger.error(
                    f"Generate Workflow Action: Exception occurred during run_workflow_action_task: {str(e)}",
                    exc_info=True,
                )
                ref = None

            is_running = None
            if ref:
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                is_running = False

            if not is_running:
                dn = DiscordNotification()
                dn.send_message(
                    "[Trigger next action] - Send queue to run-action failed."
                )
                at.status = "failed"
                at.save()

            print("[Generate Workflow Action] - Action running successfully")
            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="アクションは正常に実行されます",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Action running successfully",
                    type="success",
                )
        else:
            wat.status = "success"
            wat.save()

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
            response["HX-Redirect"] = (
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={wat.workflow_history.workflow.id}"
            )
        else:
            response["HX-Redirect"] = reverse("main", host="app")
        return response
    else:
        if not (at or wat):
            return HttpResponse(status=400)

        if node.input_data["multiple_approval"]:
            # Approvers should be in correct order
            approvers = []
            for approver in node.input_data["approvers"]:
                approvers.append(
                    workspace.user.filter(username=approver["username"]).first()
                )
        else:
            approvers = node.input_data["approvers"]
            approvers = workspace.user.filter(username__in=approvers)
        at.input_data = node.input_data
        at.status = "review"
        at.save()
        wat.status = "review"
        wat.workflow_history.assignee.clear()
        wat.workflow_history.assignee.add(*approvers)
        wat.workflow_history.save()
        wat.save()

        module_slug = request.POST.get("module")
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
        if not module_slug:
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug

        print("approvers", approvers)

        approvers_to_notify = approvers
        if node.input_data["multiple_approval"]:
            approvers_to_notify = [approvers[0]]
            input_data = at.input_data.copy()
            input_data["current_approver"] = approvers[0].username
            at.input_data = input_data
            at.save()
        for approver in approvers_to_notify:
            lang = approver.verification.language
            link = (
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=review"
            )
            Notification.objects.create(
                workspace=workspace,
                user=approver,
                cta_target=link,
                message=f"{wat.workflow_history.workflow.title}のレビュー依頼"
                if lang == "ja"
                else f"Review request for {wat.workflow_history.workflow.title}",
                type="warning",
            )

            title = wat.workflow_history.workflow.title
            requesters = wat.workflow_history.workflow.assignee.all().values_list(
                "first_name", flat=True
            )

            context = {
                "fullname": approver.first_name,
                "requesters": requesters,
                "link": link,
            }
            if lang == "ja":
                mail_subject = f"ワークフローレビューリクエスト - {title}"
                message = render_to_string("data/email/email-changed-ja.html", context)
            else:
                mail_subject = f"Workflow Review Request - {title}"
                message = render_to_string("data/email/email-changed.html", context)

            from_email = "Sanka <<EMAIL>>"
            to_email = [approver.email]
            send_mail(
                mail_subject,
                message,
                from_email,
                to_email,
                fail_silently=False,
            )

        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + f"?h_workflow={node.workflow_history.workflow.id}&drawer_tab=history"
        )


@login_or_hubspot_required
def sync_actions(request):
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        action_name = None
        if postfix:
            if node and node.input_data:
                if "action_name" in node.input_data:
                    action_name = node.input_data["action_name"]
        action_names = [
            ("sync_item_and_inventory", "Sync Item and Inventory", "商品と在庫を同期"),
            (
                "sync_item_shopify_webhook",
                "Sync Item Shopify Webhook",
                "商品をShopify Webhookと同期",
            ),
        ]

        context = {
            "action_index": action_index,
            "action_node_id": action_node_id,
            "action_name": action_name,
            "action_names": action_names,
        }
        return render(request, "data/shopturbo/sync-actions.html", context)

    print(request.POST)
    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    if at_id:
        try:
            ActionTracker.objects.get(id=at_id)
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    if wat_id:
        try:
            WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index

    action_name = request.POST.get("action_name" + postfix)

    if submit_option == "save":
        input_data = {}
        if action_name:
            input_data["action_name"] = action_name

        node.input_data = input_data
        node.save()
    else:
        try:
            action_name = node.input_data["action_name"]
        except:
            pass

    if action_name == "sync_item_and_inventory":
        return sync_item_and_inventory(request)
    elif action_name == "sync_item_shopify_webhook":
        return sync_item_shopify_webhook(request)

    return HttpResponse(status=200)


@login_or_hubspot_required
def run_action_view_bg(request):
    """
    General-purpose view function to run any ACTION view function as a background job.

    This function accepts POST requests, collects form data, and triggers a background job
    using the run_action_view_function management command.

    Parameters:
    - action_slug: The slug of the view function to run
    - redirect_url: The slug of the view function to run
    - All other form data will be passed to the view function

    Returns:
    - Redirects to the appropriate page after job submission
    - Returns HTTP 400 if required parameters are missing
    - Returns HTTP 500 if job submission fails
    """
    print("=== DEBUG: run_action_view_bg called ===")
    print("Request method:", request.method)
    print("Request POST data:", dict(request.POST))
    print("Request user:", request.user)

    if request.method != "POST":
        print("=== DEBUG: Not a POST request, returning 405 ===")
        return HttpResponse(status=405)

    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    # Get required parameters
    action_slug = request.POST.get("action_slug")
    redirect_url = request.POST.get("redirect_url")
    bulk_action = request.POST.get("bulk_action")
    is_record_action = request.POST.get("is_record_action")

    print(f"=== DEBUG: Action slug: {action_slug} ===")
    print(f"=== DEBUG: Redirect URL: {redirect_url} ===")
    print(f"=== DEBUG: Bulk action: {bulk_action} ===")
    print(f"=== DEBUG: Is record action: {is_record_action} ===")

    if not action_slug or not redirect_url:
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message="Missing required parameters"
            if lang == "en"
            else "必要なパラメータがありません",
            type="error",
        )
        return HttpResponse(status=400)

    history = None
    if bulk_action or is_record_action:
        print(f"=== DEBUG: Creating ActionHistory for action_slug: {action_slug} ===")
        action = Action.objects.filter(slug=action_slug).first()
        print(f"=== DEBUG: Found action: {action} ===")

        if action:
            history = ActionHistory.objects.create(
                workspace=workspace,
                action=action,
                status="initialized",
                created_by=request.user,
            )
            print(f"=== DEBUG: Created ActionHistory: {history.id} ===")

            # For record actions, set the object_id and object_type
            if is_record_action:
                # Get the selected_ids from the form data
                selected_ids = request.POST.getlist("selected_ids")
                if not selected_ids:
                    # Try to get from object_type parameter
                    object_type = request.POST.get("object_type")
                    if object_type:
                        # Try to extract from redirect_url as fallback
                        redirect_url = request.POST.get("redirect_url", "")
                        if "id=" in redirect_url:
                            import re

                            match = re.search(r"id=([^&]+)", redirect_url)
                            if match:
                                selected_ids = [match.group(1)]

                if selected_ids and len(selected_ids) > 0:
                    object_type = request.POST.get("object_type")
                    if object_type:
                        history.object_id = selected_ids[0]
                        history.object_type = object_type
                        history.save()
                        print(
                            f"=== DEBUG: Updated ActionHistory with object_id: {selected_ids[0]}, object_type: {object_type} ==="
                        )
        else:
            print(f"=== DEBUG: No action found for slug: {action_slug} ===")
    else:
        print(
            f"=== DEBUG: Not creating ActionHistory - bulk_action: {bulk_action}, is_record_action: {is_record_action} ==="
        )

    # Prepare parameters for background job
    param = {
        "function": "run_action_view_function",
        "job_id": str(uuid.uuid4()),
        "workspace_id": str(workspace.id),
        "user_id": str(request.user.id),
        "args": [
            f"--workspace_id={workspace.id}",
            f"--user_id={request.user.id}",
            f"--lang={lang}",
            f"--action_slug={action_slug}",
        ],
    }

    # Add tracking IDs if provided
    if bulk_action:
        param["args"].append("--bulk_action=True")

    if is_record_action:
        param["args"].append("--is_record_action=True")

    # Collect all form data
    form_data = {}
    print(request.POST)
    for key in request.POST.keys():
        values = request.POST.getlist(key)

        if len(values) == 1:
            value = values[0]
            if (
                isinstance(value, str)
                and value
                and value[0] == "["
                and value[-1] == "]"
            ):
                form_data[key] = ast.literal_eval(value)
            else:
                form_data[key] = value
        else:
            form_data[key] = values
    if history:
        form_data["history_id"] = str(history.id)

    # Add form data
    # form_data_json = json.dumps(form_data)
    param["args"].append(f"--form_data={form_data}")

    # Trigger background job
    print("=== DEBUG: Triggering background job ===")
    print("Background job param:", param)
    param = json.dumps(param)
    print("Background job param JSON:", param)
    is_running = trigger_bg_job(param)
    print("Background job trigger result:", is_running)

    # Check response status
    if not is_running:
        Notification.objects.create(
            workspace=workspace,
            user=request.user,
            message="Failed to queue background job"
            if lang == "en"
            else "バックグラウンドジョブのキューに失敗しました",
            type="error",
        )
        return HttpResponse(status=500)

    # Create success notification
    Notification.objects.create(
        workspace=workspace,
        user=request.user,
        message="Background job queued successfully"
        if lang == "en"
        else "バックグラウンドジョブがキューに追加されました",
        type="success",
    )

    # Redirect to the specified URL
    return redirect(redirect_url)


def update_order_status(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    if request.method == "GET":
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")
        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        action_slug = request.GET.get("action_id" + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        header_list = [
            {"name": "Draft", "name_ja": "下書き", "value": "draft", "skip": False},
            {
                "name": "Approved",
                "name_ja": "承認済み",
                "value": "approved",
                "skip": False,
            },
            {
                "name": "Received",
                "name_ja": "受領済み",
                "value": "received",
                "skip": False,
            },
            {"name": "Paid", "name_ja": "入金済み", "value": "paid", "skip": False},
        ]

        if node and node.input_data:
            mapping_status_data = node.input_data.get("mapping_status_data", {})
            for item in header_list:
                if item["value"] in mapping_status_data:
                    item["default"] = mapping_status_data[item["value"]]["value"]
                    item["skip"] = mapping_status_data[item["value"]]["skip"]

        context = {
            "action_index": action_index,
            "action_slug": action_slug,
            "header_list": header_list,
            "SHOPTURBO_ORDER_DELIVERY_STATUS": SHOPTURBO_ORDER_DELIVERY_STATUS,
            "order": ShopTurboOrders(workspace=workspace),
        }
        return render(
            request, "data/shopturbo/sync-orders-status-import-mapping.html", context
        )

    # POST
    module_slug = request.POST.get("module")
    object_type = request.POST.get("object_type", TYPE_OBJECT_WORKFLOW)

    submit_option = request.POST.get("submit-option")
    action_index = request.POST.get("action_index")
    action_node_id = request.POST.get("action_node_id")
    action_slug = request.POST.get("action_slug")
    at_id = request.POST.get("action_tracker_id")
    wat_id = request.POST.get("workflow_action_tracker_id")
    node = None
    if action_node_id:
        try:
            node = ActionNode.objects.get(id=action_node_id)
        except ActionNode.DoesNotExist:
            print("ActionNode does not exist")
            return HttpResponse(status=404)
    at = None
    if at_id:
        try:
            at = ActionTracker.objects.get(id=at_id)
            if at.workspace:
                workspace = at.workspace
        except ActionTracker.DoesNotExist:
            print("ActionTracker does not exist")
            return HttpResponse(status=404)
    wat = None
    if wat_id:
        try:
            wat = WorkflowActionTracker.objects.get(id=wat_id)
        except WorkflowActionTracker.DoesNotExist:
            print("WorkflowActionTracker does not exist")
            return HttpResponse(status=404)

    postfix = ""
    if action_index:
        postfix = "-" + action_index
    object_type = request.POST.get("object_type" + postfix)

    if submit_option == "save":
        mapping_custom_fields_status = {}
        # Mapping Status
        file_columns_status = request.POST.getlist("order-status-file-column", [])
        sanka_properties_status = request.POST.getlist(
            "order-status-sanka-properties", []
        )
        for idx, file_column in enumerate(file_columns_status):
            mapping_custom_fields_status[file_column] = sanka_properties_status[idx]

        mapping_status_data = {}
        for i, field in enumerate(mapping_custom_fields_status):
            field_data = {"value": mapping_custom_fields_status[field], "skip": False}
            mapping_status_data[field] = field_data

        if node.workflow:
            workspace = node.workflow.workspace
        else:
            workspace = node.workflow_history.workspace
        node.valid_to_run = True
        input_data = {}
        input_data["mapping_status_data"] = mapping_status_data
        input_data["mapping_custom_fields_status"] = mapping_custom_fields_status
        input_data["object_type"] = object_type

        node.input_data = input_data
        node.predefined_input = input_data
        node.save()
        return HttpResponse()

    else:
        print("Running order status update.")
        if not (node and at and wat):
            print("No node, action tracker and workflow action tracker.")
            return HttpResponse(status=400)

        if node.workflow:
            workspace = node.workflow.workspace
        else:
            workspace = node.workflow_history.workspace

        mapping_custom_fields_status = node.input_data.get(
            "mapping_custom_fields_status", {}
        )
        try:
            invoice_id = at.input_data["invoice_id"]
            invoice = Invoice.objects.filter(id=invoice_id).first()
        except:
            invoice_id = None
            invoice = None

        print("log:", invoice.status)
        updated_orders = []
        if invoice:
            orders = ShopTurboOrders.objects.filter(
                workspace=workspace, invoice__id=invoice_id
            )
            for order in orders:
                order.delivery_status = mapping_custom_fields_status.get(
                    invoice.status, "draft"
                )
                order.save()
                updated_orders.append(str(order.id))
        print("end log:", mapping_custom_fields_status, invoice_id)

        at.status = "success"
        at.output_data = {"order_ids": updated_orders}
        at.completed_at = timezone.now()
        at.save()

        wat.status = "success"
        wat.save()

        next_node = None
        if node:
            next_node = node.next_node
        if next_node:
            next_at = ActionTracker.objects.get(
                node=next_node, workflow_action_tracker=wat
            )
            transfer_output_to_target_input(at, next_at)
            trigger_next_action(
                current_action_tracker=at,
                workflow_action_tracker=wat,
                current_node=node,
                user_id=str(request.user.id),
                lang=lang,
            )

    if not module_slug and workspace:
        return HttpResponse(status=200)
    return redirect(reverse("main", host="app"))
