from django.conf import settings
from django.conf.urls.i18n import i18n_patterns
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from django.views.generic.base import TemplateView
from django.views.generic import RedirectView

from data import views
from data.accounts import (
    account_makeshop,
    account_stripe,
    account_view,
    association_labels,
    role,
    workspace,
    workspace_billing,
    workspace_custom_object,
    workspace_module,
    workspace_setting,
)
from data.action import util_actions
from data.action.approval.urls import urlpatterns as approval_urls
from data.bill import bill_drawer, bills
from data.commerce import (
    commerce_actions,
    commerce_association,
    commerce_currency_check,
    commerce_functions,
    commerce_list_items_check,
    commerce_pdf_download,
)
from data.commerce_meter import commerce_meter
from data.company import companies
from data.constants.properties_constant import (
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_DASHBOARD,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_FORM,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PANEL,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SESSION_EVENT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_TIMEGENIE,
    TYPE_OBJECT_WORKER,
    TYPE_OBJECT_WORKER_ABSENCE,
    TYPE_OBJECT_WORKER_REVIEW,
)
from data.contact import (
    contact_drawer,
    contact_list,
    contact_merge_action,
    contact_notes,
    convert_custom_property_price_to_x,
    create_contacts_workflow,
    customer_update_association_object,
    edit_delete_profile,
)
from data.custom_object import (
    co_association,
    co_data_object,
    co_drawer,
    co_hubspot,
    co_integration,
    co_main,
    co_utility,
)
from data.delivery_note import (
    delivery_note_create_and_update,
    delivery_note_drawer,
    delivery_notes,
)
from data.demo_bot import demo_bot
from data.estimate import (
    create_dg_payment,
    estimate_create_and_update,
    estimate_drawer,
    estimates,
)
from data.expense import expense_drawer, expenses
from data.integration import integrations, smtp_integration, stripe_webhook
from data.inventory import inventory_warehouse
from data.inventory_transaction import (
    create_inventory_stock,
    inventory_transaction_barcode,
    inventory_transaction_date_input,
    inventory_transactions,
)
from data.invoice import (
    create_invoice_zengin_format,
    invoice_create_and_update,
    invoice_drawer,
    invoice_payment_link,
    invoices,
)
from data.invoice.invoices import sync_invoice_header_extractor
from data.subscriptions.subscriptions import sync_subscription_header_extractor
from data.item import generate_item_pdf, item_api, items_drawer, items_drawer_prices
from data.messages import message_templates, messages
from data.orders import actions as order_actions
from data.orders import aggregate_order_records
from data.orders import create_order
from data.orders import integration_import as order_integration_import
from data.orders import (
    manage_order,
    order_dynamic_calcuations,
    order_pdf_download,
    order_stripe_actions,
    order_update_association_object,
    orders,
    orders_drawer,
    sync_order_header_extractor,
)
from data.purchase_order import purchase_order_drawer, purchase_orders
from data.receipt import receipt_create_and_update, receipt_drawer, receipts
from data.report import render_drawer_metrics, report_forms, report_views, sheet_views
from data.san_ai import san_ai
from data.san_ai.api import (
    canvas as san_canvas,
    expense as san_expense,
    file as san_file,
    session as san_session,
    view as san_view,
)
from data.slip import slip_create_and_update, slip_drawer, slips
from data.store import store
from data.subscriptions import actions as subscription_actions
from data.subscriptions import (
    create_subscriptions,
    manage_subscriptions,
    subscription_file_size,
    subscriptions,
    subscriptions_drawer,
)
from data.workflow import workflow, workflow_template
from utils import dg_payment_integration, next_engine_integration

from . import (
    actions,
    advance_search,
    api,
    api_bg_job,
    association,
    auth,
    bill,
    campaigns,
    cases,
    chatbot,
    code_snippet_campaign,
    commerce,
    contacts,
    contractwise,
    csv_export,
    csv_upload,
    custom_pdf,
    developers,
    expense,
    formroom,
    home,
    hubspot,
    items,
    items_inventory,
    jobs,
    journal,
    line_item,
    location,
    manager,
    object,
    price_information,
    property,
    purchase_order,
    reports,
    send_email_campaign,
    shopturbo,
    support,
    taskflow,
    tasks,
    timegenie,
    user_management,
    users_import_to_workspace,
    widget,
    worker,
)

app_name = "data"

# router = routers.DefaultRouter()


# Handle language prefix without trailing slash - redirect to proper URL
# This prevents Resolver404 errors for paths like '/ja' instead of '/ja/'
urlpatterns = [
    path("ja", RedirectView.as_view(url="/ja/", permanent=True)),
    path("en", RedirectView.as_view(url="/en/", permanent=True)),
]

urlpatterns += i18n_patterns(
    # Hubspot Sanka CRM card webhook for local test
    path(
        "hubspot/crm_card/webhook/",
        home.hubspot_sanka_crm_card_webhook,
        name="hubspot_sanka_crm_card_webhook",
    ),
    # APIs
    path("api/v1/contacts/get", api.get_contacts, name="api_get_contacts"),
    path("api/v1/contacts/create", api.create_contacts, name="api_create_contacts"),
    path("api/v1/companies/get", api.get_companies, name="api_get_companies"),
    path("api/v1/companies/create", api.create_companies, name="api_create_companies"),
    path("api/v1/orders/create", api.create_orders, name="api_create_orders"),
    path("api/v1/code/popup/", api.popup_content, name="popup_content"),
    path(
        "api/v1/event/rules/<uuid:id>/",
        api.event_capturing_rule,
        name="event_capturing_rule",
    ),
    path("api/v1/event/create/", api.event_create, name="event_create"),
    # APIs for Background Jobs
    path(
        "api/social-media/publish/",
        api_bg_job.post_scheduled_content,
        name="post_scheduled_content",
    ),
    path(
        "api/campaign/send_scheduled_email/",
        api_bg_job.send_scheduled_email,
        name="send_scheduled_email",
    ),
    path(
        "api/social-media/anomalies/",
        api_bg_job.get_anomalies_post,
        name="get_anomalies_post",
    ),
    path(
        "api/social-media/twitter-rate/",
        api_bg_job.check_twitter_rate_limit,
        name="check_twitter_rate_limit",
    ),
    path("api/billing/", api_bg_job.check_billing, name="check_billing"),
    path(
        "api/ne_token_refresh/",
        api_bg_job.run_next_engine_refresh_token,
        name="run_next_engine_refresh_token",
    ),
    path(
        "api/ne_auto_pull_orders/",
        api_bg_job.run_auto_pull_orders_next_engine,
        name="run_auto_pull_orders_next_engine",
    ),
    path(
        "api/meter/reset/", api_bg_job.reset_entries_meter, name="reset_entries_meter"
    ),
    path(
        "api/action/run-checker/",
        api_bg_job.run_action_scheduler,
        name="run_action_scheduler",
    ),
    path(
        "api/subscription-order/",
        api_bg_job.subscription_order,
        name="subscription_order",
    ),
    path("api/run-job/", api_bg_job.run_job, name="run_job"),
    path(
        "api/task-history/run-checker/",
        api_bg_job.task_history_run_check,
        name="task_history_run_check",
    ),
    path("api/file/delete/", api_bg_job.delete_api_file, name="delete_api_file"),
    path(
        "api/send-daily-onboarding-email/",
        api_bg_job.send_daily_onboarding_email,
        name="send_daily_onboarding_email",
    ),
    path(
        "api/migrate/events/",
        api_bg_job.migrate_session_events,
        name="migrate_session_events",
    ),
    path(
        "api/sync-stripe/subscriptions/",
        api_bg_job.sync_stripe_subscriptions,
        name="sync_stripe_subscriptions",
    ),
    path(
        "api/sync-stripe/checkouts/",
        api_bg_job.sync_stripe_checkouts,
        name="sync_stripe_checkouts",
    ),
    # QRBOT
    path("api/qrbot/<uuid:channel_id>/<str:api_key>/", api.qr_bot_api, name="qrbot"),
    # Admin
    path("admin/", admin.site.urls),
    # AVOID INDEX
    path(
        "robots.txt",
        TemplateView.as_view(template_name="robots_app.txt", content_type="text/plain"),
    ),
    # Auth
    path("signup/", auth.signup, name="signup"),
    path("invite/", auth.invite, name="invite"),
    path("login/", auth.login_view, name="login"),
    path("logout/", auth.logout_view, name="logout"),
    path("resend_activation/", auth.resend_activation, name="resend_activation"),
    path("password_reset/", auth.password_reset_request, name="password_reset"),
    path(
        "password_reset_done/",
        auth.PasswordResetDoneView.as_view(template_name="auth/reset_pass_sent.html"),
        name="password_reset_done",
    ),
    path("need_verification/", auth.need_verification, name="need_verification"),
    path("start/", home.start, name="start"),
    path("start/workspace/", home.start_workspace, name="start_workspace"),
    # CREATE DRAWER
    path(
        "create/campaign/",
        campaigns.get_create_campaign_drawer,
        name="create_campaign_drawer",
    ),
    path("create/list/", contacts.get_create_list_drawer, name="create_list_drawer"),
    path(
        "create/report/",
        render_drawer_metrics.get_create_report_drawer,
        name="create_report_drawer",
    ),
    # Home and workflows
    path("", home.main, name="main"),
    path("search/", home.search, name="search"),
    path(
        "modules/<str:module_slug>/<str:object_slug>/",
        property.load_object_page,
        name="load_object_page",
    ),
    path(
        "chatbot_category_list",
        chatbot.get_chatbot_category,
        name="get_chatbot_category_list",
    ),
    path(
        "chatbot_category",
        chatbot.search_chatbot_category,
        name="search_chatbot_category",
    ),
    path(
        "chat/<str:slug>",
        chatbot.internal_chatbot_thread,
        name="internal_chatbot_thread",
    ),
    path("chat/<uuid:thread_id>/send/", chatbot.add_chat, name="add_chat"),
    path(
        "chat/<uuid:thread_id>/response/",
        chatbot.get_bot_response,
        name="get_bot_response",
    ),
    path(
        "chat/<uuid:thread_id>/stream/",
        chatbot.stream_bot_response,
        name="stream_bot_response",
    ),
    path("projects/form/", tasks.project_form, name="project_form"),
    path(
        "projects/form/delete/<uuid:id>/",
        tasks.project_form_delete,
        name="project_form_delete",
    ),
    path("view/form/", tasks.view_form, name="view_form"),
    path("view/delete/<uuid:id>/", tasks.delete_view, name="delete_view"),
    path("view/dropdown/", tasks.views_dropdown, name="views_dropdown"),
    path(
        "record/action/history/",
        workflow.record_action_history,
        name="record_action_history",
    ),
    path("workflows/", taskflow.taskflow, name="main_workflows"),
    path("workflows/data/", tasks.workflows_data, name="workflows_data"),
    path("workflows/data/<uuid:id>/", tasks.workflows_data, name="workflows_data"),
    path("workflows/history/", tasks.action_history, name="action_history"),
    path(
        "workflows/history/<str:category>/", tasks.action_history, name="action_history"
    ),
    path(
        "workflows/query-workflow",
        tasks.query_workflows_view,
        name="query_workflows_view",
    ),
    path(
        "workflows/generated_file/<uuid:id>/",
        taskflow.preview_generated_file,
        name="preview_generated_file",
    ),
    path("workflows/gallery/", taskflow.taskflow, name="gallery"),
    path("workflows/studio/post", workflow.studio_workflow, name="studio_workflow"),
    path(
        "workflows/studio/basic/<uuid:id>/",
        workflow.workflow_basic_data,
        name="workflow_basic_data",
    ),
    path(
        "workflows/studio/form",
        workflow.create_workflow_form,
        name="create_workflow_form",
    ),
    path("workflows/studio/workflow/", workflow.workflow, name="workflow"),
    path("workflows/studio/node/", workflow.action_node, name="action_node"),
    path("workflows/studio/node/<uuid:id>/", workflow.action_node, name="action_node"),
    path(
        "workflows/studio/node/input/",
        workflow.update_node_input,
        name="update_node_input",
    ),
    path(
        "workflows/studio/node/<uuid:id>/index/",
        workflow.get_node_index,
        name="get_node_index",
    ),
    path("workflows/detail/<uuid:id>/", home.workflow_detail, name="workflow_detail"),
    path("workflows/<uuid:id>/del", workflow.delete_workflow, name="delete_workflow"),
    path("workflows/del/", workflow.bulk_delete_workflow, name="bulk_delete_workflow"),
    path(
        "workflows/edit/bulk/", workflow.bulk_edit_workflow, name="bulk_edit_workflow"
    ),
    path(
        "workflows/duplicate/bulk/",
        workflow.bulk_duplicate_workflow,
        name="bulk_duplicate_workflow",
    ),
    path(
        "workflows/<uuid:id>/valid_action/",
        workflow.workflow_valid_action,
        name="workflow_valid_action",
    ),
    path(
        "workflows/action/", workflow.action_form, name="action_form"
    ),  # old action form selector
    path(
        "workflows/action/selector/", workflow.action_selector, name="action_selector"
    ),
    path(
        "workflows/action/order/", workflow.action_node_order, name="action_node_order"
    ),
    path(
        "workflows/action/load-prev-output/",
        workflow.reload_previous_output,
        name="reload_previous_output",
    ),
    path(
        "workflows/action/load-prev-output/all/",
        workflow.all_previous_output,
        name="all_previous_output",
    ),
    path(
        "workflows/condition/property-operator/",
        workflow.get_property_operator,
        name="get_property_operator",
    ),
    path(
        "workflows/activate/trigger",
        workflow.activate_workflow_trigger,
        name="activate_workflow_trigger",
    ),
    path(
        "workflows/deactivate/trigger",
        workflow.toggle_workflow_trigger,
        name="toggle_workflow_trigger",
    ),
    path(
        "workflows/toggle-template/<uuid:id>/",
        workflow_template.toggle_workflow_as_template,
        name="toggle_workflow_as_template",
    ),
    path(
        "workflows/template/",
        workflow_template.workflow_templates,
        name="workflow_templates",
    ),
    path(
        "workflows/initialize/",
        workflow_template.initialize_workflows,
        name="initialize_workflows",
    ),
    # ACTION FORM AND RUNNER
    path(
        "workflows/action/form/", workflow.action_form_mapper, name="action_form_mapper"
    ),
    path(
        "workflows/action/form/purchase-order-to-inventories/",
        purchase_order.purchase_order_to_inventory_form,
        name="purchase_order_to_inventory_form",
    ),  # Workspace Updated
    path(
        "workflows/action/form/generate-barcode-actions/",
        shopturbo.generate_barcode_form,
        name="generate_barcode_form",
    ),  # Workspace Updated
    path(
        "workflows/action/form/generate-item-pdf-actions/",
        # Workspace Updated
        generate_item_pdf.generate_item_pdf_form,
        name="generate_item_pdf_form",
    ),
    path(
        "workflows/action/form/create-inventory-transaction/",
        items_inventory.create_inventory_transaction,
        name="create_inventory_transaction",
    ),  # Workspace Updated
    path(
        "workflows/action/form/bulk-inventory-transaction/",
        items_inventory.bulk_inventory_transaction,
        name="bulk_inventory_transaction",
    ),  # No Workflow
    path(
        "workflows/action/form/auto-inventory-email/",
        items_inventory.auto_inventory_email,
        name="auto_inventory_email",
    ),  # Workspace Updated
    path(
        "workflows/action/form/convert-order-to-purchase-order/",
        order_actions.convert_order_to_po,
        name="convert_order_to_po",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-order-to-slips/",
        lambda request: shopturbo.convert_order_to_commerce(request, TYPE_OBJECT_SLIP),
        name="convert_order_to_slips",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-order-to-invoices/",
        lambda request: shopturbo.convert_order_to_commerce(
            request, TYPE_OBJECT_INVOICE
        ),
        name="convert_order_to_invoices",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-order-to-delivery-notes/",
        lambda request: shopturbo.convert_order_to_commerce(
            request, TYPE_OBJECT_DELIVERY_NOTE
        ),
        name="convert_order_to_delivery_notes",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-order-to-receipts/",
        lambda request: shopturbo.convert_order_to_commerce(
            request, TYPE_OBJECT_RECEIPT
        ),
        name="convert_order_to_receipts",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-order-to-estimate/",
        shopturbo.convert_order_to_estimate,
        name="convert_order_to_estimate",
    ),  # Workspace Updated
    path(
        "workflows/action/form/convert-order-to-subscription/",
        shopturbo.convert_order_to_subscription,
        name="convert_order_to_subscription",
    ),  # No Workflow
    path(
        "workflows/action/form/export-order-to-next-engine/",
        shopturbo.export_order_to_next_engine,
        name="export_order_to_next_engine",
    ),  # Workspace Updated
    path(
        "workflows/action/form/export-invoice-to-hubspot-custom-object/",
        workflow.export_invoice_to_hubspot_custom_object,
        name="export_invoice_to_hubspot_custom_object",
    ),  # Workspace Updated
    path(
        "workflows/action/form/generate-bulk-production-plan/",
        orders.generate_bulk_production_plan,
        name="generate_bulk_production_plan",
    ),  # Workspace Updated
    path(
        "workflows/action/form/change-hubspot-deal-stage/",
        shopturbo.change_hubspot_deal_stage,
        name="change_hubspot_deal_stage",
    ),  # Workspace Updated
    path(
        "workflows/action/form/change-hubspot-deal-property/",
        shopturbo.change_hubspot_deal_property,
        name="change_hubspot_deal_property",
    ),  # Workspace Updated
    path(
        "workflows/action/form/convert-estimates-to-orders/",
        commerce_actions.convert_estimate_to_orders,
        name="convert_estimate_to_orders",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-estimates-to-invoices/",
        commerce_actions.convert_estimate_to_invoices,
        name="convert_estimate_to_invoices",
    ),  # No Workflow
    path(
        "workflows/action/form/when-purchase-order-item-updated/",
        # Workspace Updated
        purchase_order.when_purchase_item_updated,
        name="when_purchase_item_updated",
    ),
    path(
        "workflows/action/form/download-record-file/",
        commerce_actions.download_record_file,
        name="download_record_file",
    ),  # No Workflow
    path(
        "workflows/action/form/send-record-email/",
        commerce_actions.send_record_email,
        name="send_record_email",
    ),  # No Workflow
    path(
        "workflows/action/form/generate-order-from-subscription/",
        shopturbo.generate_order_from_subscription,
        name="generate_order_from_subscription",
    ),  # No Workflow
    path(
        "workflows/action/form/generate-invoice-from-subscription/",
        shopturbo.generate_invoice_from_subscription,
        name="generate_invoice_from_subscription",
    ),  # No Workflow
    path(
        "workflows/action/form/hubspot-upload-properties/",
        shopturbo.hubspot_upload_properties,
        name="hubspot_upload_properties",
    ),  # Workspace Updated
    path(
        "workflows/action/form/import-orders/",
        order_actions.import_orders,
        name="import_orders",
    ),  # Workspace Updated
    path(
        "workflows/action/form/export-orders/",
        shopturbo.export_orders,
        name="export_orders",
    ),  # Workspace Updated
    path(
        "workflows/action/form/import-items/",
        shopturbo.import_items,
        name="import_items",
    ),  # Workspace Updated
    path(
        "workflows/action/form/export-items/", items.export_items, name="export_items"
    ),  # Workspace Updated
    path(
        "workflows/action/form/sync-item-and-inventory/",
        items_inventory.sync_item_and_inventory,
        name="sync_item_and_inventory",
    ),  # Workspace Updated
    path(
        "workflows/action/form/import-multiple-items/",
        shopturbo.import_multiple_items,
        name="import_multiple_items",
    ),  # Workspace Updated
    path(
        "workflows/action/form/import-messages/",
        messages.import_messages,
        name="import_messages",
    ),  # Workspace Updated
    path(
        "workflows/action/form/aggregate-invoice/",
        lambda request: commerce_functions.aggregate_commerce_action(
            request, object_type=TYPE_OBJECT_INVOICE
        ),
        name="aggregate_invoice",
    ),  # Workspace Updated
    path(
        "workflows/action/form/aggregate-order-to-invoice/",
        lambda request: commerce_functions.aggregate_commerce_action(
            request, object_type=TYPE_OBJECT_ORDER, convert_to=TYPE_OBJECT_INVOICE
        ),
        name="aggregate_order_to_invoice",
    ),
    path(
        "workflows/action/form/aggregate-delivery-note-to-invoice/",
        lambda request: commerce_functions.aggregate_commerce_action(
            # Workspace Updated
            request,
            object_type=TYPE_OBJECT_DELIVERY_NOTE,
        ),
        name="aggregate_delivery_note_to_invoice",
    ),
    path(
        "workflows/action/form/aggregate-estimate/",
        lambda request: commerce_functions.aggregate_commerce_action(
            request, object_type=TYPE_OBJECT_ESTIMATE
        ),
        name="aggregate_estimate",
    ),  # Workspace Updated
    path(
        "workflows/action/form/aggregate-order-to-purchase-order/",
        lambda request: commerce_functions.aggregate_commerce_action(
            request,
            object_type=TYPE_OBJECT_ORDER,
            convert_to=TYPE_OBJECT_PURCHASE_ORDER,
        ),
        name="aggregate_order_to_purchase_order",
    ),
    path(
        "workflows/action/form/create-dg-payment/",
        create_dg_payment,
        name="create_dg_payment",
    ),  # Workspace Updated
    path(
        "workflows/action/form/create-invoice-zengin-format/",
        create_invoice_zengin_format,
        name="create_invoice_zengin_format",
    ),  # Workspace Updated
    path(
        "workflows/action/form/convert-custom-property-price-to-estimate/",
        convert_custom_property_price_to_x.convert_custom_property_price_to_estimate,
        name="convert_custom_property_price_to_estimate",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-custom-property-price-to-invoice/",
        convert_custom_property_price_to_x.convert_custom_property_price_to_invoice,
        name="convert_custom_property_price_to_invoice",
    ),  # No Workflow
    path(
        "workflows/action/form/upload-wholesale-price/",
        contacts.upload_wholesale_price,
        name="upload_wholesale_price",
    ),  # Workspace Updated
    path(
        "workflows/action/form/create-new-purchase-order/",
        purchase_order.create_new_purchase_order,
        name="create_new_purchase_order",
    ),  # No Workflow
    path(
        "workflows/action/form/export-purchase-order/",
        purchase_order.export_purchase_order,
        name="export_purchase_order",
    ),
    path(
        "workflows/action/form/update-order-price/",
        orders.update_order_price,
        name="update_order_price",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-invoice-to-accounting-transaction/",
        commerce_actions.convert_invoice_to_accounting_transaction,
        name="convert_invoice_to_accounting_transaction",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-expense-to-accounting-transaction/",
        expense.convert_expense_to_accounting_transaction,
        name="convert_expense_to_accounting_transaction",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-subscription-to-accounting-transaction/",
        shopturbo.convert_subscription_to_accounting_transaction,
        name="convert_subscription_to_accounting_transaction",
    ),  # No Workflow
    path(
        "workflows/action/form/add-shipping-cost-to-order/",
        orders.add_shipping_cost_to_order,
        name="add_shipping_cost_to_order",
    ),  # No Workflow
    path(
        "workflows/action/form/contact-merge/",
        contact_merge_action.contact_merge_action,
        name="contact_merge",
    ),
    path(
        "workflows/action/form/create-bill-from-work-hours-and-rates/",
        bill.create_bill_from_work_hours_and_rates,
        name="create_bill_from_work_hours_and_rates",
    ),  # No Workflow
    path(
        "workflows/action/form/create-invoice-from-line-item-property/",
        commerce_functions.create_invoice_from_line_item_property,
        name="create_invoice_from_line_item_property",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-line-items-to-slips/",
        commerce_functions.convert_line_item_property_to_slips,
        name="convert_line_item_property_to_slips",
    ),  # No Workflow
    path(
        "workflows/action/form/send-emails-with-object-pdf/",
        commerce_functions.send_emails_with_object_pdf,
        name="send_emails_with_object_pdf",
    ),  # No Workflow
    path(
        "workflows/action/form/send-emails/",
        commerce_functions.send_emails_with_object_pdf,
        name="send_emails",
    ),  # No Workflow
    path(
        "workflows/action/form/send-emails-from-workflow/",
        commerce_functions.send_emails_from_workflow,
        name="send_emails_from_workflow",
    ),  # No Workflow
    path(
        "workflows/action/form/when-contract-completed/",
        contractwise.when_contract_completed,
        name="when_contract_completed",
    ),
    path(
        "workflows/action/form/convert-case-to-order/",
        convert_custom_property_price_to_x.convert_case_to_order,
        name="convert_case_to_order",
    ),  # No Workflow
    path(
        "workflows/action/form/convert-case-to-subscription/",
        convert_custom_property_price_to_x.convert_case_to_subscription,
        name="convert_case_to_subscription",
    ),  # No Workflow
    # Refactored Actions
    path(
        "workflows/action/form/record-updated/",
        actions.record_updated,
        name="record_updated",
    ),
    path(
        "workflows/action/form/record-updated-name/",
        actions.record_updated_name,
        name="record_updated_name",
    ),
    path(
        "workflows/action/form/record-created/",
        actions.record_created,
        name="record_created",
    ),
    path(
        "workflows/action/form/record-created-name/",
        actions.record_created_name,
        name="record_created_name",
    ),
    path(
        "workflows/action/form/create-record/",
        actions.create_record,
        name="create_record",
    ),
    path(
        "workflows/action/form/create-record-name/",
        actions.create_record_name,
        name="create_record_name",
    ),
    path(
        "workflows/action/form/create-action-name/",
        actions.create_action_name,
        name="create_action_name",
    ),
    path(
        "workflows/action/form/update-record/",
        actions.update_record,
        name="update_record",
    ),
    path(
        "workflows/action/form/update-record-name/",
        actions.update_record_name,
        name="update_record_name",
    ),
    path(
        "workflows/action/form/update-action-name/",
        actions.update_action_name,
        name="update_action_name",
    ),
    path(
        "workflows/action/form/aggregate-record/",
        actions.aggregate_record,
        name="aggregate_record",
    ),
    path(
        "workflows/action/form/aggregate-record-name/",
        actions.aggregate_record_name,
        name="aggregate_record_name",
    ),
    path(
        "workflows/action/form/aggregate-action-name/",
        actions.aggregate_action_name,
        name="aggregate_action_name",
    ),
    path(
        "workflows/action/form/convert-record/",
        actions.convert_record,
        name="convert_record",
    ),
    path(
        "workflows/action/form/convert-record-target/",
        actions.convert_record_target,
        name="convert_record_target",
    ),
    path(
        "workflows/action/form/convert-action-name/",
        actions.convert_action_name,
        name="convert_action_name",
    ),
    path(
        "workflows/action/form/create-picking-list-from-order/",
        actions.create_pickup_list_from_order_form,
        name="create_pickup_list_from_order_form",
    ),
    path(
        "workflows/action/form/export-data-to-integration/",
        actions.export_data_to_integration,
        name="export_data_to_integration",
    ),
    path(
        "workflows/action/form/export-integration-action-name/",
        actions.export_integration_action_name,
        name="export_integration_action_name",
    ),
    path(
        "workflows/action/form/import-data-from-integration/",
        actions.import_data_from_integration,
        name="import_data_from_integration",
    ),
    path(
        "workflows/action/form/import-integration-action-name/",
        actions.import_integration_action_name,
        name="import_integration_action_name",
    ),
    path("workflows/action/form/ai-actions/", actions.ai_actions, name="ai_actions"),
    path(
        "workflows/action/form/ai-action-name/",
        actions.ai_action_name,
        name="ai_action_name",
    ),
    path(
        "workflows/action/form/sync-actions/", actions.sync_actions, name="sync_actions"
    ),
    path(
        "workflows/action/form/sync-action-name/",
        util_actions.sync_action_name,
        name="sync_action_name",
    ),
    path("workflows/action/form/approval/", actions.approval, name="approval"),
    path(
        "workflows/action/form/create-stripe-subscription-link/",
        subscription_actions.create_stripe_subscription_link,
        name="create_stripe_subscription_link",
    ),
    path(
        "workflows/action/form/schedule-update-stripe-subscription/",
        subscription_actions.schedule_update_stripe_subscription,
        name="schedule_update_stripe_subscription",
    ),
    path(
        "workflows/action/form/create-stripe-order-payment-link/",
        order_actions.create_stripe_order_payment_link,
        name="create_stripe_order_payment_link",
    ),
    path(
        "workflows/action/form/aggregate-order-records/",
        aggregate_order_records,
        name="aggregate_order_records",
    ),
    path(
        "workflows/action/form/sync-stripe-order-payment-status/",
        order_stripe_actions.sync_stripe_order_payment_status,
        name="sync_stripe_order_payment_status",
    ),
    path(
        "workflows/action/form/import-stripe-invoices/",
        commerce_actions.import_stripe_invoices,
        name="import_stripe_invoices",
    ),
    path(
        "workflows/action/form/run-view-function/",
        actions.run_action_view_bg,
        name="run_action_view_bg",
    ),
    path("ai/run_action/", home.run_action, name="run_action"),
    path("ai/workflow/form/<uuid:id>/", home.workflow_form, name="workflow_form"),
    path("workflows/action/approval/", include(approval_urls)),
    # Integrations
    path("channel_wizard/", home.channel_wizard, name="channel_wizard"),
    path("channel_wizard/<uuid:id>/", home.update_channel, name="update_channel"),
    path("gmail_connect/", home.gmail_connect, name="gmail_connect"),
    path("gworkspace_connect/", home.gworkspace_connect, name="gworkspace_connect"),
    path("gdrive_connect/", home.gdrive_connect, name="gdrive_connect"),
    path("gcal_connect/", home.gcal_connect, name="gcal_connect"),
    path("stripe_connect/", home.stripe_connect, name="stripe_connect"),
    path("gmb_connect/", home.gmb_connect, name="gmb_connect"),
    path("bigquery_connect/", home.bigquery_connect, name="bigquery_connect"),
    # path('gdrive_connect/<uuid:channel_id>/', home.gdrive_connect, name='gdrive_connect'),
    path("ganalytics_connect/", home.ganalytics_connect, name="ganalytics_connect"),
    path("gsearch_connect/", home.gsearch_connect, name="gsearch_connect"),
    path("wordpress_connect/", home.wordpress_connect, name="wordpress_connect"),
    path("woocommerce_connect/", home.woocommerce_connect, name="woocommerce_connect"),
    path("sanka_pixel_connect/", home.sanka_pixel_connect, name="sanka_pixel_connect"),
    path("google_callback/", home.google_callback, name="google_callback"),
    path("twitter_connect/", home.twitter_connect, name="twitter_connect"),
    path("twitter_callback/", home.twitter_callback, name="twitter_callback"),
    path(
        "square_connect/", home.square_connect, name="square_connect"
    ),  # for production
    path(
        "square_callback/", home.square_callback, name="square_callback"
    ),  # for production
    path("shopify_connect/", home.shopify_connect, name="shopify_connect"),
    path("shopify_callback/", home.shopify_callback, name="shopify_callback"),
    path("shopify_webhook/", home.shopify_webhook, name="shopify_webhook"),
    path("hubspot_connect/", home.hubspot_connect, name="hubspot_connect"),
    path(
        "hubspot_connect_power_subscription/",
        home.hubspot_connect_power_subscription,
        name="hubspot_connect_power_subscription",
    ),
    path(
        "hubspot_connect_power_message/",
        home.hubspot_connect_power_message,
        name="hubspot_connect_power_message",
    ),
    path("hubspot_callback/", home.hubspot_callback, name="hubspot_callback"),
    path(
        "hubspot_callback_power_order/",
        home.hubspot_callback_power_order,
        name="hubspot_callback_power_order",
    ),
    path(
        "hubspot_callback_power_subscription/",
        home.hubspot_callback_power_subscription,
        name="hubspot_callback_power_subscription",
    ),
    path(
        "hubspot_callback_power_message/",
        home.hubspot_callback_power_message,
        name="hubspot_callback_power_message",
    ),
    path(
        "hubspot_order_connect/",
        home.hubspot_order_connect,
        name="hubspot_order_connect",
    ),
    path(
        "hubspot_order_callback",
        home.hubspot_order_callback,
        name="hubspot_order_callback",
    ),
    path(
        "hubspot_order_callback/",
        home.hubspot_order_callback,
        name="hubspot_order_callback",
    ),
    path(
        "hubspot_subscription_connect/",
        home.hubspot_subscription_connect,
        name="hubspot_subscription_connect",
    ),
    path(
        "hubspot_subscription_callback",
        home.hubspot_subscription_callback,
        name="hubspot_subscription_callback",
    ),
    path(
        "hubspot_subscription_callback/",
        home.hubspot_subscription_callback,
        name="hubspot_subscription_callback",
    ),
    path(
        "hubspot/users/<uuid:channel_id>/mapping/",
        hubspot.hubspot_load_mapping,
        name="hubspot_mapping",
    ),
    path(
        "hubspot/users/<uuid:channel_id>/mapping/save/",
        hubspot.hubspot_save_mapping,
        name="hubspot_save_mapping",
    ),
    path(
        "makeshop_webhook", account_makeshop.makeshop_webhook, name="makeshop_webhook"
    ),
    path(
        "makeshop_webhook/", account_makeshop.makeshop_webhook, name="makeshop_webhook"
    ),
    path("facebook_callback/", home.facebook_callback, name="facebook_callback"),
    path("tiktok_callback/", home.tiktok_callback, name="tiktok_callback"),
    path("eccube_callback/", home.eccube_callback, name="eccube_callback"),
    path("freee_callback/", home.freee_callback, name="freee_callback"),
    path("eccube_callback", home.eccube_callback, name="eccube_callback"),
    path("freee_callback", home.freee_callback, name="freee_callback"),
    path("yahoo_callback/", home.yahoo_callback, name="yahoo_callback"),
    path("yahoo_callback", home.yahoo_callback, name="yahoo_callback"),
    path("amazon_callback/", home.amazon_callback, name="amazon_callback"),
    path("amazon_callback", home.amazon_callback, name="amazon_callback"),
    path("salesforce_callback/", home.salesforce_callback, name="salesforce_callback"),
    path("salesforce_callback", home.salesforce_callback, name="salesforce_callback"),
    path("ebay_callback/", home.ebay_callback, name="ebay_callback"),
    path("ebay_callback", home.ebay_callback, name="ebay_callback"),
    path(
        "ebay_marketplace_account_deletion/",
        home.ebay_marketplace_account_deletion,
        name="ebay_marketplace_account_deletion",
    ),
    path(
        "ebay_marketplace_account_deletion",
        home.ebay_marketplace_account_deletion,
        name="ebay_marketplace_account_deletion",
    ),
    path(
        "moneyforward_callback/",
        home.moneyforward_callback,
        name="moneyforward_callback",
    ),
    path(
        "moneyforward_callback",
        home.moneyforward_callback,
        name="moneyforward_callback",
    ),
    path(
        "shopify_product_create/",
        home.shopify_product_create,
        name="shopify_product_create",
    ),
    path(
        "shopify_product_create",
        home.shopify_product_create,
        name="shopify_product_create",
    ),
    path(
        "shopify_product_delete/",
        home.shopify_product_delete,
        name="shopify_product_delete",
    ),
    path(
        "shopify_product_delete",
        home.shopify_product_delete,
        name="shopify_product_delete",
    ),
    # path('instagram_webhook/', home.instagram_webhook, name='instagram_webhook'),
    path(
        "twitter_update_integrations/<uuid:id>/",
        home.twitter_update_integrations,
        name="twitter_update_integrations",
    ),
    path(
        "instagram_update_integrations/<uuid:id>/",
        home.instagram_update_integrations,
        name="instagram_update_integrations",
    ),
    path(
        "facebook_update_integrations/<uuid:id>/",
        home.facebook_update_integrations,
        name="facebook_update_integrations",
    ),
    path(
        "wordpress_update_integrations/<uuid:id>",
        home.wordpress_update_integrations,
        name="wordpress_update_integrations",
    ),
    path(
        "woocommerce_update_integrations/<uuid:id>",
        home.woocommerce_update_integrations,
        name="woocommerce_update_integrations",
    ),
    path(
        "twilio_update_integration/<uuid:id>",
        home.twilio_update_integration,
        name="twilio_update_integration",
    ),
    path(
        "hubspot_update_integrations/<uuid:id>",
        home.hubspot_update_integration,
        name="hubspot_update_integrations",
    ),
    path(
        "freee_update_integrations/<uuid:id>",
        home.freee_update_integration,
        name="freee_update_integrations",
    ),
    path(
        "azure_update_integrations/<uuid:id>",
        home.azure_update_integration,
        name="azure_update_integration",
    ),
    path(
        "line_update_integrations/<uuid:id>",
        home.line_update_integration,
        name="line_update_integrations",
    ),
    path(
        "whatsapp_update_integrations/<uuid:id>",
        home.whatsapp_update_integration,
        name="whatsapp_update_integrations",
    ),
    path(
        "rakuten_update_integrations/<uuid:id>",
        home.rakuten_update_integration,
        name="rakuten_update_integrations",
    ),
    path(
        "salesforce_update_integrations/<uuid:id>",
        home.salesforce_update_integration,
        name="salesforce_update_integrations",
    ),
    path(
        "sanka_pixel_update_integration/<uuid:id>",
        home.sanka_pixel_update_integration,
        name="sanka_pixel_update_integration",
    ),
    path(
        "eccube_update_integration/<uuid:id>",
        home.eccube_update_integration,
        name="eccube_update_integration",
    ),
    path(
        "makeshop_update_integration/<uuid:id>",
        home.makeshop_update_integration,
        name="makeshop_update_integration",
    ),
    path(
        "yahoo_update_integration/<uuid:id>",
        home.yahoo_update_integration,
        name="yahoo_update_integration",
    ),
    path(
        "bcart_update_integration/<uuid:id>",
        home.bcart_update_integration,
        name="bcart_update_integration",
    ),
    path(
        "amazon_update_integration/<uuid:id>",
        home.amazon_update_integration,
        name="amazon_update_integration",
    ),
    path(
        "qrbot_update_integration/<uuid:id>",
        home.qrbot_update_integration,
        name="qrbot_update_integration",
    ),
    path(
        "temu_update_integration/<uuid:id>",
        home.temu_update_integration,
        name="temu_update_integration",
    ),
    path(
        "smtp_update_integration/<uuid:id>/",
        smtp_integration.smtp_update_integration,
        name="smtp_update_integration",
    ),
    # Notifications
    path("notifications/", home.notifications, name="notifications"),
    path("notifications/<int:page>/", home.notifications, name="notifications"),
    path(
        "notifications/number/", home.notifications_number, name="notifications_number"
    ),
    path(
        "notifications/del/<str:id>",
        home.delete_notification,
        name="delete_notification",
    ),
    path(
        "notifications/del/bulk/",
        home.bulk_delete_notifications,
        name="bulk_delete_notifications",
    ),
    path(
        "notifications/direct/", home.direct_notifications, name="direct_notifications"
    ),
    # Widget
    path("widget/", widget.widgets, name="widgets"),
    path(
        "widget/<str:solution_slug>", widget.solution_widgets, name="solution_widgets"
    ),
    # Job
    path("job/", jobs.jobs, name="jobs"),
    path("job/new/", jobs.new_edit_job, name="new_job"),
    path("job/load_drawer/", jobs.load_drawer, name="job_load_drawer"),
    path("job/<uuid:id>/", jobs.job, name="get_job"),
    path("job/delete/<uuid:id>/", jobs.job, name="delete_job"),
    path("job/image/<id>/", jobs.job_img, name="upload_job_img"),
    path("job/image/delete/<uuid:id>/", jobs.job_img, name="delete_job_img"),
    path("job/edit/<uuid:id>/", jobs.new_edit_job, name="edit_job"),
    path("job/applicants/new/", jobs.new_applicant, name="new_applicant"),
    path("job/applicants/<int:id>/", jobs.applicant_detail, name="applicant_detail"),
    path(
        "job/applicants/delete/<int:id>/",
        jobs.delete_applicant,
        name="delete_applicant",
    ),
    path(
        "job/generate/desc/",
        jobs.generate_job_description,
        name="generate_job_description",
    ),
    path("job/generate/job/", jobs.generate_job, name="generate_job"),
    path("job/applicants/pull/", jobs.job_applicants_pull, name="job_applicants_pull"),
    path(
        "job/applicants/send_mail/",
        jobs.send_email_to_job_applicants,
        name="job_applicants_send_mail",
    ),
    path(
        "job/applicants/view/<uuid:job_id>/",
        jobs.job_applicants,
        name="job_applicants_view",
    ),
    path("job/interview/view/<uuid:job_id>/", jobs.job_interview, name="job_interview"),
    path(
        "job/interview/submit_interview/",
        jobs.submit_interview,
        name="submit_interview",
    ),
    path("job/scorecard/view/<uuid:job_id>/", jobs.job_scorecard, name="job_scorecard"),
    path(
        "job/scorecard/submit_scorecard/",
        jobs.submit_scorecard,
        name="submit_scorecard",
    ),
    path("job/settings/", jobs.app_settings, name="job_settings"),
    # Contacts
    path("contacts/", contacts.contacts, name="contacts"),
    path(
        "contacts/row/<uuid:id>", contacts.contact_row_detail, name="contact_row_detail"
    ),
    path(
        "contacts/request_sync_contact/",
        contacts.request_sync_contact,
        name="request_sync_contact",
    ),
    path("contacts/lists/", contact_list.contact_lists, name="contact_lists"),
    path(
        "contacts/new/",
        create_contacts_workflow.create_contacts_workflow,
        name="create_contacts_workflow",
    ),
    path("contacts/create/", contacts.create_contacts, name="create_contacts"),
    path(
        "contacts/options/", contacts.get_customer_options, name="get_customer_options"
    ),
    path(
        "campaign/options/",
        campaigns.get_sendgrid_sender_options,
        name="get_sendgrid_sender_options",
    ),
    path(
        "campaign/sync_senders/",
        campaigns.sync_sendgrid_senders,
        name="sync_sendgrid_senders",
    ),
    path(
        "contacts/drawer/",
        contact_drawer.load_drawer,
        name="new_customerlinkapp_drawer",
    ),
    path(
        "contacts/drawer/sync_contacts/",
        contacts.load_drawer_sync_contacts,
        name="load_drawer_sync_contacts",
    ),
    path(
        "contacts/default_conditions_model/",
        contacts.default_conditions_model,
        name="default_conditions_model",
    ),
    path(
        "contacts/delete/<uuid:id>/",
        edit_delete_profile.delete_explore_profile,
        name="delete_contact",
    ),
    path(
        "contacts/update/<uuid:id>/",
        edit_delete_profile.update_explore_profile,
        name="update_contact",
    ),
    path(
        "contacts/api/bussiness_card/",
        contacts.business_card_reader,
        name="business_card_reader",
    ),
    path(
        "contacts/api/csv_header_extractor/",
        contacts.csv_header_extractor,
        name="csv_header_extractor",
    ),
    path(
        "contacts/api/sync_header_extractor/",
        contacts.sync_header_extractor,
        name="sync_header_extractor",
    ),
    path(
        "contacts/api/get_salesforce_field/",
        contacts.get_salesforce_field,
        name="get_salesforce_field",
    ),
    path(
        "contacts/api/sync_header_extractor_page/",
        contacts.sync_header_extractor_page,
        name="sync_header_extractor_page",
    ),
    path(
        "contacts/api/sync_hubspot_list/",
        contact_list.sync_hubspot_list,
        name="sync_hubspot_list",
    ),
    path(
        "contacts/api/table_mapping_url/",
        contacts.table_mapping_url,
        name="table_mapping_url",
    ),
    path(
        "contacts/api/sync_table_mapping_url/",
        contacts.sync_table_mapping_url,
        name="sync_table_mapping_url",
    ),
    path(
        "contacts/phone/<uuid:id>/",
        contacts.get_contact_phone_number,
        name="get_contact_phone_number",
    ),
    path(
        "contacts/form/update/<uuid:id>/",
        contact_notes.contacts_update_notes_form,
        name="contacts_notes_form_update",
    ),
    path(
        "contacts/form/clear/",
        contact_notes.contacts_update_notes_form,
        name="contacts_notes_form_clear",
    ),
    path(
        "contacts/contacts/form/",
        contact_notes.contacts_notes_form,
        name="contacts_notes_form",
    ),
    path(
        "contacts/association/drawer/",
        contacts.contact_association_drawer,
        name="contact_association_drawer",
    ),
    path(
        "customer/costumer/item/price",
        contacts.customer_item_price,
        name="customer_item_price",
    ),
    path(
        "customer/item/price-table/checking",
        contacts.price_table_checking,
        name="price_table_checking",
    ),
    path(
        "customer/fetch_customer_points",
        contacts.fetch_customer_points,
        name="fetch_customer_points",
    ),
    path(
        "workflows/action/form/add-to-contact-list/",
        # Workspace Updated
        contacts.add_to_contact_list,
        name="add_to_contact_list",
    ),
    # Companies
    path("companies/", companies.companies, name="companies"),
    path(
        "companies/row/<uuid:id>/",
        contacts.company_row_detail,
        name="company_row_detail",
    ),
    path("companies/create/", contacts.create_companies, name="create_companies"),
    path(
        "companies/delete/<uuid:id>/",
        edit_delete_profile.delete_explore_profile,
        name="delete_company",
    ),
    path(
        "companies/update/<uuid:id>/",
        edit_delete_profile.update_explore_profile,
        name="update_company",
    ),
    path(
        "companies/form/update/<uuid:id>/",
        contacts.company_update_notes_form,
        name="company_notes_form_update",
    ),
    path(
        "companies/form/clear/",
        contacts.company_update_notes_form,
        name="company_notes_form_clear",
    ),
    path("companies/form/", contacts.company_notes_form, name="company_notes_form"),
    path(
        "companies/upload_img/",
        contacts.company_notes_form,
        name="company_notes_upload_img",
    ),
    path(
        "companies/association/drawer/",
        companies.company_association_drawer,
        name="company_association_drawer",
    ),
    path(
        "customers/update_association/<uuid:id>/",
        customer_update_association_object.customer_update_association_object,
        name="customer_update_association_object",
    ),
    path("cases/", cases.deals, name="service_deals"),
    path("cases/create", cases.create_deals, name="service_create_deals"),
    path("cases/row/<uuid:id>", cases.case_row_detail, name="case_row_detail"),
    path("cases/manage/<uuid:id>/", cases.manage_deals, name="service_manage_deals"),
    path(
        "cases/form/update/<uuid:id>/",
        cases.case_update_notes_form,
        name="case_notes_form_update",
    ),
    path(
        "cases/form/clear/", cases.case_update_notes_form, name="case_notes_form_clear"
    ),
    path("cases/form/", cases.case_notes_form, name="case_notes_form"),
    path("cases/<uuid:id>/events/", cases.get_cases_events, name="get_cases_events"),
    path("cases/options/", cases.get_cases_options, name="get_cases_options"),
    path(
        "cases/association/drawer/",
        cases.case_association_drawer,
        name="case_association_drawer",
    ),
    path(
        "contacts/request_sync_company/",
        contacts.request_sync_company,
        name="request_sync_company",
    ),
    path("cases/drawer/", cases.load_drawer, name="case_load_drawer"),
    path(
        "customers/delete_list/<uuid:id>/", contact_list.delete_list, name="delete_list"
    ),
    path(
        "customers/update_list/<uuid:id>/", contact_list.update_list, name="update_list"
    ),
    path(
        "customers/v/profile_detail/<uuid:id>/",
        contacts.load_explore_profile,
        name="load_explore_profile",
    ),
    path(
        "customers/v/company_detail/<uuid:id>/",
        contacts.load_explore_company,
        name="load_explore_company",
    ),
    path("customers/settings/", contacts.app_settings, name="customerlink_settings"),
    path("customers/activity-log/", contacts.activity_log_view, name="activity_log"),
    path(
        "customers/settings/kanban/board/",
        cases.kanban_board_api,
        name="kanban_board_api",
    ),
    path(
        "customers/settings/kanban/status/",
        cases.kanban_status_api,
        name="kanban_status_api",
    ),
    path("messages/", messages.inbox, name="contactline"),
    path(
        "messages/view/drawer",
        messages.message_view_drawer,
        name="conversation_view_drawer",
    ),
    path("messages/<uuid:id>", messages.message_detail, name="message_detail"),
    path("messages/status/", messages.thread_status, name="thread_status"),
    path("messages/assignee/", messages.thread_assignee, name="thread_assignee"),
    path("messages/send/<uuid:id>", messages.send_message, name="send_message"),
    path("messages/save/", messages.save_message, name="save_message"),
    path("messages/send/bulk/", messages.send_bulk_message, name="send_bulk_message"),
    path(
        "messages/sync/",
        messages.sync_conversation_drawer,
        name="sync_conversation_drawer",
    ),
    path(
        "messages/profile_messages/<uuid:id>/",
        messages.load_message_profile,
        name="load_message_profile",
    ),
    path("messages/review/<uuid:id>/", messages.review_message, name="review_message"),
    path("messages/delete/<uuid:id>/", messages.delete_message, name="delete_message"),
    path(
        "messages/templates/",
        message_templates.message_templates,
        name="message_templates",
    ),
    path(
        "messages/templates/create/", messages.create_templates, name="create_template"
    ),
    path(
        "messages/templates/<uuid:id>/delete/",
        messages.delete_template,
        name="delete_template",
    ),
    path(
        "messages/recorded-events/",
        messages.recorded_events_view,
        name="recorded_events",
    ),
    path("messages/settings/", messages.app_settings, name="messagerunner_settings"),
    path(
        "messages/load_template_drawer/<str:id>/",
        messages.load_template_drawer,
        name="load_template_drawer",
    ),
    path("call/outgoing/", messages.outgoing_call, name="outgoing_call"),
    path(
        "call/client/<str:status>/",
        messages.client_status_toggle,
        name="client_status_toggle",
    ),
    path(
        "call/conf/add/",
        messages.add_conference_participant,
        name="add_conference_participant",
    ),
    path("messages/bots/", messages.bots, name="bots"),
    path(
        "messages/bots/form/file/",
        messages.extract_bot_knowledge_file,
        name="extract_bot_knowledge_file",
    ),
    path("messages/bots/form/", messages.bot_form, name="bot_form"),
    path("messages/bots/test/", messages.test_bot, name="test_bot"),
    path(
        "chatbot/<uuid:bot_id>/thread/", chatbot.chatbot_thread, name="chatbot_thread"
    ),
    path(
        "messages/form/update/<uuid:id>/",
        messages.message_update_notes_form,
        name="message_notes_form_update",
    ),
    path("messages/form/", messages.message_notes_form, name="message_notes_form"),
    # line_item
    path("line_item/load_item_row/", line_item.load_item_row, name="load_item_row"),
    # Marketing
    path("campaigns/", campaigns.campaigns, name="campaigns"),
    path("campaigns/", campaigns.campaigns, name="emails"),
    path("campaigns/calendar", campaigns.event_api, name="studio_calendar"),
    path(
        "campaigns/<content_type>", campaigns.campaigns, name="campaigns"
    ),  # with content type
    path("campaigns/load_more/", campaigns.load_more, name="load_more"),
    path(
        "campaigns/review/<uuid:id>/",
        campaigns.get_review_social_post_drawer,
        name="get_review_social_post_drawer",
    ),
    path("campaigns/review/bulk/", campaigns.delete_content, name="bulk_post"),
    path("campaigns/new_campaign/", campaigns.new_campaign, name="new_campaign"),
    path(
        "campaigns/delete_content/<id>/",
        campaigns.delete_content,
        name="delete_content",
    ),
    path("campaign/file/upload/", campaigns.file_upload, name="file_upload"),
    path("campaign/file/sort/", campaigns.file_sorting, name="file_sorting"),
    path(
        "campaign/load_create_drawer/",
        campaigns.load_create_drawer,
        name="load_create_drawer",
    ),
    path(
        "campaign/send_email/",
        send_email_campaign.send_email_request,
        name="send_email_post",
    ),
    path(
        "campaign/send_email/template/",
        send_email_campaign.send_email_template,
        name="send_email_template",
    ),
    path(
        "campaign/send_email/set_template/",
        send_email_campaign.send_email_template,
        name="get_email_template",
    ),
    path(
        "campaign/send_email/exclude_list/<uuid:id>/",
        send_email_campaign.send_email_contact_exclude,
        name="send_email_contact_exclude",
    ),
    path(
        "campaign/send_email/respose_list/<uuid:id>/",
        send_email_campaign.send_email_contact_response,
        name="send_email_contact_response",
    ),
    path(
        "campaign/send_email/export_exclude_list/<uuid:id>/",
        send_email_campaign.export_email_contact_exclude,
        name="export_email_contact_exclude",
    ),
    path(
        "campaign/send_email/edit/<uuid:id>/",
        send_email_campaign.send_email_request,
        name="get_review_emails_drawer",
    ),
    path(
        "campaign/send_email/update/",
        send_email_campaign.send_email_request,
        name="set_review_emails_drawer",
    ),
    path(
        "campaign/code_snippet/form/",
        code_snippet_campaign.campaign_code_snippet,
        name="campaign_code_snippet",
    ),
    path(
        "campaign/code_snippet/form/leave-popup/",
        code_snippet_campaign.leave_popup_code_snippet,
        name="leave_popup_code_snippet",
    ),
    path("campaign/settings/", campaigns.app_settings, name="campaignscale_settings"),
    path("campaign/content/create/", campaigns.content_create, name="content_create"),
    path(
        "campaign/content/review/<uuid:id>/",
        campaigns.get_review_content_drawer,
        name="get_review_content_drawer",
    ),
    path(
        "campaign/request_sync_email/",
        campaigns.request_sync_email,
        name="request_sync_email",
    ),
    path(
        "campaign/api/load_field/",
        campaigns.load_campaign_field,
        name="load_campaign_field",
    ),
    path(
        "campaign/api/load_campaign_relates/",
        campaigns.load_campaign_relates,
        name="load_campaign_relates",
    ),
    path(
        "campaign/assistant/ask", campaigns.ask_assistant, name="campaign_ask_assistant"
    ),
    path(
        "campaign/details/<uuid:id>/",
        campaigns.manage_ad_post,
        name="campaign_manage_ad_post",
    ),
    path(
        "hubspot/power_message/line_chat/",
        home.hubspot_power_message_line_chat,
        name="hubspot_power_message_line_chat",
    ),
    path(
        "hubspot/power_contract/",
        home.hubspot_contract_handler,
        name="hubspot_power_contract_handler",
    ),
    path(
        "hubspot/power_crm/table/",
        home.hubspot_power_crm_handler,
        {"type_card": "table"},
        name="hubspot_power_crm_table",
    ),
    path(
        "hubspot/crm_card/",
        home.hubspot_crm_card_handler,
        name="hubspot_crm_card_handler",
    ),
    # Time
    path("attendance/", timegenie.timegenie, name="timegenie"),
    path("attendance/settings/", timegenie.app_settings, name="timegenie_settings"),
    path(
        "attendance/view/drawer",
        timegenie.timegenie_view_drawer,
        name="timegenie_view_drawer",
    ),
    path(
        "attendance/control/<uuid:id>/",
        timegenie.timegenie_control,
        name="timegenie_control",
    ),
    path(
        "attendance/create/track/", timegenie.timegenie_create, name="timegenie_create"
    ),
    path(
        "attendance/manage/track/<uuid:id>/",
        timegenie.timegenie_manage_track,
        name="timegenie_manage_track",
    ),
    path(
        "attendance/create/subtrack/",
        timegenie.timegenie_create_subtrack,
        name="timegenie_create_subtrack",
    ),
    path(
        "attendance/manage/subtrack/<uuid:id>/",
        timegenie.timegenie_manage_subtrack,
        name="timegenie_manage_subtrack",
    ),
    path(
        "attendance/track/delete/<uuid:id>/",
        timegenie.timegenie_track_delete,
        name="timegenie_track_delete",
    ),
    path(
        "attendance/subtrack/delete/<uuid:id>/",
        timegenie.timegenie_subtrack_delete,
        name="timegenie_subtrack_delete",
    ),
    path(
        "attendance/bulk_attendance/",
        timegenie.timegenie_update,
        name="bulk_attendance",
    ),
    path(
        "attendance/load_drawer/", timegenie.load_drawer, name="timegenie_load_drawer"
    ),
    path(
        "attendance/calendar/", timegenie.timegenie_calendar, name="timegenie_calendar"
    ),
    path(
        "attendance/hubspot_attendance_fields_mapping_url/",
        timegenie.hubspot_attendance_fields_mapping_url,
        name="hubspot_attendance_fields_mapping_url",
    ),
    path(
        "attendance/hubspot_attendance_save_properties_mapping/",
        timegenie.hubspot_attendance_save_properties_mapping,
        name="hubspot_attendance_save_properties_mapping",
    ),
    path(
        "attendance/form_map_hubspot_attendance_object_name/",
        timegenie.form_map_hubspot_attendance_object_name,
        name="form_map_hubspot_attendance_object_name",
    ),
    path(
        "attendance/save_mapped_hubspot_attendance_object_name/",
        timegenie.save_mapped_hubspot_attendance_object_name,
        name="save_mapped_hubspot_attendance_object_name",
    ),
    path(
        "attendance/create_bulk_properties_hubspot_attendance/",
        timegenie.create_bulk_properties_hubspot_attendance,
        name="create_bulk_properties_hubspot_attendance",
    ),
    path(
        "attendance/patch_properties_hubspot_attendances/",
        timegenie.patch_properties_hubspot_attendances,
        name="patch_properties_hubspot_attendances",
    ),
    path(
        "attendance/export_attendance_to_hubspot/",
        timegenie.export_attendance_to_hubspot,
        name="export_attendance_to_hubspot",
    ),
    path(
        "attendance/import_attendance_to_hubspot/",
        timegenie.import_attendance_to_hubspot,
        name="import_attendance_to_hubspot",
    ),
    path(
        "attendance/options/",
        timegenie.get_attendance_options,
        name="get_attendance_options",
    ),
    path(
        "attendance/tracker_table_mapping_url/",
        timegenie.timegenie_table_mapping_url,
        name="timegenie_table_mapping_url",
    ),
    path(
        "attendance/tracker_csv_header_extractor/",
        timegenie.timegenie_csv_header_extractor,
        name="timegenie_csv_header_extractor",
    ),
    path(
        "attendance/tracker_csv_bulk_create/",
        timegenie.timegenie_csv_bulk_create,
        name="timegenie_csv_bulk_create",
    ),
    path(
        "attendance/timegenie_timetracker_toggle/",
        timegenie.timegenie_timetracker_toggle,
        name="timegenie_timetracker_toggle",
    ),
    path(
        "attendance/<uuid:id>/",
        timegenie.row_detail,
        {"object_type": TYPE_OBJECT_TIMEGENIE},
        name="timegenie_row_detail",
    ),
    # Forms
    path(
        "forms/", formroom.formroom, {"object_type": TYPE_OBJECT_FORM}, name="formroom"
    ),
    path("forms/settings", formroom.form_settings, name="form_settings"),
    path("forms/manage/<uuid:id>", formroom.form_manage, name="form_manage"),
    path("forms/<uuid:id>", formroom.form_entry, name="form_entry"),
    path(
        "forms/<uuid:id>/question/sorter",
        formroom.form_questions_sorter,
        name="form_questions_sorter",
    ),
    path("forms/form_view_drawer/", formroom.form_view_drawer, name="form_view_drawer"),
    # CSV Upload
    path("csv/upload/type/", csv_upload.master_csv_upload, name="csv_upload"),
    path(
        "csv/upload/csv_upload_submit/",
        csv_upload.csv_upload_submit,
        name="csv_upload_submit",
    ),
    path(
        "csv/upload/variable_stored/",
        csv_upload.variable_stored,
        name="variable_stored",
    ),
    # Item Objects
    path("items/", items.shopturbo_items, name="shopturbo_items"),
    path("items/create", items.create_items, name="shopturbo_create_items"),
    path(
        "items/row/<uuid:id>/",
        items.shopturbo_item_detail,
        name="shopturbo_item_row_detail",
    ),
    path(
        "items/row/column/",
        items.shopturbo_column_detail,
        name="shopturbo_item_row_column_detail",
    ),
    path(
        "items/row/group/",
        items.shopturbo_item_group_row,
        name="shopturbo_item_group_row",
    ),
    path(
        "items/row/group/cascade_dropdown/<uuid:id>/",
        items.shopturbo_cascade_dropdown_parent,
        name="shopturbo_cascade_dropdown_parent",
    ),
    path(
        "items/row/warehouse_inventory_amount/",
        inventory_warehouse.warehouse_inventory_amount,
        name="warehouse_inventory_amount",
    ),
    path("items/manage/<uuid:id>/", items.manage_items, name="shopturbo_manage_items"),
    path("items/preview/", items.item_pdf_download, name="item_pdf_preview"),
    path(
        "items/drawer/single-entry/",
        items_drawer.items_single_entry_drawer,
        name="items_single_entry_drawer",
    ),
    path(
        "items/drawer/bulk-entry/",
        items_drawer.items_bulk_entry_drawer,
        name="items_bulk_entry_drawer",
    ),
    path(
        "items/drawer/prices/",
        items_drawer_prices.items_prices_drawer,
        name="items_prices_drawer",
    ),
    path(
        "items/drawer/prices/page/",
        items_drawer_prices.items_prices_page_drawer,
        name="items_prices_page_drawer",
    ),
    path(
        "items/drawer/prices/page-partial/",
        items_drawer_prices.items_prices_page_partial_drawer,
        name="items_prices_page_partial_drawer",
    ),
    path(
        "items/drawer/prices/volume-discount/",
        items_drawer_prices.items_prices_volume_discount_drawer,
        name="items_prices_volume_discount_drawer",
    ),
    path(
        "items/drawer/prices/update/",
        items_drawer_prices.items_update_price,
        name="items_update_price",
    ),
    path(
        "items/drawer/prices/delete/",
        items_drawer_prices.items_update_price_delete,
        name="items_update_price_delete",
    ),
    path(
        "items/drawer/prices/volume-update/",
        items_drawer_prices.items_update_volume_price,
        name="items_update_volume_price",
    ),
    # Inventory Objects
    path(
        "items/drawer/purchase-price/",
        items_inventory.item_purchase_prices,
        name="item_purchase_prices",
    ),
    path("items/options/", items_inventory.item_options, name="item_options"),
    path(
        "inventory_items/options/",
        items_inventory.inventory_items_options,
        name="inventory_items_options",
    ),
    path(
        "items/<uuid:id>/purchase_price/",
        items_inventory.item_purchase_price,
        name="item_purchase_price",
    ),
    path(
        "items/purchase_price/currency/options/",
        items_inventory.purchase_price_currency_options,
        name="purchase_price_currency_options",
    ),
    path(
        "items/get-export-import-item-configuration/",
        items_inventory.get_export_import_item_configuration,
        name="get_export_import_item_configuration",
    ),
    path(
        "items/get-count-of-items-by-filter/",
        items.get_count_of_items_by_filter,
        name="get_count_of_items_by_filter",
    ),
    path(
        "inventory/create/",
        items_inventory.shopturbo_create_inventory,
        name="shopturbo_create_inventory",
    ),
    path("inventory/", items_inventory.shopturbo_inventory, name="shopturbo_inventory"),
    path(
        "inventory/row/<uuid:id>",
        items_inventory.inventory_detail,
        name="inventory_detail",
    ),
    path(
        "inventory/<str:inventory_id>/item/",
        items_inventory.get_inventory_item,
        name="get_inventory_item",
    ),
    path(
        "inventory/actions/drawer/",
        items_inventory.inventory_bulk_action_drawer,
        name="inventory_bulk_action_drawer",
    ),
    path(
        "inventory/options/",
        items_inventory.inventory_options,
        name="inventory_options",
    ),
    path(
        "inventory/components/",
        items_inventory.inventory_components,
        name="inventory_components",
    ),
    path(
        "inventory/manage/<uuid:id>/",
        items_inventory.manage_inventory,
        name="shopturbo_manage_inventory",
    ),
    path(
        "inventory/options/",
        items_inventory.get_inventory_object_options,
        name="get_inventory_object_options",
    ),
    path(
        "inventory/association/drawer/",
        items_inventory.inventory_association_drawer,
        name="inventory_association_drawer",
    ),
    path(
        "inventory_transactions/",
        inventory_transactions.shopturbo_inventory_transactions,
        name="shopturbo_inventory_transactions",
    ),
    path(
        "inventory_transactions/create/",
        create_inventory_stock.shopturbo_create_inventory_stock,
        name="shopturbo_create_inventory_stock",
    ),
    path(
        "inventory_transactions/select_price/",
        items_inventory.shopturbo_create_inventory_transaction_select_price,
        name="shopturbo_create_inventory_transaction_select_price",
    ),
    path(
        "inventory_transactions/date-input/",
        inventory_transaction_date_input.inventory_transaction_date_input,
        name="inventory_transaction_date_input",
    ),
    path(
        "inventory_transactions/date-input/<uuid:inventory_id>/",
        inventory_transaction_date_input.inventory_transaction_date_input,
        name="inventory_transaction_date_input",
    ),
    path(
        "inventory_transactions/options/",
        items_inventory.inventory_transactions_options,
        name="inventory_transactions_options",
    ),
    path(
        "inventory_transactions/move/",
        items_inventory.generate_move_to_inventory,
        name="shopturbo_move_inventory",
    ),
    path(
        "inventory_transactions/create/drawer/",
        items_inventory.create_transaction_form,
        name="create_transaction_form",
    ),
    path(
        "inventory_transactions/order/association/drawer/",
        items_inventory.create_transaction_form,
        name="create_transaction_form",
    ),
    path(
        "inventory_transactions/manage/<uuid:id>/",
        items_inventory.manage_inventory_transaction,
        name="shopturbo_manage_inventory_transaction",
    ),
    path(
        "inventory_transactions/scan/",
        inventory_transaction_barcode.inventory_transaction_barcode_scan,
        name="inventory_transaction_barcode_scan",
    ),
    path(
        "locations/",
        items_inventory.shopturbo_inventory_warehouse,
        name="shopturbo_inventory_warehouse",
    ),
    path(
        "locations/options/",
        items_inventory.inventory_warehouse_options,
        name="inventory_warehouse_options",
    ),
    path(
        "locations/shopify/sync/map/",
        items_inventory.shopify_sync_map_locations,
        name="shopify_sync_map_locations",
    ),
    path(
        "inventory/stock",
        items_inventory.calculate_inventory_stock,
        name="calculate_inventory_stock",
    ),
    path(
        "locations/create/",
        items_inventory.shopturbo_create_inventory_warehouse,
        name="shopturbo_create_inventory_warehouse",
    ),
    path(
        "manage/locations/<uuid:id>/",
        items_inventory.manage_inventory_warehouse,
        name="shopturbo_manage_inventory_warehouse",
    ),
    path(
        "manage/associated/locations/<str:source_object_type>/<uuid:object_id>",
        location.get_associated_locations,
        name="get_associated_locations",
    ),
    # Order Objects
    path("orders/", orders.shopturbo_orders, name="shopturbo"),
    path(
        "orders/dynamic_calculations",
        order_dynamic_calcuations.shopturbo_order_dynamic_calculations,
        name="shopturbo_order_dynamic_calculations",
    ),
    path(
        "orders/subscription_dynamic_calculations",
        order_dynamic_calcuations.shopturbo_subscription_dynamic_calculations,
        name="shopturbo_subscription_dynamic_calculations",
    ),
    path(
        "orders/row/<uuid:id>",
        shopturbo.shopturbo_order_detail,
        name="shopturbo_order_row_detail",
    ),
    path("orders/<uuid:id>/", orders.shopturbo_orders, name="open_order"),
    path(
        "orders/create/",
        create_order.shopturbo_create_orders,
        name="shopturbo_create_orders",
    ),
    path(
        "orders/pdf/download",
        order_pdf_download.order_pdf_download,
        name="order_pdf_download",
    ),  # Preview PDF Template
    path(
        "orders/association/drawer/",
        shopturbo.orders_association_drawer,
        name="orders_association_drawer",
    ),
    path(
        "orders/purchase-orders/association/drawer/",
        shopturbo.purchase_orders_to_order_drawer,
        name="purchase_orders_to_order_drawer",
    ),
    path(
        "orders/purchase-orders/association/",
        shopturbo.purchase_orders_to_order_association,
        name="purchase_orders_to_order_association",
    ),
    path(
        "orders/cases/association/drawer/",
        cases.cases_to_order_association_drawer,
        name="cases_to_order_association_drawer",
    ),
    path(
        "orders/options/",
        shopturbo.get_order_object_options,
        name="get_order_object_options",
    ),
    path("orders/load_drawer/", shopturbo.load_drawer, name="shopturbo_load_drawer"),
    path(
        "orders/load_drawer/orders/",
        orders.load_drawer_orders,
        name="load_drawer_orders",
    ),
    path(
        "orders/load_drawer/create/",
        orders_drawer.create_drawer,
        name="load_create_order_drawer",
    ),
    path(
        "orders/load_drawer/manage/",
        orders_drawer.manage_drawer,
        name="load_manage_order_drawer",
    ),
    path(
        "orders/load_drawer/add_item_id_barcode_query/",
        orders_drawer.add_item_id_barcode_query,
        name="add_item_id_barcode_query",
    ),
    path(
        "orders/load_drawer/create_contact_from_order/",
        orders_drawer.create_contact_from_order,
        name="create_contact_from_order",
    ),
    path(
        "orders/load_drawer/create_company_from_order/",
        orders_drawer.create_company_from_order,
        name="create_company_from_order",
    ),
    path(
        "orders/manage/<uuid:id>/",
        manage_order.manage_orders,
        name="shopturbo_manage_orders",
    ),
    path(
        "orders/<uuid:order_id>/events/",
        shopturbo.get_order_events,
        name="get_order_events",
    ),
    path("orders/options/", shopturbo.orders_options, name="orders_options"),
    path("orders/line-items/", orders.order_line_items, name="order_line_items"),
    path("orders/settings/", shopturbo.app_settings, name="shopturbo_settings"),
    path(
        "orders/api/kanban/board/",
        shopturbo.kanban_shopturbo_board_api,
        name="kanban_shopturbo_board_api",
    ),
    path(
        "orders/api/kanban/status/",
        shopturbo.kanban_shopturbo_status_api,
        name="kanban_shopturbo_status_api",
    ),
    path(
        "orders/api/shopturbo_api_formula",
        shopturbo.shopturbo_api_formula,
        name="shopturbo_api_formula",
    ),
    path(
        "orders/api/shopturbo_item_api_formula",
        item_api.shopturbo_item_api_formula,
        name="shopturbo_item_api_formula",
    ),
    path("orders/csv_id_upload", shopturbo.csv_id_upload, name="csv_id_upload"),
    path(
        "orders/integration-import/",
        order_integration_import.import_integration_orders,
        name="import_integration_orders",
    ),
    path(
        "orders/stripe-payment-status/update-property/",
        order_integration_import.stripe_order_property_update,
        name="stripe_order_property_update",
    ),
    path(
        "orders/update_association/<uuid:id>/",
        order_update_association_object.order_update_association_object,
        name="order_update_association_object",
    ),
    path("commerce/settings/", shopturbo.app_settings, name="shopturbo_settings"),
    path(
        "commerce/settings/data/",
        shopturbo.get_order_settings_data,
        name="get_order_settings_data",
    ),
    path(
        "commerce/settings/shipping/",
        shopturbo.get_shipping_costs_data,
        name="get_shipping_costs_data",
    ),
    path(
        "commerce/settings/taxes/",
        shopturbo.get_order_taxes_data,
        name="get_order_taxes_data",
    ),
    path(
        "orders/get_import_order_configuration/",
        shopturbo.get_import_order_configuration,
        name="get_import_order_configuration",
    ),
    # Subscription Objects
    path(
        "subscriptions/",
        subscriptions.shopturbo_subscriptions,
        name="shopturbo_subscriptions",
    ),
    path(
        "subscriptions/create/",
        create_subscriptions.shopturbo_create_subscriptions,
        name="shopturbo_create_subscriptions",
    ),
    path(
        "subscriptions/<uuid:id>/",
        subscriptions.shopturbo_subscriptions,
        name="open_subscription",
    ),
    path(
        "subscriptions/manage/<uuid:id>/",
        manage_subscriptions.shopturbo_manage_subscriptions,
        name="shopturbo_manage_subscriptions",
    ),
    path(
        "subscriptions/manage",
        subscriptions.shopify_manage_subscriptions,
        name="shopify_manage_subscriptions",
    ),
    path(
        "subscriptions/api/export_subscription_to_hubspot/",
        subscriptions.export_subscription_to_hubspot,
        name="export_subscription_to_hubspot",
    ),
    path(
        "subscriptions/api/import_subscription_to_hubspot/",
        subscriptions.import_subscription_to_hubspot,
        name="import_subscription_to_hubspot",
    ),
    path(
        "subscriptions/association/drawer/",
        subscriptions.subscription_association_drawer,
        name="subscription_association_drawer",
    ),
    path(
        "subscriptions/options/",
        subscriptions.get_subscription_object_options,
        name="get_subscription_object_options",
    ),
    path(
        "subscriptions/update/subscriptions/<uuid:id>/",
        subscriptions.update_subscriptions,
        name="update_subscriptions",
    ),
    path(
        "subscriptions/file_size/",
        subscription_file_size.subscription_files_size,
        name="subscription_files_size",
    ),
    path(
        "subscriptions/load_drawer/create/",
        subscriptions_drawer.create_drawer,
        name="load_create_subscriptions_drawer",
    ),
    path(
        "subscriptions/load_drawer/manage/",
        subscriptions_drawer.manage_drawer,
        name="load_manage_subscriptions_drawer",
    ),
    path(
        "subscriptions/load_drawer/create_contact_from_subscriptions/",
        subscriptions_drawer.create_contact_from_subscriptions,
        name="create_contact_from_subscriptions",
    ),
    path(
        "subscriptions/load_drawer/create_company_from_subscriptions/",
        subscriptions_drawer.create_company_from_subscriptions,
        name="create_company_from_subscriptions",
    ),
    path(
        "subscriptions/load_drawer/export_and_import_drawer/",
        subscriptions_drawer.export_and_import_drawer,
        name="export_and_import_drawer_subscriptions",
    ),
    path(
        "commerce/api/kanban/board/",
        shopturbo.kanban_shopturbo_board_api,
        name="kanban_shopturbo_board_api",
    ),
    path(
        "commerce/api/kanban/status/",
        shopturbo.kanban_shopturbo_status_api,
        name="kanban_shopturbo_status_api",
    ),
    path(
        "commerce/api/shopturbo_api_formula",
        shopturbo.shopturbo_api_formula,
        name="shopturbo_api_formula",
    ),
    path(
        "commerce/api/shopturbo_item_api_formula",
        item_api.shopturbo_item_api_formula,
        name="shopturbo_item_api_formula",
    ),
    path("commerce/csv_id_upload", shopturbo.csv_id_upload, name="csv_id_upload"),
    path(
        "commerce/line_item_order",
        orders_drawer.line_item_order,
        name="line_item_order",
    ),
    path(
        "commerce/line_item_order/load/",
        orders_drawer.load_line_item_order,
        name="load_line_item_order",
    ),
    path(
        "commerce/line_item_order/dynamic_calculations",
        line_item.dynamic_calculations,
        name="dynamic_calculations",
    ),
    path(
        "commerce/api/commerce_csv_header_extractor/",
        shopturbo.shopturbo_csv_header_extractor,
        name="shopturbo_csv_header_extractor",
    ),
    path(
        "commerce/api/sync_header_extractor/",
        sync_order_header_extractor.sync_order_header_extractor,
        name="sync_order_header_extractor",
    ),
    path(
        "commerce/api/sync_order_association_header_extractor/",
        sync_order_header_extractor.sync_order_association_header_extractor,
        name="sync_order_association_header_extractor",
    ),
    path(
        "commerce/api/sync_status_header_extractor/",
        sync_order_header_extractor.sync_order_status_header_extractor,
        name="sync_order_status_header_extractor",
    ),
    path(
        "commerce/api/sync_platform_id_mapping_extractor/",
        sync_order_header_extractor.sync_platform_id_mapping_extractor,
        name="sync_platform_id_mapping_extractor",
    ),
    path(
        "commerce/api/sync_hubspot_filter_selector/",
        sync_order_header_extractor.sync_hubspot_filter_selector,
        name="sync_hubspot_filter_selector",
    ),
    path(
        "commerce/api/sync_item_import_filter_selector/",
        sync_order_header_extractor.sync_item_import_filter_selector,
        name="sync_item_import_filter_selector",
    ),
    path(
        "commerce/api/sync_order_import_filter_selector/",
        sync_order_header_extractor.sync_order_import_filter_selector,
        name="sync_order_import_filter_selector",
    ),
    path(
        "commerce/api/sync_case_header_extractor/",
        cases.sync_case_header_extractor,
        name="sync_case_header_extractor",
    ),
    path(
        "commerce/api/sync_item_header_extractor/",
        shopturbo.sync_item_header_extractor,
        name="sync_item_header_extractor",
    ),
    path(
        "commerce/api/sync_invoice_header_extractor/",
        sync_invoice_header_extractor,
        name="sync_invoice_header_extractor",
    ),
    path(
        "commerce/api/sync_subscription_header_extractor/",
        sync_subscription_header_extractor,
        name="sync_subscription_header_extractor",
    ),
    path(
        "commerce/api/sync_item_mapping_url/",
        shopturbo.sync_item_mapping_url,
        name="sync_item_mapping_url",
    ),
    path(
        "commerce/api/hubspot_subscription_fields_mapping_url/",
        shopturbo.hubspot_subscription_fields_mapping_url,
        name="hubspot_subscription_fields_mapping_url",
    ),
    path(
        "commerce/api/hubspot_inventory_fields_mapping_url/",
        shopturbo.hubspot_inventory_fields_mapping_url,
        name="hubspot_inventory_fields_mapping_url",
    ),
    path(
        "commerce/api/hubspot_billing_fields_mapping_url/",
        shopturbo.hubspot_billing_fields_mapping_url,
        name="hubspot_billing_fields_mapping_url",
    ),
    path(
        "commerce/api/hubspot_contact_fields_mapping_url/",
        shopturbo.hubspot_contact_fields_mapping_url,
        name="hubspot_contact_fields_mapping_url",
    ),
    path(
        "commerce/api/form_map_hubspot_object_name/",
        shopturbo.form_map_hubspot_object_name,
        name="form_map_hubspot_object_name",
    ),
    path(
        "commerce/api/save_mapped_hubspot_object_name/",
        shopturbo.save_mapped_hubspot_object_name,
        name="save_mapped_hubspot_object_name",
    ),
    path(
        "commerce/api/hubspot_save_properties_mapping/",
        shopturbo.hubspot_save_properties_mapping,
        name="hubspot_save_properties_mapping",
    ),
    path(
        "commerce/api/create_bulk_properties_hubspot/",
        shopturbo.create_bulk_properties_hubspot,
        name="create_bulk_properties_hubspot",
    ),
    path(
        "commerce/api/patch_properties_hubspot/",
        shopturbo.patch_properties_hubspot,
        name="patch_properties_hubspot",
    ),
    path(
        "commerce/api/export_billing_to_hubspot/",
        shopturbo.export_billing_to_hubspot,
        name="export_billing_to_hubspot",
    ),
    path(
        "commerce/api/import_commerce_to_hubspot/",
        shopturbo.import_commerce_to_hubspot,
        name="import_commerce_to_hubspot",
    ),
    path(
        "commerce/api/export_inventory_to_hubspot/",
        shopturbo.export_inventory_to_hubspot,
        name="export_inventory_to_hubspot",
    ),
    path(
        "commerce/api/import_inventory_to_hubspot/",
        shopturbo.import_inventory_to_hubspot,
        name="import_inventory_to_hubspot",
    ),
    path(
        "orders/form/update/<uuid:id>/",
        shopturbo.shopturbo_update_notes_form,
        name="shopturbo_notes_form_update",
    ),
    path("orders/form/", shopturbo.shopturbo_notes_form, name="shopturbo_notes_form"),
    # Worker
    path("worker/", worker.worker, {"object_type": TYPE_OBJECT_WORKER}, name="worker"),
    path(
        "worker/<uuid:id>",
        worker.row_detail,
        {"object_type": TYPE_OBJECT_WORKER},
        name="worker_row_detail",
    ),
    path("worker/new/", worker.worker_drawer, name="worker_drawer"),
    path("worker/manage/<uuid:id>", worker.worker_drawer, name="worker_manage"),
    path("worker/create/", worker.create_worker, name="new_worker"),
    path("worker/edit/<uuid:id>", worker.worker_edit, name="worker_edit"),
    path("worker/bulk_worker/", worker.worker_update, name="bulk_worker"),
    path(
        "worker/custom_fields/options/",
        worker.get_worker_custom_fields_options,
        name="get_worker_custom_fields_options",
    ),
    path(
        "worker/reviews/",
        worker.worker,
        {"object_type": TYPE_OBJECT_WORKER_REVIEW},
        name="worker_review",
    ),
    path(
        "worker/reviews/new/", worker.worker_review_drawer, name="worker_review_drawer"
    ),
    path(
        "worker/reviews/manage/<uuid:id>",
        worker.worker_review_drawer,
        name="worker_review_manage",
    ),
    path(
        "worker/reviews/<uuid:id>",
        worker.row_detail,
        {"object_type": TYPE_OBJECT_WORKER_REVIEW},
        name="worker_review_row_detail",
    ),
    path(
        "worker/worker_table_mapping_url/",
        worker.table_mapping_url,
        name="worker_table_mapping_url",
    ),
    path(
        "worker/worker_csv_header_extractor/",
        worker.csv_header_extractor,
        name="worker_csv_header_extractor",
    ),
    path(
        "worker/worker_csv_bulk_create/",
        worker.csv_bulk_create,
        name="worker_csv_bulk_create",
    ),
    path(
        "worker/worker_import_export_section/",
        worker.worker_import_export_section,
        name="worker_import_export_section",
    ),
    path(
        "worker/absence/",
        worker.worker,
        {"object_type": TYPE_OBJECT_WORKER_ABSENCE},
        name="absence",
    ),
    path("worker/absence/form/", worker.absence_form, name="absence_form"),
    path("worker/absence/action/", worker.absence_action, name="absence_action"),
    path(
        "worker/absence/manage/<uuid:id>",
        worker.absence_form,
        name="worker_absence_manage",
    ),
    path(
        "worker/absence/<uuid:id>",
        worker.row_detail,
        {"object_type": TYPE_OBJECT_WORKER_ABSENCE},
        name="worker_absence_detail",
    ),
    path("worker/options/", worker.worker_options, name="worker_options"),
    path("worker/settings/", worker.app_settings, name="worker_settings"),
    path("worker/options/", worker.worker_options, name="worker_options"),
    path("worker/settings/", worker.app_settings, name="worker_settings"),
    path(
        "journal/import-export-section/",
        journal.import_export_section,
        name="journal_import_export_section",
    ),
    path("journal/settings/", journal.app_settings, name="journal_settings"),
    path(
        "journal/",
        journal.journal,
        {"object_type": TYPE_OBJECT_JOURNAL},
        name="journal",
    ),
    path("journal/drawer/", journal.journal_manage, name="journal_new_drawer"),
    path("journal/new/", journal.journal_manage, name="journal_new"),
    path("journal/bulk/", journal.journal_manage, name="journal_bulk"),
    path(
        "journal/manage/<uuid:id>/",
        journal.journal_manage,
        name="journal_manage_drawer",
    ),
    path("journal/edit/<uuid:id>/", journal.journal_manage, name="journal_manage"),
    path(
        "journal/<uuid:id>/",
        journal.row_detail,
        {"object_type": TYPE_OBJECT_JOURNAL},
        name="journal_row_detail",
    ),
    path("journal/addon-partner/", journal.addon_partner, name="journal_addon_partner"),
    path(
        "journal/<uuid:id>/remove-expense",
        journal.remove_expense,
        name="remove_journal_expense",
    ),
    path(
        "journal/<uuid:id>/remove-invoice",
        journal.remove_invoice,
        name="remove_journal_invoice",
    ),
    path(
        "journal/<uuid:id>/remove-subscription",
        journal.remove_subscription,
        name="remove_journal_subscription",
    ),
    path(
        "journal/remove-associate-object",
        journal.remove_associate_object,
        name="remove_associate_journal_entry",
    ),
    path(
        "journal/update_association/",
        journal.journal_update_association_object,
        name="journal_update_association_object",
    ),
    path(
        "journal/association/drawer/",
        journal.journal_association_drawer,
        name="journal_association_drawer",
    ),
    # ================================ ### ↓↓↓ Commerce APP ↓↓↓ ### ============================================== #
    # General URL
    path(
        "commerce/workspace/settings/",
        commerce_functions.app_settings,
        name="commerce_settings",
    ),
    path(
        "commerce/settings/apply-to-others",
        commerce_functions.commerce_app_settings_child_apply_to_others,
        name="commerce_settings_apply_to_others",
    ),
    path(
        "commerce/addon/",
        commerce_functions.addon_create_company_contact,
        name="addon_contact_company",
    ),
    path(
        "commerce/email_customer_handler/",
        commerce_functions.email_customer_handler,
        name="email_customer_handler",
    ),
    path(
        "commerce/currency_check/",
        commerce_currency_check,
        name="commerce_currency_check",
    ),
    path(
        "commerce/customer_property_check/",
        commerce_functions.customer_property_check,
        name="customer_property_check",
    ),
    path(
        "commerce/commerce_list_items_check/",
        commerce_list_items_check,
        name="commerce_list_items_check",
    ),
    path(
        "commerce/commerce_table_mapping_url/",
        commerce_functions.commerce_table_mapping_url,
        name="commerce_table_mapping_url",
    ),  # CSV Table Mapping
    path(
        "commerce/commerce_csv_header_extractor/",
        commerce_functions.commerce_csv_header_extractor,
        name="commerce_csv_header_extractor",
    ),  # CSV Header extractor
    path(
        "commerce/commerce_get_form/",
        commerce_functions.commerce_get_form,
        name="commerce_get_form",
    ),  # commerce_get_form
    path(
        "commerce/association/drawer/",
        commerce_association.commerce_association_drawer,
        name="biling_association_drawer",
    ),
    path(
        "commerce/association/update/",
        commerce_association.save_object_association,
        name="save_object_association",
    ),
    # Specify URL
    path("invoices/", invoices, name="invoices"),  # Show All in Table
    path("invoices/show/<uuid:id>/", invoices, name="show_invoice"),  # Showing From URL
    path("invoices/new/", invoice_create_and_update, name="new_invoice"),  # Create New
    path(
        "invoices/update/<uuid:id>/", invoice_create_and_update, name="update_invoice"
    ),  # Update Existing
    path(
        "invoices/invoice_edit/<uuid:id>/", invoice_drawer, name="invoice_edit"
    ),  # Manage
    path(
        "invoices/invoice_edit/new/", invoice_drawer, name="invoice_drawer"
    ),  # Opening Drawer
    path(
        "invoices/bulk_invoice/", invoice_create_and_update, name="bulk_invoice"
    ),  # Bulk Edit
    path(
        "invoices/pdfview/<uuid:id>/",
        commerce_pdf_download,
        # Preview PDF
        {"preview": True, "object_type": TYPE_OBJECT_INVOICE},
        name="invoice_pdf_view",
    ),
    path(
        "invoices/preview/",
        commerce_pdf_download,
        # Preview PDF Template
        {"preview": True, "object_type": TYPE_OBJECT_INVOICE},
        name="invoice_pdf_preview",
    ),
    path(
        "invoices/download/",
        commerce_pdf_download,
        {"object_type": TYPE_OBJECT_INVOICE},
        name="invoice_pdf_download",
    ),  # Download PDF
    path(
        "invoices/download/<uuid:id>/",
        commerce_pdf_download,
        {"object_type": TYPE_OBJECT_INVOICE},
        name="invoice_pdf_download",
    ),  # Download PDF
    path(
        "invoices/bulk_download/",
        commerce_functions.commerce_zip_download,
        {"object_type": TYPE_OBJECT_INVOICE},
        name="invoice_download",
    ),  # Zip Download
    path(
        "invoices/invoice_csv_header_extractor/",
        commerce_functions.commerce_csv_header_extractor,
        # CSV Header extractor
        {"object_type": TYPE_OBJECT_INVOICE},
        name="invoice_csv_header_extractor",
    ),
    path(
        "invoices/<uuid:id>",
        commerce_functions.commerce_row_detail,
        {"object_type": TYPE_OBJECT_INVOICE},
        name="invoice_row_detail",
    ),
    path(
        "invoices/options/",
        commerce_functions.get_invoice_object_options,
        name="get_invoice_object_options",
    ),
    path(
        "invoices/<uuid:id>/payment_link/",
        invoice_payment_link,
        name="invoice_payment_link",
    ),
    path("estimates/", estimates, name="estimates"),  # Show All in Table
    path(
        "estimates/new/", estimate_create_and_update, name="new_estimate"
    ),  # Create New
    path(
        "estimates/update/<uuid:id>/",
        estimate_create_and_update,
        name="update_estimate",
    ),  # Update
    path(
        "estimates/estimate_edit/<uuid:id>/", estimate_drawer, name="estimate_edit"
    ),  # Manage
    path("estimates/estimate_edit/new/", estimate_drawer, name="estimate_drawer"),
    path(
        "estimates/bulk_estimate/", estimate_create_and_update, name="bulk_estimate"
    ),  # Bulk Edit
    path("estimates/show/<uuid:id>/", estimates, name="show_estimate"),
    path(
        "estimates/pdfview/<uuid:id>/",
        commerce_pdf_download,
        # Preview PDF
        {"preview": True, "object_type": TYPE_OBJECT_ESTIMATE},
        name="estimate_pdf_view",
    ),
    path(
        "estimates/preview/",
        commerce_pdf_download,
        # Preview PDF Template
        {"preview": True, "object_type": TYPE_OBJECT_ESTIMATE},
        name="estimate_pdf_preview",
    ),
    path(
        "estimates/download/<uuid:id>/",
        commerce_pdf_download,
        # Download PDF
        {"object_type": TYPE_OBJECT_ESTIMATE},
        name="estimate_pdf_download",
    ),
    path(
        "estimates/bulk_download/",
        commerce_functions.commerce_zip_download,
        # Zip Download
        {"object_type": TYPE_OBJECT_ESTIMATE},
        name="estimate_bulk_download",
    ),
    path(
        "estimates/estimate_csv_header_extractor/",
        commerce_functions.commerce_csv_header_extractor,
        # CSV Header extractor
        {"object_type": TYPE_OBJECT_ESTIMATE},
        name="estimate_csv_header_extractor",
    ),
    path(
        "estimates/<uuid:id>",
        commerce_functions.commerce_row_detail,
        {"object_type": TYPE_OBJECT_ESTIMATE},
        name="estimate_row_detail",
    ),
    path(
        "estimates/options/",
        commerce_functions.get_estimate_object_options,
        name="get_estimate_object_options",
    ),
    path("receipts/", receipts, name="receipts"),  # Show All in Table
    path("receipts/new/", receipt_create_and_update, name="new_receipt"),  # Create New
    path(
        "receipts/update/<uuid:id>/", receipt_create_and_update, name="update_receipt"
    ),  # Update Existing
    path(
        "receipts/receipt_edit/<uuid:id>/", receipt_drawer, name="receipt_edit"
    ),  # Manage
    path(
        "receipts/receipt_edit/new/", receipt_drawer, name="receipt_drawer"
    ),  # Opening Drawer
    path(
        "receipts/association/drawer/",
        commerce_association.receipts_association_drawer,
        name="receipts_association_drawer",
    ),
    path(
        "receipts/bulk_receipt/", receipt_create_and_update, name="bulk_receipt"
    ),  # Bulk Edit
    path("receipts/show/<uuid:id>/", receipts, name="show_receipt"),  # Showing From URL
    path(
        "receipts/pdfview/<uuid:id>/",
        commerce_pdf_download,
        {"preview": True, "object_type": TYPE_OBJECT_RECEIPT},
        name="receipt_pdf_view",
    ),
    path(
        "receipts/preview/",
        commerce_pdf_download,
        {"preview": True, "object_type": TYPE_OBJECT_RECEIPT},
        name="receipt_pdf_preview",
    ),
    path(
        "receipts/download/<uuid:id>/",
        commerce_pdf_download,
        {"object_type": TYPE_OBJECT_RECEIPT},
        name="receipt_pdf_download",
    ),
    path(
        "receipts/bulk_download/<uuid:id>/",
        commerce_functions.commerce_zip_download,
        # Zip Download
        {"object_type": TYPE_OBJECT_RECEIPT},
        name="receipt_bulk_download",
    ),
    path(
        "receipts/receipt_csv_header_extractor/",
        commerce_functions.commerce_csv_header_extractor,
        # CSV Header extractor
        {"object_type": TYPE_OBJECT_RECEIPT},
        name="receipt_csv_header_extractor",
    ),
    path(
        "receipts/<uuid:id>",
        commerce_functions.commerce_row_detail,
        {"object_type": TYPE_OBJECT_RECEIPT},
        name="receipt_row_detail",
    ),
    path("delivery_notes/", delivery_notes, name="delivery_slips"),
    path(
        "delivery_notes/new/", delivery_note_create_and_update, name="new_delivery_slip"
    ),
    path(
        "delivery_notes/update/<uuid:id>/",
        delivery_note_create_and_update,
        name="update_delivery_slip",
    ),
    path(
        "delivery_notes/delivery_slip_edit/<uuid:id>/",
        delivery_note_drawer,
        name="delivery_slip_edit",
    ),  # Manage
    path(
        "delivery_notes/delivery_slip_edit/new/",
        delivery_note_drawer,
        name="delivery_slip_drawer",
    ),
    path(
        "delivery_notes/bulk_delivery_slip/",
        delivery_note_create_and_update,
        name="bulk_delivery_slip",
    ),  # Bulk Edit
    path("delivery_notes/show/<uuid:id>/", delivery_notes, name="show_delivery_slip"),
    path(
        "delivery_notes/pdfview/<uuid:id>/",
        commerce_pdf_download,
        {"preview": True, "object_type": TYPE_OBJECT_DELIVERY_NOTE},
        name="delivery_slip_pdf_view",
    ),
    path(
        "delivery_notes/preview/",
        commerce_pdf_download,
        {"preview": True, "object_type": TYPE_OBJECT_DELIVERY_NOTE},
        name="delivery_slip_pdf_preview",
    ),
    path(
        "delivery_notes/download/<uuid:id>/",
        commerce_pdf_download,
        {"object_type": TYPE_OBJECT_DELIVERY_NOTE},
        name="delivery_slip_pdf_download",
    ),
    path(
        "delivery_notes/bulk_download/<uuid:id>/",
        commerce_functions.commerce_zip_download,
        # Zip Download
        {"object_type": TYPE_OBJECT_DELIVERY_NOTE},
        name="delivery_slip_bulk_download",
    ),
    path(
        "delivery_notes/delivery_slip_csv_header_extractor/",
        commerce_functions.commerce_csv_header_extractor,
        # CSV Header extractor
        {"object_type": TYPE_OBJECT_DELIVERY_NOTE},
        name="delivery_slip_csv_header_extractor",
    ),
    path(
        "delivery_notes/<uuid:id>",
        commerce_functions.commerce_row_detail,
        {"object_type": TYPE_OBJECT_DELIVERY_NOTE},
        name="delivery_slip_row_detail",
    ),
    path(
        "delivery_notes/options/",
        commerce_functions.get_delivery_notes_object_options,
        name="get_delivery_notes_object_options",
    ),
    path("slips/", slips, name="slips"),  # Show All in Table
    path("slips/new/", slip_create_and_update, name="new_slip"),  # Create New
    path(
        "slips/update/<uuid:id>/", slip_create_and_update, name="update_slip"
    ),  # Update Existing
    path("slips/slip_edit/<uuid:id>/", slip_drawer, name="slip_edit"),  # Manage
    path("slips/slip_edit/new/", slip_drawer, name="slip_drawer"),  # Opening Drawer
    path("slips/bulk_slip/", slip_create_and_update, name="bulk_slip"),  # Bulk Edit
    path("slips/show/<uuid:id>/", slips, name="show_slip"),  # Showing From URL
    path(
        "slips/pdfview/<uuid:id>/",
        commerce_pdf_download,
        # Preview PDF
        {"preview": True, "object_type": TYPE_OBJECT_SLIP},
        name="slip_pdf_view",
    ),
    path(
        "slips/preview/",
        commerce_pdf_download,
        # Preview PDF Template
        {"preview": True, "object_type": TYPE_OBJECT_SLIP},
        name="slip_pdf_preview",
    ),
    path(
        "slips/download/<uuid:id>/",
        commerce_pdf_download,
        {"object_type": TYPE_OBJECT_SLIP},
        # Download PDF
        name="slip_pdf_download",
    ),
    path(
        "slips/bulk_download/<uuid:id>/",
        commerce_functions.commerce_zip_download,
        {"object_type": TYPE_OBJECT_SLIP},
        name="slip_bulk_download",
    ),  # Zip Download
    path(
        "slips/slip_csv_header_extractor/",
        commerce_functions.commerce_csv_header_extractor,
        # CSV Header extractor
        {"object_type": TYPE_OBJECT_SLIP},
        name="slip_csv_header_extractor",
    ),
    path(
        "slips/<uuid:id>",
        commerce_functions.commerce_row_detail,
        {"object_type": TYPE_OBJECT_SLIP},
        name="slip_row_detail",
    ),
    # ================================ ### ↑↑↑ Billing APP ↑↑↑ ### ============================================== #
    path("commerce/contracts/", contractwise.contractwise, name="contractwise"),
    path(
        "commerce/contracts/settings/",
        contractwise.app_settings,
        name="contractwise_settings",
    ),
    path(
        "commerce/contracts/doc/form/", contractwise.document_form, name="document_form"
    ),
    path(
        "commerce/contracts/doc/signers/", contractwise.add_signers, name="add_signers"
    ),
    path(
        "commerce/contracts/doc/signature/",
        contractwise.add_signature,
        name="add_signature",
    ),
    path(
        "commerce/contracts/doc/place_free_text_form/",
        contractwise.place_free_text_form,
        name="place_free_text_form",
    ),
    path(
        "commerce/contracts/doc/place_fields_form/",
        contractwise.place_fields_form,
        name="place_fields_form",
    ),
    path(
        "commerce/contracts/doc/send_request/",
        contractwise.send_signature_request,
        name="send_signature_request",
    ),
    path(
        "commerce/contracts/doc/sign/", contractwise.sign_document, name="sign_document"
    ),
    path(
        "commerce/contracts/template/",
        contractwise.document_template,
        name="document_template",
    ),
    path(
        "commerce/contracts/template/form/",
        contractwise.document_template_form,
        name="document_template_form",
    ),
    path("commerce/contracts/signature/", contractwise.signature, name="signature"),
    path(
        "commerce/contracts/signature/form/",
        contractwise.signature_form,
        name="signature_form",
    ),
    path(
        "commerce/contracts/doc/link/generate/",
        contractwise.generate_doc_sign_link,
        name="generate_doc_sign_link",
    ),
    path(
        "commerce/contracts/doc/link/delete/",
        contractwise.delete_doc_sign_link,
        name="delete_doc_sign_link",
    ),
    path(
        "commerce/contracts/template/<uuid:pdf_template_id>/free_texts/refresh/",
        contractwise.refresh_free_text_template,
        name="refresh_free_text_template",
    ),
    path(
        "commerce/contracts/template/<uuid:pdf_template_id>/free_texts/",
        contractwise.manage_free_text_template,
        name="manage_free_text_template",
    ),
    path(
        "purchase_orders/",
        purchase_orders,
        {"object_type": TYPE_OBJECT_PURCHASE_ORDER},
        name="purchaseorder",
    ),
    path("purchase_orders/drawer/", purchase_order_drawer, name="purchase_drawer"),
    path(
        "purchase_orders/manage/<uuid:id>/",
        purchase_order_drawer,
        name="purchase_manage",
    ),
    path(
        "purchase_orders/set/<uuid:id>/",
        purchase_order.purchaseEdit,
        name="purchase_set",
    ),
    path("purchase_orders/new/", purchase_order.purchaseEdit, name="purchase_new"),
    path(
        "purchase_orders/available_items/",
        purchase_order_drawer,
        name="po_available_items",
    ),
    path(
        "purchase_orders/purchase_order_list_items_check/",
        purchase_order_drawer,
        name="purchase_order_list_items_check",
    ),
    path(
        "purchase_orders/preview/",
        purchase_order.purchase_order_pdf_download,
        # Preview PDF Template
        {"preview": True},
        name="purchase_order_pdf_preview",
    ),
    path(
        "purchase_orders/download/<uuid:id>",
        purchase_order.purchase_order_pdf_download,
        name="purchase_order_pdf_download",
    ),  # Preview PDF Template
    path(
        "purchase_orders/options/",
        purchase_order.purchase_orders_options,
        name="purchase_orders_options",
    ),
    path(
        "purchase_orders/<uuid:id>/events/",
        purchase_order.get_purchase_order_events,
        name="get_purchase_order_events",
    ),
    path(
        "purchase_orders/bot",
        purchase_order.purchase_order_bot,
        name="purchase_order_bot",
    ),
    path(
        "purchase_orders/<uuid:id>/",
        purchase_order.row_detail,
        {"object_type": TYPE_OBJECT_PURCHASE_ORDER},
        name="purchase_order_row_detail",
    ),
    path(
        "purchase_orders/select-item/",
        purchase_order.select_item,
        {"object_type": TYPE_OBJECT_PURCHASE_ORDER},
        name="purchase_order_select_item",
    ),
    path(
        "purchase_orders/association/drawer/",
        purchase_order.purchase_orders_association_drawer,
        name="purchase_orders_association_drawer",
    ),
    path(
        "purchase_orders/options/",
        purchase_order.get_purchase_order_object_options,
        name="get_purchase_order_object_options",
    ),
    # path('commerce/suppliers/', purchase_orders {'object_type': TYPE_OBJECT_PURCHASE_SUPPLIER}, name='purchasesupplier'),
    path(
        "procurement/settings", purchase_order.app_settings, name="procurement_settings"
    ),
    path(
        "commerce/api/purchase_csv_header_extractor/",
        purchase_order.csv_header_extractor,
        name="purchase_csv_header_extractor",
    ),
    path(
        "commerce/api/purchase_table_mapping_url/",
        purchase_order.table_mapping_url,
        name="purchase_table_mapping_url",
    ),
    path(
        "commerce/api/journal_table_mapping_url/",
        journal.table_mapping_url,
        name="journal_table_mapping_url",
    ),
    path(
        "commerce/api/journal_csv_header_extractor/",
        journal.csv_header_extractor,
        name="journal_csv_header_extractor",
    ),
    path(
        "commerce/api/journal_csv_bulk_create/",
        journal.csv_bulk_create,
        name="journal_csv_bulk_create",
    ),
    # Will Change from payment to expense name
    path("expenses/", expenses, name="expense"),
    path(
        "expenses/<uuid:id>/remove-account-transaction/",
        expense.remove_account_transaction,
        name="remove_account_transaction",
    ),
    path("expenses/new/", expense.new_expense, name="new_expense"),
    path("expenses/update/<uuid:id>/", expense.expense_update, name="expense_update"),
    path(
        "expenses/expense_file/",
        expense.new_expense,
        {"expense_file": 1},
        name="expense_file_upload",
    ),
    path(
        "expenses/api/expense_file/",
        expense.expense_file_reader,
        name="expense_file_reader",
    ),
    path(
        "expenses/file/delete/<uuid:id>/",
        expense.expense_file_delete,
        name="expense_file_delete",
    ),
    path("expenses/<uuid:id>/", expense.row_detail, name="expense_row_detail"),
    path("expenses/expense/<uuid:id>/", expense_drawer, name="expense_drawer"),
    path("expenses/expense/", expense_drawer, {"id": None}, name="expense_drawer_new"),
    path("expenses/expense/action", expense.expense_action, name="expense_action"),
    path("expenses/expense/bulk/", expense.expense_update, name="bulk_expense"),
    path(
        "expenses/expense/expense_preview/<str:preview>/<uuid:id>/",
        expense.expense_pdf_preview,
        name="expense_pdf_preview",
    ),
    path(
        "expenses/expense/expense_download/<str:download>/<uuid:id>/",
        expense.expense_pdf_preview,
        name="expense_pdf_download",
    ),
    path("expense/settings", expense.app_settings, name="expense_settings"),
    path(
        "expense/expense_csv_header_extractor/",
        expense.csv_header_extractor,
        name="expense_csv_header_extractor",
    ),
    path(
        "expense/expense_table_mapping_url/",
        expense.table_mapping_url,
        name="expense_table_mapping_url",
    ),  # CSV Table Mapping
    path(
        "expense/get_exchange_rate/",
        expense.get_exchange_rate,
        name="get_exchange_rate",
    ),  # get_exchange_rate
    path("bills/", bills, {"object_type": TYPE_OBJECT_BILL}, name="bill"),
    path("bills/settings", bill.app_settings, name="bill_settings"),
    path("bills/drawer/", bill_drawer, name="bill_drawer"),
    path("bills/new/", bill.bills_update, name="bill_new"),
    path("bills/manage/<uuid:id>/", bill_drawer, name="bill_manage"),
    path("bills/bulk/", bill.bills_update, name="bulk_billing"),
    path("bills/download/<uuid:id>/", bill.bills_download, name="bills_download"),
    path("bills/set/<uuid:id>/", bill.bills_update, name="bill_set"),
    path("bills/bill_preview/<uuid:id>/", bill.bill_preview, name="bill_preview"),
    path(
        "bills/<uuid:id>/",
        bill.row_detail,
        {"object_type": TYPE_OBJECT_BILL},
        name="bill_row_detail",
    ),
    path(
        "commerce/bulk_download/<str:type_spend>/",
        bill.bulk_download,
        name="bulk_download",
    ),
    path(
        "bills/options/", bill.get_bills_object_options, name="get_bills_object_options"
    ),
    path(
        "expense/association/drawer/",
        expense.expense_association_drawer,
        name="expense_association_drawer",
    ),
    path(
        "expenses/options/", expense.get_expenses_options, name="get_expenses_options"
    ),
    path(
        "bill/association/drawer/",
        bill.bill_association_drawer,
        name="bill_association_drawer",
    ),
    path("commerce/view_setting", commerce.view_settings, name="commerce_view_setting"),
    path(
        "commerce/hubspot/create/",
        shopturbo.create_hubspot_order,
        name="shopturbo_create_hubspot_order",
    ),
    path("commerce/api_formula", commerce.api_formula, name="commerce_api_formula"),
    path(
        "commerce/import-export-section/",
        commerce.import_export_section,
        name="import_export_section",
    ),
    # Workspace - Dashboards
    path(
        "reports/",
        report_views.reports,
        {"object_type": TYPE_OBJECT_PANEL},
        name="report_reports",
    ),
    path("reports/form/", report_forms.panel_form, name="panel_form"),
    path("reports/ratio/", reports.panel_ratio, name="panel_ratio"),
    path("reports/template/", reports.template, name="panel_template"),
    path("reports/sort/", reports.panel_sort, name="panel_sort"),
    path(
        "reports/export/<uuid:panel_id>/",
        report_views.panel_export,
        name="panel_export",
    ),
    path("reports/<uuid:panel_id>/", report_views.panel_view, name="panel_view"),
    path(
        "reports/<uuid:panel_id>/<int:page>", report_views.panel_view, name="panel_view"
    ),
    path("reports/metric/", report_forms.panel_metric, name="panel_metric"),
    path("reports/formula/", reports.panel_formula, name="panel_formula"),
    path(
        "reports/<uuid:metric_id>/summary/", reports.panel_summary, name="panel_summary"
    ),
    path("reports/metric/<uuid:id>", reports.update_sql_metric, name="update_sql"),
    path(
        "reports/panel/table/metric/columns/",
        reports.table_columns_form,
        name="table_columns_form",
    ),
    path(
        "reports/template", report_views.panels_templates, name="panels_templates"
    ),
    path(
        "dashboards/",
        report_views.reports,
        {"object_type": TYPE_OBJECT_DASHBOARD},
        name="dashboards",
    ),
    path(
        "dashboards/views/drawer",
        render_drawer_metrics.report_views_drawer,
        name="report_views_drawer",
    ),
    path("dashboards/form/", report_forms.report_form, name="report_form"),
    path("dashboards/<uuid:report_id>/", report_views.report_view, name="report_view"),
    path("dashboards/<uuid:report_id>/panel/list", report_views.panels_list_view, name="panels_list_view"),
    path(
        "dashboards/<uuid:report_id>/<uuid:panel_id>/",
        report_views.report_panel_view,
        name="report_panel_view",
    ),
    path(
        "dashboards/<uuid:report_id>/<uuid:panel_id>/<int:page>/",
        report_views.report_panel_view,
        name="report_panel_view",
    ),
    path("dashboards/settings/", report_views.app_settings, name="reportwise_settings"),
    path("dashboards/settings/form/", report_forms.event_form, name="event_form"),
    path(
        "dashboards/settings/properties/",
        reports.event_properties,
        name="event_properties",
    ),
    path(
        "dashboards/template", report_views.template, name="dashboards_templates"
    ),
    # this url also used for pinned pages. Please also update the pin_page if new changes are made
    path("dashboards/detail/", reports.report_detail, name="report_detail"),
    path("dashboards/generate/", reports.generate_report, name="generate_report"),
    path(
        "dashboards/render_drawer_metrics",
        render_drawer_metrics.render_drawer_metrics,
        name="render_drawer_metrics",
    ),
    path(
        "dashboards/render_manage_drawer",
        render_drawer_metrics.render_manage_drawer,
        name="render_manage_drawer",
    ),
    path(
        "dashboards/download/",
        reports.download_company_list,
        name="download_company_list",
    ),
    path("dashboards/sheet/", reports.save_sheet, name="save_sheet"),
    path("dashboards/sheet/tab", reports.save_tab, name="save_tab"),
    path(
        "pixel/events/",
        reports.event,
        {"object_type": TYPE_OBJECT_SESSION_EVENT},
        name="event",
    ),
    path(
        "pixel/events/<uuid:rule_id>/history/",
        reports.event_history,
        name="event_history",
    ),
    path(
        "<slug:app_slug>/app/taskflow/",
        taskflow.taskflow_in_app,
        name="taskflow_in_app",
    ),
    path(
        "<slug:app_slug>/app/workflows/",
        taskflow.taskflow_in_app,
        name="workflows_in_app",
    ),
    path(
        "<slug:app_slug>/app/workflows/<uuid:id>/",
        taskflow.taskflow_in_app,
        name="history_workflows_in_app",
    ),
    path(
        "<slug:app_slug>/app/gallery/", taskflow.taskflow_in_app, name="gallery_in_app"
    ),
    path("projects/order/", tasks.project_order, name="project_order"),
    path("projects/", taskflow.taskflow, name="taskflow"),
    path("task/view/settings/", taskflow.task_view_setting, name="task_view_settings"),
    path("tasks/options/", taskflow.tasks_options, name="tasks_options"),
    path("tasks/data/", tasks.tasks_data, name="tasks_data"),
    path("tasks/projects_list_api/", tasks.projects_list_api, name="projects_list_api"),
    path("tasks/data/<uuid:id>/", tasks.tasks_data, name="tasks_data"),
    path(
        "tasks/generate/",
        tasks.generate_workflow_history,
        name="generate_workflow_history",
    ),
    path("tasks/form/", tasks.task_form, name="task_form"),
    path("tasks/form/bulk", tasks.tasks_bulk_edit, name="tasks_bulk_edit"),
    path(
        "tasks/form/description/<uuid:id>/",
        tasks.task_description,
        name="task_description",
    ),
    path("tasks/form/custom_field/", tasks.task_custom_field, name="task_custom_field"),
    path("tasks/upload_img/", tasks.task_upload_img, name="task_upload_img"),
    path(
        "tasks/form/usage_status/<uuid:id>/",
        tasks.task_form_usage_status,
        name="task_form_usage_status",
    ),
    path(
        "tasks/form/usage_status/bulk/",
        tasks.bulk_task_usage_status,
        name="bulk_task_usage_status",
    ),
    path(
        "tasks/form/duplicate/bulk/",
        tasks.bulk_task_duplicate,
        name="bulk_task_duplicate",
    ),
    path(
        "tasks/workflow/delete/<uuid:id>/",
        tasks.workflow_history_delete,
        name="workflow_history_delete",
    ),
    path(
        "tasks/workflow/form/", tasks.get_workflow_history, name="get_workflow_history"
    ),
    path(
        "tasks/workflow/history/",
        workflow.load_workflow_history,
        name="load_workflow_history",
    ),
    path(
        "task/association/drawer/",
        taskflow.task_association_drawer,
        name="task_association_drawer",
    ),
    path(
        "task/sub_task/add/", tasks.add_subtask_drawer, name="task_add_subtask_drawer"
    ),
    path("task/sub_task/create/", tasks.create_subtask, name="create_subtask"),
    path("task/sub_task/load/", tasks.load_subtask, name="load_subtask"),
    path(
        "task/sub_task/remove/",
        tasks.remove_subtask_relation,
        name="remove_subtask_relation",
    ),
    path("commerce_meter/", commerce_meter.commerce_meter, name="commerce_meter"),
    path(
        "commerce_meter/content/",
        commerce_meter.commerce_meter_content,
        name="commerce_meter_content",
    ),
    path(
        "commerce_meter/content/<uuid:id>/",
        commerce_meter.commerce_meter_table_row,
        name="commerce_meter_table_row",
    ),
    path(
        "commerce_meter/create/",
        commerce_meter.create_commerce_meter,
        name="create_commerce_meter",
    ),
    path(
        "commerce_meter/manage/<uuid:id>/",
        commerce_meter.manage_commerce_meter_record,
        name="manage_commerce_meter_record",
    ),
    path(
        "commerce_meter/status/",
        commerce_meter.toggle_commerce_meter_status,
        name="toggle_commerce_meter_status",
    ),
    path(
        "commerce_meter/data-transfer/",
        commerce_meter.data_transfer_commerce_meter_drawer,
        name="data_transfer_commerce_meter_drawer",
    ),
    path(
        "commerce_meter/options/",
        commerce_meter.get_meter_options,
        name="get_meter_options",
    ),
    path("projects/settings/", tasks.projects_settings, name="projects_settings"),
    path("projects/options/", tasks.projects_options, name="projects_options"),
    path("property/list/", property.properties, name="properties"),
    path("property/manage/", property.manage_property, name="new_property"),
    path(
        "property/add_sub_property/", property.add_sub_property, name="add_sub_property"
    ),
    path("property/manage/<str:id>/", property.manage_property, name="manage_property"),
    path(
        "property/manage/<str:id>/base",
        property.manage_base_property,
        name="manage_base_property",
    ),
    path(
        "property/manage/reorder",
        property.manage_reorder_property,
        name="manage_reorder_property",
    ),
    path("property/child/", property.property_child, name="property_child"),
    path(
        "property/formula/res/", property.get_formula_result, name="get_formula_result"
    ),
    path(
        "property/related-custom-object/",
        property.get_related_custom_object,
        name="get_related_custom_object",
    ),
    path(
        "property/related-custom-object/values/",
        property.get_related_custom_object_values,
        name="get_related_custom_object_values",
    ),
    path("property/object/", property.get_object, name="get_object"),
    path(
        "property/get_process_master/",
        property.get_process_master,
        name="get_process_master",
    ),
    path(
        "property/get_production_line/",
        property.get_production_line,
        name="get_production_line",
    ),
    path(
        "property/choice-conditional-options/",
        property.choice_conditional_options,
        name="property_choice_conditional_options",
    ),
    path("property_set/", property.property_sets, name="property_sets"),
    path(
        "custom_property_list/",
        property.custom_property_list,
        name="custom_property_list",
    ),
    path(
        "create_custom_value/", property.create_custom_value, name="create_custom_value"
    ),
    path("form_sets/", property.form_sets, name="form_sets"),
    path("form_sets/manage/", property.form_sets_manage, name="form_sets_new"),
    path(
        "form_sets/manage/<str:id>/", property.form_sets_manage, name="form_sets_manage"
    ),
    path("property_set/manage/", property.manage_property_set, name="new_property_set"),
    path(
        "property_set/manage/<str:id>/",
        property.manage_property_set,
        name="manage_property_set",
    ),
    path(
        "property/text_unique/<str:object_type>/<uuid:property_id>/<uuid:object_id>",
        property.property_text_unique_check,
        name="property_text_unique_check",
    ),
    path(
        "property/text_unique/<str:object_type>/<uuid:property_id>",
        property.property_text_unique_check,
        name="property_text_unique_check",
    ),
    path(
        "property/text_form_unique/<str:form>/<str:object_type>/<uuid:property_id>/<uuid:object_id>",
        property.property_text_unique_check,
        name="property_text_form_unique_check",
    ),
    path(
        "property/text_form_unique/<str:form>/<str:object_type>/<uuid:property_id>",
        property.property_text_unique_check,
        name="property_text_form_unique_check",
    ),
    path(
        "association/<uuid:id>/retrieve/",
        association.retrieve_association,
        name="retrieve_association",
    ),
    path(
        "association/<uuid:id>/add_drawer/",
        association.add_drawer_association,
        name="add_drawer_association",
    ),
    path(
        "association/<uuid:id>/add_select/",
        association.add_select_associate,
        name="add_select_associate",
    ),
    path(
        "association/<uuid:id>/remove_related/",
        association.remove_related_association,
        name="remove_related_association",
    ),
    path(
        "association/label/apply/",
        association_labels.apply_association_label,
        name="apply_association_label",
    ),
    path(
        "association/label/unapply/<uuid:id>/",
        association_labels.unapply_association_label,
        name="unapply_association_label",
    ),
    path(
        "association/label/load_form/",
        association_labels.load_association_label_form,
        name="load_association_label_form",
    ),
    # Search Import Export History
    path(
        "search_import_export_history",
        shopturbo.search_import_export_history,
        name="search_import_export_history",
    ),
    # Accounts
    path("account/", account_view.account, name="account"),
    path("invitations/", account_view.invitation, name="invitation"),
    path(
        "delete_invitation/<id>/",
        account_view.delete_invitation,
        name="delete_invitation",
    ),
    path("security/", account_view.security, name="security"),
    path("account/delete_account", account_view.delete_account, name="delete_account"),
    path("download/", account_view.download_app, name="download_app"),
    # Workspace User Management
    path("workspace/users/", user_management.user_management, name="workspace"),
    path(
        "workspace/users/row-details/<int:id>/",
        user_management.user_management_row_detail,
        name="user_management_row_detail",
    ),
    path(
        "workspace/users/drawer/",
        user_management.user_management_drawer,
        name="user_management_drawer",
    ),
    path(
        "workspace/users/drawer/<int:id>/",
        user_management.user_management_drawer,
        name="user_management_drawer",
    ),
    path(
        "workspace/users/manage/",
        user_management.user_management_manage,
        name="user_management_manage",
    ),
    path(
        "workspace/users/data/<username>/",
        user_management.teammate,
        name="user_management_manage_permission",
    ),
    path(
        "workspace/users/options/",
        user_management.workspace_users_options,
        name="workspace_users_options",
    ),
    path(
        "workspace/users/options/management/",
        user_management.user_management_options,
        name="user_management_options",
    ),
    # Workspace User Management (Account)
    path(
        "workspace/remove_teammate/<username>/",
        role.manage_remove_workspace,
        name="remove_teammate",
    ),
    path(
        "workspace/leave_workspace/<username>/",
        role.manage_remove_workspace,
        name="leave_workspace",
    ),
    path(
        "workspace/available_app_teammate/<username>/",
        role.available_app_teammate,
        name="available_app_teammate",
    ),
    path("workspace/change_role/<username>/", role.change_role, name="change_role"),
    path("workspace/integrations/", home.integrations, name="integrations"),
    path("workspace/datasets", home.datasets, name="datasets"),
    path("workspace/datasets/form", home.dataset_form, name="dataset_form"),
    path("workspace/datasets/view/<uuid:dataset_id>", home.dataset_view, name="dataset_view"),
    path(
        "workspace/integrations/sarch",
        home.search_integrations,
        name="search_integrations",
    ),
    path(
        "workspace/integrations/connect/<slug:slug>/",
        integrations.connect_session,
        name="connect_session",
    ),
    path(
        "workspace/integrations/re-connect/<slug:slug>/",
        integrations.reconnect_integration,
        name="reconnect_integration",
    ),
    path(
        "workspace/integrations/update/sendgrid/<slug:slug>/",
        home.sendgrid_update_integrations,
        name="sendgrid_update_integrations",
    ),
    path(
        "workspace/import_google_to_workspace/",
        users_import_to_workspace.workspace_pull_users,
        name="workspace_pull_users",
    ),
    path(
        "workspace/import_microsoft_to_workspace/",
        users_import_to_workspace.microsoft_graph_login,
        name="microsoft_workspace_pull_users",
    ),
    path(
        "workspace/dg_payment_integration/",
        dg_payment_integration.dg_payment_request,
        name="dg_payment_request",
    ),
    path(
        "microsoft_graph_callback/",
        users_import_to_workspace.microsoft_graph_callback,
        name="microsoft_graph_callback",
    ),
    path(
        "workspace/integrations/ig/pp/<uuid:id>",
        home.get_instagram_profile_pic,
        name="get_instagram_profile_pic",
    ),
    path(
        "workspace/next_engine_login/",
        next_engine_integration.next_engine_login,
        name="next_engine_login",
    ),
    path("workspace/portal/", store.index, name="store_index"),
    path("workspace/portal/update/", store.store_update, name="store_update"),
    path(
        "workspace/store/component/sort/",
        store.sort_store_components,
        name="sort_store_components",
    ),
    path(
        "workspace/portal/item/<uuid:id>/", store.store_item_row, name="store_item_row"
    ),
    path(
        "workspace/store/item/<uuid:id>/detail/",
        store.manage_store_item,
        name="manage_store_item",
    ),
    path(
        "workspace/portal/customer/allow/", store.allow_customer, name="allow_customer"
    ),
    path("workspace/data_transfer/", home.data_transfer, name="data_transfer"),
    path(
        "workspace/data_transfer/new/", home.data_transfer_new, name="data_transfer_new"
    ),
    path(
        "workspace/data_transfer/new/<import_export_type>",
        home.data_transfer_new,
        name="data_transfer_new",
    ),
    # Workspace Billing
    path(
        "workspace/starter/",
        workspace_billing.workspace_starter_tier,
        name="workspace_starter_tier",
    ),
    path(
        "workspace/meter/sync/",
        workspace_billing.sync_workspace_usage,
        name="sync_workspace_usage",
    ),
    path(
        "workspace/billing/",
        workspace_billing.workspace_billing,
        name="workspace_billing",
    ),
    path(
        "workspace/billing_confirmation/",
        workspace_billing.billing_confirmation,
        name="billing_confirmation",
    ),
    # Workspace Billing
    path(
        "workspace/billing/change_plan/",
        workspace_billing.workspace_billing_change_plan,
        name="workspace_billing_change_plan",
    ),
    path(
        "workspace/billing/plan_price/",
        workspace_billing.subscription_plan_price,
        name="subscription_plan_price",
    ),
    path(
        "workspace/billing/change_plan/summary/",
        workspace_billing.change_plan_summary,
        name="change_plan_summary",
    ),
    path(
        "workspace/billing/success/",
        workspace_billing.workspace_billing_success,
        name="workspace_billing_success",
    ),
    path(
        "workspace/billing/delete/<payment_id>",
        workspace_billing.workspace_delete_payment_method,
        name="workspace_delete_payment_method",
    ),
    path(
        "workspace/billing/default/<payment_id>",
        workspace_billing.workspace_set_default_payment_method,
        name="workspace_set_default_payment_method",
    ),
    path(
        "workspace/billing/credit_purchase/",
        workspace_billing.credit_purchase,
        name="credit_purchase",
    ),
    path(
        "workspace/billing/subscription/",
        workspace_billing.workspace_subscription,
        name="workspace_subscription",
    ),
    path(
        "workspace/billing/manual_billing/",
        workspace_billing.workspace_manual_billing,
        name="workspace_manual_billing",
    ),
    # Workspace
    path(
        "workspace/settings/",
        workspace_setting.workspace_setting,
        name="workspace_setting",
    ),
    # Workspace Modules
    path(
        "workspace/settings/modules/",
        workspace_module.workspace_modules,
        name="workspace_modules",
    ),
    path(
        "workspace/modules/initialize/",
        workspace_module.initialize_modules,
        name="initialize_modules",
    ),
    path(
        "workspace/settings/modules/name/",
        workspace_module.module_name,
        name="module_name",
    ),
    path(
        "workspace/settings/modules/sort/",
        workspace_module.sort_workspace_modules,
        name="sort_workspace_modules",
    ),
    path(
        "workspace/settings/modules/template/",
        workspace_module.module_templates,
        name="module_templates",
    ),
    path(
        "workspace/settings/modules/template/<str:slug>/",
        workspace_module.module_templates,
        name="module_templates",
    ),
    path(
        "workspace/settings/modules/slug/",
        workspace_module.module_slug,
        name="module_slug",
    ),
    # Workspace
    path("workspace/settings/logs/", workspace.workspace_logs, name="workspace_logs"),
    path(
        "workspace/info/", workspace.workspace_basic_info, name="workspace_basic_info"
    ),
    path("workspace/drawer/", workspace.load_drawer, name="workspace_load_drawer"),
    path("workspace/delete/", workspace.delete_workspace, name="delete_workspace"),
    path(
        "workspace/settings/delete_records/",
        workspace.delete_records,
        name="delete_records",
    ),
    path("workspace/settings/currency", workspace.currency, name="workspace_currency"),
    path("new_workspace/", workspace.new_workspace, name="new_workspace"),
    # object association label
    path(
        "workspace/settings/association_label/",
        association_labels.association_labels_settings,
        name="association_labels_settings",
    ),
    path(
        "workspace/settings/association_label/manage/",
        association_labels.manage_association_label,
        name="manage_association_label",
    ),
    path(
        "workspace/settings/association_label/manage_related/",
        association_labels.manage_related_association_label,
        name="manage_related_association_label",
    ),
    path(
        "workspace/settings/save_association_label/",
        association_labels.save_association_label_side_menu,
        name="save_association_label",
    ),
    # Workspace Group
    path("workspace/groups/", workspace.workspace_groups, name="workspace_groups"),
    path(
        "workspace/groups/<uuid:id>",
        workspace.group_teammate,
        name="workspace_group_teammate",
    ),
    # Workspace Search & Switch
    path("workspace/search/", workspace.search_workspaces, name="search_workspaces"),
    path("switch_workspace/", workspace.switch_workspace, name="switch_workspace"),
    # Workspace Custom Object
    path(
        "workspace/settings/create-custom-object/",
        workspace_custom_object.create_custom_object,
        name="create_custom_object",
    ),
    path(
        "workspace/settings/delete-custom-object/<id>/",
        workspace_custom_object.delete_custom_object,
        name="delete_custom_object",
    ),
    # Workspace Others
    path("workspace/settings/task", taskflow.app_settings, name="taskflow_settings"),
    path(
        "workspace/settings/object/visibility/",
        property.toggle_object_visibility,
        name="toggle_object_visibility",
    ),
    path("forbidden/", views.handle_ip_restriction, name="forbidden"),
    ############
    # DO NOT TOUCH THIS. EVEN REMOVING "/"" WILL BREAK THE CODE.
    ############
    path("stripe_config/", account_stripe.stripe_config),
    path("stripe_checkout/", account_stripe.stripe_checkout, name="stripe_checkout"),
    path("stripe_webhook/", account_stripe.stripe_webhook),  # for production
    path(
        "customer_stripe_webhook/<id>/", account_stripe.customer_stripe_webhook
    ),  # for production
    path(
        "stripe_webhook/<uuid:id>/", stripe_webhook.channel_stripe_webhook
    ),  # for production
    # Support
    path("support/tickets/", support.tickets, name="support"),
    path("support/new_thread/", support.new_thread, name="new_thread"),
    path("support/new_ticket/<str:id>", support.new_ticket, name="new_ticket"),
    # Partners
    path(
        "developers/workflows/dummy_users/", developers.dummy_users, name="dummy_users"
    ),
    path(
        "developers/workflows/dummy_users_id/<uuid:id>/",
        developers.dummy_users_id,
        name="dummy_users_id",
    ),
    path("developers/api", developers.developers_api, name="developers_api"),
    path("developers/api/createjwt", developers.create_api, name="create_api"),
    path("developers/api/viewjwt", developers.view_token, name="view_token"),
    path(
        "developers/api/createapiintegration",
        developers.create_api_integration,
        name="create_api_integration",
    ),
    # Content Management
    path("manage/workflows/", manager.manage_workflows, name="manage_workflows"),
    path(
        "manage/workflows/fixtures", manager.workflow_fixtures, name="workflow_fixtures"
    ),
    path(
        "manage/workflows/fixtures/items",
        manager.fixtures_shopturbo_items,
        name="fixtures_shopturbo_items",
    ),
    path(
        "manage/workflows/fixtures/property_custom_field",
        manager.fixtures_shopturbo_property_custom_field,
        name="fixtures_shopturbo_property_custom_field",
    ),
    path("manage/actions/", manager.manage_actions, name="manage_actions"),
    path("manage/reports/", manager.manage_reports, name="manage_reports"),
    path(
        "manage/integrations/", manager.manage_integrations, name="manage_integrations"
    ),
    path("manage/solutions/", manager.manage_solutions, name="manage_solutions"),
    path("manage/download_user/", manager.download_user, name="download_user"),
    # path('manage/download_pizzahut/', manager.generate_csv_pizzahut, name='download_pizzahut'),
    # path('manage/download_pizzahut/check/', manager.check_generate_csv_pizzahut, name='check_download_pizzahut'),
    # path('manage/download_pizzahut/continue/', manager.continue_generate_csv_pizzahut, name='continue_download_pizzahut'),
    # path('manage/download_pizzahut/delete/', manager.delete_event_file_pizzahut, name='delete_event_file_pizzahut'),
    path(
        "manage/merge_csv/", manager.merge_into_single_csv, name="merge_into_single_csv"
    ),
    path(
        "manage/distribution_insights/",
        manager.distribution_insights,
        name="distribution_insights",
    ),
    path("manage/data/", manager.manage_data, name="manage_data"),
    path("manage/users/", manager.manage_user, name="manage_user"),
    path("manage/payment/", manager.manage_payment, name="manage_payment"),
    path("manage/contents/<id>", manager.contents_edit, name="contents_edit"),
    path(
        "manage/contents_file/edit/",
        manager.contents_file_edit,
        name="contents_file_edit",
    ),
    path("manage/contents/<id>/del/", manager.contents_delete, name="contents_delete"),
    path("manage/contents/create/", manager.contents_create, name="contents_create"),
    path("manage/contents/<slug:slug>", manager.contents_edit, name="contents_edit"),
    path(
        "manage/contents/<slug:slug>/del",
        manager.contents_delete,
        name="contents_delete",
    ),
    path(
        "manage/contents_file_upload",
        manager.contents_file_upload,
        name="contents_file_upload",
    ),
    path("manage/function_logs", manager.function_logs, name="function_logs"),
    path(
        "manage/function_logs/details/<str:function_name>",
        manager.function_logs_details,
        name="function_logs_details",
    ),
    path(
        "manage/function_logs/traces/<str:operation_id>/<str:invocation_id>",
        manager.function_logs_details_traces,
        name="function_logs_details_traces",
    ),
    path(
        "price-info/load_price_info",
        price_information.load_price_info,
        name="load_price_info",
    ),
    path(
        "price-info/load_case_price_info",
        cases.load_price_info,
        name="load_case_price_info",
    ),
    path(
        "price-info/object/load_price_info",
        price_information.load_object_price_info,
        name="load_object_price_info",
    ),
    path("csv_export/", csv_export.load_content, name="csv_export_load_content"),
    path(
        "csv_export/load_manage_form/",
        csv_export.load_manage_form,
        name="load_new_form",
    ),
    path(
        "csv_export/load_manage_form/<uuid:id>",
        csv_export.load_manage_form,
        name="load_manage_form",
    ),
    path(
        "csv_export/submitexportproperty/",
        csv_export.submitExportProperty,
        name="submitExportProperty",
    ),
    path(
        "csv_export/submitexportpropertydefault/<uuid:id>",
        csv_export.submitExportPropertyDefault,
        name="submitExportPropertyDefault",
    ),
    # Workflows
    # path('workflows/uuid', workflow.workflows, name='main_workflows'),
    # shopify app proxy
    # localization
    path("i18n/", include("django.conf.urls.i18n")),
    path("i18n/", include("django.conf.urls.i18n")),
    path(
        "lang/<str:lang_code>/",
        home.redirect_language_get,
        name="redirect_language_get",
    ),
    path("change_language/", home.change_language, name="change_language"),
    # Override the default SAML ACS view to handle the SAML response
    path("<str:saml_slug>/saml/acs/", auth.saml_acs, name="saml_acs"),
    path("<str:saml_slug>/saml/login/", auth.saml_login, name="saml_login"),
    # SAML LOGIN AUTH
    # Select2 Endpoint
    path(
        "autocomplete/location/",
        inventory_warehouse.autocomplete_location,
        name="autocomplete_location",
    ),
    path(
        "autocomplete/expenses/",
        expense.autocomplete_expense,
        name="auto_complete_expense",
    ),
    path("autocomplete/bill/", bill.autocomplete_bill, name="auto_complete_bill"),
    path(
        "autocomplete/orders/", shopturbo.autocomplete_order, name="auto_complete_order"
    ),
    path("autocomplete/items/", shopturbo.autocomplete_item, name="auto_complete_item"),
    path(
        "autocomplete/item-properties/",
        actions.search_item_properties,
        name="search_item_properties",
    ),
    path(
        "autocomplete/subscriptions/",
        subscriptions.autocomplete_subscription,
        name="auto_complete_subscription",
    ),
    path(
        "autocomplete/invoices/",
        commerce_functions.autocomplete_invoice,
        name="auto_complete_invoice",
    ),
    path(
        "autocomplete/estimates/",
        commerce_functions.autocomplete_estimate,
        name="auto_complete_estimate",
    ),
    path(
        "autocomplete/payments/",
        commerce_functions.autocomplete_receipt,
        name="auto_complete_receipt",
    ),
    path(
        "autocomplete/delivery-slips/",
        commerce_functions.autocomplete_delivery_slip,
        name="auto_complete_delivery_slip",
    ),
    path(
        "autocomplete/slips/",
        commerce_functions.autocomplete_slip,
        name="auto_complete_slips",
    ),
    path(
        "autocomplete/contacts/",
        contacts.autocomplete_contact,
        name="auto_complete_contact",
    ),
    path(
        "autocomplete/companies/",
        contacts.autocomplete_company,
        name="auto_complete_company",
    ),
    path("autocomplete/cases/", cases.autocomplete_case, name="auto_complete_case"),
    path(
        "autocomplete/item-inventory/",
        items_inventory.autocomplete_inventory,
        name="auto_complete_inventory",
    ),
    path(
        "autocomplete/purchase-orders/",
        purchase_order.autocomplete_purchase_order,
        name="auto_complete_purchase_order",
    ),
    path(
        "autocomplete/journal-entry/",
        journal.autocomplete_journal_entry,
        name="auto_complete_journal_entry",
    ),
    path(
        "autocomplete/custom-object/",
        co_utility.autocomplete_custom_object,
        name="auto_complete_custom_object",
    ),
    # Custom Object Path
    path("custom-object/<uuid:id>/", co_main.custom_object, name="custom_object"),
    path(
        "custom-object-hubspot-save-mapping/",
        co_hubspot.custom_object_hubspot_save_mapping,
        name="custom_object_hubspot_save_mapping",
    ),
    path(
        "custom-object/drawer/",
        co_drawer.custom_object_drawer,
        name="custom_object_drawer",
    ),
    path(
        "custom-object/<uuid:id>/drawer/",
        co_drawer.custom_object_drawer,
        name="custom_object_drawer",
    ),
    path(
        "custom-object/<uuid:id>/create-object/",
        co_data_object.custom_object_create_object,
        name="custom_object_create_object",
    ),
    path(
        "custom-object/<uuid:id>/row-details/<uuid:row_id>/",
        co_data_object.custom_object_row_details,
        name="custom_object_row_details",
    ),
    path(
        "custom-object/<uuid:id>/manage/<uuid:row_id>/",
        co_data_object.custom_object_manage,
        name="custom_object_manage",
    ),
    path(
        "custom-object/<uuid:id>/sync_header_extractor/",
        co_integration.sync_custom_object_header_extractor,
        name="sync_custom_object_header_extractor",
    ),
    path(
        "custom-object/<uuid:id>/sync_custom_object_association_header_extractor/",
        co_integration.sync_custom_object_association_header_extractor,
        name="sync_custom_object_association_header_extractor",
    ),
    path(
        "custom-object/sync_header_extractor/",
        co_hubspot.sync_custom_object_hubspot_header_extractor,
        name="sync_custom_object_hubspot_header_extractor",
    ),
    path(
        "custom-object/api/get_integration_custom_object_options/",
        co_integration.get_integration_custom_object_options,
        name="get_integration_custom_object_options",
    ),
    path(
        "custom-object/<uuid:id>/settings/",
        co_utility.app_settings,
        name="custom_object_settings",
    ),
    path(
        "custom-object/<uuid:id>/hubspot-row-details/<uuid:row_id>/",
        co_hubspot.hubspot_custom_object_row_details,
        name="hubspot_custom_object_row_details",
    ),
    path(
        "custom-object/<uuid:id>/association/drawer/",
        co_association.custom_object_association_drawer,
        name="custom_object_association_drawer",
    ),
    path(
        "custom-object/<uuid:id>/association/manage/",
        co_association.custom_object_association_manage,
        name="custom_object_association_manage",
    ),
    path(
        "custom-object/<uuid:id>/options/",
        co_utility.get_custom_object_options,
        name="get_custom_object_options",
    ),
    path(
        "custom-object/action/import_custom_objects/",
        co_utility.import_custom_objects,
        name="action_import_custom_objects",
    ),
    path(
        "hubspot/custom-object/match_hubspot_custom_objects/<uuid:id>/",
        co_hubspot.hubspot_match_custom_object,
        name="hubspot_match_custom_object",
    ),
    # Advanced Search
    path(
        "advance-search/drawer/",
        advance_search.advance_search_drawer,
        name="advance_search_drawer",
    ),
    path(
        "advance-search/menu/",
        advance_search.advance_search_menu,
        name="advance_search_menu",
    ),
    path(
        "advance-search/template/",
        advance_search.advance_search_template,
        name="advance_search_template",
    ),
    path(
        "advance-search/update/",
        advance_search.advance_search_update,
        name="advance_search_update",
    ),
    # Object
    path("object/options/", object.object_options, name="object_options"),
    # SAN AI
    path(
        "san-ai/completion/", san_ai.san_api_completion, name="san_api_completion"
    ),  # Async view
    path("san-ai/view/", san_view.san_view, name="san_view"),
    #     SAN AI HISTORY Related
    path("san-ai/history/", san_session.load_san_history, name="load_san_history"),
    #     SAN AI SESSION Related
    path("san-ai/session/", san_session.get_san_session, name="get_san_session"),
    path(
        "san-ai/session/async/",
        san_session.load_san_session_async,
        name="load_san_session_async",
    ),
    path(
        "san-ai/session/conversation/",
        san_session.get_san_conversation,
        name="get_san_conversation",
    ),
    path(
        "san-ai/session/delete/",
        san_session.delete_san_session,
        name="delete_san_session",
    ),
    #     SAN AI FILE Related
    path("san-ai/file/url/", san_file.get_san_file_url, name="get_san_file_url"),
    path("san-ai/file/upload/", san_file.upload_file, name="upload_file"),
    path("san-ai/file/delete/", san_file.delete_file, name="delete_file"),
    #     SAN AI CANVAS Related
    path(
        "san-ai/canvas/table-data/",
        san_canvas.get_san_canvas_table_data,
        name="get_san_canvas_table_data",
    ),
    path(
        "san-ai/canvas/update/",
        san_canvas.update_san_canvas_table_data,
        name="update_san_canvas_table_data",
    ),
    #     SAN AI DEMO BOT Related
    path("san-ai/setup/runner/", demo_bot.demo_bot_runner, name="setup_runner"),
    # SAN AI BACKEND API
    path(
        "san-ai/expense/get_available_currency/",
        san_expense.get_available_currency,
        name="get_available_currency",
    ),
    # Bulk Update Record
    path(
        "object/bulk-update/",
        object.get_bulk_update_properties,
        name="get_bulk_update_properties",
    ),
    path(
        "object/bulk-update/update/",
        object.bulk_updates_objects,
        name="bulk_updates_objects",
    ),
    # Bulk Update Record
    path(
        "object/bulk-update/",
        object.get_bulk_update_properties,
        name="get_bulk_update_properties",
    ),
    path(
        "object/bulk-update/update/",
        object.bulk_updates_objects,
        name="bulk_updates_objects",
    ),
    # customizable pdf
    path(
        "custom-pdf/view-drawer/", custom_pdf.view_drawer, name="view_drawer_custom_pdf"
    ),
    path(
        "custom-pdf/view-drawer/<uuid:id>",
        custom_pdf.view_drawer,
        name="view_drawer_custom_pdf",
    ),
    path(
        "custom-pdf/load-custom-pdf-property/",
        custom_pdf.load_custom_pdf_property_form,
        name="load_custom_pdf_property_form",
    ),
    path("custom-pdf/<uuid:id>/duplicate", custom_pdf.duplicate, name="duplicate_pdf"),
    path("custom-pdf/<uuid:id>/delete", custom_pdf.delete, name="delete_custom_pdf"),
    path("custom-pdf/save", custom_pdf.save, name="save_custom_pdf"),
    path(
        "custom-pdf/generate/<uuid:id>/demo",
        custom_pdf.generate_pdf_demo,
        name="generate_pdf_demo",
    ),
    path(
        "custom-pdf/generate/<uuid:id>",
        custom_pdf.generate_pdf,
        name="generate_custom_pdf",
    ),
    path(
        "custom-pdf/generate-preview/<uuid:id>/",
        custom_pdf.generate_preview_pdf,
        name="generate_preview_pdf",
    ),
    path(
        "billing/send_mail/<uuid:id>/",
        commerce_functions.commerce_send_mail,
        name="commerce_send_mail",
    ),
    # Sheet http request
    path("items/list", sheet_views.items_list, name="sheet_item_list"),
    path("items/supplier/list", sheet_views.suppliers_list, name="sheet_supplier_list"),
    path("warehouse/list", sheet_views.warehouse_list, name="warehouse_list"),
    path(
        "inventory-transaction/stock-in",
        sheet_views.inventory_transaction,
        {"transaction_type": "in"},
        name="inventory_transaction_stock_in",
    ),
    path(
        "inventory-transaction/stock-out",
        sheet_views.inventory_transaction,
        {"transaction_type": "out"},
        name="inventory_transaction_stock_out",
    ),
    path("inventory/sheet", sheet_views.inventory, name="inventory_sheet"),
    path("company/sheet", sheet_views.company_list, name="company_sheet"),
    path("invoice/sheet", sheet_views.invoice, name="invoice_sheet"),
    path("expense/sheet", sheet_views.expense, name="expense_sheet"),
    path("expense/list/sheet", sheet_views.expense_list, name="expense_list_sheet"),
    path("subscription/sheet", sheet_views.subscription, name="subscription_sheet"),
    path(
        "subscription/list/sheet",
        sheet_views.subscription_list,
        name="subscription_list_sheet",
    ),
    path("account/sheet", sheet_views.account_list, name="account_list_sheet"),
    path("balance/sheet", sheet_views.balance, name="balance_sheet"),
    path("balance/sheet/all", sheet_views.balance_summary, name="all_balance_sheet"),
    prefix_default_language=False,
)

if settings.DEBUG:
    import debug_toolbar

    urlpatterns = urlpatterns + [
        path("__debug__/", include(debug_toolbar.urls)),
    ]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

handler404 = "data.views.handle_page_notfound"
handler500 = "data.views.handle_server_error"
