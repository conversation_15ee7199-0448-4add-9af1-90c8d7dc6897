import uuid

from django.contrib.auth.models import User
from django.db import models
from django.utils import timezone

from data.constants.constant import *
from data.models.base import save_tags_values, validate_value_custom_field
from data.models.constant import *


class ChatbotCategory(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.SlugField(max_length=100)

    title = models.CharField(max_length=500, null=False, blank=False)
    title_ja = models.CharField(max_length=500, null=False, blank=False)

    tagline = models.TextField(null=True, blank=True,)
    tagline_ja = models.TextField(null=True, blank=True,)

    description = models.TextField(null=True, blank=True,)
    description_ja = models.TextField(null=True, blank=True,)
    image_file = models.FileField(
        verbose_name="Image File", upload_to="chatbot-files", null=True, blank=True)
    icon = models.TextField(null=True, blank=True)
    order = models.IntegerField(null=True, blank=True)
    version = models.IntegerField(default=1)

    published = models.BooleanField(default=True)

    def __str__(self):
        return str(self.title) + '- slug:' + str(self.slug)


class ChatBot(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    chatbot_category = models.ForeignKey(
        ChatbotCategory, on_delete=models.CASCADE, null=True, blank=True)
    assistant_id = models.TextField(blank=True, null=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=256, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    knowledge = models.JSONField(null=True, blank=True)
    knowledge_file = models.FileField(
        verbose_name="Knowledge File", upload_to="knowledge-files", null=True, blank=True)
    installation_code = models.TextField(null=True, blank=True)
    version = models.IntegerField(default=1)
    created_at = models.DateTimeField(default=timezone.now)


MESSAGES_TYPES = [
    ('dm', 'Dms'),
    ('mention', 'Mentions'),
    ('draft', 'Draft'),
    ('email', 'Email'),
    ('call', 'Call'),
    ('chatbot', 'Chatbot'),
    ('sms', 'SMS'),
    ('line', 'LINE'),
    ('internal-chatbot', 'Internal Chatbot'),
]


class MessageThread(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    # channel posts / system posts
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True)
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    message_type = models.CharField(
        choices=MESSAGES_TYPES, max_length=40, null=True, blank=True)
    social_accounts = models.ManyToManyField("SocialAccount", blank=True)
    contacts = models.ManyToManyField("Contact", blank=True)
    company = models.ManyToManyField("Company", blank=True)
    title = models.TextField(blank=True, null=True)
    status = models.CharField(
        max_length=25, choices=THREAD_STATUS_CHOICES, blank=True, null=True)
    assignee = models.ForeignKey(
        User, blank=True, null=True, on_delete=models.SET_NULL)
    has_unread = models.BooleanField(default=False)
    test = models.BooleanField(default=False)
    meta_data = models.JSONField(null=True, blank=True)
    usage_status = models.CharField(
        max_length=25, choices=USAGE_STATUS, blank=True, null=True)

    # chatbot
    chatbot = models.ForeignKey(
        ChatBot, on_delete=models.SET_NULL, null=True, blank=True)
    ongoing_run = models.CharField(max_length=200, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)


MESSAGE_STATUS = [
    ('sent', 'Sent'),
    ('sending', 'Sending'),
    ('posted', 'Posted'),
    ('draft', 'Draft'),
    ('scheduled', 'Scheduled'),
    ('failed', 'Failed'),

    # Call
    ('queued', 'Queued'),
    ('initiated', 'Initiated'),
    ('ringing', 'Ringing'),
    ('in-progress', 'In Progress'),
    ('completed', 'Completed'),
    ('busy', 'Busy'),
    ('failed', 'Failed'),
    ('no-answer', 'No Answer'),
]


class Message(models.Model):
    thread = models.ForeignKey(
        MessageThread, on_delete=models.CASCADE, blank=True, null=True)
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    sender = models.ForeignKey(
        "SocialAccount", on_delete=models.CASCADE, blank=True, null=True)
    contact_sender = models.ForeignKey(
        "Contact", on_delete=models.SET_NULL, blank=True, null=True)
    internal_sender = models.ForeignKey(
        User, on_delete=models.CASCADE, blank=True, null=True)
    body = models.TextField(null=True, blank=True)
    date = models.DateTimeField(default=timezone.now)
    is_read = models.BooleanField(default=False)
    test = models.BooleanField(default=False)
    status = models.CharField(choices=MESSAGE_STATUS,
                              max_length=40, null=True, blank=True)
    status_body = models.TextField(null=True, blank=True)
    reply_token = models.TextField(null=True, blank=True)
    scheduled_at = models.DateTimeField(null=True, blank=True)
    to_username = models.TextField(null=True, blank=True)
    to_contactlist = models.TextField(null=True, blank=True)
    to_cc = models.TextField(null=True, blank=True)
    to_bcc = models.TextField(null=True, blank=True)
    # created at on the platform
    sent_at = models.DateTimeField(null=True, blank=True)
    to_phone = models.TextField(null=True, blank=True)
    from_phone = models.TextField(null=True, blank=True)
    duration = models.IntegerField(null=True, blank=True)
    from_bot = models.BooleanField(default=False, blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)


class MessageBodyTemplate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    body = models.TextField(null=True, blank=True)
    object_type = models.CharField(max_length=60, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)


class MessageVariableFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(
        Message, on_delete=models.CASCADE, null=True, blank=True)
    file = models.FileField(verbose_name="Message file",
                            upload_to="content-files")
    created_at = models.DateTimeField(default=timezone.now)


# ShopTurboShopTurbo
SHOPTURBO_NUMBER_FORMAT = [
    ('number', 'Number'),
    ('%', '%'),
    ('usd', 'USD'),
    ('jpy', 'JPY'),
    ('idr', 'IDR'),
]


class MessageNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=OBJECTS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    order = models.IntegerField(null=True, default=0, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)


class MessageValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        MessageNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    message = models.ForeignKey(MessageThread, on_delete=models.CASCADE,
                                null=True, blank=True, related_name='message_custom_field_relations')
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(verbose_name="Custom Field file",
                            upload_to="message-customfield-files", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == 'tag'):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


BOT_PDF_STATUS = [
    ('uploaded', 'Uploaded'),
    ('queue', 'Queue'),
    ('ocr_processing', 'OCR Processing'),
    ('ai_processing', 'AI Processing'),
    ('completed', 'Completed'),
    ('failed', 'Failed'),
]


class BotPdfUpload(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    file = models.FileField(verbose_name="file",
                            upload_to="bot-pdf", null=True, blank=True)
    object_type = models.CharField(max_length=100)
    status = models.CharField(
        max_length=30, choices=BOT_PDF_STATUS, default='uploaded')
    created_at = models.DateTimeField(default=timezone.now)
    error_message = models.TextField(blank=True, null=True)


TWILIO_CLIENT_STATUS_CHOICES = [
    ('active', 'Active'),
    ('inactive', 'Inactive'),
    ('active-oncall', 'On Call'),
    ('inactive-oncall', 'On Call'),
]


class TwilioClient(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    status = models.CharField(
        max_length=50, choices=TWILIO_CLIENT_STATUS_CHOICES, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


PAYMENT_TYPES = [
    ('payment_intent', 'Pay As You Go'),
]

PAYMENT_HISTORY_STATUS = [
    ('paid', 'Paid'),
    ('unpaid', 'Unpaid')

]

PAYMENT_TYPE = [
    ('top_up_credit', 'Top Up Credit'),
    ('usage', 'Usage')
]


class GeneratedEmails(models.Model):
    email_type = models.CharField(max_length=200, null=True, blank=True)
    subject = models.TextField(null=True, blank=True)
    body = models.TextField(null=True, blank=True)
    lang = models.CharField(max_length=200, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)


class ConversationLog(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    language = models.CharField(max_length=50, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)


class AnalyticsEvent(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.SET_NULL, null=True, blank=True)
    event = models.CharField(max_length=200, null=True, blank=True)
    slug = models.CharField(max_length=200, null=True, blank=True)
    referral = models.CharField(max_length=200, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    session = models.CharField(max_length=200, null=True, blank=True)
    utm_medium = models.CharField(max_length=200, null=True, blank=True)


class MessageTemplate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    template = models.TextField(null=True, blank=True)
    primary_category = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
