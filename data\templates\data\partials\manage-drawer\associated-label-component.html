{% load tz %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}
{% generate_uuid as my_uuid %}


<div class="border-gray-300 border-bottom mb-2">
    <div class="{% include "data/utility/association.html" %}">
        <div class="accordion-header mx-5 my-3 d-flex collapsed" data-bs-toggle="collapse" data-bs-target="#related_{{my_uuid}}">
            <span class="accordion-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-down accordion-icon-off" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
                </svg>
            </span>
            <div class="fs-4 fw-bold mb-0 ms-4">

                {% if not label.created_by_sanka %}
                    {{label.label}}
                {% else %}
                    {% with args=label.label|stringify|add:'|'|add:from %} 
                    {% with column_display=args|get_column_display:request %}
                        {{column_display.name}}
                    {% endwith %}
                    {% endwith %}
                {% endif %}

            </div>
        </div>
        <button class="align-items-center d-flex btn btn-dark btn-md create-association-wizard-button py-1 ms-3" type="button"
            
            {% if label.created_by_sanka and add_association_url %}
                hx-get="{{add_association_url}}" 
                hx-vals = '{
                    "view_id":"{{view_id}}", 
                    "object_id":"{{obj.id}}", 
                    "source":"{{source}}", 
                    "module":"{{module}}",
                    "page": "{{page}}",
                    "object_type": "{{label.label}}",
                    "from": "{{from}}"
                }'
            {% else %}
            hx-get="{% url 'load_association_label_form' %}" 
            hx-vals='{"obj_id":"{{obj.id}}", 
                      "property":"{{label.label}}", 
                      "page_group_type":"{{from}}", 
                      "side_menu":"True" }' 
            {% endif %}

            hx-target="#create-association-wizard-content"
            hx-swap="innerHTML"
            hx-indicator="#create-association-wizard-content" 
            
            
            >
            <span class="svg-icon svg-icon-4">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                    <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                    <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                </svg>
            </span>
    
            <span class="fs-7 ps-1 fw-bolder">
                {% if LANGUAGE_CODE == 'ja'%}
                追加
                {% else %}
                Add
                {% endif %}
            </span>
        </button> 
    </div>
    <div id="related_{{my_uuid}}" class="collapse fs-6 mx-5 my-3" data-bs-parent="#related_label_{{my_uuid}}">
       
        {% get_association_members label obj as association_objects %}

        {% if constant.TYPE_OBJECT_CONTACT in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_CONTACT from obj.id as accordion_type %}
            {% for contact in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                        hx-vals='{"source":"{{source}}","hide_associated_data":true}'
                        hx-trigger="click"
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">  
                        
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{contact.contact_id|stringformat:"04d"}} | {{contact|display_contact_name:LANGUAGE_CODE}} ({% if LANGUAGE_CODE == 'ja'%}連絡先{% else %}Contact{% endif %})
                            </div>
                        </div>

                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}
        
        {% if constant.TYPE_OBJECT_COMPANY in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_COMPANY from obj.id as accordion_type %}
            {% for company in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                        hx-vals='{"source":"{{source}}","hide_associated_data":true}'
                        hx-trigger="click"
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            
                            <div class="mb-1">
                                #{{company.company_id|stringformat:"04d"}} | {{company.name}} ({% if LANGUAGE_CODE == 'ja'%}企業{% else %}Company{% endif %})
                            </div>
                        </div>
                    </button>
               
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}
            
        {% if constant.TYPE_OBJECT_ESTIMATE in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_ESTIMATE from obj.id as accordion_type %}
            {% for estimate in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        hx-get="{% host_url 'estimate_edit' estimate.id host 'app' %}" 
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true"}'
                        hx-indicator=".loading-drawer-spinner"
                        >  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{estimate.id_est|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}見積{% else %}Estimate{% endif %}
                            </div>
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_PURCHASE_ORDER in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_PURCHASE_ORDER from obj.id as accordion_type %}
            {% for purchase_order in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        onclick="openRelatedDrawerWithParent()"
                        hx-get="{% host_url 'purchase_manage' purchase_order.id host 'app' %}?view_id={{view_id}}&page={{page}}&module={{menu_key}}&selected_order_id={{order.id}}&source={{constant.TYPE_OBJECT_ORDER}}" 
                        hx-vals='{"hide_associated_data":true, "from_drawer": "order"}'
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-indicator=".loading-drawer-spinner"
                        hx-on::after-request="ensureOrderDrawerStaysOpen()">
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{purchase_order.id_po|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}発注{% else %}Purchase Order{% endif %}
                            </div>
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_INVENTORY_TRANSACTION|in_list:label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_INVENTORY_TRANSACTION from obj.id as accordion_type %}
            {% for transaction in association_objects|get_association_object_by_type:accordion_type %}
                {% if transaction.usage_status == 'active' %}
                    <div class="p-1 px-3 w-100 justify-content-between rounded border align-items-center text-start bg-white text-primary d-flex mb-3"
                        id="related-inventory-transaction-{{transaction.id}}">
                        <button class="fs-6 bg-white fw-bold border-0 mb-2 py-4 px-3 text-start text-primary hover:text-primary-500 manage-related-wizard-button"
                            type="button"
                            hx-get="{% url 'shopturbo_load_drawer' %}" 
                            hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-transaction-manage", "transaction_id":"{{transaction.id}}", "from": "{{from}}", "hide_associated_data": true }'
                            hx-target="#manage-related-drawer-content"
                            hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">

                            <div class="mb-1">
                                {% get_object_display transaction 'commerce_inventory_transaction' %}
                            </div>
                            <div>
                                {% if transaction.inventory.inventory_status == 'available'%}
                                    {% if LANGUAGE_CODE == 'ja'%}販売可能{% else %}Available{% endif %}
                                {% elif transaction.inventory.inventory_status == 'committed'%}
                                    {% if LANGUAGE_CODE == 'ja'%}確定済み{% else %}Committed{% endif %}
                                {% elif transaction.inventory.inventory_status == 'unavailable'%}
                                    {% if LANGUAGE_CODE == 'ja'%}販売不可{% else %}Unavailable{% endif %}
                                {% endif %} - 
                                {% if transaction.transaction_type == 'in' %}
                                    {% if LANGUAGE_CODE == 'ja'%}入庫{% else %}Stock In{% endif %}
                                {% elif transaction.transaction_type == 'out'%}
                                    {% if LANGUAGE_CODE == 'ja'%}出庫{% else %}Stock Out{% endif %}
                                {% elif transaction.transaction_type == 'adjust' %}
                                    {% if LANGUAGE_CODE == 'ja'%}調整{% else %}Adjust{% endif %}
                                {% endif %}
                                : {{transaction.amount}}
                            </div>
                        </button>
                    </div>
                {% endif %}
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_INVOICE in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_INVOICE from obj.id as accordion_type %}
            {% for invoice in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        onclick="clickRelation()"
                        hx-get="{% host_url 'invoice_edit' invoice.id host 'app' %}"
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"hide_associated_data": "true"}'
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content"
                        >  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                {% if invoice.contact %}
                                    #{{invoice.id_inv|stringformat:'04d'}} {% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoice{% endif %} | {{invoice.contact|display_contact_name:LANGUAGE_CODE}}
                                {% elif invoice.company %}
                                    #{{invoice.id_inv|stringformat:'04d'}} {% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoice{% endif %} | {{invoice.company.name}}
                                {% else %}
                                    #{{invoice.id_inv|stringformat:'04d'}} {% if LANGUAGE_CODE == 'ja'%}売上請求{% else %}Invoice{% endif %} | (Unnamed)
                                {% endif %}
                            </div>
                        </div>
                    </button>
                    
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}


        {% if constant.TYPE_OBJECT_SUBSCRIPTION in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_SUBSCRIPTION from obj.id as accordion_type %}
            {% for subscription in association_objects|get_association_object_by_type:accordion_type %}
            <div class="d-flex">
                <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                    hx-get="{% host_url 'load_manage_subscriptions_drawer' host 'app' %}" 
                    hx-target="#manage-related-drawer-content"
                    hx-trigger="click"
                    hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true", "drawer_type":"subscriptions-manage", "subscription_id":"{{subscription.id}}"}'
                    hx-indicator=".loading-drawer-spinner">  
                    <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                        <div class="mb-1">
                            #{{subscription.subscriptions_id|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}サブスクリプション{% else %}Subscription{% endif %}
                        </div>
                    </div>
                </button>
            </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_CASE in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_CASE from obj.id as accordion_type %}
            {% for case in association_objects|get_association_object_by_type:accordion_type %}

                <div class="d-flex w-100">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        onclick="clickRelation()"
                        hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                        hx-vals = '{"drawer_type":"manage-deal", "deal_id":"{{case.id}}" ,"view_id":"{{view_id}}", "page": "{{page}}","hide_associated_data":true}'
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content"
                        >  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{case.deal_id|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}案件{% else %}Case{% endif %}
                            </div>
                            <div class="text-gray-600 fs-7">
                                {{case.name|default:"(Unnamed)"}}
                            </div>
                            {% if case.case_status %}
                            <div class="text-gray-500 fs-8 mt-1">
                                {% if LANGUAGE_CODE == 'ja' %}
                                    ステータス: {{case.case_status}}
                                {% else %}
                                    Status: {{case.case_status}}
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}


        {% if constant.TYPE_OBJECT_DELIVERY_NOTE in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_DELIVERY_NOTE from obj.id as accordion_type %}
            {% for delivery_note in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        hx-get="{% host_url 'delivery_slip_edit' delivery_note.id host 'app' %}" 
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true"}'
                        hx-indicator=".loading-drawer-spinner">  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{delivery_note.id_ds|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}納品{% else %}Delivery Note{% endif %}
                            </div>
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_ORDER in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_ORDER from obj.id as accordion_type %}
            {% for order in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}" 
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true", "drawer_type":"manage-orders", "order_id":"{{order.id}}"}'
                        hx-indicator=".loading-drawer-spinner">  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{order.order_id|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}受注{% else %}Order{% endif %}
                            </div>
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}
        
        {% if label.object_target|is_uuid %}
            {% combine_text_with_pipe label.object_target from obj.id as accordion_type %}
            {% for row in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        hx-get="{% host_url 'custom_object_drawer' label.object_target host 'app' %}"
                        hx-vals = '{"drawer-type":"manage", "row_id":"{{row.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}"}'
                        hx-target="#manage-related-drawer-content"
                        hx-swap="innerHTML"
                        hx-indicator=".loading-drawer-spinner"
                        >
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                {% get_object_display row 'custom_object' %} 
                            </div>
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %} 
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_RECEIPT in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_RECEIPT from obj.id as accordion_type %}
            {% for receipt in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        onclick="clickRelation()"
                        hx-get="{% host_url 'receipt_edit' receipt.id host 'app' %}" 
                        hx-vals = 'js:{"source_url": getDrawerURL(), "hide_associated_data": "true"}'
                        hx-trigger="click"
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">   
                        <div class="d-flex p-2 rounded-2 bg-white border fw-bolder mb-2 align-items-center">
                            <h5 class="mx-2 my-0">
                                {% get_object_display receipt 'receipts' %}
                            </h5>
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_BILL in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_BILL from obj.id as accordion_type %}
            {% for bill in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button"
                        type="button"
                        hx-get="{% host_url 'bill_manage' bill.id host 'app' %}"
                        hx-target="#manage-related-drawer-content"
                        hx-trigger="click"
                        hx-vals = '{"set_id": "{{set_id}}", "hide_associated_data": "true", "from": "company"}'
                        id="worker_wizard"
                        hx-indicator=".loading-drawer-spinner,.manage-related-wizard-button"
                        >
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                #{{bill.id_bill|stringformat:"04d"}}
                            </div>
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_EXPENSE in label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_EXPENSE from obj.id as accordion_type %}
            {% for expense in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button id="related_btn" class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        onclick="clickRelation()"
                        hx-get="{% host_url 'expense_drawer' expense.id host 'app' %}" 
                        hx-vals = 'js:{"source_url": getDrawerURL(), "view_id":"{{view_id}}", "page":"{{page}}", "module":"{{module.slug}}", "hide_associated_data": "true"}'
                        hx-trigger="click"
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">   
                        <div class="d-flex p-2 rounded-2 bg-white border fw-bolder mb-2 align-items-center">
                            <h5 class="mx-2 my-0">
                                #{{expense.id_pm|stringformat:"04d"}} {% if LANGUAGE_CODE == 'ja'%}経費{% else %}Expense{% endif %}
                            </h5>
                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}
              
        {% if constant.TYPE_OBJECT_INVENTORY|in_list:label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_INVENTORY from obj.id as accordion_type %}
            {% for inventory in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="py-0 pe-0 btn w-100 justify-content-start text-start bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        hx-get="{% url 'shopturbo_load_drawer' %}"
                        hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-manage", "inventory_id":"{{inventory.id}}", "view_id": "{{view_id}}", "hide_associated_data": true, "from": "commerce_inventory", "from": "{{from}}"  }'
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content">  
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">

                            <div class="mb-1">
                                {% get_object_display inventory 'commerce_inventory' %}
                            </div>

                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_INVENTORY_WAREHOUSE|in_list:label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_INVENTORY_WAREHOUSE from obj.id as accordion_type %}
            {% for location in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="btn bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"inventory-warehouse-manage", "warehouse_id":"{{location.id}}", "hide_associated_data": "true", "from": "{{from}}" }'
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content"
                        hx-trigger="click">
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">
                            <div class="mb-1">
                                    #{{location.id_iw}} - {% if location.location %}{{location.location}}{% else %}{{location.id|get_inventory_warehouse_name:request}}{% endif %}
                            </div>

                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        {% if constant.TYPE_OBJECT_ITEM|in_list:label.object_target %}
            {% combine_text_with_pipe constant.TYPE_OBJECT_ITEM from obj.id as accordion_type %}
            {% for item_ in association_objects|get_association_object_by_type:accordion_type %}
                <div class="d-flex">
                    <button class="btn bg-white cursor-pointer text-primary manage-related-wizard-button" type="button"
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals = 'js:{"source_url": getDrawerURL(), "drawer_type":"item-manage", "item_id":"{{item_.id}}", "page": "{{page}}", "with_related_data": 0, "module": "{{menu_key}}", "hide_associated_data": "true", "from": "{{from}}"}'
                        hx-target="#manage-related-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#manage-related-drawer-content"
                        hx-trigger="click">
                        <div class="rounded fs-6 bg-white fw-bold border mb-2 py-4 px-6">

                            <div class="mb-1">
                                {% get_object_display item_ 'commerce_items' %}
                            </div>

                        </div>
                    </button>
                </div>
            {% if forloop.last %}
            <script>
                $(document).ready(function() {
                    document.getElementById("no_related_data_{{my_uuid}}").classList.add("d-none");
                })
            </script>
            {% endif %}
            {% endfor %}
        {% endif %}

        <div id="no_related_data_{{my_uuid}}" class="text-center text-gray-500">
            {% if LANGUAGE_CODE == 'ja' %}
                関連データなし
            {% else %}
                No related data
            {% endif %}
        </div>

    </div>
</div>


<script>
    function getDrawerURL() {
        return encodeURIComponent(window.location.href.replace(window.location.search, "") + "{{drawer_query_params|safe}}");
    }
    
</script>