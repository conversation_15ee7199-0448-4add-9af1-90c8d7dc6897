{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}


<div class="mb-10" id="manage-task-app">
    <div class="">
        <h1 class="">
            {% if LANGUAGE_CODE == 'ja'%}
            タスク設定
            {% else %}
            Task Settings 
            {% endif %}
        </h1>
    </div>

    {% if project %}
    <div class="mt-10">
        <div id="taskflow-setting-section" class="mb-10">
            
            {% include "data/projects/component/taskflow_project.html" %}
            
            <form method="POST" action="{% host_url 'taskflow_settings' host 'app' %}" class="" id="taskflow_settings" enctype="multipart/form-data"
                onkeydown="return event.key != 'Enter';">
                {% csrf_token %}

                <input type="hidden" class="" name="app_settings" value="manage_app"/>

                {% if project %}
                <input type="hidden" class="" name="p_id" value="{{project.id}}"/>
                {% endif %}
                
                <div
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "task", "p_id": "{{project.id}}"}'
                    hx-trigger="load"
                    hx-target="#properties-table"
                >
                    <div id="properties-table"></div>
                </div>

                <button name="" type="submit" class="btn btn-dark mt-5">
                    {% if LANGUAGE_CODE == 'ja'%}
                    更新
                    {% else %}
                    Update
                    {% endif %}
                </button>
                <style>
                    #task-field-indicator{
                        display:none;
                    }
                    .htmx-request#task-field-indicator{
                        display:inline-block;
                    }
                </style>
                <span class="spinner-border spinner-border-sm text-secondary task-field-indicator" id="task-field-indicator" style="position:relative; right:-6px" role="status">
                    <span class="visually-hidden">Loading...</span>
                </span>
            </form>
        </div>
    </div>
    {% else %}
    <div class="col-12 mt-5">
        <div class="card border mb-5">
            <div class="card-body">
                <h1>
                    {% if LANGUAGE_CODE == 'ja'%}
                    プロジェクトを作成
                    {% else %}
                    Create your Project 
                    {% endif %}
                </h1>
                <p class="fs-4">
                    {% if LANGUAGE_CODE == 'ja'%}
                    プロジェクトを作成して、業務管理を行いましょう。
                    {% else %}
                    Create your first project to streamline operations!
                    {% endif %}
                </p>
                <button id="channel_create_button" class="btn btn-light-primary view-wizard-button"
                    hx-vals='{"source": "workspace_setting", "module_slug" : "{{module.slug}}", "module_object": "{{module_object}}"}'
                    hx-get="{% url 'projects_settings' %}" 
                    hx-target="#view-drawer">
                    {% if LANGUAGE_CODE == 'ja'%}
                        新規プロジェクト
                    {% else %}
                        New Project
                    {% endif %}
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% endblock %}
{% block js %}
<script>
    
    $(document).ready(function(){
        $('.task_wizard .select2-this').select2();

        function checking_type(elm){
            var number_format_id = elm.id.replace('type', 'number_format');
            var number_format_elm=document.getElementById(number_format_id)

            var choice_id = elm.id.replace('type', 'choice');
            var choice_elm=document.getElementById(choice_id)

            if (elm.value=='number'){
                if (number_format_elm){
                    number_format_elm.classList.remove("d-none")
                    choice_elm.classList.add("d-none")
                }
                
            } 
            else if (elm.value=='choice'){
                if (choice_elm){
                    choice_elm.classList.remove("d-none")
                    number_format_elm.classList.add("d-none")
                }
            }
            else{
                if (number_format_elm){
                    number_format_elm.classList.add("d-none")
                }
                if (choice_elm){
                    choice_elm.classList.add("d-none")
                }
            }
        }
})
</script>
{% endblock %}