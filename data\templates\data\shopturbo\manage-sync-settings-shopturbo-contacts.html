{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="{% include "data/utility/manage-sync.html" %}" id="manage-sync">
    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if import_export_type == 'import' %}
                {% if LANGUAGE_CODE == 'ja'%}
                インポート
                {% else %}
                Import
                {% endif %}
            {% elif import_export_type == 'export' %}
                {% if LANGUAGE_CODE == 'ja'%}
                エクスポート
                {% else %}
                Export 
                {% endif %}
            {% endif %}
        
        </h5>

        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    

    <div class="{% include "data/utility/import-tab.html" %}" role="tabpanel">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="d-flex align-items-center justify-content-between w-100" >
                <div class="{% include "data/utility/view-menu-default-common.html" %} me-2 border-end-0">
                    <div class="d-flex align-items-center me-3">
                        <a id="main-tab" onclick="showMainContent(this)"  class="tab-switcher fw-bolder nav-link ms-0 me-0 active border-bottom-2" type="button"
                        {% if import_export_type == 'import'%}
                            {% if page_group_type == 'customer_case' %}
                                hx-get="{% url 'case_load_drawer' %}" 
                                hx-vals = '{"drawer_type":"case-view-export-import","section":"import_selector","import_export_type":"{{import_export_type}}","contact_ids":"{{contact_ids}}","view_id":"{{view_id}}" }'
                            {% elif page_group_type == 'company' %}
                                hx-get="{% url 'load_drawer_sync_contacts' %}" 
                                hx-vals = '{"drawer_type":"company-view-export-import","section":"import_selector","import_export_type":"{{import_export_type}}","view_id":"{{view_id}}" }'
                            {% else %}
                                hx-get="{% url 'load_drawer_sync_contacts' %}" 
                                hx-vals = '{"drawer_type":"shopturbo-view-sync-contacts","section":"import_selector","import_export_type":"{{import_export_type}}","contact_ids":"{{contact_ids}}","view_id":"{{view_id}}" }'
                            {% endif %}
                        {% else %}
                            {% if page_group_type == 'customer_case' %}
                                hx-get="{% url 'case_load_drawer' %}" 
                                hx-vals = '{"drawer_type":"case-view-export-import","section":"export_selector","import_export_type":"{{import_export_type}}","contact_ids":"{{contact_ids}}","view_id":"{{view_id}}" }'
                            {% elif page_group_type == 'company' %}
                                hx-get="{% url 'load_drawer_sync_contacts' %}" 
                                hx-vals = '{"drawer_type":"company-view-export-import","section":"export_selector","import_export_type":"{{import_export_type}}","company_ids":"{{company_ids}}","view_id":"{{view_id}}" }'
                            {% else %}
                                hx-get="{% url 'load_drawer_sync_contacts' %}" 
                                hx-vals = '{"drawer_type":"shopturbo-view-sync-contacts","section":"export_selector","import_export_type":"{{import_export_type}}","contact_ids":"{{contact_ids}}","view_id":"{{view_id}}" }'
                            {% endif %}
                        {% endif %}
                        hx-target="#import-and-export-container" 
                        hx-trigger="load,click"
                        hx-on::before-request="document.getElementById('import-and-export-container').innerHTML = ''"
                        
                        >{% if import_export_type == 'import' %}
                            {% if LANGUAGE_CODE == 'ja'%}インポート{% else %}Import{% endif %}
                        {% elif import_export_type == 'export' %}
                            {% if LANGUAGE_CODE == 'ja'%}エクスポート{% else %}Export{% endif %}
                        {% endif %}
                        </a>
                    </div> 
                    <div class="d-flex align-items-center me-3">
                        <a id="history-tab" onclick="showHistory(this)" 
                        class="tab-switcher fw-bolder nav-link ms-1 me-0" type="button"
                        
                        {% if page_group_type == 'customer_case' %}
                            hx-get="{% url 'case_load_drawer' %}" 
                            hx-vals = '{"drawer_type":"case-view-export-import","import_export_type":"{{import_export_type}}","section":"history" }'
                        {% elif page_group_type == 'company' %}
                            hx-get="{% url 'load_drawer_sync_contacts' %}" 
                            hx-vals = '{"drawer_type":"company-view-export-import","import_export_type":"{{import_export_type}}","section":"history" }'
                        {% else %}
                            hx-get="{% url 'load_drawer_sync_contacts' %}" 
                            hx-vals = '{"drawer_type":"shopturbo-view-sync-contacts","import_export_type":"{{import_export_type}}","section":"history" }'
                        {% endif %}
                        hx-target="#import-and-export-container" 
                        
                        hx-trigger="click"

                        >{% if LANGUAGE_CODE == 'ja'%}履歴{% else %}History{% endif %}
                        </a>
                    </div>
                
                    {% if import_export_type == 'export' %}
                    <div class="d-flex align-items-center me-3">
                        <a id="template-tab" onclick="showTemplate(this)" 
                        class="tab-switcher fw-bolder nav-link ms-1 me-0" type="button"

                        hx-get="{% url 'csv_export_load_content' %}" 
                    
                        hx-vals = '{"import_export_type":"{{import_export_type}}","page_group_type":"{{page_group_type}}" }'
                        
                        hx-target="#csv_export" 
                        hx-trigger="click"

                        >{% if LANGUAGE_CODE == 'ja' %}テンプレート{% else %}Template{% endif %}
                        </a>
                    </div>
                    {% endif %}
                    

                </div> 
            </div> 
        </div> 
    </div>


    <div id="import-and-export-container" ></div>
    <div id="csv_export" ></div>
    
</div>

<style>
    #task-field-indicator{
        display:none;
    }
    .htmx-request#task-field-indicator{
        display:inline-block;
    }
</style>

<script>
    $('.select2-this').select2();
</script>

<script>
    var history_selector = document.getElementById("history-tab");
    var main_selector = document.getElementById("main-tab");
    var template_selector = document.getElementById("template-tab");
    

    function showHistory(elm) {
        elm.classList.add("active","border-bottom-2");
        main_selector.classList.remove("active","border-bottom-2");
        template_selector.classList.remove("active","border-bottom-2");
        document.getElementById('csv_export').classList.add('d-none');
        document.getElementById('import-and-export-container').classList.remove('d-none')
        
    }

    function showMainContent(elm) {
        elm.classList.add("active","border-bottom-2");
        history_selector.classList.remove("active","border-bottom-2");
        template_selector.classList.remove("active","border-bottom-2");
        document.getElementById('csv_export').classList.add('d-none');
        document.getElementById('import-and-export-container').classList.remove('d-none')
    }

    function showTemplate(elm) {
        elm.classList.add("active","border-bottom-2");
        main_selector.classList.remove("active","border-bottom-2");
        history_selector.classList.remove("active","border-bottom-2");
        document.getElementById('import-and-export-container').classList.add('d-none')
        document.getElementById('csv_export').classList.remove('d-none')
    }
</script>


