import math
import traceback
import uuid

from django.db import models
from django.db.models import F, Func
from django.db.models import IntegerField
from django.db.models.functions import Cast

from data.constants.date_constant import DEFAULT_DATE_FORMAT
from data.constants.properties_constant import (
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SLIP,
)
from data.models import (
    Company,
    Contact,
    DateFormat,
    Invoice,
    Notification,
    ShopTurboDecimalPoint,
    ShopTurboItems,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsPrice,
    ShopTurboItemsValueCustomField,
)
from utils import formula
from utils.date import format_date, format_date_to_str
from utils.inventory import item_inventory_amount
from utils.logger import logger
from utils.properties.properties import get_page_object
from utils.utility import get_download_value


def is_valid_uuid(input_str):
    try:
        # Check if input_str is a model instance with an id attribute
        if hasattr(input_str, "id"):
            input_str = input_str.id

        input_str = str(input_str).replace(" ", "")
        uuid.UUID(input_str)
        return True
    except Exception:
        return False


def handling_items(obj, lang="en", object_type=None):
    type_obj = str(obj)
    workspace = obj.workspace

    # DEBUG: Add debugging for invoice 7d0ca0f1-d6dc-478d-b085-1548c94edc28
    debug_invoice_id = "7d0ca0f1-d6dc-478d-b085-1548c94edc28"
    is_debug_invoice = str(obj.id) == debug_invoice_id

    # Force debug mode for this invoice
    if is_debug_invoice:
        print(f"\n🔍 DEBUGGING handling_items for invoice {obj.id} 🔍")

    if is_debug_invoice:
        print(f"\n=== DEBUG handling_items for invoice {obj.id} ===")
        print(f"Object type: {type_obj}")
        print(f"Discount: {getattr(obj, 'discount', 'None')}")
        print(f"Discount option: {getattr(obj, 'discount_option', 'None')}")
        print(f"Discount tax option: {getattr(obj, 'discount_tax_option', 'None')}")
        print(f"Tax inclusive: {getattr(obj, 'tax_inclusive', 'None')}")
        print(f"Tax option: {getattr(obj, 'tax_option', 'None')}")
        print(f"Current total_price: {getattr(obj, 'total_price', 'None')}")
        print(f"Current total_price_without_tax: {getattr(obj, 'total_price_without_tax', 'None')}")
    decimal_point = ShopTurboDecimalPoint.objects.filter(
        workspace=workspace, app_name="general"
    ).first()
    if not object_type:
        if "Estimate" in type_obj:
            type_obj = TYPE_OBJECT_ESTIMATE
        elif "Invoice" in type_obj:
            type_obj = TYPE_OBJECT_INVOICE
        elif "Receipt" in type_obj:
            type_obj = TYPE_OBJECT_RECEIPT
        elif "DeliverySlip" in type_obj:
            type_obj = TYPE_OBJECT_DELIVERY_NOTE
        elif "Slip" in type_obj:
            type_obj = TYPE_OBJECT_SLIP
        elif "ShopTurboOrders" in type_obj:
            type_obj = TYPE_OBJECT_ORDER
        else:
            return None
    else:
        type_obj = object_type
    page_obj = get_page_object(type_obj, lang)
    item_model = page_obj["item_model"]
    field_item_name = page_obj["field_item_name"]

    total_price = 0.0
    total_price_without_tax = 0.0
    obj_items = []
    obj_amounts = []
    obj_amount_items = []
    obj_tax_lists = []
    obj_total_price_without_tax = []
    obj_total_price = []

    # Handling Amount Price
    if type_obj:
        objs_item = item_model.objects.filter(**{field_item_name: obj}).order_by(
            "created_at"
        )
    else:
        return obj

    if not objs_item and type_obj != TYPE_OBJECT_RECEIPT:
        obj.total_price_without_tax = 0
        obj.total_price = 0
        obj.save()
        return obj

    # Start Counting Item Value
    try:
        if objs_item:
            if type_obj == TYPE_OBJECT_ORDER:
                for obj_item in objs_item:
                    # Handle both linked items and custom items
                    if obj_item.item:
                        obj_items.append(obj_item.item.name)
                        obj_amounts.append(
                            obj_item.item_price.price
                            if obj_item.item_price
                            else obj_item.item_price_order
                        )
                        obj_tax_lists.append(obj_item.item.tax)
                        tax_item_rate = obj_item.item.tax
                    else:
                        # Custom item without item link
                        obj_items.append(obj_item.custom_item_name or "Custom Item")
                        obj_amounts.append(obj_item.item_price_order)
                        obj_tax_lists.append(0)  # Default tax for custom items
                        tax_item_rate = 0

                    obj_amount_items.append(obj_item.number_item)
                    if not tax_item_rate:
                        tax_item_rate = "0"

                    # Get the price for calculations (either from item_price or item_price_order)
                    item_price = (
                        obj_item.item_price.price
                        if obj_item.item_price
                        else obj_item.item_price_order
                    )
                    if not item_price:
                        item_price = 0

                    if obj.tax:
                        obj_item.total_price_without_tax = (
                            float(item_price)
                            * float(obj_item.number_item)
                            / (1 + (float(tax_item_rate) / 100.0))
                        )
                        obj_item.total_price = float(item_price) * float(
                            obj_item.number_item
                        )
                    else:
                        if tax_item_rate != "0" and tax_item_rate != "None":
                            if item_price and obj_item.number_item:
                                obj_item.total_price_without_tax = float(
                                    item_price
                                ) * float(obj_item.number_item)
                            else:
                                obj_item.total_price_without_tax = 0
                            obj_item.total_price = (
                                float(item_price) * (float(tax_item_rate) / 100.0)
                                + float(item_price)
                            ) * float(obj_item.number_item)
                        else:
                            if (
                                item_price
                                and obj_item.number_item
                                and item_price != "None"
                                and obj_item.number_item != "None"
                            ):
                                obj_item.total_price_without_tax = float(
                                    item_price
                                ) * float(obj_item.number_item)
                                obj_item.total_price = float(item_price) * float(
                                    obj_item.number_item
                                )
                            else:
                                obj_item.total_price_without_tax = 0
                                obj_item.total_price = 0
                    obj_total_price_without_tax.append(obj_item.total_price_without_tax)
                    obj_total_price.append(obj_item.total_price)
                    obj_item.save()
            else:
                for obj_item in objs_item:
                    if obj_item.item_link:
                        obj_items.append(obj_item.item_link.name)
                    else:
                        obj_items.append(obj_item.item_name)
                    obj_amounts.append(obj_item.amount_price)
                    obj_amount_items.append(obj_item.amount_item)
                    obj_tax_lists.append(obj_item.tax_rate)
                    tax_item_rate = obj_item.tax_rate
                    if not tax_item_rate:
                        tax_item_rate = "0"
                    if obj.tax_inclusive:
                        if obj.currency.lower() == "jpy":
                            obj_item.total_price_without_tax = (
                                float(obj_item.amount_price)
                                / (1 + (float(tax_item_rate) / 100.0))
                            ) * float(obj_item.amount_item)
                            # Round to integer for JPY
                            obj_item.total_price_without_tax = int(obj_item.total_price_without_tax)
                        else:
                            obj_item.total_price_without_tax = (
                                float(obj_item.amount_price)
                                * float(obj_item.amount_item)
                                / (1 + (float(tax_item_rate) / 100.0))
                            )
                        obj_item.total_price = float(obj_item.amount_price) * float(
                            obj_item.amount_item
                        )
                    else:
                        if tax_item_rate != "0" and tax_item_rate != "None":
                            if obj_item.amount_price and obj_item.amount_item:
                                obj_item.total_price_without_tax = float(
                                    obj_item.amount_price
                                ) * float(obj_item.amount_item)
                            else:
                                obj_item.total_price_without_tax = 0

                            if obj.currency.lower() == "jpy":
                                obj_item.total_price = (
                                    float(obj_item.amount_price)
                                    * float(obj_item.amount_item)
                                ) + float(
                                    (
                                        float(obj_item.amount_price)
                                        * float(obj_item.amount_item)
                                    )
                                    * (float(tax_item_rate) / 100.0)
                                )
                            else:
                                obj_item.total_price = (
                                    float(obj_item.amount_price)
                                    * (float(tax_item_rate) / 100.0)
                                    + float(obj_item.amount_price)
                                ) * float(obj_item.amount_item)
                        else:
                            if (
                                obj_item.amount_price
                                and obj_item.amount_item
                                and obj_item.amount_price != "None"
                                and obj_item.amount_item != "None"
                            ):
                                obj_item.total_price_without_tax = float(
                                    obj_item.amount_price
                                ) * float(obj_item.amount_item)
                                obj_item.total_price = float(
                                    obj_item.amount_price
                                ) * float(obj_item.amount_item)
                            else:
                                obj_item.total_price_without_tax = 0
                                obj_item.total_price = 0
                    
                    # Apply line-item specific decimal point formatting if configured
                    if decimal_point and decimal_point.type in ['line_item_cut_off', 'line_item_cut_over']:
                        math_func = math.floor if decimal_point.type == 'line_item_cut_off' else math.ceil
                        obj_item.total_price_without_tax = math_func(obj_item.total_price_without_tax)
                        obj_item.total_price = math_func(obj_item.total_price)
                    
                    obj_total_price_without_tax.append(obj_item.total_price_without_tax)
                    obj_total_price.append(obj_item.total_price)
                    obj_item.save()

        if isinstance(obj_amounts, list) and obj_items:
            # Handling Amount of Tax Price
            obj.items = obj_items
            for price, price_without_tax in zip(
                obj_total_price, obj_total_price_without_tax
            ):
                total_price_without_tax += float(price_without_tax)
                total_price += float(price)

            if type_obj == TYPE_OBJECT_ORDER:
                if obj.tax:
                    total_price = float(total_price_without_tax) * (
                        (100.0 + float(obj.tax)) / 100.0
                    )
                else:
                    total_price = total_price_without_tax
            else:
                if obj.tax_rate and obj.tax_option != "item_based_tax":
                    total_price = float(total_price_without_tax) * (
                        (100.0 + float(obj.tax_rate)) / 100.0
                    )

                if obj.tax_option == "non_tax":
                    total_price = total_price_without_tax

            obj.total_price_without_tax = total_price_without_tax
            obj.total_price = total_price

            if type_obj == TYPE_OBJECT_ORDER:
                if obj.tax_applied_to == "only_items":
                    obj.amount_total = obj.total_price
                else:
                    obj.amount_total = obj.total_price_without_tax
            else:
                if obj.tax_option == "item_based_tax":
                    # Here IF POST TAX DISCOUNT
                    obj.amount_total = obj.total_price
                    # ELSE PRE TAX DISCOUNT
                else:
                    obj.amount_total = obj.total_price_without_tax
            obj.amount_tax = obj.total_price - obj.total_price_without_tax
            obj.save()

            if hasattr(obj, "amount"):
                obj.amount = total_price
            try:
                obj.tax_val_lists = ["0"] * len(obj_amounts)
                obj.totals = []
                index = 0
                for price, amount, tax in zip(
                    obj_amounts, obj_amount_items, obj_tax_lists
                ):
                    temp_price = (
                        price  # For handling not set price item or blank as the price
                    )
                    if tax is None:
                        tax = 0.0
                    if price == "":
                        temp_price = ""
                        price = 0.0
                    if price is None:
                        temp_price = None
                        price = 0.0
                    if type_obj == TYPE_OBJECT_ORDER:
                        if obj.tax_applied_to == "only_items":
                            if obj.tax:
                                obj.tax_val_lists[index] = (
                                    float(price) * float(amount)
                                ) - (
                                    float(price)
                                    * float(amount)
                                    / (1 + (float(obj.tax) / 100.0))
                                )
                                obj_amounts[index] = float(price) / (
                                    1 + (float(obj.tax) / 100.0)
                                )
                                obj.totals.append(float(price) * float(amount))
                            else:
                                obj.tax_val_lists[index] = (
                                    float(price) * float(amount)
                                ) * (float(obj.tax) / 100.0)
                                obj.totals = obj_total_price_without_tax
                        else:
                            obj.totals.append(float(price) * float(amount))
                    else:
                        if obj.tax_option == "item_based_tax":
                            if obj.tax_inclusive:
                                obj.tax_val_lists[index] = (
                                    float(price) * float(amount)
                                ) - (
                                    float(price)
                                    * float(amount)
                                    / (1 + (float(tax) / 100.0))
                                )
                                obj_amounts[index] = float(price) / (
                                    1 + (float(tax) / 100.0)
                                )
                                obj.totals.append(float(price) * float(amount))
                            else:
                                obj.tax_val_lists[index] = (
                                    float(price) * float(amount)
                                ) * (float(tax) / 100.0)
                                obj.totals = obj_total_price_without_tax
                        else:
                            obj.totals.append(float(price) * float(amount))
                    if temp_price == "":
                        obj_amounts[index] = temp_price
                    index += 1

                obj.show = list(
                    zip(
                        obj_items,
                        obj_amount_items,
                        obj_amounts,
                        obj.tax_val_lists,
                        obj_tax_lists,
                        obj.totals,
                    )
                )
                obj.total_without_discount = obj.total_price_without_tax
                obj.total_price_without_discount = total_price
            except:
                pass
        if type_obj == TYPE_OBJECT_RECEIPT and obj.entry_type == "manual":
            tax_rate = obj.tax_rate if obj.tax_rate is not None else 0
            obj.total_price = obj.manual_price * (1 + tax_rate / 100)
            obj.total_price_without_tax = obj.manual_price

        temp_total_price = obj.total_price
        temp_total_price_without_tax = obj.total_price_without_tax

        # If There any discount:
        # To-DO add option for POST TAX DISCOUNT and PRE TAX DISCOUNT
        if type_obj == TYPE_OBJECT_ORDER:
            obj.discount = None

        if is_debug_invoice:
            print(f"\n--- BEFORE DISCOUNT CALCULATION ---")
            print(f"obj.total_price: {obj.total_price}")
            print(f"obj.total_price_without_tax: {obj.total_price_without_tax}")
            print(f"obj.discount: {obj.discount}")
            print(f"obj_tax_lists: {obj_tax_lists}")
            print(f"obj_total_price_without_tax: {obj_total_price_without_tax}")

        if obj.discount:
            if is_debug_invoice:
                print(f"\n✅ DISCOUNT FOUND: {obj.discount}")
                print(f"Discount option: {obj.discount_option}")
                print(f"Discount tax option: {obj.discount_tax_option}")
            discount = 0.0
            if obj.discount_option == "%":
                # Here IF PRE TAX DISCOUNT
                if obj.discount_tax_option == "pre_tax":
                    discount = obj.total_price_without_tax * (
                        float(obj.discount) / 100.0
                    )
                # ELSE POST TAX DISCOUNT
                else:
                    discount = obj.total_price * (float(obj.discount) / 100.0)

            else:
                discount = float(obj.discount)

            # HERE IF PRE TAX DISCOUNT
            # # ================== To Count discount each item (Pre Tax Discount)
            if obj.discount_tax_option == "pre_tax":
                total_price = 0.0
                discount_item_price = 0.0
                for tax, price_without_tax in zip(
                    obj_tax_lists, obj_total_price_without_tax
                ):
                    discount_item_price = (
                        float(price_without_tax)
                        / obj.total_price_without_tax
                        * discount
                    )
                    discount_final_price = (
                        float(price_without_tax) - discount_item_price
                    )
                    if obj.tax_rate and obj.tax_option != "item_based_tax":
                        total_price = total_price + (
                            discount_final_price * (1 + (float(obj.tax_rate)) / 100.0)
                        )
                    elif obj.tax_option == "item_based_tax":
                        total_price = total_price + (
                            discount_final_price * (1 + (float(tax)) / 100.0)
                        )
                    else:
                        total_price += discount_final_price
                obj.total_without_discount = obj.total_price_without_tax
                obj.total_price_without_discount = total_price + discount
                total_price_without_tax = obj.total_price_without_tax - discount

            else:  # Post tax ==== Subtract it just for Total Price but no for Total Price Without Tax
                obj.total_without_discount = obj.total_price_without_tax
                obj.total_price_without_discount = total_price
                total_price = obj.total_price - discount

                # For post-tax discount with item-based tax, apply discount to without-tax amount
                if obj.tax_option == "item_based_tax":
                    # Apply discount directly to the without-tax amount
                    total_price_without_tax = obj.total_price_without_tax - discount

                    # Calculate the total tax amount from original prices (unchanged)
                    total_tax = 0
                    for tax_rate, price_without_tax in zip(obj_tax_lists, obj_total_price_without_tax):
                        # For JPY, ensure price_without_tax is properly rounded before tax calculation
                        if obj.currency and obj.currency.lower() == "jpy":
                            price_without_tax = int(float(price_without_tax))

                        item_tax = float(price_without_tax) * (float(tax_rate) / 100.0)
                        item_tax = round(item_tax)
                        total_tax += item_tax

                    # Final total is without-tax amount plus original tax
                    total_price = total_price_without_tax + total_tax
                else:
                    # For unified tax, keep the original logic
                    total_price_without_tax = obj.total_price_without_tax

            obj.total_price_without_tax = total_price_without_tax
            obj.total_price = total_price
            obj.discount = discount

            if is_debug_invoice:
                print(f"\n--- AFTER DISCOUNT CALCULATION ---")
                print(f"discount: {discount}")
                print(f"total_price_without_tax: {total_price_without_tax}")
                print(f"total_price: {total_price}")
                print(f"obj.discount_tax_option: {obj.discount_tax_option}")
                print(f"Expected total_price_without_tax: 6656")
                print(f"Expected total_price: 7320")
                print(f"Match without tax: {int(total_price_without_tax) == 6656}")
                print(f"Match with tax: {int(total_price) == 7320}")
        else:
            if is_debug_invoice:
                print(f"\n❌ NO DISCOUNT FOUND")
                print(f"obj.discount: {obj.discount}")
                print(f"obj.discount_option: {getattr(obj, 'discount_option', 'None')}")
                print(f"obj.discount_tax_option: {getattr(obj, 'discount_tax_option', 'None')}")
                print(f"Current total_price_without_tax: {obj.total_price_without_tax}")
                print(f"Current total_price: {obj.total_price}")

        if decimal_point:  # Adding Decimal Point Option
            if decimal_point.type == "cut_off":
                if obj.currency.lower() == "usd":
                    obj.total_price = math.floor(obj.total_price * 100) / 100
                    obj.total_price_without_tax = (
                        math.floor(obj.total_price_without_tax * 100) / 100
                    )
                else:
                    obj.total_price = math.floor(obj.total_price)
                    obj.total_price_without_tax = math.floor(
                        obj.total_price_without_tax
                    )

            elif decimal_point.type == "cut_over":
                if obj.currency.lower() == "usd":
                    obj.total_price = math.ceil(obj.total_price * 100) / 100
                    obj.total_price_without_tax = (
                        math.ceil(obj.total_price_without_tax * 100) / 100
                    )
                else:
                    obj.total_price = math.ceil(obj.total_price)
                    obj.total_price_without_tax = math.ceil(obj.total_price_without_tax)

        if obj.shipping_cost:
            if obj.shipping_cost.value:
                obj.total_price_without_tax += obj.shipping_cost.value
                obj.total_price += obj.shipping_cost.value

        # Handle None values before comparison to prevent TypeError
        total_price = obj.total_price if obj.total_price is not None else 0
        total_price_without_tax = (
            obj.total_price_without_tax
            if obj.total_price_without_tax is not None
            else 0
        )

        if total_price < 0 or total_price_without_tax < 0:
            # obj.total_price = 0
            # Handling If discount is greater than total price
            obj.total_price = temp_total_price
            obj.total_price_without_tax = temp_total_price_without_tax
            obj.discount = 0

            for user in obj.workspace.user.all():
                if lang == "ja":
                    Notification.objects.create(
                        workspace=obj.workspace,
                        user=user,
                        message="割引価格は総価格よりも大きい.",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=obj.workspace,
                        user=user,
                        message="Discount Value is greater than Total Price.",
                        type="error",
                    )

        if is_debug_invoice:
            print(f"\n--- FINAL RESULT BEFORE SAVE ---")
            print(f"Final obj.total_price: {obj.total_price}")
            print(f"Final obj.total_price_without_tax: {obj.total_price_without_tax}")
            print(f"Expected: total_price_without_tax=6656, total_price=7320")
            print(f"Match: {int(obj.total_price_without_tax) == 6656 and int(obj.total_price) == 7320}")
            print(f"🔍 ABOUT TO SAVE TO DATABASE 🔍")
            print(f"=== END DEBUG handling_items ===\n")

        obj.save()
    except Exception as e:
        # Include workspace ID for better debugging as per user preferences
        workspace_id = (
            getattr(obj.workspace, "id", "unknown")
            if hasattr(obj, "workspace") and obj.workspace
            else "unknown"
        )
        logger.error(f"error item billing (workspace: {workspace_id}): {e}")
        logger.error(f"traceback: {traceback.format_exc()}")

    return obj


def get_item_property(
    item,
    item_type,
    item_property: str,
    lang="ja",
    namecustomfields=None,
    timezone="UTC",
    field_value=None,
    components_name=None,
):
    """Returns item property. Also handles custom property"""
    # Handle ShopTurboItemComponents objects which don't have a direct workspace attribute
    if (
        hasattr(item, "__class__")
        and item.__class__.__name__ == "ShopTurboItemComponents"
    ):
        # Get workspace from either the parent item or the component item
        if item.item and hasattr(item.item, "workspace"):
            workspace = item.item.workspace
        elif item.item_component and hasattr(item.item_component, "workspace"):
            workspace = item.item_component.workspace
        else:
            # Fallback to getting workspace from the property's field_name
            if (
                item.property
                and item.property.field_name
                and hasattr(item.property.field_name, "workspace")
            ):
                workspace = item.property.field_name.workspace
            else:
                raise AttributeError(
                    f"Cannot determine workspace for {item.__class__.__name__} object"
                )
    else:
        # For other objects that have a direct workspace attribute
        workspace = item.workspace
    if not namecustomfields:
        namecustomfields = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace
        ).values_list("name", flat=True)
        namecustomfields = [data.lower() for data in namecustomfields]
    shopturbo_items_column_lower = item_property.lower()
    col_data = None
    
    # filter supplier (contact or company)
    # - replace with 'contact' or 'company' to access the correct related field
    # - store the remaining part of the column name to filter the related object
    contact_info = None
    company_info = None
    if "supplier" in shopturbo_items_column_lower:
        if 'contact' in shopturbo_items_column_lower:
            contact_info = shopturbo_items_column_lower.replace('supplier__contact__', '')
            shopturbo_items_column_lower = 'contact'
        elif 'company' in shopturbo_items_column_lower:
            company_info = shopturbo_items_column_lower.replace('supplier__company__', '')
            shopturbo_items_column_lower = 'company'
        
    if shopturbo_items_column_lower in [
        str(v.name) for v in ShopTurboItems._meta.fields if v.name != "product_id"
    ] + ["purchase_price_currency"]:
        # For component items, we need to get the actual ShopTurboItems object
        if (
            item_type == "components"
            and hasattr(item, "__class__")
            and item.__class__.__name__ == "ShopTurboItemComponents"
        ):
            # Use the component item for field access
            query_item = item.item_component
        else:
            # Use the item directly for regular items
            query_item = item

        col_data = getattr(query_item, shopturbo_items_column_lower, None)

        if col_data and isinstance(
            ShopTurboItems._meta.get_field(shopturbo_items_column_lower),
            models.DateTimeField,
        ):
            date_format_object = DateFormat.objects.filter(
                workspace=workspace, is_workspace_level=True
            ).first()
            format = DEFAULT_DATE_FORMAT[lang]
            if date_format_object and date_format_object.value:
                format = date_format_object.value
            col_data = format_date_to_str(col_data, format, timezone, with_time=True)

        if "tax" in shopturbo_items_column_lower:
            if item.tax:
                col_data = f"{item.tax}%"
            else:
                item_price = ShopTurboItemsPrice.objects.filter(
                    item=item, default=True
                ).first()
                if item_price:
                    col_data = f"{item_price.tax}%"

        elif "owner" in shopturbo_items_column_lower:
            if isinstance(item, ShopTurboItems) and item.owner and item.owner.user:
                col_data = item.owner.user.first_name
        
        elif "purchase_price_currency" in shopturbo_items_column_lower:
            item_price = item.purchase_prices.filter(default=True).first()
            if item_price:
                col_data = getattr(item_price, 'currency', None)
            else:
                col_data = item.currency
        
        elif 'contact' in shopturbo_items_column_lower and contact_info:
            contact = item.contact
            col_data = getattr(contact, contact_info, None)
        
        elif 'company' in shopturbo_items_column_lower and company_info:
            company = item.company
            col_data = getattr(company, company_info, None)
            
    elif "-item id" in shopturbo_items_column_lower:
        if item.platform and item.platform in shopturbo_items_column_lower:
            col_data = item.product_id

    elif "platform_ids" in shopturbo_items_column_lower:
        platforms = item.item.filter(platform_type="default")
        col_data = [
            platform.platform_id for platform in platforms if platform.platform_id
        ]

    elif (
        item_type == "components"
        and "component_quantity|" in shopturbo_items_column_lower
    ):
        field_id = str(field_value.field_name.id)
        if field_id == shopturbo_items_column_lower.replace("component_quantity|", ""):
            col_data = item.quantity

    elif item_type == "components" and components_name in shopturbo_items_column_lower:
        col_data = f"#{item.item_component.item_id:04d} {item.item_component.name}"

    elif shopturbo_items_column_lower in namecustomfields:
        value_custom_field = ShopTurboItemsValueCustomField.objects.filter(
            field_name__name__iexact=shopturbo_items_column_lower, items=item
        ).last()
        if value_custom_field:
            if value_custom_field.field_name.type == "image":
                value_file = value_custom_field.shopturboitemsvaluefile_set.all().last()
                if value_file:
                    col_data = value_file.file.url
            elif value_custom_field.field_name.type in ["date", "date_time"]:
                if value_custom_field.value_time:
                    try:
                        user_tz = timezone
                        if not user_tz:
                            user_tz = None
                    except:
                        user_tz = None

                    col_data = format_date(
                        value_custom_field.value_time,
                        lang,
                        include_time=(
                            value_custom_field.field_name.type == "date_time"
                        ),
                        tz=user_tz,
                    )
                else:
                    col_data = None
            elif value_custom_field.field_name.type == "contact":
                try:
                    _contact = Contact.objects.filter(
                        id=value_custom_field.value
                    ).first()
                    if _contact:
                        col_data = f"#{'%04d' % _contact.contact_id} {_contact.name}"
                        if _contact.last_name:
                            col_data += f" {_contact.last_name}"
                except:
                    pass
            elif value_custom_field.field_name.type == "company":
                if is_valid_uuid(value_custom_field.value):
                    company = Company.objects.filter(
                        id=value_custom_field.value
                    ).first()
                    if company:
                        col_data = f"#{company.company_id}-{company.name}"
            elif value_custom_field.field_name.type == "components":
                col_data = None
            elif value_custom_field.field_name.type == "formula":
                page_obj = get_page_object(TYPE_OBJECT_ITEM, lang)
                col_data = formula.calculate_math(
                    page_obj, item, value_custom_field.field_name
                )
            else:
                col_data = get_download_value(value_custom_field, lang)

    elif "inventory__" in shopturbo_items_column_lower:
        field_ = (
            shopturbo_items_column_lower.replace("inventory__", "")
            .replace("_amount", "")
            .replace("_inventory", "")
        )
        col_data = item_inventory_amount(item, inventory_type=field_)
    return col_data


def get_custom_field_value(value_custom_field, lang):
    field_type = value_custom_field.field_name.type

    if field_type == "invoice_objects":
        if not is_valid_uuid(value_custom_field.value):
            return None

        invoice = Invoice.objects.filter(id=value_custom_field.value).last()
        if not invoice:
            return None

        if invoice.contact:
            from utils.contact import display_contact_name

            return f"#{invoice.id_inv} - {display_contact_name(invoice.contact, lang)}"
        elif invoice.company:
            return f"#{invoice.id_inv} - {invoice.company.name}"
        else:
            return f"#{invoice.id_inv}"

    return None


def get_store_item_price(item, property_to_classify_price, val):
    item_component_ids = item.shopturboitemcomponents_set.all().values("item_component")
    item_component_prop_values = ShopTurboItemsValueCustomField.objects.filter(
        items__in=item_component_ids,
        field_name__type="number",
        field_name__id=property_to_classify_price,
    )
    item_component_prop_values = (
        item_component_prop_values.annotate(value_as_int=Cast("value", IntegerField()))
        .annotate(
            diff=Cast(Func(F("value_as_int") - val, function="ABS"), IntegerField())
        )
        .order_by("diff")
    )
    item_component_prop_values = (
        item_component_prop_values.filter(value__gte=int(val)).first()
        or item_component_prop_values.last()
    )
    if item_component_prop_values:
        return item_component_prop_values.items.price
    return None


def get_store_item_representative_price(item, property_to_classify_price, val):
    """
    Get item which price is selected based on a property value.
    """
    item_component_ids = item.shopturboitemcomponents_set.all().values("item_component")
    item_component_prop_values = ShopTurboItemsValueCustomField.objects.filter(
        items__in=item_component_ids,
        field_name__type="number",
        field_name__id=property_to_classify_price,
    )
    item_component_prop_values = (
        item_component_prop_values.annotate(value_as_int=Cast("value", IntegerField()))
        .annotate(
            diff=Cast(Func(F("value_as_int") - val, function="ABS"), IntegerField())
        )
        .order_by("diff")
    )
    item_component_prop_values = (
        item_component_prop_values.filter(value__gte=int(val)).first()
        or item_component_prop_values.last()
    )
    if item_component_prop_values:
        return item_component_prop_values.items
    return None
