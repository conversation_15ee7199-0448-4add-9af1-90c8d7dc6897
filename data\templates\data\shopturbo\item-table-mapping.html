{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div>
    <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
        {% if LANGUAGE_CODE == 'ja' %}
        マッピングテーブル
        {% else %}
        Mapping Table
        {% endif %}
    </span>

    <table class="{% include "data/utility/table.html" %} px-5">
        <thead class="{% include "data/utility/table-header.html" %}">
            <tr>
                <th class="min-w-50px">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        プラットフォームヘッダー
                        {% else %}
                        Platform Header
                        {% endif %}
                    </span>
                </th>
                <th class="min-w-50px">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        Sankaプロパティ
                        {% else %}
                        Sanka Property
                        {% endif %}
                    </span>
                </th>
                <th class="min-w-50px text-center">
                    <span>
                        {% if LANGUAGE_CODE == 'ja' %}
                        除外
                        {% else %}
                        Skip
                        {% endif %}
                    </span>
                </th>
            </tr>
        </thead>
        <tbody class="fs-6">
            {% for header in header_list %}
                <tr>
                    <td>
                        <input name="item-file-column{% if action_index %}-{{action_index}}{% endif %}" type="hidden" value="{{header|parse_header_item}}"/>
                        {% if LANGUAGE_CODE == 'ja' %}
                            <input name="item-file-column-name{% if action_index %}-{{action_index}}{% endif %}" type="hidden" value="{{header|parse_header_item:'name_ja'}}"/>
                            {{header|parse_header_item:'name_ja'}}
                        {% else %}
                            <input name="item-file-column-name{% if action_index %}-{{action_index}}{% endif %}" type="hidden" value="{{header|parse_header_item:'name'}}"/>
                            {{header|parse_header_item:'name'}}
                        {% endif %}
                    </td>

                    <td>

                        <div id="sanka-properties-{{header|parse_header_item}}{% if action_index %}-{{action_index}}{% endif %}" class="">
                            <select name="sanka-item-properties{% if action_index %}-{{action_index}}{% endif %}" class="bg-white form-select form-select-solid border h-40px select2-this" 
                                {% if LANGUAGE_CODE == 'ja'%}
                                data-placeholder="ヘッダーの選択"
                                {% else %}
                                data-placeholder="Select Header"
                                {% endif %}
                                >  
                                
                                {% for column in item_columns %}
                                    {% if column == 'create_new' %}
                                        <option value="create_new">{% if LANGUAGE_CODE == 'ja' %}新しい Sanka プロパティの作成{% else %}Create New Sanka Text Property{% endif %}</option>
                                    {% else %}
                                        <option {% if column == header|parse_header_item:'default'%} selected {%endif%} value='{{column}}'>
                                            {{column|display_column_items:request}}
                                        </option>  
                                    {% endif %}
                                {% endfor %} 

                            </select>
                        </div>
                        

                    </td>
                    <td class="text-center">
                        <input type="checkbox" {% if header|parse_header_item:'skip' == 'True'%} checked {%endif%}class="form-check-input" onchange="handleCheckbox(this)">
                        <input type="hidden" name="item-ignore{% if action_index %}-{{action_index}}{% endif %}" value="{{header|parse_header_item:'skip'}}">
                    </td>
                </tr>
            {% endfor %} 
        </tbody> 
    </table>
    {% if not action_index %}
    <button type="submit" class="btn btn-light-primary" name="save_item_mapping{% if action_index %}-{{action_index}}{% endif %}">
        {% if LANGUAGE_CODE == 'ja'%}
        マッピングの保存
        {% else %}
        Save Mapping
        {% endif %}
    </button>
    {% endif %}

</div>

<script>
    $(document).ready(function() {
        $('.select2-this').select2();
    });
    
    
    function handleCheckbox(checkbox) {
        // Get the parent element of the checkbox
        var parent = checkbox.parentNode;
        // Find the next input element within the parent
        var nextInput = parent.querySelector('input[type="hidden"]');
        if (!checkbox.checked) {
            nextInput.value = "False";
        } else {
            nextInput.value = "True";
        }
    }

    function showSankaProperties(elm){
        var header = elm.id.split("-");
        header = header[header.length - 1]
        
        var sanka_properties_contacts_id = "sanka-properties-"+header
        var sanka_properties_company_id = "sanka-properties-company-"+header
        var sanka_properties_contacts_elm = document.getElementById(sanka_properties_contacts_id)
        var sanka_properties_company_elm = document.getElementById(sanka_properties_company_id)
        
        if (elm.value == "contact"){
            if (sanka_properties_contacts_elm && sanka_properties_company_elm){
                sanka_properties_contacts_elm.classList.remove("d-none")
                sanka_properties_company_elm.classList.add("d-none")
            }
        }
        else if (elm.value == "company"){
            if (sanka_properties_contacts_elm && sanka_properties_company_elm){
                sanka_properties_contacts_elm.classList.add("d-none")
                sanka_properties_company_elm.classList.remove("d-none")
            }
        }
    }
</script>