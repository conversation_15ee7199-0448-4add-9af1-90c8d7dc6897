import base64
import ast
import json
import re
import cv2
import numpy as np
import uuid
from django.core.paginator import Empty<PERSON><PERSON>, <PERSON><PERSON><PERSON>, PageNotAnInteger
from django.db.models import (
    Case,
    F,
    Sum,
    Value,
    When,
    Subquery,
    OuterRef,
    IntegerField,
)
from django.db.models.functions import Concat

# Signal
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_GET
from django_hosts.resolvers import reverse

from action.action import trigger_next_action
from utils.logger import logger

from data.applog import (
    manual_log,
)  # main initiation for All log. If you delete tell <PERSON>ardi
from data.commerce import get_page_object
from data.constants.constant import COUNTRY_CODE, CURRENCY_MODEL
from data.constants.constant import OBJECTS_CF_TYPE as COMPANY_CF_TYPE
from data.constants.constant import OBJECTS_CF_TYPE as CONTACT_CF_TYPE
from data.constants.contact_constant import (
    CONTACT_APP_SLUG,
    CONTACT_APP_TARGET,
)
from data.constants.properties_constant import (
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_ITEM,
)
from data.models import (
    Contact,
    Company,
    ContactsNameCustomField,
    ContactsValueCustomField,
    CompanyNameCustomField,
    CompanyValueCustomField,
    DealsNameCustomField,
    ObjectManager,
    AppSetting,
    UserManagement,
    Group,
    Log,
    AdvanceSearchFilter,
    ContactList,
    Channel,
    ShopTurboOrders,
    ShopTurboItems,
    ContatcsFile,
    ImportMappingFields,
    TransferHistory,
    Notification,
    ContactLineChannel,
    Deals,
    Module,
    View,
    ViewFilter,
    CustomObjectPropertyRow,
    ContactsMappingFields,
    ShopTurboItemsPrice,
    PropertySet,
    Notes,
    TempHeaderList,
    VIEW_MODE,
    DEFAULT_COLUMNS_CONTACTS,
    DEFAULT_PERMISSION,
    CONTACT_USAGE_CATEGORY,
    EXCLUDE_SYNC_CHANNEL_NAME,
)
from data.property import properties
from utils.actions import transfer_output_to_target_input
from utils.decorator import login_or_hubspot_required
from utils.eccube import *
from utils.filter import build_view_filter
from utils.freee_bg_jobs.freee import *
from utils.hubspot import *
from utils.meter import has_quota
from utils.openaiAPI import OpenaiAPI
from utils.bgjobs.handler import create_bg_job, add_hatchet_run_id
from data.contact.background.export_csv_contacts import (
    ExportCSVContactsPayload,
    export_csv_contacts,
)
from data.company.background.import_salesforce_companies import (
    ImportSalesforceCompaniesPayload,
    import_salesforce_companies_task,
)
from data.company.background.import_hubspot_companies import (
    ImportHubspotCompaniesPayload,
    import_hubspot_companies_task,
)
from data.contact.background.import_hubspot_contacts import (
    ImportHubspotContactsPayload,
    import_hubspot_contacts_task,
)
from data.contact.background.import_salesforce_contacts import (
    ImportSalesforceContactsPayload,
    import_salesforce_contacts_task,
)
from data.contact.background.import_salesforce_contacts_as_companies import (
    ImportSalesforceContactsAsCompaniesPayload,
    import_salesforce_contacts_as_companies_task,
)
from data.contact.background.import_shopify_contacts import (
    ImportShopifyContactsPayload,
    import_shopify_contacts_task,
)
from data.contact.background.import_freee_contacts import (
    ImportFreeeContactsPayload,
    import_freee_contacts_task,
)
from data.contact.background.import_line_contacts import (
    ImportLineContactsPayload,
    import_line_contacts_task,
)
from data.contact.background.export_hubspot_contacts import (
    ExportHubspotContactsPayload,
    export_hubspot_contacts_task,
)
from data.contact.background.export_hubspot_contacts_as_companies import (
    ExportHubspotContactsAsCompaniesPayload,
    export_hubspot_contacts_as_companies_task,
)
from data.contact.background.export_shopify_contacts import (
    ExportShopifyContactsPayload,
    export_shopify_contacts_task,
)
from data.contact.background.export_freee_contacts import (
    ExportFreeeContactsPayload,
    export_freee_contacts_task,
)
from data.company.background.export_salesforce_companies import (
    ExportSalesforceCompaniesPayload,
    export_salesforce_companies_task,
)
from data.company.background.export_hubspot_companies import (
    ExportHubspotCompaniesPayload,
    export_hubspot_companies_task,
)
from utils.project import get_ordered_views
from utils.properties.properties import (
    get_default_prompt_set,
    get_default_property_set,
    get_properties_with_details,
)
from utils.salesforce.contacts import get_contact_mapping_fields
from utils.salesforce.companies import (
    export_salesforce_companies,
    get_company_mapping_fields,
)
from utils.serializer import *
from utils.shopify_bg_job.shopify_orders import (
    push_shopify_customer,
)
from utils.twitter import *
from utils.utility import (
    get_workspace,
    is_valid_uuid,
    modular_view_filter,
    read_csv,
    save_custom_property,
    update_query_params_url,
    assign_object_owner,
    get_permission_filter,
)
from data.accounts.association_labels import save_association_label
from utils.workspace import get_permission


@login_or_hubspot_required
def app_settings(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if lang == "ja":
        page_title = "機能設定"
    else:
        page_title = "Feature Settings"

    if request.method == "POST":
        if request.POST.get("app_settings", None) == "manage_app":
            display = request.POST.get("display")
            page_group_type = request.POST.get("page_group_type")
            # additional default display settings
            if display and page_group_type:
                om, _ = ObjectManager.objects.get_or_create(
                    workspace=workspace, page_group_type=page_group_type
                )
                om.column_display = display
                om.save()

            if "submit-customfield-contacts-button" in request.POST:
                search_setting_contact = request.POST.get(
                    "search_setting_contact", "unset"
                )
                if not search_setting_contact:
                    search_setting_contact = "unset"
                app_setting, _ = AppSetting.objects.get_or_create(
                    workspace=workspace, app_target=CONTACT_APP_TARGET
                )
                app_setting.search_setting_contact = search_setting_contact
                app_setting.save()

                return properties(request)

            elif "submit-customfield-company-button" in request.POST:
                search_setting_company = request.POST.get(
                    "search_setting_company", "unset"
                )
                if not search_setting_company:
                    search_setting_company = "unset"
                app_setting, _ = AppSetting.objects.get_or_create(
                    workspace=workspace, app_target=CONTACT_APP_TARGET
                )
                app_setting.search_setting_company = search_setting_company
                app_setting.save()

                return properties(request)

            elif "submit-customfield-deals-button" in request.POST:
                return properties(request)

    elif request.method == "GET":
        setting_type = request.GET.get("setting_type", "contacts")
        custom_field_mode = request.GET.get("custom_field_mode")
        if custom_field_mode == "individual-contacts":
            customfield_id = request.GET.get("CustomFieldName_id", None)
            contacts_custom_field_type = request.GET.get(
                "contacts_custom_field_type", None
            )
            checking_valid_uuid = True

            contacts_customfield = ContactsNameCustomField.objects.filter(
                id=customfield_id
            ).first()
            if contacts_customfield:
                if contacts_custom_field_type:
                    contacts_customfield.type = contacts_custom_field_type
            else:
                if not contacts_custom_field_type:
                    contacts_custom_field_type = "text"
                contacts_customfield = {
                    "id": uuid.uuid4(),
                    "type": contacts_custom_field_type,
                }
                checking_valid_uuid = False
            default_columns = [
                str(v.name).lower()
                for v in Contact._meta.fields
                if v.name != "location"
            ]
            context = {
                "contacts_customfield": contacts_customfield,
                "CONTACT_CF_TYPES": CONTACT_CF_TYPE,
                "number_formats": CONTACT_NUMBER_FORMAT,
                "checking_valid_uuid": checking_valid_uuid,
                "ContactsNameCustomField": ContactsNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("order"),
                "contacts_default_column": default_columns,
            }
            return render(request, "data/contacts/contacts-custom-field.html", context)

        elif custom_field_mode == "individual-company":
            customfield_id = request.GET.get("CustomFieldName_id", None)
            company_custom_field_type = request.GET.get(
                "company_custom_field_type", None
            )
            checking_valid_uuid = True

            company_customfield = CompanyNameCustomField.objects.filter(
                id=customfield_id
            ).first()
            if company_customfield:
                if company_custom_field_type:
                    company_customfield.type = company_custom_field_type
            else:
                if not company_custom_field_type:
                    company_custom_field_type = "text"
                company_customfield = {
                    "id": uuid.uuid4(),
                    "type": company_custom_field_type,
                }
                checking_valid_uuid = False

            context = {
                "company_customfield": company_customfield,
                "COMPANY_CF_TYPES": COMPANY_CF_TYPE,
                "number_formats": COMPANY_NUMBER_FORMAT,
                "checking_valid_uuid": checking_valid_uuid,
                "CompanyNameCustomField": CompanyNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("order"),
                "company_default_column": [
                    str(v.name).lower() for v in Company._meta.fields
                ],
            }
            return render(request, "data/contacts/company-custom-field.html", context)

        elif custom_field_mode == "individual-deals":
            customfield_id = request.GET.get("CustomFieldName_id", None)
            deals_custom_field_type = request.GET.get("deals_custom_field_type", None)
            checking_valid_uuid = True

            deals_customfield = DealsNameCustomField.objects.filter(
                id=customfield_id
            ).first()
            if deals_customfield:
                if deals_custom_field_type:
                    deals_customfield.type = deals_custom_field_type
            else:
                if not deals_custom_field_type:
                    deals_custom_field_type = "text"
                deals_customfield = {
                    "id": uuid.uuid4(),
                    "type": deals_custom_field_type,
                }
                checking_valid_uuid = False

            case_default_column = [str(v.name).lower() for v in Deals._meta.fields]
            context = {
                "case_customfield": deals_customfield,
                "DEALS_CF_TYPES": DEALS_CF_TYPE,
                "number_formats": DEALS_NUMBER_FORMAT,
                "checking_valid_uuid": checking_valid_uuid,
                "CaseNameCustomField": DealsNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("order"),
                "case_default_column": case_default_column,
            }
            return render(request, "data/contacts/case-custom-field.html", context)

        else:
            object_type = CONTACT_APP_TARGET
            app_setting, _ = AppSetting.objects.get_or_create(
                workspace=workspace, app_target=CONTACT_APP_TARGET
            )

            column_values = []
            if setting_type == "contacts":
                object_type = TYPE_OBJECT_CONTACT
                column_values = get_properties_with_details(
                    "contacts",
                    workspace,
                    lang,
                    searchable_only=True,
                    excludes=["image_url"],
                )
                column_values = [str(p["id"]) for p in column_values]
                if not app_setting.search_setting_contact:
                    app_setting.search_setting_contact = "first_name,last_name,email"
                    app_setting.save()

            elif setting_type == "company":
                object_type = TYPE_OBJECT_COMPANY
                column_values = get_properties_with_details(
                    "company", workspace, lang, searchable_only=True
                )
                column_values = [str(p["id"]) for p in column_values]
                if not app_setting.search_setting_company:
                    app_setting.search_setting_company = "name,company_id"
                    app_setting.save()

            # add request.user to AppSetting
            if request.user not in app_setting.user.all():
                app_setting.user.add(request.user)
                app_setting.save()

            role = UserManagement.objects.filter(user=request.user, workspace=workspace)
            if role:
                role = UserManagement.objects.get(
                    user=request.user, workspace=workspace
                )
            else:
                role, _ = UserManagement.objects.get_or_create(
                    user=request.user,
                    workspace=workspace,
                    type=UserManagement.RoleType.STAFF,
                )
            groups = Group.objects.filter(workspace=workspace)
            default_columns = [
                str(v.name) for v in Contact._meta.fields if v.name != "location"
            ]
            context = {
                "app_setting": app_setting,
                "column_values": column_values,
                "role": role,
                "groups": groups,
                "ContactsNameCustomField": ContactsNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("order"),
                "CompanyNameCustomField": CompanyNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("order"),
                "DealsNameCustomField": DealsNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("order"),
                "CONTACT_CF_TYPES": CONTACT_CF_TYPE,
                "COMPANY_CF_TYPES": COMPANY_CF_TYPE,
                "number_formats": CONTACT_NUMBER_FORMAT,
                "page_title": page_title,
                "menu_key": "workspace",
                "page_type": "settings",
                "contact_default_column": default_columns,
                "company_default_column": [str(v.name) for v in Company._meta.fields],
                "page_group_type": "workspace_setting",
                "setting_type": setting_type,
            }
            return render(
                request, "data/account/workspace/workspace_template.html", context
            )

    return redirect(reverse("customerlink_settings", host="app"))


@login_or_hubspot_required
def contacts(request, id=None):
    target = "contacts"
    page_group_type = "contacts"
    lang = request.LANGUAGE_CODE

    workspace = get_workspace(request.user)

    page_obj = get_page_object(TYPE_OBJECT_CONTACT, lang)
    base_model = page_obj["base_model"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    custom_value_file_model = page_obj["custom_value_file_model"]
    custom_value_file_relation = page_obj["custom_value_file_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    id_field = page_obj["id_field"]

    if request.method == "GET":
        # help
        page_type = request.GET.get("page_type", "contacts")

        if lang == "ja":
            page_title = "連絡先"
        else:
            page_title = "Contacts"

        permission = get_permission(object_type=page_group_type, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == "hide":
            context = {
                "permission": permission,
                "page_type": page_type,
                "page_title": page_title,
            }
            return render(request, "data/contacts/contacts.html", context)

        # view
        config_view = None
        view_filter = None
        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=workspace, app_target=CONTACT_APP_TARGET
        )

        if not app_setting.search_setting_contact:
            app_setting.search_setting_contact = "first_name,last_name,email"
            app_setting.save()

        view_id = request.GET.get("view_id", None)
        views = get_ordered_views(workspace, target, user=request.user)

        # Log ViewFilter creation details
        logger.info(
            "Creating ViewFilter for contacts search - view_id: %s, workspace: %s, target: %s",
            view_id,
            workspace.id,
            target,
        )
        view_filter = modular_view_filter(
            workspace,
            target,
            view_id=view_id,
            column_view=DEFAULT_COLUMNS_CONTACTS.copy(),
            user=request.user,
        )

        # Log ViewFilter object state after creation
        logger.info(
            "ViewFilter created - view_id: %s, view_type: %s, column_count: %s, filter_value_keys: %s, has_view: %s",
            str(view_filter.view.id) if view_filter.view else None,
            view_filter.view_type,
            len(view_filter.column) if view_filter.column else 0,
            list(view_filter.filter_value.keys()) if view_filter.filter_value else [],
            view_filter.view is not None,
        )

        config_view = view_filter.view_type
        if not config_view:
            config_view = "list"
        # archive = view_filter.archive
        contacts_columns = view_filter.column
        print(contacts_columns)

        # Determine records per page from view_filter
        records_per_page = view_filter.pagination
        if (
            not records_per_page
        ):  # If view_filter.pagination is None or 0 or empty string
            records_per_page = 25  # Default value
        else:
            try:
                records_per_page = int(records_per_page)
                if records_per_page <= 0:  # ensure positive integer
                    records_per_page = 25
            except ValueError:
                records_per_page = 25  # Default if conversion fails

        # This offset is used for calculating paginator_item_begin correctly
        # (page_number - 1) * records_per_page + 1
        # The existing formula structure: (records_per_page * page_number) - (records_per_page - 1)
        records_per_page_offset_for_calc = records_per_page - 1

        Log.objects.create(
            workspace=workspace,
            user=request.user,
            page_name="contacts",
            sub_page_name="all contacts",
        )

        filter_conditions = Q(workspace=workspace)

        filter_conditions &= get_permission_filter(permission, request.user)
        # Handle status filter for archived contacts
        status = request.GET.get("status")
        if status == "archived":
            filter_conditions &= Q(status="archived")
        else:
            # By default, show only active contacts
            filter_conditions &= Q(status="active")

        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_CONTACT, type="default"
        ).first()
        if not advance_search:
            advance_search = None

        if advance_search:
            if advance_search.search_settings:
                app_setting.search_setting_contact = advance_search.search_settings
                app_setting.save()
        print("search settings:", app_setting.search_setting_contact)

        search_q = request.GET.get("q")
        if search_q:
            # Log search query details
            logger.info(
                "Processing search query - query: %s, view_id: %s, search_settings: %s",
                search_q,
                view_id,
                app_setting.search_setting_contact,
            )

            match_special_char = re.search(r"#(\d+)", search_q)
            if match_special_char:
                number = match_special_char.group(1)  # Get the number part
                if number:
                    filter_conditions &= Q(contact_id=number)
                    logger.debug(
                        "Applied special character search for contact_id: %s", number
                    )
            else:
                search_filters = Q()
                search_filters |= Q(contact_id__icontains=search_q)
                if app_setting.search_setting_contact:
                    search_fields = app_setting.search_setting_contact.split(",")
                    search_fields = [field for field in search_fields]
                    force_filter_list = []

                    logger.info(
                        "Processing search fields - fields: %s, view_filter_view_id: %s, view_filter_type: %s",
                        search_fields,
                        str(view_filter.view.id) if view_filter.view else None,
                        view_filter.view_type,
                    )

                    for s_field in search_fields:
                        if s_field == "company":
                            s_field = "customers__companies__name"
                            force_filter_list.append(s_field)

                        # Log each search field processing
                        logger.debug(
                            "Processing search field - field: %s, is_uuid: %s",
                            s_field,
                            is_valid_uuid(s_field),
                        )

                        search_filters |= apply_search_setting(
                            "contacts",
                            view_filter,
                            s_field,
                            search_q,
                            force_filter_list=force_filter_list,
                        )

                filter_conditions &= search_filters
                logger.info(
                    "Search filters applied - query_str: %s", str(search_filters)
                )
                logger.info(
                    "Combined filter conditions after search - query_str: %s",
                    str(filter_conditions),
                )
                print("== search_filters: ", search_filters)
                print("== filter_conditions after search: ", filter_conditions)

        advance_search_filter = None
        if advance_search:
            if advance_search.is_active:
                advance_search_filter = advance_search.search_filter
                advance_search_filter_status = advance_search.search_filter_status
                if advance_search_filter_status:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                        and advance_search_filter_status.get(k) != "False"
                    }
                else:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }
                try:
                    if (
                        advance_search_filter.get("usage_status", {}).get("value")
                        == "all"
                    ):
                        del advance_search_filter["usage_status"]
                except KeyError:
                    pass

                view_filter.filter_value = advance_search_filter

        # Store the search conditions before applying view filters
        search_conditions = filter_conditions
        logger.info(
            "Filter conditions before build_view_filter - query_str: %s",
            str(filter_conditions),
        )

        filter_conditions = build_view_filter(
            filter_conditions, view_filter, TYPE_OBJECT_CONTACT, request=request
        )

        logger.info(
            "Filter conditions after build_view_filter - query_str: %s",
            str(filter_conditions),
        )

        # If build_view_filter resulted in no results but we had search conditions,
        # fall back to just the search conditions
        if search_q and filter_conditions != search_conditions:
            test_count = Contact.objects.filter(filter_conditions).count()
            search_count = Contact.objects.filter(search_conditions).count()
            logger.info(
                "build_view_filter result count: %s, search_conditions count: %s",
                test_count,
                search_count,
            )

            if test_count == 0 and search_count > 0:
                logger.warning(
                    "build_view_filter eliminated all search results, falling back to search conditions only"
                )
                filter_conditions = search_conditions
        print("......................... Contacts filter:", filter_conditions)

        filter_values = view_filter.filter_value
        if filter_values:
            if "name" in filter_values.keys():
                full_name_to_filter = filter_values.get("name").get("value")
                operator_ = filter_values.get("name").get("key")

                if operator_ == "is":
                    filter_conditions &= Q(full_name__iexact=full_name_to_filter)
                elif operator_ == "is_not":
                    filter_conditions &= ~Q(full_name__iexact=full_name_to_filter)
                elif operator_ == "contains":
                    filter_conditions &= Q(full_name__icontains=full_name_to_filter)
                elif operator_ == "does_not_contain":
                    filter_conditions &= ~Q(full_name__icontains=full_name_to_filter)
                elif operator_ == "starts_with":
                    filter_conditions &= Q(full_name__istartswith=full_name_to_filter)
                elif operator_ == "ends_with":
                    filter_conditions &= Q(full_name__iendswith=full_name_to_filter)
                elif operator_ == "includes":
                    filter_conditions &= Q(full_name__icontains=full_name_to_filter)
                elif operator_ == "excludes":
                    filter_conditions &= ~Q(full_name__icontains=full_name_to_filter)
                elif operator_ == "is_empty":
                    filter_conditions &= Q(full_name__isnull=True)
                elif operator_ == "is_not_empty":
                    filter_conditions &= ~Q(full_name__isnull=True)

                if " " in full_name_to_filter:
                    contacts = (
                        Contact.objects.annotate(
                            full_name=Case(
                                # If last_name is empty, use only 'name'
                                When(last_name="", then=F("name")),
                                # If last_name is null, use only 'name'
                                When(last_name__isnull=True, then=F("name")),
                                # Otherwise, concat with space
                                default=Concat(F("name"), Value(" "), F("last_name")),
                            )
                        )
                        .filter(filter_conditions, workspace=workspace)
                        .order_by("-contact_id", "-created_at")
                        .distinct()
                    )
                else:
                    contacts = (
                        Contact.objects.annotate(
                            full_name=Concat(F("name"), F("last_name"))
                        )
                        .filter(filter_conditions, workspace=workspace)
                        .order_by("-contact_id", "-created_at")
                        .distinct()
                    )
            else:
                contacts = (
                    Contact.objects.filter(filter_conditions, workspace=workspace)
                    .order_by("-contact_id", "-created_at")
                    .distinct()
                )
        else:
            contacts = (
                Contact.objects.filter(filter_conditions, workspace=workspace)
                .order_by("-contact_id", "-created_at")
                .distinct()
            )

        # Apply sorting based on view filter settings
        contacts_base_qs = Contact.objects.filter(
            filter_conditions, workspace=workspace
        )

        # Log the final filter conditions and result count
        logger.info(
            "Final filter conditions for base queryset: %s", str(filter_conditions)
        )
        base_count = contacts_base_qs.count()
        logger.info("Base queryset count before sorting: %s", base_count)
        try:
            if view_filter.sort_order_by:
                order_method = view_filter.sort_order_method
                order_by = view_filter.sort_order_by

                if is_valid_uuid(order_by):
                    field_name = ContactsNameCustomField.objects.filter(
                        id=order_by
                    ).first()
                    if field_name:
                        custom_value_subquery = ContactsValueCustomField.objects.filter(
                            contact=OuterRef("pk"), field_name=field_name
                        )

                        if field_name.type in ["date", "date_time"]:
                            custom_value_subquery = custom_value_subquery.values(
                                "value_time"
                            )[:1]
                        else:
                            custom_value_subquery = custom_value_subquery.values(
                                "value"
                            )[:1]

                        contacts_base_qs = contacts_base_qs.annotate(
                            custom_value=Subquery(custom_value_subquery)
                        )

                        if order_method == "asc":
                            contacts_base_qs = (
                                contacts_base_qs.distinct("custom_value", "id")
                                .values("id", "custom_value")
                                .order_by("custom_value")
                            )
                        else:
                            contacts_base_qs = (
                                contacts_base_qs.distinct("custom_value", "id")
                                .values("id", "custom_value")
                                .order_by("-custom_value")
                            )
                else:
                    # Standard field sorting
                    if order_method == "asc":
                        contacts_base_qs = (
                            contacts_base_qs.distinct(order_by, "id")
                            .values("id")
                            .order_by(order_by)
                        )
                    else:
                        contacts_base_qs = (
                            contacts_base_qs.distinct(order_by, "id")
                            .values("id")
                            .order_by("-" + order_by)
                        )
            else:
                contacts_base_qs = (
                    contacts_base_qs.distinct("contact_id", "id")
                    .values("id")
                    .order_by("-contact_id")
                )
        except Exception as e:
            print("===== Debug Error at Contacts Sorting =====", e)
            contacts_base_qs = (
                contacts_base_qs.distinct("contact_id", "id")
                .values("id")
                .order_by("-contact_id")
            )

        # Debug: Check the base queryset before pagination
        logger.info(f"🔍 DEBUG: contacts_base_qs count: {contacts_base_qs.count()}")
        logger.info(f"🔍 DEBUG: contacts_base_qs type: {type(contacts_base_qs)}")
        logger.info(f"🔍 DEBUG: records_per_page: {records_per_page}")

        # ROBUST PAGINATION WITH FALLBACK
        page_number = request.GET.get("page", 1)
        contact_id = request.GET.get("contact_id", None)
        logger.info(f"🔍 DEBUG: page_number: {page_number}")

        # Initialize variables
        page_content = None
        paginator = None
        contact_ids = []

        try:
            # Check if we have any contacts in the base queryset
            base_count = contacts_base_qs.count()
            logger.info(f"🔍 DEBUG: Base queryset has {base_count} contacts")

            if base_count > 0:
                # Try pagination
                paginator = Paginator(contacts_base_qs, records_per_page)
                logger.info(f"🔍 DEBUG: paginator.count: {paginator.count}")
                logger.info(f"🔍 DEBUG: paginator.num_pages: {paginator.num_pages}")

                try:
                    page_content = paginator.page(page_number)
                    logger.info(f"🔍 DEBUG: Successfully got page {page_number}")
                    contact_ids = page_content.object_list
                except PageNotAnInteger:
                    logger.info(f"🔍 DEBUG: PageNotAnInteger exception, using page 1")
                    page_content = paginator.page(1)
                    page_number = 1
                    contact_ids = page_content.object_list
                except EmptyPage:
                    logger.info(
                        f"🔍 DEBUG: EmptyPage exception, using last page {paginator.num_pages}"
                    )
                    if paginator.num_pages > 0:
                        page_content = paginator.page(paginator.num_pages)
                        page_number = paginator.num_pages
                        contact_ids = page_content.object_list
                    else:
                        # Fallback: use first records directly
                        logger.info(
                            f"🔍 DEBUG: No pages available, using direct queryset slice"
                        )
                        contact_ids = list(contacts_base_qs[:records_per_page])
                except Exception as e:
                    logger.error(f"🔍 DEBUG: Unexpected pagination error: {e}")
                    # Fallback: use first records directly
                    logger.info(f"🔍 DEBUG: Using direct queryset slice as fallback")
                    contact_ids = list(contacts_base_qs[:records_per_page])
            else:
                logger.info(f"🔍 DEBUG: Base queryset is empty, no contacts found")
                # For search queries, this might indicate a search issue
                if search_q:
                    logger.warning(
                        f"🔍 DEBUG: Search query '{search_q}' returned no results"
                    )
                    # Try a simpler search as fallback
                    simple_search_filter = Q(workspace=workspace) & (
                        Q(name__icontains=search_q)
                        | Q(last_name__icontains=search_q)
                        | Q(email__icontains=search_q)
                        | Q(contact_id__icontains=search_q)
                    )
                    simple_contacts = Contact.objects.filter(simple_search_filter)[
                        :records_per_page
                    ]
                    contact_ids = [contact.id for contact in simple_contacts]
                    logger.info(
                        f"🔍 DEBUG: Simple search fallback found {len(contact_ids)} contacts"
                    )
                else:
                    contact_ids = []

        except Exception as e:
            logger.error(f"🔍 DEBUG: Critical error in pagination setup: {e}")
            # Ultimate fallback: try to get contacts directly from the search
            logger.info(
                f"🔍 DEBUG: Attempting ultimate fallback - direct contact search"
            )
            try:
                # Get contacts directly without pagination complexity
                fallback_contacts = Contact.objects.filter(filter_conditions)[
                    :records_per_page
                ]
                contact_ids = [contact.id for contact in fallback_contacts]
                logger.info(
                    f"🔍 DEBUG: Ultimate fallback found {len(contact_ids)} contacts"
                )
            except Exception as fallback_error:
                logger.error(
                    f"🔍 DEBUG: Ultimate fallback also failed: {fallback_error}"
                )
                contact_ids = []

        # ROBUST CONTACT PROCESSING WITH MULTIPLE FALLBACKS
        logger.info(
            f"🔍 DEBUG: Processing contact_ids: {type(contact_ids)}, length: {len(contact_ids) if contact_ids else 0}"
        )

        # Process contact_ids based on their type and structure
        processed_contact_ids = []

        if contact_ids:
            try:
                # Check if we have a values queryset (dict-like objects)
                if hasattr(contact_ids[0], "get") or isinstance(contact_ids[0], dict):
                    logger.info(f"🔍 DEBUG: Extracting IDs from values queryset")
                    processed_contact_ids = [
                        item["id"] if isinstance(item, dict) else item.get("id")
                        for item in contact_ids
                    ]
                    processed_contact_ids = [
                        id for id in processed_contact_ids if id
                    ]  # Remove None values
                # Check if we have Contact objects
                elif hasattr(contact_ids[0], "id"):
                    logger.info(f"🔍 DEBUG: Extracting IDs from Contact objects")
                    processed_contact_ids = [contact.id for contact in contact_ids]
                # Check if we already have UUIDs/IDs
                else:
                    logger.info(f"🔍 DEBUG: Using contact IDs directly")
                    processed_contact_ids = list(contact_ids)

                logger.info(
                    f"🔍 DEBUG: Processed {len(processed_contact_ids)} contact IDs"
                )

            except Exception as e:
                logger.error(f"🔍 DEBUG: Error processing contact IDs: {e}")
                processed_contact_ids = []

        # Build the final contacts queryset with multiple fallbacks
        contacts = Contact.objects.none()  # Default empty queryset

        if processed_contact_ids:
            try:
                logger.info(
                    f"🔍 DEBUG: Building contacts queryset with {len(processed_contact_ids)} IDs"
                )
                # Create a case statement to preserve the order
                preserved_order = Case(
                    *[
                        When(pk=pk, then=pos)
                        for pos, pk in enumerate(processed_contact_ids)
                    ],
                    output_field=IntegerField(),
                )
                contacts = Contact.objects.filter(
                    id__in=processed_contact_ids, workspace=workspace
                ).order_by(preserved_order)

                logger.info(
                    f"🔍 DEBUG: Successfully built contacts queryset with {contacts.count()} contacts"
                )

            except Exception as e:
                logger.error(f"🔍 DEBUG: Error building ordered contacts queryset: {e}")
                # Fallback: simple filter without ordering
                try:
                    contacts = Contact.objects.filter(
                        id__in=processed_contact_ids, workspace=workspace
                    )
                    logger.info(
                        f"🔍 DEBUG: Fallback queryset built with {contacts.count()} contacts"
                    )
                except Exception as e2:
                    logger.error(f"🔍 DEBUG: Even fallback queryset failed: {e2}")
                    contacts = Contact.objects.none()
        else:
            logger.info(f"🔍 DEBUG: No processed contact IDs, returning empty queryset")

        # Calculate custom field formula All - NOW ONLY FOR THE CURRENT PAGE
        contact_name_custom_fields = ContactsNameCustomField.objects.filter(
            workspace=workspace, type="formula", choice_value__isnull=False
        )
        for contact_name_custom_field in contact_name_custom_fields:
            for contact in contacts:
                result = calculate_math(contact_name_custom_field, contact, "contact")
                if result:
                    CustomFieldValue, _ = (
                        ContactsValueCustomField.objects.get_or_create(
                            field_name=contact_name_custom_field, contact=contact
                        )
                    )
                    CustomFieldValue.value = float(result)
                    CustomFieldValue.save()

        # =============Global Variable For Front End==========#
        contact_lists = (
            ContactList.objects.filter(workspace=workspace)
            .exclude(Q(status="unused") | Q(platforms__in=["ethereum"]))
            .order_by("-created_at")
        )

        # ROBUST PAGINATION CALCULATIONS WITH FALLBACKS
        try:
            if paginator and paginator.count > 0:
                paginator_item_begin = (
                    records_per_page * int(page_number)
                ) - records_per_page_offset_for_calc
                paginator_item_end = records_per_page * int(page_number)
                if paginator_item_end > paginator.count:
                    paginator_item_end = paginator.count
            else:
                # Fallback when paginator is None or empty
                contact_count = contacts.count() if contacts else 0
                paginator_item_begin = 1 if contact_count > 0 else 0
                paginator_item_end = contact_count
                logger.info(
                    f"🔍 DEBUG: Using fallback pagination: {paginator_item_begin}-{paginator_item_end}"
                )
        except Exception as e:
            logger.error(f"🔍 DEBUG: Error calculating pagination items: {e}")
            contact_count = contacts.count() if contacts else 0
            paginator_item_begin = 1 if contact_count > 0 else 0
            paginator_item_end = contact_count

        # Property Set
        default_property_set = get_default_property_set(
            TYPE_OBJECT_CONTACT, workspace, lang
        )
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_CONTACT
        ).order_by("created_at")

        set_id = request.GET.get("set_id")
        if view_filter.view.form:
            set_id = view_filter.view.form.id
        else:
            if not set_id:
                property_set_default = property_sets.filter(as_default=True).first()
                if property_set_default:
                    set_id = property_set_default.id

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list("id", flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ",".join(str(id) for id in members)

        # Debug: Log the pagination and final results
        logger.info(
            f"🔍 DEBUG: Page content object list: {page_content.object_list if page_content else 'None'}"
        )
        logger.info(
            f"🔍 DEBUG: Page content count: {len(page_content.object_list) if page_content else 0}"
        )
        logger.info(
            f"🔍 DEBUG: Contact IDs extracted: {contact_ids if 'contact_ids' in locals() else 'Not set'}"
        )
        logger.info(f"🔍 DEBUG: Final contacts count: {contacts.count()}")
        logger.info(f"🔍 DEBUG: Search query: '{request.GET.get('q', '')}'")
        if contacts.exists():
            first_contact = contacts.first()
            logger.info(
                f"🔍 DEBUG: First contact: {first_contact.contact_id} - {first_contact.name} {first_contact.last_name}"
            )
        else:
            logger.info(f"🔍 DEBUG: No contacts in final queryset")

        # ENSURE SAFE CONTEXT VALUES
        # Create a safe paginator if none exists
        if not paginator and contacts.exists():
            try:
                # Create a minimal paginator for the template
                paginator = Paginator(contacts, records_per_page)
                if not page_content:
                    page_content = paginator.page(1)
                logger.info(
                    f"🔍 DEBUG: Created fallback paginator with {paginator.count} items"
                )
            except Exception as e:
                logger.error(f"🔍 DEBUG: Could not create fallback paginator: {e}")
                paginator = None
                page_content = None

        context = {
            "object_type": page_group_type,
            "app_slug": CONTACT_APP_SLUG,
            "app_setting": app_setting,
            "contacts_columns": contacts_columns,
            "contacts": contacts,
            "workspace": workspace,
            "search_q": request.GET.get("q", ""),
            "config_view": config_view,
            "page_title": page_title,
            "contact_lists": contact_lists,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "paginator": paginator,
            "views": views,
            "view_filter": view_filter,
            "view_id": view_id,
            "target": target,
            "contact_id": contact_id,
            "property_sets": property_sets,
            "permission": permission,
            "set_id": set_id,
            "advance_search": advance_search,
            "advance_search_filter": advance_search_filter,
            "group_members": members,
        }

        return render(request, "data/contacts/contacts.html", context)

    # Special Functionality
    elif request.method == "POST":
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CONTACT]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_CONTACT
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
        if "download-contacts-button" in request.POST:
            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="export_contact",
            )

            encoded_format = request.POST.get("encoded_format", None)

            columns = request.POST.get("column", None)
            view_id = request.POST.get("view_id", None)

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }

            filter_dictionary = json.dumps(filter_dictionary)

            object_ids = request.POST.getlist("object_ids")
            object_ids = json.dumps(object_ids) if object_ids else None
            # Create Pydantic payload
            payload = ExportCSVContactsPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                view_id=str(view_id),
                history_id=str(history.id),
                columns=columns,
                filter_dictionary=filter_dictionary,
                encoded_format=encoded_format,
                record_ids=object_ids,
                language=lang,
                target=TYPE_OBJECT_CONTACT,
            )

            job_id = create_bg_job(
                workspace,
                request.user,
                "export_csv_contacts",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id

            # Log export job parameters for debugging
            logger.info(
                f"EXPORT_JOB: Starting contacts export for user {request.user.email} in workspace {workspace.id}"
            )
            logger.info(
                f"EXPORT_JOB: Export parameters - function: {payload.function}, job_id: {job_id}"
            )
            logger.info(
                f"EXPORT_JOB: View ID: {view_id}, History ID: {history.id}, Language: {lang}"
            )
            logger.info(f"EXPORT_JOB: Columns: {columns}")
            logger.info(f"EXPORT_JOB: Filter dictionary: {filter_dictionary}")

            ref = None
            try:
                ref = export_csv_contacts.run_no_wait(input=payload)
            except Exception as e:
                logger.error(
                    f"EXPORT_JOB: Exception occurred during export_csv_contacts: {str(e)}",
                    exc_info=True,
                )
                ref = None

            is_running = None
            if ref:
                logger.info(
                    f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}"
                )
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit background job for user {request.user.email}"
                )
                is_running = False

            if is_running:
                logger.info(
                    f"EXPORT_JOB: Successfully submitted contacts export job for user {request.user.email}"
                )
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブが正常に送信されました。CSVはメールで送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, csv will be sent to your email.",
                        type="success",
                    )
            else:
                logger.error(
                    f"EXPORT_JOB: Failed to submit contacts export job for user {request.user.email} in workspace {workspace.id}"
                )
                logger.error(
                    f"EXPORT_JOB: trigger_bg_job returned falsy value: {is_running}"
                )
                logger.error(f"EXPORT_JOB: Parameters used: {payload}")
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブの送信中にエラーが発生しました。サポートに連絡してください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="There is an error while submitting the export job. Please contact support.",
                        type="error",
                    )

            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )
            return HttpResponseRedirect(
                request.META.get("HTTP_REFERER", reverse("main", host="app"))
            )

        elif "bulk_update_addtolist" in request.POST:
            destinations_contacts_list_id = request.POST.getlist("account_addtolist")
            account_ids = request.POST.getlist("checkbox")
            flag_all = request.POST.get("flag_all")

            if flag_all:
                contacts = Contact.objects.filter(workspace=workspace)
            elif account_ids:
                contacts = Contact.objects.filter(id__in=account_ids)
            else:
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))

            contact_lists = ContactList.objects.filter(
                id__in=destinations_contacts_list_id
            ).order_by("-created_at")
            for contact_list in contact_lists:
                contact_list.contacts.add(*contacts)

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        elif "bulk_restore_contacts" in request.POST:
            flag_all = request.POST.get("flag_all")
            if flag_all:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_CONTACT, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_CONTACT,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)
            objects.update(status="active")

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "bulk_delete_contacts" in request.POST:
            flag_all = request.POST.get("flag_all")
            if flag_all:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)
            objects.update(status="archived")

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))
        elif "update-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            view_name = request.POST.get("view_name", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column = request.POST.get("column", None)
            kanban_unlisted = (
                True
                if request.POST.get("kanban_unlisted", False) == "on"
                else request.POST.get("kanban_unlisted", False)
            )
            status_selector = request.POST.get("status-selector", None)

            order_by = request.POST.get("order-by", None)
            sort_method = request.POST.get("sort-method", None)

            property_set_is_default_input = request.POST.get(
                "property_set_default", None
            )

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if view.is_private and (view.user == request.user):
                    view.is_private = bool(request.POST.get("set-private-view", False))
                    view.user = request.user if view.is_private else None
                    view.save()
            # create
            else:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                view = View.objects.create(
                    workspace=workspace,
                    title=view_name,
                    target="contacts",
                    is_private=is_private,
                    user=user,
                )

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            if property_set_is_default_input:
                default_set = PropertySet.objects.get(id=property_set_is_default_input)
                view.form = default_set
                default_set.as_default = True
                default_set.save()
                # check default
                PropertySet.objects.filter(
                    workspace=default_set.workspace, target=default_set.target
                ).exclude(id=default_set.id).update(as_default=False)

            if view_name:
                view.title = view_name

            if view_table:
                view_filter.view_type = view_table

            contact_list = request.POST.get("contact_list", None)
            print("========== contact_list: ", contact_list)
            if contact_list:
                contact_list = ContactList.objects.filter(id=contact_list).first()
                print("contact_list: ", contact_list)
                view_filter.contact_list = contact_list
            else:
                view_filter.contact_list = None

            pagination_value = request.POST.get("pagination", None)
            if pagination_value and pagination_value.isdigit():
                view_filter.pagination = int(pagination_value)
            else:
                view_filter.pagination = 25  # Default value if not provided or invalid

            view_filter.kanban_unlisted = kanban_unlisted
            view_filter.archive = archive

            # Handle sorting parameters
            if order_by:
                view_filter.sort_order_by = order_by
            else:
                view_filter.sort_order_by = None

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = "asc"
                else:
                    view_filter.sort_order_method = None

            # convert to normal List
            column = column.split(",")
            if view and column[0]:
                view_filter.column = column
            else:
                view_filter.column = None

            if view_table and status_selector:
                if view_table == "kanban":
                    view_filter.choice_customfield = status_selector
                else:
                    view_filter.choice_customfield = None

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }
            view_filter.filter_value = filter_dictionary
            view_filter.save()
            view.save()

            if view.title:
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + "?view_id="
                        + str(view_filter.view.id)
                    )
                return redirect(reverse("main", host="app"))
            else:
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))

        elif "delete-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            if not view_id:
                return HttpResponse(status=400)

            view_id = request.POST.get("view_id", None)
            if view_id:
                View.objects.get(id=view_id).delete()
                if module:
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))

        elif "bulk_update_contact" in request.POST:
            account_ids = request.POST.getlist("account_id", None)
            contact_name = request.POST.get("contact_name", None)
            company_name = request.POST.get("company_name", None)
            email = request.POST.get("email", None)
            files = request.FILES.getlist("fileUpload")
            urls = request.POST.getlist("urls", None)
            status = request.POST.get("status", None)

            for account_id in account_ids:
                contact = Contact.objects.get(id=account_id)
                if "name|checkbox" in request.POST:
                    contact.name = contact_name

                if "company|checkbox" in request.POST:
                    contact.companies.clear()
                    company_name_list = ast.literal_eval(company_name)
                    for comp_name in company_name_list:
                        comp_id = comp_name["id"]
                        company = Company.objects.get(id=comp_id)
                        contact.companies.add(company)

                if "img|checkbox" in request.POST:
                    for file in files:
                        file.name = str(uuid.uuid4()) + "." + file.name.split(".")[-1]
                        contact.image_file = file
                        contact.save()

                if "email|checkbox" in request.POST:
                    contact.email = email

                if "url|checkbox" in request.POST:
                    for url in urls:
                        if not url:
                            continue

                        SocialAccountLink.objects.get_or_create(
                            contact=contact, platform_url=url
                        )
                    SocialAccountLink.objects.filter(contact=contact).exclude(
                        platform_url__in=urls
                    ).delete()

                if "status|checkbox" in request.POST:
                    contact.status = status

                contact.save()
                save_association_label(request, contact, TYPE_OBJECT_CONTACT)

        elif "bulk_duplicate" in request.POST:
            flag_all = request.POST.get("flag_all")
            if flag_all:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            for obj in objects:
                customFields = custom_value_model.objects.filter(
                    **{custom_value_relation: obj}
                )

                old_obj_id = obj.id
                obj.id = None
                setattr(obj, id_field, None)
                obj.created_at = timezone.now()
                obj.save()

                # keep company
                old_objects = base_model.objects.filter(id=old_obj_id).first()
                for company in old_objects.companies.all():
                    obj.companies.add(company)
                obj.save()

                for field in customFields:
                    old_field_id = field.id
                    field.id = None
                    setattr(field, custom_value_relation, obj)
                    field.save()

                    # duplicate file or image
                    if custom_value_file_relation is None:
                        continue

                    customFieldsValueFile = custom_value_file_model.objects.filter(
                        **{f"{custom_value_file_relation}__id": old_field_id}
                    )
                    if customFieldsValueFile:
                        for value_file in customFieldsValueFile:
                            value_file.id = None
                            value_file.created_at = timezone.now()
                            setattr(value_file, custom_value_file_relation, field)
                            value_file.save()

        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))
    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


################################# CREATE ##########################################


def load_drawer_sync_contacts(request):
    drawer_type = request.GET.get("drawer_type", False)
    workspace = get_workspace(request.user)
    companies = Company.objects.filter(workspace=workspace)
    lang = request.LANGUAGE_CODE

    if drawer_type == "shopturbo-view-sync-contacts":
        import_export_type = request.GET.get("import_export_type", None)
        section = request.GET.get("section", None)

        if section == "integrations":
            import_export_type = request.GET.get("import_export_type", None)
            platforms = ["hubspot", "shopify", "freee", "ec-cube", "line", "salesforce"]

            # Safely parse contact_ids with proper error handling
            contact_ids_param = request.GET.get("contact_ids")
            try:
                if contact_ids_param and contact_ids_param != "None":
                    checkbox = ast.literal_eval(contact_ids_param)
                else:
                    checkbox = None
            except (ValueError, SyntaxError) as e:
                logger.warning(
                    f"Error parsing contact_ids parameter '{contact_ids_param}': {e}"
                )
                checkbox = None

            if checkbox is None:
                checkbox = list(
                    str(contact_id)
                    for contact_id in Contact.objects.filter(
                        workspace=workspace, status="active"
                    ).values_list("id", flat=True)
                )

            channels = Channel.objects.filter(
                workspace=get_workspace(request.user), integration__slug__in=platforms
            )

            for name in EXCLUDE_SYNC_CHANNEL_NAME:
                channels = channels.exclude(name__icontains=name)

            count_contacts = len(checkbox)
            # count_contacts = Contact.objects.filter(workspace=workspace, status='active').count()

            context = {
                "contact_ids": checkbox,
                "channels": channels,
                "count_contacts": count_contacts,
                "import_export_type": import_export_type,
            }

            return render(
                request,
                "data/contacts/manage-sync-settings-shopturbo-contacts-import-export.html",
                context,
            )

        elif section == "history":
            import_export_type = request.GET.get("import_export_type", None)

            filter_conditions = Q(workspace=workspace)
            if import_export_type == "import":
                filter_conditions &= Q(type="import_contact")
            elif import_export_type == "export":
                filter_conditions &= Q(type="export_contact")

            history = TransferHistory.objects.filter(filter_conditions).order_by(
                "-created_at"
            )

            context = {
                "history": history,
                "object_type": TYPE_OBJECT_CONTACT,
                "import_export_type": import_export_type,
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-items-import-export-history.html",
                context,
            )

        elif section == "import_csv":
            predefined_data = request.GET.getlist("predefined-data", [])
            add_on = request.GET.get("addon_source", None)

            set_id = request.GET.get("set_id", None)
            view_id = request.GET.get("view_id", None)
            if set_id:
                property_set = PropertySet.objects.filter(id=set_id).first()
            else:
                if view_id == "None":
                    view_id = None
                if view_id:
                    view = View.objects.get(id=view_id)
                    if view.form:
                        property_set = view.form
                    else:
                        condition_filter = Q(
                            workspace=workspace, target=TYPE_OBJECT_CONTACT
                        )
                        condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                        property_set = PropertySet.objects.filter(
                            condition_filter
                        ).first()
                else:
                    condition_filter = Q(
                        workspace=workspace, target=TYPE_OBJECT_CONTACT
                    )
                    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                    property_set = PropertySet.objects.filter(condition_filter).first()

            properties = {"list_all": []}
            if property_set:
                for p in property_set.children:
                    properties["list_all"].append(p)

            CustomFieldMap = {}
            explorecustomfield = ContactsNameCustomField.objects.filter(
                workspace=workspace
            ).order_by("order")
            for ctf in explorecustomfield:
                CustomFieldMap[str(ctf.id)] = ctf

            if add_on:
                properties, CustomFieldMap = get_default_prompt_set(
                    workspace, TYPE_OBJECT_CONTACT, ContactsNameCustomField
                )

            context = {
                "predefined_data": predefined_data,
                "create_type": add_on,
                "companies": companies,
                "country_code": COUNTRY_CODE,
                "LANGUAGE_CODE": request.LANGUAGE_CODE,
                "CustomFieldMap": CustomFieldMap,
                "properties": properties,
                "set_id": set_id,
                "object_type": TYPE_OBJECT_CONTACT,
            }

            return render(
                request,
                "data/contacts/manage-sync-settings-shopturbo-contacts-bulk-entry.html",
                context,
            )

        elif section == "export_download":
            page = request.GET.get("page", False)
            view_type = VIEW_MODE

            column_values = []

            namecustomfieldchoices = []
            view_type = view_type[0:1]  # only table is required

            target = "contacts"
            column_values = [str(v.name) for v in Contact._meta.fields]
            contactsnamecustomfield = ContactsNameCustomField.objects.filter(
                workspace=workspace
            ).values_list("id", flat=True)
            contactsnamecustomfield = [str(uuid) for uuid in contactsnamecustomfield]
            column_values.extend(contactsnamecustomfield)
            channels = Channel.objects.filter(
                workspace=get_workspace(request.user),
                integration__slug__in=[
                    "hubspot",
                    "shopify",
                    "freee",
                    "ec-cube",
                    "line",
                    "rakuten",
                    "salesforce",
                ],
            )
            for channel in channels:
                if channel.integration.slug == "line":
                    column_values.insert(
                        len(column_values),
                        f"{str(channel.id)} - {str(channel.name)} - Line User ID",
                    )
                else:
                    column_values.insert(
                        len(column_values), f"{str(channel.name)} - contact ID"
                    )
            for field in ["image_file", "property_set"]:
                column_values.remove(field)

            namecustomfieldchoices = ContactsNameCustomField.objects.filter(
                workspace=workspace, type="choice"
            )
            column_values.remove("contact_id")
            column_values.append("first_name")

            pre_default_column = DEFAULT_COLUMNS_CONTACTS.copy()

            column_values.remove("id")
            column_values.remove("workspace")

            view_id = request.GET.get("view_id", None)
            if view_id == "None":
                view_id = None
            view_filter = None
            if view_id:
                view = View.objects.get(id=view_id)
                view_filter = ViewFilter.objects.filter(view=view).first()
                if not view_filter.column:
                    view_filter.column = pre_default_column
            else:
                view_filter = ViewFilter()
                view_filter.column = pre_default_column

            export_template = ExportTemplate.objects.filter(
                workspace=workspace, target=TYPE_OBJECT_CONTACT, as_default=True
            ).first()
            if export_template:
                if export_template.properties:
                    view_filter.column = export_template.properties.split(",")

            context = {
                "VIEW_MODE": view_type,
                "view_filter": view_filter,
                "column_values": column_values,
                "page": "contacts",
                "namecustomfieldchoices": namecustomfieldchoices,
                "download_view": True,
                "object_type": TYPE_OBJECT_CONTACT,
            }
            return render(
                request,
                "data/contacts/manage-sync-settings-shopturbo-contacts-export-download.html",
                context,
            )

        elif section == "import_selector":
            import_export_type = request.GET.get("import_export_type", None)
            context = {
                "page_group_type": "contacts",
                "contact_ids": request.GET.get("contact_ids", None),
                "view_id": request.GET.get("view_id", None),
                "import_export_type": import_export_type,
                "page": request.GET.get("page", None),
            }
            # Debug information
            print(f"Rendering import_selector with context: {context}")
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-contacts-import-source-selector.html",
                context,
            )

        elif section == "export_selector":
            context = {
                "page_group_type": "contacts",
                "contact_ids": request.GET.get("contact_ids", None),
                "pre_selected_export_selector": request.GET.get(
                    "pre_selected_export_selector", None
                ),
                "view_id": request.GET.get("view_id", None),
            }
            return render(
                request,
                "data/shopturbo/sync-contacts-export-source-selector.html",
                context,
            )

        else:
            checkbox = None

            if "checkbox" in request.GET:
                checkbox = request.GET.getlist("checkbox", False)

            import_export_type = request.GET.get("import_export_type", None)
            view_id = request.GET.get("view_id", None)
            context = {
                "contact_ids": checkbox,
                "import_export_type": import_export_type,
                "page_group_type": "contacts",
                "view_id": view_id,
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-contacts.html",
                context,
            )

    elif drawer_type == "company-view-export-import":
        section = request.GET.get("section", None)

        if section == "integrations":
            import_export_type = request.GET.get("import_export_type", None)
            platforms = ["hubspot", "shopify", "freee", "ec-cube", "line", "salesforce"]

            checkbox = None
            company_ids = request.GET.get("company_ids", None)
            if company_ids and company_ids != "None":
                try:
                    checkbox = ast.literal_eval(company_ids)
                except (ValueError, SyntaxError) as e:
                    logger.warning(
                        f"Error parsing company_ids parameter '{company_ids}': {e}"
                    )
                    checkbox = None

            if checkbox is None:
                checkbox = list(
                    str(company_id)
                    for company_id in Company.objects.filter(
                        workspace=workspace, status="active"
                    ).values_list("id", flat=True)
                )

            channels = Channel.objects.filter(
                workspace=get_workspace(request.user), integration__slug__in=platforms
            )

            for name in EXCLUDE_SYNC_CHANNEL_NAME:
                channels = channels.exclude(name__icontains=name)

            count_companies = len(checkbox)
            # count_contacts = Contact.objects.filter(workspace=workspace, status='active').count()

            context = {
                "company_ids": checkbox,
                "channels": channels,
                "count_companies": count_companies,
                "import_export_type": import_export_type,
            }

            return render(
                request,
                "data/contacts/manage-sync-settings-shopturbo-companies-import-export.html",
                context,
            )

        elif section == "import_csv":
            predefined_data = request.GET.getlist("predefined-data", [])
            add_on = request.GET.get("addon_source", None)

            set_id = request.GET.get("set_id", None)
            view_id = request.GET.get("view_id", None)
            if set_id:
                property_set = PropertySet.objects.filter(id=set_id).first()
            else:
                if view_id == "None":
                    view_id = None
                if view_id:
                    view = View.objects.get(id=view_id)
                    if view.form:
                        property_set = view.form
                    else:
                        condition_filter = Q(
                            workspace=workspace, target=TYPE_OBJECT_COMPANY
                        )
                        condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                        property_set = PropertySet.objects.filter(
                            condition_filter
                        ).first()
                else:
                    condition_filter = Q(
                        workspace=workspace, target=TYPE_OBJECT_COMPANY
                    )
                    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                    property_set = PropertySet.objects.filter(condition_filter).first()

            properties = {"list_all": []}
            if property_set:
                for p in property_set.children:
                    properties["list_all"].append(p)

            CustomFieldMap = {}
            explorecustomfield = CompanyNameCustomField.objects.filter(
                workspace=workspace
            ).order_by("order")
            for ctf in explorecustomfield:
                CustomFieldMap[str(ctf.id)] = ctf

            if add_on:
                properties, CustomFieldMap = get_default_prompt_set(
                    workspace, TYPE_OBJECT_CONTACT, ContactsNameCustomField
                )

            context = {
                "predefined_data": predefined_data,
                "create_type": add_on,
                "companies": companies,
                "country_code": COUNTRY_CODE,
                "LANGUAGE_CODE": request.LANGUAGE_CODE,
                "CustomFieldMap": CustomFieldMap,
                "properties": properties,
                "set_id": set_id,
                "object_type": TYPE_OBJECT_COMPANY,
            }

            return render(
                request, "data/contacts/manage-sync-companies-bulk-entry.html", context
            )

        elif section == "history":
            import_export_type = request.GET.get("import_export_type", None)
            filter_conditions = Q(workspace=workspace)
            if import_export_type == "import":
                filter_conditions &= Q(type="import_company")
            elif import_export_type == "export":
                filter_conditions &= Q(type="export_company")

            history = TransferHistory.objects.filter(filter_conditions).order_by(
                "-created_at"
            )
            context = {
                "history": history,
                "object_type": TYPE_OBJECT_COMPANY,
                "import_export_type": import_export_type,
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-items-import-export-history.html",
                context,
            )

        elif section == "import_selector":
            context = {
                "page_group_type": TYPE_OBJECT_COMPANY,
                "view_id": request.GET.get("view_id", None),
                "section": "bulk-entry",
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-company-import-source-selector.html",
                context,
            )

        elif section == "export_selector":
            context = {
                "page_group_type": TYPE_OBJECT_COMPANY,
                "company_ids": request.GET.get("company_ids", None),
                "pre_selected_export_selector": request.GET.get(
                    "pre_selected_export_selector", None
                ),
                "view_id": request.GET.get("view_id", None),
            }
            return render(
                request,
                "data/shopturbo/sync-contacts-export-source-selector.html",
                context,
            )

        else:
            checkbox = None

            if "checkbox" in request.GET:
                checkbox = request.GET.getlist("checkbox", False)

            import_export_type = request.GET.get("import_export_type", None)
            view_id = request.GET.get("view_id", None)
            context = {
                "company_ids": checkbox,
                "import_export_type": import_export_type,
                "page_group_type": TYPE_OBJECT_COMPANY,
                "view_id": view_id,
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-contacts.html",
                context,
            )

    return HttpResponse(200)


@login_or_hubspot_required
def customer_item_price(request):
    if request.method == "POST":
        lang = request.LANGUAGE_CODE
        workspace = get_workspace(request.user)

        CustomFieldName = request.POST.get("CustomFieldName", None)
        customer_item_price_input = request.POST.get("customer-item-price", None)
        price_precentage = request.POST.get("price-precentage", None)
        tax_precentage = request.POST.get("tax-precentage", None)

        item_id = request.POST.get("item_id", None)
        obj_type = request.POST.get("obj_type", None)
        obj_id = request.POST.get("obj_id", None)
        tax = request.POST.get("customer-item-tax", 0)
        if not tax:
            tax = 0

        def save_partial(
            item_id,
            obj_type,
            obj_id,
            CustomFieldName,
            customer_item_price_input,
            price_precentage,
            tax,
            lang,
        ):
            item = ShopTurboItems.objects.get(id=item_id)

            if obj_type == TYPE_OBJECT_CONTACT:
                contact = Contact.objects.get(id=obj_id)

                CustomFieldName = ContactsNameCustomField.objects.get(
                    id=CustomFieldName
                )
                CustomFieldValue = ContactsValueCustomField.objects.filter(
                    field_name=CustomFieldName, contact=contact
                ).first()
                if price_precentage or tax:
                    try:
                        CustomFieldValue, _ = (
                            ContactsValueCustomField.objects.get_or_create(
                                field_name=CustomFieldName, contact=contact
                            )
                        )
                    except ContactsValueCustomField.MultipleObjectsReturned:
                        CustomFieldValue = ContactsValueCustomField.objects.filter(
                            field_name=CustomFieldName, contact=contact
                        ).first()
                        ContactsValueCustomField.objects.filter(
                            field_name=CustomFieldName, contact=contact
                        ).exclude(id=CustomFieldValue.id).delete()

                    CustomFieldValue.value = {
                        "price_precentage": price_precentage,
                        "tax_precentage": tax,
                    }
                    CustomFieldValue.save()
                else:
                    ContactsValueCustomField.objects.filter(
                        field_name=CustomFieldName, contact=contact
                    ).delete()

                customer_item_price, _ = (
                    ShopTurboCustomerItemsPrice.objects.get_or_create(
                        item=item, contact=contact
                    )
                )
                if customer_item_price_input or tax:
                    if customer_item_price_input:
                        if type(customer_item_price_input) == str:
                            if "," in customer_item_price_input:
                                customer_item_price_input = (
                                    customer_item_price_input.replace(",", "")
                                )
                        customer_item_price.price = customer_item_price_input
                        if item.currency:
                            customer_item_price.currency = item.currency
                        else:
                            if lang == "ja":
                                customer_item_price.currency = "JPY"
                            else:
                                customer_item_price.currency = "USD"

                            item.currency = customer_item_price.currency
                            item.save()

                    if tax:
                        if tax != "do-nothing":
                            customer_item_price.tax = tax

                    customer_item_price.target = TYPE_OBJECT_CONTACT
                    customer_item_price.save()
                else:
                    customer_item_price.delete()

                if type(tax) == str:
                    tax = float(tax)
                if not customer_item_price_input and not tax:
                    customer_item_price.delete()

            elif obj_type == TYPE_OBJECT_COMPANY:
                company = Company.objects.get(id=obj_id)

                CustomFieldName = CompanyNameCustomField.objects.get(id=CustomFieldName)
                CustomFieldValue = CompanyValueCustomField.objects.filter(
                    field_name=CustomFieldName, company=company
                ).first()
                if price_precentage or tax:
                    try:
                        CustomFieldValue, _ = (
                            CompanyValueCustomField.objects.get_or_create(
                                field_name=CustomFieldName, company=company
                            )
                        )
                    except CompanyValueCustomField.MultipleObjectsReturned:
                        CustomFieldValue = CompanyValueCustomField.objects.filter(
                            field_name=CustomFieldName, company=company
                        ).first()
                        CompanyValueCustomField.objects.filter(
                            field_name=CustomFieldName, company=company
                        ).exclude(id=CustomFieldValue.id).delete()

                    CustomFieldValue.value = {
                        "price_precentage": price_precentage,
                        "tax_precentage": tax,
                    }
                    CustomFieldValue.save()
                else:
                    CompanyValueCustomField.objects.filter(
                        field_name=CustomFieldName, company=company
                    ).delete()

                customer_item_price, _ = (
                    ShopTurboCustomerItemsPrice.objects.get_or_create(
                        item=item, company=company
                    )
                )
                if customer_item_price_input or tax:
                    if customer_item_price_input:
                        if type(customer_item_price_input) == str:
                            if "," in customer_item_price_input:
                                customer_item_price_input = (
                                    customer_item_price_input.replace(",", "")
                                )
                        customer_item_price.price = customer_item_price_input
                        if item.currency:
                            customer_item_price.currency = item.currency
                        else:
                            if lang == "ja":
                                customer_item_price.currency = "JPY"
                            else:
                                customer_item_price.currency = "USD"
                            item.currency = customer_item_price.currency
                            item.save()

                    if tax:
                        if tax != "do-nothing":
                            customer_item_price.tax = tax

                    customer_item_price.target = TYPE_OBJECT_COMPANY
                    customer_item_price.save()

                else:
                    customer_item_price.delete()

                if type(tax) == str:
                    tax = float(tax)
                if not customer_item_price_input and not tax:
                    customer_item_price.delete()

        if "save-type" in request.POST:
            exclude_items = request.POST.getlist("exclude-item", [])
            items = (
                ShopTurboItems.objects.filter(workspace=workspace)
                .exclude(id__in=exclude_items)
                .order_by("item_id")
            )

            for item in items:
                customer_item_price_input = 0
                shopturbo_price = ShopTurboItemsPrice.objects.filter(
                    item=item, default=True
                ).first()
                if shopturbo_price:
                    if not shopturbo_price.price:
                        shopturbo_price.price = 0
                    if type(price_precentage) == str:
                        price_precentage = float(price_precentage)
                    customer_item_price_input = (
                        shopturbo_price.price * price_precentage / 100
                    )
                save_partial(
                    item.id,
                    obj_type,
                    obj_id,
                    CustomFieldName,
                    customer_item_price_input,
                    price_precentage,
                    tax_precentage,
                    lang,
                )
        else:
            # save partial
            save_partial(
                item_id,
                obj_type,
                obj_id,
                CustomFieldName,
                customer_item_price_input,
                price_precentage,
                tax,
                lang,
            )

    # If we've reached here, it means no specific section was handled
    # Log the request parameters for debugging
    print(
        f"Unhandled request in load_drawer_sync_contacts: section={request.GET.get('section')}, import_export_type={request.GET.get('import_export_type')}"
    )

    # For import requests, always default to import_selector
    if (
        request.GET.get("drawer_type") == "shopturbo-view-sync-contacts"
        and request.GET.get("import_export_type") == "import"
    ):
        import_export_type = request.GET.get("import_export_type")
        context = {
            "page_group_type": "contacts",
            "contact_ids": request.GET.get("contact_ids", None),
            "view_id": request.GET.get("view_id", None),
            "import_export_type": import_export_type,
            "page": request.GET.get("page", None),
        }
        print(f"Rendering import selector with context: {context}")
        return render(
            request,
            "data/shopturbo/manage-sync-settings-shopturbo-contacts-import-source-selector.html",
            context,
        )

    # Never return a plain 200 response - always return HTML content
    # This is a fallback that should rarely be reached
    print(
        "WARNING: Reached fallback in load_drawer_sync_contacts - returning generic HTML response"
    )
    return HttpResponse(
        '<div class="alert alert-warning">No content available for this request. Please try again or contact support.</div>'
    )


@login_or_hubspot_required
def request_sync_company(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    select_integration_ids = request.POST.getlist("select_integration_ids", [])

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_COMPANY]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_COMPANY
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug

    channels = Channel.objects.filter(
        workspace=workspace, id__in=select_integration_ids
    )
    file_columns = request.POST.getlist("file-column", [])
    contact_file_columns_name = request.POST.getlist("file-column-name", [])
    sanka_properties = request.POST.getlist("sanka-properties", [])
    ignores = request.POST.getlist("ignore", [])
    mapping_custom_fields = {}
    company_mapping = {}

    header_page = request.POST.get("page", None)
    header_mapping_id = request.POST.get("header_mapping_id", None)
    processed_page = request.POST.get("previous_page", None)
    search_q = request.POST.get("search_q", None)
    processed_search_q = request.POST.get("previous_search_q", None)

    enable_salesforce_filter = request.POST.get("enable-salesforce-filter", None)
    if enable_salesforce_filter == "1":
        enable_salesforce_filter = True
    else:
        enable_salesforce_filter = False

    # Get the multiple filters JSON array
    salesforce_filters = request.POST.get("salesforce-filters", "[]")
    if salesforce_filters == "None" or not salesforce_filters:
        salesforce_filters = "[]"

    if search_q == "":
        search_q = None
    if processed_search_q == "":
        processed_search_q = None

    if header_mapping_id:
        header_mapping = TempHeaderList.objects.filter(id=header_mapping_id).first()

        header_list = []
        if header_mapping and header_mapping.header_list:
            if search_q:
                for key, value in header_mapping.header_list.items():
                    for val in value:
                        if (
                            search_q.lower() in val.get("name", "").lower()
                            or search_q.lower() in val.get("name_ja", "").lower()
                        ):
                            header_list.append(val)
            else:
                header_list = header_mapping.header_list.get(header_page, [])

        if processed_search_q:
            processed_header_list = []
            file_columns = request.POST.getlist("file-column", [])
            contact_file_columns_name = request.POST.getlist("file-column-name", [])
            sanka_properties = request.POST.getlist("sanka-properties", [])
            ignores = request.POST.getlist("ignore", [])

            for idx, file_column in enumerate(file_columns):
                for key, value in header_mapping.header_list.items():
                    for val in value:
                        if val["value"] == file_column:
                            val["default"] = sanka_properties[idx]
                            val["skip"] = ignores[idx]
                            break
            header_mapping.save()
        elif processed_page:
            processed_header_list = header_mapping.header_list.get(processed_page, [])

            for idx, file_column in enumerate(file_columns):
                for header in processed_header_list:
                    if header["value"] == file_column:
                        header["default"] = sanka_properties[idx]
                        header["skip"] = ignores[idx]
                        break
            header_mapping.header_list[processed_page] = processed_header_list
            header_mapping.save()

        header_mapping = TempHeaderList.objects.filter(id=header_mapping_id).first()
        if header_mapping and header_mapping.header_list:
            value_list = []
            name_list = []
            default_list = []
            skip_list = []
            for page_key, head_vals in header_mapping.header_list.items():
                for head_val in head_vals:
                    value_list.append(head_val["value"])
                    if lang == "ja":
                        name_list.append(head_val["name_ja"])
                    else:
                        name_list.append(head_val["name"])
                    default_list.append(head_val["default"])
                    skip_list.append(head_val["skip"])

            file_columns = value_list
            contact_file_columns_name = name_list
            sanka_properties = default_list
            ignores = skip_list

    print("file_columns", file_columns)
    print("contact_file_columns_name", contact_file_columns_name)
    print("sanka_properties", sanka_properties)
    print("ignores", ignores)

    for idx, file_column in enumerate(file_columns):
        # check ignore
        company_mapping[file_column] = sanka_properties[idx]
        if ignores[idx] == "True":
            continue
        mapping_custom_fields[file_column] = sanka_properties[idx]

    if "save_mapping" in request.POST:
        try:
            mapping = ContactsMappingFields.objects.get(
                workspace=workspace, platform__isnull=True, object_type="company"
            )
            mapping.platform = channels.first().integration.slug
            mapping.save()
        except:
            pass
        mapping, _ = ContactsMappingFields.objects.get_or_create(
            workspace=workspace,
            platform=channels.first().integration.slug,
            object_type="company",
        )
        data = {}
        for i, field in enumerate(company_mapping):
            if (
                company_mapping[field] in ["create_new", "create_new_choice"]
                and ignores[i] != "True"
            ):
                field_type = "text"
                if company_mapping[field] == "create_new_choice":
                    field_type = "choice"

                _, _ = CompanyNameCustomField.objects.get_or_create(
                    workspace=workspace,
                    name=contact_file_columns_name[i],
                    type=field_type,
                )
                field_data = {"value": contact_file_columns_name[i], "skip": ignores[i]}
            elif company_mapping[field] == "create_new|order" and ignores[i] != "True":
                new_column_value = f"{contact_file_columns_name[i]}"
                new_field, _ = ShopTurboOrdersNameCustomField.objects.get_or_create(
                    workspace=workspace, name=contact_file_columns_name[i], type="text"
                )
                field_data = {"value": new_column_value, "skip": ignores[i]}
            else:
                field_data = {"value": company_mapping[field], "skip": ignores[i]}
            data[field] = field_data
        mapping.input_data = data
        mapping.save()

    else:
        for channel in channels:
            print("channel", "platform", channel.integration.slug)
            contact_workspace_mapping = None
            contact_workspace_mapping, _ = ContactsMappingFields.objects.get_or_create(
                workspace=workspace,
                platform=channel.integration.slug,
                object_type="company",
            )
            contact_data = {}
            for i, field in enumerate(company_mapping):
                if (
                    company_mapping[field] in ["create_new", "create_new_choice"]
                    and ignores[i] != "True"
                ):
                    field_type = "text"
                    if company_mapping[field] == "create_new_choice":
                        field_type = "choice"

                    _, _ = CompanyNameCustomField.objects.get_or_create(
                        workspace=workspace,
                        name=contact_file_columns_name[i],
                        type=field_type,
                    )
                    field_data = {
                        "value": contact_file_columns_name[i],
                        "skip": ignores[i],
                    }
                elif (
                    company_mapping[field] == "create_new|order"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{contact_file_columns_name[i]}"
                    new_field, _ = ShopTurboOrdersNameCustomField.objects.get_or_create(
                        workspace=workspace,
                        name=contact_file_columns_name[i],
                        type="text",
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                else:
                    field_data = {"value": company_mapping[field], "skip": ignores[i]}
                contact_data[field] = field_data

                if field_data["skip"] == "False" or field_data["skip"] == False:
                    mapping_custom_fields[field] = field_data["value"]
            contact_workspace_mapping.input_data = contact_data
            contact_workspace_mapping.save()
            if channel.integration.slug == "hubspot":
                hubspot_clients = {
                    settings.HUBSPOT_CLIENT_ID: {
                        "client_id": settings.HUBSPOT_CLIENT_ID,
                        "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                    }
                }

                # Auto refresh token
                if channel.api_key in hubspot_clients:
                    client_credentials = hubspot_clients[channel.api_key]
                    token_url = "https://api.hubapi.com/oauth/v1/token"
                    data = {
                        "grant_type": "refresh_token",
                        "client_id": client_credentials["client_id"],
                        "client_secret": client_credentials["client_secret"],
                        "refresh_token": channel.refresh_token,
                    }
                    response = requests.post(token_url, data=data)
                    token_info = response.json()
                    print(token_info)
                    channel.access_token = token_info.get("access_token")
                    channel.save()

            if "import_companies" in request.POST:
                if lang == "ja":
                    task_name = f"{channel.integration.slug} の企業をインポート"
                else:
                    task_name = f"Import {channel.integration.slug} companies"
                history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="import_company",
                    name=task_name,
                    channel=channel or None,
                )

                # Create platform-specific payload and run background task
                if channel.integration.slug == "salesforce":
                    payload = ImportSalesforceCompaniesPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        mapping_custom_fields=json.dumps(mapping_custom_fields),
                        history_id=str(history.id),
                        lang=lang,
                        enable_salesforce_filter=enable_salesforce_filter,
                        salesforce_filters=salesforce_filters,
                        salesforce_checkpoint=0,
                    )

                    job_id = create_bg_job(
                        workspace,
                        request.user,
                        "import_salesforce_companies",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id

                    ref = None
                    try:
                        ref = import_salesforce_companies_task.run_no_wait(
                            input=payload
                        )
                    except Exception as e:
                        logger.error(
                            f"Exception occurred during import_salesforce_companies_task: {str(e)}",
                            exc_info=True,
                        )
                        ref = None

                    is_running = None
                    if ref:
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        is_running = False

                elif channel.integration.slug == "hubspot":
                    payload = ImportHubspotCompaniesPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        mapping_custom_fields=json.dumps(mapping_custom_fields),
                        history_id=str(history.id),
                        lang=lang,
                    )

                    job_id = create_bg_job(
                        workspace,
                        request.user,
                        "import_hubspot_companies",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id

                    ref = None
                    try:
                        ref = import_hubspot_companies_task.run_no_wait(input=payload)
                    except Exception as e:
                        logger.error(
                            f"Exception occurred during import_hubspot_companies_task: {str(e)}",
                            exc_info=True,
                        )
                        ref = None

                    is_running = None
                    if ref:
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        is_running = False
                else:
                    # Unsupported platform
                    logger.error(
                        f"Unsupported platform for company import: {channel.integration.slug}"
                    )
                    is_running = False
                if is_running:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="企業をインポートしております。少々お待ちください...",
                            type="success",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Companies being imported. Please give it a few moment...",
                            type="success",
                        )

            else:
                if lang == "ja":
                    task_name = f"{channel.integration.slug} の企業をエクスポート"
                else:
                    task_name = f"Export {channel.integration.slug} companies"
                history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="export_company",
                    name=task_name,
                    channel=channel or None,
                )

                # Get company IDs for export
                company_ids = request.POST.get("export_companies", "")
                if company_ids == "None":
                    company_ids = ""

                # Get HubSpot list data if applicable
                hubspot_list_data = ""
                if (
                    channel.integration.slug == "hubspot"
                    and "set-hubspot-list" in request.POST
                ):
                    hubspot_list_data = request.POST.get("hubspot-list-data", "0")

                # Create platform-specific payload and run background task
                if channel.integration.slug == "salesforce":
                    payload = ExportSalesforceCompaniesPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        mapping_custom_fields=json.dumps(mapping_custom_fields),
                        history_id=str(history.id),
                        lang=lang,
                        company_ids=company_ids,
                    )

                    job_id = create_bg_job(
                        workspace,
                        request.user,
                        "export_salesforce_companies",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id

                    ref = None
                    try:
                        ref = export_salesforce_companies_task.run_no_wait(
                            input=payload
                        )
                    except Exception as e:
                        logger.error(
                            f"Exception occurred during export_salesforce_companies_task: {str(e)}",
                            exc_info=True,
                        )
                        ref = None

                    is_running = None
                    if ref:
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        is_running = False

                elif channel.integration.slug == "hubspot":
                    payload = ExportHubspotCompaniesPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        mapping_custom_fields=json.dumps(mapping_custom_fields),
                        history_id=str(history.id),
                        lang=lang,
                        company_ids=company_ids,
                        hubspot_list_data=hubspot_list_data,
                    )

                    job_id = create_bg_job(
                        workspace,
                        request.user,
                        "export_hubspot_companies",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id

                    ref = None
                    try:
                        ref = export_hubspot_companies_task.run_no_wait(input=payload)
                    except Exception as e:
                        logger.error(
                            f"Exception occurred during export_hubspot_companies_task: {str(e)}",
                            exc_info=True,
                        )
                        ref = None

                    is_running = None
                    if ref:
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        is_running = False
                else:
                    # Unsupported platform
                    logger.error(
                        f"Unsupported platform for company export: {channel.integration.slug}"
                    )
                    is_running = False
                if is_running:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="企業をエクスポートしております。少々お待ちください...",
                            type="success",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Companies being exported. Please give it a few moment...",
                            type="success",
                        )

    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def request_sync_contact(request):
    lang = request.LANGUAGE_CODE
    workspace = get_workspace(request.user)
    select_integration_ids = request.POST.getlist("select_integration_ids", [])

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CONTACT]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_CONTACT
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug

    channels = Channel.objects.filter(
        workspace=workspace, id__in=select_integration_ids
    )
    file_columns = request.POST.getlist("file-column", [])
    contact_file_columns_name = request.POST.getlist("file-column-name", [])
    sanka_properties = request.POST.getlist("sanka-properties", [])
    ignores = request.POST.getlist("ignore", [])
    mapping_custom_fields = {}
    contact_mapping = {}

    header_page = request.POST.get("page", None)
    header_mapping_id = request.POST.get("header_mapping_id", None)
    processed_page = request.POST.get("previous_page", None)
    search_q = request.POST.get("search_q", None)
    processed_search_q = request.POST.get("previous_search_q", None)

    enable_salesforce_filter = request.POST.get("enable-salesforce-filter", None)
    if enable_salesforce_filter == "1":
        enable_salesforce_filter = True
    else:
        enable_salesforce_filter = False

    # Get the multiple filters JSON array
    salesforce_filters = request.POST.get("salesforce-filters", "[]")
    if salesforce_filters == "None" or not salesforce_filters:
        salesforce_filters = "[]"

    print("request.POST", request.POST)

    if search_q == "":
        search_q = None
    if processed_search_q == "":
        processed_search_q = None

    if header_mapping_id:
        header_mapping = TempHeaderList.objects.filter(id=header_mapping_id).first()

        header_list = []
        if header_mapping and header_mapping.header_list:
            if search_q:
                for key, value in header_mapping.header_list.items():
                    for val in value:
                        if (
                            search_q.lower() in val.get("name", "").lower()
                            or search_q.lower() in val.get("name_ja", "").lower()
                        ):
                            header_list.append(val)
            else:
                header_list = header_mapping.header_list.get(header_page, [])

        if processed_search_q:
            processed_header_list = []
            file_columns = request.POST.getlist("file-column", [])
            contact_file_columns_name = request.POST.getlist("file-column-name", [])
            sanka_properties = request.POST.getlist("sanka-properties", [])
            ignores = request.POST.getlist("ignore", [])

            for idx, file_column in enumerate(file_columns):
                for key, value in header_mapping.header_list.items():
                    for val in value:
                        if val["value"] == file_column:
                            val["default"] = sanka_properties[idx]
                            val["skip"] = ignores[idx]
                            break
            header_mapping.save()
        elif processed_page:
            processed_header_list = header_mapping.header_list.get(processed_page, [])

            for idx, file_column in enumerate(file_columns):
                for header in processed_header_list:
                    if header["value"] == file_column:
                        header["default"] = sanka_properties[idx]
                        header["skip"] = ignores[idx]
                        break
            header_mapping.header_list[processed_page] = processed_header_list
            header_mapping.save()

        header_mapping = TempHeaderList.objects.filter(id=header_mapping_id).first()
        if header_mapping and header_mapping.header_list:
            value_list = []
            name_list = []
            default_list = []
            skip_list = []
            for page_key, head_vals in header_mapping.header_list.items():
                for head_val in head_vals:
                    value_list.append(head_val["value"])
                    if lang == "ja":
                        name_list.append(head_val["name_ja"])
                    else:
                        name_list.append(head_val["name"])
                    default_list.append(head_val["default"])
                    skip_list.append(head_val["skip"])

            file_columns = value_list
            contact_file_columns_name = name_list
            sanka_properties = default_list
            ignores = skip_list

    print("file_columns", file_columns)
    print("contact_file_columns_name", contact_file_columns_name)
    print("sanka_properties", sanka_properties)
    print("ignores", ignores)

    for idx, file_column in enumerate(file_columns):
        # check ignore
        contact_mapping[file_column] = sanka_properties[idx]
        if ignores[idx] == "True":
            continue
        mapping_custom_fields[file_column] = sanka_properties[idx]

    if "save_mapping" in request.POST:
        try:
            mapping = ContactsMappingFields.objects.get(
                workspace=workspace, platform__isnull=True, object_type="contact"
            )
            mapping.platform = channels.first().integration.slug
            mapping.save()
        except:
            pass
        mapping, _ = ContactsMappingFields.objects.get_or_create(
            workspace=workspace,
            platform=channels.first().integration.slug,
            object_type="contact",
        )
        data = {}
        for i, field in enumerate(contact_mapping):
            if (
                contact_mapping[field] in ["create_new", "create_new_choice"]
                and ignores[i] != "True"
            ):
                field_type = "text"
                if contact_mapping[field] == "create_new_choice":
                    field_type = "choice"

                _, _ = ContactsNameCustomField.objects.get_or_create(
                    workspace=workspace,
                    name=contact_file_columns_name[i],
                    type=field_type,
                )
                field_data = {
                    "value": contact_file_columns_name[i],
                    "skip": ignores[i],
                }
            elif contact_mapping[field] == "create_new|order" and ignores[i] != "True":
                new_column_value = f"{contact_file_columns_name[i]}"
                new_field, _ = ShopTurboOrdersNameCustomField.objects.get_or_create(
                    workspace=workspace,
                    name=contact_file_columns_name[i],
                    type="text",
                )
                field_data = {"value": new_column_value, "skip": ignores[i]}
            else:
                field_data = {"value": contact_mapping[field], "skip": ignores[i]}
            data[field] = field_data
        mapping.input_data = data
        mapping.save()
    else:
        for channel in channels:
            print("channel", "platform", channel.integration.slug)
            contact_workspace_mapping = None
            contact_workspace_mapping, _ = ContactsMappingFields.objects.get_or_create(
                workspace=workspace,
                platform=channel.integration.slug,
                object_type="contact",
            )
            contact_data = {}
            for i, field in enumerate(contact_mapping):
                if (
                    contact_mapping[field] in ["create_new", "create_new_choice"]
                    and ignores[i] != "True"
                ):
                    field_type = "text"
                    if contact_mapping[field] == "create_new_choice":
                        field_type = "choice"

                    _, _ = ContactsNameCustomField.objects.get_or_create(
                        workspace=workspace,
                        name=contact_file_columns_name[i],
                        type=field_type,
                    )
                    field_data = {
                        "value": contact_file_columns_name[i],
                        "skip": ignores[i],
                    }
                elif (
                    contact_mapping[field] == "create_new|order"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{contact_file_columns_name[i]}"
                    new_field, _ = ShopTurboOrdersNameCustomField.objects.get_or_create(
                        workspace=workspace,
                        name=contact_file_columns_name[i],
                        type="text",
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                else:
                    field_data = {"value": contact_mapping[field], "skip": ignores[i]}
                contact_data[field] = field_data

                if field_data["skip"] == "False" or field_data["skip"] == False:
                    mapping_custom_fields[field] = field_data["value"]
            contact_workspace_mapping.input_data = contact_data
            contact_workspace_mapping.save()
            if channel.integration.slug == "hubspot":
                hubspot_clients = {
                    settings.HUBSPOT_CLIENT_ID: {
                        "client_id": settings.HUBSPOT_CLIENT_ID,
                        "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                    }
                }

                # Auto refresh token
                if channel.api_key in hubspot_clients:
                    client_credentials = hubspot_clients[channel.api_key]
                    token_url = "https://api.hubapi.com/oauth/v1/token"
                    data = {
                        "grant_type": "refresh_token",
                        "client_id": client_credentials["client_id"],
                        "client_secret": client_credentials["client_secret"],
                        "refresh_token": channel.refresh_token,
                    }
                    response = requests.post(token_url, data=data)
                    token_info = response.json()
                    print(token_info)
                    channel.access_token = token_info.get("access_token")
                    channel.save()

            if "import_contacts" in request.POST:
                if lang == "ja":
                    task_name = f"{channel.integration.slug} 連絡先のインポート"
                else:
                    task_name = f"Import {channel.integration.slug} contacts"
                history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="import_contact",
                    name=task_name,
                    channel=channel or None,
                )

                if "set-contact-as-company" in request.POST:
                    set_contact_as_company = True
                else:
                    set_contact_as_company = False

                # Create background job and run appropriate task based on platform
                background_job = create_bg_job(
                    workspace=workspace,
                    user=request.user,
                    name=f"Import {channel.integration.slug} contacts",
                    transfer_history=history,
                )

                try:
                    if channel.integration.slug == "hubspot":
                        payload = ImportHubspotContactsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            history_id=str(history.id),
                            lang=lang,
                            background_job_id=str(background_job.id),
                        )
                        ref = import_hubspot_contacts_task.run_no_wait(input=payload)
                    elif channel.integration.slug == "salesforce":
                        if set_contact_as_company:
                            payload = ImportSalesforceContactsAsCompaniesPayload(
                                user_id=str(request.user.id),
                                workspace_id=str(workspace.id),
                                channel_id=str(channel.id),
                                history_id=str(history.id),
                                lang=lang,
                                salesforce_checkpoint=0,
                                background_job_id=str(background_job.id),
                            )
                            ref = import_salesforce_contacts_as_companies_task.run_no_wait(
                                input=payload
                            )
                        else:
                            payload = ImportSalesforceContactsPayload(
                                user_id=str(request.user.id),
                                workspace_id=str(workspace.id),
                                channel_id=str(channel.id),
                                mapping_custom_fields=json.dumps(mapping_custom_fields),
                                history_id=str(history.id),
                                lang=lang,
                                salesforce_checkpoint=0,
                                enable_salesforce_filter=enable_salesforce_filter,
                                salesforce_filters=json.dumps(salesforce_filters),
                                background_job_id=str(background_job.id),
                            )
                            ref = import_salesforce_contacts_task.run_no_wait(
                                input=payload
                            )
                    elif channel.integration.slug == "shopify":
                        payload = ImportShopifyContactsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields),
                            history_id=str(history.id),
                            lang=lang,
                            background_job_id=str(background_job.id),
                        )
                        ref = import_shopify_contacts_task.run_no_wait(input=payload)
                    elif channel.integration.slug == "freee":
                        payload = ImportFreeeContactsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            history_id=str(history.id),
                            lang=lang,
                            background_job_id=str(background_job.id),
                        )
                        ref = import_freee_contacts_task.run_no_wait(input=payload)
                    elif channel.integration.slug == "line":
                        payload = ImportLineContactsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            history_id=str(history.id),
                            lang=lang,
                            background_job_id=str(background_job.id),
                        )
                        ref = import_line_contacts_task.run_no_wait(input=payload)
                    else:
                        raise ValueError(
                            f"Unsupported platform: {channel.integration.slug}"
                        )

                    add_hatchet_run_id(background_job.id, ref.run_id)
                    is_running = True
                except Exception as e:
                    logger.error(f"Error starting import task: {str(e)}")
                    is_running = False

                if is_running:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="連絡先をインポートしております。少々お待ちください...",
                            type="success",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Contacts being imported. Please give it a few moment...",
                            type="success",
                        )

            else:
                if lang == "ja":
                    task_name = f"{channel.integration.slug} 連絡先のエクスポート"
                else:
                    task_name = f"Export {channel.integration.slug} contacts"
                history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="export_contact",
                    name=task_name,
                    channel=channel or None,
                )

                if "set-contact-as-company" in request.POST:
                    set_contact_as_company = True
                else:
                    set_contact_as_company = False

                # Get contact IDs for export
                contact_ids = request.POST.get("export_contacts", "")
                if contact_ids == "None":
                    contact_ids = ""

                # Get HubSpot list data if applicable
                hubspot_list_data = ""
                if (
                    channel.integration.slug == "hubspot"
                    and "set-hubspot-list" in request.POST
                ):
                    hubspot_list_data = request.POST.get("hubspot-list-data", "0")

                # Create background job and run appropriate task based on platform
                background_job = create_bg_job(
                    workspace=workspace,
                    user=request.user,
                    name=f"Export {channel.integration.slug} contacts",
                    transfer_history=history,
                )

                try:
                    if channel.integration.slug == "hubspot":
                        if set_contact_as_company:
                            payload = ExportHubspotContactsAsCompaniesPayload(
                                user_id=str(request.user.id),
                                workspace_id=str(workspace.id),
                                channel_id=str(channel.id),
                                history_id=str(history.id),
                                lang=lang,
                                contact_ids=contact_ids,
                                hubspot_list_data=hubspot_list_data,
                                background_job_id=str(background_job.id),
                            )
                            ref = export_hubspot_contacts_as_companies_task.run_no_wait(
                                input=payload
                            )
                        else:
                            payload = ExportHubspotContactsPayload(
                                user_id=str(request.user.id),
                                workspace_id=str(workspace.id),
                                channel_id=str(channel.id),
                                mapping_custom_fields=json.dumps(mapping_custom_fields),
                                history_id=str(history.id),
                                lang=lang,
                                contact_ids=contact_ids,
                                hubspot_list_data=hubspot_list_data,
                                background_job_id=str(background_job.id),
                            )
                            ref = export_hubspot_contacts_task.run_no_wait(
                                input=payload
                            )
                    elif channel.integration.slug == "shopify":
                        payload = ExportShopifyContactsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            mapping_custom_fields=json.dumps(mapping_custom_fields),
                            history_id=str(history.id),
                            lang=lang,
                            contact_ids=contact_ids,
                            background_job_id=str(background_job.id),
                        )
                        ref = export_shopify_contacts_task.run_no_wait(input=payload)
                    elif channel.integration.slug == "freee":
                        payload = ExportFreeeContactsPayload(
                            user_id=str(request.user.id),
                            workspace_id=str(workspace.id),
                            channel_id=str(channel.id),
                            history_id=str(history.id),
                            lang=lang,
                            contact_ids=contact_ids,
                            background_job_id=str(background_job.id),
                        )
                        ref = export_freee_contacts_task.run_no_wait(input=payload)
                    else:
                        raise ValueError(
                            f"Unsupported platform: {channel.integration.slug}"
                        )

                    add_hatchet_run_id(background_job.id, ref.run_id)
                    is_running = True
                except Exception as e:
                    logger.error(f"Error starting export task: {str(e)}")
                    is_running = False

                if is_running:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="連絡先をエクスポートしております。少々お待ちください...",
                            type="success",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Contacts being exported. Please give it a few moment...",
                            type="success",
                        )

    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@csrf_exempt
def business_card_reader(request):
    if request.method == "POST":
        data = {}
        upload_file = request.FILES.get("upload-file", None)
        if upload_file:
            file_content = upload_file.read()

            data = img_recognizer(file_content, upload_file.name.split(".")[-1])

            if data:
                # Rename uploaded file
                filename = upload_file.name.split(".")[0]
                upload_file.name = (
                    upload_file.name.split(".")[0]
                    + "__"
                    + str(uuid.uuid4())
                    + "."
                    + upload_file.name.split(".")[-1]
                )
                contact_file = ContatcsFile.objects.create(
                    name=filename, bussiness_card=upload_file
                )
                data["contact_file_id"] = str(contact_file.id)

    return JsonResponse(data)


def csv_header_extractor(request):
    workspace = get_workspace(request.user)
    if request.method == "POST":
        csv = request.FILES.get("csv_upload", False)
        upload_type = request.POST.get("upload_type", None)
        mapping_type = request.POST.get("mapping_type", None)
        try:
            if not csv:
                workspace = get_workspace(request.user)
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Failed to retreive the CSV file.",
                    type="error",
                )
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CONTACT]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_CONTACT
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))

            df = read_csv(csv)

            header_list = df.columns.tolist()
            contacts_columns = [
                "contact_id",
                "first_name",
                "last_name",
                "email",
                "phone_number",
                "image_url",
            ]
            contactsnamecustomfields = (
                ContactsNameCustomField.objects.filter(workspace=workspace)
                .exclude(type__in=["image", "user", "formula"])
                .order_by("order")
                .values_list("id", flat=True)
            )
            contactsnamecustomfield = [
                str(contactsnamecustomfield)
                for contactsnamecustomfield in contactsnamecustomfields
            ]
            contacts_columns.extend(contactsnamecustomfield)

            company_columns = [
                "company_id",
                "name",
                "email",
                "url",
                "address",
                "phone_number",
            ]
            companynamecustomfields = (
                CompanyNameCustomField.objects.filter(workspace=workspace)
                .exclude(type__in=["image", "user", "formula"])
                .order_by("order")
                .values_list("id", flat=True)
            )
            companynamecustomfield = [
                str(companynamecustomfield)
                for companynamecustomfield in companynamecustomfields
            ]
            company_columns.extend(companynamecustomfield)

            deal_columns = ["deal_id", "name", "contact", "company"]
            deal_name_customfields = (
                DealsNameCustomField.objects.filter(workspace=workspace)
                .exclude(type__in=["image", "user", "formula"])
                .order_by("order")
                .values_list("id", flat=True)
            )
            deal_name_customfield = [
                str(deal_name_customfield)
                for deal_name_customfield in deal_name_customfields
            ]
            deal_columns.extend(deal_name_customfield)

            context = {
                "header_list": header_list,
                "contacts_columns": contacts_columns,
                "company_columns": company_columns,
                "deal_columns": deal_columns,
                "upload_type": upload_type,
            }
            return render(
                request, "data/contacts/csv-contacts-import-mapping.html", context
            )

        except:
            if mapping_type == "deal":
                mapping_type = "customer_case"
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Wrong format uploaded, Please use this format",
                message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                cta_text="Template",
                cta_text_ja="テンプレート",
                cta_target=TEMPLATE_FILE[mapping_type][request.LANGUAGE_CODE],
                type="error",
            )

    return render(request, "data/common/csv-table-mapping-error.html")


def sync_header_extractor(request):
    header_list = []  # Initialize header_list
    workspace = get_workspace(request.user)
    select_integration_ids = request.POST.get("select_integration_ids")
    channel = Channel.objects.get(id=select_integration_ids)
    object_type = "contact"

    if request.method == "POST":
        if channel.integration.slug == "shopify":
            header_list = [
                {
                    "name": "first_name",
                    "value": "first_name",
                    "skip": False,
                    "default": "name",
                },
                {
                    "name": "last_name",
                    "value": "last_name",
                    "skip": False,
                    "default": "last_name",
                },
                {"value": "email", "default": "email", "skip": False, "name": "email"},
                {
                    "value": "phone",
                    "name": "phone",
                    "skip": False,
                    "default": "phone_number",
                },
                {
                    "value": "address1",
                    "name": "address",
                    "skip": False,
                    "default": "name",
                },
                {
                    "value": "address2",
                    "name": "apartment",
                    "skip": False,
                    "default": "name",
                },
                {
                    "value": "country",
                    "name": "country",
                    "skip": False,
                    "default": "name",
                },
                {
                    "value": "province",
                    "name": "state",
                    "skip": False,
                    "default": "name",
                },
                {"name": "city", "value": "city", "skip": False, "default": "name"},
                {
                    "name": "postal_code",
                    "value": "zip",
                    "skip": False,
                    "default": "name",
                },
            ]
        elif channel.integration.slug == "hubspot":
            header_list = []
            access_token = channel.access_token
            object_type = request.POST.get("object_type")

            try:
                token_url = "https://api.hubapi.com/oauth/v1/token"
                data = {
                    "grant_type": "refresh_token",
                    "client_id": settings.HUBSPOT_CLIENT_ID,
                    "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                    "refresh_token": channel.refresh_token,
                }
                response = requests.post(token_url, data=data)
                token_info = response.json()
                channel.access_token = token_info.get("access_token")
                channel.save()

                access_token = channel.access_token
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                hubspot_defined_inclusions = [
                    "hubspot_owner_id",
                    "name",
                    "firstname",
                    "lastname",
                    "email",
                    "phone",
                    "address",
                    "address2",
                    "contact_address",
                    "company_address",
                    "city",
                    "company",
                    "country",
                    "fax",
                    "industry",
                    "jobtitle",
                    "lastmodifieddate",
                    "mobilephone",
                    "state",
                    "website",
                    "zip",
                    "total_revenue",
                    "work_email",
                    "annualrevenue",
                ]

                label_to_ja = {
                    "Street Address": "住所",
                    "Street Address 2": "住所2",
                    "Annual Revenue": "年間収益",
                    "City": "市区町村",
                    "Contact Owner": "連絡先所有者",
                    "Contact owner": "連絡先所有者",  # for both cases
                    "Company Onwer": "会社所有者",
                    "Company owner": "会社所有者",  # for both cases
                    "Company Name": "会社名",
                    "Company name": "会社名",  # for both cases
                    "Country/Region": "国/地域",
                    "Email": "メールアドレス",
                    "Fax Number": "FAX番号",
                    "First Name": "名",
                    "Industry": "業種",
                    "Job Title": "役職",
                    "Last Modified Date": "最終更新日",
                    "Last Name": "姓",
                    "Mobile Phone Number": "携帯電話番号",
                    "Phone Number": "電話番号",
                    "State/Region": "都道府県",
                    "Total Revenue": "総収益",
                    "Website URL": "ウェブサイトURL",
                    "Work email": "勤務先メールアドレス",
                    "Postal Code": "郵便番号",
                    "Company Address": "会社住所",
                }

                if object_type == "company":
                    company_properties_url = (
                        "https://api.hubapi.com/properties/v1/companies/properties"
                    )
                    company_properties_response = requests.get(
                        company_properties_url, headers=headers
                    )
                    company_properties_response.raise_for_status()

                    company_properties = company_properties_response.json()
                    custom_company_properties = [
                        prop
                        for prop in company_properties
                        if not prop.get("hubspotDefined", True)
                        or prop.get("name") in hubspot_defined_inclusions
                    ]
                    # Sort so that hubspotDefined=True comes first
                    custom_company_properties = sorted(
                        custom_company_properties,
                        key=lambda x: not x.get("hubspotDefined", False),
                    )
                    for property in custom_company_properties:
                        if property.get("name", "") not in [
                            "sanka_id",
                            "platform",
                            "platform_id",
                            "email",
                        ]:
                            name = property.get("name", "")
                            label = property.get("label", name)
                            label_ja = label_to_ja.get(label, label)
                            header_list.append(
                                {
                                    "value": name,
                                    "name": f"{label} ({name})",
                                    "name_ja": f"{label_ja} ({name})",
                                    "skip": False,
                                    "default": "name",
                                }
                            )
                else:
                    contact_properties_url = (
                        "https://api.hubapi.com/properties/v1/contacts/properties"
                    )
                    contact_properties_response = requests.get(
                        contact_properties_url, headers=headers
                    )
                    contact_properties_response.raise_for_status()

                    contact_properties = contact_properties_response.json()
                    custom_contact_properties = [
                        prop
                        for prop in contact_properties
                        if not prop.get("hubspotDefined", True)
                        or prop.get("name") in hubspot_defined_inclusions
                    ]
                    # Sort so that hubspotDefined=True comes first
                    custom_contact_properties = sorted(
                        custom_contact_properties,
                        key=lambda x: not x.get("hubspotDefined", False),
                    )
                    for property in custom_contact_properties:
                        if property.get("name", "") not in [
                            "sanka_id",
                            "platform",
                            "platform_id",
                        ]:
                            name = property.get("name", "")
                            label = property.get("label", name)
                            label_ja = label_to_ja.get(label, label)
                            header_list.append(
                                {
                                    "value": name,
                                    "name": f"{label} ({name})",
                                    "name_ja": f"{label_ja} ({name})",
                                    "skip": False,
                                    "default": "name",
                                }
                            )

            except:
                pass

        elif channel.integration.slug == "salesforce":
            header_list = []
            mapping_fields = None
            object_type = request.POST.get("object_type", "contact")
            if object_type == "contact":
                mapping_fields = get_contact_mapping_fields(str(channel.id))
            elif object_type == "company":
                mapping_fields = get_company_mapping_fields(str(channel.id))

            if mapping_fields:
                # Convert contact_mapping_fields to header_list format
                for field in mapping_fields:
                    field_name = field.get("name", "")
                    field_label = field.get("label", field_name)

                    # Skip system fields and certain lookup fields
                    if field_name not in ["sanka_id", "platform", "platform_id"]:
                        default_value = ""

                        # Set default mappings for common fields
                        if (
                            field_name == "FirstName"
                            or field_name == "Name"
                            or field_name == "Name|lead"
                        ):
                            default_value = "name"
                        elif field_name == "LastName":
                            default_value = "last_name"
                        elif field_name == "Email" or field_name == "Email|lead":
                            default_value = "email"
                        elif (
                            field_name == "Phone"
                            or field_name == "MobilePhone"
                            or field_name == "Phone|lead"
                        ):
                            default_value = "phone_number"

                        header_list.append(
                            {
                                "value": field_name,
                                "name": field_label,
                                "name_ja": field_label,  # Using same label for Japanese
                                "skip": False,
                                "default": default_value,
                            }
                        )

        try:
            if not object_type:
                object_type = "contact"

            if object_type == "contact":
                mapping = ContactsMappingFields.objects.get(
                    workspace=workspace, platform__isnull=True, object_type="contact"
                )
            else:
                mapping = ContactsMappingFields.objects.get(
                    workspace=workspace, platform__isnull=True, object_type="company"
                )
            mapping.platform = channel.integration.slug
            mapping.save()
        except:
            pass

        if not object_type:
            object_type = "contact"

        mapping = ContactsMappingFields.objects.filter(
            workspace=workspace,
            platform=channel.integration.slug,
            object_type=object_type,
            input_data__isnull=False,
        ).last()
        if not mapping:
            mapping, _ = ContactsMappingFields.objects.get_or_create(
                workspace=workspace,
                platform=channel.integration.slug,
                object_type=object_type,
            )
        if mapping.input_data:
            for item in header_list:
                if item["value"] in mapping.input_data:
                    item["default"] = mapping.input_data[item["value"]]["value"]
                    item["skip"] = mapping.input_data[item["value"]]["skip"]
        contacts_columns = []
        if channel.integration.slug == "shopify":
            contactsnamecustomfield = (
                ContactsNameCustomField.objects.filter(workspace=workspace)
                .exclude(type__in=["image", "user", "formula"])
                .values_list("name", flat=True)
            )
        else:
            if request.POST.get("object_type") == "company":
                exclude = [
                    "id",
                    "company_id",
                    "image_file",
                    "created_at",
                    "updated_at",
                    "status",
                ]
                contacts_columns = [
                    field.name
                    for field in Company._meta.get_fields()
                    if not field.is_relation
                ]
                contacts_columns = [
                    field for field in contacts_columns if field not in exclude
                ]
                contactsnamecustomfield = (
                    CompanyNameCustomField.objects.filter(workspace=workspace)
                    .exclude(type__in=["image", "user", "formula"])
                    .values_list("name", flat=True)
                )
            else:
                exclude = [
                    "id",
                    "contact_id",
                    "image_file",
                    "created_at",
                    "updated_at",
                    "status",
                ]
                contacts_columns = [
                    field.name
                    for field in Contact._meta.get_fields()
                    if not field.is_relation
                ]
                contacts_columns = [
                    field for field in contacts_columns if field not in exclude
                ]
                contactsnamecustomfield = (
                    ContactsNameCustomField.objects.filter(workspace=workspace)
                    .exclude(type__in=["image", "user", "formula"])
                    .values_list("name", flat=True)
                )

        contacts_columns.extend(contactsnamecustomfield)

        if len(header_list) > 50:
            print("len:", len(header_list))
            for hl in header_list:
                if hl["default"] == "":
                    hl["default"] = "create_new"
            temp_header_list = {}
            for i in range(0, len(header_list), 25):
                temp_header_list[f"{i // 25 + 1}"] = header_list[i : i + 25]

            print("Header List", temp_header_list)

            header_mapping, _ = TempHeaderList.objects.get_or_create(
                channel=channel, object_type=object_type
            )
            header_mapping.header_list = temp_header_list
            header_mapping.save()

            context = {
                "contacts_columns": contacts_columns,
                "platform": channel.integration.slug,
                "object_type": object_type,
                "header_mapping_id": header_mapping.id,
                "header_page": 1,
                "header_length": len(header_list),
                "channel_id": channel.id,
                "integration_type": channel.integration.slug,
            }
            return render(
                request,
                "data/contacts/sync-contacts-import-mapping-pagination.html",
                context,
            )
        else:
            if channel.integration.slug == "salesforce":
                temp_header_list = {}
                for i in range(0, len(header_list), 25):
                    temp_header_list[f"{i // 25 + 1}"] = header_list[i : i + 25]

                header_mapping, _ = TempHeaderList.objects.get_or_create(
                    channel=channel, object_type=object_type
                )
                header_mapping.header_list = temp_header_list
                header_mapping.save()

        context = {
            "header_list": header_list,
            "contacts_columns": contacts_columns,
            "platform": channel.integration.slug,
            "object_type": object_type,
            "channel_id": channel.id,
            "integration_type": channel.integration.slug,
        }
        return render(
            request, "data/contacts/sync-contacts-import-mapping.html", context
        )

    return HttpResponse(200)


def get_salesforce_field(request):
    q = request.GET.get("q", "")
    page = request.GET.get("page", 1)
    lang = request.LANGUAGE_CODE

    channel_id = request.GET.get("channel_id", None)
    object_type = request.GET.get("object_type", "contact")
    context = {"results": [], "pagination": {"more": False}}

    try:
        channel = Channel.objects.get(id=channel_id)
    except Channel.DoesNotExist:
        return JsonResponse(context)

    header_mapping = None
    try:
        header_mapping = TempHeaderList.objects.get(
            channel=channel, object_type=object_type
        )
    except:
        return JsonResponse(context)

    print("Header Mapping:", header_mapping.header_list)

    if header_mapping and header_mapping.header_list:
        mapping_data = header_mapping.header_list

        if page:
            page = f"{page}"
            next_page = f"{int(page) + 1}"
        else:
            page = "1"
            next_page = "2"

        results = []

        # If query is provided, search across all mapping data
        if q:
            all_filtered_items = []
            print(f'Searching for query: "{q}"')
            print(f"Available pages in mapping_data: {list(mapping_data.keys())}")

            # Search through all pages in mapping_data
            for page_key, page_items in mapping_data.items():
                print(
                    f"Processing page: {page_key}, items type: {type(page_items)}, count: {len(page_items) if isinstance(page_items, list) else 'N/A'}"
                )
                if isinstance(page_items, list):
                    for item in page_items:
                        name = item.get("name", "")
                        name_ja = item.get("name_ja", "")

                        # Check if query matches value, name, or name_ja (case-insensitive)
                        if q.lower() in name.lower() or q.lower() in name_ja.lower():
                            print(f"Found match: {name} | {name_ja}")
                            all_filtered_items.append(item)

            print(f"Total filtered items: {len(all_filtered_items)}")

            # Calculate pagination for filtered results
            items_per_page = 20  # Adjust as needed
            start_index = (int(page) - 1) * items_per_page
            end_index = start_index + items_per_page
            page_data = all_filtered_items[start_index:end_index]

            # Check if there are more pages
            total_filtered = len(all_filtered_items)
            is_next_page = end_index < total_filtered

            print(
                f"Page {page}: showing {len(page_data)} items, has more: {is_next_page}"
            )
        else:
            # No query - use original pagination logic
            page_data = mapping_data[page] if page in mapping_data else []
            is_next_page = next_page in mapping_data

        for item in page_data:
            data = {
                "id": item["value"],
                "text": item["name_ja"] if lang == "ja" else item["name"],
            }
            results.append(data)

        context["pagination"]["more"] = is_next_page
        context["results"] = results

    return JsonResponse(context)


def sync_header_extractor_page(request):
    header_list = []  # Initialize header_list
    print("[SYNC_HEADER_EXTRACTOR_PAGE]")
    print(request.POST)
    header_mapping_id = request.POST.get("header_mapping_id", None)
    header_page = request.POST.get("page", None)
    contacts_columns = request.POST.get("contacts_columns", None)
    if contacts_columns:
        try:
            contacts_columns = ast.literal_eval(contacts_columns)
        except:
            pass
    platform = request.POST.get("platform", None)
    processed_page = request.POST.get("previous_page", None)
    search_q = request.POST.get("search_q", None)
    processed_search_q = request.POST.get("previous_search_q", None)
    source_object_type = request.POST.get("source_object_type", "contact")
    if not header_page:
        header_page = processed_page

    if search_q == "":
        search_q = None
    if processed_search_q == "":
        processed_search_q = None

    if request.method == "POST":
        if header_mapping_id:
            header_mapping = TempHeaderList.objects.filter(id=header_mapping_id).first()

            header_list = []
            if header_mapping and header_mapping.header_list:
                if search_q:
                    for key, value in header_mapping.header_list.items():
                        for val in value:
                            if (
                                search_q.lower() in val.get("name", "").lower()
                                or search_q.lower() in val.get("name_ja", "").lower()
                            ):
                                header_list.append(val)
                else:
                    header_list = header_mapping.header_list.get(header_page, [])

            if processed_search_q:
                processed_header_list = []
                file_columns = request.POST.getlist("file-column", [])
                contact_file_columns_name = request.POST.getlist("file-column-name", [])
                sanka_properties = request.POST.getlist("sanka-properties", [])
                ignores = request.POST.getlist("ignore", [])

                for idx, file_column in enumerate(file_columns):
                    for key, value in header_mapping.header_list.items():
                        for val in value:
                            if val["value"] == file_column:
                                val["default"] = sanka_properties[idx]
                                val["skip"] = ignores[idx]
                                break
                header_mapping.save()

            elif processed_page:
                processed_header_list = header_mapping.header_list.get(
                    processed_page, []
                )
                file_columns = request.POST.getlist("file-column", [])
                contact_file_columns_name = request.POST.getlist("file-column-name", [])
                sanka_properties = request.POST.getlist("sanka-properties", [])
                ignores = request.POST.getlist("ignore", [])

                for idx, file_column in enumerate(file_columns):
                    for header in processed_header_list:
                        if header["value"] == file_column:
                            header["default"] = sanka_properties[idx]
                            header["skip"] = ignores[idx]
                            break
                header_mapping.header_list[processed_page] = processed_header_list
                header_mapping.save()
        context = {
            "header_list": header_list,
            "contacts_columns": contacts_columns,
            "platform": platform,
            "object_type": source_object_type,
            "current_page": header_page,
            "search_q": search_q,
        }
        print("[SYNC_HEADER_EXTRACTOR_PAGE] context:", context)

        return render(
            request, "data/contacts/sync-contacts-import-mapping-page.html", context
        )
    return HttpResponse(200)


def table_mapping_url(request):
    workspace = get_workspace(request.user)
    header_list = request.POST.get("header_list")
    contacts_columns = request.POST.get("contacts_columns")
    company_columns = request.POST.get("company_columns")
    deal_columns = request.POST.get("deal_columns")
    import_data = request.POST.getlist("import-data")

    try:
        context = {
            "header_list": ast.literal_eval(header_list) if header_list else [],
            "contacts_columns": ast.literal_eval(contacts_columns)
            if contacts_columns
            else [],
            "company_columns": ast.literal_eval(company_columns)
            if company_columns
            else [],
            "deal_columns": ast.literal_eval(deal_columns) if deal_columns else [],
            "import_data": import_data,
        }
        mapping_storage = ImportMappingFields.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_CONTACT
        ).first()
        if mapping_storage:
            context["mapping_storage_contact"] = mapping_storage
        mapping_storage = ImportMappingFields.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_COMPANY
        ).first()
        if mapping_storage:
            context["mapping_storage_company"] = mapping_storage
        mapping_storage = ImportMappingFields.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_CASE
        ).first()
        if mapping_storage:
            context["mapping_storage_case"] = mapping_storage

        return render(request, "data/contacts/csv-table-mapping.html", context)
    except Exception as e:
        print(f"Error in table_mapping_url: {e}")
        mapping_type = "contacts"  # Default mapping type

        # Check import_data safely
        if import_data and len(import_data) > 0:
            if "company" in import_data[0]:
                mapping_type = "company"
            elif "contact" in import_data[0]:
                mapping_type = "contacts"

        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="Wrong format uploaded, Please use this format",
            message_ja="アップロードされた形式が間違っています。この形式を使用してください",
            cta_text="Template",
            cta_text_ja="テンプレート",
            cta_target=TEMPLATE_FILE.get(mapping_type, {}).get(
                request.LANGUAGE_CODE, ""
            ),
            type="error",
        )

    return render(request, "data/common/csv-table-mapping-error.html")


def sync_table_mapping_url(request):
    header_list = request.POST.get("header_list")
    contacts_columns = request.POST.get("contacts_columns")
    orders_columns = request.POST.get("orders_columns")
    mapping_type = request.POST.get("mapping_type")
    platform = request.POST.get("platform")
    if mapping_type == "contact":
        mapping_type = "Shopify"
    else:
        mapping_type = "Next Engine"

    try:
        contacts_columns_evaluated = ast.literal_eval(contacts_columns)
    except SyntaxError as e:
        print(f"Error parsing contact columns: {e}")
        contacts_columns_evaluated = {}
    try:
        orders_columns_evaluated = ast.literal_eval(orders_columns)
    except SyntaxError as e:
        print(f"Error parsing order columns: {e}")
        orders_columns_evaluated = {}

    context = {
        "header_list": ast.literal_eval(header_list),
        "contacts_columns": contacts_columns_evaluated,
        "orders_columns": orders_columns_evaluated,
        "mapping_type": mapping_type,
        "platform": platform,
    }
    return render(request, "data/contacts/contact-table-mapping.html", context)


def img_recognizer(file_content, filename_ext):
    trial = []
    data = {}

    def img_recognizer_recursive():
        data = {}
        img_arr = np.frombuffer(file_content, np.uint8)
        input_image = file_content
        if len(trial) == 1:
            img_gray = cv2.imdecode(img_arr, cv2.IMREAD_GRAYSCALE)
            _, buffer = cv2.imencode("." + filename_ext, img_gray)
            input_image = buffer
        elif len(trial) == 2:
            img_gray = cv2.imdecode(img_arr, cv2.IMREAD_GRAYSCALE)
            _, thresh = cv2.threshold(
                img_gray, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU
            )
            _, buffer = cv2.imencode("." + filename_ext, thresh)
            input_image = buffer

        base64_image_ext = filename_ext
        base64_image = base64.b64encode(input_image).decode("utf-8")
        url = f"data:image/{base64_image_ext};base64,{base64_image}"

        data = OpenaiAPI().imgtotext_business_card_parser(url)

        if not data or all(value == "Null" for value in data.values()):
            trial.append("pass")
            data = img_recognizer_recursive()

        return data

    data = img_recognizer_recursive()
    return data


@login_or_hubspot_required
def create_contacts(request):
    target = "contacts"
    workspace = get_workspace(request.user)
    source_url = request.POST.get("source_url")

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CONTACT]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_CONTACT
        )
        .order_by("order", "created_at")
        .first()
    )
    module_slug = None
    if module:
        module_slug = module.slug

    lang = request.LANGUAGE_CODE
    if "single-entry" in request.POST:
        name = request.POST.get("contact_name", None)
        last_name = request.POST.get("contact_last_name", None)
        email = request.POST.get("email", None)
        phone_header = request.POST.get("phone_header", None)
        phone_number = request.POST.get("phone_number", None)
        urls = request.POST.getlist("urls", None)
        company_name = request.POST.getlist("company_name", None)
        contact_file_id = request.POST.get("contact_file_id", None)
        owner = request.POST.get("owner", None)

        if phone_number:
            if phone_header:
                phone_number = phone_header + "-" + phone_number
            else:
                phone_number = phone_number

        if not has_quota(workspace, CONTACT_USAGE_CATEGORY):
            msg = "Contact could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space."
            if lang == "ja":
                msg = "制限を超えたため、連絡先を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。"
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message=msg,
                type="error",
            )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        contact = Contact.objects.create(
            name=name,
            last_name=last_name,
            email=email,
            phone_number=phone_number,
            workspace=workspace,
            status="active",
        )

        if company_name:
            for company_id in company_name:
                if is_valid_uuid(company_id):
                    company = Company.objects.get(id=company_id)
                    contact.companies.add(company)
                else:
                    company__name = company_id
                    try:
                        company = Company.objects.get(
                            workspace=workspace, name=company__name
                        )
                    except Company.DoesNotExist:
                        if has_quota(workspace, COMPANY_USAGE_CATEGORY):
                            company = Company.objects.create(
                                workspace=workspace, name=company__name
                            )
                            contact.companies.add(company)
                        else:
                            msg = "Company could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space."
                            if lang == "ja":
                                msg = "制限を超えたため、会社を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。"
                            Notification.objects.create(
                                workspace=get_workspace(request.user),
                                user=request.user,
                                message=msg,
                                type="error",
                            )

        if contact_file_id:
            contact_file = ContatcsFile.objects.get(id=contact_file_id)
            contact_file.contact = contact
            contact_file.save()

        # File [Image]
        files = request.FILES.getlist("fileUpload")
        for file in files:
            file.name = str(uuid.uuid4()) + "." + file.name.split(".")[-1]
            contact.image_file = file
            break

        for url in urls:
            if not url:
                continue

            SocialAccountLink.objects.get_or_create(contact=contact, platform_url=url)

        assign_object_owner(contact, owner, request, TYPE_OBJECT_CONTACT)
        # contact.property_set_id = request.POST.get('set_id')
        contact.save(
            log_data={"user": request.user, "status": "create", "workspace": workspace}
        )

        save_custom_property(request, contact)
        save_association_label(request, contact, TYPE_OBJECT_CONTACT)

        # association related
        if request.POST.get("type_association", "") == "create-association":
            if "object_type" in request.POST:
                object_type = request.POST.get("object_type")
            source = request.POST.get("source")
            filter_conditions = Q(workspace=workspace)
            if request.POST.get("custom_object_id", None):
                custom_object_id = request.POST.get("custom_object_id")
                custom_object = CustomObject.objects.get(id=custom_object_id)
                module_object_slug = custom_object.slug
                filter_conditions &= Q(object_values__contains=str(custom_object_id))
            else:
                module_object_slug = OBJECT_TYPE_TO_SLUG[source]
                filter_conditions &= Q(object_values__contains=source)

            module = (
                Module.objects.filter(filter_conditions)
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
            if source == TYPE_OBJECT_COMPANY:
                object_id = request.POST.get("source_object_id")
                company = Company.objects.filter(id=object_id).first()
                if object_type == TYPE_OBJECT_CONTACT:
                    contact.companies.add(company)
                contact.save()

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?target={source}&id={company.id}"
                )
            if source == TYPE_OBJECT_CUSTOM_OBJECT:
                object_id = request.POST.get("source_object_id")
                obj = get_object_or_404(CustomObjectPropertyRow, id=object_id)
                contact.custom_object_relation.add(obj)

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={obj.id}"
                )

            if source == TYPE_OBJECT_SUBSCRIPTION:
                object_id = request.POST.get("source_object_id")
                subscription = ShopTurboSubscriptions.objects.get(id=object_id)
                if subscription:
                    subscription.contact = contact
                    subscription.company = None
                    subscription.save()

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?target={source}&id={subscription.id}"
                )

            if source == TYPE_OBJECT_ORDER:
                object_id = request.POST.get("source_object_id")
                order = ShopTurboOrders.objects.get(id=object_id)
                order.contacts.add(contact)

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?target={source}&id={order.id}"
                )

        if request.POST.get("addon-order", None):
            response = HttpResponse(200)
            response["HX-Trigger"] = json.dumps(
                {
                    "showOrderDrawerSingleEntry": {"event_contact_id": str(contact.id)},
                }
            )
            return response

        if source_url:
            source_url = update_query_params_url(
                source_url, {"target": target, "id": [str(contact.id)]}, overwrite=True
            )
            return redirect(source_url)
        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + f"?contact_id={contact.id}"
            )
        return redirect(reverse("main", host="app"))

    elif "upload-entry" in request.POST:
        csv = request.FILES.get("csv_upload", False)
        if not csv:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Failed to retreive the CSV file.",
                type="error",
            )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        import_data = request.POST.get("import-data")
        file_columns = request.POST.getlist("file-column")
        import_as = request.POST.getlist("import-as")
        sanka_properties_contacts = request.POST.getlist("sanka-properties-contacts")
        sanka_properties_company = request.POST.getlist("sanka-properties-company")
        ignores = request.POST.getlist("ignore")

        import_method_contact = request.POST.get("import-method-contact")
        import_method_company = request.POST.get("import-method-company")
        contact_key_field = request.POST.getlist("contact-key-field")
        company_key_field = request.POST.getlist("company-key-field")

        try:
            if "contact" in import_data or "contact_and_company":
                mapping_storage, _ = ImportMappingFields.objects.get_or_create(
                    workspace=workspace, object_type=TYPE_OBJECT_CONTACT
                )
                mapping = {
                    sanka_prop: file_col
                    for sanka_prop, file_col in zip(
                        sanka_properties_contacts, file_columns
                    )
                }
                mapping_storage.input_pair = mapping
                mapping_storage.save()

            if "company" in import_data or "contact_and_company":
                mapping_storage, _ = ImportMappingFields.objects.get_or_create(
                    workspace=workspace, object_type=TYPE_OBJECT_COMPANY
                )
                mapping = {
                    sanka_prop: file_col
                    for sanka_prop, file_col in zip(
                        sanka_properties_company, file_columns
                    )
                }
                mapping_storage.input_pair = mapping
                mapping_storage.save()

            df = read_csv(csv)

            for _, row in df.iterrows():
                create_json_contacts = {}
                create_json_contacts_custom_field = {}
                create_json_company = {}
                create_json_company_custom_field = {}
                contact_key_field_json = {}
                company_key_field_json = {}
                contact = None
                company = None

                for idx, file_column in enumerate(file_columns):
                    # check ignore
                    if ignores[idx] == "True":
                        continue

                    if row[file_column] == "nan" or row[file_column] == None:
                        continue

                    # Checking Table Mapping
                    if sanka_properties_contacts[idx] == "first_name":
                        sanka_properties_contacts[idx] = "name"

                    if "contact" in import_as[idx]:
                        if is_valid_uuid(sanka_properties_contacts[idx]):
                            custom_field = ContactsNameCustomField.objects.filter(
                                id=sanka_properties_contacts[idx], workspace=workspace
                            ).first()
                            create_json_contacts_custom_field[custom_field.name] = row[
                                file_column
                            ]
                        else:
                            create_json_contacts[sanka_properties_contacts[idx]] = row[
                                file_column
                            ]

                    elif "company" in import_as[idx]:
                        if is_valid_uuid(sanka_properties_contacts[idx]):
                            custom_field = CompanyNameCustomField.objects.filter(
                                id=sanka_properties_company[idx], workspace=workspace
                            ).first()
                            create_json_company_custom_field[custom_field.name] = row[
                                file_column
                            ]
                        else:
                            create_json_company[sanka_properties_company[idx]] = row[
                                file_column
                            ]

                    if contact_key_field:
                        for contact_key in contact_key_field:
                            if contact_key in create_json_contacts:
                                contact_key_field_json[contact_key] = (
                                    create_json_contacts[contact_key]
                                )

                    if company_key_field:
                        for company_key in company_key_field:
                            if company_key in create_json_company:
                                company_key_field_json[company_key] = (
                                    create_json_company[company_key]
                                )

                if import_method_contact == "create_and_update_contact":
                    if contact_key_field:
                        try:
                            contact = Contact.objects.get(
                                **contact_key_field_json, workspace=workspace
                            )
                        except Contact.MultipleObjectsReturned:
                            contact = Contact.objects.filter(
                                **contact_key_field_json, workspace=workspace
                            ).first()
                            for contact_key_field_sub_json in contact_key_field_json:
                                setattr(
                                    contact,
                                    contact_key_field_sub_json,
                                    contact_key_field_json[contact_key_field_sub_json],
                                )
                        except Contact.DoesNotExist:
                            if not has_quota(workspace, CONTACT_USAGE_CATEGORY):
                                if lang == "ja":
                                    Notification.objects.create(
                                        workspace=get_workspace(request.user),
                                        user=request.user,
                                        message="一部の連絡先は上限を超えたため、作成できませんでした。サブスクリプションプランをアップグレードして容量を増やすか、既存のアイテムをアーカイブして容量を空けてください。",
                                        type="error",
                                    )
                                else:
                                    Notification.objects.create(
                                        workspace=get_workspace(request.user),
                                        user=request.user,
                                        message="Some contacts could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                                        type="error",
                                    )
                                break

                            if "contact_id" in create_json_contacts:
                                create_json_contacts.pop("contact_id")

                            if "insert_to_list" in create_json_contacts:
                                create_json_contacts.pop("insert_to_list")

                            contact = Contact.objects.create(
                                **create_json_contacts,
                                status="active",
                                workspace=workspace,
                            )
                            contact.save()

                elif import_method_contact == "create_contact":
                    if "insert_to_list" in create_json_contacts:
                        create_json_contacts.pop("insert_to_list")

                    if not has_quota(workspace, CONTACT_USAGE_CATEGORY):
                        msg = "Some contact could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space."
                        if lang == "ja":
                            msg = "制限を超えたため、一部の連絡先を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存のレコードをアーカイブしてスペースを解放してください。"
                        Notification.objects.create(
                            workspace=get_workspace(request.user),
                            user=request.user,
                            message=msg,
                            type="error",
                        )
                        break

                    if "contact_id" in create_json_contacts:
                        create_json_contacts.pop("contact_id")
                    contact = Contact.objects.create(
                        **create_json_contacts, status="active", workspace=workspace
                    )
                    contact.save(
                        log_data={
                            "user": request.user,
                            "status": "create",
                            "workspace": workspace,
                        }
                    )

                elif import_method_contact == "update_contact":
                    if contact_key_field:
                        # if Data is not exist, Skip
                        contact = Contact.objects.filter(
                            **contact_key_field_json, workspace=workspace
                        ).first()
                        if contact:
                            try:
                                contact, _ = Contact.objects.get_or_create(
                                    **contact_key_field_json, workspace=workspace
                                )
                            except Contact.MultipleObjectsReturned:
                                contact = Contact.objects.filter(
                                    **contact_key_field_json, workspace=workspace
                                ).first()
                                for (
                                    contact_key_field_sub_json
                                ) in contact_key_field_json:
                                    setattr(
                                        contact,
                                        contact_key_field_sub_json,
                                        contact_key_field_json[
                                            contact_key_field_sub_json
                                        ],
                                    )
                if contact:
                    # Write Custom Field
                    for (
                        create_json_contacts_cs_field
                    ) in create_json_contacts_custom_field:
                        contact_name_custom_field = ContactsNameCustomField.objects.get(
                            name=create_json_contacts_cs_field, workspace=workspace
                        )
                        CustomFieldValue, _ = (
                            ContactsValueCustomField.objects.get_or_create(
                                field_name=contact_name_custom_field, contact=contact
                            )
                        )
                        CustomFieldValue.value = create_json_contacts_custom_field[
                            create_json_contacts_cs_field
                        ]
                        CustomFieldValue.save()

                    # Populate rest of the key
                    for create_json_contact_key in create_json_contacts:
                        if create_json_contact_key == "insert_to_list":
                            id_lists = create_json_contacts["insert_to_list"]
                            id_lists = id_lists.split(CSV_DELIMITER_LIST_FIELD)
                            for id_list in id_lists:
                                contact_list = ContactList.objects.filter(
                                    id=id_list
                                ).first()
                                if contact_list:
                                    contact_list.contacts.add(contact)
                        else:
                            setattr(
                                contact,
                                create_json_contact_key,
                                create_json_contacts[create_json_contact_key],
                            )

                    setattr(contact, "status", "active")
                    contact.save(
                        log_data={"user": request.user, "workspace": workspace}
                    )

                # #================================================================#

                if import_method_company == "create_and_update_company":
                    if company_key_field:
                        try:
                            company = Company.objects.get(
                                **company_key_field_json, workspace=workspace
                            )
                        except Company.MultipleObjectsReturned:
                            company = Company.objects.filter(
                                **company_key_field_json, workspace=workspace
                            ).first()
                            for company_key_field_sub_json in company_key_field_json:
                                setattr(
                                    company,
                                    company_key_field_sub_json,
                                    company_key_field_json[company_key_field_sub_json],
                                )
                        except Company.DoesNotExist:
                            if not has_quota(workspace, COMPANY_USAGE_CATEGORY):
                                if lang == "ja":
                                    Notification.objects.create(
                                        workspace=get_workspace(request.user),
                                        user=request.user,
                                        message="一部のアイテムは上限を超えたため、作成できませんでした。サブスクリプションプランをアップグレードして容量を増やすか、既存のアイテムをアーカイブして容量を空けてください。",
                                        type="error",
                                    )
                                else:
                                    Notification.objects.create(
                                        workspace=get_workspace(request.user),
                                        user=request.user,
                                        message="Some items could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                                        type="error",
                                    )
                                    if module:
                                        return redirect(
                                            reverse(
                                                "load_object_page",
                                                host="app",
                                                kwargs={
                                                    "module_slug": module_slug,
                                                    "object_slug": module_object_slug,
                                                },
                                            )
                                        )
                                    return redirect(reverse("main", host="app"))

                            if "company_id" in create_json_company:
                                create_json_company.pop("company_id")

                            company = Company.objects.create(
                                **create_json_company,
                                status="active",
                                workspace=workspace,
                            )
                            company.save()

                        if not company:
                            if module:
                                return redirect(
                                    reverse(
                                        "load_object_page",
                                        host="app",
                                        kwargs={
                                            "module_slug": module_slug,
                                            "object_slug": module_object_slug,
                                        },
                                    )
                                )
                            return redirect(reverse("main", host="app"))

                elif import_method_company == "create_company":
                    if "insert_to_list" in create_json_company:
                        create_json_company.pop("insert_to_list")

                    if not has_quota(workspace, COMPANY_USAGE_CATEGORY):
                        msg = "Some companies could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing companies to free up space."
                        if lang == "ja":
                            msg = "制限を超えたため、一部の会社を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存の会社の一部をアーカイブしてスペースを解放してください。"
                        Notification.objects.create(
                            workspace=get_workspace(request.user),
                            user=request.user,
                            message=msg,
                            type="error",
                        )
                        break

                    company = Company.objects.create(
                        **create_json_company, status="active", workspace=workspace
                    )
                    company.save(
                        log_data={
                            "user": request.user,
                            "status": "create",
                            "workspace": workspace,
                        }
                    )

                elif import_method_company == "update_company":
                    if company_key_field:
                        # if Data is exist, Continoue
                        company = Company.objects.filter(
                            **company_key_field_json, workspace=workspace
                        ).first()
                        if company:
                            try:
                                company, _ = Company.objects.get_or_create(
                                    **company_key_field_json, workspace=workspace
                                )
                            except Company.MultipleObjectsReturned:
                                company = Company.objects.filter(
                                    **company_key_field_json, workspace=workspace
                                ).first()
                                for (
                                    company_key_field_sub_json
                                ) in company_key_field_json:
                                    setattr(
                                        company,
                                        company_key_field_sub_json,
                                        company_key_field_json[
                                            company_key_field_sub_json
                                        ],
                                    )

                if company:
                    # Write Custom Field
                    for (
                        create_json_company_cs_field
                    ) in create_json_company_custom_field:
                        company_name_custom_field = CompanyNameCustomField.objects.get(
                            name=create_json_company_cs_field, workspace=workspace
                        )
                        CustomFieldValue, _ = (
                            CompanyValueCustomField.objects.get_or_create(
                                field_name=company_name_custom_field, company=company
                            )
                        )
                        CustomFieldValue.value = create_json_company_custom_field[
                            create_json_company_cs_field
                        ]
                        CustomFieldValue.save()

                    # Populate rest of the key
                    for create_json_company_key in create_json_company:
                        if create_json_company_key == "insert_to_list":
                            id_lists = create_json_company["insert_to_list"]
                            id_lists = id_lists.split(CSV_DELIMITER_LIST_FIELD)
                            for id_list in id_lists:
                                company_list = CompanyList.objects.filter(
                                    id=id_list
                                ).first()
                                if company_list:
                                    company_list.companies.add(company)
                        else:
                            setattr(
                                company,
                                create_json_company_key,
                                create_json_company[create_json_company_key],
                            )
                    setattr(company, "status", "active")
                    company.save(
                        log_data={"user": request.user, "workspace": workspace}
                    )

                if "contact" in import_data and "company" in import_data:
                    if contact and company:
                        contact.companies.add(company)
                        contact.save()

        except Exception as e:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message=f"Wrong format uploaded, Please use this format | {str(e)}",
                message_ja=f"アップロードされた形式が間違っています。この形式を使用してください | {str(e)}",
                cta_text="Template",
                cta_text_ja="テンプレート",
                cta_target=TEMPLATE_FILE["contacts"][lang],
                type="error",
            )

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="連絡先が正常に追加されました。",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Contact Sucessfully added.",
                type="success",
            )

        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))

    if lang == "ja":
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="連絡先が正常に作成されました",
            type="success",
        )
    else:
        Notification.objects.create(
            workspace=get_workspace(request.user),
            user=request.user,
            message="Contacts Created Succesfully",
            type="success",
        )
    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def create_companies(request):
    target = "company"
    workspace = get_workspace(request.user)
    source_url = request.POST.get("source_url")

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_COMPANY]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_COMPANY
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug

    lang = request.LANGUAGE_CODE
    if "single-entry" in request.POST:
        name = request.POST.get("company_name", None)
        phone_header = request.POST.get("phone_header", None)
        phone_number = request.POST.get("phone_number", None)
        address = request.POST.get("address", None)
        email = request.POST.get("email", None)
        urls = request.POST.get("urls", None)
        owner = request.POST.get("owner", None)
        if phone_number:
            if phone_header:
                phone_number = phone_header + "-" + phone_number
            else:
                phone_number = phone_number

        if not has_quota(workspace, COMPANY_USAGE_CATEGORY):
            msg = "Company could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing companies to free up space."
            if lang == "ja":
                msg = "制限を超えたため、会社を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存の会社の一部をアーカイブしてスペースを解放してください。"
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message=msg,
                type="error",
            )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        company = Company.objects.create(
            name=name,
            address=address,
            phone_number=phone_number,
            workspace=workspace,
            status="active",
        )
        if email:
            company.email = email
        if urls:
            company.url = urls

        files = request.FILES.getlist("fileUpload")
        for file in files:
            file.name = str(uuid.uuid4()) + "." + file.name.split(".")[-1]
            company.image_file = file
            company.save()
            break

        assign_object_owner(company, owner, request, TYPE_OBJECT_COMPANY)
        company.save(
            log_data={"user": request.user, "status": "create", "workspace": workspace}
        )

        save_custom_property(request, company)
        save_association_label(request, company, TYPE_OBJECT_COMPANY)

        # association related
        if request.POST.get("type_association", "") == "create-association":
            if "object_type" in request.POST:
                object_type = request.POST.get("object_type")
            source = request.POST.get("source")
            filter_conditions = Q(workspace=workspace)
            if request.POST.get("custom_object_id", None):
                custom_object_id = request.POST.get("custom_object_id")
                custom_object = CustomObject.objects.get(id=custom_object_id)
                module_object_slug = custom_object.slug
                filter_conditions &= Q(object_values__contains=str(custom_object_id))
            else:
                module_object_slug = OBJECT_TYPE_TO_SLUG[source]
                filter_conditions &= Q(object_values__contains=source)

            module = (
                Module.objects.filter(filter_conditions)
                .order_by("order", "created_at")
                .first()
            )
            module_slug = module.slug
            if source == TYPE_OBJECT_SUBSCRIPTION:
                object_id = request.POST.get("source_object_id")
                subscription = ShopTurboSubscriptions.objects.get(id=object_id)
                if subscription:
                    subscription.company = company
                    subscription.contact = None
                    subscription.save()

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?target={source}&id={subscription.id}"
                )

            if source == TYPE_OBJECT_CONTACT:
                object_id = request.POST.get("source_object_id")
                contact = Contact.objects.get(id=object_id)
                if contact:
                    contact.companies.add(company)

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?target={source}&id={contact.id}"
                )

            if source == TYPE_OBJECT_ORDER:
                object_id = request.POST.get("source_object_id")
                order = ShopTurboOrders.objects.get(id=object_id)
                order.companies.add(company)

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?target={source}&id={order.id}"
                )

        if request.POST.get("addon-order", None):
            response = HttpResponse(200)
            response["HX-Trigger"] = json.dumps(
                {
                    "showOrderDrawerCompanySingleEntry": {
                        "event_company_id": str(company.id)
                    },
                }
            )
            return response

        if source_url:
            source_url = update_query_params_url(
                source_url, {"target": target, "id": [str(company.id)]}, overwrite=True
            )
            return redirect(source_url)
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
            + f"?company_id={company.id}"
        )

    elif "upload-entry" in request.POST:
        csv = request.FILES.get("csv_upload", False)
        if not csv:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Failed to retreive the CSV file.",
                type="error",
            )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        file_columns = request.POST.getlist("file-column")
        import_as = request.POST.getlist("import-as")
        sanka_properties_company = request.POST.getlist("sanka-properties-company")
        ignores = request.POST.getlist("ignore")

        import_method_company = request.POST.get("import-method-company")
        company_key_field = request.POST.getlist("company-key-field")

        try:
            mapping_storage, _ = ImportMappingFields.objects.get_or_create(
                workspace=workspace, object_type=TYPE_OBJECT_COMPANY
            )
            mapping = {
                sanka_prop: file_col
                for sanka_prop, file_col in zip(sanka_properties_company, file_columns)
            }
            mapping_storage.input_pair = mapping
            mapping_storage.save()

            transfer_history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="import_company",
            )
            transfer_history.name = csv.name
            transfer_history.save()

            df = read_csv(csv)

            df = df.astype(str)
            for index, row in df.iterrows():
                create_json_company = {}
                create_json_company_custom_field = {}
                company_key_field_json = {}
                for idx, file_column in enumerate(file_columns):
                    # check ignore
                    if ignores[idx] == "True":
                        continue

                    if row[file_column] == "nan" or row[file_column] == None:
                        continue

                    if "company" in import_as[idx]:
                        if is_valid_uuid(sanka_properties_company[idx]):
                            custom_field = CompanyNameCustomField.objects.filter(
                                id=sanka_properties_company[idx], workspace=workspace
                            ).first()
                            create_json_company_custom_field[custom_field.name] = row[
                                file_column
                            ]
                        else:
                            create_json_company[sanka_properties_company[idx]] = row[
                                file_column
                            ]

                    if company_key_field:
                        for company_key in company_key_field:
                            if company_key in create_json_company:
                                company_key_field_json[company_key] = (
                                    create_json_company[company_key]
                                )

                if import_method_company == "create_and_update_company":
                    if company_key_field:
                        try:
                            company = Company.objects.get(
                                **company_key_field_json, workspace=workspace
                            )
                        except Company.MultipleObjectsReturned:
                            company = Company.objects.filter(
                                **company_key_field_json, workspace=workspace
                            ).first()
                            for company_key_field_sub_json in company_key_field_json:
                                setattr(
                                    company,
                                    company_key_field_sub_json,
                                    company_key_field_json[company_key_field_sub_json],
                                )
                        except Company.DoesNotExist:
                            if not has_quota(workspace, COMPANY_USAGE_CATEGORY):
                                if lang == "ja":
                                    Notification.objects.create(
                                        workspace=get_workspace(request.user),
                                        user=request.user,
                                        message="一部のアイテムは上限を超えたため、作成できませんでした。サブスクリプションプランをアップグレードして容量を増やすか、既存のアイテムをアーカイブして容量を空けてください。",
                                        type="error",
                                    )
                                else:
                                    Notification.objects.create(
                                        workspace=get_workspace(request.user),
                                        user=request.user,
                                        message="Some items could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some records to free up space.",
                                        type="error",
                                    )
                                if module:
                                    return redirect(
                                        reverse(
                                            "load_object_page",
                                            host="app",
                                            kwargs={
                                                "module_slug": module_slug,
                                                "object_slug": module_object_slug,
                                            },
                                        )
                                    )
                                return redirect(reverse("main", host="app"))

                            if "company_id" in create_json_company:
                                create_json_company.pop("company_id")

                            company = Company.objects.create(
                                **create_json_company,
                                status="active",
                                workspace=workspace,
                            )
                            company.save()

                        if not company:
                            if module:
                                return redirect(
                                    reverse(
                                        "load_object_page",
                                        host="app",
                                        kwargs={
                                            "module_slug": module_slug,
                                            "object_slug": module_object_slug,
                                        },
                                    )
                                )
                            return redirect(reverse("main", host="app"))

                elif import_method_company == "create_company":
                    if not has_quota(workspace, COMPANY_USAGE_CATEGORY):
                        msg = "Some companies could not be created due to exceeding the limit. Upgrade your subscription plan to increase your quota or archive some existing companies to free up space."
                        if lang == "ja":
                            msg = "制限を超えたため、一部の会社を作成できませんでした。サブスクリプション プランをアップグレードして割り当てを増やすか、既存の会社の一部をアーカイブしてスペースを解放してください。"
                        Notification.objects.create(
                            workspace=get_workspace(request.user),
                            user=request.user,
                            message=msg,
                            type="error",
                        )
                        break

                    if "company_id" in create_json_company:
                        create_json_company.pop("company_id")
                    company = Company.objects.create(
                        **create_json_company, status="active", workspace=workspace
                    )
                    company.save(
                        log_data={"user": request.user, "workspace": workspace}
                    )

                elif import_method_company == "update_company":
                    if company_key_field:
                        # if Data is exist, Continoue
                        company = Company.objects.filter(
                            **company_key_field_json, workspace=workspace
                        ).first()
                        if company:
                            try:
                                company, _ = Company.objects.get_or_create(
                                    **company_key_field_json, workspace=workspace
                                )
                            except Company.MultipleObjectsReturned:
                                company = Company.objects.filter(
                                    **company_key_field_json, workspace=workspace
                                ).first()
                                for (
                                    company_key_field_sub_json
                                ) in company_key_field_json:
                                    setattr(
                                        company,
                                        company_key_field_sub_json,
                                        company_key_field_json[
                                            company_key_field_sub_json
                                        ],
                                    )

                # Write Custom Field
                for create_json_company_cs_field in create_json_company_custom_field:
                    company_name_custom_field = CompanyNameCustomField.objects.get(
                        name=create_json_company_cs_field, workspace=workspace
                    )
                    CustomFieldValue, _ = CompanyValueCustomField.objects.get_or_create(
                        field_name=company_name_custom_field, company=company
                    )

                    if (
                        create_json_company_custom_field[create_json_company_cs_field]
                        != "nan"
                    ):
                        CustomFieldValue.value = create_json_company_custom_field[
                            create_json_company_cs_field
                        ]
                        CustomFieldValue.save()

                # Populate rest of the key
                for create_json_company_key in create_json_company:
                    setattr(
                        company,
                        create_json_company_key,
                        create_json_company[create_json_company_key],
                    )
                setattr(company, "status", "active")
                company.save(log_data={"user": request.user, "workspace": workspace})

                progress = 100 * (index + 1) / df.shape[0]
                transfer_history.progress = progress
                transfer_history.save()

                if transfer_history.status == "canceled":
                    break

            if transfer_history.status != "canceled":
                transfer_history.status = "completed"
                transfer_history.save()

        except Exception:
            Notification.objects.create(
                workspace=workspace,
                user=request.user,
                message="Wrong format uploaded, Please use this format",
                message_ja="アップロードされた形式が間違っています。この形式を使用してください",
                cta_text="Template",
                cta_text_ja="テンプレート",
                cta_target=TEMPLATE_FILE["company"][lang],
                type="error",
            )
            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        lang = request.LANGUAGE_CODE
        if lang == "ja":
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="CSVから企業が追加されました。",
                type="success",
            )
        else:
            Notification.objects.create(
                workspace=get_workspace(request.user),
                user=request.user,
                message="Companies added successfully from CSV.",
                type="success",
            )
        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))

    if module:
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def load_explore_company(request, id: uuid):
    company = get_object_or_404(Company, id=id)
    workspace = get_workspace(request.user)
    type = request.GET.get("type", None)
    hide_associated_data = request.GET.get("hide_associated_data", None)
    view_form_target = request.GET.get("view_form_target", None)
    if view_form_target:
        view_form_target = ast.literal_eval(view_form_target)
    if type == "image":
        context = {"company": company}
        return render(
            request, "data/partials/profile-company-detail-image.html", context
        )
    else:
        notes = Notes.objects.filter(
            company=company, workspace=get_workspace(request.user)
        ).order_by("-created_at")
        exploreCustomField = CompanyNameCustomField.objects.filter(
            workspace=get_workspace(request.user)
        )
        invoices = Invoice.objects.filter(
            workspace=workspace, company=company
        ).order_by("-created_at")
        estimates = Estimate.objects.filter(
            workspace=workspace, company=company
        ).order_by("-created_at")
        delivery_slips = DeliverySlip.objects.filter(
            workspace=workspace, company=company
        ).order_by("-created_at")
        receipts = Receipt.objects.filter(
            workspace=workspace, company=company
        ).order_by("-created_at")
        subscriptions = ShopTurboSubscriptions.objects.filter(
            workspace=workspace, company=company
        ).order_by("-created_at")
        expenses = Expense.objects.filter(
            workspace=workspace, company=company
        ).order_by("-created_at")

        # Safely query bills with error handling for potential database issues
        bills = []
        try:
            from data.models.expensebill import Bill

            bills = Bill.objects.filter(workspace=workspace, company=company).order_by(
                "-created_at"
            )
        except Exception as e:
            # Log the error but don't break the page
            import logging

            logger = logging.getLogger(__name__)
            logger.error(f"Error querying bills for company {company.id}: {e}")
            print(f"Error querying bills for company {company.id}: {e}")
            bills = []

        # company_list = CompanyList.objects.filter(workspace=workspace).exclude(status="unused")

        app_logs = AppLog.objects.filter(workspace=workspace, company=company).order_by(
            "-created_at"
        )

        companies = []
        object_group_type = "company"
        permission = get_permission(object_type=object_group_type, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        set_id = request.GET.get("set_id", None)
        view_id = request.GET.get("view_id", None)
        source = request.GET.get("source", None)

        # Extract association-related parameters for maintaining association context
        type_association = request.GET.get("type_association", None)
        source_object_id = request.GET.get("source_object_id", None)
        object_id = request.GET.get("object_id", None)
        module_param = request.GET.get("module", None)

        if set_id:
            property_set = PropertySet.objects.filter(id=set_id).first()
        else:
            if view_id == "None":
                view_id = None
            if view_id:
                view = View.objects.filter(
                    id=view_id, target=TYPE_OBJECT_COMPANY
                ).first()
                if view:
                    if view.form:
                        property_set = view.form
                    else:
                        condition_filter = Q(
                            workspace=workspace,
                            target=TYPE_OBJECT_COMPANY,
                            as_default=True,
                        )
                        property_set = PropertySet.objects.filter(
                            condition_filter
                        ).first()
                else:
                    condition_filter = Q(
                        workspace=workspace, target=TYPE_OBJECT_COMPANY, as_default=True
                    )
                    property_set = PropertySet.objects.filter(condition_filter).first()
            else:
                condition_filter = Q(
                    workspace=workspace, target=TYPE_OBJECT_COMPANY, as_default=True
                )
                property_set = PropertySet.objects.filter(condition_filter).first()

            if not property_set:
                property_set = PropertySet.objects.filter(
                    workspace=workspace, target=TYPE_OBJECT_COMPANY, name__isnull=True
                ).first()

        properties = {"list_all": []}
        if property_set:
            set_id = str(property_set.id)
            if property_set.children:
                for p in property_set.children:
                    properties["list_all"].append(p)

        CustomFieldMap = {}
        explorecustomfield = CompanyNameCustomField.objects.filter(
            workspace=workspace
        ).order_by("order")
        for ctf in explorecustomfield:
            CustomFieldMap[str(ctf.id)] = ctf

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_COMPANY
        ).order_by("created_at")

        try:
            channels = Channel.objects.filter(
                workspace=get_workspace(request.user),
                integration__slug__in=["hubspot", "salesforce", "b-cart"],
            )
            for channel in channels:
                properties["list_all"].insert(
                    len(properties["list_all"]), f"{str(channel.name)} - contact ID"
                )
        except:
            pass

        if company and company.owner and company.owner.user:
            permission += f"|{company.owner.user.id}#{request.user.id}"

        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_COMPANY
        ).order_by("created_at")
        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_COMPANY,
            created_by_sanka=True,
        )
        association_label_list_sanka_true = [
            association_label.label
            for association_label in association_labels_sanka_true
        ]
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_COMPANY,
            created_by_sanka=False,
        )
        association_label_list_sanka_false = [
            str(association_label.id)
            for association_label in association_labels_sanka_false
        ]
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )

        # related associate label
        related_association_labels = AssociationLabel.objects.filter(
            created_by_sanka=False,
            workspace=workspace,
            object_target__icontains=TYPE_OBJECT_COMPANY,
        ).order_by("created_at")

        context = {
            "companies": companies,
            "company": company,
            "explorecustomfield": exploreCustomField,
            "page_type": "companies",
            "notes": notes,
            "invoices": invoices,
            "estimates": estimates,
            "country_code": COUNTRY_CODE,
            "delivery_slips": delivery_slips,
            "receipts": receipts,
            "subscriptions": subscriptions,
            "expenses": expenses,
            "bills": bills,
            "from": request.GET.get("from", None),
            "from_view_id": request.GET.get("from_view_id", None),
            # 'company_list':company_list,
            "app_logs": app_logs,
            "permission": permission,
            "object_type": TYPE_OBJECT_COMPANY,
            "source": TYPE_OBJECT_COMPANY,
            "properties": properties,
            "CustomFieldMap": CustomFieldMap,
            "property_sets": property_sets,
            "set_id": set_id,
            "hide_associated_data": hide_associated_data,
            "view_form_target": view_form_target,
            # Association context for maintaining associations when form is submitted from association drawer
            "type_association": type_association,
            "source_object_id": source_object_id,
            "association_object_id": object_id,
            "association_module": module_param,
            "association_labels": association_labels,
            "association_label_list": association_label_list,
            "related_association_labels": related_association_labels,
        }

        return render(request, "data/partials/profile-company-detail.html", context)


@login_or_hubspot_required
def default_conditions_model(request):
    platform = request.GET.get("community_platform", "all")
    # FLOW
    actions = Action.objects.filter().order_by("created_at")
    context = {"platform": platform, "actions": actions}
    return render(
        request, "data/partials/contact-partials-default_conditions_model.html", context
    )


def load_explore_profile(request, id: uuid):
    workspace = get_workspace(request.user)
    contact = get_object_or_404(Contact, id=id)

    type = request.GET.get("type", None)
    hide_associated_data = request.GET.get("hide_associated_data", None)
    view_form_target = request.GET.get("view_form_target", None)
    if view_form_target:
        view_form_target = ast.literal_eval(view_form_target)

    if type == "image":
        context = {"contact": contact}
        return render(
            request, "data/partials/profile-contact-detail-image.html", context
        )
    else:
        module_slug = request.GET.get("module")

        module = Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_CONTACT
        ).order_by("order", "created_at")

        if module_slug:
            module = module.filter(slug=module_slug)

        if module:
            module = module.first()
            module_slug = module.slug

        contact_list_id = request.GET.get("contact_list_id", None)
        companies = Company.objects.filter(workspace=workspace)

        notes = Notes.objects.filter(
            contact=contact, workspace=get_workspace(request.user)
        ).order_by("-created_at")
        exploreCustomField = ContactsNameCustomField.objects.filter(
            workspace=get_workspace(request.user)
        ).order_by("order")

        contact_events = []
        pixel_event = PixelEvent.objects.filter(
            contact=contact, workspace=workspace
        ).first()
        if pixel_event:
            session = pixel_event.session
            contact_events = PixelEvent.objects.filter(
                workspace=workspace, session=session
            ).order_by("-created_at")

        app_logs = AppLog.objects.filter(workspace=workspace, contact=contact).order_by(
            "-created_at"
        )

        invoices = Invoice.objects.filter(
            workspace=workspace, contact=contact
        ).order_by("-created_at")
        estimates = Estimate.objects.filter(
            workspace=workspace, contact=contact
        ).order_by("-created_at")
        delivery_slips = DeliverySlip.objects.filter(
            workspace=workspace, contact=contact
        ).order_by("-created_at")
        receipts = Receipt.objects.filter(
            workspace=workspace, contact=contact
        ).order_by("-created_at")
        subscriptions = ShopTurboSubscriptions.objects.filter(
            workspace=workspace, contact=contact
        ).order_by("-created_at")
        expenses = Expense.objects.filter(
            workspace=workspace, contact=contact
        ).order_by("-created_at")

        properties = None
        set_id = request.GET.get("set_id", None)
        view_id = request.GET.get("view_id", None)
        source = request.GET.get("source", None)

        # Extract association-related parameters for maintaining association context
        type_association = request.GET.get("type_association", None)
        source_object_id = request.GET.get("source_object_id", None)
        object_id = request.GET.get("object_id", None)
        module_param = request.GET.get("module", None)

        if set_id:
            property_set = PropertySet.objects.filter(id=set_id).first()
        else:
            if view_id == "None":
                view_id = None

            if source == "messages":
                view = View.objects.filter(
                    workspace=workspace, target=TYPE_OBJECT_CONTACT, title__isnull=True
                ).first()
            elif view_id:
                view = View.objects.filter(
                    id=view_id, target=TYPE_OBJECT_CONTACT
                ).first()
            else:
                view = None

            if view and view.form:
                property_set = view.form
            else:
                condition_filter = Q(workspace=workspace, target=TYPE_OBJECT_CONTACT)
                condition_filter &= Q(as_default=True) | Q(name__isnull=True)
                property_set = PropertySet.objects.filter(condition_filter).first()

        properties = {"list_all": []}
        if property_set:
            set_id = str(property_set.id)
            for p in property_set.children:
                properties["list_all"].append(p)

        CustomFieldMap = {}
        explorecustomfield = ContactsNameCustomField.objects.filter(
            workspace=workspace
        ).order_by("order")
        for ctf in explorecustomfield:
            CustomFieldMap[str(ctf.id)] = ctf

        permission = get_permission("contacts", request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        custom_objects = CustomObject.objects.filter(workspace=workspace)

        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_CONTACT
        ).order_by("created_at")

        try:
            channels = Channel.objects.filter(
                workspace=get_workspace(request.user),
                integration__slug__in=[
                    "hubspot",
                    "shopify",
                    "freee",
                    "rakuten",
                    "salesforce",
                ],
            )
            for channel in channels:
                properties["list_all"].insert(
                    len(properties["list_all"]), f"{str(channel.name)} - contact ID"
                )
        except:
            pass

        if contact and contact.owner and contact.owner.user:
            permission += f"|{contact.owner.user.id}#{request.user.id}"

        association_labels = AssociationLabel.objects.filter(
            workspace=workspace, object_source=TYPE_OBJECT_CONTACT
        ).order_by("created_at")
        association_labels_sanka_true = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_CONTACT,
            created_by_sanka=True,
        )
        association_label_list_sanka_true = [
            association_label.label
            for association_label in association_labels_sanka_true
        ]
        association_labels_sanka_false = AssociationLabel.objects.filter(
            workspace=workspace,
            object_source=TYPE_OBJECT_CONTACT,
            created_by_sanka=False,
        )
        association_label_list_sanka_false = [
            str(association_label.id)
            for association_label in association_labels_sanka_false
        ]
        association_label_list = (
            association_label_list_sanka_true + association_label_list_sanka_false
        )

        # related associate label
        related_association_labels = AssociationLabel.objects.filter(
            created_by_sanka=False,
            workspace=workspace,
            object_target__icontains=TYPE_OBJECT_CONTACT,
        ).order_by("created_at")

        context = {
            "contact": contact,
            "companies": companies,
            "contact_list_id": contact_list_id,
            "contact_lists": ContactList.objects.filter(workspace=workspace).order_by(
                "created_at"
            ),
            "CURRENCY_MODEL": CURRENCY_MODEL,
            "contact_events": contact_events,
            "country_code": COUNTRY_CODE,
            "notes": notes,
            "app_logs": app_logs,
            "from": request.GET.get("from", None),
            "from_view_id": request.GET.get("from_view_id", None),
            "invoices": invoices,
            "estimates": estimates,
            "delivery_slips": delivery_slips,
            "receipts": receipts,
            "subscriptions": subscriptions,
            "expenses": expenses,
            "CustomFieldMap": CustomFieldMap,
            "properties": properties,
            "set_id": set_id,
            "permission": permission,
            "object_type": TYPE_OBJECT_CONTACT,
            "source": source if source else TYPE_OBJECT_CONTACT,
            "view_form_target": view_form_target,
            "custom_objects": custom_objects,
            "object_action": {
                "additional_params": {
                    "contact_id": str(contact.id),
                },
            },
            "line_details": ContactLineChannel.objects.filter(
                contact=contact, workspace=workspace
            ),
            "property_sets": property_sets,
            "hide_associated_data": hide_associated_data,
            "obj": contact,
            "module": module_slug,
            # Association context for maintaining associations when form is submitted from association drawer
            "type_association": type_association,
            "source_object_id": source_object_id,
            "association_object_id": object_id,
            "association_module": module_param,
            "association_labels": association_labels,
            "association_label_list": association_label_list,
            "related_association_labels": related_association_labels,
        }

        return render(request, "data/partials/profile-contact-detail.html", context)


@login_or_hubspot_required
def get_contact_phone_number(request, id):
    workspace = get_workspace(request.user)
    contact = Contact.objects.get(pk=id)
    data = []
    if contact.phone_number:
        data.append(contact.phone_number.replace("-", ""))

    custom_fields = ContactsNameCustomField.objects.filter(
        workspace=workspace, type="phone"
    )

    for field in custom_fields:
        value_custom_field, _ = ContactsValueCustomField.objects.get_or_create(
            field_name=field, contact=contact
        )
        if value_custom_field.value:
            data.append(value_custom_field.value.replace("-", ""))
    return JsonResponse(data, safe=False)


@login_or_hubspot_required
def get_create_list_drawer(request):
    context = {
        "community_platform_tags": [
            "twitter",
            "instagram",
            "youtube",
            "github",
        ],
        "channels": Channel.objects.filter(
            workspace=get_workspace(request.user),
            integration__slug__in=["twitter", "instagram", "youtube"],
        ),
    }
    return render(request, "data/partials/create-list-drawer.html", context)

    if contacts:
        number = contacts.count()

        if number > contact_list.num_contacts:
            deviation = number - contact_list.num_contacts
            new_number = contact_list.num_contacts + deviation
            contact_list.num_contacts = new_number
            contact_list.save()

            contact_list.contacts.add(*contacts)


def company_update_notes_form(request, id=None):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == "POST":
        description = request.POST.get("note_description")

        if request.POST.get("edit"):
            notes = Notes.objects.get(id=id)
            notes.description = description
            notes.save()

            update = Notes.objects.filter(
                company=notes.company, workspace=workspace
            ).order_by("-created_at")
            context = {
                "updates": update,
                # 'notes':notes
            }
            response = render(request, "data/contacts/partial-notes-rows.html", context)
            return response

        elif request.POST.get("delete"):
            notes = Notes.objects.get(id=id)
            notes.delete()

            update = Notes.objects.filter(
                company=notes.company, workspace=workspace
            ).order_by("-created_at")

            context = {
                "updates": update,
                # 'notes':notes
            }
            response = render(request, "data/contacts/partial-notes-rows.html", context)
            return response

        if id:
            company = Company.objects.get(id=id)
            Notes.objects.create(
                company=company,
                description=description,
                # date=note_date,
                assignee=request.user,
                workspace=workspace,
                target=TYPE_OBJECT_COMPANY,
            )

            update = Notes.objects.filter(
                company=company, workspace=workspace
            ).order_by("-created_at")

            context = {
                "updates": update,
            }
            response = render(request, "data/contacts/partial-notes-rows.html", context)
        else:
            response = render(request, "data/contacts/partial-notes-rows.html")

        return response

    company = Company.objects.get(id=id)

    context = {"company": company, "uuid": str(uuid.uuid4())}
    return render(request, "data/contacts/companies-notes-form.html", context)


@login_or_hubspot_required
def company_notes_form(request):
    return render(request, "data/contacts/companies-notes-form.html")


def apply_search_setting(
    page_group_type,
    view_filter: ViewFilter,
    search_key,
    search_value,
    force_filter_list=[],
):
    # Log search setting application details
    logger.info(
        "apply_search_setting called - page_group_type: %s, search_key: %s, search_value: %s, view_filter_view_id: %s, is_uuid_key: %s",
        page_group_type,
        search_key,
        search_value,
        str(view_filter.view.id) if view_filter.view else None,
        is_valid_uuid(search_key),
    )

    # Handle None or invalid view_filter
    if not view_filter or not view_filter.view:
        logger.warning("apply_search_setting called with invalid view_filter")
        # Return a basic Q filter as fallback
        if search_key in ["name", "first_name"]:
            return Q(name__icontains=search_value)
        elif search_key == "last_name":
            return Q(last_name__icontains=search_value)
        elif search_key == "email":
            return Q(email__icontains=search_value)
        else:
            return Q()

    view = view_filter.view
    dummy_view_filter = ViewFilter(
        view=view, filter_value={search_key: {"key": "contains", "value": search_value}}
    )

    logger.debug(
        "Created dummy ViewFilter - filter_value: %s, force_filter_list: %s",
        dummy_view_filter.filter_value,
        force_filter_list,
    )

    result = build_view_filter(
        Q(), dummy_view_filter, page_group_type, force_filter_list=force_filter_list
    )

    logger.debug("build_view_filter result - query_str: %s", str(result))

    return result


def check_columns_type(data_filter, default_columns_dict):
    data_type = "string"
    if data_filter in default_columns_dict.keys():
        for check in ["float", "integer"]:
            if check in default_columns_dict[data_filter].lower():
                data_type = "number"
                break
        for check in ["date"]:
            if check in default_columns_dict[data_filter].lower():
                data_type = "date"
                break
    return data_type


@login_or_hubspot_required
@require_GET
def get_customer_options(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    q = request.GET.get("q")
    page = request.GET.get("page", 1)
    purpose = request.GET.get("purpose", "")
    usage = request.GET.get("usage", "")

    if purpose:
        if "custom_property_contact" in purpose:
            if "custom_property_contact_choice" in purpose:
                filter_conditions = Q(workspace=workspace, type="choice")
            else:
                filter_conditions = Q(workspace=workspace)

            if q:
                filter_conditions &= Q(name__icontains=q)

            cf_name = ContactsNameCustomField.objects.filter(
                filter_conditions
            ).order_by("-created_at")

            res = []
            ITEMS_PER_PAGE = 30
            cf_name_paginator = Paginator(cf_name, ITEMS_PER_PAGE)
            paginated_cf_name = []
            more_pagination = False
            if page:
                try:
                    cf_name_page_content = cf_name_paginator.page(page if page else 1)
                    paginated_cf_name = cf_name_page_content.object_list
                    more_pagination = cf_name_page_content.has_next()
                except EmptyPage:
                    pass

            for cf in paginated_cf_name:
                try:
                    text = f"{cf.name}"
                except Exception:
                    pass

                res.append(
                    {
                        "id": str(cf.id),
                        "text": text,
                    }
                )

            return JsonResponse(
                {"results": res, "pagination": {"more": more_pagination}}
            )

    conditions = Q(workspace=workspace)
    company_conditions = conditions
    if q:
        conditions &= (
            Q(contact_id__icontains=q)
            | Q(name__icontains=q)
            | Q(last_name__icontains=q)
            | Q(email__icontains=q)
            | Q(phone_number__icontains=q)
        )
        company_conditions &= (
            Q(company_id__icontains=q)
            | Q(name__icontains=q)
            | Q(email__icontains=q)
            | Q(phone_number__icontains=q)
        )

    contacts = (
        Contact.objects.filter(conditions, status="active")
        .order_by("-contact_id")
        .distinct()
    )
    companies = (
        Company.objects.filter(company_conditions, status="active")
        .order_by("-company_id")
        .distinct()
    )

    if "exclude_company" in request.GET:
        exclude_company = request.GET.get("exclude_company")
        companies = companies.exclude(id=exclude_company)

    ITEMS_PER_PAGE = 30
    contacts_paginator = Paginator(contacts, ITEMS_PER_PAGE)
    paginated_contacts = []
    more_pagination = False
    if page:
        try:
            contact_page_content = contacts_paginator.page(page if page else 1)
            paginated_contacts = contact_page_content.object_list
            more_pagination = contact_page_content.has_next()
        except EmptyPage:
            pass

    companies_paginator = Paginator(companies, ITEMS_PER_PAGE)
    paginated_companies = []
    if page:
        try:
            company_page_content = companies_paginator.page(page if page else 1)
            paginated_companies = company_page_content.object_list
            more_pagination = more_pagination or company_page_content.has_next()
        except EmptyPage:
            pass

    res = []
    customer_type = "(連絡先)" if lang == "ja" else "(Contact)"

    if "company_only" not in request.GET:
        for contact in paginated_contacts:
            full_name = ""
            first_name = ""
            last_name = ""

            if contact.contact_id:
                contact_id = f"{contact.contact_id:04}"
            else:
                contact_id = ""

            if contact.name:
                first_name = contact.name
            else:
                first_name = ""

            if contact.last_name:
                last_name = contact.last_name
            else:
                last_name = ""

            if lang == "ja":
                full_name = (
                    "#" + contact_id + " " + last_name + " " + first_name + " | "
                )
            else:
                full_name = (
                    "#" + contact_id + " " + first_name + " " + last_name + " | "
                )

            res.append(
                {
                    "id": "contact|" + str(contact.id) if usage else str(contact.id),
                    "text": full_name + " " + customer_type,
                    "data_type": customer_type,
                }
            )

    customer_type = "(企業)" if lang == "ja" else "(Company)"

    if "contact_only" not in request.GET:
        company_res = []
        for company in paginated_companies:
            fullname = f"#{company.company_id:04} | "
            if company.name:
                fullname = fullname + " " + company.name
            company_res.append(
                {
                    "id": "company|" + str(company.id) if usage else str(company.id),
                    "text": fullname + " " + customer_type,
                    "data_type": customer_type,
                }
            )
        company_res.extend(res)
        res = company_res

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


@login_or_hubspot_required
def contact_row_detail(request, id):
    workspace = get_workspace(request.user)
    contact = get_object_or_404(Contact, id=id)

    view_id = request.GET.get("view_id", None)
    target = request.GET.get("target", None)
    column_view = []

    # Fix: Handle target parameter more robustly
    if target and "contacts" in target:
        column_view = DEFAULT_COLUMNS_CONTACTS.copy()
    else:
        # Fallback to default columns if target is None or doesn't contain "contacts"
        column_view = DEFAULT_COLUMNS_CONTACTS.copy()

    try:
        view_filter = modular_view_filter(
            workspace,
            target,
            view_id=view_id,
            column_view=column_view,
            user=request.user,
        )
        contacts_columns = view_filter.column

        # Log column configuration for debugging
        logger.info(
            "contact_row_detail - contact_id: %s, view_id: %s, columns: %s",
            contact.contact_id,
            view_id,
            contacts_columns,
        )

    except Exception as e:
        # Fallback to default columns if modular_view_filter fails
        logger.error(f"modular_view_filter failed for contact {id}: {e}")
        contacts_columns = DEFAULT_COLUMNS_CONTACTS.copy()

        # Ensure checkbox is included if it should be
        if "checkbox" not in contacts_columns:
            contacts_columns.insert(0, "checkbox")

    # Ensure column consistency - add checkbox if missing and expected
    if contacts_columns and "checkbox" not in contacts_columns:
        # Check if the main view expects checkbox by looking at the first column
        main_view_has_checkbox = request.GET.get("has_checkbox", "false") == "true"
        if main_view_has_checkbox:
            contacts_columns.insert(0, "checkbox")

    property_sets = PropertySet.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_CONTACT
    ).order_by("-created_at")

    context = {
        "object_type": TYPE_OBJECT_CONTACT,
        "contact": contact,
        "contacts_columns": contacts_columns,
        "view_id": view_id,
        "selected_contact_id": request.GET.get("selected_contact_id"),
        "page": request.GET.get("page", 1),
        "property_sets": property_sets,
    }
    return render(request, "data/contacts/contact-row-partial.html", context)


@login_or_hubspot_required
def company_row_detail(request, id):
    workspace = get_workspace(request.user)
    company = get_object_or_404(Company, id=id)

    view_id = request.GET.get("view_id", None)
    target = request.GET.get("target", None)
    column_view = []

    if "company" in target:
        column_view = DEFAULT_COLUMNS_COMPANY.copy()

    view_filter = modular_view_filter(
        workspace, target, view_id=view_id, column_view=column_view
    )

    companies_columns = view_filter.column

    property_sets = PropertySet.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_COMPANY
    ).order_by("-created_at")
    context = {
        "object_type": TYPE_OBJECT_COMPANY,
        "company": company,
        "companies_columns": companies_columns,
        "view_id": view_id,
        "selected_company_id": request.GET.get("selected_company_id"),
        "page": request.GET.get("page", 1),
        "property_sets": property_sets,
    }
    return render(request, "data/contacts/company-row-partial.html", context)


def activity_log_view(request):
    workspace = get_workspace(request.user)
    obj_id = request.GET.get("id", None)
    obj_type = request.GET.get("obj_type", None)
    obj = None
    app_logs = AppLog.objects.none()  # Initialize with empty queryset

    if not (obj_type and obj_id):
        return HttpResponse(200)

    filter_conditions = ~Q(new_value=models.F("old_value"))

    if obj_type == TYPE_OBJECT_CASE:
        obj = Deals.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, deal=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_CONVERSATION:
        obj = MessageThread.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, message=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_CONTACT:
        obj = Contact.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, contact=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_COMPANY:
        obj = Company.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, company=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_ORDER:
        obj = ShopTurboOrders.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, order=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_SUBSCRIPTION:
        obj = ShopTurboSubscriptions.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, subscriptions=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_ITEM:
        obj = ShopTurboItems.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, item=obj) & filter_conditions
        ).order_by("-created_at")

    elif obj_type == TYPE_OBJECT_ESTIMATE:
        obj = Estimate.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, estimate=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_DELIVERY_NOTE:
        obj = DeliverySlip.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, deliveryslip=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_INVOICE:
        obj = Invoice.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, invoice=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_RECEIPT:
        obj = Receipt.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, receipt=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_SLIP:
        obj = Slip.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, slip=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_INVENTORY:
        obj = ShopTurboInventory.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, inventory=obj) & filter_conditions
        ).order_by("-created_at")

    elif obj_type == TYPE_OBJECT_PURCHASE_ORDER:
        obj = PurchaseOrders.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, purchaseorders=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_BILL:
        obj = Bill.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, bill=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_EXPENSE:
        obj = Expense.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, expense=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_TASK:
        if not obj_id:
            return HttpResponse(200)
        obj = Task.objects.filter(id=obj_id).first()
        if not obj:
            return HttpResponse(200)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, task=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_INVENTORY_WAREHOUSE:
        obj = InventoryWarehouse.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, location=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
        obj = InventoryTransaction.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, inventorytransaction=obj) & filter_conditions
        ).order_by("-created_at")
    elif obj_type == TYPE_OBJECT_JOURNAL:
        obj = JournalEntry.objects.get(id=obj_id)
        app_logs = AppLog.objects.filter(
            Q(workspace=workspace, journal=obj) & filter_conditions
        ).order_by("-created_at")
    else:
        # Handle unknown object types gracefully
        # app_logs is already initialized to empty queryset at the beginning
        pass

    paginator = Paginator(app_logs, 30)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    context = {"page_obj": page_obj, "obj": obj, "obj_type": obj_type}
    return render(request, "data/common/activity_log.html", context)


def upload_wholesale_price(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        context = {
            "action_index": request.GET.get("action_index", None),
        }

        action_node_id = request.GET.get("action_node_id")
        if action_node_id:
            node = ActionNode.objects.filter(id=action_node_id).first()
            node.valid_to_run = True
            node.save()
            if node.input_data:
                if "customer" in node.input_data:
                    context["customer"] = node.input_data["customer"]

                if "csv_file_id" in node.input_data:
                    if node.input_data["csv_file_id"]:
                        csv_file = GeneralStorage.objects.get(
                            id=node.input_data["csv_file_id"]
                        )
                        context["csv_file"] = csv_file

        return render(request, "data/contacts/upload-wholesale.html", context)
    else:
        # Add save section for workflow =====================
        action_node_id = request.POST.get("action_node_id")
        action_index = request.POST.get("action_index")
        at_id = request.POST.get("action_tracker_id")
        wat_id = request.POST.get("workflow_action_tracker_id")
        submit_option = request.POST.get("submit-option")

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except ActionNode.DoesNotExist:
                print("ActionNode does not exist")
                return HttpResponse(status=404)

        at = None
        if at_id:
            try:
                at = ActionTracker.objects.get(id=at_id)
                if at.workspace:
                    workspace = at.workspace
            except ActionTracker.DoesNotExist:
                print("ActionTracker does not exist")
                return HttpResponse(status=404)

        wat = None
        if wat_id:
            try:
                wat = WorkflowActionTracker.objects.get(id=wat_id)
            except WorkflowActionTracker.DoesNotExist:
                print("WorkflowActionTracker does not exist")
                return HttpResponse(status=404)

        # Text Field
        postfix = ""
        if action_index:
            postfix = "-" + action_index
        action_name = request.POST.get("action_name" + postfix, None)

        customer = request.POST.get("customer" + postfix, None)
        csv_file_input = request.POST.get("csv_file_input" + postfix, None)
        csv_file = request.FILES.get("csv_file" + postfix, None)
        if submit_option == "save":
            input_data = {}
            node.valid_to_run = True

            if csv_file:
                csv_file_id = GeneralStorage.objects.create(
                    file=csv_file, user=request.user
                )
                csv_file_id = str(csv_file_id.id)
                input_data["customer"] = customer
                input_data["csv_file_id"] = csv_file_id

            if not csv_file:
                if not csv_file_input:
                    node.valid_to_run = False
                else:
                    csv_file_id = GeneralStorage.objects.get(id=csv_file_input)
                    csv_file_id = str(csv_file_id.id)
                    input_data["customer"] = customer
                    input_data["csv_file_id"] = csv_file_id

            if action_name:
                input_data["action_name"] = action_name
            node.input_data = input_data
            node.predefined_input = input_data
            node.save()

            print(node.valid_to_run)

            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module_slug:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif submit_option == "run":
            if "csv_file_id" in node.input_data:
                csv_file_id = node.input_data["csv_file_id"]

            if "customer" in node.input_data:
                customer = node.input_data["customer"]

            try:
                csv_file = GeneralStorage.objects.get(id=csv_file_id)
                csv_file = csv_file.file
            except:
                csv_file = None
                at.status = "failed"
                at.completed_at = timezone.now()
                at.save()
                return HttpResponse(500)

            # ### ============= Add save section for workflow ===================== ####\

            try:
                df = read_csv(csv_file)
                df = df.astype(str)
                for _, row in df.iterrows():
                    customer_id = row[0]
                    item_id = row[1]
                    new_price = row[2]
                    tax = row[3]

                    if not customer and not customer_id or not item_id:
                        continue

                    print(
                        customer,
                        " ",
                        customer_id,
                        " ",
                        item_id,
                        " ",
                        new_price,
                        " ",
                        tax,
                    )

                    obj_type = TYPE_OBJECT_COMPANY
                    if customer == TYPE_OBJECT_CONTACT:
                        obj_type = TYPE_OBJECT_CONTACT

                    if obj_type == TYPE_OBJECT_COMPANY:
                        customer_obj = Company.objects.filter(
                            workspace=workspace, company_id=customer_id
                        ).first()
                    else:
                        customer_obj = Contact.objects.filter(
                            workspace=workspace, contact_id=customer_id
                        ).first()

                    item = ShopTurboItems.objects.get(
                        workspace=workspace, item_id=item_id
                    )

                    if customer_obj and item:
                        if obj_type == TYPE_OBJECT_CONTACT:
                            customer_item_price, _ = (
                                ShopTurboCustomerItemsPrice.objects.get_or_create(
                                    item=item, contact=customer_obj
                                )
                            )
                            customer_item_price.target = TYPE_OBJECT_CONTACT
                        elif obj_type == TYPE_OBJECT_COMPANY:
                            customer_item_price, _ = (
                                ShopTurboCustomerItemsPrice.objects.get_or_create(
                                    item=item, company=customer_obj
                                )
                            )
                            customer_item_price.target = TYPE_OBJECT_COMPANY

                        if customer_item_price and new_price:
                            if new_price == "nan":
                                new_price = 0
                            customer_item_price.price = new_price
                            if item.currency:
                                customer_item_price.currency = item.currency
                            else:
                                if lang == "ja":
                                    customer_item_price.currency = "JPY"
                                else:
                                    customer_item_price.currency = "USD"

                                item.currency = customer_item_price.currency
                                item.save()

                            if tax == "nan":
                                tax = 0
                            customer_item_price.tax = tax
                            customer_item_price.save()
                        else:
                            customer_item_price.delete()

            except Exception as e:
                at.status = "failed"
                at.completed_at = timezone.now()

                at.output_data = {f"{'エラー' if lang == 'ja' else 'Error'}": str(e)}
                at.save()
                return HttpResponse(500)

            at.status = "success"
            at.completed_at = timezone.now()
            at.save()

            next_node = None
            if node:
                next_node = node.next_node
                at = ActionTracker.objects.filter(id=at.id).first()
                if at:
                    at.input_data = {
                        f"{'顧客' if lang == 'ja' else 'Customer'}": str(customer),
                        f"{'CSVファイル' if lang == 'ja' else 'CSV File'}": csv_file.file.name,
                    }
                    at.output_data = {}
                    at.save()
            if next_node:
                next_at = ActionTracker.objects.get(
                    node=next_node, workflow_action_tracker=wat
                )
                transfer_output_to_target_input(at, next_at)
                trigger_next_action(
                    current_action_tracker=at,
                    workflow_action_tracker=wat,
                    current_node=node,
                    user_id=str(request.user.id),
                    lang=lang,
                )

            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_WORKFLOW]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_WORKFLOW
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module_slug:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )

        return HttpResponse(200)


def price_table_checking(request):
    contact_or_company_id = request.POST.get("contact_or_company_id", None)
    has_price_table = False

    if contact_or_company_id:
        customer_type = TYPE_OBJECT_COMPANY
        customer = Company.objects.filter(id=contact_or_company_id).first()
        if not customer:
            customer_type = TYPE_OBJECT_CONTACT
            customer = Contact.objects.filter(id=contact_or_company_id).first()

        if customer_type == TYPE_OBJECT_COMPANY:
            if CompanyValueCustomField.objects.filter(
                company=customer, field_name__type="price-table"
            ):
                has_price_table = True

        elif customer_type == TYPE_OBJECT_CONTACT:
            if ContactsValueCustomField.objects.filter(
                contact=customer, field_name__type="price-table"
            ):
                has_price_table = True

    return JsonResponse(
        {
            "has_price_table": has_price_table,
        }
    )


def fetch_customer_points(request):
    contact_id = request.POST.get("contact_id", None)

    if contact_id:
        contact = Contact.objects.filter(id=contact_id).first()
        points = ContactsValueCustomField.objects.filter(
            field_name__type="point", contact=contact
        ).first()
        if points:
            point_field = points.field_name.name
            points = points.value
            return JsonResponse({"name": point_field, "points": points})

    return JsonResponse({"name": "", "points": 0})


@login_or_hubspot_required
@require_GET
def autocomplete_contact(request):
    lang = request.LANGUAGE_CODE
    if not lang:
        lang = "ja"

    workspace = get_workspace(request.user)
    search = request.GET.get("search", "")
    ids = request.GET.getlist("ids[]", "")

    # Filter out invalid IDs (empty strings, "None", etc.) and validate UUIDs
    valid_ids = []
    if ids:
        for id_value in ids:
            if id_value and id_value != "None" and is_valid_uuid(id_value):
                valid_ids.append(id_value)

    filter_condition = Q(workspace=workspace) & ~Q(status="archived")
    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_CONTACT, user=request.user)
    filter_condition &= get_permission_filter(
        permission, request.user, permission_type="edit"
    )

    page = int(request.GET.get("page", 1))

    results_per_page = 10 if len(valid_ids) <= 0 else len(valid_ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target=CONTACT_APP_TARGET
        ).first()

        search_filters = Q()
        search_filters |= Q(contact_id__icontains=search)

        if app_setting:
            if app_setting.search_setting_contact:
                search_fields = app_setting.search_setting_contact.split(",")
                search_fields = [
                    "name" if field == "first_name" else field
                    for field in search_fields
                ]

                view_filter = modular_view_filter(
                    workspace,
                    TYPE_OBJECT_COMPANY,
                    view_id=None,
                    column_view=DEFAULT_COLUMNS_CONTACTS.copy(),
                )

                for s_field in search_fields:
                    search_filters |= apply_search_setting(
                        "contacts", view_filter, s_field, search
                    )

            filter_condition &= search_filters

    if valid_ids:
        filter_condition &= Q(id__in=valid_ids)

    results = Contact.objects.filter(filter_condition).order_by("-created_at")[
        start:end
    ]

    data = []
    for contact in results:
        full_name = ""
        first_name = ""
        last_name = ""

        if contact.contact_id:
            contact_id = f"{contact.contact_id:04}"
        else:
            contact_id = ""

        if contact.name:
            first_name = contact.name
        else:
            first_name = ""

        if contact.last_name:
            last_name = contact.last_name
        else:
            last_name = ""

        if lang == "ja":
            full_name = "#" + contact_id + " " + last_name + " " + first_name
        else:
            full_name = "#" + contact_id + " " + first_name + " " + last_name

        data.append(
            {
                "id": contact.id,
                "text": full_name,
            }
        )

    return JsonResponse({"results": data, "pagination": {"more": results.exists()}})


def contact_association_drawer(request):
    if request.method == "GET":
        source = request.GET.get("source", None)
        object_id = request.GET.get("object_id", None)
        page = request.GET.get("page", 1)
        view_id = request.GET.get("view_id", None)
        module = request.GET.get("module", None)
        from_object = request.GET.get("from", None)
        print("[DEBUG] module contact_association", module)

        if source == TYPE_OBJECT_COMPANY:
            obj = Company.objects.get(id=object_id)
        elif source == TYPE_OBJECT_CUSTOM_OBJECT:
            custom_object_id = request.GET.get("custom_object_id")
            custom_object = CustomObject.objects.get(id=custom_object_id)
            obj = CustomObjectPropertyRow.objects.get(id=object_id)
        elif source == TYPE_OBJECT_SUBSCRIPTION:
            obj = ShopTurboSubscriptions.objects.get(id=object_id)
        elif source == TYPE_OBJECT_ORDER:
            obj = ShopTurboOrders.objects.get(id=object_id)

        context = {
            "source": source,
            "object_id": object_id,
            "page": page,
            "view_id": view_id,
            "obj": obj,
            "module": module,
        }
        if source == TYPE_OBJECT_CUSTOM_OBJECT:
            context["custom_object_id"] = custom_object.id
        if from_object:
            context["from"] = from_object
        return render(
            request, "data/association/default-create-add/contact.html", context
        )
    else:
        return HttpResponse(200)


def calculate_math(formula_custom_field, model, type):
    result = 0
    try:
        formula_raws = [
            data["value"]
            for data in ast.literal_eval(formula_custom_field.choice_value)
        ]
        # Checking row on custom field
        formula_as_number = []

        if type == "contact":
            for formula_entity in formula_raws:
                shopturboordersnamecustomfield = ContactsNameCustomField.objects.filter(
                    workspace=model.workspace, type="number", name=formula_entity
                ).last()
                if shopturboordersnamecustomfield:
                    value_custom_field = ContactsValueCustomField.objects.filter(
                        field_name=shopturboordersnamecustomfield, contact=model
                    ).last()
                    if value_custom_field:
                        if value_custom_field.value:
                            formula_as_number.append(float(value_custom_field.value))
                        else:
                            formula_as_number.append(float(0))
                    else:
                        formula_as_number.append(float(0))

                elif (
                    "sum" in formula_entity.lower() or "count" in formula_entity.lower()
                ):
                    if (
                        "orders" in formula_entity.lower()
                        and "sum" in formula_entity.lower()
                    ):
                        if "total_price" in formula_entity.lower():
                            # sum the orders
                            number = model.shopturboorders_set.aggregate(
                                total_price_sum=Sum("total_price")
                            )["total_price_sum"]
                            if number:
                                formula_as_number.append(float(number))

                    elif (
                        "orders" in formula_entity.lower()
                        and "count" in formula_entity.lower()
                    ):
                        number = model.shopturboorders_set.all().count()
                        if number:
                            formula_as_number.append(float(number))

                else:
                    formula_as_number.append(formula_entity)

        # Calculate
        # Iterate through the list
        current_operator = None
        operand = 0
        for idx, entity in enumerate(formula_as_number):
            if entity:
                # warm_up
                if idx == 0:
                    if entity not in ("+", "-", "*", "/"):
                        result = float(entity)

                # Check if the item is an operator
                if isinstance(entity, str) and entity in ("+", "-", "*", "/"):
                    current_operator = entity
                # If there was a previous operator, perform the operation
                else:
                    operand = float(entity)
                    if current_operator:
                        if current_operator == "+":
                            result = result + operand if result is not None else operand
                        elif current_operator == "-":
                            result = (
                                result - operand if result is not None else -operand
                            )
                        elif current_operator == "*":
                            result = result * operand if result is not None else 0
                        elif current_operator == "/":
                            # Ensure not dividing by zero
                            if operand != 0:
                                result = result / operand if result is not None else 0
    except:
        pass

    return result


def add_to_contact_list(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE

    if request.method == "GET":
        # module_slug = request.GET.get('module')
        # view_id = request.GET.get('view_id')
        is_record_action = request.GET.get("is_record_action")
        contact_ids = request.GET.get("contact_ids", None)
        # Required
        action_index = request.GET.get("action_index")
        action_node_id = request.GET.get("action_node_id")

        postfix = ""
        if action_index:
            postfix = "-" + str(action_index)
        # action_slug = request.GET.get('action_id' + postfix)

        node = None
        if action_node_id:
            try:
                node = ActionNode.objects.get(id=action_node_id)
            except:
                print("Action node does not exist")
                return HttpResponse(status=404)

        print("is_record_action: ", is_record_action)
        print("contact_ids: ", contact_ids)
        if contact_ids:
            contact_ids = ast.literal_eval(contact_ids)
        context = {
            "contact_lists": ContactList.objects.filter(workspace=workspace),
            "contact_ids": contact_ids,
            "is_record_action": is_record_action,
        }
        return render(request, "data/contacts/add-to-contact-list.html", context)

    else:
        contact_list_id = request.POST.get("contact_list", None)
        contact_ids = request.POST.getlist("contact_ids", None)
        is_record_action = request.POST.get("is_record_action", "")

        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CONTACT]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_CONTACT
            )
            .order_by("order", "created_at")
            .first()
        )
        module_slug = None
        if module:
            module_slug = module.slug

        print("contact_ids: ", contact_ids)
        print("contact_list_id: ", contact_list_id)
        print("is_record_action: ", is_record_action)

        task_id = request.POST.get("action_id", None)
        action = Action.objects.filter(slug=task_id).first()
        history = ActionHistory.objects.create(
            workspace=workspace,
            action=action,
            status="initialized",
            created_by=request.user,
        )
        if is_record_action:
            history.object_type = TYPE_OBJECT_CONTACT
            history.object_id = contact_ids[0]
            history.save()

        if contact_list_id and contact_ids:
            contact_list = ContactList.objects.get(id=contact_list_id)
            for contact_id in contact_ids:
                contact = Contact.objects.get(id=contact_id)
                contact_list.contacts.add(contact)

                try:
                    # Try to get or create the custom field value
                    custom_field_value, _ = (
                        ContactsValueCustomField.objects.get_or_create(
                            field_name__workspace=workspace,
                            field_name=contact_list.contact_name_customfield,
                            contact=contact,
                        )
                    )

                    if custom_field_value.value:
                        custom_field_value.value = ast.literal_eval(
                            custom_field_value.value
                        )
                        if any(
                            d["id"] == contact_list.id for d in custom_field_value.value
                        ):
                            continue
                        custom_field_value.value.append(
                            {"value": contact_list.name, "id": str(contact_list.id)}
                        )
                    else:
                        custom_field_value.value = [
                            {"value": contact_list.name, "id": str(contact_list.id)}
                        ]
                    custom_field_value.save()

                except ContactsValueCustomField.MultipleObjectsReturned:
                    # Handle duplicates - keep the most recent one, delete others
                    custom_field_values = ContactsValueCustomField.objects.filter(
                        field_name__workspace=workspace,
                        field_name=contact_list.contact_name_customfield,
                        contact=contact,
                    ).order_by("-created_at")

                    # Keep the first (most recent) record
                    custom_field_value = custom_field_values.first()
                    if custom_field_value.value:
                        custom_field_value.value = ast.literal_eval(
                            custom_field_value.value
                        )
                        if any(
                            d["id"] == contact_list.id for d in custom_field_value.value
                        ):
                            continue
                        custom_field_value.value.append(
                            {"value": contact_list.name, "id": str(contact_list.id)}
                        )
                    else:
                        custom_field_value.value = [
                            {"value": contact_list.name, "id": str(contact_list.id)}
                        ]
                    custom_field_value.save()

                    # Delete the duplicates
                    custom_field_values.exclude(id=custom_field_value.id).delete()

        history.status = "success"
        history.completed_at = timezone.now()
        history.save()
        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
                + "?open_drawer=action_drawer_history"
            )
        return redirect(reverse("main", host="app"))


@login_or_hubspot_required
@require_GET
def autocomplete_company(request):
    lang = request.LANGUAGE_CODE
    if not lang:
        lang = "ja"

    workspace = get_workspace(request.user)
    search = request.GET.get("search", "")
    ids = request.GET.getlist("ids[]", "")

    # Filter out invalid IDs (empty strings, "None", etc.) and validate UUIDs
    valid_ids = []
    if ids:
        for id_value in ids:
            if id_value and id_value != "None" and is_valid_uuid(id_value):
                valid_ids.append(id_value)

    filter_condition = Q(workspace=workspace) & ~Q(status="archived")
    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_COMPANY, user=request.user)
    filter_condition &= get_permission_filter(
        permission, request.user, permission_type="edit"
    )

    page = int(request.GET.get("page", 1))

    results_per_page = 10 if len(valid_ids) <= 0 else len(valid_ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        app_setting = AppSetting.objects.filter(
            workspace=workspace, app_target=CONTACT_APP_TARGET
        ).first()

        search_filters = Q()
        search_filters |= Q(contact_id__icontains=search)

        if app_setting:
            if app_setting.search_setting_company:
                search_fields = app_setting.search_setting_company.split(",")
                search_fields = [
                    "name" if field == "first_name" else field
                    for field in search_fields
                ]

                view_filter = modular_view_filter(
                    workspace,
                    TYPE_OBJECT_COMPANY,
                    view_id=None,
                    column_view=DEFAULT_COLUMNS_CONTACTS.copy(),
                )

                for s_field in search_fields:
                    search_filters |= apply_search_setting(
                        "company", view_filter, s_field, search
                    )

            filter_condition &= search_filters

    if valid_ids:
        filter_condition &= Q(id__in=valid_ids)

    results = Company.objects.filter(filter_condition).order_by("-created_at")[
        start:end
    ]

    data = []
    for company in results:
        company_id = ""
        name = ""
        if company.company_id:
            company_id = f"{company.company_id:04}"
        else:
            company_id = ""

        if company.name:
            name = company.name
        else:
            name = ""

        full_name = "#" + company_id + " " + name

        data.append(
            {
                "id": company.id,
                "text": full_name,
            }
        )

    return JsonResponse({"results": data, "pagination": {"more": results.exists()}})
