import csv
import traceback
import json
from io import String<PERSON>
from typing import Optional, List
from datetime import timedelta
from pydantic import BaseModel, Field
from hatchet_sdk import Context

from utils.bgjobs.hatchet_client import hatchet
from utils.logger import logger

from django.core.mail import EmailMessage
from django.db.models import Q
from django.template.loader import render_to_string
from django.utils import timezone
from django.conf import settings

from data.constants.date_constant import *
from data.constants.properties_constant import *
from data.models import *
from utils.contact import (get_company_downloadable_value,
                           get_contact_downloadable_value)
from utils.date import format_date
from utils.filter import build_view_filter
from utils.utility import is_valid_uuid, remove_unsupported_characters
from utils.bgjobs.handler import set_bg_job_running, set_bg_job_completed, set_bg_job_failed

from utils.hubspot import export_hubspot_cases

class ExportCasesPayload(BaseModel):
    user: str
    workspace_id: str
    platform: str
    channel_id: str
    case_ids: List[str]
    mapping_custom_fields: Optional[str] = None
    history_id: str
    lang: Optional[str] = Field(default="ja")
    background_job_id: Optional[str] = Field(default="")

@hatchet.task(name="ExportCases", input_validator=ExportCasesPayload, execution_timeout=timedelta(hours=2), schedule_timeout=timedelta(hours=1))
def export_cases(input: ExportCasesPayload, ctx: Context):
    # Extract parameters from the input payload
    user_id = input.user
    workspace_id = input.workspace_id
    platform = input.platform
    channel_id = input.channel_id
    case_ids = input.case_ids
    mapping_custom_fields = input.mapping_custom_fields
    history_id = input.history_id
    lang = input.lang or 'ja'
    background_job_id = input.background_job_id

    # Set background job as running
    if background_job_id:
        set_bg_job_running(background_job_id)

    logger.info(
        f"Starting cases export with parameters: workspace_id={workspace_id},history_id={history_id}")

    try:
        history = TransferHistory.objects.get(id=history_id)
    except TransferHistory.DoesNotExist as e:
        logger.error(f"TransferHistory does not exist: {e}")
        if background_job_id:
            set_bg_job_failed(background_job_id)
        return

    try:
        workspace = Workspace.objects.get(id=workspace_id)
    except Workspace.DoesNotExist as e:
        logger.error(f"Workspace does not exist: {e}")
        history.status = 'canceled'
        history.save()
        if background_job_id:
            set_bg_job_failed(background_job_id)
        return

    try:
        user = User.objects.get(id=int(user_id))
    except User.DoesNotExist as e:
        logger.error(f"User does not exist: {e}")
        history.status = 'canceled'
        history.save()
        if background_job_id:
            set_bg_job_failed(background_job_id)
        return

    if mapping_custom_fields:
        try:
            mapping_custom_fields = json.loads(mapping_custom_fields)
        except Exception as e:
            logger.error(f"Error parsing mapping_custom_fields: {e}")
            mapping_custom_fields = {}
    try:
        export_hubspot_cases(channel_id, case_ids, mapping_custom_fields, history_id, lang)

    except Exception as e:
        if lang == 'ja':
            Notification.objects.create(
                workspace=workspace, user=user, message=f"案件のエクスポートに失敗しました。サポートにお問い合わせください。 |{e}", type="error")
        else:
            Notification.objects.create(
                workspace=workspace, user=user, message=f"Export Case Failed. Please contact support. | {e}", type="error")

        Notification.objects.create(
            workspace=workspace, user=user, message=f"{str(traceback.format_exc())}", type="error")

        history.name = 'Export Case'
        history.status = 'failed'
        history.progress = 0
        history.save()

        # Mark background job as failed
        if background_job_id:
            set_bg_job_failed(background_job_id)

        logger.error(f"Case export failed for user {user.email}: {str(e)}")
        return False
