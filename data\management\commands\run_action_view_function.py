import ast
import json
import traceback

from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.http import HttpRequest
from django.urls import resolve
from django_hosts.resolvers import reverse

from data.management.commands.base_bg_job import BaseBgJobCommand
from utils.logger import logger


class Command(BaseBgJobCommand):
    """
    Generic command to run any view function as a background job.

    This command dynamically resolves a view function based on the action slug
    and executes it with the provided form data.
    """

    help = "Run a view function as a background job"

    def add_command_arguments(self, parser):
        """Add command-specific arguments"""
        parser.add_argument(
            "--action_slug",
            type=str,
            required=True,
            help="The slug of the action to run",
        )
        parser.add_argument(
            "--form_data",
            type=str,
            required=False,
            help="JSON-encoded form data to pass to the view function",
        )
        parser.add_argument("--background_job_id", type=str, default="")

    def process_command(self, workspace, user, at, wat, node, lang, options):
        """Process the command by dynamically resolving and calling the view function"""
        try:
            logger.info("=== DEBUG: process_command started ===")
            logger.info(f"Workspace: {workspace}")
            logger.info(f"User: {user}")
            logger.info(f"Options: {options}")

            action_slug = options.get("action_slug")
            logger.info(f"Action slug: {action_slug}")

            # If node is provided, use its action slug
            if node and node.action:
                action_slug = node.action.slug
                logger.info(f"Action slug from node: {action_slug}")

            if not action_slug:
                logger.error("No action slug provided")
                return {
                    "success": False,
                    "error_message": "No action slug provided",
                    "error_message_en": "No action slug provided",
                    "error_message_ja": "アクションスラグが提供されていません",
                }

            # Create a mock request to pass to the view function
            request = HttpRequest()
            request.method = "POST"
            request.user = user if user else AnonymousUser()
            request.workspace = workspace
            request.LANGUAGE_CODE = lang

            # Add POST data to the request
            request.POST = {}

            # Add common form data
            if at:
                request.POST["action_tracker_id"] = str(at.id)
            if wat:
                request.POST["workflow_action_tracker_id"] = str(wat.id)
            if node:
                request.POST["action_node_id"] = str(node.id)

            # Add any additional form data that was passed
            if "form_data" in options and options["form_data"]:
                try:
                    additional_form_data = json.loads(options["form_data"])
                except Exception:
                    logger.warning(
                        f"Could not parse form_data as JSON: {options['form_data']}. Continue without parsing. {traceback.format_exc()}"
                    )
                    if isinstance(options["form_data"], str):
                        additional_form_data = ast.literal_eval(options["form_data"])
                    else:
                        additional_form_data = options["form_data"]
                    logger.debug("additional_form_data", additional_form_data)
                for key, value in additional_form_data.items():
                    logger.debug(key, value)
                    request.POST[key] = value
            logger.info("loaded request POST", request.POST)

            # Dynamically resolve the view function
            try:
                view_func = resolve(
                    reverse("action_form_mapper", host="app")[2:].replace(
                        "app." + settings.PARENT_HOST, ""
                    )
                    + action_slug
                    + "/",
                    urlconf="data.urls",
                ).func

                # Call the view function with the mock request
                logger.info(f"Calling view function for action: {action_slug}")
                response = view_func(request)

                # Check response status
                if hasattr(response, "status_code") and response.status_code >= 400:
                    result = {
                        "success": False,
                        "error_message": f"View function returned error status: {response.status_code}",
                        "error_message_en": f"Failed to process action {action_slug}",
                        "error_message_ja": f"アクション {action_slug} の処理に失敗しました",
                    }
                    logger.error(json.dumps(result))
                    return result

                # Return success
                result = {
                    "success": True,
                    "output_data": {
                        "action_slug": action_slug,
                        "response_status": getattr(response, "status_code", 200),
                    },
                    "success_message_en": f"Successfully processed action {action_slug}",
                    "success_message_ja": f"アクション {action_slug} を正常に処理しました",
                }
                logger.info(json.dumps(result))
                return result
            except Exception as e:
                logger.error(
                    f"Error resolving or calling view function: {traceback.format_exc()}"
                )
                return {
                    "success": False,
                    "error_message": f"Error resolving or calling view function: {str(e)}",
                    "error_message_en": f"Failed to resolve or call view function for action {action_slug}",
                    "error_message_ja": f"アクション {action_slug} の関数の解決または呼び出しに失敗しました",
                }

        except Exception as e:
            logger.error(f"Error running view function: {traceback.format_exc()}")
            return {
                "success": False,
                "error_message": f"Error running view function: {str(e)}",
                "error_message_en": "Failed to run view function",
                "error_message_ja": "ビュー関数の実行に失敗しました",
            }
