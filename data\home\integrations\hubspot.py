import json
import random
import traceback
from datetime import datetime
from urllib.parse import unquote, urlencode, urlparse
from uuid import UUID, uuid4

import requests
from requests.auth import HTTPBasicAuth
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import login
from django.contrib.auth.models import User
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import redirect, render
from django.utils import timezone, translation
from django.utils.encoding import force_bytes
from django.utils.http import (url_has_allowed_host_and_scheme,
                               urlsafe_base64_decode, urlsafe_base64_encode)
from django.views.decorators.csrf import csrf_exempt
from django_hosts.resolvers import reverse
from hubspot import HubSpot
from data.constants.properties_constant import *
from data.models import (
    Notification, Integration, Channel, Verification, HubspotUser,
    IntegrationApp, PixelAllowedDomain, QRBotAccess, GoogleOauth, Page2, Workspace,
    User, MessageThread, ShopTurboOrders, ShopTurboInventory, ShopTurboSubscriptions,
    Contact, Company, Task, OBJECT_SLUG_TO_OBJECT_TYPE, TYPE_OBJECT_FORM,
    Workflow, TransferHistory, BackgroundJob, Error, Log,
    Module, AppSetting, ObjectManager, View, ViewFilter, Group, ContactsNameCustomField,
    ContactsMappingFields, GeneralStorage, Action, UserManagementNameCustomField,
    InventoryTransactionNameCustomField, ContactsPlatforms, ContractDocument,
    DEFAULT_COLUMNS_CONTACTS, DEFAULT_COLUMNS_COMPANY, ShopTurboOrdersPlatforms,
    ContactsValueCustomField, CompanyValueCustomField, ShopTurboOrdersValueCustomField,
    CompanyNameCustomField, ShopTurboOrdersNameCustomField, ShopTurboSubscriptionPlatforms,
    BasePricing, MONTHLY, HOLIDAY_COUNTRY_CODE, ConversationLog, OBJECT_GROUP_TYPE,
    DEFAULT_PERMISSION, Message, Invitation, ContactLineChannel, DataTransfer,
    TASK_STATUS_JA, TASK_STATUS, TYPE_OBJECT_CONTACT, TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_ORDER, ShopTurboItemsOrdersNameCustomField, ShopTurboItemsOrdersValueCustomField,
)
from utils.contact import display_contact_name
from utils.decorator import login_or_hubspot_required
from utils.filter import build_view_filter
from utils.hubspot import (push_hubspot_orders, push_hubspot_subscriptions,
                           refresh_hubspot_token, validate_hubspot_request)
from utils.stripe.stripe import *
from utils.utility import (check_if_int_castable, check_staff, get_workspace,
                           modular_view_filter)
from utils.workspace import create_workspace, get_permission

LANGUAGE_QUERY_PARAMETER = 'language'
POSTS_PER_PAGE = 12
POSTS_PER_PAGE_BEGIN = POSTS_PER_PAGE - 1

def hubspot_connect(request):
    params = {}
    if request.LANGUAGE_CODE:
        params['lang'] = request.LANGUAGE_CODE
    if request.user:
        params['user_id'] = str(request.user.id)

    link_state = ""
    for idx, key in enumerate(params):
        if idx == 0:
            link_state += f"{key}:"+params[key]
        else:
            link_state += f"#{key}:"+params[key]

    if link_state:
        link_state = "&state="+urlsafe_base64_encode(force_bytes(link_state))

    client_id = settings.HUBSPOT_CLIENT_ID
    redirect_uri = settings.HUBSPOT_REDIRECT_URI
    scopes = settings.HUBSPOT_SCOPES

    try:
        auth_url = f"https://app.hubspot.com/oauth/authorize?client_id={client_id}&scope={scopes}&redirect_uri={redirect_uri}"+link_state
        print("Redirect to URL: ", auth_url)
        return redirect(auth_url)
    except Exception:
        Notification.objects.create(workspace=get_workspace(
            request.user), user=request.user, message="Unable to login. Please try again.", type='error')
        return render(request, 'data/auth/error.html')


def hubspot_connect_power_message(request):
    params = {}
    if request.LANGUAGE_CODE:
        params['lang'] = request.LANGUAGE_CODE
    if request.user:
        params['user_id'] = str(request.user.id)

    link_state = ""
    for idx, key in enumerate(params):
        if idx == 0:
            link_state += f"{key}:"+params[key]
        else:
            link_state += f"#{key}:"+params[key]

    if link_state:
        link_state = "&state="+urlsafe_base64_encode(force_bytes(link_state))

    client_id = settings.HUBSPOT_POWER_MESSAGE_CLIENT_ID
    redirect_uri = settings.HUBSPOT_POWER_MESSAGE_REDIRECT_URI
    scopes = settings.HUBSPOT_POWER_MESSAGE_SCOPES

    try:
        auth_url = f"https://app.hubspot.com/oauth/authorize?client_id={client_id}&scope={scopes}&redirect_uri={redirect_uri}"+link_state
        print("Redirect to URL: ", auth_url)
        return redirect(auth_url)
    except Exception:
        Notification.objects.create(workspace=get_workspace(
            request.user), user=request.user, message="Unable to login. Please try again.", type='error')
        return render(request, 'data/auth/error.html')


def hubspot_connect_power_subscription(request):
    params = {}
    if request.LANGUAGE_CODE:
        params['lang'] = request.LANGUAGE_CODE
    if request.user:
        params['user_id'] = str(request.user.id)

    link_state = ""
    for idx, key in enumerate(params):
        if idx == 0:
            link_state += f"{key}:"+params[key]
        else:
            link_state += f"#{key}:"+params[key]
    print(request.user)

    if link_state:
        link_state = "&state="+urlsafe_base64_encode(force_bytes(link_state))

    client_id = settings.HUBSPOT_POWER_SUBSCRIPTION_CLIENT_ID
    redirect_uri = settings.HUBSPOT_POWER_SUBSCRIPTION_REDIRECT_URI
    scopes = settings.HUBSPOT_POWER_SUBSCRIPTION_SCOPES

    try:
        auth_url = f"https://app.hubspot.com/oauth/authorize?client_id={client_id}&scope={scopes}&redirect_uri={redirect_uri}"+link_state
        print("Redirect to URL: ", auth_url)
        return redirect(auth_url)
    except Exception:
        Notification.objects.create(workspace=get_workspace(
            request.user), user=request.user, message="Unable to login. Please try again.", type='error')
        return render(request, 'data/auth/error.html')


def hubspot_callback(request):
    code = request.GET.get('code')
    states = request.GET.get("state", None)
    lang = None
    user_id = None
    user = None
    workspace = None
    if states:
        decode_link = urlsafe_base64_decode(states).decode('utf-8')
        data = {k: v for k, v in (pair.split(':')
                                  for pair in decode_link.split('#'))}
        if "lang" in data:
            lang = data['lang']
        if "user_id" in data:
            user_id = data['user_id']

    if user_id and user_id != 'None':
        workspace = get_workspace(User.objects.get(id=user_id))
        user = User.objects.get(id=user_id)
    if code:
        try:
            token_url = "https://api.hubapi.com/oauth/v1/token"
            data = {
                'grant_type': 'authorization_code',
                'client_id': settings.HUBSPOT_CLIENT_ID,
                'client_secret': settings.HUBSPOT_CLIENT_SECRET,
                'redirect_uri': settings.HUBSPOT_REDIRECT_URI,
                'code': code,
            }
            response_token = requests.post(token_url, data=data)
            if response_token.status_code == 200:
                token_info = response_token.json()
                access_token = token_info.get('access_token')
                refresh_token = token_info.get('refresh_token')
                expires_in = token_info.get('expires_in')
                print("access token: ", access_token)

                responseApp = requests.get(
                    f'https://api.hubapi.com/oauth/v1/access-tokens/{access_token}')
                print('[Debug] responseApp: ', responseApp)

                if responseApp.status_code == 200:
                    response = responseApp.json()
                    hub_id = response['hub_id']
                    app_id = response['app_id']
                    split_hub_domain = response['hub_domain'].split('-')
                    if len(split_hub_domain) > 2:
                        if 'dev' in split_hub_domain:
                            result = ' '.join(
                                split_hub_domain[:len(split_hub_domain)-2])
                        else:
                            result = ' '.join(
                                split_hub_domain[:len(split_hub_domain)])
                        hub_domain = result
                    else:
                        hub_domain = response['hub_domain']
                else:
                    print("Error: ", response_token)
                    raise Exception("Cannot get Access token")

                print("add to channel")
                integration = Integration.objects.get(slug='hubspot')
                # integrationApp = IntegrationApp.objects.get(slug='hubspot')
                channel, _ = Channel.objects.get_or_create(
                    workspace=workspace,
                    app_id=app_id,
                    integration=integration,
                    # integration_app=integrationApp,
                    account_id=hub_id,
                )
                channel.user.add(user)
                if not channel.name:
                    channel.name = f"HubSpot - {hub_domain}"
                channel.access_token = access_token
                channel.api_key = settings.HUBSPOT_CLIENT_ID
                channel.account_id = hub_id
                channel.access_token_expires_in = expires_in
                channel.refresh_token = refresh_token
                channel.updated_at = timezone.now()
                channel.save()
                print("Channel saved", channel)

                if lang == 'en':
                    Notification.objects.create(
                        workspace=workspace, user=user, message="Channel was connected successfully.", type="success")
                else:
                    Notification.objects.create(
                        workspace=workspace, user=user, message="連携サービスが正常に接続されました。", type="success")

                translation.activate(lang)
                response = HttpResponse(200)
                response.set_cookie(settings.LANGUAGE_COOKIE_NAME, lang)
                return redirect(reverse('integrations', host='app'))
            else:
                return render(request, 'data/auth/error.html')

        except Exception:
            traceback.print_exc()
            messages.warning(request, "There was an error.")
            if lang == 'en':
                Notification.objects.create(
                    workspace=workspace, user=user, message="Connect channel(s) failed.", type="error")
            else:
                Notification.objects.create(
                    workspace=workspace, user=user, message="チャネルの接続に失敗しました。", type="error")

            return redirect(reverse('integrations', host='app'))


def hubspot_callback_power_order(request):
    code = request.GET.get('code')
    states = request.GET.get("state", None)
    lang = None
    user_id = None
    user = None
    workspace = None
    if states:
        decode_link = urlsafe_base64_decode(states).decode('utf-8')
        data = {k: v for k, v in (pair.split(':')
                                  for pair in decode_link.split('#'))}
        if "lang" in data:
            lang = data['lang']
        if "user_id" in data:
            user_id = data['user_id']
    if user_id and user_id != 'None':
        workspace = get_workspace(User.objects.get(id=user_id))
        user = User.objects.get(id=user_id)
        print(user_id)
    if code:
        try:
            token_url = "https://api.hubapi.com/oauth/v1/token"
            data = {
                'grant_type': 'authorization_code',
                'client_id': settings.HUBSPOT_CLIENT_ID,
                'client_secret': settings.HUBSPOT_CLIENT_SECRET,
                'redirect_uri': settings.HUBSPOT_REDIRECT_URI,
                'code': code,
            }
            response_token = requests.post(token_url, data=data)
            if response_token.status_code == 200:
                token_info = response_token.json()
                print(token_info)
                access_token = token_info.get('access_token')
                refresh_token = token_info.get('refresh_token')
                expires_in = token_info.get('expires_in')
                print("access token: ", access_token)

                responseApp = requests.get(
                    f'https://api.hubapi.com/oauth/v1/access-tokens/{access_token}')
                print(responseApp.json())
                if responseApp.status_code == 200:
                    response = responseApp.json()
                    hub_id = response['hub_id']
                    app_id = response['app_id']
                    split_hub_domain = response['hub_domain'].split('-')
                    if len(split_hub_domain) > 2:
                        result = ' '.join(
                            split_hub_domain[:len(split_hub_domain)-2])
                        hub_domain = result
                    else:
                        hub_domain = response['hub_domain']
                else:
                    print("Error: ", response_token)
                    raise Exception("Cannot get Access token")

                print("add to channel")
                integration = Integration.objects.get(slug='hubspot')
                integrationApp = IntegrationApp.objects.get(
                    slug='hubspot-power-order')
                channel, _ = Channel.objects.get_or_create(
                    workspace=workspace,
                    # platform ='hubspot',
                    app_id=app_id,
                    integration=integration,
                    name=f"Hubspot Power Order - {hub_domain}",
                    integration_app=integrationApp
                )
                channel.user.add(user)
                channel.name = f"Hubspot Power Order - {hub_domain}"
                channel.access_token = access_token
                channel.api_key = settings.HUBSPOT_CLIENT_ID
                channel.account_id = hub_id
                channel.access_token_expires_in = expires_in
                channel.refresh_token = refresh_token
                channel.updated_at = timezone.now()
                channel.save()
                print("Channel saved", channel)

                if lang == 'en':
                    Notification.objects.create(
                        workspace=workspace, user=user, message="Channel was connected successfully.", type="success")
                else:
                    Notification.objects.create(
                        workspace=workspace, user=user, message="連携サービスが正常に接続されました。", type="success")

                translation.activate(lang)
                response = HttpResponse(200)
                response.set_cookie(settings.LANGUAGE_COOKIE_NAME, lang)
                return redirect(f"{reverse('integrations', host='app')}")

        except Exception:
            traceback.print_exc()
            messages.warning(request, "There was an error.")
            if lang == 'en':
                Notification.objects.create(
                    workspace=workspace, user=user, message="Connect channel(s) failed.", type="error")
            else:
                Notification.objects.create(
                    workspace=workspace, user=user, message="チャネルの接続に失敗しました。", type="error")

            return redirect(reverse('integrations', host='app'))


def hubspot_callback_power_subscription(request):
    code = request.GET.get('code')
    states = request.GET.get("state", None)
    lang = None
    user_id = None
    user = None
    workspace = None
    if states:
        decode_link = urlsafe_base64_decode(states).decode('utf-8')
        data = {k: v for k, v in (pair.split(':')
                                  for pair in decode_link.split('#'))}
        if "lang" in data:
            lang = data['lang']
        if "user_id" in data:
            user_id = data['user_id']
    if user_id and user_id != 'None':
        workspace = get_workspace(User.objects.get(id=user_id))
        user = User.objects.get(id=user_id)
        print(user_id)
    if code:
        try:
            token_url = "https://api.hubapi.com/oauth/v1/token"
            data = {
                'grant_type': 'authorization_code',
                'client_id': settings.HUBSPOT_CLIENT_ID,
                'client_secret': settings.HUBSPOT_CLIENT_SECRET,
                'redirect_uri': settings.HUBSPOT_REDIRECT_URI,
                'code': code,
            }
            response_token = requests.post(token_url, data=data)
            if response_token.status_code == 200:
                token_info = response_token.json()
                print(token_info)
                access_token = token_info.get('access_token')
                refresh_token = token_info.get('refresh_token')
                expires_in = token_info.get('expires_in')
                print("access token: ", access_token)

                responseApp = requests.get(
                    f'https://api.hubapi.com/oauth/v1/access-tokens/{access_token}')
                print(responseApp.json())
                if responseApp.status_code == 200:
                    response = responseApp.json()
                    hub_id = response['hub_id']
                    app_id = response['app_id']
                    split_hub_domain = response['hub_domain'].split('-')
                    if len(split_hub_domain) > 2:
                        result = ' '.join(
                            split_hub_domain[:len(split_hub_domain)-2])
                        hub_domain = result
                    else:
                        hub_domain = response['hub_domain']
                else:
                    print("Error: ", response_token)
                    raise Exception("Cannot get Access token")

                print("add to channel")
                integration = Integration.objects.get(slug='hubspot')
                integrationApp = IntegrationApp.objects.get(
                    slug='hubspot-power-subscription')
                channel, _ = Channel.objects.get_or_create(
                    workspace=workspace,
                    # platform ='hubspot',
                    app_id=app_id,
                    integration=integration,
                    name=f"Hubspot Power Subscription - {hub_domain}",
                    integration_app=integrationApp
                )
                channel.user.add(user)
                channel.name = f"Hubspot Power Subscription - {hub_domain}"
                channel.access_token = access_token
                channel.api_key = settings.HUBSPOT_CLIENT_ID
                channel.account_id = hub_id
                channel.access_token_expires_in = expires_in
                channel.refresh_token = refresh_token
                channel.updated_at = timezone.now()
                channel.save()
                print("Channel saved", channel)

                if lang == 'en':
                    Notification.objects.create(
                        workspace=workspace, user=user, message="Channel was connected successfully.", type="success")
                else:
                    Notification.objects.create(
                        workspace=workspace, user=user, message="連携サービスが正常に接続されました。", type="success")

                translation.activate(lang)
                response = HttpResponse(200)
                response.set_cookie(settings.LANGUAGE_COOKIE_NAME, lang)
                return redirect(f"{reverse('integrations', host='app')}")

        except Exception:
            traceback.print_exc()
            messages.warning(request, "There was an error.")
            if lang == 'en':
                Notification.objects.create(
                    workspace=workspace, user=user, message="Connect channel(s) failed.", type="error")
            else:
                Notification.objects.create(
                    workspace=workspace, user=user, message="チャネルの接続に失敗しました。", type="error")

            return redirect(reverse('integrations', host='app'))


def hubspot_callback_power_message(request):
    code = request.GET.get('code')
    states = request.GET.get("state", None)
    lang = None
    user_id = None
    user = None
    workspace = None
    if states:
        decode_link = urlsafe_base64_decode(states).decode('utf-8')
        data = {k: v for k, v in (pair.split(':')
                                  for pair in decode_link.split('#'))}
        if "lang" in data:
            lang = data['lang']
        if "user_id" in data:
            user_id = data['user_id']
    if user_id and user_id != 'None':
        workspace = get_workspace(User.objects.get(id=user_id))
        user = User.objects.get(id=user_id)
    if code:
        try:
            token_url = "https://api.hubapi.com/oauth/v1/token"
            data = {
                'grant_type': 'authorization_code',
                'client_id': settings.HUBSPOT_POWER_MESSAGE_CLIENT_ID,
                'client_secret': settings.HUBSPOT_POWER_MESSAGE_CLIENT_SECRET,
                'redirect_uri': settings.HUBSPOT_POWER_MESSAGE_REDIRECT_URI,
                'code': code,
            }
            response_token = requests.post(token_url, data=data)
            if response_token.status_code == 200:
                token_info = response_token.json()
                print(token_info)
                access_token = token_info.get('access_token')
                refresh_token = token_info.get('refresh_token')
                expires_in = token_info.get('expires_in')
                print("access token: ", access_token)

                responseApp = requests.get(
                    f'https://api.hubapi.com/oauth/v1/access-tokens/{access_token}')
                print('[Debug] responseApp: ', responseApp)
                print(responseApp.json())
                if responseApp.status_code == 200:
                    response = responseApp.json()
                    hub_id = response['hub_id']
                    app_id = response['app_id']
                    hubspot_domain = response['hub_domain']
                    split_hub_domain = response['hub_domain'].split('-')
                    if len(split_hub_domain) > 2:
                        result = ' '.join(
                            split_hub_domain[:len(split_hub_domain)-2])
                        hub_domain = result
                    else:
                        hub_domain = response['hub_domain']
                else:
                    print("Error: ", response_token)
                    raise Exception("Cannot get Access token")

                print("add to channel")
                integration = Integration.objects.get(slug='hubspot')
                integrationApp = IntegrationApp.objects.get(
                    slug='hubspot-power-message')
                channel, _ = Channel.objects.get_or_create(
                    workspace=workspace,
                    app_id=app_id,
                    integration=integration,
                    integration_app=integrationApp,
                    account_id=hub_id,
                )
                channel.user.add(user)
                channel.name = f"Hubspot Power Message ({hubspot_domain})"
                channel.access_token = access_token
                channel.api_key = settings.HUBSPOT_POWER_MESSAGE_CLIENT_ID
                channel.account_id = hub_id
                channel.access_token_expires_in = expires_in
                channel.refresh_token = refresh_token
                channel.updated_at = timezone.now()
                channel.save()
                print("Channel saved", channel)

                if lang == 'en':
                    Notification.objects.create(
                        workspace=workspace, user=user, message="Channel was connected successfully.", type="success")
                else:
                    Notification.objects.create(
                        workspace=workspace, user=user, message="連携サービスが正常に接続されました。", type="success")

                translation.activate(lang)
                response = HttpResponse(200)
                response.set_cookie(settings.LANGUAGE_COOKIE_NAME, lang)
                return redirect(f"{reverse('integrations', host='app')}")

        except Exception:
            traceback.print_exc()
            messages.warning(request, "There was an error.")
            if lang == 'en':
                Notification.objects.create(
                    workspace=workspace, user=user, message="Connect channel(s) failed.", type="error")
            else:
                Notification.objects.create(
                    workspace=workspace, user=user, message="チャネルの接続に失敗しました。", type="error")

            return redirect(reverse('integrations', host='app'))


def hubspot_order_connect(request):
    date = datetime.now().strftime("%Y-%m-%d")
    portalId = request.GET.get('portalId')
    linkUrl = f"https://app.sanka.com/hubspot_order_callback?portalId={portalId}"
    response = {
        "responseVersion": "v3",
        "totalCount": 1,
        "cardLabel": "Deals",
        "sections": [
            {
                "id": "123",
                "title": "Import Deals",
                "linkUrl": linkUrl,
                "tokens": [
                    {
                        "name": "created",
                        "label": "date",
                        "dataType": "DATE",
                        "value": date
                    }
                ],
                "actions": [
                ]
            }
        ]
    }
    return JsonResponse(response)


def hubspot_sanka_crm_card_webhook(request):

    hs_language = request.GET.get('hs_language', None)
    userId = request.GET.get('userId')
    portalId = request.GET.get('portalId')
    associatedObjectId = request.GET.get('associatedObjectId')
    associatedObjectType = request.GET.get('associatedObjectType')

    app_id = settings.HUBSPOT_APP_ID
    date = datetime.now().strftime("%Y-%m-%d")
    channels = Channel.objects.filter(account_id=portalId, app_id=app_id)
    def random_id(): return random.randint(1000000, 9999999)

    results = []
    exclude_object = ['user_management', 'session_event', 'worker', TYPE_OBJECT_FORM,
                      'calendar', 'customer_case', 'contact_lists']  # exclude non table-able objects
    for channel in channels:
        workspace = channel.workspace
        if not hs_language:
            hs_language = 'ja'  # default language
        data = {
            "objectId": random_id(),
            "title": f"{workspace.name}" if hs_language == 'ja' else f"{workspace.name}",
            "created": date,
            "actions": [
                {
                    "type": "IFRAME",
                    "width": 1680,
                    "height": 900,
                    "uri": f"{settings.SITE_URL}/hubspot/crm_card/?source_integration=hubspot&workspace_id={workspace.id}&channel_id={channel.id}&associatedObjectId={associatedObjectId}&associatedObjectType={associatedObjectType}&hs_language={hs_language}&userId={userId}",
                    "label": "Sanka ERP" if hs_language == 'ja' else "Sanka ERP",
                    "associatedObjectProperties": []
                }
            ],
        }
        results.append(data)

    if results:
        response = {
            "results": results,
        }
    else:
        response = {
            "results": [
                {
                    "objectId": random_id(),
                    "title": "Sanka Objects",
                    "properties": [
                        {
                            "label": "Information",
                            "dataType": "STRING",
                            "value": "No Objects Found."
                        }
                    ]
                }
            ]
        }

    return JsonResponse(response)

# Case: Triggered when there's activities in Hubspot Contact (e.g merge, remove, etc)


@csrf_exempt
def hubspot_event_subscriptions(request):
    app_secret = settings.HUBSPOT_CLIENT_SECRET
    if not validate_hubspot_request(request, app_secret):
        return JsonResponse({"error": "Invalid request"}, status=401)

    try:
        body_data = json.loads(request.body.decode("utf-8"))
    except json.JSONDecodeError:
        body_data = request.body.decode("utf-8")

    if body_data:
        data = body_data[0]
        subcription_type = data.get('subscriptionType')
        portalId = data.get('portalId')
        app_id = data.get('app_id')

        app_id = settings.HUBSPOT_APP_ID
        channels = Channel.objects.filter(account_id=portalId, app_id=app_id)

        if 'contact.merge' in subcription_type:
            mergedObjectIds = data.get('mergedObjectIds')
            newObjectId = data.get('newObjectId')

            for channel in channels:
                workspace = channel.workspace
                contact_platforms = ContactsPlatforms.objects.filter(
                    channel=channel, platform_id__in=mergedObjectIds)

                for contact_platform in contact_platforms:
                    message_thread = MessageThread.objects.filter(workspace=workspace, message_type='line', contacts__in=[
                                                                  contact_platform.contact]).order_by('-created_at').first()
                    if message_thread:
                        contact_platform.platform_id = newObjectId
                    contact_platform.save()

    return HttpResponse('OK')


def hubspot_contract_handler(request, type_card=None):
    workspace_id = request.GET.get('workspace_id')
    channel_id = request.GET.get('channel_id')
    userId = request.GET.get('userId')
    contract_id = request.GET.get('contract_id', None)
    lang = request.GET.get('hs_language', None)

    if not lang or lang == '' or lang == 'None':
        lang = 'ja'

    workspace = Workspace.objects.get(id=workspace_id)
    channel = Channel.objects.get(id=channel_id, workspace=workspace)
    if contract_id:
        contract_documents = ContractDocument.objects.get(
            id=contract_id, workspace=workspace)
    else:
        contract_documents = ContractDocument.objects.filter(
            workspace=workspace)

    view = None
    view_filter = None
    view = View.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_CONTRACT, title__isnull=True).first()
    if view:
        view_filter = ViewFilter.objects.filter(view=view).first()
    request = hubspot_power_authentication_handler(request)
    if isinstance(request, HttpResponse):
        return request

    context = {
        'object_type': TYPE_OBJECT_CONTRACT,
        'source_integration': 'hubspot',
        'workspace': workspace,
        'channel': channel,
        'docs': contract_documents,
        'view_filter': view_filter,
    }

    # Return HTML
    template_page = 'data/contractwise/contract-table.html'
    permission = {
        'type': 'edit'
    }
    context['permission'] = permission

    context['template_page'] = template_page

    rendered_content = render(
        request, 'data/static/partial-crm-card-render-template.html', context)
    # rendered_content["X-Frame-Options"] = "ALLOW-FROM *"
    rendered_content["Content-Security-Policy"] = "frame-ancestors *;"

    return rendered_content


def hubspot_power_crm_handler(request, type_card=None):
    workspace_id = request.GET.get('workspace_id')
    channel_id = request.GET.get('channel_id')
    lang = request.GET.get('hs_language', None)

    object_type = request.GET.get('object_type', None)

    if not lang or lang == '' or lang == 'None':
        lang = 'ja'

    workspace = Workspace.objects.get(id=workspace_id)
    channel = Channel.objects.get(id=channel_id, workspace=workspace)

    if object_type == TYPE_OBJECT_CONTACT:
        objs = Contact.objects.filter(workspace=workspace)
    elif object_type == TYPE_OBJECT_COMPANY:
        objs = Company.objects.filter(workspace=workspace)

    view = None
    view_filter = None
    view = View.objects.filter(
        workspace=workspace, target=object_type, title__isnull=True).first()
    if view:
        if object_type == TYPE_OBJECT_CONTACT:
            view_filter = modular_view_filter(
                workspace, TYPE_OBJECT_CONTACT, view_id=view.id, column_view=DEFAULT_COLUMNS_CONTACTS.copy())
        elif object_type == TYPE_OBJECT_COMPANY:
            view_filter = modular_view_filter(
                workspace, TYPE_OBJECT_COMPANY, view_id=view.id, column_view=DEFAULT_COLUMNS_COMPANY.copy())

    request = hubspot_power_crm_auth(request, object_type)
    if isinstance(request, HttpResponse):
        return request
    request.user = workspace.user.all().first()
    request.LANGUAGE_CODE = lang

    context = {
        'object_type': object_type,
        'source_integration': 'hubspot',
        'workspace': workspace,
        'channel': channel,
        'objs': objs,
        'view_filter': view_filter,
    }

    if object_type == TYPE_OBJECT_CONTACT:
        template_page = 'data/contacts/contact-table.html'
    elif object_type == TYPE_OBJECT_COMPANY:
        template_page = 'data/contacts/company-table.html'
    permission = {
        'type': 'edit'
    }
    context['permission'] = permission

    context['template_page'] = template_page

    if len(objs) == 0:
        rendered_content = render(
            request, 'data/static/no-crm-data.html', {'hs_language': lang})
    else:
        rendered_content = render(
            request, 'data/static/partial-crm-card-render-template.html', context)
    # rendered_content["X-Frame-Options"] = "ALLOW-FROM *"
    rendered_content["Content-Security-Policy"] = "frame-ancestors *;"

    return rendered_content


def hubspot_crm_card_handler(request):
    workspace_id = request.GET.get('workspace_id')
    workspace = Workspace.objects.get(id=workspace_id)

    channel_id = request.GET.get('channel_id', '')

    # Getting Auth User
    handler = hubspot_crm_auth_handler(request)
    if isinstance(handler, HttpResponse):
        return handler

    try:
        ver_ = Verification.objects.get(user=handler)
        lang = ver_.language
        print("Language: ", ver_)
    except Exception as e:
        lang = None
        print("Error Verif: ", e)

    if lang:
        translation.activate(lang)
        request.LANGUAGE_CODE = translation.get_language()

    # hubspot parameters
    params = {
        'channel_id': Channel.objects.filter(id=channel_id).first().id,
        'associatedObjectId': request.GET.get('associatedObjectId', ''),
        'associatedObjectType': request.GET.get('associatedObjectType', ''),
        'source_integration': request.GET.get('source_integration', ''),
        'userId': request.GET.get('userId')
    }

    response = redirect(reverse('main', host='app') + '?' + urlencode(params))
    response.set_cookie(
        'from_hubspot', str(handler.id), max_age=3600,
        samesite='None',
        secure=True
    )
    for key, value in params.items():
        response.set_cookie(key, value, max_age=3600,
                            samesite='None', secure=True)

    return response


def hubspot_order_connect(request):
    date = datetime.now().strftime("%Y-%m-%d")
    portalId = request.GET.get('portalId')
    linkUrl = f"https://app.sanka.com/hubspot_order_callback?portalId={portalId}"
    response = {
        "responseVersion": "v3",
        "totalCount": 1,
        "cardLabel": "Deals",
        "sections": [
            {
                "id": "123",
                "title": "Import Deals",
                "linkUrl": linkUrl,
                "tokens": [
                    {
                        "name": "created",
                        "label": "date",
                        "dataType": "DATE",
                        "value": date
                    }
                ],
                "actions": [
                ]
            }
        ]
    }
    return JsonResponse(response)


def hubspot_power_message_line_chat(request):
    workspace_id = request.GET.get('workspace_id')
    channel_id = request.GET.get('channel_id')
    message_thread_id = request.GET.get('message_thread_id')

    workspace = Workspace.objects.get(id=workspace_id)
    channel = Channel.objects.get(id=channel_id, workspace=workspace)
    message_thread = MessageThread.objects.get(
        id=message_thread_id, workspace=workspace)

    context = {
        'workspace': workspace,
        'channel': channel,
        'message_thread': message_thread,
    }

    # Return HTML
    return render(request, 'data/inbox/power-message/chat-window.html', context)


def hubspot_order_connect(request):
    date = datetime.now().strftime("%Y-%m-%d")
    portalId = request.GET.get('portalId')
    linkUrl = f"https://app.sanka.com/hubspot_order_callback?portalId={portalId}"
    response = {
        "responseVersion": "v3",
        "totalCount": 1,
        "cardLabel": "Deals",
        "sections": [
            {
                "id": "123",
                "title": "Import Deals",
                "linkUrl": linkUrl,
                "tokens": [
                    {
                        "name": "created",
                        "label": "date",
                        "dataType": "DATE",
                        "value": date
                    }
                ],
                "actions": [
                ]
            }
        ]
    }
    return JsonResponse(response)


def hubspot_order_callback(request=None, order_ids=[], channel_id=None, mapping_custom_fields=None, lang=None, user=None, mapping_association_custom_fields=None):
    print('==> hubspot_order_callback')
    if request:
        lang = request.LANGUAGE_CODE
        user = request.user
    else:
        lang = lang
        user = User.objects.get(id=user)
    if channel_id is None:
        portalId = request.GET.get('portalId')
        channel = Channel.objects.filter(
            api_key=settings.HUBSPOT_CLIENT_ID, account_id=portalId).first()
    else:
        channel = Channel.objects.get(id=channel_id)
    if channel is None:
        if lang == 'ja':
            Notification.objects.create(workspace=get_workspace(
                user), user=user, message="機能を利用するにはHubspot Power Orderと連携してください。", type="error")
        else:
            Notification.objects.create(workspace=get_workspace(
                user), user=user, message="Please integrate with Hubspot Power Order to use the function.", type="error")
        return redirect(reverse('integrations', host='app'))
    workspace = channel.workspace

    # Auto refresh token
    if channel.api_key == settings.HUBSPOT_CLIENT_ID:
        token_url = 'https://api.hubapi.com/oauth/v1/token'
        data = {
            'grant_type': 'refresh_token',
            'client_id': settings.HUBSPOT_CLIENT_ID,
            'client_secret': settings.HUBSPOT_CLIENT_SECRET,
            'refresh_token': channel.refresh_token
        }
        response = requests.post(token_url, data=data)
        token_info = response.json()
        print(token_info)
        channel.access_token = token_info.get('access_token')
        channel.save()
    else:
        token_url = 'https://api.hubapi.com/oauth/v1/token'
        data = {
            'grant_type': 'refresh_token',
            'client_id': settings.HUBSPOT_CLIENT_ID,
            'client_secret': settings.HUBSPOT_CLIENT_SECRET,
            'refresh_token': channel.refresh_token
        }
        response = requests.post(token_url, data=data)
        token_info = response.json()
        print(token_info)
        channel.access_token = token_info.get('access_token')
        channel.save()

    tax_rate_url = 'https://api.hubapi.com/tax-rates/v1/tax-rates'
    headers = {
        'Authorization': f'Bearer {channel.access_token}'
    }
    tax_rate_response = requests.get(tax_rate_url, headers=headers)
    if tax_rate_response.status_code == 200:
        tax_rate_response = tax_rate_response.json()

    if len(order_ids) == 0:
        order_ids = list(ShopTurboOrders.objects.filter(
            workspace=workspace).values_list('id', flat=True))
    order_id_map_orders = {}
    for order_id in order_ids:
        task = TransferHistory.objects.filter(
            workspace=channel.workspace, status='running', type="export_order").first()
        if task:
            try:
                task.progress = 50 * order_ids.index(order_id) / len(order_ids)
                task.save()
            except:
                pass
        shopturbo_order = ShopTurboOrders.objects.get(id=order_id)
        mapping_results = {}
        deal_mapping = {}
        contact_mapping = {}
        company_mapping = {}
        item_mapping = {}
        platform = ShopTurboOrdersPlatforms.objects.filter(
            order_id=order_id).order_by('created_at').first()
        if mapping_custom_fields:
            for key, value in mapping_custom_fields.items():
                sanka_field = mapping_custom_fields[key]
                if '|contact' in sanka_field:
                    sanka_field = sanka_field.replace('|contact', '')
                    contactsnamecustomfields = ContactsNameCustomField.objects.filter(
                        workspace=workspace, name=sanka_field)
                elif '|company' in sanka_field:
                    sanka_field = sanka_field.replace('|company', '')
                    contactsnamecustomfields = CompanyNameCustomField.objects.filter(
                        workspace=workspace, name=sanka_field)
                else:
                    contactsnamecustomfields = ShopTurboOrdersNameCustomField.objects.filter(
                        workspace=workspace, name=sanka_field)

                if len(contactsnamecustomfields) > 0:
                    if isinstance(contactsnamecustomfields[0], ContactsNameCustomField):
                        if shopturbo_order.contact:
                            custom_value, _ = ContactsValueCustomField.objects.get_or_create(
                                field_name=contactsnamecustomfields[0], contact=shopturbo_order.contact)
                    elif isinstance(contactsnamecustomfields[0], CompanyNameCustomField):
                        if shopturbo_order.company:
                            custom_value, _ = CompanyValueCustomField.objects.get_or_create(
                                field_name=contactsnamecustomfields[0], company=shopturbo_order.company)
                    else:
                        custom_value, _ = ShopTurboOrdersValueCustomField.objects.get_or_create(
                            field_name=contactsnamecustomfields[0], orders=shopturbo_order)

                    if '|deal' in key:
                        key = key.replace('|deal', '')
                        deal_mapping[key] = custom_value.value if custom_value.value else ''
                    elif '|contact' in key:
                        key = key.replace('|contact', '')
                        if key not in ['name', 'email', 'phone']:
                            contact_mapping[key] = custom_value.value if custom_value.value else ''
                    elif '|company' in key:
                        key = key.replace('|company', '')
                        if key not in ['name', 'email', 'phone']:
                            company_mapping[key] = custom_value.value if custom_value.value else ''
                    else:
                        mapping_results[key] = custom_value.value
                if value == 'platform_id':
                    if platform is not None:
                        mapping_results[key] = platform.platform_order_id
                if value == 'order_id':
                    mapping_results[key] = shopturbo_order.order_id
                if value == 'customer':
                    if shopturbo_order.contact:
                        mapping_results[key] = shopturbo_order.contact.name
                    else:
                        mapping_results[key] = shopturbo_order.company.name
        print('mapping:', mapping_results, deal_mapping,
              contact_mapping, company_mapping)
        if platform is not None:
            platform_channel = platform.channel
        else:
            platform_channel = channel
        if mapping_results.get('deal_name', None):
            platform_order_id = mapping_results.get('deal_name')
        elif platform is not None:
            platform_order_id = platform.platform_order_id
        else:
            platform_order_id = workspace.name + \
                ' - ' + str(shopturbo_order.order_id)

        _items = []
        _amount = 0
        for item_order in shopturbo_order.shopturboitemsorders_set.all():
            if item_order.item is not None:
                _item_platform = item_order.item.item.filter(
                    channel=platform_channel).first()
            else:
                _item_platform = None
            if _item_platform and _item_platform.platform_id:
                default_item_price_tax = ShopTurboItemsPrice.objects.filter(
                    item=item_order.item, default=True).first()
                if default_item_price_tax:
                    default_item_price_tax = default_item_price_tax.tax
                else:
                    default_item_price_tax = 0

                default_item_price_tax_id = ''
                try:
                    tax_rate_results = [
                        tax_rate for tax_rate in tax_rate_response.get('results')]
                    for tax_rate in tax_rate_results:
                        if tax_rate.get('percentageRate') == default_item_price_tax:
                            default_item_price_tax_id = tax_rate.get('id')
                            break
                except:
                    pass

                item_properties = {
                    'name': item_order.item.name,
                    'description': item_order.item.description,
                    'hs_sku': _item_platform.platform_id,
                    'price': item_order.item.price,
                    'quantity': item_order.number_item,
                    'hs_tax_rate_group_id': default_item_price_tax_id,
                }
                if mapping_custom_fields:
                    for key, value in mapping_custom_fields.items():
                        if '|line_item' in key and not 'discount' in key and not 'discount' in key:
                            print(f"Processing custom field: {key}, value: {value}")
                            sanka_field = mapping_custom_fields[key].replace('|line_item', '')
                            itemsnamecustomfields = ShopTurboItemsOrdersNameCustomField.objects.filter(
                                workspace=workspace, name=sanka_field).first()
                            print(f"Found custom field: {sanka_field},{itemsnamecustomfields}")
                            if itemsnamecustomfields:
                                custom_value, _ = ShopTurboItemsOrdersValueCustomField.objects.get_or_create(
                                    field_name=itemsnamecustomfields, item_order=item_order)
                                item_mapping[key.replace('|line_item', '')] = custom_value.value if custom_value.value else ''
                item_properties.update(item_mapping)

                _items.append(item_properties)

                try:
                    _amount += item_order.number_item * item_order.item.price
                except:
                    _amount += 0
            else:
                if item_order.item is None:
                    default_item_price_tax = item_order.item_price_order_tax if item_order.item_price_order_tax else 0
                    default_item_price_tax_id = ''
                    try:
                        tax_rate_results = [
                            tax_rate for tax_rate in tax_rate_response.get('results')]
                        for tax_rate in tax_rate_results:
                            if tax_rate.get('percentageRate') == default_item_price_tax:
                                default_item_price_tax_id = tax_rate.get('id')
                                break
                    except:
                        pass

                    _items.append({
                        'name': item_order.custom_item_name,
                        'description': '',
                        'hs_sku': None,
                        'price': item_order.item_price_order,
                        'quantity': item_order.number_item,
                        'hs_tax_rate_group_id': default_item_price_tax_id,
                    })
                    try:
                        _amount += item_order.number_item * item_order.item_price_order
                    except:
                        _amount += 0
                else:
                    default_item_price_tax = ShopTurboItemsPrice.objects.filter(
                        item=item_order.item, default=True).first()
                    if default_item_price_tax:
                        default_item_price_tax = default_item_price_tax.tax
                    else:
                        default_item_price_tax = 0
                    default_item_price_tax_id = ''
                    try:
                        tax_rate_results = [
                            tax_rate for tax_rate in tax_rate_response.get('results')]
                        for tax_rate in tax_rate_results:
                            if tax_rate.get('percentageRate') == default_item_price_tax:
                                default_item_price_tax_id = tax_rate.get('id')
                                break
                    except:
                        pass

                    _items.append({
                        'name': item_order.item.name,
                        'description': item_order.item.description,
                        'hs_sku': None,
                        'price': item_order.item.price,
                        'quantity': item_order.number_item,
                        'hs_tax_rate_group_id': default_item_price_tax_id,
                    })
                    try:
                        _amount += item_order.number_item * item_order.item.price
                    except:
                        _amount += 0
        if _amount == 0:
            _amount = shopturbo_order.total_price
        _contact = shopturbo_order.contact
        _company = shopturbo_order.company
        if channel.ms_refresh_token:
            if shopturbo_order.contact:
                _company = {
                    "sanka_id": f'{shopturbo_order.contact.id}',
                    "name": display_contact_name(shopturbo_order.contact),
                    "email": shopturbo_order.contact.email,
                    "phone": shopturbo_order.contact.phone_number,
                }
            elif shopturbo_order.company:
                _company = {
                    "sanka_id": f'{shopturbo_order.company.id}',
                    "name": shopturbo_order.company.name,
                    "email": shopturbo_order.company.email,
                    "phone": shopturbo_order.company.phone_number,
                }
            _contact = None
        else:
            if shopturbo_order.contact:
                _contact = {
                    "sanka_id": f'{shopturbo_order.contact.id}',
                    "name": display_contact_name(shopturbo_order.contact),
                    "email": shopturbo_order.contact.email,
                    "phone": shopturbo_order.contact.phone_number,
                }
            if shopturbo_order.company:
                _company = {
                    "sanka_id": f'{shopturbo_order.company.id}',
                    "name": shopturbo_order.company.name,
                    "email": shopturbo_order.company.email,
                    "phone": shopturbo_order.company.phone_number,
                }
        if _contact:
            _contact.update(contact_mapping)
        if _company:
            _company.update(company_mapping)
        order_id_map_orders[f'{shopturbo_order.id}'] = {
            'order_at': shopturbo_order.order_at,
            'platform_order_id': platform_order_id,
            'platform': platform_channel.integration.title_ja if platform_channel.integration.title_ja else platform_channel.integration.title,
            'amount': _amount,
            'items': _items,
            'company': _company,
            'contact': _contact,
            'mapping_custom_fields': mapping_custom_fields,
            'deal_mapping': deal_mapping,
        }
        # print(order_id_map_orders)
    push_hubspot_orders(str(channel.id), order_id_map_orders, mapping_association_custom_fields=mapping_association_custom_fields)
    if lang == 'ja':
        Notification.objects.create(
            workspace=workspace, user=user, message="受注レコードが Hubspot に正常にエクスポートされました", type="success")
    else:
        Notification.objects.create(workspace=workspace, user=user,
                                    message="Orders successfully exported to Hubspot", type="success")

    return redirect(reverse('main', host='app'))


def hubspot_subscription_connect(request):
    date = datetime.now().strftime("%Y-%m-%d")
    portalId = request.GET.get('portalId')
    linkUrl = f"https://app.sanka.com/hubspot_subscription_callback?portalId={portalId}"
    response = {
        "responseVersion": "v3",
        "totalCount": 1,
        "cardLabel": "Subscriptions",
        "sections": [
            {
                "id": "123",
                "title": "Import Subscriptions",
                "linkUrl": linkUrl,
                "tokens": [
                    {
                        "name": "created",
                        "label": "date",
                        "dataType": "DATE",
                        "value": date
                    }
                ],
                "actions": [
                ]
            }
        ]
    }
    return JsonResponse(response)


def hubspot_subscription_callback(request=None, sub_ids=[], channel_id=None, lang=None, user=None):
    print('==> hubspot_subscription_callback')
    if request:
        lang = request.LANGUAGE_CODE
        user = request.user
    else:
        lang = lang
        user = User.objects.get(id=user)
    if channel_id is None:
        portalId = request.GET.get('portalId')
        channel = Channel.objects.filter(
            api_key=settings.HUBSPOT_CLIENT_ID, account_id=portalId).first()
    else:
        channel = Channel.objects.get(id=channel_id)
    if channel is None:
        if lang == 'ja':
            Notification.objects.create(workspace=get_workspace(
                user), user=user, message="機能を利用するにはHubspot Power Subscriptionと連携してください。", type="error")
        else:
            Notification.objects.create(workspace=get_workspace(
                user), user=user, message="Please integrate with Hubspot Power Subscription to use the function.", type="error")
        return redirect(reverse('integrations', host='app'))
    workspace = channel.workspace

    # Auto refresh token
    token_url = 'https://api.hubapi.com/oauth/v1/token'
    data = {
        'grant_type': 'refresh_token',
        'client_id': settings.HUBSPOT_CLIENT_ID,
        'client_secret': settings.HUBSPOT_CLIENT_SECRET,
        'refresh_token': channel.refresh_token
    }
    response = requests.post(token_url, data=data)
    token_info = response.json()
    print(token_info)
    channel.access_token = token_info.get('access_token')
    channel.save()

    if len(sub_ids) == 0:
        sub_ids = list(ShopTurboSubscriptions.objects.filter(
            workspace=workspace).values_list('id', flat=True))
    sub_id_map_subs = {}
    for sub_id in sub_ids:
        shopturbo_subscription = ShopTurboSubscriptions.objects.get(id=sub_id)
        platform = ShopTurboSubscriptionPlatforms.objects.filter(
            source_subscription=shopturbo_subscription, channel=channel).first()
        if platform is not None:
            platform_id = platform.platform_id
        else:
            platform_id = workspace.name + ' - ' + \
                str(shopturbo_subscription.subscriptions_id)

        _items = []
        _amount = 0
        for item_sub in shopturbo_subscription.shopturboitemssubscriptions_set.all():
            if item_sub.item is not None:
                _item_platform = item_sub.item.item.filter(
                    channel=channel).first()
            else:
                _item_platform = None
            if _item_platform and _item_platform.platform_id:
                _items.append({
                    'name': item_sub.item.name,
                    'description': item_sub.item.description,
                    'hs_sku': _item_platform.platform_id,
                    'price': item_sub.item.price,
                    'quantity': item_sub.number_item,
                })
                try:
                    _amount += item_sub.number_item * item_sub.item.price
                except:
                    _amount += 0
            else:
                _items.append({
                    'name': item_sub.item.name,
                    'description': item_sub.item.description,
                    'hs_sku': None,
                    'price': item_sub.item.price,
                    'quantity': item_sub.number_item,
                })
                try:
                    _amount += item_sub.number_item * item_sub.item.price
                except:
                    _amount += 0
        if _amount == 0:
            _amount = shopturbo_subscription.total_price
        _contact = shopturbo_subscription.contact
        if shopturbo_subscription.contact:
            _contact = {
                "sanka_id": f'{shopturbo_subscription.contact.id}',
                "name": display_contact_name(shopturbo_subscription.contact),
                "email": shopturbo_subscription.contact.email,
                "phone": shopturbo_subscription.contact.phone_number,
            }
        _company = shopturbo_subscription.company
        if shopturbo_subscription.company:
            _company = {
                "sanka_id": f'{shopturbo_subscription.company.id}',
                "name": shopturbo_subscription.company.name,
                "email": shopturbo_subscription.company.email,
                "phone": shopturbo_subscription.company.phone_number,
            }
        sub_id_map_subs[f'{shopturbo_subscription.id}'] = {
            'id': sub_id,
            'order_at': shopturbo_subscription.created_at,
            'platform_id': platform_id,
            'platform': channel.integration.title_ja if channel.integration.title_ja else channel.integration.title,
            'amount': _amount,
            'items': _items,
            'company': _company,
            'contact': _contact,
            'status': shopturbo_subscription.subscription_status,
            'subscription_status': shopturbo_subscription.subscription_status,
            'number_item': shopturbo_subscription.number_item,
            'start_date': shopturbo_subscription.start_date,
            'end_date': shopturbo_subscription.end_date,
            'currency': shopturbo_subscription.currency,
            'frequency': shopturbo_subscription.frequency,
            'frequency_time': shopturbo_subscription.frequency_time,
            'prior_to_next': shopturbo_subscription.prior_to_next,
            'prior_to_time': shopturbo_subscription.prior_to_time,
            'total_price': shopturbo_subscription.total_price,
            'shipping_cost_tax_status': shopturbo_subscription.shipping_cost_tax_status,
            'tax': shopturbo_subscription.tax,
            'tax_applied_to': shopturbo_subscription.tax_applied_to,
            'created_at': shopturbo_subscription.created_at,
        }
        # print(sub_id_map_subs)
    push_hubspot_subscriptions(str(channel.id), sub_id_map_subs)

    return redirect(reverse('main', host='app'))

def hubspot_crm_auth_handler(request):
    try:
        channel_id = request.GET.get('channel_id')
        userId = request.GET.get('userId')
        lang = request.GET.get('hs_language', 'ja')
        channel = Channel.objects.get(id=channel_id)
        workspace = channel.workspace
        hs_user = HubspotUser.objects.filter(
            channel=channel, hubspot_id=userId).first()
        if hs_user:
            if hs_user.sanka_user:
                user = hs_user.sanka_user.user

                if request.user.is_authenticated:
                    if user != request.user:
                        return render(request, 'data/static/hubspot-user-mismatch-warning.html', {})

                # force to change workspace
                verification = Verification.objects.get(user=user)
                verification.workspace = workspace
                verification.save()
                return user

    except:
        pass

    lang = 'ja'
    translation.activate(lang)
    request.LANGUAGE_CODE = translation.get_language()

    rendered_content = render(
        request, 'data/static/crm-confirmation.html', {'hs_language': lang})

    # rendered_content["X-Frame-Options"] = "ALLOW-FROM *"
    rendered_content["Content-Security-Policy"] = "frame-ancestors *;"
    return rendered_content

def hubspot_power_authentication_handler(request):
    try:
        channel_id = request.GET.get('channel_id')
        userId = request.GET.get('userId')
        lang = request.GET.get('hs_language', None)
        channel = Channel.objects.get(id=channel_id)
        hs_user = HubspotUser.objects.filter(
            channel=channel, hubspot_id=userId).first()
        if hs_user:
            if hs_user.sanka_user:
                user = hs_user.sanka_user.user
                print("Logging in: ", user.id)
                try:
                    ver_ = Verification.objects.get(user=user)
                    ver_.language = lang
                    ver_.save()
                except Exception as e:
                    print("Error Verif: ", e)

                # login(request, user)
                # request.session["from_cross_site"] = True

                return request
    except:
        pass

    rendered_content = render(
        request, 'data/static/crm-confirmation.html', {'hs_language': lang})

    # rendered_content["X-Frame-Options"] = "ALLOW-FROM *"
    rendered_content["Content-Security-Policy"] = "frame-ancestors *;"
    return rendered_content


def hubspot_power_crm_auth(request, object_type):
    try:
        channel_id = request.GET.get('channel_id')
        userId = request.GET.get('userId')
        lang = request.GET.get('hs_language', None)
        channel = Channel.objects.get(id=channel_id)
        hs_user = HubspotUser.objects.filter(
            channel=channel, hubspot_id=userId).first()
        no_hs_user = False

        if hs_user:
            no_hs_user = True

            if hs_user.sanka_user:
                user = hs_user.sanka_user.user
                login(request, user)
                request.user = user
                request.LANGUAGE_CODE = lang

                permission = get_permission(object_type=object_type, user=user)
                if permission != 'hide':
                    return request
    except:
        pass

    rendered_content = render(request, 'data/static/power-crm-permission.html', {
                              'hs_language': lang, 'no_hs_user': no_hs_user})

    # rendered_content["X-Frame-Options"] = "ALLOW-FROM *"
    rendered_content["Content-Security-Policy"] = "frame-ancestors *;"
    return rendered_content

@login_or_hubspot_required
def hubspot_update_integration(request, id):
    lang = request.LANGUAGE_CODE
    access_token = request.POST.get('hubspot-access-token', None)
    app_name = request.POST.get('hubspot-app-name', None)
    contact_map_settings = request.POST.get('set-contact-as-company', None)
    custom_object_name = request.POST.get('hubspot_custom_object', None)
    workspace = get_workspace(request.user)
    channel = Channel.objects.get(id=id)

    refresh_hubspot_token(channel.id)
    access_token = channel.access_token

    try:
        hubspot_client = HubSpot(access_token=access_token)
        _ = hubspot_client.crm.contacts.get_all()
    except:
        if lang == 'ja':
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message='HubSpot アクセス トークンが間違っている可能性があります。', type='error')
        else:
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message='Your HubSpot access token might be wrong.', type='error')
        return redirect(reverse('integrations', host='app'))

    try:
        # Update channel
        channel.user.add(request.user)
        channel.last_updated_by = request.user
        channel.name = app_name
        channel.access_token = access_token
        # used to determine how to map contacts in hubspot
        channel.ms_refresh_token = contact_map_settings
        # used to determine the custom subscription object name
        channel.service_account = custom_object_name

        channel.status = 'updated'
        channel.save()

        if lang == 'ja':
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message='インテグレーションが作成されました。', type="success")
        else:
            Notification.objects.create(workspace=get_workspace(
                request.user), user=request.user, message='Integration updated successfully.', type="success")

    except:
        Notification.objects.create(workspace=workspace, user=request.user,
                                    message='入力の形式が正しくありませんでした。' if lang == 'ja' else 'Input was malformed.', type="error")

    # api_integration

    return redirect(reverse('integrations', host='app'))
