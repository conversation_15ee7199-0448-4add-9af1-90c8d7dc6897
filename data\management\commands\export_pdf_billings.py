from django.core.mail import EmailMessage
from django.core.management.base import BaseCommand
from django.template.loader import render_to_string
from io import BytesIO
from data.commerce.commerce_functions import generate_zip
from data.commerce import commerce_pdf_download
from data.constants.properties_constant import (
    TYPE_OBJECT_INVOICE, TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_DELIVERY_NOTE, TYPE_OBJECT_SLIP
)
from data.models import (
    View as ViewModel, TransferHistory, Workspace, User, PropertySet, ViewFilter,
    Q, timezone
)
from utils.filter import build_view_filter
from utils.properties.properties import get_page_object


class Command(BaseCommand):

    def add_arguments(self, parser):
        parser.add_argument("--user_id", type=str)
        parser.add_argument("--workspace", type=str)
        parser.add_argument("--view_id", type=str)
        parser.add_argument("--history_id", type=str)
        parser.add_argument("--object_type", type=str)
        parser.add_argument("--language", type=str)
        parser.add_argument("--background_job_id", type=str, default="")
        parser.add_argument("--record_ids", type=str, default="")

    def handle(self, **options):
        workspace_id = options["workspace"] if options["workspace"] != "None" else None
        view_id = options["view_id"] if options["view_id"] != "None" else None
        history_id = options["history_id"] if options["history_id"] != "None" else None
        lang = options["language"] if options["language"] != "None" else 'ja'
        object_type = options["object_type"] if options["object_type"] != "None" else None
        record_ids = options["record_ids"] if options["record_ids"] else ""

        try:
            history = TransferHistory.objects.get(id=history_id)
        except TransferHistory.DoesNotExist:
            print("[DEBUG] - export_pdf_billings - TransferHistory does not exist")
            return

        try:
            workspace = Workspace.objects.get(id=workspace_id)
        except Workspace.DoesNotExist:
            print("[DEBUG] - export_pdf_billings - Workspace does not exist")
            history.status = 'canceled'
            history.save()
            return

        try:
            user = User.objects.get(id=int(options["user_id"]))
        except User.DoesNotExist:
            print("[DEBUG] - export_pdf_billings - User does not exist")
            history.status = 'canceled'
            history.save()
            return

        if not object_type:
            print("[DEBUG] - export_pdf_billings - object_type is required")
            history.status = 'canceled'
            history.save()
            return

        page_obj = get_page_object(object_type, lang)
        id_field = page_obj['id_field']  # id_inv, id_est, id_rcp, id_ds
        default_columns = page_obj['default_columns']
        if id_field and id_field not in default_columns:
            default_columns.insert(0, id_field)
        base_model = page_obj['base_model']  # Invoice
        custom_model = page_obj['custom_model']  # InvoiceNameCustomField etc
        additional_filter_fields = page_obj['additional_filter_fields']

        view = None
        view_filter = None
        if not ViewModel.objects.filter(workspace=workspace, target=object_type).exists():
            view = ViewModel.objects.create(
                workspace=workspace,
                title='main',
                target=object_type,
            )
            view_filter = ViewFilter.objects.create(
                view=view,
                column=default_columns,
                view_type='list',
            )

        try:
            if view_id:
                view = ViewModel.objects.filter(id=view_id).first()
                if object_type == TYPE_OBJECT_SLIP:
                    if not view.form:
                        prop_ = PropertySet.objects.filter(
                            workspace=workspace, target=object_type).first()
                        if prop_:
                            view.form = prop_
                            view.save()
            else:
                view = ViewModel.objects.filter(
                    workspace=workspace,
                    title='main',
                    target=object_type
                ).first()

                view_id = view.id
        except Exception as e:
            print(f'[DEBUG] - export_pdf_billings - Error: {e}')

        if not view:
            view = ViewModel.objects.filter(
                title='main', target=object_type).first()
        view_filter = view.viewfilter_set.first()

        filter_conditions = Q(workspace=workspace)
        filter_conditions = build_view_filter(
            filter_conditions, view_filter, object_type, force_filter_list=additional_filter_fields)

        file_tuple = []

        # Haegwan - removing archived invoices
        if record_ids:
            filter_conditions &= Q(id__in=record_ids.split(","))
        objects = base_model.objects.filter(filter_conditions, usage_status='active').distinct(
            'id', 'created_at').order_by('-created_at')
        namecustomfields = custom_model.objects.filter(
            workspace=workspace).order_by("order").values_list('name', flat=True)
        namecustomfields = [data.lower() for data in namecustomfields]

        try:
            for obj in objects:
                obj_id = getattr(obj, id_field, None)
                obj_id = f"{obj_id:04d}"
                buffer_file = commerce_pdf_download(
                    request=None,
                    id=obj.id,
                    buffer='buffer',
                    object_type=object_type,
                    workspace=workspace,
                    lang=lang
                )
                file_tuple.append(
                    (
                        obj_id + ".pdf", BytesIO(buffer_file.content)
                    )
                )

            zip_data = generate_zip(file_tuple)
        except Exception as e:
            print(f'[DEBUG] - export_pdf_billings - Error: {e}')
            history.status = 'canceled'
            history.save()
            return

        if object_type == TYPE_OBJECT_INVOICE:
            if lang == 'ja':
                file_name = "売上請求レコードのエクスポート"
            else:
                file_name = "Export Invoice Records"
        elif object_type == TYPE_OBJECT_ESTIMATE:
            if lang == 'ja':
                file_name = "見積レコードのエクスポート"
            else:
                file_name = "Export Estimate Records"
        elif object_type == TYPE_OBJECT_RECEIPT:
            if lang == 'ja':
                file_name = "入金レコードのエクスポート"
            else:
                file_name = "Export Payment Records"
        elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
            if lang == 'ja':
                file_name = "納品レコードのエクスポート"
            else:
                file_name = "Export Delivery Note Records"
        elif object_type == TYPE_OBJECT_SLIP:
            if lang == 'ja':
                file_name = "伝票レコードのエクスポート"
            else:
                file_name = "Export Slip Records"
        else:
            if lang == 'ja':
                file_name = "支払請求レコードのエクスポート"
            else:
                file_name = "Export Bill Records"

        if lang == 'ja':
            curr_datetime = timezone.now().strftime('%Y年%m月%d日_%H:%M:%S')
            filename = f'Sanka_{file_name}_{curr_datetime}.zip'
        else:
            curr_datetime = timezone.now().strftime('%Y-%m-%d_%H:%M:%S')
            filename = f'Sanka_{file_name}_{curr_datetime}.zip'

        # Send Email
        # we'll use this for signup users without invitation code
        if lang == 'ja':
            mail_subject = f'Sanka - {file_name}'
            message = render_to_string(
                'data/email/export-billing-ja.html', {'file_name': filename})
        else:
            mail_subject = f'Sanka - {file_name}'
            message = render_to_string(
                'data/email/export-billing.html', {'file_name': filename})

        from_email = 'Sanka <<EMAIL>>'
        to_email = [user.email]
        email_message = EmailMessage(
            mail_subject,
            message,
            from_email,
            to_email
        )

        mimetype = 'application/zip'
        email_message.attach(filename, zip_data, mimetype)
        email_message.send(fail_silently=False)

        history.name = f'Export Orders: {filename}'
        history.status = 'completed'
        history.progress = 100
        history.save()
