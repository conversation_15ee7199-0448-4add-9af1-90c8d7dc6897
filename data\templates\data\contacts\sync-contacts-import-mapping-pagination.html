{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="{% include "data/utility/manage-sync.html" %}">
    <div>
        <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
            {% if LANGUAGE_CODE == 'ja' %}
            マッピングテーブル
            {% else %}
            Mapping Table
            {% endif %}
        </span>
    
        <input hidden name="platform" value="{{platform}}">

        <input hidden name="contacts_columns" value="{{contacts_columns}}">

        <input hidden name="header_mapping_id" value="{{header_mapping_id}}">

        <input hidden name="previous_page" value="{{page}}">
        
        <input hidden name="source_object_type" value="{{object_type}}">

        <!-- Add a search input field -->
        <div class="d-flex flex-column mb-5 fv-row">
            <div class="mb-3 mt-3 w-50 d-flex">
                <input type="text" name="search_q" class="form-control input-prehead h-40px" placeholder="{% if LANGUAGE_CODE == 'ja' %}テーブルを検索...{% else %}Search the Table...{% endif %}">
                <button type="submit" class="btn btn-primary input-posthead h-40px w-100px "
                    hx-post="{% host_url 'sync_header_extractor_page' host 'app' %}"
                    hx-target="#table-body"
                    hx-encoding="multipart/form-data"
                    hx-indicator=".loading-spinner"
                    hx-vals='{"object_type":"contact"}'
                    hx-on::before-send="
                        document.getElementById('table-body').classList.add('d-none');
                        document.getElementById('table-body').innerHTML = '';
                        document.getElementById('loading-spinner-header').classList.remove('d-none');">
                    {% translate_lang "Search" LANGUAGE_CODE %}
                </button>
            </div>
        </div>

        <input hidden class="form-check-input" type="checkbox"
            hx-post="{% host_url 'sync_header_extractor_page' host 'app' %}"
            hx-target="#table-body"
            hx-trigger="load"
            hx-encoding="multipart/form-data"
            hx-vals='{"object_type":"contact","page":1}'
            hx-indicator=".loading-spinner"
            hx-on::before-send="
                document.getElementById('table-body').classList.add('d-none');
                document.getElementById('table-body').innerHTML = '';
                document.getElementById('loading-spinner-header').classList.remove('d-none');"
        ></input>

        <div id="table-body">

        </div>

        <div id="loading-spinner-header" class="text-center mb-3 d-none">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div>
                {% if LANGUAGE_CODE == 'ja' %}
                プロパティを読み込んでいます...
                {% else %}
                Loading Properties...
                {% endif %}
            </div>
        </div>

        <!-- Pagination controls -->
        <div id="header-pagination" class="d-flex justify-content-between align-items-center my-3">
            <div>
                <span id="pagination-info">
                    {% if LANGUAGE_CODE == 'ja' %}
                    1 - <span id="items-per-page">25</span> / <span id="total-items">0</span> アイテム
                    {% else %}
                    1 - <span id="items-per-page">25</span> of <span id="total-items">0</span> items
                    {% endif %}
                </span>
            </div>
            <div class="btn-group">
                <button type="button" id="prev-page" class="btn btn-light border" disabled
                        hx-post="{% host_url 'sync_header_extractor_page' host 'app' %}"
                        hx-target="#table-body"
                        hx-encoding="multipart/form-data"
                        hx-vals='{"object_type":"contact","page":1}'
                        hx-indicator=".loading-spinner"
                        hx-on::before-send="
                            document.getElementById('table-body').classList.add('d-none');
                            document.getElementById('table-body').innerHTML = '';
                            document.getElementById('loading-spinner-header').classList.remove('d-none');">
                    {% translate_lang "Previous" LANGUAGE_CODE %}
                </button>
                <span id="page-numbers" class="d-flex"></span>
                <button type="button" id="next-page" class="btn btn-light border"
                        hx-post="{% host_url 'sync_header_extractor_page' host 'app' %}"
                        hx-target="#table-body"
                        hx-encoding="multipart/form-data"
                        hx-vals='{"object_type":"contact","page":2}'
                        hx-indicator=".loading-spinner"
                        hx-on::before-send="
                            document.getElementById('table-body').classList.add('d-none');
                            document.getElementById('table-body').innerHTML = '';
                            document.getElementById('loading-spinner-header').classList.remove('d-none');">
                    {% translate_lang "Next" LANGUAGE_CODE %}
                </button>
            </div>
        </div>
    </div> 

    <script>
        $(document).ready(function() {
            initMapping();
        });

        function initMapping() {
            // Hide content and show spinner at the very beginning
            if (document.getElementById('csvMappingContainer')) {
                document.getElementById('csvMappingContainer').classList.add('d-none');
                document.getElementById('loading-spinner').classList.remove('d-none');
            }
            
            // Step 1: Run pagination setup first
            setupPagination();
            
            // Step 3: After Select2 initialization is complete, set up events
            setTimeout(function() {

                setupCheckboxHandlers();
                // Show content and hide spinner when everything is done
                if (document.getElementById('csvMappingContainer')) {
                    document.getElementById('loading-spinner').classList.add('d-none');
                    document.getElementById('csvMappingContainer').classList.remove('d-none');
                }
            }, 50);
        }

        // Pagination functionality
        function setupPagination() {
            const itemsPerPage = 25;
            const totalItems = "{{header_length}}";
            
            // Update total count display
            document.getElementById('total-items').textContent = totalItems;
            document.getElementById('items-per-page').textContent = Math.min(itemsPerPage, totalItems);
            
            let currentPage = 1;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            
            function showImportPage(page) {
                const start = (page - 1) * itemsPerPage;
                const end = start + itemsPerPage;
                
                // Update pagination info
                const firstVisible = Math.min(start + 1, totalItems);
                const lastVisible = Math.min(end, totalItems);
                document.getElementById('pagination-info').innerHTML = 
                    `${firstVisible} - ${lastVisible} ${LANGUAGE_CODE === 'ja' ? 'アイテム / ' : 'of '} ${totalItems} ${LANGUAGE_CODE === 'ja' ? '' : 'items'}`;
                
                // Update button states
                document.getElementById('prev-page').disabled = (page === 1);
                document.getElementById('next-page').disabled = (page === totalPages);
                
                // Update current page
                currentPage = page;
                
                // Update page numbers
                updatePageNumbers();
            }
            
            function updatePageNumbers() {
                const pageNumbers = document.getElementById('page-numbers');
                pageNumbers.innerHTML = '';
                
                // Determine range of pages to show
                let startPage = Math.max(1, currentPage - 1);
                let endPage = Math.min(totalPages, startPage + 2);
                
                // Adjust if we're near the end
                if (endPage - startPage < 2) {
                    startPage = Math.max(1, endPage - 2);
                }
                
                // Add first page button if not showing from page 1
                if (startPage > 1) {
                    addPageButton(1, pageNumbers);
                    if (startPage > 2) {
                        addEllipsis(pageNumbers);
                    }
                }
                
                // Add numbered page buttons
                for (let i = startPage; i <= endPage; i++) {
                    addPageButton(i, pageNumbers);
                }
                
                // Add last page button if not showing to the last page
                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        addEllipsis(pageNumbers);
                    }
                    addPageButton(totalPages, pageNumbers);
                }
            }
            
            function addPageButton(pageNum, container) {
                const button = document.createElement('button');
                button.className = `btn btn-light border rounded-0 ${pageNum === currentPage ? 'active btn-primary text-white' : ''}`;
                button.textContent = pageNum;
                button.setAttribute('hx-post', '{% host_url "sync_header_extractor_page" host "app" %}');
                button.setAttribute('hx-target', '#table-body');
                button.setAttribute('hx-encoding', 'multipart/form-data');
                button.setAttribute('hx-vals', JSON.stringify({
                    object_type: 'contact',
                    page: pageNum
                }));
                button.setAttribute('hx-indicator', '.loading-spinner');
                button.setAttribute('hx-on::before-send', `
                    document.getElementById('table-body').classList.add('d-none');
                    document.getElementById('table-body').innerHTML = '';
                    document.getElementById('loading-spinner-header').classList.remove('d-none');
                `);
                htmx.process(button);

                button.addEventListener('click', () => showImportPage(pageNum));
                container.appendChild(button);
            }
            
            function addEllipsis(container) {
                const span = document.createElement('span');
                span.className = 'btn btn-light border disabled rounded-0';
                span.textContent = '...';
                container.appendChild(span);
            }
            
            // Set up event listeners
            document.getElementById('prev-page').addEventListener('click', () => {
                if (currentPage > 1) {
                    const newPage = currentPage - 1;
                    const prevButton = document.getElementById('prev-page');
                    prevButton.setAttribute('hx-vals', JSON.stringify({
                        object_type: 'contact',
                        page: newPage
                    }));
                    showImportPage(newPage);
                }
            });
            
            document.getElementById('next-page').addEventListener('click', () => {
                if (currentPage < totalPages) {
                    const newPage = currentPage + 1;
                    const nextButton = document.getElementById('next-page');
                    nextButton.setAttribute('hx-vals', JSON.stringify({
                        object_type: 'contact',
                        page: newPage
                    }));
                    showImportPage(newPage);
                }
            });
            
            // Show first page initially
            showImportPage(1);
        }

        function setupCheckboxHandlers() {
            // Use event delegation instead of attaching to each checkbox
            const checkboxesContainer = document.getElementById('body-section');
            if (!checkboxesContainer) return;
            
            // Set up the global variable for shift-click functionality
            window.lastChecked = null;
            
            checkboxesContainer.addEventListener('click', function(e) {
                // Only handle checkbox clicks
                if (e.target.type !== 'checkbox') return;
                
                if (e.shiftKey && window.lastChecked) {
                    const checkboxes = Array.from(checkboxesContainer.querySelectorAll('input[type="checkbox"]'));
                    let start = checkboxes.indexOf(window.lastChecked);
                    let end = checkboxes.indexOf(e.target);

                    if (start > -1 && end > -1) {
                        const range = checkboxes.slice(
                            Math.min(start, end), 
                            Math.max(start, end) + 1
                        );
                        
                        range.forEach(checkbox => {
                            checkbox.checked = window.lastChecked.checked;
                            updateHiddenInput(checkbox);
                        });
                    }
                } else {
                    updateHiddenInput(e.target);
                }
                
                window.lastChecked = e.target;
            });

            function updateHiddenInput(checkbox) {
                const parent = checkbox.parentNode;
                const nextInput = parent.querySelector('input[type="hidden"]');
                if (nextInput) {
                    nextInput.value = checkbox.checked ? "True" : "False";
                }
            }
        }

        function showSankaProperties(elm){
            var header = elm.id.split("-");
            header = header[header.length - 1]
            
            var sanka_properties_contacts_id = "sanka-properties-"+header
            var sanka_properties_company_id = "sanka-properties-company-"+header
            var sanka_properties_contacts_elm = document.getElementById(sanka_properties_contacts_id)
            var sanka_properties_company_elm = document.getElementById(sanka_properties_company_id)
            
            if (elm.value == "contact"){
                if (sanka_properties_contacts_elm && sanka_properties_company_elm){
                    sanka_properties_contacts_elm.classList.remove("d-none")
                    sanka_properties_company_elm.classList.add("d-none")
                }
            }
            else if (elm.value == "company"){
                if (sanka_properties_contacts_elm && sanka_properties_company_elm){
                    sanka_properties_contacts_elm.classList.add("d-none")
                    sanka_properties_company_elm.classList.remove("d-none")
                }
            }
        }
    </script>

    {% if integration_type == 'salesforce' %}
    <div id="salesforce-filter-field" class="mt-8">
        <div id="filter-checkbox" class="form-check mb-5">
            <input class="form-check-input" type="checkbox" id="enable-salesforce-filter" name="enable-salesforce-filter" value=1>
            <label class="form-check-label ms-1 fs-5 text-gray-700" for="enable-salesforce-filter" >
                {% if LANGUAGE_CODE == 'ja' %}フィルターを有効にする{% else %}Enable Filters{% endif %}
            </label>
        </div>
        
        <div id="salesforce-filters-container" class="border border-gray-300 rounded p-4 bg-light-subtle">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <label class="fs-6 fw-bold text-dark mb-0">
                    {% if LANGUAGE_CODE == 'ja' %}
                        Salesforce フィルター
                    {% else %}
                        Salesforce Filters
                    {% endif %}
                </label>
                <button type="button" id="add-filter-btn" class="btn btn-primary btn-sm d-flex align-items-center gap-2">
                    +
                    <span class="fw-semibold">
                        {% if LANGUAGE_CODE == 'ja' %}フィルターを追加{% else %}Add Filter{% endif %}
                    </span>
                </button>
            </div>
            
            <div id="filters-list" class="mb-3">
                <!-- Filter rows will be added here dynamically -->
            </div>
            
            <!-- Hidden input to store filters JSON -->
            <input type="hidden" name="salesforce-filters" id="salesforce-filters-input" value="[]">
        </div>
        
        <script>
            $(document).ready(function() {
                initializeSalesforceFilters();
            });
            
            let filterIndex = 0;
            let filtersData = [];
            
            // Language-specific strings
            const i18n = {
                fieldLabel: "{% if LANGUAGE_CODE == 'ja' %}フィールド{% else %}Field{% endif %}",
                valueLabel: "{% if LANGUAGE_CODE == 'ja' %}値{% else %}Value{% endif %}",
                selectFieldPlaceholder: "{% if LANGUAGE_CODE == 'ja' %}フィールドを選択...{% else %}Select Field...{% endif %}",
                valuePlaceholder: "{% if LANGUAGE_CODE == 'ja' %}値を入力...{% else %}Enter value...{% endif %}",
                noResults: "{% if LANGUAGE_CODE == 'ja'%}フィールドが見つかりません{% else %}No field found{% endif %}",
                searching: "{% if LANGUAGE_CODE == 'ja'%}検索中...{% else %}Searching...{% endif %}",
                loadingMore: "{% if LANGUAGE_CODE == 'ja'%}読み込み中...{% else %}Loading more...{% endif %}"
            };
            
            function initializeSalesforceFilters() {
                // Add first filter row by default
                addFilterRow();
                
                // Add filter button click handler
                $('#add-filter-btn').click(function() {
                    addFilterRow();
                });
                
                // Global event delegation for remove buttons
                $(document).on('click', '.remove-filter-btn', function() {
                    const index = $(this).data('filter-index');
                    console.log('Remove button clicked for index:', index);
                    removeFilterRow(index);
                });
                
                // Enable/disable filters based on checkbox
                $('#enable-salesforce-filter').change(function() {
                    console.log("Checkbox changed:", $(this).is(':checked'));
                    const isEnabled = $(this).is(':checked');
                    $('#salesforce-filters-container').toggle(isEnabled);
                    if (!isEnabled) {
                        filtersData = [];
                        updateFiltersInput();
                    }
                });
                
                // Initially hide filters container
                $('#salesforce-filters-container').hide();
            }
            
            function addFilterRow() {
                const rowId = 'filter-row-' + filterIndex;
                const selectId = 'salesforce-field-' + filterIndex;
                const valueId = 'salesforce-value-' + filterIndex;
                
                const filterRow = `
                    <div class="filter-row mb-4" id="${rowId}">
                        <div class="d-flex align-items-end gap-3">
                            <div class="flex-fill">
                                <label class="form-label small fw-semibold text-gray-700 mb-2">
                                    ${i18n.fieldLabel}
                                </label>
                                <select id="${selectId}" class="form-select h-40px select2-salesforce-field" data-filter-index="${filterIndex}">
                                    <option value="">${i18n.selectFieldPlaceholder}</option>
                                </select>
                            </div>
                            <div class="flex-fill">
                                <label class="form-label small fw-semibold text-gray-700 mb-2">
                                    ${i18n.valueLabel}
                                </label>
                                <input type="text" id="${valueId}" class="form-control h-40px" data-filter-index="${filterIndex}" placeholder="${i18n.valuePlaceholder}">
                            </div>
                            <div class="flex-shrink-0">
                                <button type="button" class="btn btn-sm btn-light-danger h-40px w-40px remove-filter-btn" data-filter-index="${filterIndex}" title="Remove filter">
                                    X
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#filters-list').append(filterRow);
                
                // Initialize Select2 for the new field dropdown
                initializeFieldSelect(selectId, filterIndex);
                
                // Add event handlers
                $(`#${valueId}`).on('input', function() {
                    const currentIndex = $(this).data('filter-index');
                    console.log(`Text input event fired for index: ${currentIndex}, value: ${$(this).val()}`);
                    updateFilterData(currentIndex);
                });
                
                filterIndex++;
                updateRemoveButtonsVisibility();
            }
            
            function initializeFieldSelect(selectId, index) {
                $(`#${selectId}`).select2({
                    ajax: {
                        delay: 250,
                        dataType: 'json',
                        url: '{% host_url "get_salesforce_field" host "app" %}',
                        data: function (params) {
                            return {
                                q: params.term,
                                page: params.page || 1,
                                channel_id: '{{ channel_id }}',
                                object_type: 'contact',
                                json_response: true
                            };
                        },
                        minimumInputLength: 2,
                    },
                    language: {
                        "noResults": function(){
                            return i18n.noResults;
                        },
                        searching: function(){
                            return i18n.searching;
                        },
                        "loadingMore": function(){
                            return i18n.loadingMore;
                        },
                    },
                }).on('change', function() {
                    updateFilterData(index);
                });
            }
            
            function removeFilterRow(index) {
                $(`#filter-row-${index}`).remove();
                // Remove from filtersData array
                filtersData = filtersData.filter(filter => filter.index !== index);
                updateFiltersInput();
                updateRemoveButtonsVisibility();
            }
            
            function updateFilterData(index) {
                const fieldSelect = $(`#salesforce-field-${index}`);
                const valueInput = $(`#salesforce-value-${index}`);
                
                const field = fieldSelect.val();
                const value = valueInput.val();
                
                console.log(`updateFilterData called for index ${index}:`, { field, value });
                
                // Remove existing data for this index
                filtersData = filtersData.filter(filter => filter.index !== index);
                
                // Always store filter data (even if incomplete) to maintain UI state
                if (field || value) {
                    filtersData.push({
                        index: index,
                        field: field || '',
                        value: value || ''
                    });
                    console.log('Added to filtersData:', filtersData);
                } else {
                    console.log('No field or value, not adding to filtersData');
                }
                
                updateFiltersInput();
            }
            
            function updateFiltersInput() {
                // Convert to the format expected by backend (without index)
                // Only include complete filters (both field and value present)
                console.log('updateFiltersInput called with filtersData:', filtersData);
                const backendFilters = filtersData
                    .filter(filter => filter.field && filter.value)
                    .map(filter => ({
                        field: filter.field,
                        value: filter.value
                    }));
                console.log('Complete filters for backend:', backendFilters);
                const jsonValue = JSON.stringify(backendFilters);
                console.log('Setting salesforce-filters-input to:', jsonValue);
                $('#salesforce-filters-input').val(jsonValue);
                console.log('Current value of salesforce-filters-input:', $('#salesforce-filters-input').val());
            }
            
            function updateRemoveButtonsVisibility() {
                const filterRows = $('.filter-row');
                console.log('Filter rows count:', filterRows.length);
                if (filterRows.length <= 1) {
                    $('.remove-filter-btn').hide();
                } else {
                    $('.remove-filter-btn').show();
                }
            }
        </script>
    </div>
    {% endif %}
</div>