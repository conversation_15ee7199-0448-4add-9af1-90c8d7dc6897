import traceback
from typing import Dict, Any
from utils.logger import logger
from ..utilities.client_utils import ShopifyGraphQLClient
from ..queries.order_queries import ORDERS_QUERY, DRAFT_ORDERS_QUERY, DISCOUNT_ORDER_QUERY

api_version = "2025-07"


class GraphQLOrderAdapter:
    """
    Adapter class to make GraphQL order responses compatible with REST API responses.
    This ensures the existing business logic continues to work unchanged.
    """
    
    def __init__(self, order_data: Dict[str, Any], is_draft: bool = False):
        self.data = order_data
        self.is_draft = is_draft
        self._setup_attributes()
    
    def _setup_attributes(self):
        """Setup attributes to match REST API structure"""
        # Basic order information
        self.id = self.data.get('legacyResourceId', self.data.get('id'))
        self.name = self.data.get('name')
        self.order_number = self.data.get('number')
        self.currency = self.data.get('currencyCode')
        self.created_at = self.data.get('createdAt')
        
        # Price information
        subtotal_price_set = self.data.get('subtotalPriceSet', {})
        shop_money = subtotal_price_set.get('shopMoney', {})
        self.subtotal_price = shop_money.get('amount')
        
        total_price_set = self.data.get('totalPriceSet', {})
        shop_money = total_price_set.get('shopMoney', {})
        self.total_price = shop_money.get('amount')
        
        # Tax information
        if self.is_draft:
            tax_set = self.data.get('totalTaxSet', {})
        else:
            tax_set = self.data.get('currentTotalTaxSet', {})
        
        tax_shop_money = tax_set.get('shopMoney', {})
        self.total_tax = tax_shop_money.get('amount')
        self.taxes_included = self.data.get('taxesIncluded', False)
        
        # For backward compatibility
        self.tax_inclusive = self.taxes_included
        
        # Status information
        if self.is_draft:
            self.financial_status = None
            self.fulfillment_status = None
            self.return_status = None
        else:
            self.financial_status = self.data.get('displayFinancialStatus')
            self.fulfillment_status = self.data.get('displayFulfillmentStatus')
            self.return_status = self.data.get('returnStatus')
        
        # Customer information
        customer_data = self.data.get('customer')
        if customer_data:
            self.customer = GraphQLCustomerAdapter(customer_data)
        else:
            self.customer = None
        
        # Line items
        line_items_data = self.data.get('lineItems', {}).get('edges', [])
        self.line_items = [GraphQLLineItemAdapter(item['node']) for item in line_items_data]
        
        # Shipping information
        shipping_data = self.data.get('shippingLine')
        if shipping_data:
            self.shipping_line = GraphQLShippingLineAdapter(shipping_data)
        else:
            self.shipping_line = None
        
        # Set klass for draft order detection
        if self.is_draft:
            # Import the actual DraftOrder class for compatibility
            import shopify
            self.klass = shopify.resources.draft_order.DraftOrder
        else:
            import shopify
            self.klass = shopify.resources.order.Order


class GraphQLCustomerAdapter:
    """Adapter for customer data"""
    
    def __init__(self, customer_data: Dict[str, Any]):
        self.data = customer_data
        self.id = customer_data.get('legacyResourceId', customer_data.get('id'))
        self.first_name = customer_data.get('firstName')
        self.last_name = customer_data.get('lastName')
        self.email = customer_data.get('email')
        self.phone = customer_data.get('phone')
        
        # Addresses
        addresses_data = customer_data.get('addresses', [])
        self.addresses = [GraphQLAddressAdapter(addr) for addr in addresses_data]


class GraphQLAddressAdapter:
    """Adapter for address data"""
    
    def __init__(self, address_data: Dict[str, Any]):
        self.data = address_data
        self.zip = address_data.get('zip')
        self.address1 = address_data.get('address1')
        self.address2 = address_data.get('address2')
        self.city = address_data.get('city')
        self.province = address_data.get('province')
        self.country = address_data.get('country')
        self.phone = address_data.get('phone')


class GraphQLLineItemAdapter:
    """Adapter for line item data"""
    
    def __init__(self, line_item_data: Dict[str, Any]):
        self.data = line_item_data
        self.id = line_item_data.get('id')
        self.name = line_item_data.get('name')
        self.quantity = line_item_data.get('quantity')
        
        # Price information
        price_set = line_item_data.get('originalUnitPriceSet', {})
        shop_money = price_set.get('shopMoney', {})
        self.price = shop_money.get('amount')
        
        # Product information
        variant_data = line_item_data.get('variant')
        if variant_data:
            self.product_id = variant_data.get('legacyResourceId')
            self.sku = variant_data.get('sku')
            
            # Get product info from variant
            product_data = variant_data.get('product')
            if product_data:
                self.product_id = product_data.get('legacyResourceId')
        else:
            # Fallback to direct product info
            product_data = line_item_data.get('product')
            if product_data:
                self.product_id = product_data.get('legacyResourceId')
                self.sku = None
            else:
                self.product_id = None
                self.sku = None


class GraphQLShippingLineAdapter:
    """Adapter for shipping line data"""
    
    def __init__(self, shipping_data: Dict[str, Any]):
        self.data = shipping_data
        
        # Price information
        price_set = shipping_data.get('originalPriceSet', {})
        shop_money = price_set.get('shopMoney', {})
        amount = shop_money.get('amount')
        currency = shop_money.get('currencyCode')
        
        # Create the expected structure
        self.original_price_set = {
            "shop_money": {
                "amount": amount,
                "currency_code": currency
            }
        }
        
        # Tax lines
        tax_lines_data = shipping_data.get('taxLines', [])
        self.tax_lines = [GraphQLTaxLineAdapter(tax_line) for tax_line in tax_lines_data]


class GraphQLTaxLineAdapter:
    """Adapter for tax line data"""
    
    def __init__(self, tax_line_data: Dict[str, Any]):
        self.data = tax_line_data
        self.rate = tax_line_data.get('rate')
        self.title = tax_line_data.get('title')


class GraphQLLineDiscountAdapter:
    """Adapter for line discount data"""

    def __init__(self, order_discount: Dict[str, Any]):
        self.data = order_discount
        # self.code = order_discount.get('code', None)
        # self.description = order_discount.get('description', None)
        self.target = order_discount.get('targetType', None)

        value = order_discount.get('value', None)
        self.type = value.get('__typename', None)
        if self.type == 'PricingPercentageValue':
            self.currency = None
            self.percentage = value.get('percentage', None)
            self.amount = 0
        elif self.type == 'MoneyV2':
            self.currency = value.get('currencyCode', None)
            self.amount = value.get('amount', None)
            self.percentage = 0

def get_shopify_orders(api_access_key, account_id):
    """
    Fetch orders and draft orders from Shopify using GraphQL API.
    Returns a list of order objects compatible with the REST API format.
    """
    orders = []
    
    try:
        # Create a temporary channel-like object for the GraphQL client
        from data.models import Channel
        
        # Find the channel by matching the account_id and access_token
        channel = Channel.objects.filter(
            account_id=account_id,
            access_token=api_access_key
        ).first()
        
        if not channel:
            logger.error(f"No channel found for account_id: {account_id}")
            return orders
        
        with ShopifyGraphQLClient(channel.id) as client:
            # Fetch regular orders
            try:
                logger.info("Executing orders GraphQL query...")
                orders_result = client.execute(
                    ORDERS_QUERY,
                    variables={
                        "first": 250,  # Max allowed per request
                        "query": ""
                    }
                )
                
                logger.info(f"Orders GraphQL response keys: {list(orders_result.keys())}")
                
                orders_data = orders_result.get('orders', {})
                orders_edges = orders_data.get('edges', [])
                
                logger.info(f"Orders data structure keys: {list(orders_data.keys()) if orders_data else 'No orders data'}")
                logger.info(f"Number of order edges: {len(orders_edges)}")
                
                if not orders_edges and orders_data:
                    logger.info(f"Full orders data: {orders_data}")
                
                for edge in orders_edges:
                    order_node = edge['node']
                    order_adapter = GraphQLOrderAdapter(order_node, is_draft=False)
                    orders.append(order_adapter)
                
                # Handle pagination if needed
                page_info = orders_data.get('pageInfo', {})
                while page_info.get('hasNextPage'):
                    cursor = page_info.get('endCursor')
                    
                    next_result = client.execute(
                        ORDERS_QUERY,
                        variables={
                            "first": 250,
                            "after": cursor,
                            "query": ""
                        }
                    )
                    
                    next_orders_data = next_result.get('orders', {})
                    next_edges = next_orders_data.get('edges', [])
                    
                    for edge in next_edges:
                        order_node = edge['node']
                        order_adapter = GraphQLOrderAdapter(order_node, is_draft=False)
                        orders.append(order_adapter)
                    
                    page_info = next_orders_data.get('pageInfo', {})
                
                logger.info(f"Fetched {len(orders)} regular orders")
                
            except Exception as e:
                logger.error(f"Error fetching orders: {str(e)}")
                traceback.print_exc()
            
            # Fetch draft orders
            try:
                logger.info("Executing draft orders GraphQL query...")
                draft_orders_result = client.execute(
                    DRAFT_ORDERS_QUERY,
                    variables={
                        "first": 250,
                        "query": ""
                    }
                )
                
                logger.info(f"Draft Orders GraphQL response keys: {list(draft_orders_result.keys())}")
                
                draft_orders_data = draft_orders_result.get('draftOrders', {})
                draft_orders_edges = draft_orders_data.get('edges', [])
                
                logger.info(f"Draft Orders data structure keys: {list(draft_orders_data.keys()) if draft_orders_data else 'No draft orders data'}")
                logger.info(f"Number of draft order edges: {len(draft_orders_edges)}")
                
                if not draft_orders_edges and draft_orders_data:
                    logger.info(f"Full draft orders data: {draft_orders_data}")
                
                for edge in draft_orders_edges:
                    draft_order_node = edge['node']
                    draft_order_adapter = GraphQLOrderAdapter(draft_order_node, is_draft=True)
                    orders.append(draft_order_adapter)
                
                # Handle pagination if needed
                page_info = draft_orders_data.get('pageInfo', {})
                while page_info.get('hasNextPage'):
                    cursor = page_info.get('endCursor')
                    
                    next_result = client.execute(
                        DRAFT_ORDERS_QUERY,
                        variables={
                            "first": 250,
                            "after": cursor,
                            "query": ""
                        }
                    )
                    
                    next_draft_orders_data = next_result.get('draftOrders', {})
                    next_edges = next_draft_orders_data.get('edges', [])
                    
                    for edge in next_edges:
                        draft_order_node = edge['node']
                        draft_order_adapter = GraphQLOrderAdapter(draft_order_node, is_draft=True)
                        orders.append(draft_order_adapter)
                    
                    page_info = next_draft_orders_data.get('pageInfo', {})
                
                logger.info(f"Fetched {len([o for o in orders if o.is_draft])} draft orders")
                
            except Exception as e:
                logger.error(f"Error fetching draft orders: {str(e)}")
                traceback.print_exc()
        
        logger.info(f"Shopify GraphQL API version: {api_version}")
        logger.info(f"Total orders fetched: {len(orders)}")
        
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Failed to fetch shopify orders: {str(e)}")
    
    return orders


def get_shopify_order_discount(api_access_key, account_id, order_id):
    """
    Fetch orders and draft orders from Shopify using GraphQL API.
    Returns a list of order objects compatible with the REST API format.
    """
    discount_data = []

    try:
        # Create a temporary channel-like object for the GraphQL client
        from data.models import Channel

        # Find the channel by matching the account_id and access_token
        channel = Channel.objects.filter(
            account_id=account_id,
            access_token=api_access_key
        ).first()

        if not channel:
            logger.error(f"No channel found for account_id: {account_id}")
            return discount_data

        with ShopifyGraphQLClient(channel.id) as client:
            # Fetch regular orders
            try:
                logger.info("Executing orders discount GraphQL query...")
                discount_result = client.execute(
                    DISCOUNT_ORDER_QUERY,
                    variables={
                        "first": 250,  # Max allowed per request
                        "id": order_id,
                        "query": ""
                    }
                )

                logger.info(
                    f"Orders GraphQL response keys: {list(discount_result.keys())}")

                discount_data_ = discount_result.get('order', {})

                discount_datas = discount_data_[
                    'discountApplications']['edges']

                for edge in discount_datas:
                    discount_node = edge['node']
                    discount_adapter = GraphQLLineDiscountAdapter(
                        discount_node)
                    discount_data.append(discount_adapter)

                logger.info(f"Discount data: {discount_data}")

            except Exception as e:
                logger.error(f"Error fetching orders: {str(e)}")
                traceback.print_exc()

        logger.info(f"Shopify GraphQL API version: {api_version}")

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Failed to fetch shopify discount: {str(e)}")

    return discount_data
