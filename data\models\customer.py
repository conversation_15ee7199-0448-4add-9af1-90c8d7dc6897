import uuid

from data.constants.constant import OBJECTS_CF_TYPE, CONTACT_CF_TYPE
from data.models import ObjectManager
from data.models.constant import (
    PLATFORM,
    POST_MEDIA_TYPE,
    USAGE_STATUS,
)
from data.constants.constant import (
    CURRENCY_MODEL,
    WORKER_STATUS,
    SHOPTURBO_NUMBER_FORMAT,
    ABSENCE_TYPE,
    ABSENCE_STATUS,
)
from data.models.base import save_tags_values, validate_value_custom_field

from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from sanka.storage_backends import PrivateMediaStorage

COMPANY_CF = OBJECTS_CF_TYPE + [
    ("hierarchy", "Hierarchy"),
]

COMPANY_STATUS = [
    ("active", "Active"),
    ("archived", "Archived"),
]


class Company(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    address = models.CharField(max_length=500, null=True, blank=True)
    email = models.EmailField(max_length=70, null=True, blank=True)
    url = models.URLField(max_length=1000, null=True, blank=True)
    phone_number = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    image_file = models.FileField(
        verbose_name="Company file",
        upload_to="social-account-files",
        null=True,
        blank=True,
    )
    status = models.CharField(choices=COMPANY_STATUS, max_length=30, default="active")
    allowed_in_store = models.BooleanField(default=True)

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True)

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    def save(self, *args, **kwargs):
        from django.db import transaction, connection
        
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if not self.company_id:
            # Use atomic transaction with table lock to prevent race conditions
            with transaction.atomic():
                # Lock the table to prevent concurrent ID generation
                with connection.cursor() as cursor:
                    cursor.execute(
                        f'LOCK TABLE "{Company._meta.db_table}" IN EXCLUSIVE MODE'
                    )
                
                # Generate a new company_id if it doesn't exist
                last_company = (
                    Company.objects.filter(
                        workspace=self.workspace, company_id__isnull=False
                    )
                    .order_by("-company_id")
                    .first()
                )
                
                if last_company and last_company.company_id:
                    try:
                        last_company_id = int(last_company.company_id)
                    except (ValueError, TypeError):
                        last_company_id = 0
                    next_company_id = last_company_id + 1
                else:
                    # If it's the first company, start with 1
                    next_company_id = 1

                # Format the company_id as a three-digit string
                self.company_id = f"{next_company_id:03d}"

        super().save(*args, **kwargs)

    def get_display_name(self):
        platforms = self.company_platform.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.platform_id
        return data


class CompanyNotes(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, null=True, blank=True
    )
    description = models.TextField(null=True, blank=True)
    date = models.DateTimeField(
        null=True,
        blank=True,
    )
    assignee = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(default=timezone.now)


class CompanyFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    company = models.ForeignKey(
        Company, on_delete=models.CASCADE, null=True, blank=True
    )
    file = models.FileField(verbose_name="Task File", upload_to="task-files")
    created_at = models.DateTimeField(default=timezone.now)


class CompanyPlatforms(models.Model):
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="company_platform",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


CONTACT_STATUS = [
    ("active", "Active"),
    ("archived", "Archived"),
]


class Contact(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    contact_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    last_name = models.CharField(max_length=500, null=True, blank=True)
    email = models.EmailField(max_length=70, null=True, blank=True)
    image_url = models.URLField(max_length=1000, null=True, blank=True)
    company = models.CharField(max_length=500, null=True, blank=True)
    companies = models.ManyToManyField(Company, blank=True)
    phone_number = models.CharField(max_length=256, null=True, blank=True)
    image_file = models.FileField(
        verbose_name="Social Account file",
        upload_to="social-account-files",
        null=True,
        blank=True,
    )
    status = models.CharField(choices=CONTACT_STATUS, max_length=30, default="active")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    property_set = models.ForeignKey(
        "PropertySet", on_delete=models.SET_NULL, null=True, blank=True
    )
    allowed_in_store = models.BooleanField(default=True)

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True)

    # Association with CustomObject
    custom_object_relation = models.ManyToManyField(
        "CustomObjectPropertyRow", blank=True
    )

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()

    def save(self, *args, **kwargs):
        from utils.safe_id_generation import safe_id_mixin_save
        
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        # Use the safe ID generation utility to prevent race conditions
        safe_id_mixin_save(self, 'contact_id', '{:03d}')

        super().save(*args, **kwargs)

    def get_display_name(self):
        platforms = self.contact_platform.all()
        data = {}
        for platform in platforms:
            data[platform.channel.name.lower()] = platform.platform_id
        return data


class ContactsPlatforms(models.Model):
    contact = models.ForeignKey(
        Contact,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="contact_platform",
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True
    )
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    platform_phone_number = models.CharField(max_length=200, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class ContactsMappingFields(models.Model):
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    platform = models.CharField(choices=PLATFORM, max_length=100, null=True, blank=True)
    object_type = models.CharField(
        max_length=100, null=True, blank=True, default="contact"
    )
    input_data = models.JSONField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

class TempHeaderList(models.Model):
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    header_list = models.JSONField(null=True, blank=True)
    object_type = models.CharField(
        max_length=100, null=True, blank=True, default="contact"
    )
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


CUSTOMER_APP = [
    ("contact-list", "Contact List"),
    ("company-list", "Company List"),
]


class ContactLineChannel(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    line_channel_id = models.CharField(max_length=200, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    contact = models.ForeignKey(
        Contact, on_delete=models.CASCADE, null=True, blank=True
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


class ContactsNotes(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    contact = models.ForeignKey(
        Contact, on_delete=models.CASCADE, null=True, blank=True
    )
    description = models.TextField(null=True, blank=True)
    date = models.DateTimeField(
        null=True,
        blank=True,
    )
    assignee = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(default=timezone.now)


class CustomerDynamicList(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    contact_list = models.ForeignKey(
        "contactlist", on_delete=models.CASCADE, null=True, blank=True
    )
    company_list = models.ForeignKey(
        "companylist", on_delete=models.CASCADE, null=True, blank=True
    )
    page = models.CharField(max_length=200, null=True, blank=True, choices=CUSTOMER_APP)
    filter_value = models.JSONField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)


class ContatcsFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    contact = models.ForeignKey(
        Contact, on_delete=models.CASCADE, null=True, blank=True
    )  # member posts engagement
    name = models.CharField(max_length=256, null=True, blank=True)
    bussiness_card = models.FileField(
        verbose_name="Business Card file",
        upload_to="bussiness-card",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id)


class SocialAccountLink(models.Model):
    """For Contacts Related"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    socialaccount = models.ForeignKey(
        # member posts engagement
        "SocialAccount",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    platform_url = models.URLField(max_length=200, null=True, blank=True)

    contact = models.ForeignKey(
        Contact, on_delete=models.CASCADE, related_name="links", null=True, blank=True
    )

    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id)


class ExploreNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    # channel posts / system posts
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    multiple_select = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)


class ExploreValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ExploreNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    social_account = models.ForeignKey(
        "SocialAccount",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="socialaccountscustomfield",
    )
    contact = models.ForeignKey(
        Contact,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="custom_field_value",
    )
    value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


CONTACT_NUMBER_FORMAT = [
    ("number", "Number"),
    ("%", "%"),
    # Convert 3 tuples from currency_model to 2 tuples (Follow django choice rule))
    *[(currency[0].lower(), currency[0].upper()) for currency in CURRENCY_MODEL],
]


class ContactsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=CONTACT_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=CONTACT_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class ContactsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        ContactsNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    contact = models.ForeignKey(
        Contact,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="contact_custom_field_relations",
    )
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="customerlink-contacts-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)

    value = models.TextField(null=True, blank=True)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = [["field_name", "contact"]]

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class ContactsValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Contacts Custom Field file",
        upload_to="contacts-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        ContactsValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


COMPANY_NUMBER_FORMAT = [
    ("number", "Number"),
    ("%", "%"),
    # Convert 3 tuples from currency_model to 2 tuples (Follow django choice rule))
    *[(currency[0].lower(), currency[0].upper()) for currency in CURRENCY_MODEL],
]


class CompanyNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=COMPANY_CF, max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=COMPANY_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class CompanyValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        CompanyNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="company_custom_field_relations",
    )
    value = models.TextField(null=True, blank=True)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    property_parent = models.ManyToManyField(
        "company", blank=True, symmetrical=False, related_name="property_child_item"
    )

    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="customerlink-company-customfield-files",
        null=True,
        blank=True,
    )

    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = [["field_name", "company"]]

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)
            self._log_data = log_data

        if self.field_name.type in ["number", "formula", "date", "date_time"]:
            validate_value_custom_field(self, args, kwargs)

        elif self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            super().save(*args, **kwargs)


class CompanyValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Company Custom Field file", upload_to="company-custom-field-files"
    )
    valuecustomfield = models.ForeignKey(
        CompanyValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


ACCOUNT_SEARCH_CONDITIONS_TYPE = [
    # market
    ("outbond_mentions", "Outbond Mentions"),
    ("profile_bio", "Profile Bio"),
    ("custom_field", "Custom Field"),
]

# CustomerLink

CONTACT_LIST_STATUS = [
    ("initializing", "Initializing"),
    ("refreshing", "Refreshing"),
    ("updating", "Updating"),
    ("updated", "Updated"),
    ("error", "Error"),
    ("unused", "Unused"),
    ("flow", "Flows"),
]


class ContactList(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # channel posts / system posts
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    status = models.CharField(
        choices=CONTACT_LIST_STATUS, max_length=20, null=True, blank=True
    )

    contacts = models.ManyToManyField(Contact, blank=True, related_name="contactlist")
    social_account = models.ManyToManyField("SocialAccount", blank=True)
    name = models.CharField(max_length=256, null=True, blank=True)
    platforms = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True
    )
    keyword = models.CharField(max_length=500, null=True, blank=True)
    language = models.CharField(max_length=10, null=True, blank=True)
    followers_min = models.IntegerField(null=True, blank=True)
    followers_max = models.IntegerField(null=True, blank=True)

    # condition is used for Account Search option. If empty the contact list belongs to Incfluencers Database option.
    condition = models.CharField(
        choices=ACCOUNT_SEARCH_CONDITIONS_TYPE, max_length=100, null=True, blank=True
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    num_mentions = models.IntegerField(default=0, blank=True)

    num_contacts = models.IntegerField(default=0, blank=True)

    contact_name_customfield = models.ForeignKey(
        "ContactsNameCustomField", on_delete=models.CASCADE, null=True, blank=True
    )

    updated_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


# Dictionary to store data shared between signals
signal_data = None


class CompanyList(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # channel posts / system posts
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    status = models.CharField(
        choices=CONTACT_LIST_STATUS, max_length=20, null=True, blank=True
    )

    companies = models.ManyToManyField(Company, blank=True, related_name="companylist")

    social_account = models.ManyToManyField("SocialAccount", blank=True)

    name = models.CharField(max_length=256, null=True, blank=True)
    platforms = models.CharField(
        choices=PLATFORM, max_length=100, null=True, blank=True
    )
    keyword = models.CharField(max_length=500, null=True, blank=True)
    language = models.CharField(max_length=10, null=True, blank=True)
    followers_min = models.IntegerField(null=True, blank=True)
    followers_max = models.IntegerField(null=True, blank=True)

    # condition is used for Account Search option. If empty the contact list belongs to Incfluencers Database option.
    condition = models.CharField(
        choices=ACCOUNT_SEARCH_CONDITIONS_TYPE, max_length=100, null=True, blank=True
    )
    channel = models.ForeignKey(
        "Channel", on_delete=models.CASCADE, null=True, blank=True
    )
    num_mentions = models.IntegerField(default=0, blank=True)

    num_contacts = models.IntegerField(default=0, blank=True)

    updated_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


# Workers
class Worker(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="user_worker",
    )
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=256, null=True, blank=True)
    manager = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="manager_workers",
    )
    status = models.CharField(
        max_length=20, choices=WORKER_STATUS, null=True, blank=True
    )
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    team = models.CharField(max_length=256, null=True, blank=True)
    location = models.CharField(max_length=256, null=True, blank=True)
    date_from = models.DateField(null=True, blank=True)
    date_to = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    email = models.CharField(max_length=256, null=True, blank=True)
    id_worker = models.IntegerField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if not self.id_worker:
            # Generate a new order_id if it doesn't exist
            last_ = (
                Worker.objects.filter(workspace=self.workspace, id_worker__isnull=False)
                .order_by("id_worker")
                .last()
            )
            if last_:
                if last_.id_worker:
                    try:
                        last_ = int(last_.id_worker)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.id_worker = f"{next_id:04d}"

        super().save(*args, **kwargs)


class WorkerFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, null=True, blank=True)
    file = models.FileField(
        verbose_name="Worker File",
        upload_to="worker-files",
        storage=PrivateMediaStorage(),
    )
    created_at = models.DateTimeField(default=timezone.now)


class WorkerNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    order = models.IntegerField(null=True, default=0, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class WorkerValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        WorkerNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    worker = models.ForeignKey(
        Worker,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="worker_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="worker-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class WorkerValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Worker Custom Field file", upload_to="worker-custom-field-files"
    )
    valuecustomfield = models.ForeignKey(
        WorkerValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class WorkerReviews(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    score = models.CharField(max_length=256, null=True, blank=True)
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    reviewer = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="reviewed_by",
    )
    whats_going_well = models.TextField(null=True, blank=True)
    areas_of_improvements = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    id_worker_review = models.IntegerField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if not self.id_worker_review:
            # Generate a new order_id if it doesn't exist
            last_ = (
                WorkerReviews.objects.filter(
                    workspace=self.workspace, id_worker_review__isnull=False
                )
                .order_by("id_worker_review")
                .last()
            )
            if last_:
                if last_.id_worker_review:
                    try:
                        last_ = int(last_.id_worker_review)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.id_worker_review = f"{next_id:04d}"

        super().save(*args, **kwargs)


class WorkerReviewsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    order = models.IntegerField(null=True, default=0, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class WorkerReviewsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        WorkerReviewsNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    worker_review = models.ForeignKey(
        WorkerReviews,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="worker_review_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="worker-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class WorkerReviewsValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Worker Review Custom Field file",
        upload_to="worker-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        WorkerReviewsValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class Absence(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    absence_type = models.CharField(
        choices=ABSENCE_TYPE, max_length=256, null=True, blank=True
    )
    status = models.CharField(
        choices=ABSENCE_STATUS, max_length=256, null=True, blank=True
    )
    usage_status = models.CharField(
        max_length=20, choices=USAGE_STATUS, null=True, blank=True, default="active"
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="absence_approval_requests",
    )
    requested_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="absences"
    )
    requester_name = models.CharField(max_length=256, null=True, blank=True)
    note = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    id_absence = models.IntegerField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop("log_data", None)  # Log
            self._log_data = log_data

        if not self.id_absence:
            # Generate a new order_id if it doesn't exist
            last_ = (
                Absence.objects.filter(
                    workspace=self.workspace, id_absence__isnull=False
                )
                .order_by("id_absence")
                .last()
            )
            if last_:
                if last_.id_absence:
                    try:
                        last_ = int(last_.id_absence)
                    except:
                        last_ = 0

                    next_id = last_ + 1
                else:
                    next_id = 1
            else:
                # If it's the first id, start with 1
                next_id = 1

            # Format the id as a four-digit string
            self.id_absence = f"{next_id:04d}"

        super().save(*args, **kwargs)


class AbsenceNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(
        choices=OBJECTS_CF_TYPE, max_length=20, null=True, blank=True
    )
    number_format = models.CharField(
        choices=SHOPTURBO_NUMBER_FORMAT, max_length=20, null=True, blank=True
    )
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    unique = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    order = models.IntegerField(null=True, default=0, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class AbsenceValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        AbsenceNameCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    absence = models.ForeignKey(
        Absence,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="absence_custom_field_relations",
    )
    value = models.CharField(max_length=500, null=True, blank=True)
    file = models.FileField(
        verbose_name="Custom Field file",
        upload_to="worker-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)
    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.field_name and (self.field_name.type == "tag"):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class AbsenceValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Absence Worker Custom Field file",
        upload_to="worker-custom-field-files",
    )
    valuecustomfield = models.ForeignKey(
        AbsenceValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


TRACK_TYPE = [("running", "Running"), ("stop", "Stop")]


class Track(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    track_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    worker = models.ForeignKey(
        "Worker", on_delete=models.CASCADE, null=True, blank=True
    )
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    duration = models.CharField(max_length=200, null=True, blank=True)
    status = models.CharField(choices=TRACK_TYPE, max_length=30, null=True, blank=True)
    usage_status = models.TextField(choices=USAGE_STATUS, null=True, blank=True)
    is_manual_entry = models.BooleanField(default=False)
    hours = models.IntegerField(null=True, blank=True)
    minutes = models.IntegerField(null=True, blank=True)
    seconds = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if not self.track_id:
            # Generate a new order_id if it doesn't exist
            last_track = (
                Track.objects.filter(workspace=self.workspace)
                .order_by("-track_id")
                .first()
            )
            if last_track:
                if last_track.track_id:
                    try:
                        last_track_id = int(last_track.track_id)
                    except:
                        last_track_id = 0

                    next_track_id = last_track_id + 1
                else:
                    next_track_id = 1
            else:
                # If it's the first order, start with 1
                next_track_id = 1

            # Format the order_id as a three-digit string
            self.track_id = f"{next_track_id:03d}"

        super().save(*args, **kwargs)


TRACK_CF_TYPE = [
    ("text", "Text"),
    ("text-area", "Text Area"),
    ("number", "Number"),
    ("date", "Date"),
    ("choice", "Choice"),
    ("tag", "Tag"),
    ("case", "Case Object"),
    ("file", "File"),
]

ACTIVITY_TRACK_TYPE = [("break", "Break"), ("work", "Work")]


class TrackNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=500, null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    type = models.CharField(choices=TRACK_CF_TYPE, max_length=20, null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)


class TrackValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    track = models.ForeignKey(
        Track, on_delete=models.CASCADE, related_name="track_custom_field_value"
    )
    field_name = models.ForeignKey(TrackNameCustomField, on_delete=models.CASCADE)
    value = models.TextField(null=True, blank=True)
    file = models.FileField(
        verbose_name="Track Custom Field file",
        upload_to="track-customfield-files",
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(default=timezone.now)


class TrackValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(
        verbose_name="Track Custom Field file", upload_to="track-customfield-files"
    )
    valuecustomfield = models.ForeignKey(
        TrackValueCustomField, on_delete=models.CASCADE, null=True, blank=True
    )
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default="image"
    )
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)


class SubTrack(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey("Workspace", on_delete=models.CASCADE)
    name = models.CharField(max_length=200, null=True, blank=True)
    track = models.ForeignKey(Track, on_delete=models.CASCADE)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    duration = models.CharField(max_length=200, null=True, blank=True)
    status = models.CharField(choices=TRACK_TYPE, max_length=30, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    activity_track_type = models.TextField(
        choices=ACTIVITY_TRACK_TYPE, null=True, blank=True
    )
