{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% get_current_language as LANGUAGE_CODE %}

<form method="POST" id="{{object_type}}-form" action="{% host_url 'toggle_commerce_meter_status' host 'app' %}">
    {% csrf_token %}

    <div class="pt-0 table-responsive" style="max-height: 75vh;">
        {% if not config_view or config_view == 'list' %}
            <table class="{% include "data/utility/table.html" %}" id='commerce_meter-table'>
                <thead class="{% include "data/utility/table-header.html" %} position-sticky">
                    <tr class="align-middle">

                        {% for column, label in column_labels.items %}
                            {% if column == 'checkbox' %}
                                <th class="min-w-40px"></th>
                            {% else %}
                                <th {% if column == 'meter_id' %}class="{% include "data/utility/column-id-size.html" %}" {% endif %}>
                                    {% if column|is_uuid %}
                                        {{label}}
                                    {% else %}
                                        {{label|get_attr:LANGUAGE_CODE}}
                                    {% endif %}
                                </th>
                            {% endif %}

                            {% if column == 'meter_id' %}
                            <th class="" style="width: 20px;">
                            </th>
                            {% endif %}
                        {% endfor %}

                    </tr>
                </thead>
                <tbody class="fs-6">

                    {% for commerce_meter in commerce_meters %}
                        <tr hx-get="{% host_url 'commerce_meter_table_row' commerce_meter.id host 'app' %}"
                            hx-vals='{"module":"{{module}}"{% if view_id %},"view_id": "{{view_id}}"{% endif %} {% if page %},"page": "{{page}}"{% endif %} }'
                            hx-target="this"
                            hx-swap="innerHTML"
                            hx-trigger="load"
                            hx-indicator=".row_load-{{commerce_meter.id}}">
                            <td class="d-flex justify-content-center w-100">
                                <style>
                                    /* Styles for the spinner */
                                    .row_load-{{commerce_meter.id}} {
                                        display: none; /* Initially hidden */
                                    }
                                    .htmx-request .row_load-{{commerce_meter.id}},
                                    .htmx-request.row_load-{{commerce_meter.id}} {
                                        display: inline-block; /* Display during htmx request */
                                    }
                                </style>
                                <!-- Spinner icon -->
                                <span class="spinner-border spinner-border-lg text-secondary row_load-{{commerce_meter.id}}" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </span>
                            </td>
                        </tr>
                    {% endfor %}

                </tbody>
            </table>
        {% endif %}

        <input id="flag_all" type="checkbox" name='flag_all' class="flag_all" hidden>
        <input name='view_id' value="{{view_id}}" hidden>
        <input name='q' {% if search_q %}value="{{search_q}}"{% endif %} hidden>

        <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="pb-0 border-0 modal-header justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="pb-0 modal-body">
                        <div class="text-center mb-13">
                            <h3 class="modal-title">

                                {% if LANGUAGE_CODE == 'ja'%}
                                一括アーカイブの確認
                                {% else %}
                                Bulk Archive Confirmation
                                {% endif %}

                            </h3>
                        </div>
                        <div class="border-bottom">
                            <div class="{% include "data/utility/form-div.html" %}">
                                <label class="mb-2 d-flex align-items-center fs-6 fw-bold">
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        選択されたレコードをアーカイブしてもよろしいですか?
                                        {% else %}
                                        Are you sure to archive selected records?
                                        {% endif %}
                                    </span>
                                </label>

                            </div>
                        </div>
                    </div>


                    <div class="border-0 modal-footer">
                        <button name="status" value="archive" type="submit" class="btn btn-danger">

                            {% if LANGUAGE_CODE == 'ja'%}
                                アーカイブ
                                {% else %}
                                Archive
                                {% endif %}
                            </button>

                        </button>
                        <a data-bs-dismiss="modal" class="bg-gray-200 border btn">
                            {% if LANGUAGE_CODE == 'ja'%}
                            キャンセル
                            {% else %}
                            Cancel
                            {% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="pb-0 border-0 modal-header justify-content-end">
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        </div>
                    </div>
                    <div class="pb-0 modal-body">
                        <div class="text-center mb-13">
                            <h3 class="modal-title">

                                {% if LANGUAGE_CODE == 'ja'%}
                                一括有効化の確認
                                {% else %}
                                Bulk Activate Confirmation
                                {% endif %}

                            </h3>
                        </div>
                        <div class="border-bottom">
                            <div class="{% include "data/utility/form-div.html" %}">
                                <label class="mb-2 d-flex align-items-center fs-6 fw-bold">
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        これらの在庫を有効化してもよろしいですか?
                                        {% else %}
                                        Are you sure to activate these inventory?
                                        {% endif %}
                                    </span>
                                </label>

                            </div>
                        </div>
                    </div>


                    <div class="border-0 modal-footer">
                        <button name="status" value="active" type="submit" class="btn btn-success">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                        <a data-bs-dismiss="modal" class="bg-gray-200 border btn">
                            {% if LANGUAGE_CODE == 'ja'%}
                            キャンセル
                            {% else %}
                            Cancel
                            {% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

    </div>
</form>

<div class="{% include "data/utility/pagination.html" %}">
    {% if commerce_meters|length > 0 %}
        {% if LANGUAGE_CODE == 'ja'%}
        {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
        {% else %}
        Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
        {% endif %}
        <div>
            {% if page_content.has_previous %}
                <a class="bg-gray-100 btn w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">{% translate_lang "First" LANGUAGE_CODE %}</a>
                <a class="bg-gray-100 btn w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">{% translate_lang "Previous" LANGUAGE_CODE %}</a>
            {% endif %}

            {% if page_content.has_next %}
                <a class="bg-gray-100 btn w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% translate_lang "Next" LANGUAGE_CODE %}</a>
                <a class="bg-gray-100 btn w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% translate_lang "Last" LANGUAGE_CODE %}</a>
            {% endif %}
        </div>
    {% endif %}
</div>


{% if object_id %}
    <a id="selected_show_items" class="d-none text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage-full-wizard-button"
        hx-get="{% host_url 'manage_commerce_meter_record' object_id host 'app' %}"
        hx-vals='{"module":"{{menu_key}}" {% if view_id %},"view_id": "{{view_id}}"{% endif %} {% if page %},"page": "{{page}}"{% endif %}}'
        hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
        hx-target="#manage-full-drawer-content"
        hx-trigger="click"
    ></a>
    <script>
        $(document).ready(function() {
            setTimeout(function() {
                document.getElementById('selected_show_items').click();
            }, 500);
        });
    </script>
{% endif %}


<script>

    function ensureDataTable(callback) {
        if (typeof $.fn.DataTable !== 'undefined') {
            callback();
        } else {
            // If DataTables isn't loaded yet, wait and try again
            setTimeout(function() {
                ensureDataTable(callback);
            }, 100);
        }
    }

    $(document).ready(function() {
        ensureDataTable(function() {
            $('#commerce_meter-table').DataTable({
                responsive: true,
                scrollX: true,
                scrollY: "75vh",
                scrollCollapse: true,
                fixedColumns: {
                    left: 2
                },
                ordering: false,      // This disables sorting
                searching: false,     // Hide the search bar
                paging: false,        // Hide pagination
                info: false,          // Hide the information text
                language: {
                    emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                },
                columnDefs: [
                    {
                        targets: '_all',
                        className: 'text-nowrap',
                        orderable: false,  // Explicitly disable ordering for all columns
                        sortable: false    // Additional flag to prevent sorting
                    }
                ],
                // Prevent DataTables from adding sorting classes
                initComplete: function() {
                    $('#commerce_meter-table th').removeClass('sorting sorting_asc sorting_desc');
                    $('#commerce_meter-table th').removeAttr('tabindex aria-controls aria-label aria-sort');
                    // Additional cleanup
                    $('#commerce_meter-table th').css('padding-right', '0');
                    $('#commerce_meter-table th').css('background-image', 'none');

                    // Handle dropdown z-index for table cells - similar to contacts template
                    var table = $('#commerce_meter-table').DataTable();
                    if (table) {
                        try {
                            var dropdownNodes = table.cells(null, 1).nodes();
                            if (dropdownNodes && dropdownNodes.length > 0) {
                                const dropdowns = $('.dropdown-toggle', dropdownNodes);
                                if (dropdowns && dropdowns.length > 0) {
                                    dropdowns.each(function(index, dropdownToggleEl) {
                                        if (dropdownToggleEl && typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                                            try {
                                                var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                                                    popperConfig: function(defaultBsPopperConfig) {
                                                        return { ...defaultBsPopperConfig, strategy: "fixed" };
                                                    },
                                                });

                                                if (dropdownToggleEl.addEventListener) {
                                                    dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                                                        $(event.target).closest("td").addClass("z-index-3");
                                                    });

                                                    dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                                                        $(event.target).closest("td").removeClass("z-index-3");
                                                    });
                                                }
                                            } catch (dropdownError) {
                                                console.log("Error setting up dropdown:", dropdownError);
                                            }
                                        }
                                    });
                                }
                            }
                        } catch (tableError) {
                            console.log("Error setting up table dropdowns:", tableError);
                        }
                    }
                }
            });
        });
    });


    function select_all() {
        $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
        if ($('#selectAll').prop('checked') == true) {
            //downloadelem = document.getElementById("csv_download");
            //downloadelem.classList.add("disabled");

            $('input[type=checkbox]').prop('checked', false);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

        } else {
            //downloadelem = document.getElementById("csv_download");
            //downloadelem.classList.remove("disabled");

            $('input[type=checkbox]').prop('checked', true);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.remove("d-none")

        }
    }


    var contactChecked = {}
    var first_id_check_box = null;
    const checking_checkbox = (elem,event) => {
        if (elem.checked){
            contactChecked[elem.id] = elem.id;

            if (event.shiftKey) {

                if (first_id_check_box){

                    var idList=[]
                    var all_checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    all_checkboxes.forEach(function(checkbox) {
                        if (checkbox.id != "selectAll") {
                            idList.push(checkbox.id);
                        }
                    });


                    // Find the index of id1 and id2 in the idList
                    var index_first_id = idList.indexOf(first_id_check_box);
                    var index_shift_pressed_id = idList.indexOf(elem.id);
                    // If either id1 or id2 is not found in the idList, or they are next to each other, return null
                    if (index_first_id === -1 || index_shift_pressed_id === -1 || Math.abs(index_first_id - index_shift_pressed_id) === 1) {

                    } else {
                        // Determine the smaller and larger index
                        var minIndex = Math.min(index_first_id, index_shift_pressed_id);
                        var maxIndex = Math.max(index_first_id, index_shift_pressed_id);
                        // Extract the IDs between the indices
                        var idsBetween = idList.slice(minIndex + 1, maxIndex);

                        if (idsBetween){

                            idsBetween.forEach(function(id) {
                                var checkbox = document.getElementById(id);
                                if (checkbox) {
                                    checkbox.checked = true;
                                }
                            });

                        }

                    }

                }

            }
            else{
                var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');

                first_id_check_box = elem.id
            }
        }
        else{
            delete contactChecked[elem.id];
            if (Object.keys(contactChecked).length == 0){

                var checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            }
        }
        countCheckedItems()
    }

    {% include "data/common/open-drawer.js" %}
</script>

{% include 'data/javascript/toggleSearch.html' %}