from django.db.models import QuerySet
from data.models import Estimate, ShopTurboDecimalPoint
from data.models.workspace import Workspace
import math
from typing import List
from django.db import transaction


def estimate_total_fix(estimate: Estimate, workspace: Workspace):
    """
    Fix individual estimate total amounts based on decimal point configuration.
    This is to fix the amount issue when Decimal Point settings are updated.
    
    Args:
        estimate: Estimate instance to fix
        workspace: Workspace instance
        
    Returns:
        Updated estimate instance
    """
    # Get general decimal point setting
    general_decimal_point = ShopTurboDecimalPoint.objects.filter(
        workspace=workspace, app_name="general"
    ).first()
    
    if not general_decimal_point:
        return estimate
    
    # Get estimate line items
    line_items = estimate.estimate_object.all()
    
    total_price_without_tax = 0.0
    total_price_with_tax = 0.0
    line_item_totals_without_tax = []
    line_item_totals_with_tax = []
    
    # Process each line item
    for line_item in line_items:
        if not line_item.amount_price or not line_item.amount_item:
            continue
            
        unit_price = float(line_item.amount_price)
        quantity = float(line_item.amount_item)
        tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
        
        # Calculate line item totals based on tax configuration
        if estimate.tax_option == "item_based_tax":
            # Use item-specific tax rates
            if estimate.tax_inclusive:
                # Tax-inclusive: derive tax-exclusive from inclusive price
                line_item_total_with_tax = unit_price * quantity
                line_item_total_without_tax = line_item_total_with_tax / (1 + tax_rate / 100.0)
                
                # Apply JPY integer rounding for tax-exclusive amounts
                if estimate.currency and estimate.currency.lower() == "jpy":
                    line_item_total_without_tax = int(line_item_total_without_tax)
            else:
                # Tax-exclusive: calculate inclusive from exclusive price
                line_item_total_without_tax = unit_price * quantity
                line_item_total_with_tax = line_item_total_without_tax * (1 + tax_rate / 100.0)
        else:
            # Use unified tax rate from estimate
            unified_tax_rate = float(estimate.tax_rate) if estimate.tax_rate else 0.0
            
            if estimate.tax_inclusive:
                # Tax-inclusive
                line_item_total_with_tax = unit_price * quantity
                line_item_total_without_tax = line_item_total_with_tax / (1 + unified_tax_rate / 100.0)
                
                # Apply JPY integer rounding for tax-exclusive amounts
                if estimate.currency and estimate.currency.lower() == "jpy":
                    line_item_total_without_tax = int(line_item_total_without_tax)
            else:
                # Tax-exclusive
                line_item_total_without_tax = unit_price * quantity
                line_item_total_with_tax = line_item_total_without_tax * (1 + unified_tax_rate / 100.0)
        
        # Apply line-item specific decimal point formatting if configured
        if general_decimal_point.type in ['line_item_cut_off', 'line_item_cut_over']:
            math_func = math.floor if general_decimal_point.type == 'line_item_cut_off' else math.ceil
            line_item_total_without_tax = math_func(line_item_total_without_tax)
            line_item_total_with_tax = math_func(line_item_total_with_tax)
        
        line_item_totals_without_tax.append(line_item_total_without_tax)
        line_item_totals_with_tax.append(line_item_total_with_tax)
        
        total_price_without_tax += line_item_total_without_tax
        total_price_with_tax += line_item_total_with_tax
    
    # Handle discount if present
    if estimate.discount and float(estimate.discount) > 0:
        discount_amount = float(estimate.discount)
        
        if estimate.discount_option == "percentage":
            # Percentage discount
            if estimate.discount_tax_option == "pre_tax":
                # Pre-tax discount
                discount_amount = total_price_without_tax * (discount_amount / 100.0)
                total_price_without_tax -= discount_amount
                
                if estimate.tax_option == "item_based_tax":
                    # For item-based tax, recalculate tax on discounted amount
                    total_tax = 0
                    for line_item, orig_without_tax in zip(line_items, line_item_totals_without_tax):
                        item_tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
                        # Apply proportional discount to each line item
                        discounted_line_total = orig_without_tax * (1 - float(estimate.discount) / 100.0)
                        if estimate.currency and estimate.currency.lower() == "jpy":
                            discounted_line_total = int(discounted_line_total)
                        item_tax = discounted_line_total * (item_tax_rate / 100.0)
                        item_tax = round(item_tax)
                        total_tax += item_tax
                    total_price_with_tax = total_price_without_tax + total_tax
                else:
                    # Recalculate tax on discounted amount
                    if estimate.tax_option != "item_based_tax":
                        unified_tax_rate = float(estimate.tax_rate) if estimate.tax_rate else 0.0
                        total_price_with_tax = total_price_without_tax * (1 + unified_tax_rate / 100.0)
            else:
                # Post-tax discount
                    discount_amount = total_price_with_tax * (discount_amount / 100.0)
                    
                    if estimate.tax_option == "item_based_tax":
                        total_price_without_tax -= discount_amount
                        # Calculate total tax from original prices
                        total_tax = 0
                        for line_item, orig_without_tax in zip(line_items, line_item_totals_without_tax):
                            item_tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
                            if estimate.currency and estimate.currency.lower() == "jpy":
                                orig_without_tax = int(float(orig_without_tax))
                            item_tax = float(orig_without_tax) * (item_tax_rate / 100.0)
                            item_tax = round(item_tax)
                            total_tax += item_tax
                        total_price_with_tax = total_price_without_tax + total_tax
                    else:
                        total_price_with_tax -= discount_amount
        else:
            # Fixed amount discount
            if estimate.discount_tax_option == "pre_tax":
                total_price_without_tax -= discount_amount
                if estimate.tax_option != "item_based_tax":
                    unified_tax_rate = float(estimate.tax_rate) if estimate.tax_rate else 0.0
                    total_price_with_tax = total_price_without_tax * (1 + unified_tax_rate / 100.0)
            else:
                # Post-tax discount
                if estimate.tax_option == "item_based_tax":
                    total_price_without_tax -= discount_amount
                else:
                    total_price_with_tax -= discount_amount
    
    # Apply final decimal point formatting
    if general_decimal_point.type == 'cut_off':
        if estimate.currency and estimate.currency.lower() == "usd":
            total_price_without_tax = math.floor(total_price_without_tax * 100) / 100
            total_price_with_tax = math.floor(total_price_with_tax * 100) / 100
        else:
            total_price_without_tax = math.floor(total_price_without_tax)
            total_price_with_tax = math.floor(total_price_with_tax)
    elif general_decimal_point.type == 'cut_over':
        if estimate.currency and estimate.currency.lower() == "usd":
            total_price_without_tax = math.ceil(total_price_without_tax * 100) / 100
            total_price_with_tax = math.ceil(total_price_with_tax * 100) / 100
        else:
            total_price_without_tax = math.ceil(total_price_without_tax)
            total_price_with_tax = math.ceil(total_price_with_tax)
        
    # Update estimate totals if changed
    needs_update = False
    if estimate.total_price_without_tax != total_price_without_tax:
        estimate.total_price_without_tax = total_price_without_tax
        needs_update = True
    
    if estimate.total_price != total_price_with_tax:
        estimate.total_price = total_price_with_tax
        needs_update = True
    
    if needs_update:
        estimate.save(update_fields=['total_price_without_tax', 'total_price'])
    
    return estimate


async def estimates_total_fix_bulk_async(estimates: QuerySet[Estimate], workspace: Workspace):
    """
    Async version of estimates_total_fix_bulk for efficient processing of multiple estimates.
    Uses abulk_update to minimize database queries and native async operations.
    
    Args:
        estimates: QuerySet of Estimate instances to fix
        workspace: Workspace instance
        
    Returns:
        List of updated estimates
    """
    # Get general decimal point setting
    general_decimal_point = await ShopTurboDecimalPoint.objects.filter(
        workspace=workspace, app_name="general"
    ).afirst()
    
    if not general_decimal_point:
        return []
    
    estimates_to_update = []
    updated_estimates = []
    
    # Process estimates in batches to avoid memory issues
    async for estimate in estimates:
        # Get estimate line items
        line_items = [item async for item in estimate.estimate_object.all()]
        
        total_price_without_tax = 0.0
        total_price_with_tax = 0.0
        line_item_totals_without_tax = []
        line_item_totals_with_tax = []
        
        # Process each line item
        for line_item in line_items:
            if not line_item.amount_price or not line_item.amount_item:
                continue
                
            unit_price = float(line_item.amount_price)
            quantity = float(line_item.amount_item)
            tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
            
            # Calculate line item totals based on tax configuration
            if estimate.tax_option == "item_based_tax":
                # Use item-specific tax rates
                if estimate.tax_inclusive:
                    # Tax-inclusive: derive tax-exclusive from inclusive price
                    line_item_total_with_tax = unit_price * quantity
                    line_item_total_without_tax = line_item_total_with_tax / (1 + tax_rate / 100.0)
                    
                    # Apply JPY integer rounding for tax-exclusive amounts
                    if estimate.currency and estimate.currency.lower() == "jpy":
                        line_item_total_without_tax = int(line_item_total_without_tax)
                else:
                    # Tax-exclusive: calculate inclusive from exclusive price
                    line_item_total_without_tax = unit_price * quantity
                    line_item_total_with_tax = line_item_total_without_tax * (1 + tax_rate / 100.0)
            else:
                # Use unified tax rate from estimate
                unified_tax_rate = float(estimate.tax_rate) if estimate.tax_rate else 0.0
                
                if estimate.tax_inclusive:
                    # Tax-inclusive
                    line_item_total_with_tax = unit_price * quantity
                    line_item_total_without_tax = line_item_total_with_tax / (1 + unified_tax_rate / 100.0)
                    
                    # Apply JPY integer rounding for tax-exclusive amounts
                    if estimate.currency and estimate.currency.lower() == "jpy":
                        line_item_total_without_tax = int(line_item_total_without_tax)
                else:
                    # Tax-exclusive
                    line_item_total_without_tax = unit_price * quantity
                    line_item_total_with_tax = line_item_total_without_tax * (1 + unified_tax_rate / 100.0)
            
            # Apply line-item specific decimal point formatting if configured
            if general_decimal_point.type in ['line_item_cut_off', 'line_item_cut_over']:
                math_func = math.floor if general_decimal_point.type == 'line_item_cut_off' else math.ceil
                line_item_total_without_tax = math_func(line_item_total_without_tax)
                line_item_total_with_tax = math_func(line_item_total_with_tax)
            
            line_item_totals_without_tax.append(line_item_total_without_tax)
            line_item_totals_with_tax.append(line_item_total_with_tax)
            
            total_price_without_tax += line_item_total_without_tax
            total_price_with_tax += line_item_total_with_tax
        
        # Handle discount if present
        if estimate.discount and float(estimate.discount) > 0:
            discount_amount = float(estimate.discount)
            
            if estimate.discount_option == "percentage":
                # Percentage discount
                if estimate.discount_tax_option == "pre_tax":
                    # Pre-tax discount
                    discount_amount = total_price_without_tax * (discount_amount / 100.0)
                    total_price_without_tax -= discount_amount
                    
                    if estimate.tax_option == "item_based_tax":
                        # For item-based tax, recalculate tax on discounted amount
                        total_tax = 0
                        for line_item, orig_without_tax in zip(line_items, line_item_totals_without_tax):
                            item_tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
                            # Apply proportional discount to each line item
                            discounted_line_total = orig_without_tax * (1 - float(estimate.discount) / 100.0)
                            if estimate.currency and estimate.currency.lower() == "jpy":
                                discounted_line_total = int(discounted_line_total)
                            item_tax = discounted_line_total * (item_tax_rate / 100.0)
                            item_tax = round(item_tax)
                            total_tax += item_tax
                        total_price_with_tax = total_price_without_tax + total_tax
                    else:
                        # Recalculate tax on discounted amount
                        if estimate.tax_option != "item_based_tax":
                            unified_tax_rate = float(estimate.tax_rate) if estimate.tax_rate else 0.0
                            total_price_with_tax = total_price_without_tax * (1 + unified_tax_rate / 100.0)
                else:
                    # Post-tax discount
                    discount_amount = total_price_with_tax * (discount_amount / 100.0)
                    
                    if estimate.tax_option == "item_based_tax":
                        total_price_without_tax -= discount_amount
                        # Calculate total tax from original prices
                        total_tax = 0
                        for line_item, orig_without_tax in zip(line_items, line_item_totals_without_tax):
                            item_tax_rate = float(line_item.tax_rate) if line_item.tax_rate else 0.0
                            if estimate.currency and estimate.currency.lower() == "jpy":
                                orig_without_tax = int(float(orig_without_tax))
                            item_tax = float(orig_without_tax) * (item_tax_rate / 100.0)
                            item_tax = round(item_tax)
                            total_tax += item_tax
                        total_price_with_tax = total_price_without_tax + total_tax
                    else:
                        total_price_with_tax -= discount_amount
            else:
                # Fixed amount discount
                if estimate.discount_tax_option == "pre_tax":
                    total_price_without_tax -= discount_amount
                    if estimate.tax_option != "item_based_tax":
                        unified_tax_rate = float(estimate.tax_rate) if estimate.tax_rate else 0.0
                        total_price_with_tax = total_price_without_tax * (1 + unified_tax_rate / 100.0)
                else:
                    # Post-tax discount
                    if estimate.tax_option == "item_based_tax":
                        total_price_without_tax -= discount_amount
                    else:
                        total_price_with_tax -= discount_amount
        
        # Apply final decimal point formatting
        if general_decimal_point.type == 'cut_off':
            if estimate.currency and estimate.currency.lower() == "usd":
                total_price_without_tax = math.floor(total_price_without_tax * 100) / 100
                total_price_with_tax = math.floor(total_price_with_tax * 100) / 100
            else:
                total_price_without_tax = math.floor(total_price_without_tax)
                total_price_with_tax = math.floor(total_price_with_tax)
        elif general_decimal_point.type == 'cut_over':
            if estimate.currency and estimate.currency.lower() == "usd":
                total_price_without_tax = math.ceil(total_price_without_tax * 100) / 100
                total_price_with_tax = math.ceil(total_price_with_tax * 100) / 100
            else:
                total_price_without_tax = math.ceil(total_price_without_tax)
                total_price_with_tax = math.ceil(total_price_with_tax)
        
        # Check if updates are needed and prepare for bulk update
        needs_update = False
        if estimate.total_price_without_tax != total_price_without_tax:
            estimate.total_price_without_tax = total_price_without_tax
            needs_update = True
        
        if estimate.total_price != total_price_with_tax:
            estimate.total_price = total_price_with_tax
            needs_update = True
        
        if needs_update:
            estimates_to_update.append(estimate)
        
        updated_estimates.append(estimate)
        
    # Perform bulk update if there are estimates to update (async)
    if estimates_to_update:
        await Estimate.objects.abulk_update(
            estimates_to_update,
            ['total_price_without_tax', 'total_price'],
            batch_size=1000  # Process in batches to avoid memory issues
        )
    
    return updated_estimates