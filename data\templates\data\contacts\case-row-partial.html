{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_language as LANGUAGE_CODE %}


    {% if "checkbox" == row_type %}
    <td class="w-10px ">
        <input style="" id="deals-selection-{{deal.id}}" class="form-check-input cursor-pointer deals-selection check_input" type="checkbox" name="checkbox" value="{{deal.id}}" data-owner="{{deal.owner.user.id}}" onclick="checking_checkbox(this,event)"/>
    </td>

    {% elif "deal_id" == row_type and view != "kanban" %}
        <td class="fw-bold text-nowrap special-col min-w-50px" style="border-right: 1px solid !important; border-right-color: rgb(234, 234, 234) !important;">
            <div class="d-flex align-items-center">
                <span class="d-none manage_full_wizard_button deal_{{deal.id}}_activity"
                    hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                    hx-vals = '{"tab":activity", "drawer_type":"manage-deal", "deal_id":"{{deal.id}}" ,"view_id":"{{view_filter.view.id}}","module":"{{menu_key}}"}'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner" 
                    hx-trigger="click"
                    hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
                    >
                </span>
                <a class="text-nowrap text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage_full_wizard_button view_form_trigger{{deal.id}} deal_{{deal.id}}" 
                    hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                    hx-vals = '{"drawer_type":"manage-deal", "deal_id":"{{deal.id}}" ,"view_id":"{{view_filter.view.id}}","module":"{{menu_key}}"}'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner" 
                    hx-trigger="click"
                    hx-on::before-request="document.getElementById('manage-full-drawer-content').innerHTML = '';"
                >
                {{deal.deal_id|stringformat:"04d"}}
                </a>
                
                {% if property_sets %}
                <div class="dropdown">
                    <button type="button" class="btn btn-primary-outline text-dark text-hover-primary px-1 py-0 dropdown-toggle" data-bs-toggle="dropdown">
                        <span class="d-inline-flex align-items-center justify-content-center" style="width: 20px; height: 20px;">
                            <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                            </svg>
                        </span>
                    </button>
                    <ul class="dropdown-menu">
                        {% for set in property_sets %}
                        <li>
                            <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden manage_full_wizard_button" type="button"
                                hx-get="{% host_url 'new_customerlinkapp_drawer' host 'app' %}"
                                hx-vals = '{"drawer_type":"manage-deal", "deal_id":"{{deal.id}}" ,"view_id":"{{view_filter.view.id}}","set_id": "{{set.id}}"}'
                                hx-target="#manage-full-drawer-content"
                                style="border-radius: 0.475rem 0 0 0.475rem;"
                                >
                                {% if set.name %}
                                    {{ set.name}}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                                {% endif %}
                            </button>
                        </li>
                        {% endfor %}
                    </ul>
                </div>  
                {% endif %}

            </div>  
        </td>
        <td class="" style="width: 20px;">
        </td>

    {% elif "name" == row_type %}
        <td class="fw-bold">
                
                <a class="{% if config_view != 'kanban' %} text-nowrap text-center {% endif %} mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage_full_wizard_button deal_name_{{deal.id}}" 
                    hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                    hx-vals = '{"drawer_type":"manage-deal", "deal_id":"{{deal.id}}" ,"view_id":"{{view_id}}","module":"{{menu_key}}"}'
                    hx-target="#manage-full-drawer-content"
                    hx-indicator=".loading-drawer-spinner" 
                    hx-trigger="click"
                >
                {{deal.name}}
                </a>
        </td>

    {% elif "owner" == row_type %}
    <td class="fw-bold">
        {% if deal.owner and deal.owner.user %}
            {% with deal.owner.user as user %}
            {% if user.verification.profile_photo %}
            <img class="w-20px rounded-circle me-2" src="{{user.verification.profile_photo.url}}" style="object-fit: cover !important;aspect-ratio: 16 / 16;" />
            {% else %}
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.28548 15.0861C7.34369 13.1814 9.35142 12 11.5304 12H12.4696C14.6486 12 16.6563 13.1814 17.7145 15.0861L19.3493 18.0287C20.0899 19.3618 19.1259 21 17.601 21H6.39903C4.87406 21 3.91012 19.3618 4.65071 18.0287L6.28548 15.0861Z" fill="currentColor"/>
                    <rect opacity="0.3" x="8" y="3" width="8" height="8" rx="4" fill="currentColor"/>
                </svg>    
            {% endif %}
            <span >
                {{user.first_name}}
            </span>
            {% endwith %}
        {% endif %}
    </td>
    {% elif row_type == 'invoices' %}
        <td class="fw-bold">
                {% with invoices=deal.invoices.all %}
                    {% if invoices %}
                        {% for invoice in invoices %}
                            <div>
                                <a class="{% include 'data/utility/table-link.html' %} manage-related-wizard-button invoice_{{invoice.id}}" 
                                    hx-get="{% host_url 'invoice_edit' invoice.id host 'app' %}" 
                                    hx-target="#manage-related-drawer-content"
                                    hx-vals = 'js:{"object_type": "invoices"}'
                                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                                    hx-trigger="click"
                                >   
                                    {% get_object_display invoice 'invoices' %}
                                </a>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
        </td>

    {% elif row_type == 'estimates' %}
        <td class="fw-bold">
                {% with estimates=deal.estimates.all %}
                    {% if estimates %}
                        {% for estimate in estimates %}
                            <div>
                                <a class="{% include "data/utility/table-link.html" %} manage-related-wizard-button estimate_{{estimate.id}}" 
                                    hx-get="{% host_url 'estimate_edit' estimate.id host 'app' %}"
                                    hx-target="#manage-related-drawer-content"
                                    hx-vals = 'js:{"object_type": "estimates"}'
                                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                                    hx-trigger="click"
                                >   
                                    {% get_object_display estimate 'estimates' %}
                                </a>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
        </td>

    {% elif "customer" == row_type %}
    <td class="fw-bold">
            {% if deal.contact.all %}
                {% for contact in deal.contact.all %}
                <a class="{% include "data/utility/table-link.html" %} create-view-settings-button contact_{{contact.id}}" 
                    hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}" 
                    hx-target="#manage-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                    hx-trigger="click"
                >
                    #{{contact.contact_id|stringformat:"04d"}} - {{contact|display_contact_name:LANGUAGE_CODE}}
                </a>
                <br/>
                {% endfor %}
            {% endif %}

            {% if deal.company.all %}
                {% for company in deal.company.all %}
                    <a class="{% include "data/utility/table-link.html" %} create-view-settings-button company_{{company.id}}" 
                        hx-get="{% host_url 'load_explore_company' company.id host 'app' %}" 
                        hx-target="#manage-view-settings-drawer"
                        hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                        hx-trigger="click"
                    >
                        #{{company.company_id|stringformat:"04d"}} - {{company.name}}
                    </a>
                {% endfor %}
            {% endif %}
    </td>


    {% elif "status" == row_type %}
    <td class="fw-bold">
        {% translate_lang deal.get_status_display LANGUAGE_CODE %}
    </td>

    {% elif "case_status" == row_type %}
    <td class="fw-bold">
        {% if deal.case_status %}
            {% if 'case_status'|get_custom_property_object:deal %}
                {% with value_map_label='case_status'|get_custom_property_object:deal|get_attr:'value'|string_list_to_list %}
                    {% for value, label in value_map_label.items %}
                        {% if deal.case_status == value %}{{label}}{% endif %}
                    {% endfor %}
                {% endwith %}
            {% else %}
                {% with status=deal|get_default_status_display:row_type %}
                    {% for key, value in  status.items %}
                        {% if deal.case_status ==  key %} 
                            {% if LANGUAGE_CODE == 'ja' %}
                                {{value.ja}}
                            {% else %}
                                {{value.en}}
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                {% endwith %}
            {% endif %}
        {% endif %}

    </td>


    {% elif "created_at" == row_type %}
    <td class="fw-bold">
            {% if deal.created_at %}
                {% date_format deal.created_at 1 %}
            {% endif %}
    </td>


    {% elif "updated_at" == row_type %}
    <td class="fw-bold">
            {% if deal.updated_at %}
                {% date_format deal.updated_at 1 %}
            {% endif %}
    </td>
    

    {% elif "customer|" in row_type and "contact" in row_type %}
    <td class="fw-bold">
            {% with parts=row_type|split:'|' %}
                {% if deal.contact %}
                    {% for contact in deal.contact.all %}
                    <a class="text-dark text-hover-primary cursor-pointer customer-manage-wizard contact_{{contact.id}}"
                        hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}"
                        hx-target="#customer-manage-drawer"
                        hx-trigger="click"
                        > 
                            {% with contact_customfield_value=parts.2|get_value_custom_field_contacts:contact.id %}
                                {% if contact_customfield_value  %}
                                    {{contact_customfield_value}}
                                {% elif parts.2 == 'name' %}
                                    {{contact|display_contact_name:LANGUAGE_CODE}}
                                {% elif parts.2 == 'first_name' %}
                                    {{contact|get_attr:'name'}}
                                {% elif contact|get_attr:parts.2 %}
                                    {{contact|get_attr:parts.2}}
                                {% endif %}
                            {% endwith %}
                        </a>
                    {% endfor %}
                {% endif %}
            {% endwith %} 
    </td>

    {% elif "customer|" in row_type and "company" in row_type %}
    <td class="fw-bold">
            {% with parts=row_type|split:'|' %}
                {% if deal.company %}
                    {% for company in deal.company.all %}
                    <a  class="{% include "data/utility/table-link.html" %} customer-manage-wizard company_{{company.id}}" 
                        hx-get="{% host_url 'load_explore_company' company.id host 'app' %}"
                        hx-target="#customer-manage-drawer"
                        hx-trigger="click"
                    >
                        <div>
                        {% with company_customfield_value=parts.2|get_value_custom_field_company:company.id %}
                            {% if company_customfield_value  %}
                                {{company_customfield_value}}
                            {% elif parts.2 == 'name' %}
                                {{company.name}}
                            {% elif deal.contact|get_attr:parts.2 %}
                                {{contact|get_attr:parts.2}}
                            {% endif %}
                        {% endwith %}
                        </div>
                    </a>
                    {% endfor %}
                {% endif %}
            {% endwith %} 
    </td>

    {% elif "price_information" in row_type %}
    <td class="fw-bold">
            {% with parts=row_type|split:'|' %}
                {% with custom_field_value=parts.2|get_value_custom_field_deals_objects:deal.id %}
                    {% if custom_field_value %}
                    <div class="price-info">
                        {% with custom_field_currency=custom_field_value.value|string_list_to_list|first|get_attr:'currency'|first|get_currency_symbol %}
                            {% if custom_field_currency %}
                                {{ custom_field_currency }}
                            {% endif %}
                            {% if custom_field_currency and custom_field_currency|is_two_decimal_currencies %}
                                {{ custom_field_value.value|string_list_to_list|calculate_total_price|floatformat:"2g" }}
                            {% else %}
                                {{ custom_field_value.value|string_list_to_list|calculate_total_price|floatformat:"2" }}
                            {% endif %}
                        {% endwith %}
                    </div>
                    {% endif %}
                {% endwith %} 
            {% endwith %}        
    </td>

    {% elif "tasks" in row_type %}
    <td class="fw-bold">
            {% with tasks=deal.tasks.all %}
                {% if tasks %}
                    {% for task in tasks %}
                        <div class="mb-1">
                            <a class="{% include "data/utility/table-link.html" %} task-wizard-button tw-overflow-hidden tw-text-ellipsis"
                                hx-target=".task-drawer"
                                hx-get="{% host_url 'task_form' host 'app' %}"
                                hx-vals='{"task_id":"{{task.id}}", "view_id":"", "from":"{{from}}", "p_id": "{{task.project_target.id}}", "type": "update" }'
                                hx-swap="innerHTML"
                            >
                                {{task.project_target.title}} | #{{task.task_id}}- {{task.title|truncatechars:10}}
                            </a>
                        </div>
                        </br>
                    {% endfor %}
                {% endif %}
            {% endwith %}
    </td>

    {% else %}
    <td class="fw-bold">
            {% with CustomFieldName=row_type|search_custom_field_object_deals:request %}
                {% get_custom_field_value CustomFieldName.id deal.id "customer_case" as custom_field_value %}  
                {% if custom_field_value %}
                    {% if CustomFieldName.type == 'number'%}
                        {% if CustomFieldName.number_format == '%' %}
                            {{ CustomFieldName.id|get_value_custom_field_deals:deal.id }} {{CustomFieldName.number_format|upper}}
                        {% elif CustomFieldName.number_format == 'number' %}
                            {{ CustomFieldName.id|get_value_custom_field_deals:deal.id }} 
                        {% else %}
                         
                            {% if CustomFieldName.number_format == 'usd' %}
                                {{CustomFieldName.number_format|upper|get_currency_symbol}} {{custom_field_value}}
                            {% elif CustomFieldName.number_format|is_two_decimal_currencies %}
                                {{CustomFieldName.number_format|upper|get_currency_symbol}} {{custom_field_value|floatformat:"2g"|intcomma:False}}
                            {% else %}
                                {{CustomFieldName.number_format|upper|get_currency_symbol}}  {{custom_field_value|floatformat:"0g"|intcomma:False}}
                            {% endif %}
                        
                        {% endif %}
                    {% elif CustomFieldName.type == 'choice' %}
                        <div class="fw-bold">
                            {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                                {% for choice in custom_field_value.field_name.choice_value|string_list_to_list %}
                                    {% if ';' in custom_field_value.value and custom_field_value.field_name.multiple_select %}
                                        {% for val in custom_field_value.value|split:";" %}
                                            {% if choice.value == val %}
                                                {% if custom_field_value.field_name.show_badge %}
                                                <div class="fw-bold">
                                                    <span class="d-inline-flex align-items-center">
                                                        <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                                                        {{ choice.label }}
                                                    </span>
                                                </div>
                                                {% else %}
                                                <div class="fw-bold">
                                                    {{ choice.label }}
                                                </div>
                                                {% endif %}
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        {% if choice.value == custom_field_value.value %}
                                            <div class="fw-bold">
                                                {% if custom_field_value.field_name.show_badge %}
                                                <span class="d-inline-flex align-items-center">
                                                    <span class="me-2" style="background-color: {{ choice.color|default:'#000000' }}; width: 16px; height: 16px; border-radius: 50%; display: inline-block; border: 1px solid #ccc;"></span>
                                                    {{ choice.label }}
                                                </span>
                                                {% else %}
                                                {{ choice.label }}
                                                {% endif %}
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% endfor %}
                            {% endwith %} 
                        </div>
                    {% elif CustomFieldName.type == 'tag' %}
                        <div class="">
                            {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                                {% for tag in custom_field_value.value|get_json %}
                                <div class="fw-bold mb-1">
                                    <span class="badge bg-gray-500">{{tag.value}}</span>
                                </div>
                                {% endfor %} 
                            {% endwith %}
                        </div>
                    {% elif CustomFieldName.type == 'image' %}
                        <div class="ms-2">
                            <a target="_blank" href="{{custom_field_value.file.url}}">
                                <div class="symbol symbol-lg-100px symbol-100px">
                                    <img style="object-fit: cover;" alt="Pic" src="{{custom_field_value.file.url}}" />
                                </div>
                            </a>
                        </div>
                    {% elif CustomFieldName.type == 'contact' %}                            
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% with contact=custom_field_value.value|get_contact_obj %}
                            <div class="fw-bold">
                                <a class="{% include "data/utility/table-link.html" %} create-view-settings-button contact_{{contact.id}}" 
                                    hx-get="{% host_url 'load_explore_profile' contact.id host 'app' %}" 
                                    hx-target="#manage-view-settings-drawer"
                                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                                    hx-trigger="click"
                                >
                                    #{{contact.contact_id|stringformat:"04d"}}-{{contact|display_contact_name:LANGUAGE_CODE}}   
                                </a>
                            </div>
                            {% endwith %}  
                        {% endwith %}
                    {% elif CustomFieldName.type == 'company' %}                            
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% with company=custom_field_value.value|get_company_obj %}
                            <div class="fw-bold">
                                <a class="{% include "data/utility/table-link.html" %} create-view-settings-button company_{{company.id}}" 
                                    hx-get="{% host_url 'load_explore_company' company.id host 'app' %}" 
                                    hx-target="#manage-view-settings-drawer"
                                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                                    hx-trigger="click"
                                >
                                #{{company.company_id|stringformat:"04d"}}-{{company.name}}   
                                </a>
                            </div>
                            {% endwith %}  
                        {% endwith %}
                    {% elif CustomFieldName.type == 'subscription' %}                  
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% with subs_item=custom_field_value.value|get_subscriptions_obj %}
                            <div class="fw-bold">
                                <a class="{% include "data/utility/table-link.html" %} create-view-settings-button" 
                                    hx-get="{% url 'load_manage_subscriptions_drawer' %}" 
                                    hx-vals = '{"drawer_type":"subscriptions-manage", "subscription_id":"{{subs_item.id}}" }'
                                    hx-target="#manage-view-settings-drawer"
                                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                                    hx-trigger="click"
                                >
                                #{{subs_item.subscriptions_id|stringformat:"04d"}}   
                                </a>
                            </div>
                            {% endwith %}  
                        {% endwith %}
                    {% elif CustomFieldName.type == 'warehouse_objects' %}                  
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% with location=custom_field_value.value|get_warehouse_obj %}
                            <div class="fw-bold">
                                <a class="{% include "data/utility/table-link.html" %} create-view-settings-button" 
                                    
                                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                                    hx-vals = '{"drawer_type":"inventory-warehouse-manage", "warehouse_id":"{{location.id}}" }'
                                    hx-target="#manage-view-settings-drawer"
                                    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"  
                                    hx-trigger="click"
                                >
                                
                                #{{location.id_iw|as_int|stringformat:"04d"}}   
                                </a>
                            </div>
                            {% endwith %}  
                        {% endwith %}

                    {% elif CustomFieldName.type == 'invoice_objects' %}
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% with invoice=custom_field_value.value|get_invoice_obj %}
                                {% include "data/common/custom_field/invoice_object.html" with invoice=invoice drawer_id="create-view-settings-wizard" drawer_class="create-view-settings-wizard" htmx_target="#manage-contacts-view-settings-drawer" %}
                            {% endwith %}   
                        {% endwith %}
                    {% elif CustomFieldName.type == 'bill_objects' %}
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% with bill=custom_field_value.value|get_bill_obj %}
                                {% if bill.id %}
                                <div class="fw-bold">
                                    <a class="{% include "data/utility/table-link.html" %} create-view-settings-wizard bill_{{bill.id}}" 
                                        hx-get="{% host_url 'bill_manage' bill.id host 'app' %}" 
                                        hx-target="#manage-contacts-view-settings-drawer"
                                        hx-indicator=".loading-drawer-spinner"  
                                        hx-trigger="click"
                                    >
                                        #{{bill.id_bill|stringformat:"04d"}} - {{bill.title}}
                                    </a>
                                </div>
                                {% endif %}
                            {% endwith %}   
                        {% endwith %}
                    {% comment %} {% elif CustomFieldName.type == 'attendance' %}
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% for val in custom_field_value.value|get_list:";" %}
                                {% with attendance=val|get_attendance_track_obj %}
                                    {% if attendance %}
                                        <div class="fw-bold">
                                            {{ attendance.name }} | {{ attendance.duration }}
                                        </div>
                                    {% endif %}
                                {% endwith %}
                            {% endfor %}
                        {% endwith %} {% endcomment %}
                    {% elif CustomFieldName.type == 'date' %}
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% date_format custom_field_value.value_time %}
                        {% endwith %}
                    {% elif CustomFieldName.type == 'date_time' %}
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            {% date_format custom_field_value.value_time 1 %}
                        {% endwith %}
                    
                    {% elif CustomFieldName.type == 'user' %}
                        <div class="fw-bold">
                            {% with user_id=CustomFieldName.id|get_value_custom_field_deals:deal.id %}
                                {% with user=user_id|convert_user_id_to_user %}
                                    {% if user %}
                                        {{user.first_name}}
                                    {% endif %}
                                {% endwith %}
                            {% endwith %}
                        </div>
                    {% elif CustomFieldName.type == 'price-information' %}
                        {% with custom_field_value=CustomFieldName.id|get_value_custom_field_deals_objects:deal.id %}
                            
                            {% for value in custom_field_value.value|string_list_to_list %}
                            <div class="price-info">
                                {% if value.item|is_uuid %}
                                    {% with item_obj=value.item|get_object_from_basemodel_id:"commerce_items" %}
                                        #{{item_obj.item_id|stringformat:"04d"}} {{item_obj.name}} - {{value.currency.0|get_currency_symbol}} {% if value.currency.0|is_two_decimal_currencies %}{{item_obj.price|floatformat:"2g"|intcomma:False}}{% else %}{{item_obj.price}}{% endif %} - {{value.number_of_items}}
                                    {% endwith %}
                                {% else %}
                                    {{value.item}} - {{value.currency.0|get_currency_symbol}} {% if value.currency.0|is_two_decimal_currencies %}{{value.item_price|floatformat:"2g"|intcomma:False}}{% else %}{{value.item_price}}{% endif %} - {{value.number_of_items}}
                                {% endif %}
                            </div>
                            {% endfor %}
                            
                        {% endwith %}
                    {% else %}
                        {{ CustomFieldName.id|get_value_custom_field_deals:deal.id }}
                    {% endif %}
                {% else %}
                    {% if CustomFieldName.type == 'formula' %}
                        <div class="d-none" 
                            hx-post="{% url 'get_formula_result' %}" 
                            hx-vals='{"obj_id":"{{deal.id}}","custom_field_id":"{{CustomFieldName.id}}","object_type":"{{constant.TYPE_OBJECT_CASE}}"}'
                            hx-target="#cp-formula-value-{{deal.id}}-{{CustomFieldName.id}}"
                            hx-trigger="load"
                            hx-swap="innerHTML"
                        ></div>
                        <div id="cp-formula-value-{{deal.id}}-{{CustomFieldName.id}}">
                            <div class="mb-2">
                                <span class="fw-bolder" >
                                    {% if vals %}
                                        {{ vals }}
                                    {% else %} 
                                    0
                                    {% endif %} 
                                </span>        
                            </div>
                        </div>
                    {% endif %}
                {% endif %}
            {% endwith %}
    </td>
    {% endif %}
