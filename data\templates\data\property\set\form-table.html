{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="d-flex align-items-end max-md:tw-mb-0 mt-5">
    <h3 class="fs-5 me-2">
        {% if LANGUAGE_CODE == 'ja' %}
            フォーム
        {% else %}
            Form
        {% endif %}
    </h3>

    <div class="max-md:tw-hidden tw-flex me-2 d-flex justify-content-end">
            <a class="max-md:tw-mb-3 py-1 d-flex align-items-center"
                hx-get="{% host_url 'form_sets_new' host 'app' %}" 
                hx-vals='{"page_group_type": "{{page_group_type}}","view_id":"{{view.id}}"}'

                {% if page_group_type|in_list:"commerce_orders,commerce_items,slips,journal,estimates,delivery_slips,invoices,receipts,commerce_subscription,commerce_inventory,commerce_inventory_transaction,commerce_inventory_warehouse,commerce_meter" %}
                    hx-target="#manage-contacts-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                {% elif page_group_type|in_list:"contacts,company" %}
                    hx-target="#manage-update-view-settings-drawer"
                {% elif page_group_type == "customer_case" %}
                    hx-target="#manage-view-settings-drawer" 
                {% elif page_group_type == "timegenie" %}
                    hx-target="#manage-update-view-settings-drawer" 
                {% elif page_group_type == constant.TYPE_OBJECT_TASK %}
                    hx-target="#view-drawer" 
                    hx-on::before-request="document.getElementById('view-drawer').innerHTML = ''"
                {% elif page_group_type|in_list:"purchaseorder,expense,bill"%}
                    hx-target="#expenses_form"
                    hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                    hx-indicator=".loading-drawer-spinner,.expenses-form"
                {% endif %}

                hx-trigger="click"
                type="button">
                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <polygon points="0 0 24 0 24 24 0 24"/>
                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                    </g>
                </span>
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </a>
        
    </div>

</div>

<input hidden type="text" name="page_group_type" value="{{page_group_type}}">
<table id="property-set-table" class="{% include "data/utility/table.html" %}">
    <thead class="{% include "data/utility/table-header.html" %}">
        <tr class="align-middle">  
            
            <th>
                {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %}
            </th>

            <th>
                {% if LANGUAGE_CODE == 'ja' %}フォーム名{% else %}Form Name{% endif %}
            </th>
            <th>
                {% if LANGUAGE_CODE == 'ja' %}プロパティ{% else %}Properties{% endif %}
            </th>

        </tr>
    </thead>

    <tbody>
        {% for set in sets %}
    
            <tr>

                <td class="fw-bolder min-w-100px">
                    <div class="form-check">
                        <input required value="{{set.id}}" name="property_set_default" class="form-check-input" type="radio" name="default" 
                        {% if view.form == set %}
                            checked  
                        {% elif not view.form and not set.name %} 
                            checked
                        {% elif not view.form and forloop.first %} 
                            checked
                        {% endif %}>
                    </div>
                </td>

                <td class="fw-bolder min-w-100px">
                    <a class="text-dark text-hover-primary cursor-pointer fw-bolder view-settings-button"          
                        hx-get="{% host_url 'form_sets_manage' set.id host 'app' %}" 
                        hx-vals='{"page_group_type": "{{page_group_type}}","view_id":"{{view.id}}"}'
                        
                        {% if page_group_type|in_list:"commerce_orders,commerce_items,slips,journal,estimates,delivery_slips,invoices,receipts,commerce_subscription,commerce_inventory,commerce_inventory_transaction,commerce_inventory_warehouse,commerce_meter" %}
                            hx-target="#manage-contacts-view-settings-drawer"
                        {% elif page_group_type|in_list:"contacts,company" %}
                            hx-target="#manage-update-view-settings-drawer" 
                        {% elif page_group_type == "customer_case" %}
                            hx-target="#manage-view-settings-drawer" 
                        {% elif page_group_type == constant.TYPE_OBJECT_TASK %}
                            hx-target="#view-drawer" 
                            hx-on::before-request="document.getElementById('view-drawer').innerHTML = ''"
                        {% elif page_group_type == "timegenie" %}
                            hx-target="#manage-update-view-settings-drawer" 
                        {% elif page_group_type|in_list:"purchaseorder,expense,bill"%}
                            hx-target="#expenses_form"
                            hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                            hx-indicator=".loading-drawer-spinner,.expenses-form"
                        {% endif %}
                        
                        hx-trigger="click"
                        >
                            {% if not set.name %}
                                {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %}
                            {% else %}
                                {{set.name}}
                            {% endif %}
                    </a>
                </td>
                <td class="fw-bold">
                    {% if set.children %}
                        {% for property in set.children|slice:":5" %}
                            {% with args=property|stringify|add:'|'|add:page_group_type %} 
                                {% with column_display=args|get_column_display:request %}
                                    {{ column_display.name }}{% if not forloop.last %}{% if LANGUAGE_CODE == 'ja' %}、{% else %}, {% endif %}{% elif set.children|length > 5 %}...{% endif %}
                                {% endwith %}
                            {% endwith %}
                        {% endfor %}
                    {% endif %}
                </td>
            
            </tr>


        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        var propertySetsTable = $("#property-set-table").DataTable({
            scrollX:        true,
            scrollCollapse: true,
            searching: true,  // Hide the search bar
            paging: false,      // Hide pagination
            info: false,        // Hide the information text
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            },
            sDom: "ltipr",
        });

        $('#property-set-search').on('keyup', function () {
            propertySetsTable.search(this.value).draw();
        });
    });
</script>

