import ast
import json
import traceback

from django.apps import apps
from django.core.cache import cache
from django.db.models import Q
import pytz

from data.constants.commerce_meter_constant import (
    COMMERCE_METER_COLUMN_DISPLAY,
    DEFAULT_COLUMNS_COMMERCE_METER,
    DEFAULT_FORM_FIELDS_COMMERCE_METER,
)
from data.constants.constant import (
    ABSENCE_COLUMNS_DISPLAY,
    APPLICANT_COLUMN_DISPLAY,
    BILL_OBJECTS,
    BILL_STATUS_DISPLAY,
    CASE_STATUS_DISPLAY,
    COMMERCE_STATUS_DISPLAY,
    COMPANY_COLUMNS_DISPLAY,
    COMPONENT_COLUMNS_DISPLAY,
    CONTACTS_COLUMNS_DISPLAY,
    CONTRACT_COLUMNS_DISPLAY,
    CONVERSATIONS_COLUMNS_DISPLAY,
    CUSTOM_OBJECT_COLUMN_DISPLAY,
    DASHBOARD_COLUMNS_<PERSON><PERSON><PERSON><PERSON><PERSON>,
    DEALS_COLUMNS_DISPLAY,
    DEFAULT_COLUMNS_ABSENCE,
    DEFAULT_COLUMNS_BILL,
    DEFAULT_COLUMNS_CASE,
    DEFAULT_COLUMNS_COMPANY,
    DEFAULT_COLUMNS_CONTACTS,
    DEFAULT_COLUMNS_CONTRACT,
    DEFAULT_COLUMNS_CONVERSATION,
    DEFAULT_COLUMNS_DASHBOARD,
    DEFAULT_COLUMNS_DELIVERY_SLIP,
    DEFAULT_COLUMNS_ESTIMATE,
    DEFAULT_COLUMNS_EXPENSE,
    DEFAULT_COLUMNS_FORM,
    DEFAULT_COLUMNS_INVENTORY,
    DEFAULT_COLUMNS_INVOICE,
    DEFAULT_COLUMNS_ITEM,
    DEFAULT_COLUMNS_JOBS,
    DEFAULT_COLUMNS_JOURNAL,
    DEFAULT_COLUMNS_ORDER,
    DEFAULT_COLUMNS_PANEL,
    DEFAULT_COLUMNS_PURCHASE_ORDER,
    DEFAULT_COLUMNS_RECEIPT,
    DEFAULT_COLUMNS_SLIP,
    DEFAULT_COLUMNS_SUBSCRIPTIONS,
    DEFAULT_COLUMNS_TASK,
    DEFAULT_COLUMNS_TRANSACTION,
    DEFAULT_COLUMNS_USER_MANAGEMENT,
    DEFAULT_COLUMNS_WAREHOUSE,
    DEFAULT_COLUMNS_WORKER,
    DEFAULT_COLUMNS_WORKER_REVIEW,
    DEFAULT_COLUMNS_WORKFLOW,
    DEFAULT_COLUMN_APPLICANT,
    DEFAULT_COLUMN_SOCIAL_MEDIA_ALL_POSTS,
    DEFAULT_COLUMN_TIMEGENIE,
    DEFAULT_FORM_FIELDS_EXPENSE,
    DEFAULT_FORM_FIELDS_PURHCASE_ORDER,
    DEFAULT_FORM_FIELD_BILL,
    DEFAULT_FORM_FIELD_CASE,
    DEFAULT_FORM_FIELD_COMMERCE,
    DEFAULT_FORM_FIELD_COMPANY,
    DEFAULT_FORM_FIELD_CONTACTS,
    DEFAULT_FORM_FIELD_DELIVERY_NOTE,
    DEFAULT_FORM_FIELD_INVENTORY,
    DEFAULT_FORM_FIELD_ITEM,
    DEFAULT_FORM_FIELD_JOURNAL,
    DEFAULT_FORM_FIELD_ORDER,
    DEFAULT_FORM_FIELD_SLIP,
    DEFAULT_FORM_FIELD_SUBSCRIPTIONS,
    DEFAULT_FORM_FIELD_TASK,
    DEFAULT_FORM_FIELD_TRANSACTION,
    DEFAULT_FORM_FIELD_WAREHOUSE,
    DEFAULT_SLIPS_TYPE_DISPLAY,
    DELIVERY_NOTE_COLUMNS_DISPLAY,
    DISPLAY_COLUMNS_BILL,
    DISPLAY_COLUMNS_EXPENSE,
    DISPLAY_COLUMNS_SOCIAL_MEDIA,
    DISPLAY_COLUMNS_TASK,
    DISPLAY_COLUMNS_WAREHOUSE,
    DISPLAY_COLUMNS_WORKFLOW,
    ESTIMATE_COLUMNS_DISPLAY,
    ESTIMATE_STATUS_DISPLAY,
    EVENT_COLUMNS_DISPLAY,
    EXCLUDE_SYNC_CHANNEL_NAME,
    EXPENSE_STATUS_DISPLAY,
    FORMULA_RELATED_COLUMNS_DISPLAY,
    FORM_COLUMNS_DISPLAY,
    INVENTORY_COLUMNS_DISPLAY,
    INVENTORY_STATUS_DISPLAY,
    INVENTORY_TRANSACTION_COLUMNS_DISPLAY,
    INVOICE_COLUMNS_DISPLAY,
    ITEMS_COLUMNS_DISPLAY,
    JOBS_COLUMNS_DISPLAY,
    JOBS_STATUS_DISPLAY,
    JOBS_TYPE_DISPLAY,
    JOB_STATUS_DISPLAY,
    JOB_TYPE_DISPLAY,
    JOURNAL_CATEGORY_DISPLAY,
    JOURNAL_COLUMNS_DISPLAY,
    JOURNAL_COUNTER_CATEGORY_DISPLAY,
    JOURNAL_TAX_CATEGORY_DISPLAY,
    OBJECT_GROUP_TYPE,
    OBJECT_GROUP_TYPE_SINGULAR,
    ORDERS_COLUMNS_DISPLAY,
    ORDERS_STATUS_DISPLAY,
    PANEL_COLUMNS_DISPLAY,
    PAYMENT_COLUMNS_DISPLAY,
    PURCHASE_ORDER_COLUMNS_DISPLAY,
    PURCHASE_ORDER_STATUS_DISPLAY,
    SEARCHABLE_PROPERTY_TYPES,
    SEARCH_COLUMNS_DISPLAY,
    SLIP_COLUMNS_DISPLAY,
    SUBSCRIPTIONS_COLUMNS_DISPLAY,
    SUBSCRIPTIONS_STATUS_DISPLAY,
    TASKS_STATUS_DISPLAY,
    THREAD_STATUS_DISPLAY,
    TIMEGENIE_COLUMNS_DISPLAY,
    USER_MANAGEMENT_COLUMNS_DISPLAY,
    WORKER_COLUMNS_DISPLAY,
    WORKER_REVIEW_COLUMNS_DISPLAY,
)
from data.constants.properties_constant import (
    DEFAULT_OBJECT_DISPLAY,
    OBJECT_TYPE_TO_SLUG,
    OBJECT_TYPE_TO_URL_NAME,
    TYPE_OBJECTS,
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_CAMPAIGN,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_CASE_LINE_ITEM,
    TYPE_OBJECT_COMMERCE_METER,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_CONTRACT,
    TYPE_OBJECT_CONVERSATION,
    TYPE_OBJECT_CUSTOM_OBJECT,
    TYPE_OBJECT_DASHBOARD,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_FORM,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_JOBS,
    TYPE_OBJECT_JOBS_APPLICANT,
    TYPE_OBJECT_JOURNAL,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_ORDER_LINE_ITEM,
    TYPE_OBJECT_PANEL,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SESSION_EVENT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_SUBSCRIPTION_PLATFORM,
    TYPE_OBJECT_TASK,
    TYPE_OBJECT_TIMEGENIE,
    TYPE_OBJECT_USER_MANAGEMENT,
    TYPE_OBJECT_WORKER,
    TYPE_OBJECT_WORKER_ABSENCE,
    TYPE_OBJECT_WORKER_REVIEW,
    TYPE_OBJECT_WORKFLOW,
)
from data.models import (
    Absence,
    AbsenceNameCustomField,
    AbsenceValueCustomField,
    AbsenceValueFile,
    Association,
    AssociationLabel,
    Bill,
    BillNameCustomField,
    BillValueCustomField,
    BillValueFile,
    Channel,
    CommerceMeter,
    CommerceMeterNameCustomField,
    CommerceMeterValueCustomField,
    CommerceMeterValueFile,
    Company,
    CompanyNameCustomField,
    CompanyValueCustomField,
    CompanyValueFile,
    Contact,
    ContactsNameCustomField,
    ContactsValueCustomField,
    ContactsValueFile,
    ContractDocument,
    ContractDocumentNameCustomField,
    ContractDocumentValueCustomField,
    CustomObject,
    CustomObjectPropertyName,
    CustomObjectPropertyRow,
    CustomObjectPropertyValue,
    CustomProperty,
    Deals,
    DealsItems,
    DealsItemsNameCustomField,
    DealsItemsValueCustomField,
    DealsNameCustomField,
    DealsValueCustomField,
    DealsValueFile,
    DeliverySlip,
    DeliverySlipItem,
    DeliverySlipItemsNameCustomField,
    DeliverySlipItemsValueCustomField,
    DeliverySlipNameCustomField,
    DeliverySlipValueCustomField,
    DeliverySlipValueFile,
    Estimate,
    EstimateItem,
    EstimateItemsNameCustomField,
    EstimateItemsValueCustomField,
    EstimateNameCustomField,
    EstimatePurchaseItem,
    EstimateValueCustomField,
    EstimateValueFile,
    EventNameCustomProperties,
    Expense,
    ExpenseNameCustomField,
    ExpenseValueCustomField,
    ExpenseValueFile,
    Form,
    InventoryTransaction,
    InventoryTransactionNameCustomField,
    InventoryTransactionValueCustomField,
    InventoryTransactionValueFile,
    InventoryWarehouse,
    InventoryWarehouseNameCustomField,
    InventoryWarehouseValueCustomField,
    InventoryWarehouseValueFile,
    Invoice,
    InvoiceItem,
    InvoiceItemsNameCustomField,
    InvoiceItemsValueCustomField,
    InvoiceNameCustomField,
    InvoiceValueCustomField,
    InvoiceValueFile,
    Job,
    JobApplicantNameCustomField,
    JobApplicantValueCustomField,
    JobApplicantValueFile,
    JobApplication,
    JobNameCustomField,
    JobValueCustomField,
    JobValueFile,
    JournalEntry,
    JournalEntryNameCustomField,
    JournalEntryTransaction,
    JournalEntryValueCustomField,
    JournalEntryValueFile,
    MessageNameCustomField,
    MessageThread,
    MessageValueCustomField,
    ObjectManager,
    PixelEvent,
    Post,
    PostNameCustomField,
    PostValueCustomField,
    PostValueFile,
    PropertySet,
    PurchaseItems,
    PurchaseOrders,
    PurchaseOrdersItem,
    PurchaseOrdersNameCustomField,
    PurchaseOrdersValueCustomField,
    PurchaseOrdersValueFile,
    Receipt,
    ReceiptItem,
    ReceiptItemsNameCustomField,
    ReceiptItemsValueCustomField,
    ReceiptNameCustomField,
    ReceiptValueCustomField,
    ReceiptValueFile,
    Report,
    ReportNameCustomField,
    ReportPanel,
    ReportPanelNameCustomField,
    ReportPanelValueCustomField,
    ReportValueCustomField,
    ShopTurboInventory,
    ShopTurboInventoryNameCustomField,
    ShopTurboInventoryValueCustomField,
    ShopTurboInventoryValueFile,
    ShopTurboItemComponents,
    ShopTurboItems,
    ShopTurboItemsBills,
    ShopTurboItemsNameCustomField,
    ShopTurboItemsOrders,
    ShopTurboItemsOrdersNameCustomField,
    ShopTurboItemsOrdersValueCustomField,
    ShopTurboItemsPrice,
    ShopTurboItemsSubscriptions,
    ShopTurboItemsValueCustomField,
    ShopTurboItemsValueFile,
    ShopTurboOrders,
    ShopTurboOrdersNameCustomField,
    ShopTurboOrdersValueCustomField,
    ShopTurboOrdersValueFile,
    ShopTurboSubscriptionPlatforms,
    ShopTurboSubscriptions,
    ShopTurboSubscriptionsNameCustomField,
    ShopTurboSubscriptionsValueCustomField,
    ShopTurboSubscriptionsValueFile,
    Slip,
    SlipItem,
    SlipNameCustomField,
    SlipValueCustomField,
    SlipValueFile,
    Task,
    TaskCustomFieldName,
    TaskCustomFieldValue,
    TaskItem,
    Track,
    TrackNameCustomField,
    TrackValueCustomField,
    TrackValueFile,
    User,
    UserManagement,
    UserManagementNameCustomField,
    UserManagementValueCustomField,
    UserManagementValueFile,
    Worker,
    WorkerNameCustomField,
    WorkerReviews,
    WorkerReviewsNameCustomField,
    WorkerReviewsValueCustomField,
    WorkerReviewsValueFile,
    WorkerValueCustomField,
    WorkerValueFile,
    Workflow,
)
from utils.contact import get_order_customer_custom_property
from utils.currency_symbols.currency_symbols import CurrencySymbols
from utils.discord import DiscordNotification
from utils.inventory import get_and_update_inventory_amount, item_inventory_amount
from utils.utility import get_attr, is_valid_uuid, reorder_columns, translate_language


def safe_format_id(id_value, padding=4):
    """
    Safely format an ID value with zero-padding, handling both integers and strings.

    Args:
        id_value: The ID value to format (can be int, str, or None)
        padding: Number of digits to pad to (default: 4)

    Returns:
        Formatted string with zero-padding, or empty string if None
    """
    if id_value is None:
        return ""

    try:
        # If it's already a string that looks like a formatted ID, return as-is
        if isinstance(id_value, str):
            # Check if it's already properly formatted (all digits)
            if id_value.isdigit():
                # Re-format to ensure consistent padding
                return f"{int(id_value):0{padding}d}"
            else:
                # Return as-is if it's not a pure number string
                return str(id_value)

        # If it's a number, format it
        return f"{int(id_value):0{padding}d}"
    except (ValueError, TypeError):
        # Fallback: return string representation
        return str(id_value)


OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE = {
    "components": TYPE_OBJECT_ITEM,
    "bill_objects": TYPE_OBJECT_BILL,
    "invoice_objects": TYPE_OBJECT_INVOICE,
    "contact": TYPE_OBJECT_CONTACT,
    "company": TYPE_OBJECT_COMPANY,
    "purchase_order": TYPE_OBJECT_PURCHASE_ORDER,
    "subscription": TYPE_OBJECT_SUBSCRIPTION,
    "warehouse_objects": TYPE_OBJECT_INVENTORY_WAREHOUSE,
}

SIMPLE_TYPE_MAPPING = {
    "text": "string",
    "text-area": "string",
    "choice": "string",
    "formula": "string",
    "number": "numeric",
    "date": "datetime",
    "date_time": "datetime",
}

ALIAS_TABLE = {
    "cases": "deals",
    "items": "shopturboitems",
    "inventory": "shopturboinventory",
    "inventory_transaction": "inventorytransaction",
    "inventory_warehouse": "inventorywarehouse",
    "worker": "worker",
    "journal": "journalentry",
    "purchaseorder": "purchaseorders",
    "billing": "bill",
    "bill": "bill",
    "expense": "expense",
    "subscription": "shopturbosubscriptions",
    "estimates": "estimate",
    "orders": "shopturboorders",
    "delivery_slips": "deliveryslip",
    "invoices": "invoice",
    "receipts": "receipt",
    "slips": "slip",
    "contacts": "contacts",
    "company": "company",
    "campaigns": "post",
    "conversation": "message",
    "task": "taskcustomfield",
    "production": "production",
    "panels": "reportpanel",
    "dashboards": "report",
    "contract": "contractdocument",
}


def get_report_metrics(workspace):
    props = {}

    for data_source in [
        "orders",
        "subscriptions",
        "contacts",
        "companies",
        "events",
        "deals",
        "inventory",
    ]:
        base_props = []
        custom_props = []

        if data_source == "orders":
            base_props = get_base_props(ShopTurboOrders, data_source, ["kanban_order"])
            for _prop in base_props:
                if _prop["name"] == "status":
                    _prop["values"] = ["active", "archived"]
                    break
            custom_props = get_custom_props(
                ShopTurboOrdersNameCustomField, data_source, workspace
            )

        elif data_source == "subscriptions":
            base_props = get_base_props(ShopTurboSubscriptions, data_source)
            for _prop in base_props:
                if _prop["name"] == "status":
                    _prop["values"] = [
                        "draft",
                        "active",
                        "paused",
                        "canceled",
                        "archived",
                    ]
                    break
            custom_props = get_custom_props(
                ShopTurboSubscriptionsNameCustomField, data_source, workspace
            )

        elif data_source == "contacts":
            base_props = get_base_props(Contact, data_source)
            custom_props = get_custom_props(
                ContactsNameCustomField, data_source, workspace
            )

        elif data_source == "companies":
            base_props = get_base_props(Company, data_source)
            custom_props = get_custom_props(
                CompanyNameCustomField, data_source, workspace
            )

        elif data_source == "events":
            base_props = get_base_props(PixelEvent, data_source)
            custom_fields = EventNameCustomProperties.objects.filter(
                workspace=workspace
            )
            for ct_field in custom_fields:
                try:
                    custom_props.append(
                        {
                            "name": f"{ct_field.property_name}",
                            "type": ct_field.type,
                            "source": data_source,
                            "values": [],
                        }
                    )
                except Exception as e:
                    print(f"[ERROR] === properties.py - 36: {e}")

        elif data_source == "deals":
            base_props = get_base_props(Deals, data_source)
            custom_props = get_custom_props(
                DealsNameCustomField, data_source, workspace
            )

        elif data_source == "inventory":
            base_props = get_base_props(InventoryTransaction, data_source)
            custom_props = get_custom_props(
                InventoryTransactionNameCustomField, data_source, workspace
            )

        props[data_source] = {"base": base_props, "custom": custom_props}

    return props


def get_base_props(model, data_source, excludes=[]):
    properties = []
    for field in model._meta.get_fields():
        field_type = field.get_internal_type()
        if (
            field_type
            in [
                "ForeignKey",
                "UUIDField",
                "JSONField",
                "URLField",
                "FileField",
                "ManyToManyField",
            ]
            or "url" in field.name
            or field.name in excludes
        ):
            continue

        properties.append(
            get_prop_from_field(model, field.name, field_type, data_source)
        )

    for field in model._meta.many_to_many:
        value = field.name
        properties.append(get_prop_from_field(model, value, "object", data_source))

    return properties


def get_custom_props(model, data_source, workspace):
    properties = []
    custom_fields = model.objects.filter(workspace=workspace)
    for ct_field in custom_fields:
        try:
            type_mapped = SIMPLE_TYPE_MAPPING.get(
                ct_field.type, "string"
            )  # Set default to string
            properties.append(
                {
                    "name": f"{ct_field.name}",
                    "type": type_mapped,
                    "source": data_source,
                    "values": [],
                }
            )
        except Exception as e:
            print(f"[ERROR] === properties.py - 36: {e}")

    return properties


def get_prop_from_field(model, field_name, field_type, data_source):
    property = {
        "name": field_name,
        "type": "string",
        "values": [],
        "source": data_source,
    }

    if field_type in ["CharField", "TextField"]:
        property["type"] = "string"
        choices = model._meta.get_field(field_name).choices
        if choices:
            property["values"] = choices
    elif field_type in ["IntegerField", "FloatField"]:
        property["type"] = "numeric"
    elif field_type in ["BooleanField"]:
        property["type"] = "boolean"
        property["values"] = ["true", "false"]
    elif field_type in ["DateTimeField"]:
        property["type"] = "datetime"
    elif field_type in ["object"]:
        property["type"] = "object"

    return property


def base_model_to_object_type(base_model_ref):
    """Builds the map from base_model class to object_type string."""
    for obj_type in OBJECT_TYPE_TO_SLUG:
        try:
            page_config = get_page_object(obj_type)
            base_model = page_config.get("base_model")
            if base_model == base_model_ref:
                return obj_type
        except Exception as e:
            # Handle cases where get_page_object might fail for a type or type is invalid
            print(f"[WARN] Could not map object_type '{obj_type}': {e}")
    return None


def get_page_object(object_type, lang="ja"):
    obj = {
        "base_model": None,
        "custom_object": None,
        "custom_model": None,
        "custom_value_model": None,
        "custom_item_model": None,
        "custom_item_value_model": None,
        "custom_line_item_model": None,
        "custom_line_item_value_model": None,
        "additional_filter_fields": [],  # For filtering additional column to be added
        "additional_column_fields": [],  # For managing additional column to be added
        "required_column_fields": [],  # Properties can not removed in column tagify
        "custom_value_relation": None,  # Relation between custom_value_model and base_model
        "custom_value_file_model": None,
        # Relation between custom_value_file_model and custom_value_model
        "custom_value_file_relation": None,
        # Related model of custom_model. Format: {"model":<>, "relation":<>, "type":<custom property type>}
        "custom_value_related_model": [],
        "base_columns": [],  # Get columns as list field of object
        "default_columns": None,  # List columns shown when create a new view menu
        # List form fields shown when create a new view menu
        "default_form_fields": [],
        "columns_display": None,
        "additional_columns_display": None,
        # 'reverse_url': None,            # Redirect URL
        "reverse_query": None,
        "id_field": None,
        "file_name": None,  # Name of download file
        "file_field": None,  # Name of field contains file, use for download
        "custom_relation": None,  # Relation between custom_value_model and custom_model
        "page_title": None,
        "page_type": None,  # For the pricing and usage limit
        # List fields will be apply to search pattern by default
        "search_fields": None,
        "download_formats": [],
        "download_form_url": None,  # Change download form in drawer
        "view_types": [],
        "setting_url": None,  # Setting page
        "setting_type": None,  # Query path at setting_url
        "editable_columns": {},  # Default properties could be editable by user
        "required_properties": [],  # Properties can not removed in property set
        "item_model": None,  # item child for obj
        "field_item_name": None,  # Field name for item
        "manage_obj_link": None,
        "file_upload_to": "None",
        "exclude_custom_types": [],
        # The list types of custom field which can add multiple values
        "multiple_custom_types": [],
        "item_related_name": "",
        "related_data": [],  # Tagify Formula Related Data
        "app_target": None,  # AppSettings app_target
        "parent_object": None,  # Add parent to sub object -> example: job (parent) and applicant (child)
    }

    if object_type == TYPE_OBJECT_ITEM:
        obj["base_model"] = ShopTurboItems
        obj["custom_model"] = ShopTurboItemsNameCustomField
        obj["custom_value_model"] = ShopTurboItemsValueCustomField
        obj["custom_value_relation"] = "items"
        obj["custom_value_file_model"] = ShopTurboItemsValueFile
        obj["file_upload_to"] = "shopturbo-item-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["custom_value_related_model"] = [
            {
                "model": ShopTurboItemComponents,
                "relation": "components",
                "type": "components",
                "columns_display": COMPONENT_COLUMNS_DISPLAY.copy(),
            }
        ]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["product_id", "created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ITEM.copy()
        obj["columns_display"] = ITEMS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_ITEM.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "item_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "shopturbo_item_custom_field_relations"
        obj["page_title"] = "商品" if lang == "ja" else "Items"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("group", "Group Table", "グループテーブル"),
            ("price_table", "Price Table", "価格表"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_ITEM
        obj["editable_columns"] = ["name"]
        obj["required_properties"] = ["name"]
        obj["exclude_custom_types"] = [
            "date_range",
            "bill_objects",
            "purchase_order",
            "part",
        ]
        obj["multiple_custom_types"] = ["components"]
        obj["related_data"] = ["order"]
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_ORDER:
        obj["base_model"] = ShopTurboOrders
        obj["custom_model"] = ShopTurboOrdersNameCustomField
        obj["custom_value_model"] = ShopTurboOrdersValueCustomField
        obj["item_model"] = ShopTurboItemsOrders
        obj["custom_value_relation"] = "orders"
        obj["custom_value_file_model"] = ShopTurboOrdersValueFile
        obj["custom_line_item_model"] = ShopTurboItemsOrdersNameCustomField
        obj["custom_line_item_value_model"] = ShopTurboItemsOrdersValueCustomField
        obj["file_upload_to"] = "shopturbo-order-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[
                "item_price_order",
                "kanban_order",
                "shipping_cost_tax_status",
                "created_at",
            ],
            includes=["inventory_transactions", "invoice", "estimate", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ORDER.copy()
        obj["columns_display"] = ORDERS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_ORDER.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "order_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "shopturbo_custom_field_relations"
        obj["page_title"] = "受注" if lang == "ja" else "Orders"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("kanban", "Kanban", "カンバン"),
            ("item_list", "Line Items", "商品項目"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_ORDER
        obj["editable_columns"] = {
            "delivery_status": {
                k: v
                for k, v in ORDERS_STATUS_DISPLAY.items()
                if k not in ["active", "archived"]
            },
            "line_item": "",
        }
        obj["required_properties"] = ["line_item"]
        obj["exclude_custom_types"] = [
            "part",
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["pdf_title"] = "受注" if lang == "ja" else "Order"
        obj["default_order_name"] = "デフォルト" if lang == "ja" else "Default"
        obj["related_data"] = [
            TYPE_OBJECT_ITEM,
            TYPE_OBJECT_INVENTORY_TRANSACTION,
            TYPE_OBJECT_PURCHASE_ORDER,
        ]
        obj["pdf_pattern"] = "orderPDF-pattern-"
        obj["field_item_name"] = "order"
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_SUBSCRIPTION:
        obj["base_model"] = ShopTurboSubscriptions
        obj["custom_model"] = ShopTurboSubscriptionsNameCustomField
        obj["custom_value_model"] = ShopTurboSubscriptionsValueCustomField
        obj["item_model"] = ShopTurboItemsSubscriptions
        obj["custom_value_relation"] = "subscriptions"
        obj["custom_value_file_model"] = ShopTurboSubscriptionsValueFile
        obj["file_upload_to"] = "shopturbo-subscription-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            includes=[
                "orders",
                "invoices",
                "item_name",
                "customer",
                "durations",
                "line_item",
                "owner",
            ],
            excludes=[
                "tax_applied_to",
                "tax_rate",
                "billing_timing",
                "status",
                "prior_to_next",
                "prior_to_time",
            ],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_SUBSCRIPTIONS.copy() + ["tax"]
        obj["columns_display"] = SUBSCRIPTIONS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_SUBSCRIPTIONS.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "subscriptions_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "shopturbo_subscriptions_custom_field_relations"
        obj["page_title"] = "サブスクリプション" if lang == "ja" else "Subscriptions"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_SUBSCRIPTION
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["editable_columns"] = {"subscription_status": SUBSCRIPTIONS_STATUS_DISPLAY}
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_SUBSCRIPTION_PLATFORM:
        obj["base_model"] = ShopTurboSubscriptionPlatforms
        obj["custom_model"] = None
        obj["custom_value_model"] = None
        obj["custom_value_relation"] = None
        obj["custom_value_file_model"] = None
        obj["file_upload_to"] = None
        obj["custom_value_file_relation"] = None
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=["manage_payment_session_id", "payment_setup_id"],
            includes=["source_subscription", "channel"],
        )
        obj["default_columns"] = [
            "platform_id",
            "platform_display_name",
            "platform_status",
            "payment_status",
            "created_at",
        ]
        obj["columns_display"] = {
            "platform_id": {"en": "Platform ID", "ja": "プラットフォームID"},
            "platform_display_name": {
                "en": "Platform Name",
                "ja": "プラットフォーム名",
            },
            "platform_status": {
                "en": "Platform Status",
                "ja": "プラットフォームステータス",
            },
            "payment_status": {"en": "Payment Status", "ja": "支払いステータス"},
            "payment_link": {"en": "Payment Link", "ja": "支払いリンク"},
            "source_subscription": {
                "en": "Source Subscription",
                "ja": "元のサブスクリプション",
            },
            "channel": {"en": "Channel", "ja": "チャネル"},
            "created_at": {"en": "Created At", "ja": "作成日時"},
            "updated_at": {"en": "Updated At", "ja": "更新日時"},
        }
        obj["id_field"] = None
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = (
            "サブスクリプションプラットフォーム"
            if lang == "ja"
            else "Subscription Platforms"
        )
        obj["search_fields"] = ["platform_id", "platform_display_name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_SUBSCRIPTION_PLATFORM
        obj["exclude_custom_types"] = []
        obj["editable_columns"] = {}
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_CASE:
        obj["base_model"] = Deals
        obj["custom_model"] = DealsNameCustomField
        obj["custom_value_model"] = DealsValueCustomField
        obj["custom_value_file_model"] = DealsValueFile
        obj["file_upload_to"] = "deals-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["custom_value_relation"] = "deals"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_CASE.copy()
        obj["columns_display"] = DEALS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_CASE.copy()
        obj["editable_columns"] = {"case_status": CASE_STATUS_DISPLAY, "line_item": ""}
        obj["id_field"] = "deal_id"
        obj["file_name"] = "cases"
        obj["file_field"] = None
        obj["custom_relation"] = "deals_custom_field_relations"
        obj["page_title"] = "案件" if lang == "ja" else "Case"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("table", "Table", "テーブル"),
            ("kanban", "Kanban", "カンバン"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["download_form_url"] = "service_deals"
        obj["setting_type"] = TYPE_OBJECT_CASE
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["related_data"] = []
        obj["app_target"] = "contacts"

    elif object_type == TYPE_OBJECT_CONTACT:
        obj["base_model"] = Contact
        obj["custom_model"] = ContactsNameCustomField
        obj["custom_value_model"] = ContactsValueCustomField
        obj["custom_value_file_model"] = ContactsValueFile
        obj["file_upload_to"] = "contacts-custom-field-files"
        obj["custom_value_relation"] = "contact"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=["created_at", "url", "status"],
            includes=["owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_CONTACTS.copy()
        obj["columns_display"] = CONTACTS_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_CONTACTS.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        # obj['additional_filter_fields'] = ['customers__companies__name']
        obj["id_field"] = "contact_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "contact_custom_field_relations"
        obj["page_title"] = "連絡先" if lang == "ja" else "Contacts"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("table", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CONTACT
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["required_properties"] = ["first_name", "last_name"]
        obj["related_data"] = ["journal"]
        obj["app_target"] = "contacts"

    elif object_type == TYPE_OBJECT_COMPANY:
        obj["base_model"] = Company
        obj["custom_model"] = CompanyNameCustomField
        obj["custom_value_model"] = CompanyValueCustomField
        obj["custom_value_file_model"] = CompanyValueFile
        obj["file_upload_to"] = "company-custom-field-files"
        obj["custom_value_relation"] = "company"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["created_at", "status"], includes=["owner"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_COMPANY.copy()
        obj["columns_display"] = COMPANY_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_COMPANY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "company_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "company_custom_field_relations"
        obj["page_title"] = "企業" if lang == "ja" else "Company"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("table", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_COMPANY
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["related_data"] = ["journal"]
        obj["multiple_custom_types"] = ["hierarchy"]
        obj["app_target"] = "contacts"

    elif object_type == TYPE_OBJECT_CONVERSATION:
        obj["base_model"] = MessageThread
        obj["custom_model"] = MessageNameCustomField
        obj["custom_value_model"] = MessageValueCustomField
        obj["custom_value_relation"] = "message"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_CONVERSATION.copy()
        obj["columns_display"] = CONVERSATIONS_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CONVERSATION
        obj["editable_columns"] = {"status": THREAD_STATUS_DISPLAY}
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_PURCHASE_ORDER:
        obj["base_model"] = PurchaseOrders
        obj["item_model"] = PurchaseOrdersItem
        obj["custom_model"] = PurchaseOrdersNameCustomField
        obj["custom_value_model"] = PurchaseOrdersValueCustomField
        obj["custom_value_file_model"] = PurchaseOrdersValueFile
        obj["file_upload_to"] = "purchase-order-custom-field-files"
        obj["custom_value_relation"] = "purchaseorders"
        obj["additional_filter_fields"] = ["supplier"] + [
            "supplier__company__name",
            "supplier__contact__name",
            "supplier__contact__first_name",
            "supplier__contact__last_name",
        ]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[
                "usage_status",
                "amount",
                "tax_option",
                "amount_item",
                "items",
                "item_name",
                "item_status",
                "item_price",
                "item_price_without_tax",
            ],
            includes=obj["additional_filter_fields"] + ["inventory_transactions"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_PURCHASE_ORDER.copy()
        obj["columns_display"] = PURCHASE_ORDER_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELDS_PURHCASE_ORDER.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["exclude_custom_types"] = ["purchase_order", "bill_objects"]
        obj["id_field"] = "id_po"
        obj["file_name"] = "purchase_orders"
        obj["file_field"] = None
        obj["custom_relation"] = "purchase_order_field_relations"
        obj["page_title"] = "発注" if lang == "ja" else "Purchase Orders"
        obj["page_type"] = "purchase_orders"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "dat"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("item_list", "Line Items", "商品項目"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_PURCHASE_ORDER
        obj["field_item_name"] = "purchaseorders"
        obj["pdf_pattern"] = "purchaseorderPDF-pattern-"
        obj["pdf_title"] = "発注" if lang == "ja" else "Purchase Orders"
        obj["multiple_custom_types"] = ["bill_objects"]
        obj["editable_columns"] = {
            "status": PURCHASE_ORDER_STATUS_DISPLAY,
            "line_item": "",
        }
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["row_detail_url"] = "purchase_order_row_detail"
        obj["manage_obj_link"] = "purchase_manage"

    elif object_type == TYPE_OBJECT_BILL:
        obj["base_model"] = Bill
        obj["item_model"] = ShopTurboItemsBills
        obj["custom_model"] = BillNameCustomField
        obj["custom_value_model"] = BillValueCustomField
        obj["custom_value_relation"] = "bill"
        obj["custom_value_file_model"] = BillValueFile
        obj["file_upload_to"] = "spendpocket-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=["created_at", "updated_at", "bill_type", "notified"],
            includes=["partner", "file", "journal_entry", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_BILL.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_BILL.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_BILL.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_bill"
        obj["file_name"] = "bills"
        obj["file_field"] = "file"
        obj["field_item_name"] = "bill"
        obj["custom_relation"] = "bill_custom_field_relations"
        obj["page_title"] = "支払請求" if lang == "ja" else "Bills"
        obj["page_type"] = "bills"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "pdf"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_BILL
        obj["exclude_custom_types"] = ["bill_objects"]
        obj["manage_obj_link"] = "bill_manage"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["row_detail_url"] = "bill_row_detail"
        obj["editable_columns"] = {"status": BILL_STATUS_DISPLAY}
        obj["pdf_download_link"] = "bill_pdf_download"
        obj["update_obj_link"] = "update_bill"
        obj["new_obj_link"] = "new_bill"
        obj["drawer_url"] = "bill_drawer"
        obj["pdf_pattern"] = "billPDF-pattern-"
        obj["pdf_title"] = "請求" if lang == "ja" else "Bill"
        obj["page_location"] = "data/invoices/bill-table.html"
        obj["download_form_url"] = "bill"

    elif object_type == TYPE_OBJECT_EXPENSE:  # Expenses
        obj["base_model"] = Expense
        obj["custom_model"] = ExpenseNameCustomField
        obj["custom_value_model"] = ExpenseValueCustomField
        obj["custom_value_relation"] = "expense"
        obj["custom_value_file_model"] = ExpenseValueFile
        obj["file_upload_to"] = "spendpocket-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["additional_column_fields"] = ["description"]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            includes=obj["additional_filter_fields"] + ["owner", "partner"],
            excludes=["created_at", "date_received", "display_status", "submitter"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_EXPENSE.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_EXPENSE.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELDS_EXPENSE.copy()

        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_pm"
        obj["file_name"] = "expenses"
        obj["file_field"] = "paymentfile_set"
        obj["custom_relation"] = "expense_custom_field_relations"
        obj["page_title"] = "経費" if lang == "ja" else "Expenses"
        obj["page_type"] = "expenses"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "pdf"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["manage_obj_link"] = "expense_drawer"
        obj["setting_type"] = TYPE_OBJECT_EXPENSE
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["row_detail_url"] = "expense_row_detail"
        obj["editable_columns"] = {"status": EXPENSE_STATUS_DISPLAY}

    elif object_type == TYPE_OBJECT_DELIVERY_NOTE:
        obj["base_model"] = DeliverySlip
        obj["item_model"] = DeliverySlipItem
        obj["field_item_name"] = "deliveryslip"
        obj["custom_model"] = DeliverySlipNameCustomField
        obj["custom_value_model"] = DeliverySlipValueCustomField
        obj["custom_item_model"] = DeliverySlipItemsNameCustomField
        obj["custom_item_value_model"] = DeliverySlipItemsValueCustomField
        obj["custom_value_relation"] = "deliveryslip"
        obj["custom_value_file_model"] = DeliverySlipValueFile
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "email",
                "amount",
                "amount_item",
                "cc_list",
                "amount",
                "amount_item",
                "tax_list",
                "tax_option",
                "discount_option",
                "discount_tax_option",
                "discount",
            ],
            includes=obj["additional_filter_fields"] + ["owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_DELIVERY_SLIP.copy()
        obj["columns_display"] = DELIVERY_NOTE_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_DELIVERY_NOTE.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_ds"
        obj["file_name"] = "delivery_notes"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "納品" if lang == "ja" else "Delivery Notes"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_DELIVERY_NOTE
        obj["pdf_pattern"] = "deliveryslipPDF-pattern-"
        obj["page_location"] = "data/delivery_note/delivery-note-table.html"
        obj["pdf_download_link"] = "delivery_slip_pdf_download"
        obj["update_obj_link"] = "update_delivery_slip"
        obj["drawer_url"] = "delivery_slip_drawer"
        obj["manage_obj_link"] = "delivery_slip_edit"
        obj["new_obj_link"] = "new_delivery_slip"
        obj["download_form_url"] = "delivery_slips"
        obj["pdf_title"] = "納品" if lang == "ja" else "Delivery Notes"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["editable_columns"] = {"status": COMMERCE_STATUS_DISPLAY, "line_item": ""}

    elif object_type == TYPE_OBJECT_RECEIPT:
        obj["base_model"] = Receipt
        obj["item_model"] = ReceiptItem
        obj["custom_model"] = ReceiptNameCustomField
        obj["custom_value_model"] = ReceiptValueCustomField
        obj["field_item_name"] = "receipt"
        obj["custom_value_file_model"] = ReceiptValueFile
        obj["custom_item_model"] = ReceiptItemsNameCustomField
        obj["custom_item_value_model"] = ReceiptItemsValueCustomField
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["custom_value_relation"] = "receipt"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "email",
                "amount",
                "amount_item",
                "cc_list",
                "tax_list",
                "tax_option",
                "discount_option",
                "discount_tax_option",
                "discount",
            ],
            includes=obj["additional_filter_fields"] + ["owner"],
        )
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["default_columns"] = DEFAULT_COLUMNS_RECEIPT.copy()
        obj["columns_display"] = PAYMENT_COLUMNS_DISPLAY.copy()
        obj["additional_columns_display"] = PAYMENT_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_COMMERCE.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_rcp"
        obj["file_name"] = "receipts"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "入金" if lang == "ja" else "Payments"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_RECEIPT
        obj["pdf_pattern"] = "receiptPDF-pattern-"
        obj["page_location"] = "data/receipt/receipt-table.html"
        obj["pdf_download_link"] = "receipt_pdf_download"
        obj["update_obj_link"] = "update_receipt"
        obj["new_obj_link"] = "new_receipt"
        obj["drawer_url"] = "receipt_drawer"
        obj["download_form_url"] = "receipts"
        obj["pdf_title"] = "入金" if lang == "ja" else "Payments"
        obj["manage_obj_link"] = "receipt_edit"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["editable_columns"] = {"status": COMMERCE_STATUS_DISPLAY, "line_item": ""}

    elif object_type == TYPE_OBJECT_ESTIMATE:
        obj["base_model"] = Estimate
        obj["item_model"] = EstimateItem
        obj["purchase_item_model"] = EstimatePurchaseItem
        obj["custom_model"] = EstimateNameCustomField
        obj["custom_value_model"] = EstimateValueCustomField

        obj["custom_item_model"] = EstimateItemsNameCustomField
        obj["custom_item_value_model"] = EstimateItemsValueCustomField

        obj["field_item_name"] = "estimate"
        obj["custom_value_relation"] = "estimate"
        obj["custom_value_file_model"] = EstimateValueFile
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "amount",
                "amount_item",
                "tax_list",
                "tax_option",
                "discount_tax_option",
                "old_id_est",
            ],
            includes=obj["additional_filter_fields"]
            + ["invoice", "order_association", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ESTIMATE.copy()
        obj["columns_display"] = ESTIMATE_COLUMNS_DISPLAY.copy()
        obj["additional_columns_display"] = ESTIMATE_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_COMMERCE.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_est"
        obj["file_name"] = "estimates"
        obj["file_field"] = None
        obj["custom_relation"] = "estimate_custom_field_relations"
        obj["page_title"] = "見積" if lang == "ja" else "Estimates"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "pdf"]
        obj["download_form_url"] = "estimates"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_ESTIMATE
        obj["pdf_pattern"] = "estimatePDF-pattern-"
        obj["page_location"] = "data/estimate/estimates-table.html"
        obj["pdf_download_link"] = "estimate_pdf_download"
        obj["drawer_url"] = "estimate_drawer"
        obj["update_obj_link"] = "update_estimate"
        obj["new_obj_link"] = "new_estimate"
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["pdf_title"] = "見積書" if lang == "ja" else "Estimate"
        obj["manage_obj_link"] = "estimate_edit"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["editable_columns"] = {"status": ESTIMATE_STATUS_DISPLAY, "line_item": ""}

    elif object_type == TYPE_OBJECT_INVOICE:
        obj["base_model"] = Invoice
        obj["item_model"] = InvoiceItem
        obj["custom_model"] = InvoiceNameCustomField
        obj["custom_value_model"] = InvoiceValueCustomField
        obj["custom_value_file_model"] = InvoiceValueFile
        obj["custom_item_model"] = InvoiceItemsNameCustomField
        obj["custom_item_value_model"] = InvoiceItemsValueCustomField
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["field_item_name"] = "invoice"
        obj["custom_value_relation"] = "invoice"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "amount",
                "amount_item",
                "tax_list",
                "tax_option",
                "discount_tax_option",
            ],
            includes=obj["additional_filter_fields"]
            + ["associate#receipts", "order_association", "subscription", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_INVOICE.copy()
        obj["columns_display"] = INVOICE_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_COMMERCE.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_inv"
        obj["file_name"] = "invoices"
        obj["file_field"] = None
        obj["custom_relation"] = "invoice_custom_field_relations"
        obj["page_title"] = "売上請求" if lang == "ja" else "Invoices"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv", "pdf"]
        obj["download_form_url"] = "invoices"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_INVOICE
        obj["pdf_pattern"] = "invoicePDF-pattern-"
        obj["page_location"] = "data/invoice/invoices-table.html"
        obj["pdf_download_link"] = "invoice_pdf_download"
        obj["drawer_url"] = "invoice_drawer"
        obj["update_obj_link"] = "update_invoice"
        obj["new_obj_link"] = "new_invoice"
        obj["pdf_title"] = "請求書" if lang == "ja" else "Invoice"
        obj["manage_obj_link"] = "invoice_edit"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["editable_columns"] = {"status": COMMERCE_STATUS_DISPLAY, "line_item": ""}

    elif object_type == TYPE_OBJECT_SLIP:
        obj["base_model"] = Slip
        obj["item_model"] = SlipItem
        obj["field_item_name"] = "slip"
        obj["custom_model"] = SlipNameCustomField
        obj["custom_value_model"] = SlipValueCustomField
        obj["custom_value_file_model"] = SlipValueFile
        obj["file_upload_to"] = "billing-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["custom_value_relation"] = "slip"
        obj["additional_filter_fields"] = [
            "customers__company__name",
            "customers__contact__name",
            "customers__contact__first_name",
            "customers__contact__last_name",
        ]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[
                "lang",
                "email",
                "amount",
                "amount_item",
                "cc_list",
                "amount",
                "amount_item",
                "tax_list",
                "tax_option",
                "discount_option",
                "discount_tax_option",
                "discount",
                "status",
                "input_item",
            ],
            includes=obj["additional_filter_fields"] + ["journal_entry", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_SLIP.copy()
        obj["columns_display"] = SLIP_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_slip"
        obj["file_name"] = "slips"
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_SLIP.copy()
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "伝票" if lang == "ja" else "Slips"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_SLIP
        obj["pdf_pattern"] = "slipPDF-pattern-"
        obj["page_location"] = "data/slip/slip-table.html"
        obj["pdf_download_link"] = "slip_pdf_download"
        obj["update_obj_link"] = "update_slip"
        obj["drawer_url"] = "slip_drawer"
        obj["new_obj_link"] = "new_slip"
        obj["download_form_url"] = "slips"
        obj["editable_columns"] = {"slip_type": DEFAULT_SLIPS_TYPE_DISPLAY}
        obj["pdf_title"] = "伝票" if lang == "ja" else "Slip"
        obj["default_slip_name"] = "売上伝票" if lang == "ja" else "Sales Slips"
        obj["manage_obj_link"] = "slip_edit"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_CAMPAIGN:
        obj["base_model"] = Post
        obj["custom_model"] = PostNameCustomField
        obj["custom_value_model"] = PostValueCustomField
        obj["custom_value_file_model"] = PostValueFile
        obj["file_upload_to"] = "campaign-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["custom_value_relation"] = "post"
        obj["base_columns"] = DEFAULT_COLUMN_SOCIAL_MEDIA_ALL_POSTS.copy()
        obj["default_columns"] = obj["base_columns"].copy()
        obj["columns_display"] = DISPLAY_COLUMNS_SOCIAL_MEDIA.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = None
        obj["file_name"] = "social_media_list"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "SNS" if lang == "ja" else "Social Media"
        obj["search_fields"] = None
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("calendar", "Calendar", "カレンダー"),
        ]
        obj["related_data"] = []
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CAMPAIGN
        obj["app_target"] = "campaigns"

    # elif object_type == TYPE_OBJECT_EMAIL:
    #     obj['base_model'] = Post
    #     obj['custom_model'] = None
    #     obj['custom_value_model'] = None
    #     obj['custom_value_relation'] = None
    #     obj['base_columns'] = DEFAULT_COLUMN_EMAIL.copy()
    #     obj['default_columns'] = obj['base_columns'].copy()
    #     obj['columns_display'] = DISPLAY_COLUMNS_EMAIL.copy()
    #     obj['reverse_url'] = url_name_by_object_type(object_type)
    #     obj['reverse_query'] = 'studio_type=emails'
    #     obj['id_field'] = None
    #     obj['file_name'] = 'email_list'
    #     obj['file_field'] = None
    #     obj['custom_relation'] = None
    #     obj['page_title'] = 'メール' if lang == 'ja' else 'Emails'
    #     obj['search_fields'] = None
    #     obj['download_formats'] = ['csv']
    #     obj['download_form_url'] = None
    #     obj['view_types'] = [('list', 'Table', 'テーブル')]
    #     obj['related_data'] = []

    elif object_type == TYPE_OBJECT_TASK:
        obj["base_model"] = Task
        obj["custom_model"] = TaskCustomFieldName
        obj["custom_value_model"] = TaskCustomFieldValue
        obj["custom_value_relation"] = "task"
        obj["item_model"] = TaskItem
        obj["field_item_name"] = "task"
        obj["item_related_name"] = "item_task"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["order", "usage_status"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_TASK.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_TASK.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_TASK.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "task_id"
        obj["file_name"] = "task_list"
        obj["file_field"] = None
        obj["custom_relation"] = "task_custom_field_values"
        obj["page_title"] = "タスク" if lang == "ja" else "Tasks"
        obj["search_fields"] = None
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("calendar", "Calendar", "カレンダー"),
            ("gantt_chart", "Gantt Chart", "ガントチャート"),
            (
                "gantt_chart_with_production_line",
                "Gantt Chart (Production Line)",
                "ガントチャート(生産ライン)",
            ),
        ]
        obj["setting_type"] = "task"
        obj["setting_url"] = "workspace_setting"
        obj["editable_columns"] = {"status": TASKS_STATUS_DISPLAY}
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_WORKFLOW:
        obj["base_model"] = Workflow
        obj["custom_model"] = None
        obj["custom_value_model"] = None
        obj["custom_value_relation"] = None
        obj["base_columns"] = DEFAULT_COLUMNS_WORKFLOW.copy()
        obj["default_columns"] = DEFAULT_COLUMNS_WORKFLOW.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_WORKFLOW
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = None
        obj["file_name"] = "task_list"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["page_title"] = "ワークフロー" if lang == "ja" else "Workflows"
        obj["search_fields"] = None
        obj["download_formats"] = None
        obj["download_form_url"] = None
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_INVENTORY:
        obj["base_model"] = ShopTurboInventory
        obj["custom_model"] = ShopTurboInventoryNameCustomField
        obj["custom_value_model"] = ShopTurboInventoryValueCustomField
        obj["custom_value_file_model"] = ShopTurboInventoryValueFile
        obj["custom_value_file_relation"] = "valuecustomfield"
        obj["file_upload_to"] = "shopturbo-inventory-custom-field-files"
        obj["custom_value_relation"] = "inventory"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["currency", "name"], includes=["owner"]
        )
        obj["default_columns"] = obj["base_columns"].copy()
        obj["columns_display"] = INVENTORY_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_INVENTORY
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "inventory_id"
        obj["file_name"] = "inventory_list"
        obj["file_field"] = None
        obj["custom_relation"] = "shopturbo_inventory_custom_field_relations"
        obj["page_title"] = "在庫" if lang == "ja" else "Inventory"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_INVENTORY
        obj["editable_columns"] = {
            "item": "",
            "inventory_status": INVENTORY_STATUS_DISPLAY,
        }
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("forecast", "Inventory Changes", "在庫推移"),
        ]
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_INVENTORY_WAREHOUSE:
        obj["base_model"] = InventoryWarehouse
        obj["custom_model"] = InventoryWarehouseNameCustomField
        obj["custom_value_model"] = InventoryWarehouseValueCustomField
        obj["custom_value_file_model"] = InventoryWarehouseValueFile
        obj["file_upload_to"] = "inventory-warehouse-custom-field-files"
        obj["custom_value_relation"] = "warehouse"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["map_location_id"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_WAREHOUSE.copy()
        obj["columns_display"] = DISPLAY_COLUMNS_WAREHOUSE.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_WAREHOUSE.copy()

        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "id_iw"
        obj["file_name"] = "warehouse_list"
        obj["file_field"] = None
        obj["custom_relation"] = "inventory_warehouse_custom_field_relations"
        obj["page_title"] = "ロケーション" if lang == "ja" else "Locations"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_INVENTORY_WAREHOUSE
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["exclude_custom_types"] = ["warehouse_objects"]
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
        obj["base_model"] = InventoryTransaction
        obj["custom_model"] = InventoryTransactionNameCustomField
        obj["custom_value_model"] = InventoryTransactionValueCustomField
        obj["custom_value_file_model"] = InventoryTransactionValueFile
        obj["file_upload_to"] = "inventory-transaction-custom-field-files"
        obj["custom_value_relation"] = "transaction"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=DEFAULT_COLUMNS_TRANSACTION.copy() + ["usage_status"],
            includes=["owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_TRANSACTION.copy()
        obj["columns_display"] = INVENTORY_TRANSACTION_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_TRANSACTION.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["reverse_query"] = None
        obj["id_field"] = "transaction_id"
        obj["file_name"] = "transaction_list"
        obj["file_field"] = None
        obj["custom_relation"] = "inventory_transaction_custom_field_relations"
        obj["page_title"] = "入出庫" if lang == "ja" else "Transactions"
        obj["search_fields"] = [obj["id_field"], "name"]
        obj["download_formats"] = ["csv"]
        obj["download_form_url"] = None
        obj["setting_url"] = "workspace_setting"
        obj["exclude_custom_types"] = [
            "contact",
            "company",
            "customer",
            "purchase_order",
            "subscription",
            "user_management",
            "bill_objects",
            "invoice_objects",
            "order_objects",
            "task",
            "warehouse_objects",
        ]
        obj["setting_type"] = TYPE_OBJECT_INVENTORY_TRANSACTION
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("stock_ledger", "Stock Ledger - Moving Average", "商品有高帳 - 移動平均"),
            ("fifo_ledger", "Fifo Ledger - FIFO", "商品有高帳 - FIFO"),
        ]
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_TIMEGENIE or object_type == "attendance":
        obj["base_model"] = Track
        obj["custom_model"] = TrackNameCustomField
        obj["custom_value_model"] = TrackValueCustomField
        obj["custom_value_file_model"] = TrackValueFile
        obj["custom_value_relation"] = "track"
        obj["base_columns"] = get_list_object_columns(
            Track, excludes=["created_at", "hours", "minutes", "seconds"]
        )
        obj["default_columns"] = DEFAULT_COLUMN_TIMEGENIE.copy()
        obj["columns_display"] = TIMEGENIE_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = 'timegenie'
        obj["id_field"] = "track_id"
        obj["file_name"] = "attendance"
        obj["file_field"] = None
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = "timegenie_row_detail"
        # obj['download_form_url'] = 'timegenie'
        obj["custom_relation"] = "track_custom_field_value"
        obj["page_title"] = "勤怠" if lang == "ja" else "Attendances"
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = "attendance"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["app_target"] = "attendance"
        obj["file_upload_to"] = "track-customfield-files"

    elif object_type == "item-price":
        obj["base_model"] = ShopTurboItemsPrice

    elif object_type == TYPE_OBJECT_USER_MANAGEMENT:
        obj["base_model"] = UserManagement
        obj["custom_model"] = UserManagementNameCustomField
        obj["custom_value_model"] = UserManagementValueCustomField
        obj["custom_value_file_model"] = UserManagementValueFile
        obj["file_upload_to"] = "user-management-custom-field-files"
        obj["custom_value_relation"] = "usermanagement"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=["type", "view_only_password", "location", "date_from", "date_to"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_USER_MANAGEMENT.copy()
        obj["columns_display"] = USER_MANAGEMENT_COLUMNS_DISPLAY.copy()
        obj["id_field"] = "id_user"
        obj["page_title"] = "ユーザー管理" if lang == "ja" else "User Manager"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_USER_MANAGEMENT
        obj["manage_obj_link"] = "user_management_drawer"
        obj["row_detail_url"] = "user_management_row_detail"
        obj["edit"] = "user_management_edit"
        obj["default_order_name"] = "デフォルト" if lang == "ja" else "Default"
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_WORKER:
        obj["base_model"] = Worker
        obj["custom_model"] = WorkerNameCustomField
        obj["custom_value_model"] = WorkerValueCustomField
        obj["custom_value_file_model"] = WorkerValueFile
        obj["file_upload_to"] = "worker-custom-field-files"
        obj["custom_value_relation"] = "worker"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["created_at", "team"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_WORKER.copy()
        obj["columns_display"] = WORKER_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_worker"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["file_name"] = "employee"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_WORKER
        obj["manage_obj_link"] = "worker_manage"
        obj["row_detail_url"] = "worker_row_detail"
        # obj['editable_columns'] = {'status': THREAD_STATUS_DISPLAY}
        obj["download_formats"] = ["csv"]
        obj["field_item_name"] = "worker"
        obj["custom_relation"] = "worker_custom_field_relations"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_WORKER_REVIEW:
        obj["base_model"] = WorkerReviews
        obj["custom_model"] = WorkerReviewsNameCustomField
        obj["custom_value_model"] = WorkerReviewsValueCustomField
        obj["custom_value_file_model"] = WorkerReviewsValueFile
        obj["file_upload_to"] = "worker-custom-field-files"
        obj["custom_value_relation"] = "worker_review"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=["created_at", "whats_going_well", "areas_of_improvements"],
            includes=["review"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_WORKER_REVIEW.copy()
        obj["columns_display"] = WORKER_REVIEW_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_worker_review"
        obj["file_name"] = "review"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_WORKER_REVIEW
        obj["manage_obj_link"] = "worker_review_manage"
        obj["row_detail_url"] = "worker_review_row_detail"
        obj["download_formats"] = ["csv"]
        # obj['editable_columns'] = {'status': THREAD_STATUS_DISPLAY}
        obj["field_item_name"] = "worker_review"
        obj["custom_relation"] = "worker_review_custom_field_relations"
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_WORKER_ABSENCE:
        obj["base_model"] = Absence
        obj["custom_model"] = AbsenceNameCustomField
        obj["custom_value_model"] = AbsenceValueCustomField
        obj["custom_value_file_model"] = AbsenceValueFile
        obj["file_upload_to"] = "worker-custom-field-files"
        obj["custom_value_relation"] = "absence"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            includes=["approved_by", "requested_by"],
            excludes=["created_at", "requester_name"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ABSENCE.copy()
        obj["columns_display"] = ABSENCE_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id_absence"
        obj["file_name"] = "leaves"
        obj["file_field"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_WORKER_ABSENCE
        obj["manage_obj_link"] = "worker_absence_manage"
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = "worker_absence_detail"
        obj["field_item_name"] = "absence"
        obj["custom_relation"] = "absence_custom_field_relations"
        # obj['editable_columns'] = {'status': THREAD_STATUS_DISPLAY}
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_JOURNAL:
        obj["base_model"] = JournalEntry
        obj["item_model"] = JournalEntryTransaction
        obj["custom_model"] = JournalEntryNameCustomField
        obj["custom_value_model"] = JournalEntryValueCustomField
        obj["custom_value_file_model"] = JournalEntryValueFile
        obj["file_upload_to"] = "journal-custom-field-files"
        obj["custom_value_relation"] = "journal"
        obj["page_title"] = "仕訳" if lang == "ja" else "Journal Entries"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[
                "settle_choice",
                "created_at",
                "type_journal",
                "amount_debit_with_tax",
                "amount_credit_with_tax",
                "currency",
                "settle_journal",
            ],
            includes=["partner", "owner"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_JOURNAL.copy()
        obj["columns_display"] = JOURNAL_COLUMNS_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELD_JOURNAL.copy()
        obj["id_field"] = "id_journal"
        obj["search_fields"] = ["contact__name", "contact__last_name", "company__name"]
        obj["file_name"] = "journal"
        obj["file_field"] = None
        obj["field_item_name"] = "journal"
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_JOURNAL
        obj["manage_obj_link"] = "journal_manage_drawer"
        obj["download_formats"] = ["csv"]
        obj["additional_column_fields"] = ["counter_category", "tax_rate"]
        obj["row_detail_url"] = "journal_row_detail"
        obj["editable_columns"] = {
            "category": JOURNAL_CATEGORY_DISPLAY,
            "counter_category": JOURNAL_COUNTER_CATEGORY_DISPLAY,
            "tax_rate": JOURNAL_TAX_CATEGORY_DISPLAY,
        }
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"
        obj["view_types"] = [
            ("table", "Table", "テーブル"),
            ("balance_sheet", "Balance Sheet", "バランスシート"),
            ("profit_and_loss", "Profit and Loss Sheet", "損益計算書"),
            ("payable_list", "Accounts Payable Balance List", "買掛残高一覧表"),
            ("receivable_list", "Accounts Receivable Balance List", "売掛残高一覧表"),
        ]

    elif object_type == TYPE_OBJECT_FORM:
        obj["base_model"] = Form
        obj["custom_value_relation"] = "formroom"
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["description", "updated_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_FORM.copy()
        obj["columns_display"] = FORM_COLUMNS_DISPLAY.copy()
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_FORM
        obj["manage_obj_link"] = "form_manage"
        # obj['row_detail_url'] = 'worker_row_detail'
        obj["field_item_name"] = "formroom"
        obj["custom_relation"] = "formroom_custom_field_relations"
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_SESSION_EVENT:
        obj["base_model"] = PixelEvent
        obj["custom_model"] = None
        obj["custom_value_model"] = None
        obj["custom_value_relation"] = "valuecustomfield"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["password"], includes=["action"]
        )
        obj["columns_display"] = EVENT_COLUMNS_DISPLAY.copy()

        obj["id_field"] = None
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_SESSION_EVENT
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_JOBS:
        obj["base_model"] = Job
        obj["custom_model"] = JobNameCustomField
        obj["custom_value_model"] = JobValueCustomField
        obj["custom_value_relation"] = "job"

        obj["custom_value_file_model"] = JobValueFile
        obj["file_upload_to"] = "job-custom-field-files"
        obj["custom_value_file_relation"] = "valuecustomfield"

        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"],
            excludes=[],
            includes=["preview", "applications", "interview", "scorecard"],
        )
        obj["default_columns"] = DEFAULT_COLUMNS_JOBS.copy()
        obj["columns_display"] = JOBS_COLUMNS_DISPLAY.copy()

        obj["id_field"] = "job_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = "job_custom_field_relations"
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_JOBS
        obj["editable_columns"] = {
            "status": JOBS_STATUS_DISPLAY,
            "job_type": JOBS_TYPE_DISPLAY,
        }
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_JOBS_APPLICANT:
        obj["base_model"] = JobApplication
        obj["custom_model"] = JobApplicantNameCustomField
        obj["custom_value_model"] = JobApplicantValueCustomField
        obj["custom_value_relation"] = "applicant"

        obj["custom_value_file_model"] = JobApplicantValueFile
        obj["file_upload_to"] = "job-applicant-customfield-files"
        obj["custom_value_file_relation"] = "valuecustomfield"

        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=[], includes=[]
        )
        obj["default_columns"] = DEFAULT_COLUMN_APPLICANT.copy()
        obj["columns_display"] = APPLICANT_COLUMN_DISPLAY.copy()
        obj["editable_columns"] = {
            "email": "",
            "status": "",
            "current_company": "",
            "resume": "",
            "phone_number": "",
            "url": "",
        }

        obj["id_field"] = "job_applicant_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = "applicant_custom_field_relations"
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_JOBS_APPLICANT
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []
        obj["parent_object"] = TYPE_OBJECT_JOBS

    elif object_type == TYPE_OBJECT_PANEL:
        obj["base_model"] = ReportPanel
        obj["custom_model"] = ReportPanelNameCustomField
        obj["custom_value_model"] = ReportPanelValueCustomField
        obj["custom_value_relation"] = "panels"
        obj["view_types"] = [("Table")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=[], includes=["is_deleted", "created_by"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_PANEL.copy()
        obj["columns_display"] = PANEL_COLUMNS_DISPLAY.copy()

        obj["id_field"] = "panel_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_PANEL
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []
        obj["app_target"] = "reports"

    elif object_type == TYPE_OBJECT_DASHBOARD:
        obj["base_model"] = Report
        obj["custom_model"] = ReportNameCustomField
        obj["custom_value_model"] = ReportValueCustomField
        obj["custom_value_relation"] = "reports"
        obj["view_types"] = [("Table")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=[], includes=[]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_DASHBOARD.copy()
        obj["columns_display"] = DASHBOARD_COLUMNS_DISPLAY.copy()

        obj["id_field"] = ""
        obj["file_name"] = None
        obj["file_field"] = None
        obj["field_item_name"] = None
        obj["custom_relation"] = None
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_DASHBOARD
        obj["manage_obj_link"] = ""
        obj["download_formats"] = ["csv"]
        obj["row_detail_url"] = ""
        obj["related_data"] = []
        obj["app_target"] = "reports"
        obj["page_type"] = "dashboards"
        obj["page_title"] = "ダッシュボード" if lang == "ja" else "Dashboards"

    elif object_type == TYPE_OBJECT_CONTRACT:
        obj["base_model"] = ContractDocument
        obj["custom_model"] = ContractDocumentNameCustomField
        obj["custom_value_model"] = ContractDocumentValueCustomField
        obj["custom_value_relation"] = "document"

        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["email_body", "email_sent_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_CONTRACT.copy()
        obj["columns_display"] = CONTRACT_COLUMNS_DISPLAY.copy()

        obj["id_field"] = "contract_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["app_target"] = "shopturbo"
        obj["page_title"] = "契約" if lang == "ja" else "Contract"
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CONTRACT
        obj["editable_columns"] = {
            "signer": JOB_STATUS_DISPLAY,
            "signature": JOB_TYPE_DISPLAY,
        }
        obj["manage_obj_link"] = ""
        obj["row_detail_url"] = ""
        obj["related_data"] = []

    elif object_type == TYPE_OBJECT_CUSTOM_OBJECT:
        obj["custom_object"] = CustomObject
        obj["base_model"] = CustomObjectPropertyRow
        obj["custom_model"] = CustomObjectPropertyName
        obj["custom_value_model"] = CustomObjectPropertyValue
        obj["custom_relation"] = "custom_object_property_row_relations"
        obj["default_columns"] = ["row_id", "usage_status", "created_at", "updated_at"]
        obj["columns_display"] = CUSTOM_OBJECT_COLUMN_DISPLAY.copy()
        obj["custom_value_relation"] = "object"
        obj["id_field"] = "row_id"
        obj["view_types"] = [("list", "Table", "テーブル")]
        obj["setting_url"] = "workspace_setting"

    elif object_type == TYPE_OBJECT_CASE_LINE_ITEM:
        obj["base_model"] = DealsItems
        obj["custom_model"] = DealsItemsNameCustomField
        obj["custom_value_model"] = DealsItemsValueCustomField
        obj["custom_value_relation"] = "item_deal"
        obj["custom_value_file_model"] = ""
        obj["file_upload_to"] = ""
        obj["custom_value_file_relation"] = ""
        obj["custom_value_related_model"] = []
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["product_id", "created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ITEM.copy()
        obj["columns_display"] = ITEMS_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "dealsitemsvaluecustomfield_set"
        obj["page_title"] = "商品" if lang == "ja" else "Items"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("group", "Group Table", "グループテーブル"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_CASE_LINE_ITEM
        obj["editable_columns"] = ["name"]
        obj["required_properties"] = ["name"]
        obj["exclude_custom_types"] = [
            "date_range",
            "bill_objects",
            "purchase_order",
            "part",
        ]
        obj["multiple_custom_types"] = ["components"]
        obj["related_data"] = ["order"]
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_ORDER_LINE_ITEM:
        obj["base_model"] = ShopTurboItemsOrders
        obj["custom_model"] = ShopTurboItemsOrdersNameCustomField
        obj["custom_value_model"] = ShopTurboItemsValueCustomField
        obj["custom_value_relation"] = "shopturbo_item_orders"
        obj["custom_value_file_model"] = ""
        obj["file_upload_to"] = ""
        obj["custom_value_file_relation"] = ""
        obj["custom_value_related_model"] = []
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["product_id", "created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_ITEM.copy()
        obj["columns_display"] = ITEMS_COLUMNS_DISPLAY.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = ""
        obj["page_title"] = "商品" if lang == "ja" else "Items"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
            ("group", "Group Table", "グループテーブル"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_ORDER_LINE_ITEM
        obj["editable_columns"] = ["name"]
        obj["required_properties"] = ["name"]
        obj["exclude_custom_types"] = [
            "date_range",
            "bill_objects",
            "purchase_order",
            "part",
        ]
        obj["multiple_custom_types"] = ["components"]
        obj["related_data"] = ["order"]
        obj["app_target"] = "shopturbo"

    elif object_type == TYPE_OBJECT_COMMERCE_METER:
        obj["base_model"] = CommerceMeter
        obj["custom_model"] = CommerceMeterNameCustomField
        obj["custom_value_model"] = CommerceMeterValueCustomField
        obj["custom_value_relation"] = "meter"
        obj["custom_value_file_model"] = CommerceMeterValueFile
        obj["file_upload_to"] = "commerce-meter-field-files"
        obj["custom_value_file_relation"] = ""
        obj["custom_value_related_model"] = []
        obj["base_columns"] = get_list_object_columns(
            obj["base_model"], excludes=["product_id", "created_at"]
        )
        obj["default_columns"] = DEFAULT_COLUMNS_COMMERCE_METER.copy()
        obj["columns_display"] = COMMERCE_METER_COLUMN_DISPLAY.copy()
        obj["default_form_fields"] = DEFAULT_FORM_FIELDS_COMMERCE_METER.copy()
        # obj['reverse_url'] = url_name_by_object_type(object_type)
        obj["id_field"] = "meter_id"
        obj["file_name"] = None
        obj["file_field"] = None
        obj["custom_relation"] = "commerce_meter_custom_field_relations"
        obj["page_title"] = "メーター" if lang == "ja" else "Meter"
        obj["search_fields"] = [obj["id_field"]]
        obj["download_formats"] = ["csv"]
        obj["view_types"] = [
            ("list", "Table", "テーブル"),
        ]
        obj["setting_url"] = "workspace_setting"
        obj["setting_type"] = TYPE_OBJECT_COMMERCE_METER
        obj["editable_columns"] = []
        obj["required_properties"] = []
        obj["exclude_custom_types"] = []
        obj["multiple_custom_types"] = []
        obj["related_data"] = []
        obj["app_target"] = "shopturbo"

    # Add additional Column display (Especially in commerce)
    if obj["additional_columns_display"]:
        obj["columns_display"].update(obj["additional_columns_display"])
    if not obj["item_related_name"] and obj["item_model"]:
        for field in obj["item_model"]._meta.fields:
            try:
                if field.related_model == obj["base_model"]._meta.model:
                    obj["item_related_name"] = field._related_name
                    break
            except Exception as e:
                print("ERROR: ", e)
    return obj


def get_list_object_columns(model, excludes=[], includes=[]):
    """Get columns of a model. Excludes ForeignKey, UUIDField, JSONField, ManyToManyField, FileField, BooleanField types"""
    columns = []

    for field in model._meta.get_fields():
        field_type = field.get_internal_type()
        if (
            field_type
            in [
                "ForeignKey",
                "UUIDField",
                "JSONField",
                "ManyToManyField",
                "FileField",
                "BooleanField",
            ]
            or field.name in excludes
        ):
            continue
        columns.append(field.name)

    if len(includes) > 0:
        columns.extend(includes)

    return columns


def get_property_type(property, default_properties_dict, model):
    if property in default_properties_dict.keys():
        property_type = default_properties_dict[property].lower()

        if any(item in property_type for item in ["integer", "float"]):
            return "number"
        elif any(item in property_type for item in ["date"]):
            return "date_time"
        elif model._meta.get_field(property).choices:
            return "choice"
        elif "image_" in property:
            return "profile_pic"
        # Association
        elif any(item in property_type for item in ["foreignkey", "onetoonefield"]):
            if property == "user":
                return "user"
            return "association"
    elif property == "owner":
        return "user"
    elif property == "customer":
        return "association"

    # === Purchase Order
    elif property == "supplier":
        return "association"
    elif property == "item_price":
        return "item_price"
    elif property == "item_price_without_tax":
        return "item_price_without_tax"
    # =====

    elif property in ["items", "item", "source_item"]:
        return "items"
    elif property == "durations":
        return "date_time"
    elif property in [
        "item-price",
        "line_item_price",
        "line_item_price_without_tax",
        "line_item_quantity",
    ]:
        return "number"
    elif property == "line_item":
        return "line_item"

    # Accounting
    elif property == "counter_category":
        return "choice"
    elif property == "tax_rate":
        return "choice"

    elif property == "sub_tasks":
        return "task_object"

    # Override related Inventory
    elif property in [
        "inventory__total_inventory",
        "inventory__available_amount",
        "inventory__unavailable_amount",
        "inventory__committed_amount",
    ]:
        return "number"

    # Override Association
    elif property in [
        "journal_entry",
        "journalentry",
        "customers",
        "inventory_transactions",
        "associate#purchase_orders",
        "partner",
        "invoice",
        "estimate",
        "orders",
        "item_name",
        TYPE_OBJECT_INVOICE,
        TYPE_OBJECT_ESTIMATE,
        "tasks",
        "assignee",
        TYPE_OBJECT_SUBSCRIPTION,
        TYPE_OBJECT_PURCHASE_ORDER,
        TYPE_OBJECT_CASE,
        TYPE_OBJECT_DELIVERY_NOTE,
        TYPE_OBJECT_ORDER,
        TYPE_OBJECT_INVENTORY,
        TYPE_OBJECT_INVENTORY_TRANSACTION,
        TYPE_OBJECT_TASK,
        TYPE_OBJECT_RECEIPT,
        TYPE_OBJECT_EXPENSE,
        TYPE_OBJECT_COMPANY,
        TYPE_OBJECT_CONTACT,
        TYPE_OBJECT_BILL,
        TYPE_OBJECT_ITEM,
        TYPE_OBJECT_INVENTORY_WAREHOUSE,
    ]:
        return "association"

    return "string"


def get_object_display_based_columns(page_group_type, object, columns, timezone, lang):
    """
    Get display string for an object based on its columns.

    Args:
        page_group_type (str): Type of page group
        object (Model or dict): Django model instance or dictionary representation
        columns (list): List of column names to include in display
        timezone (str): Timezone string
        lang (str): Language code

    Returns:
        str: Formatted display string containing values from specified columns
    """
    res = ""
    if page_group_type == TYPE_OBJECT_ITEM:
        if not object:  # Add this check
            return res  # Return early if object is None

        # Check if object is a dictionary (from model_to_dict) instead of a model instance
        is_dict = isinstance(object, dict)

        if not is_dict:
            # Handle ShopTurboItemsOrders objects that might be passed with TYPE_OBJECT_ITEM
            if object.__class__.__name__ == "ShopTurboItemsOrders":
                # For ShopTurboItemsOrders, we should display the item information if available
                if object.item:
                    # Recursively call with the actual item
                    return get_object_display_based_columns(
                        page_group_type, object.item, columns, timezone, lang
                    )
                else:
                    # If no item, use custom_item_name
                    return object.custom_item_name or str(object.id)

        # Get object ID for debugging - handle both dict and model instance
        object_id = object.get("id") if is_dict else getattr(object, "id", "unknown")
        print(
            f"[DEBUG] get_object_display_based_columns for item {object_id} with columns: {columns}"
        )
        print(f"[DEBUG] Object type: {'dict' if is_dict else type(object).__name__}")

        for col in columns:
            print(f"[DEBUG] Processing column: '{col}'")
            try:
                if col == "item_id":
                    if object:
                        # Handle both dict and model instance
                        item_id = (
                            object.get("item_id")
                            if is_dict
                            else getattr(object, "item_id", None)
                        )
                        if item_id is not None:
                            res += f"#{safe_format_id(item_id)} "
                elif col == "name":
                    try:
                        if not is_dict:
                            instance = type(object)
                            # Handle objects that don't have workspace attribute directly
                            workspace = None
                            if hasattr(object, "workspace"):
                                workspace = object.workspace
                            elif hasattr(object, "order") and hasattr(
                                object.order, "workspace"
                            ):
                                workspace = object.order.workspace

                            if workspace:
                                cp = CustomProperty.objects.get(
                                    workspace=workspace,
                                    model=instance._meta.db_table,
                                    name=col,
                                )
                                val = ast.literal_eval(cp.value)
                                if hasattr(object, "name") and object.name:
                                    res += object.name[: val["max_text_length"]] + " "
                        else:
                            # Handle dictionary case - just get the name value
                            name = object.get("name")
                            if name:
                                res += str(name) + " "
                    except:
                        # Fallback for both dict and model instance
                        name = (
                            object.get("name")
                            if is_dict
                            else getattr(object, "name", None)
                        )
                        if name:
                            res += str(name) + " "
                elif col == "platform":
                    if object:
                        platform = (
                            object.get("platform")
                            if is_dict
                            else getattr(object, "platform", None)
                        )
                        if platform:
                            res += f"{platform} "
                elif col == "status":
                    if is_dict:
                        status = object.get("status")
                        if status:
                            res += f"{status} "
                    else:
                        res += f"{object.get_status_display()} "
                elif col == "description":
                    description = (
                        object.get("description")
                        if is_dict
                        else getattr(object, "description", None)
                    )
                    if description:
                        res += f"{str(description)[:25]} "
                elif col == "price":
                    if is_dict:
                        # For dictionaries, we can't easily get related price data
                        # Just skip or use a default value
                        currency = object.get("currency", "")
                        res += f"{currency} 0 "
                    else:
                        currency = object.currency
                        try:
                            symbol = CurrencySymbols.get_symbol(object.currency)
                            if symbol is None:
                                currency = symbol
                        except:
                            pass
                        price = 0
                        item_price = ShopTurboItemsPrice.objects.filter(
                            item=object, default=True
                        ).first()
                        if item_price:
                            price = getattr(item_price, "price", 0)
                        res += f"{currency} {price} "
                elif col == "purchase_price":
                    purchase_price = (
                        object.get("purchase_price")
                        if is_dict
                        else getattr(object, "purchase_price", None)
                    )
                    if purchase_price:
                        currency = (
                            object.get("currency")
                            if is_dict
                            else getattr(object, "currency", "")
                        )
                        if not is_dict:
                            try:
                                symbol = CurrencySymbols.get_symbol(object.currency)
                                if symbol is None:
                                    currency = symbol
                            except:
                                pass
                        res += f"{currency} {purchase_price} "
                elif col == "tax":
                    if is_dict:
                        # For dictionaries, we can't easily get related price data
                        res += "0 % "
                    else:
                        tax = 0
                        item_price = ShopTurboItemsPrice.objects.filter(
                            item=object, default=True
                        ).first()
                        if item_price:
                            tax = getattr(item_price, "tax", 0)
                        res += f"{tax} % "
                elif "-item id" in col.lower() and col.startswith("item_platform|"):
                    if not is_dict:
                        platform_name, _ = (
                            col.lower().replace("item_platform|", "").split("-item id")
                        )
                        try:
                            res += f"{object.get_display_name()[platform_name]} "
                        except Exception:
                            pass
                elif "-sku" in col.lower() and col.startswith("item_platform|"):
                    if not is_dict:
                        platform_name, _ = (
                            col.lower().replace("item_platform|", "").split("-sku")
                        )
                        try:
                            res += f"{object.get_platform_sku()[platform_name]} "
                        except Exception:
                            pass
                elif "inventory__" in col.lower():
                    if not is_dict:
                        # Map inventory column names to inventory types
                        inventory_col_mapping = {
                            "inventory__total_inventory": "total",
                            "inventory__available_amount": "available",
                            "inventory__unavailable_amount": "unavailable",
                            "inventory__committed_amount": "committed",
                        }

                        inventory_col = inventory_col_mapping.get(col.lower())
                        if inventory_col:
                            try:
                                inventory_amount = item_inventory_amount(
                                    object, inventory_type=inventory_col
                                )
                            except Exception:
                                inventory_amount = None

                            # Add inventory amount if it's not None
                            if inventory_amount is not None:
                                res += f"({inventory_amount}) "
                elif "platform_ids" in col.lower():
                    if not is_dict:
                        platforms_ids = []
                        platforms = object.item.filter(platform_type="default")
                        for platform in platforms:
                            platforms_ids.append(platform.platform_id)
                        res += " - ".join(platforms_ids) + " "
                elif "| child" in col.lower():
                    if not is_dict:
                        obj_list = []
                        property_values = object.property_child_item.all().order_by(
                            "-created_at"
                        )
                        for property_value in property_values:
                            if property_value.items not in obj_list:
                                obj_list.append(property_value.items)
                        res += " ".join(obj_list) + " "
                elif col == "updated_at":
                    updated_at = (
                        object.get("updated_at")
                        if is_dict
                        else getattr(object, "updated_at", None)
                    )
                    if updated_at:
                        if is_dict and isinstance(updated_at, str):
                            # If it's a string from dict, just display it
                            res += f"{updated_at} "
                        elif hasattr(updated_at, "astimezone"):
                            res += f"{updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "created_at":
                    created_at = (
                        object.get("created_at")
                        if is_dict
                        else getattr(object, "created_at", None)
                    )
                    if created_at:
                        if is_dict and isinstance(created_at, str):
                            # If it's a string from dict, just display it
                            res += f"{created_at} "
                        elif hasattr(created_at, "astimezone"):
                            res += f"{created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                else:
                    # Custom Property - only works with model instances, not dictionaries
                    if not is_dict:
                        custom_prop_val = (
                            object.shopturbo_item_custom_field_relations.filter(
                                field_name__id=str(col)
                            ).first()
                        )
                        if not custom_prop_val:
                            continue
                        res += custom_prop_display_for_object(
                            custom_prop_val, timezone, lang
                        )

            except Exception as e:
                print(f"[DEBUG] Error processing column '{col}': {e}")
                traceback.print_exc()
                # Don't send Discord notification for dict vs object issues - they're expected now
                if not is_dict:
                    DiscordNotification().send_message(
                        f"Error in get_object_display_based_columns: {traceback.format_exc()}"
                    )
                continue  # Continue processing other columns instead of returning early
    elif page_group_type == TYPE_OBJECT_INVENTORY:
        res = ""
        for col in columns:
            if col == "inventory_id":
                res += f"#{safe_format_id(object.inventory_id)} "
            elif col == "items":
                items = []
                for val in object.item.all():
                    items.append(f"#{safe_format_id(val.item_id)} {val.name}")
                items_str = ",".join(items)
                res += f"{items_str} "
            elif col == "warehouse":
                if object.warehouse and object.warehouse.location:
                    res += f"{object.warehouse.location} "
            elif col == "status":
                res += f"{object.get_status_display()} "
            elif col in ["available", "committed", "unavailable", "total_inventory"]:
                res += f"{get_and_update_inventory_amount(object)} "
            elif col == "inventory_value":
                try:
                    res += f"{int(object.inventory_value)} "
                except:
                    continue
            elif col == "updated_at":
                res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "created_at":
                res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "date":
                if object.date:
                    res += f"{object.date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')} "
            elif "|" in col:
                parent_object, col_ = col.split("|")
                if parent_object != TYPE_OBJECT_ITEM:
                    continue
                object_ = object.item.first()
                res += f"{get_object_display_based_columns(parent_object, object_, [col_], timezone, lang)} "
            else:
                # Custom Property
                try:
                    custom_prop_val = (
                        object.shopturbo_inventory_custom_field_relations.filter(
                            field_name__id=str(col)
                        ).first()
                    )
                except:
                    continue
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
        res = ""
        for col in columns:
            if col == "transaction_id":
                res += f"#{safe_format_id(object.transaction_id)} "
            elif col == "inventory":
                try:
                    # Handle objects that don't have workspace attribute directly
                    workspace = None
                    if hasattr(object, "workspace"):
                        workspace = object.workspace
                    elif hasattr(object, "order") and hasattr(
                        object.order, "workspace"
                    ):
                        workspace = object.order.workspace
                    elif hasattr(object, "inventory") and hasattr(
                        object.inventory, "workspace"
                    ):
                        workspace = object.inventory.workspace

                    if workspace:
                        om, _ = ObjectManager.objects.get_or_create(
                            workspace=workspace, page_group_type=TYPE_OBJECT_INVENTORY
                        )
                        columns_ = om.column_display.split(",")
                        inventory_str = get_object_display_based_columns(
                            TYPE_OBJECT_INVENTORY,
                            object.inventory,
                            columns_,
                            timezone,
                            lang,
                        )
                    else:
                        inventory_str = ""
                except:
                    inventory_str = ""
                res += f"{inventory_str} "
            elif col == "inventory_type":
                try:
                    res += f"{INVENTORY_TRANSACTION_COLUMNS_DISPLAY[object.inventory.inventory_status]} "
                except:
                    res += f"{object.inventory.inventory_status} "
            elif col == "amount":
                res += f"{object.amount} "
            elif col == "transaction_amount":
                res += f"{object.transaction_amount} "
            elif col == "user":
                res += f"{object.user.first_name} "
            elif col == "transaction_type":
                res += f"{object.get_transaction_type_display()} "
            elif col == "updated_at":
                res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "created_at":
                res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "transaction_date":
                res += f"{object.transaction_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif "|" in col:
                parent_object, col_ = col.split("|")
                if parent_object == TYPE_OBJECT_ITEM:
                    object_ = object.inventory.item.first()
                    res += f"{get_object_display_based_columns(parent_object, object_, [col_], timezone, lang)} "
                elif parent_object == TYPE_OBJECT_INVENTORY:
                    object_ = object.inventory
                    res += f"{get_object_display_based_columns(parent_object, object_, [col_], timezone, lang)} "
            else:
                # Custom Property
                custom_prop_val = (
                    object.inventory_transaction_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                )
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_EXPENSE:
        res = ""
        for col in columns:
            if col == "id_pm":
                res += f"#{safe_format_id(object.id_pm)} "
            elif col == "partner":
                if object.contact:
                    res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                elif object.company:
                    res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
            elif col == "submitter__first_name":
                if object.submitter:
                    res += f"{object.submitter.first_name} "
            elif col == "status":
                res += f"{object.status} "
            elif col == "amount":
                res += f"{object.amount} "
            elif col == "due_date":
                res += f"{object.due_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "description":
                res += f"{object.description} "
            else:
                # Custom Property
                custom_prop_val = object.expense_custom_field_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_ORDER:
        res = ""
        for col in columns:
            if col == "order_id":
                res += f"#{safe_format_id(object.order_id)} "
            else:
                # Custom Property
                custom_prop_val = object.shopturbo_custom_field_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_SUBSCRIPTION:
        res = ""
        for col in columns:
            if col == "subscriptions_id":
                res += f"#{safe_format_id(object.subscriptions_id)} "
            elif col == "item_name":
                items = []
                for val in object.shopturboitemssubscriptions_set.all():
                    if val.item:
                        items.append(
                            f"#{safe_format_id(val.item.item_id)} {val.item.name}"
                        )
                    else:
                        items.append(val.custom_item_name)
                items_str = ",".join(items)
                res += f"{items_str} "
            elif col == "customer":
                if object.contact:
                    res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                elif object.company:
                    res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
            elif col == "durations":
                if object.start_date and object.end_date:
                    res += f"{object.start_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')} - {object.end_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')}"
                else:
                    until = "Indefinitely"
                    if lang == "ja":
                        until = "無期限"
                    res += f"{object.start_date.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')} - {until}"
            elif col == "total_price":
                currency = object.currency
                try:
                    symbol = CurrencySymbols.get_symbol(object.currency)
                    if symbol is None:
                        currency = symbol
                except:
                    pass
                res += f"{currency} {object.total_price} "
                res += f"{object}"
            elif col == "created_at":
                res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "subscription_status":
                if object.subscription_status:
                    subscription_status = translate_language(
                        object.subscription_status, lang
                    )
                    res += f"{subscription_status}"
            elif col == "tax":
                res += f"{object.tax_rate} %"
            else:
                # Custom Property
                custom_prop_val = (
                    object.shopturbo_subscriptions_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                )
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_INVOICE:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_inv":
                    res += f"#{safe_format_id(object.id_inv)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "due_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.due_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.due_date.strftime('%B %d, %Y')} "
                elif col == "status":
                    res += f"{object.get_status_display()} "
                elif col == "total_price":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price:
                        res += f"{currency} {object.total_price:,.2f} "
                    else:
                        res += f"{currency} {object.total_price} "
                elif col == "total_price_without_tax":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price_without_tax:
                        res += f"{currency} {object.total_price_without_tax:,.2f} "
                    else:
                        res += f"{currency} {object.total_price_without_tax} "
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col == "discount":
                    res += f"{object.discount} % "
                elif col == "discount_option":
                    res += f"{object.discount_option} "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = object.invoice_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_ESTIMATE:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_est":
                    res += f"#{safe_format_id(object.id_est)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "due_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.due_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.due_date.strftime('%B %d, %Y')} "
                elif col == "status":
                    res += f"{object.get_status_display()} "
                elif col == "total_price":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price:
                        res += f"{currency} {object.total_price:,.2f} "
                    else:
                        res += f"{currency} {object.total_price} "
                elif col == "total_price_without_tax":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price_without_tax:
                        res += f"{currency} {object.total_price_without_tax:,.2f} "
                    else:
                        res += f"{currency} {object.total_price_without_tax} "
                elif col == "usage_status":
                    res += f"{object.get_usage_status_display()} "
                elif col == "invoice":
                    pass
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col == "discount":
                    res += f"{object.discount} % "
                elif col == "discount_option":
                    res += f"{object.discount_option} "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = object.estimate_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_RECEIPT:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_rcp":
                    res += f"#{safe_format_id(object.id_rcp)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "status":
                    res += f"{object.get_status_display()} "
                elif col == "total_price":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price:
                        res += f"{currency} {object.total_price:,.2f} "
                    else:
                        res += f"{currency} {object.total_price} "
                elif col == "total_price_without_tax":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price_without_tax:
                        res += f"{currency} {object.total_price_without_tax:,.2f} "
                    else:
                        res += f"{currency} {object.total_price_without_tax} "
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = object.receipt_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_DELIVERY_NOTE:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_ds":
                    res += f"#{safe_format_id(object.id_ds)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "status":
                    res += f"{object.get_status_display()} "
                elif col == "total_price":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price:
                        res += f"{currency} {object.total_price:,.2f} "
                    else:
                        res += f"{currency} {object.total_price} "
                elif col == "total_price_without_tax":
                    currency = object.currency
                    try:
                        symbol = CurrencySymbols.get_symbol(object.currency)
                        if symbol is None:
                            currency = symbol
                    except:
                        pass
                    if object.total_price_without_tax:
                        res += f"{currency} {object.total_price_without_tax:,.2f} "
                    else:
                        res += f"{currency} {object.total_price_without_tax} "
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = (
                        object.delivery_slip_custom_field_relations.filter(
                            field_name__id=str(col)
                        ).first()
                    )
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_SLIP:
        res = ""
        for col in columns:
            try:
                if res != "":
                    res += "- "

                if col == "id_slip":
                    res += f"#{safe_format_id(object.id_slip)} "
                elif col == "customers":
                    if object.contact:
                        res += f"#{safe_format_id(object.contact.contact_id)} - {object.contact.name} "
                    elif object.company:
                        res += f"#{safe_format_id(object.company.company_id)} - {object.company.name} "
                elif col == "slip_type":
                    res += f"{object.get_slip_type_display()} "
                elif col == "start_date":
                    if lang == "ja":
                        # locale.setlocale(locale.LC_ALL, 'ja_JP.UTF-8')
                        res += f"{object.start_date.strftime('%Y年%m月%d日')} "
                    else:
                        # locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
                        res += f"{object.start_date.strftime('%B %d, %Y')} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "send_from":
                    res += f"{object.send_from} "
                elif col == "created_at":
                    res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "journal_entry":
                    res += f"{object.journal_entry} "
                elif col == "updated_at":
                    res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
                elif col == "currency":
                    res += f"{object.currency} "
                elif col == "notes":
                    res += f"{object.notes} "
                elif col == "tax_rate":
                    res += f"{object.tax_rate} % "
                elif col in [
                    "customers__company__name",
                    "customers__contact__name",
                    "customers__contact__first_name",
                    "customers__contact__last_name",
                ]:
                    if object.contact:
                        res += f"{object.contact.name} "
                    elif object.company:
                        res += f"{object.company.name} "
                else:
                    # Custom Property
                    custom_prop_val = object.slip_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
            except:
                pass
    elif page_group_type == TYPE_OBJECT_INVENTORY_WAREHOUSE:
        res = ""
        for col in columns:
            if col == "id_iw":
                res += f"#{safe_format_id(object.id_iw)} "
            else:
                if is_valid_uuid(str(col)):
                    # Custom Property
                    custom_prop_val = (
                        object.inventory_warehouse_custom_field_relations.filter(
                            field_name__id=str(col)
                        ).first()
                    )
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
                else:
                    res += getattr(object, str(col))

    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
        res = ""
        for col in columns:
            if col == "id_po":
                res += f"#{safe_format_id(object.id_po)} "
            else:
                # Custom Property
                custom_prop_val = object.purchase_order_field_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    elif page_group_type == TYPE_OBJECT_BILL:
        res = ""
        for col in columns:
            if col == "id_bill":
                res += f"#{safe_format_id(object.id_bill)} "
            else:
                if is_valid_uuid(str(col)):
                    # Custom Property
                    custom_prop_val = object.bill_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
                else:
                    res += getattr(object, str(col))

    elif page_group_type == TYPE_OBJECT_CASE:
        res = ""
        for col in columns:
            if col == "deal_id":
                res += f"#{safe_format_id(object.deal_id)} "
            elif col == "name":
                res += f"{object.name} "
            elif col == "customer":
                if object.contact:
                    if object.contact.first():
                        res += object.contact.first().name
                elif object.company:
                    if object.company.first():
                        res += object.company.first().name
            elif col == "updated_at":
                res += f"{object.updated_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            elif col == "created_at":
                res += f"{object.created_at.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
            else:
                if is_valid_uuid(str(col)):
                    # Custom Property
                    custom_prop_val = object.deals_custom_field_relations.filter(
                        field_name__id=str(col)
                    ).first()
                    if not custom_prop_val:
                        continue
                    res += custom_prop_display_for_object(
                        custom_prop_val, timezone, lang
                    )
                else:
                    res += getattr(object, str(col))

    elif page_group_type == TYPE_OBJECT_JOURNAL:
        res = ""
        for col in columns:
            if col == "id_journal":
                res += f"#{safe_format_id(object.id_journal)} "
            else:
                # Custom Property
                custom_prop_val = object.journal_custom_field_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)

    elif page_group_type == TYPE_OBJECT_CUSTOM_OBJECT:
        res = ""
        for col in columns:
            if col == "row_id":
                res += f"#{safe_format_id(object.row_id)} "
            else:
                # Custom Property
                custom_prop_val = object.custom_object_property_row_relations.filter(
                    field_name__id=str(col)
                ).first()
                if not custom_prop_val:
                    continue
                res += custom_prop_display_for_object(custom_prop_val, timezone, lang)
    return res


def custom_prop_display_for_object(custom_prop_val, timezone, lang):
    """
    Formats the display value for a custom property based on its type and format settings.

    Args:
        custom_prop_val: The custom property value object containing field name, value and metadata
        timezone: The timezone to use for formatting dates/times
        lang: The language code (e.g. 'en', 'ja') for localization

    Returns:
        str: The formatted display string for the custom property value
    """
    res = ""
    if custom_prop_val.field_name.type == "number":
        if custom_prop_val.field_name.number_format == "%":
            res = f"{custom_prop_val.value} {custom_prop_val.field_name.number_format.upper()} "
        elif custom_prop_val.field_name.number_format == "number":
            res = f"{custom_prop_val.value}"
        else:
            res = f"{custom_prop_val.field_name.number_format.upper()} {custom_prop_val.value} "
    elif custom_prop_val.field_name.type == "date":
        res = f"{custom_prop_val.value_time.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y')} "
    elif custom_prop_val.field_name.type == "date_time":
        res = f"{custom_prop_val.value_time.astimezone(pytz.timezone(timezone)).strftime('%B %d, %Y, %I:%M %p')} "
    elif custom_prop_val.field_name.type == "choice":
        try:
            for choice in ast.literal_eval(custom_prop_val.field_name.choice_value):
                if (
                    "value" in choice
                    and "label" in choice
                    and choice["value"] == custom_prop_val.value
                ):
                    res = f"{choice['label']} "
        except Exception as e:
            print(e)
    elif custom_prop_val.field_name.type == "components":
        for component in custom_prop_val.components.all():
            res = f"{safe_format_id(component.item_component.item_id)}-{component.item_component.name}"
    elif custom_prop_val.field_name.type == "tag":
        try:
            for tag in json.loads(custom_prop_val.value):
                res = f"{tag['value']} "
        except:
            pass
    elif custom_prop_val.field_name.type == "user":
        user = User.objects.filter(id=custom_prop_val.value).first()
        if user:
            res = f"{user.first_name} "
    elif custom_prop_val.field_name.type == "contact":
        contact = Contact.objects.filter(id=custom_prop_val.value).first()
        if contact:
            try:
                full_name = contact.name
                if contact.last_name:
                    if lang == "ja":
                        full_name = contact.last_name + " " + full_name
                    else:
                        full_name = full_name + " " + contact.last_name
            except:
                full_name = ""
            res = f"#{safe_format_id(contact.contact_id)} - {full_name} "
    elif custom_prop_val.field_name.type == "company":
        company = Company.objects.filter(id=custom_prop_val.value).first()
        if company:
            res = f"#{safe_format_id(company.company_id)}-{company.name} "
    elif custom_prop_val.field_name.type == "invoice_objects":
        invoice = Invoice.objects.filter(id=custom_prop_val.value).first()
        if invoice:
            res = f"#{safe_format_id(invoice.id_inv)}"
    elif custom_prop_val.field_name.type in ["image", "svg"]:
        pass
    else:
        res = f"{custom_prop_val.value} "
    return res


def property_display(arg, lang, workspace):
    try:
        # print(f"DEBUG PROPERTY_DISPLAY: Input arg: {arg}")
        args = arg.split("|")
        # print(f"DEBUG PROPERTY_DISPLAY: Split args: {args}")
        row_type = args[0].lower()
        page = args[1]
        item_id = None
        if len(args) > 2:
            item_id = args[2]
        # print(
        #     f"DEBUG PROPERTY_DISPLAY: row_type={row_type}, page={page}, item_id={item_id}")
        if row_type == "shipping_info":
            row_type = args[1]
            page = args[-1]
        if row_type == "line_item":
            row_type = args[0]
            page = args[-1]
        if page == "message_thread":
            page = TYPE_OBJECT_CONVERSATION
        if "_platform|" in arg:
            if is_valid_uuid(arg.split("|")[1][:36]):
                page = args[2]
                row_type = (args[0] + "|" + args[1]).lower()

        # association Label
        if is_valid_uuid(row_type):
            association_label = AssociationLabel.objects.filter(
                id=row_type, workspace=workspace
            ).first()
            if association_label:
                row_type = association_label.label
                return {
                    "name": row_type,
                    "id": str(association_label.id),
                    "value": "",
                    "type": "association",
                }
        else:
            association_labels = AssociationLabel.objects.filter(
                workspace=workspace, object_source=page
            )
            if association_labels.filter(label__iexact=row_type).exists():
                association_label = association_labels.filter(
                    label__iexact=row_type
                ).first()  # Fix return more than 1
                row_type = association_label.label

        page_obj = get_page_object(page, lang)
        custom_model = page_obj["custom_model"]
        value_model = page_obj["custom_value_model"]
        columns = page_obj["columns_display"]
        base_model = page_obj["base_model"]
        customfield_name = None
        associate = None
        result = {"name": row_type, "id": row_type, "value": "", "type": ""}

        if row_type:
            if is_valid_uuid(row_type):
                customfield_name = custom_model.objects.filter(
                    id=row_type, workspace=workspace
                ).first()

        if customfield_name or associate:
            if associate:
                if associate.case_associate:
                    attr = getattr(associate, "case_associate", None)
                    if attr:
                        if lang == "ja":
                            row_name = f"{attr.name} (アソシエーション)"
                        else:
                            row_name = f"{attr.name} (Association Object)"
                        result["name"] = row_name
                        result["id"] = associate.id

            if customfield_name:
                result["name"] = customfield_name.name
                result["id"] = str(customfield_name.id)
                result["type"] = customfield_name.type

                if customfield_name.type == "components":
                    if item_id == "amount":
                        if lang == "ja":
                            result["name"] += " | 構成数量"
                        else:
                            result["name"] += " | Component Quantity"
                elif customfield_name.type == "shipping_info":
                    try:
                        sub_property = args[3]
                        if sub_property:
                            result["name"] = (
                                customfield_name.name + " - " + sub_property
                            )
                    except:
                        pass
                elif item_id:
                    try:
                        if page == TYPE_OBJECT_BILL:
                            filter_condition = Q(bill__id=item_id)
                        else:
                            filter_condition = Q(
                                **{page_obj["custom_value_relation"] + "__id": item_id}
                            )
                    except:  # Error happening for other app if just need value of custom field , I add this to handle that Cc: Khan
                        obj = base_model.objects.filter(id=item_id).last()
                        str_base_model = str(obj).lower().split(" ")[0]
                        filter_condition = Q(**{str_base_model: obj})

                    customfield_value = value_model.objects.filter(
                        Q(field_name=customfield_name) & filter_condition
                    ).last()

                    if customfield_value:
                        if customfield_name.type == "image":
                            if customfield_value.file:
                                result["value"] = customfield_value.file.url
                        elif customfield_name.type == "contact":
                            if is_valid_uuid(customfield_value.value):
                                try:
                                    contact = Contact.objects.filter(
                                        id=customfield_value.value
                                    ).first()
                                    if contact:
                                        if lang == "ja":
                                            result["value"] = (
                                                f"#{'%04d' % contact.contact_id} "
                                            )
                                            if contact.last_name:
                                                result["value"] += (
                                                    f"{contact.last_name} "
                                                )
                                            if contact.name:
                                                result["value"] += f"{contact.name}"
                                        else:
                                            result["value"] = (
                                                f"#{'%04d' % contact.contact_id} {contact.name}"
                                            )
                                            if contact.last_name:
                                                result["value"] += (
                                                    f" {contact.last_name}"
                                                )
                                        result["contact_id"] += f" {contact.id}"
                                except:
                                    pass
                        elif customfield_name.type == "item":
                            if is_valid_uuid(customfield_value.value):
                                try:
                                    item = ShopTurboItems.objects.filter(
                                        id=customfield_value.value
                                    ).first()
                                    if item:
                                        if item.price:
                                            if item.currency == "¥":
                                                price = f"{item.currency} {'%0g' % item.price}"
                                            else:
                                                price = f"{item.currency} {'%2g' % item.price}"
                                            result["value"] = (
                                                f"#{'%04d' % item.item_id} {item.name} - {price}"
                                            )
                                        else:
                                            itemsprice = (
                                                ShopTurboItemsPrice.objects.filter(
                                                    item=item, default=True
                                                ).first()
                                            )
                                            if itemsprice.price:
                                                if itemsprice.currency == "¥":
                                                    price = f"{itemsprice.currency} {'%0g' % itemsprice.price}"
                                                else:
                                                    price = f"{itemsprice.currency} {'%2g' % itemsprice.price}"
                                                result["value"] = (
                                                    f"#{'%04d' % item.item_id} {item.name} - {price}"
                                                )
                                            else:
                                                result["value"] = (
                                                    f"#{'%04d' % item.item_id} {item.name}"
                                                )
                                except:
                                    pass
                        elif customfield_name.type == "purchase_item":
                            if is_valid_uuid(customfield_value.value):
                                purchase_item = PurchaseItems.objects.filter(
                                    id=customfield_value.value
                                ).first()
                                if purchase_item:
                                    if purchase_item.amount:
                                        if purchase_item.currency == "¥":
                                            price = f"{purchase_item.currency} {'%0g' % float(purchase_item.amount)}"
                                        else:
                                            price = f"{purchase_item.currency} {'%2g' % float(purchase_item.amount)}"
                                        print(purchase_item.id_item)
                                        result["value"] = (
                                            f"#{'%04d' % int(purchase_item.id_item)} {purchase_item.name} - {price}"
                                        )
                                    else:
                                        result["value"] = (
                                            f"#{'%04d' % int(purchase_item.id_item)} {purchase_item.name}"
                                        )
                        elif customfield_name.type == "file":
                            try:
                                result["value"] = customfield_value.file.url
                            except:
                                pass
                        elif customfield_name.type in ["date", "date_time"]:
                            try:
                                result["value"] = customfield_value.value_time
                            except:
                                pass
                        elif customfield_name.type == "choice":
                            try:
                                choices = ast.literal_eval(
                                    customfield_name.choice_value
                                )
                                for choice in choices:
                                    if choice["value"] == customfield_value.value:
                                        result["value"] = (
                                            choice["label"]
                                            if "label" in choice
                                            else choice["value"]
                                        )
                                        if "color" in choice:
                                            result["color"] = (
                                                choice["color"]
                                                if "color" in choice
                                                else "#000000"
                                            )

                                if page in [TYPE_OBJECT_ORDER,TYPE_OBJECT_CONTACT,TYPE_OBJECT_INVENTORY,TYPE_OBJECT_CASE]:
                                    result["value"] = customfield_value.value
                                    result["customfield_name"] = customfield_name
                                    result["customfield_value"] = customfield_value
                            except:
                                pass
                        else:
                            result["value"] = customfield_value.value

                elif row_type in ["customer", "contact", "company"]:
                    customer_result = get_order_customer_custom_property(
                        args[:3], workspace, lang
                    )
                    if customer_result:
                        result["id"] = str(customer_result["id"])
                        result["name"] = customer_result["name"]

        elif row_type == "line_item":
            custom_line_item_model = page_obj["custom_line_item_model"]
            line_item_id = args[1]
            if is_valid_uuid(line_item_id):
                custom_line_item_model = custom_line_item_model.objects.filter(
                    id=line_item_id, workspace=workspace
                ).first()

                result["name"] = custom_line_item_model.name + " (Line Item)"
                result["value"] = "line_item|" + str(custom_line_item_model.id)
                result["id"] = str(custom_line_item_model.id)
                result["type"] = "text"
            else:
                if row_type in columns:
                    result["name"] = columns[row_type][lang]
        elif "invoice_platform|" in row_type and is_valid_uuid(
            row_type.split("|")[1][:36]
        ):
            channel_id = row_type.split("|")[1][:36]
            print(channel_id)
            channel = Channel.objects.filter(
                workspace=workspace, id=channel_id, integration__slug="stripe"
            ).first()
            if channel:
                field = row_type.split("|")[1][37:]
                result["name"] = (
                    channel.name + " - " + INVOICE_COLUMNS_DISPLAY[field][lang]
                )
        elif "subscription_platform|" in row_type and is_valid_uuid(
            row_type.split("|")[1][:36]
        ):
            channel_id = row_type.split("|")[1][:36]
            channel = Channel.objects.filter(
                workspace=workspace, id=channel_id, integration__slug="stripe"
            ).first()
            if channel:
                field = row_type.split("|")[1][37:]
                result["name"] = (
                    channel.name + "-" + SUBSCRIPTIONS_COLUMNS_DISPLAY[field][lang]
                )
        elif (
            row_type == "commerce_inventory"
            and len(args) > 1
            and is_valid_uuid(args[1])
        ):
            # Handle commerce_inventory|{custom_field_uuid}|{page_group_type} format (3 parts)
            # or commerce_inventory|{custom_field_uuid} format (2 parts)
            custom_field_id = args[1]
            inventory_custom_field = ShopTurboInventoryNameCustomField.objects.filter(
                id=custom_field_id, workspace=workspace
            ).first()
            if inventory_custom_field:
                parent_obj_display = OBJECT_GROUP_TYPE_SINGULAR["commerce_inventory"][
                    lang
                ]
                result["name"] = f"{parent_obj_display} - {inventory_custom_field.name}"
                # Use 2-part format for ID
                result["id"] = f"commerce_inventory|{custom_field_id}"
                result["type"] = inventory_custom_field.type
                # print(
                #     f"DEBUG PROPERTY_DISPLAY: Processed inventory custom field: {result['name']}")
                # print(f"DEBUG PROPERTY_DISPLAY: Returning result: {result}")
                return result  # Make sure we return the result
            else:
                pass  # Inventory custom field not found
        else:
            row_name = row_type
            if columns:
                if row_type in columns:
                    row_name = columns[row_type][lang]
                elif page == "timegenie" and row_type == "time_card":
                    row_name = "Time Card"
            result["name"] = row_name

        return result

    except Exception as e:
        print("Error as: ", e)

        traceback.print_exc()
        print()

        return {"name": arg.split("|")[0], "id": arg.split("|")[0], "value": ""}


def get_list_properties(
    page_group_type,
    workspace,
    lang,
    project_target=None,
    searchable_only=False,
    include_integration_properties=True,
    excludes=[],
    includes=[],
    shopify_sync_only=False,
    auto_association_only=False,
):
    """WILL BE DEPRECATED"""
    return get_properties_with_details(
        page_group_type,
        workspace,
        lang,
        project_target,
        searchable_only,
        include_integration_properties,
        excludes,
        includes,
        shopify_sync_only,
        auto_association_only,
    )


def get_properties_with_details(
    page_group_type,
    workspace,
    lang,
    project_target=None,
    searchable_only=False,
    include_integration_properties=True,
    excludes=[],
    includes=[],
    shopify_sync_only=False,
    auto_association_only=False,
    custom_object=None,
    page=None,
    per_page=None,
):
    """
    Returns a list of properties along with their details.

    Returns:
        list: A list of dictionaries, where each dictionary contains the details
        of a property (e.g., name, type, value, etc.).
    """
    # Create cache key for non-paginated, non-filtered requests
    cache_key = None
    if (
        page is None
        and per_page is None
        and not searchable_only
        and not shopify_sync_only
        and not auto_association_only
        and not excludes
        and not includes
        and custom_object is None
    ):
        cache_key = f"properties_details_{page_group_type}_{workspace.id}_{lang}"

        # Try to get from cache first
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            print(
                f"DEBUG PROPERTIES CACHE: Retrieved {len(cached_result)} properties from cache"
            )
            return cached_result

    page_obj = get_page_object(page_group_type, lang)
    base_model = page_obj["base_model"]
    custom_model = page_obj["custom_model"]
    editable_columns = page_obj["editable_columns"]

    # Handle case where base_model is None (object type not fully implemented)
    if base_model is None:
        return []

    properties = []
    if not shopify_sync_only and not auto_association_only:
        default_properties_dict = {
            field.name: field.get_internal_type() for field in base_model._meta.fields
        }
        default_columns = get_list_view_columns(
            page_group_type,
            workspace,
            include_integration_properties,
            include_cf=False,
            include_default_association=False,
        )

        if not default_columns:
            default_columns = page_obj["default_columns"]
            if page_obj["additional_column_fields"]:
                default_columns = default_columns + page_obj["additional_column_fields"]

        if not default_columns:
            default_columns = []

        for _column in default_columns:
            if _column in excludes:
                continue

            if "custom_price" == _column and page_group_type == TYPE_OBJECT_ESTIMATE:
                continue
            if (
                any(item in str(_column) for item in ["checkbox", "id", "usage_status"])
                and "platform" not in str(_column)
            ) or (
                _column == "status" and "usage_status" not in default_properties_dict
            ):
                continue
            if _column == "category" and page_group_type == TYPE_OBJECT_JOURNAL:
                _type = "hierarchy_choice"
            elif (
                (_column == "tax_rate" and page_group_type == TYPE_OBJECT_JOURNAL)
                or (_column == "tax_rate" and page_group_type == TYPE_OBJECT_EXPENSE)
                or (
                    _column == "inventory_status"
                    and page_group_type == TYPE_OBJECT_INVENTORY
                )
            ):
                _type = "choice"
            elif (
                (_column == "applications" and page_group_type == TYPE_OBJECT_JOBS)
                or (_column == "interview" and page_group_type == TYPE_OBJECT_JOBS)
                or (_column == "scorecard" and page_group_type == TYPE_OBJECT_JOBS)
            ):
                _type = "object"
            elif _column == "owner":
                _type = "user"
            elif _column in [TYPE_OBJECT_COMPANY]:
                _type = "association"
            else:
                _type = get_property_type(_column, default_properties_dict, base_model)

            if searchable_only:
                if _type not in SEARCHABLE_PROPERTY_TYPES:
                    continue

            custom_property = CustomProperty.objects.filter(
                workspace=workspace, model=base_model._meta.db_table, name=_column
            ).first()

            _updated_at = custom_property.updated_at if custom_property else None
            _edit_by = custom_property.edit_by if custom_property else None
            _immutable = False if _column in editable_columns else True

            properties.append(
                {
                    "id": _column,
                    "type": _type,
                    "created_at": None,
                    "updated_at": _updated_at,
                    "edit_by": _edit_by,
                    "immutable": _immutable,
                }
            )

            print(
                {
                    "id": _column,
                    "type": _type,
                    "created_at": None,
                    "updated_at": _updated_at,
                    "edit_by": _edit_by,
                    "immutable": _immutable,
                }
            )

        if default_columns:
            properties.extend(includes)

    # NOTE: Modularized this filter if we need more filter in the future
    custom_fields_filter = Q()
    if project_target:
        custom_fields_filter = Q(project_target=project_target)

    if not custom_model:
        return properties

    if custom_object:
        custom_fields = custom_model.objects.filter(
            custom_fields_filter,
            workspace=workspace,
            custom_object=custom_object,
            name__isnull=False,
        )
    else:
        # Add for avoid not settable name of property ##Faris
        custom_fields = custom_model.objects.filter(
            custom_fields_filter, workspace=workspace, name__isnull=False
        )

        # Optimize query by prefetching edit_by user if the field exists
        if hasattr(custom_model, "edit_by"):
            custom_fields = custom_fields.select_related("edit_by")

    # Exclude Shopify Sync
    if not shopify_sync_only and not auto_association_only:
        if page_group_type in [TYPE_OBJECT_ITEM, TYPE_OBJECT_INVENTORY]:
            custom_fields = custom_fields.exclude(is_shopify_sync=True)
        if page_group_type in [TYPE_OBJECT_ORDER]:
            custom_fields = custom_fields.exclude(is_association=True)
    else:
        if shopify_sync_only:
            custom_fields = custom_fields.filter(is_shopify_sync=True)
        if auto_association_only:
            custom_fields = custom_fields.filter(is_association=True)

    if "order" in [field.name for field in custom_model._meta.fields]:
        custom_fields = custom_fields.order_by("order")
    else:
        custom_fields = custom_fields.order_by("-created_at")

    # Exclude production line for task object
    # if page_group_type == TYPE_OBJECT_TASK:
    #     custom_fields = custom_fields.exclude(type="production_line")

    # Apply pagination if specified
    total_count = None
    if page is not None and per_page is not None:
        total_count = custom_fields.count()
        start = (page - 1) * per_page
        end = start + per_page
        custom_fields = custom_fields[start:end]

    # Prefetch all users that might be needed to avoid N+1 queries
    edit_by_ids = []
    custom_fields_list = list(custom_fields)
    for ctf in custom_fields_list:
        if hasattr(ctf, "edit_by_id") and ctf.edit_by_id:
            edit_by_ids.append(ctf.edit_by_id)

    # Fetch all users in one query
    users_dict = {}
    if edit_by_ids:
        users = User.objects.filter(id__in=edit_by_ids)
        users_dict = {user.id: user for user in users}

    for ctf in custom_fields_list:
        if ctf.id in excludes:
            continue

        if searchable_only:
            if ctf.type not in SEARCHABLE_PROPERTY_TYPES:
                continue

        # Use prefetched user data instead of individual queries
        if (
            hasattr(ctf, "edit_by_id")
            and ctf.edit_by_id
            and ctf.edit_by_id in users_dict
        ):
            ctf.__dict__["edit_by"] = users_dict[ctf.edit_by_id]
        elif hasattr(ctf, "edit_by") and ctf.edit_by:
            # If select_related was used, the edit_by is already loaded
            ctf.__dict__["edit_by"] = ctf.edit_by

        properties.append(ctf.__dict__)

    associates = None

    if associates and not searchable_only:
        for associate in associates:
            properties.append(associate.__dict__)

    # Return pagination metadata if pagination was requested
    if page is not None and per_page is not None and total_count is not None:
        return {
            "properties": properties,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total_count": total_count,
                "total_pages": (total_count + per_page - 1) // per_page,
                "has_next": page * per_page < total_count,
                "has_prev": page > 1,
            },
        }

    # Cache the result if we have a cache key (non-paginated, non-filtered requests)
    if cache_key is not None:
        # Cache for 15 minutes (properties don't change very frequently)
        cache.set(cache_key, properties, 60 * 15)
        print(
            f"DEBUG PROPERTIES CACHE: Cached {len(properties)} properties with key {cache_key}"
        )

    return properties


def get_context_formula_properties(context, page_group_type, workspace, lang):
    # Custom Column type
    page_obj = get_page_object(page_group_type)
    custom_model = page_obj["custom_model"]
    base_model = page_obj["base_model"]
    base_columns = page_obj["base_columns"]
    multiple_custom_types = page_obj["multiple_custom_types"]
    related_data = page_obj["related_data"]
    custom_value_related_model = page_obj["custom_value_related_model"]

    # Handle case where base_model is None (object type not fully implemented)
    if base_model is None:
        return context

    relation_properties = custom_model.objects.filter(
        workspace=workspace, name__isnull=False
    )
    context["relation_properties"] = relation_properties

    # Default Column type
    default_properties_dict = {
        field.name: field.get_internal_type()
        for field in base_model._meta.fields
        if field.name in base_columns
    }
    default_number_properties = list(
        {
            k: v
            for k, v in default_properties_dict.items()
            if v in ["IntegerField", "FloatField"]
        }.keys()
    )
    if default_number_properties:
        # pop ID
        default_number_properties.pop(0)
        if "item_price_order" in default_number_properties:
            default_number_properties.remove("item_price_order")

    context["default_property_column_display"] = {
        **page_obj["columns_display"],
        **SEARCH_COLUMNS_DISPLAY,
    }
    context["default_number_properties"] = default_number_properties

    default_text_properties = [
        k
        for k, v in default_properties_dict.items()
        if v in ["CharField", "TextField"] or k.endswith("_id") or k.startswith("id_")
    ]

    if page_group_type in [TYPE_OBJECT_ESTIMATE, TYPE_OBJECT_ORDER]:
        for cf in SEARCH_COLUMNS_DISPLAY:
            default_text_properties.append(cf)

    context["default_text_properties"] = default_text_properties
    custom_text_properties = list(
        {"id": val.id, "name": val.name}
        for val in relation_properties.filter(type__in=["text", "text-area", "choice"])
    )
    context["custom_text_properties"] = custom_text_properties

    # Custom-object Column type
    context["object_properties_number"] = []
    obj_properties = relation_properties.filter(
        type__in=OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE.keys()
    )
    for prop in obj_properties:
        page_obj_ = get_page_object(
            OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE[prop.type], lang
        )
        base_model = page_obj_["base_model"]
        columns_display = page_obj_["columns_display"]
        id_field = page_obj_["id_field"]

        # Skip if base_model is None (object type not fully implemented)
        if base_model is None:
            continue

        default_properties_dict = {
            field.name: field.get_internal_type() for field in base_model._meta.fields
        }
        for prop_name, prop_type in default_properties_dict.items():
            if prop_type not in ["IntegerField", "FloatField"] or prop_name == id_field:
                continue

            if prop.type in multiple_custom_types:
                try:
                    context["object_properties_number"].append(
                        {
                            "id": f"{prop.id}|{prop_name}|sum",
                            "value": f"{prop.name} - SUM({columns_display[prop_name][lang]})",
                        }
                    )
                except Exception as e:
                    print(e)
                    continue
            else:
                try:
                    context["object_properties_number"].append(
                        {
                            "id": f"{prop.id}|{prop_name}",
                            "value": f"{prop.name} - {columns_display[prop_name][lang]}",
                        }
                    )
                except Exception as e:
                    print(e)
                    continue

    context["functions_properties"] = []
    context["functions_properties"].extend(
        [
            {"id": "count", "value": "Count"},
            {"id": "round", "value": "round"},
            {"id": "round_up", "value": "round_up"},
            {"id": "round_down", "value": "round_down"},
            {"id": "#if", "value": "#if"},
            {"id": "#elif", "value": "#elif"},
            {"id": "#else", "value": "#else"},
            {"id": "#endif", "value": "#endif"},
            {"id": "break;", "value": "break;"},
        ]
    )

    context["related_data"] = []
    for related_obj in related_data:
        if related_obj in TYPE_OBJECTS:
            page_obj_ = get_page_object(related_obj, lang)
            base_model = page_obj_["base_model"]
            columns_display = page_obj_["columns_display"]
            id_field = page_obj_["id_field"]
            custom_model = page_obj_["custom_model"]

            # Skip if base_model is None (object type not fully implemented)
            if base_model is None:
                continue

            default_properties_dict = {
                field.name: field.get_internal_type()
                for field in base_model._meta.fields
            }
            for prop_name, prop_type in default_properties_dict.items():
                if (
                    prop_type not in ["IntegerField", "FloatField"]
                    or prop_name == id_field
                ):
                    continue

                try:
                    context["related_data"].append(
                        {
                            "object_name": FORMULA_RELATED_COLUMNS_DISPLAY[related_obj][
                                lang
                            ]
                            if related_obj in ["order", "journal"]
                            else OBJECT_GROUP_TYPE[related_obj][lang],
                            "property_name": columns_display[prop_name][lang],
                            # expected format: object_type|field|function
                            "value": f"{related_obj}|{prop_name}|sum",
                        }
                    )
                except Exception as e:
                    print(e)
                    continue

            custom_fields = custom_model.objects.filter(
                workspace=workspace, type__in=["components", "number"]
            )
            for custom_field in custom_fields:
                if custom_field.type == "number":
                    context["related_data"].append(
                        {
                            "object_name": FORMULA_RELATED_COLUMNS_DISPLAY[related_obj][
                                lang
                            ]
                            if related_obj in ["order", "journal"]
                            else OBJECT_GROUP_TYPE[related_obj][lang],
                            "property_name": custom_field.name,
                            "value": f"{related_obj}|{custom_field.id}|sum",
                        }
                    )
                else:
                    custom_object_prop = get_page_object(related_obj, lang)
                    base_model = custom_object_prop["base_model"]
                    columns_display = custom_object_prop["columns_display"]
                    id_field = page_obj_["id_field"]

                    default_properties_dict = {
                        field.name: field.get_internal_type()
                        for field in base_model._meta.fields
                    }
                    for prop_name, prop_type in default_properties_dict.items():
                        if (
                            prop_type not in ["IntegerField", "FloatField"]
                            or prop_name == id_field
                        ):
                            continue

                        context["related_data"].append(
                            {
                                "object_name": FORMULA_RELATED_COLUMNS_DISPLAY[
                                    related_obj
                                ][lang]
                                if related_obj in ["order", "journal"]
                                else OBJECT_GROUP_TYPE[related_obj][lang],
                                "property_name": custom_field.name,
                                "sub_property_name": columns_display[prop_name][lang],
                                "value": f"{related_obj}|{custom_field.id}|{prop_name}|sum",
                            }
                        )

        else:
            context["related_data"].append(
                {"object_type": related_obj, "value": related_obj}
            )

    for custom_value_related_model_ in custom_value_related_model:
        custom_objs = custom_model.objects.filter(
            workspace=workspace, type=custom_value_related_model_["type"]
        ).values("id", "name")
        related_model = custom_value_related_model_["model"]
        related_model_columns_display = custom_value_related_model_["columns_display"]
        default_properties_dict = {
            field.name: field.get_internal_type()
            for field in related_model._meta.fields
        }
        for custom_obj in custom_objs:
            for prop_name, prop_type in default_properties_dict.items():
                if prop_type not in ["IntegerField", "FloatField"]:
                    continue

                # Single property sum
                context["object_properties_number"].append(
                    {
                        "id": f"{custom_obj['id']}|{custom_value_related_model_['relation']}|{prop_name}|sum",
                        "value": f"{custom_obj['name']} - SUM({related_model_columns_display[prop_name][lang]})",
                    }
                )

                page_obj_ = get_page_object(
                    OBJ_CUSTOM_FIELD_MAP_PAGE_GROUP_TYPE[
                        custom_value_related_model_["type"]
                    ],
                    lang,
                )
                base_model_ = page_obj_["base_model"]
                columns_display_ = page_obj_["columns_display"]
                id_field_ = page_obj_["id_field"]
                default_properties_dict_ = {
                    field.name: field.get_internal_type()
                    for field in base_model_._meta.fields
                    if field.name in columns_display_ and field.name != id_field_
                }
                for prop_name_, prop_type_ in default_properties_dict_.items():
                    if prop_type_ not in ["IntegerField", "FloatField"]:
                        continue

                    # Sumproduct interaction between two properties
                    context["object_properties_number"].append(
                        {
                            "id": f"{custom_obj['id']}|{custom_value_related_model_['relation']}|{prop_name}|sumproduct|{prop_name_}",
                            "value": f"{custom_obj['name']} - SUM({related_model_columns_display[prop_name][lang]}*{columns_display_[prop_name_][lang]})",
                        }
                    )

    context["related_property_column_display"] = FORMULA_RELATED_COLUMNS_DISPLAY

    # This is custom written properties for each page group type
    if page_group_type == "production":
        context["related_item_properties"] = (
            ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, name__isnull=False, type="number"
            )
        )
    if page_group_type in ["contacts", "company"]:
        context["related_item_properties"] = (
            ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, name__isnull=False, type="number"
            )
        )
    elif page_group_type == TYPE_OBJECT_CASE:
        # Attendance <> Employee Related Columns (Number)
        related_attendance = DealsNameCustomField.objects.filter(
            workspace=workspace, name__isnull=False, type="attendance"
        )
        if related_attendance:
            context["related_attendance_columns"] = ["hours", "minutes", "seconds"]
            # Get Attendance <> Employee Related Columns (Number)
            context["related_attendance_employee_properties"] = (
                WorkerNameCustomField.objects.filter(
                    workspace=workspace, name__isnull=False, type="number"
                )
            )
        attendance_associates = Association.objects.filter(
            workspace=workspace,
            associate_type=TYPE_OBJECT_CASE,
            attendance_associate__isnull=False,
        )
        if attendance_associates:
            context["attendance_associates"] = ["hours", "minutes", "seconds"]
            # Association Part
            # Get Attendance <> Employee Related Columns(Number)
            context["attendance_employee_associates_properties"] = (
                WorkerNameCustomField.objects.filter(
                    workspace=workspace, name__isnull=False, type="number"
                )
            )

        context["functions_properties"] = ["total_wage_cost"]

        # Get Price Information Custom Fields for Case
        price_information_cf = DealsNameCustomField.objects.filter(
            workspace=workspace, name__isnull=False, type="price-information"
        )
        if price_information_cf:
            for cf in price_information_cf:
                val = (
                    f"商品項目 - {cf.name}"
                    if lang == "ja"
                    else f"Line Items - {cf.name}"
                )
                context["object_properties_number"].append(
                    {"id": f"{str(cf.id)}", "value": val}
                )

        context["related_task_properties"] = TaskCustomFieldName.objects.filter(
            workspace=workspace, name__isnull=False, type="number"
        )

    return context


def get_list_view_columns(
    page_group_type,
    workspace,
    include_integration_properties=True,
    include_cf=True,
    include_default_association=True,
):
    """
    Returns a list of column identifiers for the list view of a specified object type.

    The columns are dynamically determined based on the object type, workspace configuration, and optional inclusion of integration properties, custom fields, and default associations. Supports a wide range of object types including items, orders, subscriptions, invoices, tasks, contacts, companies, inventory, inventory transactions, cases, purchase orders, bills, journals, expenses, and workflows. The returned columns may include base fields, integration-specific fields, custom fields, and related object properties as appropriate for each type.

    Args:
        page_group_type: The object type or page group for which to retrieve list view columns.
        include_integration_properties: If True, includes columns related to external integrations.
        include_cf: If True, includes custom field columns defined for the workspace.
        include_default_association: If True, includes columns for default associations such as related contacts or companies.

    Returns:
        A list of column names or identifiers to be displayed in the list view for the specified object type.
    """
    column_values = []
    line_item_columns = [
        "line_item",
        "line_item_name",
        "line_item_quantity",
        "line_item_name_quantity",
        "line_item_status",
        "line_item_price",
        "line_item_tax",
    ]
    if page_group_type == TYPE_OBJECT_ITEM:
        column_values = DEFAULT_COLUMNS_ITEM[1:].copy() + [
            # Below are the inventory amount columns
            "supplier__contact__name",
            "supplier__company__name",
            "supplier__contact__first_name",
            "supplier__contact__last_name",
            "inventory__total_inventory",
            "inventory__available_amount",
            "inventory__unavailable_amount",
            "inventory__committed_amount",
            "warehouse_inventory_amount",
        ]
        if include_integration_properties:
            channels = Channel.objects.filter(
                workspace=workspace,
                integration__slug__in=[
                    "shopify",
                    "square",
                    "amazon",
                    "ecforce",
                    "rakuten",
                    "hubspot",
                    "yahoo-shopping",
                    "makeshop",
                    "salesforce",
                    "smaregi",
                    "b-cart",
                ],
            )
            for name in EXCLUDE_SYNC_CHANNEL_NAME + ["Hubspot Power Message"]:
                channels = channels.exclude(name__icontains=name)

            for channel in channels:
                column_values.insert(
                    len(column_values), f"item_platform|{channel.id}-Item ID"
                )

                if channel.integration.slug == "shopify":
                    column_values.insert(
                        len(column_values), f"item_platform|{channel.id}-SKU"
                    )
                    column_values.insert(
                        len(column_values), f"item_platform|{channel.id}-Variant ID"
                    )

            if channels:
                column_values.insert(len(column_values), "platform_ids")

        if include_cf:
            page_object = get_page_object(TYPE_OBJECT_ITEM)
            custom_model = page_object["custom_model"]
            items_namecustomfields = custom_model.objects.filter(workspace=workspace)

            custom_field_ids = [
                str(uuid)
                for uuid in items_namecustomfields.values_list("id", flat=True)
            ]
            column_values.extend(custom_field_ids)

            for items_namecustomfield in items_namecustomfields:
                if items_namecustomfield.type == "components":
                    column_values.extend(
                        [f"component_quantity|{items_namecustomfield.id}"]
                    )

        return column_values

    elif page_group_type == TYPE_OBJECT_ORDER:
        column_values = DEFAULT_COLUMNS_ORDER.copy()
        column_values.remove("checkbox")
        column_values.remove("order_id")

        # Subscriptipons
        column_values.insert(len(column_values), TYPE_OBJECT_SUBSCRIPTION)

        if include_integration_properties:
            channels = Channel.objects.filter(
                workspace=workspace,
                integration__slug__in=[
                    "shopify",
                    "amazon",
                    "square",
                    "nextengine",
                    "rakuten",
                    "hubspot",
                    "ecforce",
                    "freee",
                    "ec-cube",
                    "makeshop",
                    "yahoo-shopping",
                    "b-cart",
                    "wordpress",
                    "salesforce",
                    "stripe",
                ],
            ).exclude(status="draft")

            for channel in channels:
                if channel.integration.slug == "hubspot":
                    column_values.insert(
                        len(column_values), f"{str(channel.name)}-Deal ID"
                    )
                    column_values.insert(
                        len(column_values), f"{str(channel.name)}-Item"
                    )
                elif channel.integration.slug == "nextengine":
                    column_values.insert(
                        len(column_values), f"{str(channel.name)}-Order ID"
                    )
                elif channel.integration.slug == "stripe":
                    column_values.append(f"order_platform|{channel.id}-payment_link")
                    column_values.append(f"order_platform|{channel.id}-payment_status")
                    column_values.append(f"order_platform|{channel.id}-paid_at")
                else:
                    column_values.insert(
                        len(column_values), f"{str(channel.name)}-Order ID"
                    )
                    column_values.insert(
                        len(column_values), f"{str(channel.name)}-Item"
                    )
                    if channel.integration.slug == "shopify":
                        column_values.insert(
                            len(column_values),
                            f"{str(channel.name)}-Fulfillment Status",
                        )
                        column_values.insert(
                            len(column_values), f"{str(channel.name)}-Order Status"
                        )
                        column_values.insert(
                            len(column_values), f"{str(channel.name)}-Payment Status"
                        )

        # Invoice
        column_values.append(TYPE_OBJECT_INVOICE)
        # Estimate
        column_values.append(TYPE_OBJECT_ESTIMATE)
        # Inventory Transactions
        column_values.append(TYPE_OBJECT_INVENTORY_TRANSACTION)

        # New Assoc
        column_values.append(TYPE_OBJECT_CASE)
        column_values.append(TYPE_OBJECT_DELIVERY_NOTE)
        column_values.append(TYPE_OBJECT_PURCHASE_ORDER)
        column_values.append(TYPE_OBJECT_TASK)

        # owner
        column_values.append("owner")

        # Line Items inventory amount in location
        column_values.append("line_item_inventory_location")

        # Line items tax
        column_values.append("line_item_tax")

        # Line Items
        line_item_cf = ShopTurboItemsOrdersNameCustomField.objects.filter(
            workspace=workspace
        ).order_by("type")
        for c in line_item_cf:
            column_values.append("line_item|" + str(c.id))
        # print("++++ column_values: ", column_values)

        # Warehouse inventory amount
        column_values.append("warehouse_inventory_amount")

        if include_default_association:
            # Customer Columns
            _contact_properties = get_list_view_columns("contacts", workspace)
            if "name" not in _contact_properties:
                _contact_properties.append("name")
            for p in _contact_properties:
                column_values.append(f"customer|contact|{p}")

            _company_properties = get_list_view_columns("company", workspace)
            for p in _company_properties:
                column_values.append(f"customer|company|{p}")

            # Item Columns
            _item_columns = get_list_view_columns("commerce_items", workspace)
            column_values.append("source_item")
            for p in _item_columns:
                column_values.append(f"source_item__{p}")

            # Contact/Company Object
            order_namecustomfields = ShopTurboOrdersNameCustomField.objects.filter(
                workspace=workspace
            )
            for customfield in order_namecustomfields:
                if customfield.type == "contact":
                    for p in _contact_properties:
                        column_values.append(f"contact|{customfield.id}|{p}")

                elif customfield.type == "company":
                    for p in _company_properties:
                        column_values.append(f"company|{customfield.id}|{p}")

                elif customfield.type == "shipping_info":
                    sub_property = ast.literal_eval(customfield.sub_property)
                    column_values.append(
                        f"shipping_info|{customfield.id}|{customfield.name}|ID"
                    )
                    for p in sub_property:
                        column_values.append(
                            f"shipping_info|{customfield.id}|{customfield.name}|{p}"
                        )

                else:
                    column_values.append(customfield.id)

        return column_values

    elif page_group_type == TYPE_OBJECT_SUBSCRIPTION:
        page_object = get_page_object(TYPE_OBJECT_SUBSCRIPTION)
        column_values = page_object["base_columns"]
        if include_integration_properties:
            channels = Channel.objects.filter(
                workspace=workspace, integration__slug__in=["stripe"]
            )

            for channel in channels:
                column_values.append(
                    f"subscription_platform|{channel.id}-subscription_id"
                )

                if channel.integration.slug == "stripe":
                    column_values.append(
                        f"subscription_platform|{channel.id}-platform_status"
                    )
                    column_values.append(
                        f"subscription_platform|{channel.id}-payment_link"
                    )
                    column_values.append(
                        f"subscription_platform|{channel.id}-manage_payment_link"
                    )
                    column_values.append(
                        f"subscription_platform|{channel.id}-next_billing_price"
                    )

        if include_cf:
            custom_model = page_object["custom_model"]
            namecustomfields = custom_model.objects.filter(workspace=workspace)
            _contact_properties = get_list_view_columns("contacts", workspace)
            _company_properties = get_list_view_columns("company", workspace)

            for customfield in namecustomfields:
                if customfield.type == "contact":
                    for p in _contact_properties:
                        column_values.append(f"contact|{customfield.id}|{p}")

                elif customfield.type == "company":
                    for p in _company_properties:
                        column_values.append(f"company|{customfield.id}|{p}")
                else:
                    column_values.append(str(customfield.id))

        if "item_name" in column_values:
            column_values.remove("item_name")
        if "orders" in column_values:
            column_values.remove("orders")

        column_values.append(TYPE_OBJECT_ITEM)
        column_values.append(TYPE_OBJECT_INVOICE)
        column_values.append(TYPE_OBJECT_ORDER)

        return column_values

    elif page_group_type == TYPE_OBJECT_INVOICE:
        page_object = get_page_object(TYPE_OBJECT_INVOICE)
        column_values = page_object["base_columns"] + ["line_item"]

        if include_integration_properties:
            channels = Channel.objects.filter(
                workspace=workspace, integration__slug__in=["stripe"]
            )

            for channel in channels:
                column_values.insert(
                    len(column_values),
                    f"invoice_platform|{channel.id}-platform_invoice_id",
                )
                column_values.insert(
                    len(column_values), f"invoice_platform|{channel.id}-payment_link"
                )

        if include_default_association:
            # Customer Columns
            _contact_properties = get_list_view_columns("contacts", workspace)
            if "name" not in _contact_properties:
                _contact_properties.append("name")
            for p in _contact_properties:
                column_values.append(f"customer|contact|{p}")

            _company_properties = get_list_view_columns("company", workspace)
            for p in _company_properties:
                column_values.append(f"customer|company|{p}")

        # Add custom properties for invoices
        custom_model = page_object["custom_model"]
        if custom_model:
            for field in custom_model.objects.filter(workspace=workspace):
                column_values.append(str(field.id))

        if "journalentry" in column_values:
            column_values.remove("journalentry")

        # delete customers
        if "customers__contact__name" in column_values:
            column_values.remove("customers__contact__name")
        if "customers__contact__first_name" in column_values:
            column_values.remove("customers__contact__first_name")
        if "customers__contact__last_name" in column_values:
            column_values.remove("customers__contact__last_name")
        if "customers__company__name" in column_values:
            column_values.remove("customers__company__name")
        if "order_association" in column_values:
            column_values.remove("order_association")
        if "associate#receipts" in column_values:
            column_values.remove("associate#receipts")
        if "subscription" in column_values:
            column_values.remove("subscription")

        # association label
        column_values.append("customer")
        column_values.append(TYPE_OBJECT_SUBSCRIPTION)
        column_values.append(TYPE_OBJECT_ORDER)
        column_values.append(TYPE_OBJECT_CASE)
        column_values.append(TYPE_OBJECT_RECEIPT)

        return column_values

    elif page_group_type == TYPE_OBJECT_TASK:
        column_values = get_list_object_columns(
            Task,
            includes=["assignee", "sub_tasks", "owner"],
            excludes=["line_item", "order"],
        ) + ["line_item"]
        if include_cf:
            tasknamecf = TaskCustomFieldName.objects.filter(
                workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in tasknamecf])

        # Update order of 'assignee' column to be after 'title' column
        column_values.remove("assignee")
        column_values.insert(2, "assignee")

        return column_values

    elif page_group_type == TYPE_OBJECT_CONTACT:
        column_values = get_list_object_columns(
            Contact,
            excludes=["created_at"],
            includes=[
                "owner",
                TYPE_OBJECT_COMPANY,
                TYPE_OBJECT_CASE,
                TYPE_OBJECT_ORDER,
                TYPE_OBJECT_ESTIMATE,
                TYPE_OBJECT_DELIVERY_NOTE,
                TYPE_OBJECT_INVOICE,
                TYPE_OBJECT_RECEIPT,
                TYPE_OBJECT_SUBSCRIPTION,
                TYPE_OBJECT_EXPENSE,
            ],
        )

        column_values = [
            "first_name" if col == "name" else col for col in column_values
        ]
        channels = Channel.objects.filter(
            workspace=workspace,
            integration__slug__in=[
                "line",
                "shopify",
                "hubspot",
                "rakuten",
                "salesforce",
            ],
        )  # Add Others, Might Be add others

        for channel in channels:
            if channel.integration.slug == "line":
                column_values.insert(
                    len(column_values),
                    f"{str(channel.id)} - {str(channel.name)} - Line User ID",
                )
            else:
                column_values.insert(
                    len(column_values), f"{str(channel.name)} - customer ID"
                )

        if include_cf:
            page_object = get_page_object(TYPE_OBJECT_CONTACT)
            custom_model = page_object["custom_model"]
            namecustomfields = custom_model.objects.filter(workspace=workspace)

            custom_field_ids = [
                str(uuid) for uuid in namecustomfields.values_list("id", flat=True)
            ]
            column_values.extend(custom_field_ids)

        return column_values

    elif page_group_type == TYPE_OBJECT_COMPANY:
        column_values = get_list_object_columns(
            Company,
            excludes=["created_at"],
            includes=[
                "owner",
                TYPE_OBJECT_CONTACT,
                TYPE_OBJECT_CASE,
                TYPE_OBJECT_ORDER,
                TYPE_OBJECT_ESTIMATE,
                TYPE_OBJECT_DELIVERY_NOTE,
                TYPE_OBJECT_INVOICE,
                TYPE_OBJECT_RECEIPT,
                TYPE_OBJECT_SUBSCRIPTION,
                TYPE_OBJECT_EXPENSE,
                TYPE_OBJECT_BILL,
            ],
        )
        column_values.append("image_file")
        # column_values = ['first_name' if col == 'name' else col for col in column_values]
        channels = Channel.objects.filter(
            workspace=workspace,
            integration__slug__in=["line", "hubspot", "salesforce", "b-cart"],
        )  # Add Others, Might Be add others

        for channel in channels:
            if channel.integration.slug == "line":
                column_values.insert(
                    len(column_values),
                    f"{str(channel.id)} - {str(channel.name)} - Line User ID",
                )
            else:
                column_values.insert(
                    len(column_values), f"{str(channel.name)} - company id"
                )

        if include_cf:
            page_object = get_page_object(TYPE_OBJECT_COMPANY)
            custom_model = page_object["custom_model"]
            namecustomfields = custom_model.objects.filter(workspace=workspace)

            custom_field_ids = [
                str(uuid) for uuid in namecustomfields.values_list("id", flat=True)
            ]
            column_values.extend(custom_field_ids)

        return column_values

    elif page_group_type == TYPE_OBJECT_INVENTORY:
        column_values = DEFAULT_COLUMNS_INVENTORY[1:].copy() + [
            "inventory_value",
        ]

        # Add association properties
        column_values.append(TYPE_OBJECT_ITEM)
        column_values.append(TYPE_OBJECT_INVENTORY_TRANSACTION)
        column_values.append(TYPE_OBJECT_INVENTORY_WAREHOUSE)

        if include_cf:
            inventory_namecustomfields = (
                ShopTurboInventoryNameCustomField.objects.filter(
                    workspace=workspace
                ).values_list("id", flat=True)
            )
            inventory_namecustomfields = [
                str(uuid) for uuid in inventory_namecustomfields
            ]
            column_values.extend(inventory_namecustomfields)

        if include_integration_properties:
            channels = Channel.objects.filter(
                workspace=workspace, integration__slug__in=["shopify"]
            )
            for name in EXCLUDE_SYNC_CHANNEL_NAME + ["Hubspot Power Message"]:
                channels = channels.exclude(name__icontains=name)

            for channel in channels:
                column_values.insert(
                    len(column_values), f"{str(channel.name)}-Inventory ID"
                )
                if channel.integration.slug == "shopify":
                    column_values.insert(len(column_values), f"{str(channel.name)}-SKU")
                    column_values.insert(
                        len(column_values), f"{str(channel.name)}-Variant ID"
                    )

            if channels:
                column_values.insert(len(column_values), "platform_ids")

        return column_values

    elif page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
        page_obj = get_page_object(TYPE_OBJECT_INVENTORY_TRANSACTION)
        custom_model = page_obj["custom_model"]
        base_columns = page_obj["base_columns"]
        default_columns = page_obj["default_columns"]
        pre_default_column = default_columns
        pre_default_column.remove("checkbox")
        pre_default_column.remove(page_obj["id_field"])
        column_values = base_columns
        custom_columns = []

        # Add inventory custom properties using pipe syntax
        if include_cf:
            inventory_custom_fields = ShopTurboInventoryNameCustomField.objects.filter(
                workspace=workspace
            )
            print(
                f"DEBUG GET_LIST_VIEW_COLUMNS: Found {inventory_custom_fields.count()} inventory custom fields"
            )
            for cf in inventory_custom_fields:
                column_option = f"commerce_inventory|{cf.id}"
                column_values.append(column_option)
                print(
                    f"DEBUG GET_LIST_VIEW_COLUMNS: Added inventory custom field option: {column_option} (name: {cf.name})"
                )

        column_values = base_columns.copy()
        # NOTE: Add Elements of custom columns
        if custom_model and include_cf:
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])
        # NOTE: Add Elements of default columns
        column_values.extend(
            [str(item) for item in default_columns if item not in column_values]
        )

        # already replace by TYPE_OBJECT_INVENTORY
        if "inventory" in column_values:
            column_values.remove("inventory")

        # association label
        column_values.append(TYPE_OBJECT_INVENTORY)
        column_values.append(TYPE_OBJECT_ORDER)
        column_values.append(TYPE_OBJECT_PURCHASE_ORDER)

        return column_values

    elif page_group_type == TYPE_OBJECT_CASE:
        page_obj = get_page_object(page_group_type)
        column_values = page_obj["base_columns"].copy()
        column_values.extend(
            [
                str(item)
                for item in page_obj["default_columns"].copy()
                + [
                    TYPE_OBJECT_ORDER,
                    TYPE_OBJECT_INVOICE,
                    TYPE_OBJECT_ESTIMATE,
                    TYPE_OBJECT_TASK,
                ]
                if item not in column_values
            ]
        )
        if "owner" in column_values:
            column_values.remove("owner")

        if include_cf:
            custom_columns = []
            custom_model = page_obj["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])
        column_values.remove("currency")

        if include_default_association:
            # Customer Columns
            _contact_properties = get_list_view_columns("contacts", workspace)
            if "name" not in _contact_properties:
                _contact_properties.append("name")
            for p in _contact_properties:
                column_values.append(f"customer|contact|{p}")

            _company_properties = get_list_view_columns("company", workspace)
            for p in _company_properties:
                column_values.append(f"customer|company|{p}")

        if "kanban_order" in column_values:
            column_values.remove("kanban_order")

        return column_values

    elif page_group_type == TYPE_OBJECT_SESSION_EVENT:
        page_object = get_page_object(TYPE_OBJECT_SESSION_EVENT)
        return page_object["default_columns"]

    elif page_group_type == TYPE_OBJECT_PURCHASE_ORDER:
        page_obj = get_page_object(page_group_type)
        column_values = page_obj["base_columns"].copy()
        # Line Items
        for c in line_item_columns:
            if c not in column_values:
                column_values.append(c)
        column_values.append("line_item_price_without_tax")
        # Default columns
        column_values.extend(
            [
                str(item)
                for item in page_obj["default_columns"].copy()
                if item not in column_values
            ]
        )
        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_obj["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

        if include_default_association:
            column_values.append("source_item")

        return column_values

    elif page_group_type in BILL_OBJECTS:
        page_obj = get_page_object(page_group_type)
        column_values = DEFAULT_FORM_FIELD_COMMERCE.copy()
        for col_val in column_values:
            if (
                col_val not in page_obj["base_columns"]
                and col_val not in page_obj["default_columns"]
            ):
                column_values.remove(col_val)
        column_values.extend(
            [
                str(item)
                for item in page_obj["base_columns"].copy()
                if item not in column_values
            ]
        )
        column_values.extend(
            [
                str(item)
                for item in page_obj["default_columns"].copy()
                if item not in column_values
            ]
        )

        if include_cf:
            custom_columns = []
            custom_model = page_obj["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

        if (
            page_group_type == TYPE_OBJECT_INVOICE
            or page_group_type == TYPE_OBJECT_DELIVERY_NOTE
        ):  # This follow what "order" did
            if include_default_association:
                # Remove Contact__ or Company__ -> doesnt suppoort Custom Property
                column_values = [
                    str(item) for item in column_values if "customers__" not in item
                ]
                if "journalentry" in column_values:
                    column_values.remove("journalentry")

                _contact_properties = get_list_view_columns("contacts", workspace)
                if "name" not in _contact_properties:
                    _contact_properties.append("name")
                for p in _contact_properties:
                    column_values.append(f"customer|contact|{p}")

                _company_properties = get_list_view_columns("company", workspace)
                for p in _company_properties:
                    column_values.append(f"customer|company|{p}")

                contact_custom_columns = ContactsNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("created_at")
                column_values.extend(
                    [
                        "customer|contact|" + str(item.id)
                        for item in contact_custom_columns
                    ]
                )
                contact_custom_columns = CompanyNameCustomField.objects.filter(
                    workspace=workspace
                ).order_by("created_at")
                column_values.extend(
                    [
                        "customer|company|" + str(item.id)
                        for item in contact_custom_columns
                    ]
                )
        elif page_group_type == TYPE_OBJECT_RECEIPT:
            if "customers__contact__name" in column_values:
                column_values.remove("customers__contact__name")
            if "customers__contact__first_name" in column_values:
                column_values.remove("customers__contact__first_name")
            if "customers__contact__last_name" in column_values:
                column_values.remove("customers__contact__last_name")
            if "customers__company__name" in column_values:
                column_values.remove("customers__company__name")
            if "order_association" in column_values:
                column_values.remove("order_association")
            if "invoice" in column_values:
                column_values.remove("invoice")
            if "customers" in column_values:
                column_values.remove("customers")

            column_values.append("customer")
            column_values.append(TYPE_OBJECT_INVOICE)

        elif page_group_type == TYPE_OBJECT_ESTIMATE:
            if "customers__contact__name" in column_values:
                column_values.remove("customers__contact__name")
            if "customers__contact__first_name" in column_values:
                column_values.remove("customers__contact__first_name")
            if "customers__contact__last_name" in column_values:
                column_values.remove("customers__contact__last_name")
            if "customers__company__name" in column_values:
                column_values.remove("customers__company__name")
            if "order_association" in column_values:
                column_values.remove("order_association")
            if "invoice" in column_values:
                column_values.remove("invoice")
            if "customers" in column_values:
                column_values.remove("customers")

            column_values.append("customer")
            column_values.append(TYPE_OBJECT_ORDER)
            column_values.append(TYPE_OBJECT_CASE)
            column_values.append(TYPE_OBJECT_INVOICE)

        else:
            if include_default_association:
                contact_custom_columns = ContactsNameCustomField.objects.filter(
                    workspace=workspace
                ).values_list("name", flat=True)
                column_values.extend(
                    ["Customer-" + str(item) for item in contact_custom_columns]
                )

        return column_values

    elif page_group_type == TYPE_OBJECT_BILL:
        page_obj = get_page_object(page_group_type)
        column_values = page_obj["base_columns"].copy()
        default_form_fields = page_obj["default_form_fields"].copy()
        column_values = reorder_columns(default_form_fields, column_values)
        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_obj["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

    elif page_group_type in TYPE_OBJECT_WORKFLOW:
        column_values = DEFAULT_COLUMNS_WORKFLOW.copy()

    elif page_group_type in TYPE_OBJECT_JOURNAL:
        page_object = get_page_object(TYPE_OBJECT_JOURNAL)
        column_values = page_object["base_columns"]
        # Default columns
        column_values.extend(
            [
                str(item)
                for item in page_object["default_columns"].copy()
                if item not in column_values
            ]
        )
        # Line transaction in Journal Entry
        column_values.append("line_transaction")

        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_object["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

    elif page_group_type in TYPE_OBJECT_EXPENSE:
        page_object = get_page_object(TYPE_OBJECT_EXPENSE)
        column_values = page_object["base_columns"].copy()
        # Default columns
        column_values.extend(
            [
                str(item)
                for item in page_object["default_columns"].copy()
                if item not in column_values
            ]
        )
        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_object["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])

    elif page_group_type in TYPE_OBJECT_COMMERCE_METER:
        page_object = get_page_object(TYPE_OBJECT_COMMERCE_METER)
        column_values = page_object["base_columns"].copy()
        # Default columns
        column_values.extend(
            [
                str(item)
                for item in page_object["default_columns"].copy()
                if item not in column_values
            ]
        )
        # Custom Fields
        if include_cf:
            custom_columns = []
            custom_model = page_object["custom_model"]
            custom_columns = custom_model.objects.filter(
                name__isnull=False, workspace=workspace
            ).values_list("id", flat=True)
            column_values.extend([str(item) for item in custom_columns])
    return column_values


def get_default_property_set(page_group_type, workspace, lang, name=None):
    excludes = []
    if page_group_type == TYPE_OBJECT_CASE:
        excludes = ["estimates", "invoices", "tasks", "line_item", "currency"]
    if page_group_type == TYPE_OBJECT_TASK:
        excludes = ["line_item"]

    payload = {"workspace": workspace, "name": name, "target": page_group_type}
    try:
        default_set, status = PropertySet.objects.get_or_create(**payload)
    except PropertySet.MultipleObjectsReturned:
        status = None
        default_set = PropertySet.objects.filter(**payload).first()
        PropertySet.objects.filter(**payload).exclude(id=default_set.id).delete()

    if status:
        default_set.as_default = True

    page_obj = get_page_object(page_group_type)
    default_properties = page_obj["default_form_fields"].copy()
    if default_properties:
        if not default_set.children:
            default_set.children = default_properties

            # Add inventory custom properties for inventory transactions
            if page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
                print(
                    f"DEBUG GET_DEFAULT_PROPERTY_SET: Adding inventory custom properties for {page_group_type}"
                )
                inventory_custom_fields = (
                    ShopTurboInventoryNameCustomField.objects.filter(
                        workspace=workspace
                    )
                )
                for cf in inventory_custom_fields:
                    inventory_custom_property = f"commerce_inventory|{cf.id}"
                    if inventory_custom_property not in default_set.children:
                        default_set.children.append(inventory_custom_property)
                        print(
                            f"DEBUG GET_DEFAULT_PROPERTY_SET: Added inventory custom property: {inventory_custom_property} (name: {cf.name})"
                        )
                print(
                    f"DEBUG GET_DEFAULT_PROPERTY_SET: Final children: {default_set.children}"
                )
        else:
            # PropertySet already has children, but we need to add inventory custom properties if missing
            if page_group_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
                print(
                    f"DEBUG GET_DEFAULT_PROPERTY_SET: Updating existing PropertySet children for {page_group_type}"
                )
                inventory_custom_fields = (
                    ShopTurboInventoryNameCustomField.objects.filter(
                        workspace=workspace
                    )
                )
                for cf in inventory_custom_fields:
                    inventory_custom_property = f"commerce_inventory|{cf.id}"
                    if inventory_custom_property not in default_set.children:
                        default_set.children.append(inventory_custom_property)
                        print(
                            f"DEBUG GET_DEFAULT_PROPERTY_SET: Added missing inventory custom property: {inventory_custom_property} (name: {cf.name})"
                        )
                print(
                    f"DEBUG GET_DEFAULT_PROPERTY_SET: Updated children: {default_set.children}"
                )
    else:
        properties = get_properties_with_details(
            page_group_type, workspace, lang, excludes=excludes
        )
        default_set.children = [str(get_attr(p, "id")) for p in properties]

    default_set.save()

    return default_set


def get_properties_from_set(set_id, page_group_type, workspace, as_default=None):
    property_set = None
    properties = {
        "list_all": [],  # Need to get all List (faris)
        "default": [],
        "custom": [],
    }

    try:
        if set_id and is_valid_uuid(set_id):
            property_set = PropertySet.objects.filter(
                id=set_id, name__isnull=False
            ).first()
            if not property_set:
                property_set = PropertySet.objects.filter(
                    workspace=workspace, target=page_group_type, as_default=True
                ).first()
            if not property_set:
                property_set = PropertySet.objects.filter(id=set_id).first()

        if not property_set:
            property_set = PropertySet.objects.filter(
                workspace=workspace, target=page_group_type, as_default=True
            ).first()
        if not property_set:
            property_set = PropertySet.objects.filter(
                workspace=workspace, target=page_group_type, name__isnull=True
            ).first()
        if not property_set:
            get_default_property_set(page_group_type, workspace, "en")
            property_set = PropertySet.objects.filter(
                workspace=workspace, target=page_group_type, name__isnull=True
            ).first()

        if property_set:
            set_id = str(property_set.id)
            if property_set.children:
                for p in property_set.children:
                    properties["list_all"].append(p)
                    if is_valid_uuid(p):
                        properties["custom"].append(p)
                    else:
                        properties["default"].append(p)
    except Exception as e:
        print(f"... ERROR === property.py -- 490: {e}")

    set_id = property_set.id if property_set else None
    return set_id, properties


def object_display(page_group_type, workspace, include_custom_properties=True):
    """
    Get all properties of an object type, including custom properties and nested item properties to be used in object display options.

    :param page_group_type: The type of object (e.g. TYPE_OBJECT_CONTACT)
    :param workspace: The workspace
    :param include_custom_properties: True to include custom properties, False to exclude
    :return: A list of property names
    """

    props = get_properties_with_details(
        page_group_type, workspace, "en", include_integration_properties=False
    )
    builtin_properties = []
    custom_properties = []
    default_properties = DEFAULT_OBJECT_DISPLAY[page_group_type]
    for prop in props:
        # Custom property
        if is_valid_uuid(prop["id"]):
            if not include_custom_properties:
                continue
            if str(prop["id"]) in custom_properties:
                continue
            custom_properties.append(prop["id"])
        # Built-in Property
        else:
            # Hop to sub object property
            if prop["type"] == "items":
                for val in object_display(
                    TYPE_OBJECT_ITEM, workspace, include_custom_properties=True
                ):
                    builtin_properties.append(f"{TYPE_OBJECT_ITEM}|{val}")
            else:
                builtin_properties.append(prop["id"])

    for prop_ in reversed(default_properties):
        if prop_ in builtin_properties:
            continue
        builtin_properties = [prop_] + builtin_properties

    return builtin_properties + custom_properties


def get_default_prompt_set(workspace, target, CustomFieldName=None):
    get_default_property_set(target, workspace, lang="en")

    condition_filter = Q(workspace=workspace, target=target)
    condition_filter &= Q(as_default=True) | Q(name__isnull=True)
    property_set = PropertySet.objects.filter(condition_filter).first()
    properties = {"list_all": []}

    if property_set:
        if property_set.children:
            for p in property_set.children:
                properties["list_all"].append(p)

    if not properties["list_all"]:
        _, properties = get_properties_from_set(None, target, workspace)

    CustomFieldMap = {}
    if CustomFieldName:
        explorecustomfield = CustomFieldName.objects.filter(
            workspace=workspace
        ).order_by("order")
        for ctf in explorecustomfield:
            CustomFieldMap[str(ctf.id)] = ctf

    return properties, CustomFieldMap


def url_name_by_object_type(object_type):
    try:
        return OBJECT_TYPE_TO_URL_NAME[object_type]
    except:
        return object_type


def get_field(base_model, base_columns, field_types: list):
    """
    Get the field names of the base model that match the specified field types.
    """
    properties_dict = {
        field.name: field.get_internal_type()
        for field in base_model._meta.fields
        if field.name in base_columns
    }
    res_properties = list(
        {k: v for k, v in properties_dict.items() if v in field_types}.keys()
    )
    return res_properties


def get_field_data_type(model_name, field):
    """
    Determine the data type of a field in a given model.

    Parameters:
    - model_name (str): The name of the model to check.
    - field (str): The name of the field to determine the data type for.

    Returns:
    - str or None: The data type of the field if it exists, otherwise None.
    """
    model_class = apps.get_model("data", model_name)
    if field not in [f.name for f in model_class._meta.fields]:
        return None
    return model_class._meta.get_field(field).get_internal_type()


def populate_object_display_context(request, workspace, context):
    """
    Populate the object display context with necessary information for rendering the object display page.

    This function prepares the context dictionary with the following information:
    - Workspace: The current workspace of the user.
    - Language: The language code of the current user's session.
    - Page Group Type: The type of page group being displayed (e.g., components, bill_objects, etc.).
    - Columns: A list of columns to be displayed for the page group type.
    - Object Manager: The ObjectManager instance associated with the workspace and page group type.
    - Object Display Options: A list of display options for the object, including custom properties.
    - Field ID: A dictionary containing the ID and name of the field to be used for identification.

    :param request: The current request object.
    :param context: The context dictionary to be populated.
    """
    lang = request.LANGUAGE_CODE
    columns = []
    object_manager = None
    page_group_type = request.GET.get("setting_type")
    custom_object = context["custom_object"]
    if custom_object:
        page_group_type = TYPE_OBJECT_CUSTOM_OBJECT
        object_manager = ObjectManager.objects.filter(
            workspace=workspace,
            custom_object=custom_object,
            page_group_type=page_group_type,
        ).first()
        columns = object_display(page_group_type, workspace)
    else:
        columns = object_display(page_group_type, workspace)
        object_manager = ObjectManager.objects.filter(
            workspace=workspace, page_group_type=page_group_type
        ).first()

    object_display_options = []
    page_obj = get_page_object(page_group_type, lang)
    custom_prop_model = page_obj["custom_model"]
    field_id = {}
    for col in columns:
        if is_valid_uuid(col):
            if custom_object:
                custom_prop = custom_prop_model.objects.filter(
                    id=col, workspace=workspace, custom_object=custom_object
                ).first()
            else:
                custom_prop = custom_prop_model.objects.filter(
                    id=col, workspace=workspace
                ).first()
            if custom_prop:
                object_display_options.append({"id": col, "name": custom_prop.name})
        elif "line_item" in col:
            pass
        elif "|" in col:
            col_split = col.split("|")

            if len(col_split) > 2:
                continue
            else:
                parent_obj = col_split[0]
                col_ = col_split[1]

            print(f"DEBUG POPULATE_CONTEXT: Processing pipe column: {col_split}")
            print(f"DEBUG POPULATE_CONTEXT: parent_obj={parent_obj}, col_={col_}")

            parent_obj_display = OBJECT_GROUP_TYPE_SINGULAR[parent_obj][lang]
            page_obj_ = get_page_object(parent_obj, lang)
            col_display = col_
            if col_ in page_obj_["columns_display"]:
                col_display = page_obj_["columns_display"][col_][lang]
            elif is_valid_uuid(col_):
                custom_prop = (
                    page_obj_["custom_model"]
                    .objects.filter(id=col_, workspace=workspace)
                    .first()
                )
                if custom_prop:
                    col_display = custom_prop.name
            object_display_options.append(
                {"id": col, "name": parent_obj_display + " - " + col_display}
            )
        else:
            col_display = col
            if col in page_obj["columns_display"]:
                col_display = page_obj["columns_display"][col][lang]
            object_display_options.append({"id": col, "name": col_display})

            if col == page_obj["id_field"]:
                field_id["id"] = col
                field_id["name"] = col_display

    # default display settings
    exclude_fields = []
    if page_group_type == TYPE_OBJECT_CASE:
        default_display_setting = [page_obj["id_field"], "name"]
        exclude_fields = ["estimate", "invoice", "tasks"]
    else:
        default_display_setting = [page_obj["id_field"]]

    object_display_options = [
        field for field in object_display_options if field["id"] not in exclude_fields
    ]
    if object_manager:
        if not object_manager.column_display:
            object_manager.column_display = ",".join(default_display_setting)
            object_manager.save()
    else:
        if custom_object:
            object_manager, _ = ObjectManager.objects.get_or_create(
                workspace=workspace,
                custom_object=custom_object,
                page_group_type=TYPE_OBJECT_CUSTOM_OBJECT,
            )
        else:
            object_manager, _ = ObjectManager.objects.get_or_create(
                workspace=workspace, page_group_type=page_group_type
            )
        object_manager.column_display = ",".join(default_display_setting)
        object_manager.save()

    context["object_manager"] = object_manager
    context["object_display_options"] = object_display_options
    context["field_id"] = field_id  # not being used
    return context


def get_prefix_rel(source, dest):
    prefix = ""

    ### ORDER OBJECT RELATION ============================================================
    if source == TYPE_OBJECT_ORDER:
        if dest == TYPE_OBJECT_INVOICE:
            prefix = "invoice__"

    ### INVOICE OBJECT RELATION ==========================================================
    if source == TYPE_OBJECT_INVOICE:
        if dest == TYPE_OBJECT_ORDER:
            prefix = "shopturboorders__"
    if source == TYPE_OBJECT_ITEM and dest == TYPE_OBJECT_ORDER:
        prefix = "shopturbo_item_orders__"
    if source == TYPE_OBJECT_ORDER and dest == TYPE_OBJECT_ITEM:
        prefix = "shopturboitemsorders__item__"
    if source == TYPE_OBJECT_ORDER and dest == TYPE_OBJECT_INVENTORY_TRANSACTION:
        prefix = "inventory_transactions__"
    if (
        source == TYPE_OBJECT_COMPANY or source == TYPE_OBJECT_CONTACT
    ) and dest == TYPE_OBJECT_INVOICE:
        prefix = "invoice__"
    if (
        source == TYPE_OBJECT_COMPANY or source == TYPE_OBJECT_CONTACT
    ) and dest == TYPE_OBJECT_ORDER:
        prefix = "shopturboorders__"
    if (
        source == TYPE_OBJECT_COMPANY or source == TYPE_OBJECT_CONTACT
    ) and dest == TYPE_OBJECT_ESTIMATE:
        prefix = "estimate__"
    if source == TYPE_OBJECT_ITEM and dest == TYPE_OBJECT_ORDER_LINE_ITEM:
        prefix = "shopturbo_item_orders__"
    if source == TYPE_OBJECT_ITEM and dest == TYPE_OBJECT_ORDER:
        prefix = "shopturbo_item_orders__order__"

    return prefix
