# Generated by Django 4.2.20 on 2025-07-18 14:31

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('data', '3014_alter_applog_target'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommerceMeterValueFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=256, null=True)),
                ('file', models.FileField(upload_to='commerce-meter-field-files', verbose_name='Commerce Meter Field file')),
                ('media_type', models.CharField(choices=[('image', 'Image'), ('video', 'Video')], default='image', max_length=50)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('valuecustomfield', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='data.commercemetervaluecustomfield')),
            ],
        ),
    ]
