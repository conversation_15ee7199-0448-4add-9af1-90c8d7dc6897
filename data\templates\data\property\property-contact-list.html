{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}


<div class="mb-5 sub-section">
    <div class="w-100 d-flex justify-content-center align-items-center cursor-grab z-index-1 position-relative task-grip">

        <input required class="sub-choice-label form-control d-flex w-100 h-100 me-2" id="choice-label-{{forloop.counter}}" name="contact_list_name"
            {% if LANGUAGE_CODE == 'ja'%}
            placeholder="連絡先リスト" 
            {% else %}
            placeholder="Contact List" 
            {% endif %}
            value = '{{choice.name}}'
        />
        
        {% if not property.id == 'counter_category' %}
        <button class="btn btn-danger btn-sm ms-1" onclick='$(this).parent().remove()' type="button">X</button>
        {% endif %}
    </div>

    <input type="hidden" name="contact_list_id" value="{{choice.id}}">
    
    <label class="warning-label" style="display: none; color: red;"></label>
</div>
