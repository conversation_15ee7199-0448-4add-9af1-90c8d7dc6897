import ast
import logging
import time
import uuid

from django.db import connection
from django.db import models
from django.shortcuts import redirect, render
from django_hosts.resolvers import reverse

from data.constants.constant import (
    COMPANY_COLUMNS_DISPLAY,
    CONTACTS_COLUMNS_DISPLAY,
    DEFAULT_PERMISSION,
    D<PERSON>PLAY_COLUMNS_BILL,
    HOL<PERSON>AY_COUNTRY_CODE,
    INVOICE_COLUMNS_DISPLAY,
    LINE_ITEM_DEFAULT,
    OBJECTS_CF_TYPE,
    OBJECT_GROUP_TYPE,
    ORDERS_COLUMNS_DISPLAY,
    PURCHASE_ORDER_COLUMNS_DISPLAY,
    SEARCH_COLUMNS_DISPLAY,
    SHOPTURBO_NUMBER_FORMAT,
    SUBSCRIPTIONS_COLUMNS_DISPLAY,
)
from data.constants.properties_constant import (
    TYPE_OBJECT_BILL,
    TYPE_OBJECT_CAMPAIGN,
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_COMMERCE_METER,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_CONTACT,
    TYPE_OBJECT_CONTRACT,
    TYPE_OBJECT_CONVERSATION,
    TYPE_OBJECT_CUSTOM_OBJECT,
    TYPE_OBJECT_DASHBOARD,
    TYPE_OBJECT_DELIVERY_NOTE,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_EXPENSE,
    TYPE_OBJECT_INVENTORY,
    TYPE_OBJECT_INVENTORY_TRANSACTION,
    TYPE_OBJECT_INVENTORY_WAREHOUSE,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_ITEM,
    TYPE_OBJECT_JOBS,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_PANEL,
    TYPE_OBJECT_PURCHASE_ORDER,
    TYPE_OBJECT_RECEIPT,
    TYPE_OBJECT_SLIP,
    TYPE_OBJECT_SUBSCRIPTION,
    TYPE_OBJECT_USER_MANAGEMENT,
)
from data.models import (
    AppSetting,
    Bill,
    Company,
    CompanyNameCustomField,
    Contact,
    ContactList,
    ContactsNameCustomField,
    CustomObject,
    CustomizePdfTemplate,
    DefaultBarcodeProperty,
    Group,
    InventoryTransaction,
    InventoryTransactionNameCustomField,
    InventoryWarehouse,
    InventoryWarehouseNameCustomField,
    Invoice,
    JobScore,
    Log,
    PdfTemplate,
    Projects,
    PurchaseOrders,
    ReportPanel,
    ShopTurboDecimalPoint,
    ShopTurboInventory,
    ShopTurboInventoryNameCustomField,
    ShopTurboItems,
    ShopTurboItemsDiscount,
    ShopTurboItemsNameCustomField,
    ShopTurboOrderLevelTax,
    ShopTurboOrders,
    ShopTurboOrdersNameCustomField,
    ShopTurboShippingCost,
    ShopTurboSubscriptions,
    ShopTurboSubscriptionsNameCustomField,
    Task,
    TaskCustomFieldName,
    UserManagement,
    Workspace,
)
from data.models.base import BARCODE_TYPES, QRCODE
from data.shopturbo import SHOPTURBO_APP_SLUG, SHOPTURBO_APP_TARGET
from utils.decorator import login_or_hubspot_required
from utils.properties.properties import (
    get_field,
    get_list_object_columns,
    get_page_object,
    get_properties_with_details,
    populate_object_display_context,
)
from utils.utility import get_workspace, is_valid_uuid, natural_sort_key
from utils.workspace import get_permission

# Performance monitoring logger
perf_logger = logging.getLogger("workspace_settings_performance")


@login_or_hubspot_required
def workspace_setting(request):
    # Performance monitoring - start timing
    start_time = time.time()
    initial_query_count = len(connection.queries)

    # Log request details
    setting_type = request.GET.get("setting_type", "module")
    perf_logger.info(
        f"[PERF] Starting workspace_setting view - setting_type: {setting_type}, user: {request.user.email if hasattr(request.user, 'email') else 'unknown'}"
    )

    lang = request.LANGUAGE_CODE
    if lang == "ja":
        page_title = "ワークスペース設定"
    else:
        page_title = "Workspace Settings"
    menu_key = "workspace"
    page_group_type = "workspace_setting"

    workspace = get_workspace(request.user)

    # Performance checkpoint 1
    checkpoint_1_time = time.time()
    checkpoint_1_queries = len(connection.queries)
    perf_logger.info(
        f"[PERF] Checkpoint 1 - Basic setup: {(checkpoint_1_time - start_time) * 1000:.2f}ms, queries: {checkpoint_1_queries - initial_query_count}"
    )

    if workspace:
        workspaces = Workspace.objects.filter(user=request.user).exclude(
            id=workspace.id
        )

    else:
        workspaces = None

    if request.method == "POST":
        if "edit_workspace" in request.POST:
            setting_type = request.POST.get("setting_type", "basic")
            input_name = request.POST.get("input_name")
            allowed_ip = request.POST.get("allowed_ip", None)
            if allowed_ip:
                allowed_ip = ast.literal_eval(allowed_ip)
                allowed_ip = ",".join([ip["value"] for ip in allowed_ip])
            else:
                allowed_ip = None

            workspace.name = input_name
            workspace.allowed_ip = allowed_ip
            workspace.save()

            # Decimal Point - unified setting for all objects
            decimal_format = request.POST.get("decimal_format", None)

            # Get or create the general decimal point setting
            decimal_point, _ = ShopTurboDecimalPoint.objects.get_or_create(
                workspace=workspace, app_name="general"
            )

            if decimal_point:
                if decimal_format != "normal" and decimal_format:
                    decimal_point.type = decimal_format
                    decimal_point.save()
                else:
                    decimal_point.type = None
                    decimal_point.save()
            # Update all existing app-specific decimal points to match the general setting
            ShopTurboDecimalPoint.objects.filter(workspace=workspace).exclude(
                app_name="general"
            ).update(type=decimal_point.type)

            country_code = request.POST.get("holiday-country", None)
            workspace.country_code = country_code
            workspace.save()

            return redirect(reverse("workspace_basic_info", host="app"))

    # NOTE: GET Method Page
    setting_type = request.GET.get("setting_type", "module")
    custom_object_slug = request.GET.get("custom_object", None)
    custom_object = None

    # Project
    p_id = request.GET.get("p_id", None)
    project = None
    projects = None
    permission = None

    object_managers = []
    columns = {}

    # Performance checkpoint 2
    checkpoint_2_time = time.time()
    checkpoint_2_queries = len(connection.queries)
    perf_logger.info(
        f"[PERF] Checkpoint 2 - POST handling complete: {(checkpoint_2_time - start_time) * 1000:.2f}ms, queries: {checkpoint_2_queries - initial_query_count}"
    )

    if setting_type == "task":
        print("setting type is task")
        # Get Project List
        projects = Projects.objects.filter(workspace=workspace).order_by("created_at")
        if not projects:
            tasks = Task.objects.filter(workspace=workspace)
            task_csfs = TaskCustomFieldName.objects.filter(workspace=workspace)
            if tasks:
                project, _ = Projects.objects.get_or_create(
                    workspace=workspace, title=""
                )
                p_id = project.id
                for task in tasks:
                    task.project_target = project
                    task.save()
            if task_csfs:
                project, _ = Projects.objects.get_or_create(
                    workspace=workspace, title=""
                )
                p_id = project.id
                for task_csf in task_csfs:
                    task_csf.project_target = project
                    task_csf.save()
        else:
            if p_id:
                project = Projects.objects.filter(id=p_id).first()

            if not project:
                project = projects.first()

        permission = get_permission(object_type="task", user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        Log.objects.create(
            workspace=workspace,
            user=request.user,
            page_name="workspace",
            sub_page_name="settings",
        )

        context = {
            "menu_key": menu_key,
            "page_title": page_title,
            "workspace": workspace,
            "workspaces": workspaces,
            "COUNTRY_CODE": HOLIDAY_COUNTRY_CODE,
            "page_group_type": page_group_type,
            "page_type": "object_manager",
            "setting_type": setting_type,
            # Project
            "p_id": p_id,
            "project": project,
            "projects": projects,
            "permission": permission,
            "object_managers": object_managers,
            "columns": columns,
        }
        return render(
            request, "data/account/workspace/workspace_template.html", context
        )

    custom_field_mode = request.GET.get("custom_field_mode")
    if custom_field_mode == "individual-items":
        customfield_id = request.GET.get("CustomFieldName_id", None)
        items_custom_field_type = request.GET.get("items_custom_field_type", None)
        checking_valid_uuid = True

        items_customfield = ShopTurboItemsNameCustomField.objects.filter(
            id=customfield_id
        ).first()
        if items_customfield:
            if items_custom_field_type:
                items_customfield.type = items_custom_field_type
        else:
            if not items_custom_field_type:
                items_custom_field_type = "text"
            items_customfield = {"id": uuid.uuid4(), "type": items_custom_field_type}
            checking_valid_uuid = False

        shopturbo_type = [
            cf_type for cf_type in OBJECTS_CF_TYPE if cf_type[0] != "formula"
        ]
        shopturbo_default_item_column = [
            str(v.name) for v in ShopTurboItems._meta.fields
        ]
        for column in ["product_id"]:
            shopturbo_default_item_column.remove(column)
        context = {
            "items_customfield": items_customfield,
            "OBJECTS_CF_TYPES": shopturbo_type,
            "number_formats": SHOPTURBO_NUMBER_FORMAT,
            "checking_valid_uuid": checking_valid_uuid,
            "shopturbo_default_item_column": shopturbo_default_item_column,
        }
        return render(
            request, "data/shopturbo/shopturbo-items-custom-field.html", context
        )

    elif custom_field_mode == "individual-inventory":
        customfield_id = request.GET.get("CustomFieldName_id", None)
        inventory_custom_field_type = request.GET.get(
            "inventory_custom_field_type", None
        )
        checking_valid_uuid = True

        inventory_customfield = ShopTurboInventoryNameCustomField.objects.filter(
            id=customfield_id
        ).first()
        if inventory_customfield:
            if inventory_custom_field_type:
                inventory_customfield.type = inventory_custom_field_type
        else:
            if not inventory_custom_field_type:
                inventory_custom_field_type = "text"
            inventory_customfield = {
                "id": uuid.uuid4(),
                "type": inventory_custom_field_type,
            }
            checking_valid_uuid = False

        shopturbo_type = [
            cf_type for cf_type in OBJECTS_CF_TYPE if cf_type[0] != "formula"
        ]
        shopturbo_default_inventory_column = [
            str(v.name) for v in ShopTurboInventory._meta.fields
        ]

        context = {
            "inventory_customfield": inventory_customfield,
            "OBJECTS_CF_TYPES": shopturbo_type,
            "number_formats": SHOPTURBO_NUMBER_FORMAT,
            "checking_valid_uuid": checking_valid_uuid,
            "shopturbo_default_inventory_column": shopturbo_default_inventory_column,
        }
        return render(
            request, "data/shopturbo/shopturbo-inventory-custom-field.html", context
        )

    elif custom_field_mode == "individual-transaction":
        customfield_id = request.GET.get("CustomFieldName_id", None)
        transaction_custom_field_type = request.GET.get(
            "inventory_custom_field_type", None
        )
        checking_valid_uuid = True

        transaction_customfield = InventoryTransactionNameCustomField.objects.filter(
            id=customfield_id
        ).first()
        if transaction_customfield:
            if transaction_custom_field_type:
                transaction_customfield.type = transaction_custom_field_type
        else:
            if not transaction_custom_field_type:
                transaction_custom_field_type = "text"
            transaction_customfield = {
                "id": uuid.uuid4(),
                "type": transaction_custom_field_type,
            }
            checking_valid_uuid = False

        shopturbo_type = [
            cf_type
            for cf_type in OBJECTS_CF_TYPE
            if cf_type[0] not in ["formula", "contact", "company", "invoice"]
        ]
        shopturbo_default_transaction_column = [
            str(v.name) for v in InventoryTransaction._meta.fields
        ]

        context = {
            "transaction_customfield": transaction_customfield,
            "OBJECTS_CF_TYPES": shopturbo_type,
            "number_formats": SHOPTURBO_NUMBER_FORMAT,
            "checking_valid_uuid": checking_valid_uuid,
            "shopturbo_default_transaction_column": shopturbo_default_transaction_column,
        }
        return render(
            request, "data/shopturbo/inventory-transaction-custom-field.html", context
        )

    elif custom_field_mode == "individual-warehouse":
        customfield_id = request.GET.get("CustomFieldName_id", None)
        warehouse_custom_field_type = request.GET.get(
            "inventory_custom_field_type", None
        )
        checking_valid_uuid = True

        warehouse_customfield = InventoryWarehouseNameCustomField.objects.filter(
            id=customfield_id
        ).first()
        if warehouse_customfield:
            if warehouse_custom_field_type:
                warehouse_customfield.type = warehouse_custom_field_type
        else:
            if not warehouse_custom_field_type:
                warehouse_custom_field_type = "text"
            warehouse_customfield = {
                "id": uuid.uuid4(),
                "type": inventory_custom_field_type,
            }
            checking_valid_uuid = False

        shopturbo_type = [
            cf_type for cf_type in OBJECTS_CF_TYPE if cf_type[0] != "formula"
        ]
        shopturbo_default_warehouse_column = [
            str(v.name) for v in InventoryWarehouse._meta.fields
        ]

        context = {
            "warehouse_customfield": warehouse_customfield,
            "OBJECTS_CF_TYPES": shopturbo_type,
            "number_formats": SHOPTURBO_NUMBER_FORMAT,
            "checking_valid_uuid": checking_valid_uuid,
            "shopturbo_default_warehouse_column": shopturbo_default_warehouse_column,
        }
        return render(
            request, "data/shopturbo/inventory-warehouse-custom-field.html", context
        )

    elif custom_field_mode == "individual-orders":
        customfield_id = request.GET.get("CustomFieldName_id", None)
        orders_custom_field_type = request.GET.get("orders_custom_field_type", None)
        checking_valid_uuid = True

        orders_customfield = ShopTurboOrdersNameCustomField.objects.filter(
            id=customfield_id
        ).first()
        if orders_customfield:
            if orders_custom_field_type:
                orders_customfield.type = orders_custom_field_type

            if orders_customfield.type == "formula":
                choice_values = ast.literal_eval(orders_customfield.choice_value)
                for choice_value in choice_values:
                    # Checking ID custom Field
                    if is_valid_uuid(choice_value["value"]):
                        custom_field_name = (
                            ShopTurboOrdersNameCustomField.objects.filter(
                                id=choice_value["value"]
                            ).first()
                        )
                        if custom_field_name:
                            choice_value["display"] = custom_field_name.name

                choice_values = str(choice_values)
                orders_customfield.choice_value = choice_values

        else:
            if not orders_custom_field_type:
                orders_custom_field_type = "text"
            orders_customfield = {"id": uuid.uuid4(), "type": orders_custom_field_type}
            checking_valid_uuid = False

        context = {
            "orders_customfield": orders_customfield,
            "OBJECTS_CF_TYPES": OBJECTS_CF_TYPE,
            "number_formats": SHOPTURBO_NUMBER_FORMAT,
            "BillObjects": Bill.objects.filter(workspace=workspace),
            "InvoiceObjects": Invoice.objects.filter(workspace=workspace),
            "OrdersNameCustomField": ShopTurboOrdersNameCustomField.objects.filter(
                workspace=workspace
            ).order_by("order"),
            "checking_valid_uuid": checking_valid_uuid,
            "shopturbo_default_orders_column": [
                str(v.name).lower() for v in ShopTurboOrders._meta.fields
            ],
        }

        return render(
            request, "data/shopturbo/shopturbo-orders-custom-field.html", context
        )

    elif custom_field_mode == "individual-subscriptions":
        customfield_id = request.GET.get("CustomFieldName_id", None)
        subs_custom_field_type = request.GET.get(
            "subscriptions_custom_field_type", None
        )
        checking_valid_uuid = True

        subscriptions_customfield = (
            ShopTurboSubscriptionsNameCustomField.objects.filter(
                id=customfield_id
            ).first()
        )
        if subscriptions_customfield:
            if subs_custom_field_type:
                subscriptions_customfield.type = subs_custom_field_type
        else:
            if not subs_custom_field_type:
                subs_custom_field_type = "text"
            subscriptions_customfield = {
                "id": uuid.uuid4(),
                "type": subs_custom_field_type,
            }
            checking_valid_uuid = False

        shopturbo_type = [
            cf_type for cf_type in OBJECTS_CF_TYPE if cf_type[0] != "formula"
        ]
        context = {
            "subscriptions_customfield": subscriptions_customfield,
            "OBJECTS_CF_TYPES": shopturbo_type,
            "number_formats": SHOPTURBO_NUMBER_FORMAT,
            "checking_valid_uuid": checking_valid_uuid,
            "shopturbo_default_subscriptions_column": [
                str(v.name).lower() for v in ShopTurboSubscriptions._meta.fields
            ],
        }
        return render(
            request, "data/shopturbo/shopturbo-subscriptions-custom-field.html", context
        )

    else:
        page_object = None
        if not custom_object_slug:
            column_values = []
            page_object = get_page_object(setting_type)
            app_target = page_object["app_target"]
            if app_target:
                app_setting, _ = AppSetting.objects.get_or_create(
                    workspace=workspace, app_target=app_target
                )
            else:
                app_setting, _ = AppSetting.objects.get_or_create(
                    workspace=workspace, app_target=SHOPTURBO_APP_TARGET
                )

            # Ensure default PDF templates for Shopturbo if not set
            if app_setting.app_target == SHOPTURBO_APP_TARGET:
                if not app_setting.order_pdf_template:
                    app_setting.order_pdf_template = "data/templates/data/shopturbo/orders/pdf/orderPDF-pattern-1.html"
                if not app_setting.item_pdf_template:
                    app_setting.item_pdf_template = (
                        "data/templates/data/shopturbo/items/pdf/itemPDF-pattern-1.html"
                    )

            default_barcode_property = None
            barcode_column_values = []
            supplier_properties = []

            # Handle case where setting_type is not a valid key in OBJECT_GROUP_TYPE
            if setting_type in OBJECT_GROUP_TYPE:
                object_name = OBJECT_GROUP_TYPE[setting_type][lang]
            else:
                # Default to a generic name if setting_type is not found
                object_name = "Settings" if lang == "en" else "設定"
            custom_object = None
        else:
            custom_object = CustomObject.objects.filter(
                workspace=workspace, slug=custom_object_slug
            ).first()
            app_setting, _ = AppSetting.objects.get_or_create(
                workspace=workspace,
                app_target="custom_object",
                custom_object=custom_object,
            )

        if setting_type == TYPE_OBJECT_BILL:
            # To Set Invoice and estimate stamp data
            INVOICE_APP_TARGET = "taskflow"
            app_setting_invoice_estimate, _ = AppSetting.objects.get_or_create(
                workspace=workspace, app_target=INVOICE_APP_TARGET
            )
            if app_setting_invoice_estimate.estimate_stamp_file:
                app_setting.estimate_stamp_file = (
                    app_setting_invoice_estimate.estimate_stamp_file
                )
                app_setting_invoice_estimate.estimate_stamp_file = None
                app_setting.save()
                app_setting_invoice_estimate.save()
            if app_setting_invoice_estimate.estimate_logo_file:
                app_setting.estimate_logo_file = (
                    app_setting_invoice_estimate.estimate_logo_file
                )
                app_setting_invoice_estimate.estimate_logo_file = None
                app_setting.save()
                app_setting_invoice_estimate.save()
            if app_setting_invoice_estimate.invoice_stamp_file:
                app_setting.invoice_stamp_file = (
                    app_setting_invoice_estimate.invoice_stamp_file
                )
                app_setting_invoice_estimate.invoice_stamp_file = None
                app_setting.save()
                app_setting_invoice_estimate.save()
            if app_setting_invoice_estimate.invoice_logo_file:
                app_setting.invoice_logo_file = (
                    app_setting_invoice_estimate.invoice_logo_file
                )
                app_setting_invoice_estimate.invoice_logo_file = None
                app_setting.save()
                app_setting_invoice_estimate.save()

            properties = get_properties_with_details(
                TYPE_OBJECT_BILL, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

            column_values = column_values + [
                "contact__" + val
                for val in get_field(
                    Contact,
                    get_list_object_columns(Contact, excludes=["created_at", "url"]),
                    ["CharField"],
                )
            ]
            column_values = column_values + [
                "company__" + val
                for val in get_field(
                    Company,
                    get_list_object_columns(Company, excludes=["created_at"]),
                    ["CharField"],
                )
            ]
            contact_custom_fields = ContactsNameCustomField.objects.filter(
                workspace=workspace, type="text"
            )
            for cf in contact_custom_fields:
                column_values.append(
                    "contact_custom_field_relations__value__|" + str(cf.id)
                )

            custom_fields = CompanyNameCustomField.objects.filter(
                workspace=workspace, type="text"
            )
            for cf in custom_fields:
                column_values.append(
                    "company_custom_field_relations__value__|" + str(cf.id)
                )

            custom_fields = ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, type="text"
            )
            for cf in custom_fields:
                column_values.append(
                    "shopturbo_item_custom_field_relations__value__|" + str(cf.id)
                )

            customer_company_custom_fields = (
                ShopTurboOrdersNameCustomField.objects.filter(
                    workspace=workspace, type__in=["contact", "company"]
                )
            )
            if customer_company_custom_fields:
                column_values.extend(
                    [str(cf.id) for cf in customer_company_custom_fields]
                )

        elif setting_type == TYPE_OBJECT_ITEM:
            properties = get_properties_with_details(
                TYPE_OBJECT_ITEM, workspace, lang, searchable_only=True
            )
            exclude_column = [
                "inventory__total_inventory",
                "inventory__available_amount",
                "inventory__unavailable_amount",
                "inventory__committed_amount",
            ]
            column_values = [
                str(p["id"]) for p in properties if str(p["id"]) not in exclude_column
            ]

        elif setting_type == TYPE_OBJECT_ORDER:
            # Performance monitoring - start TYPE_OBJECT_ORDER section
            order_start_time = time.time()
            order_start_queries = len(connection.queries)
            perf_logger.info(
                f"[PERF] Starting TYPE_OBJECT_ORDER section: {(order_start_time - start_time) * 1000:.2f}ms total"
            )

            properties = get_properties_with_details(
                TYPE_OBJECT_ORDER, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

            # Performance checkpoint - after get_properties_with_details
            after_properties_time = time.time()
            after_properties_queries = len(connection.queries)
            perf_logger.info(
                f"[PERF] After get_properties_with_details: {(after_properties_time - order_start_time) * 1000:.2f}ms, queries: {after_properties_queries - order_start_queries}"
            )

            column_values = column_values + [
                "contact__" + val
                for val in get_field(
                    Contact,
                    get_list_object_columns(Contact, excludes=["created_at", "url"]),
                    ["CharField"],
                )
            ]
            column_values.append("contact__first_name")
            if "contact__company" in column_values:
                column_values.remove("contact__company")
            if "contact__image_url" in column_values:
                column_values.remove("contact__image_url")

            column_values = column_values + [
                "company__" + val
                for val in get_field(
                    Company,
                    get_list_object_columns(Company, excludes=["created_at"]),
                    ["CharField"],
                )
            ]

            # Optimize custom fields queries by batching them
            custom_fields_start_time = time.time()
            custom_fields_start_queries = len(connection.queries)

            contact_custom_fields = ContactsNameCustomField.objects.filter(
                workspace=workspace, type="text"
            ).only("id")
            for cf in contact_custom_fields:
                column_values.append(
                    "contact_custom_field_relations__value__|" + str(cf.id)
                )

            company_custom_fields = CompanyNameCustomField.objects.filter(
                workspace=workspace, type="text"
            ).only("id")
            for cf in company_custom_fields:
                column_values.append(
                    "company_custom_field_relations__value__|" + str(cf.id)
                )

            items_custom_fields = ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, type="text"
            ).only("id")
            for cf in items_custom_fields:
                column_values.append(
                    "shopturbo_item_custom_field_relations__value__|" + str(cf.id)
                )

            customer_company_custom_fields = (
                ShopTurboOrdersNameCustomField.objects.filter(
                    workspace=workspace, type__in=["contact", "company"]
                ).only("id")
            )
            if customer_company_custom_fields:
                column_values.extend(
                    [str(cf.id) for cf in customer_company_custom_fields]
                )

            # Performance checkpoint - after custom fields
            after_custom_fields_time = time.time()
            after_custom_fields_queries = len(connection.queries)
            perf_logger.info(
                f"[PERF] After custom fields queries: {(after_custom_fields_time - custom_fields_start_time) * 1000:.2f}ms, queries: {after_custom_fields_queries - custom_fields_start_queries}"
            )
            perf_logger.info(
                f"[PERF] TYPE_OBJECT_ORDER section complete: {(after_custom_fields_time - order_start_time) * 1000:.2f}ms total"
            )

            if app_setting.search_setting_order:
                if "total_price" in app_setting.search_setting_order:
                    app_setting.search_setting_order = (
                        app_setting.search_setting_order.replace(",total_price", "")
                    )
                    app_setting.save()
        elif setting_type == TYPE_OBJECT_CAMPAIGN:
            properties = get_properties_with_details(
                TYPE_OBJECT_CAMPAIGN, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

        elif setting_type == TYPE_OBJECT_CONTACT:
            properties = get_properties_with_details(
                TYPE_OBJECT_CONTACT, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties] + page_object[
                "additional_filter_fields"
            ]

        elif setting_type == TYPE_OBJECT_COMPANY:
            properties = get_properties_with_details(
                TYPE_OBJECT_COMPANY, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

        elif setting_type == TYPE_OBJECT_SUBSCRIPTION:
            properties = get_properties_with_details(
                TYPE_OBJECT_SUBSCRIPTION, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

            column_values.append("customer")

            # Check if fields exist before removing them to avoid ValueError
            if "prior_to_time" in column_values:
                column_values.remove("prior_to_time")

            if "shipping_cost_tax_status" in column_values:
                column_values.remove("shipping_cost_tax_status")

        elif setting_type == TYPE_OBJECT_INVENTORY:
            properties = get_properties_with_details(
                TYPE_OBJECT_INVENTORY, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]
            column_values.extend(["inventory_id", "item__name"])
            ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, type="text"
            )
            for cf in ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, type="text"
            ):
                column_values.append(
                    "item__shopturbo_item_custom_field_relations__value|" + str(cf.id)
                )

        elif setting_type == TYPE_OBJECT_INVENTORY_TRANSACTION:
            properties = get_properties_with_details(
                TYPE_OBJECT_INVENTORY_TRANSACTION, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]
            column_values.extend(["transaction_id"])
            column_values.extend(["inventory_id"])

            # Add inventory custom properties using pipe syntax
            inventory_custom_fields = ShopTurboInventoryNameCustomField.objects.filter(
                workspace=workspace
            )
            print(
                f"DEBUG WORKSPACE_SETTING: Found {inventory_custom_fields.count()} inventory custom fields"
            )
            for cf in inventory_custom_fields:
                column_option = f"commerce_inventory|{cf.id}"
                column_values.append(column_option)
                print(
                    f"DEBUG WORKSPACE_SETTING: Added inventory custom field option: {column_option} (name: {cf.name})"
                )
            print(
                f"DEBUG WORKSPACE_SETTING: Total column_values count: {len(column_values)}"
            )
            print(f"DEBUG WORKSPACE_SETTING: Final column_values: {column_values}")

        elif setting_type == TYPE_OBJECT_INVENTORY_WAREHOUSE:
            properties = get_properties_with_details(
                TYPE_OBJECT_INVENTORY_WAREHOUSE, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

        elif setting_type == TYPE_OBJECT_PURCHASE_ORDER:
            properties = get_properties_with_details(TYPE_OBJECT_ITEM, workspace, lang)
            supplier_properties = [
                prop for prop in properties if prop["type"] in ["contact", "company"]
            ]
            properties = get_properties_with_details(
                TYPE_OBJECT_PURCHASE_ORDER, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

        elif setting_type == TYPE_OBJECT_EXPENSE:
            properties = get_properties_with_details(
                TYPE_OBJECT_EXPENSE, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

            column_values = column_values + [
                "contact__" + val
                for val in get_field(
                    Contact,
                    get_list_object_columns(Contact, excludes=["created_at", "url"]),
                    ["CharField"],
                )
            ]
            column_values = column_values + [
                "company__" + val
                for val in get_field(
                    Company,
                    get_list_object_columns(Company, excludes=["created_at"]),
                    ["CharField"],
                )
            ]
            contact_custom_fields = ContactsNameCustomField.objects.filter(
                workspace=workspace, type="text"
            )
            for cf in contact_custom_fields:
                column_values.append(
                    "contact_custom_field_relations__value__|" + str(cf.id)
                )

            custom_fields = CompanyNameCustomField.objects.filter(
                workspace=workspace, type="text"
            )
            for cf in custom_fields:
                column_values.append(
                    "company_custom_field_relations__value__|" + str(cf.id)
                )

            custom_fields = ShopTurboItemsNameCustomField.objects.filter(
                workspace=workspace, type="text"
            )
            for cf in custom_fields:
                column_values.append(
                    "shopturbo_item_custom_field_relations__value__|" + str(cf.id)
                )

            customer_company_custom_fields = (
                ShopTurboOrdersNameCustomField.objects.filter(
                    workspace=workspace, type__in=["contact", "company"]
                )
            )
            if customer_company_custom_fields:
                column_values.extend(
                    [str(cf.id) for cf in customer_company_custom_fields]
                )

        elif setting_type == TYPE_OBJECT_CONVERSATION:
            app_setting = AppSetting.objects.get(
                workspace=workspace, app_target="contacts"
            )

            properties = get_properties_with_details(
                TYPE_OBJECT_CONVERSATION, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

            # Hard coded due to subject and body in the same field
            column_values += [
                "last_message_body",
                "last_message_contact_name",
                "last_message_contact_email",
                "channel__name",
            ]

        elif setting_type == TYPE_OBJECT_JOBS or setting_type == "jobs":
            properties = get_properties_with_details(
                TYPE_OBJECT_JOBS, workspace, lang, searchable_only=True, excludes=[]
            )
            column_values = [str(p["id"]) for p in properties]

        elif setting_type == TYPE_OBJECT_CONTRACT:
            properties = get_properties_with_details(
                TYPE_OBJECT_CONTRACT, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

        elif setting_type == TYPE_OBJECT_PANEL:
            properties = get_properties_with_details(
                TYPE_OBJECT_PANEL, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

        elif setting_type == TYPE_OBJECT_DASHBOARD:
            properties = get_properties_with_details(
                TYPE_OBJECT_DASHBOARD, workspace, lang, searchable_only=True
            )
            column_values = [
                str(p["id"]) for p in properties if p["id"] not in ["panels"]
            ]
            column_values = column_values + [
                "panelreportpanel__panel__" + val
                for val in get_field(
                    ReportPanel,
                    get_list_object_columns(
                        ReportPanel,
                        excludes=[
                            "created_at",
                            "data_source_type",
                            "data_source",
                            "sheet_rows",
                            "group_by",
                            "date_format",
                            "hide_rows",
                            "hide_cols",
                        ],
                    ),
                    ["CharField"],
                )
            ]

        elif setting_type == TYPE_OBJECT_USER_MANAGEMENT:
            properties = get_properties_with_details(
                TYPE_OBJECT_USER_MANAGEMENT, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

        elif setting_type == TYPE_OBJECT_INVOICE:
            # Performance optimization - specific handler for invoices
            invoice_start_time = time.time()
            invoice_start_queries = len(connection.queries)
            perf_logger.info(
                f"[PERF] Starting TYPE_OBJECT_INVOICE section: {(invoice_start_time - start_time) * 1000:.2f}ms total"
            )

            properties = get_properties_with_details(
                TYPE_OBJECT_INVOICE, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

            # Performance checkpoint
            after_invoice_time = time.time()
            after_invoice_queries = len(connection.queries)
            perf_logger.info(
                f"[PERF] TYPE_OBJECT_INVOICE section complete: {(after_invoice_time - invoice_start_time) * 1000:.2f}ms, queries: {after_invoice_queries - invoice_start_queries}"
            )

        elif setting_type == TYPE_OBJECT_DELIVERY_NOTE:
            # Performance optimization - specific handler for delivery notes
            delivery_start_time = time.time()
            delivery_start_queries = len(connection.queries)
            perf_logger.info(
                f"[PERF] Starting TYPE_OBJECT_DELIVERY_NOTE section: {(delivery_start_time - start_time) * 1000:.2f}ms total"
            )

            properties = get_properties_with_details(
                TYPE_OBJECT_DELIVERY_NOTE, workspace, lang, searchable_only=True
            )
            column_values = [str(p["id"]) for p in properties]

            # Performance checkpoint
            after_delivery_time = time.time()
            after_delivery_queries = len(connection.queries)
            perf_logger.info(
                f"[PERF] TYPE_OBJECT_DELIVERY_NOTE section complete: {(after_delivery_time - delivery_start_time) * 1000:.2f}ms, queries: {after_delivery_queries - delivery_start_queries}"
            )

        if custom_object_slug:
            # Setup Related Value for Custom Object
            object_name = None
            default_barcode_property = None
            barcode_column_values = None
            customize_template = None
            pl_customize_template = None
            supplier_properties = None
            setting_type = None

            properties = get_properties_with_details(
                TYPE_OBJECT_CUSTOM_OBJECT,
                workspace,
                lang,
                searchable_only=True,
                custom_object=custom_object,
            )
            column_values = [str(p["id"]) for p in properties]

        # add request.user to AppSetting
        if request.user not in app_setting.user.all():
            app_setting.user.add(request.user)
            app_setting.save()
        role = UserManagement.objects.filter(user=request.user, workspace=workspace)
        if role:
            role = UserManagement.objects.get(user=request.user, workspace=workspace)
        else:
            role, _ = UserManagement.objects.get_or_create(
                user=request.user,
                workspace=workspace,
                type=UserManagement.RoleType.STAFF,
            )
        groups = Group.objects.filter(workspace=workspace)

        if not custom_object_slug:
            if setting_type in [
                TYPE_OBJECT_ITEM,
                TYPE_OBJECT_ORDER,
                TYPE_OBJECT_INVENTORY,
                TYPE_OBJECT_INVENTORY_TRANSACTION,
            ]:
                pre_default_barcode_column = []
                barcode_column_values = [
                    "item_id",
                    "name",
                    "description",
                    "price",
                    "tax",
                    "status",
                    "created_at",
                ]
                pre_default_barcode_column.extend(barcode_column_values)
                item_namecustomfields = ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace
                ).values_list("id", flat=True)
                item_namecustomfields = [str(uuid) for uuid in item_namecustomfields]
                barcode_column_values.extend(item_namecustomfields)

                # NOTE: @hira29 this is to check if the default_barcode_property already created, if not create one then fill with default values
                default_barcode_property = DefaultBarcodeProperty.objects.filter(
                    workspace=workspace, target=setting_type
                ).first()
                if not default_barcode_property:
                    default_barcode_property = DefaultBarcodeProperty.objects.create(
                        workspace=workspace, target=setting_type
                    )
                    default_barcode_property.column_field = barcode_column_values[0]
                    default_barcode_property.type = BARCODE_TYPES[0][0]
                    default_barcode_property.edit_by = request.user
                    default_barcode_property.save()

                # NOTE: @hira29 this is preventing if the column_field is not in the list of barcode_column_values
                if default_barcode_property.column_field:
                    if (
                        default_barcode_property.column_field
                        not in barcode_column_values
                    ):
                        default_barcode_property.column_field = barcode_column_values[0]
                        default_barcode_property.edit_by = request.user
                        default_barcode_property.save()

            customize_template = CustomizePdfTemplate.objects.filter(
                setting_type=setting_type, workspace=workspace
            )
            pl_customize_template = []
            custom_objects = CustomObject.objects.filter(workspace=workspace)

        else:
            custom_objects = CustomObject.objects.filter(workspace=workspace)
        line_item_table_column = LINE_ITEM_DEFAULT
        custom_fields = ShopTurboItemsNameCustomField.objects.filter(
            workspace=workspace,
            type__in=["text", "text-area", "image", "number", "choice", "svg"],
        )

        for custom_field in custom_fields:
            line_item_table_column[str(custom_field.id)] = {
                "en": custom_field.name,
                "ja": custom_field.name,
            }
        list_template = []
        import os

        if setting_type == TYPE_OBJECT_ORDER:
            DIRECTORY = "data/templates/data/shopturbo/orders/pdf"
            NEXT_DIRECTORY = "data/shopturbo/orders/pdf/"
            # Loop through all files in the directory
            for filename in os.listdir(DIRECTORY):
                # Check if the file is an HTML file and contains 'pdf_pattern' in its name
                if filename.endswith(".html") and "orderPDF-pattern" == filename[:-7]:
                    list_template.append(NEXT_DIRECTORY + filename)
        elif setting_type == TYPE_OBJECT_PURCHASE_ORDER:
            DIRECTORY = "data/templates/data/purchase_order/"
            NEXT_DIRECTORY = "data/purchase_order/"
            # Loop through all files in the directory
            for filename in os.listdir(DIRECTORY):
                # Check if the file is an HTML file and contains 'pdf_pattern' in its name
                if (
                    filename.endswith(".html")
                    and "purchaseorderPDF-pattern" == filename[:-7]
                ):
                    list_template.append(NEXT_DIRECTORY + filename)
        elif setting_type == TYPE_OBJECT_ITEM:
            DIRECTORY = "data/templates/data/shopturbo/items/pdf"
            NEXT_DIRECTORY = "data/shopturbo/items/pdf/"
            # Loop through all files in the directory
            for filename in os.listdir(DIRECTORY):
                # Check if the file is an HTML file and contains 'pdf_pattern' in its name
                if filename.endswith(".html") and "itemPDF-pattern" == filename[:-7]:
                    list_template.append(NEXT_DIRECTORY + filename)

        list_template = sorted(list_template, key=natural_sort_key)
        if custom_object_slug:
            obj = get_page_object(TYPE_OBJECT_CUSTOM_OBJECT)
        else:
            obj = get_page_object(setting_type)
        base_model = obj["base_model"]
        custom_model = obj["custom_model"]
        columns_display = obj["columns_display"]
        # related_models = obj.get('related_data', None)
        header_blocks = {}
        total_blocks = {}
        NUMERIC_FIELD_TYPES = (
            models.IntegerField,
            models.FloatField,
            models.DecimalField,
            models.BigIntegerField,
            models.SmallIntegerField,
            models.PositiveIntegerField,
            models.PositiveSmallIntegerField,
        )

        list_custom_pdf = []
        if setting_type in [
            TYPE_OBJECT_PURCHASE_ORDER,
            TYPE_OBJECT_ORDER,
            TYPE_OBJECT_ITEM,
        ]:
            list_template = []
            list_custom_pdf = PdfTemplate.objects.filter(
                master_pdf__object_type=setting_type, workspace=workspace
            ).order_by("master_pdf__name_en", "master_pdf__name_ja")

        for field in base_model._meta.fields:
            if field.is_relation or field.name in ["id"]:
                if field.name in ["id", "contact", "company", "workspace"]:
                    continue

                related_model = field.related_model
                related_fields = [
                    f.name
                    for f in related_model._meta.get_fields()
                    if not f.is_relation or f.one_to_one or f.many_to_one
                ]

                for related_field in related_fields:
                    if related_field in ["id"]:
                        continue

                    value = f"{field.name}__{related_field}"

                    trans = columns_display.get(related_field)
                    display_en = trans.get("en") if trans else value
                    display_ja = trans.get("ja") if trans else value

                    header_blocks[value] = {
                        "en": f"{field.name} - {display_en}",
                        "ja": f"{field.name} - {display_ja}",
                    }
            else:
                value = field.name
                trans = columns_display.get(field.name)

                display_en = trans.get("en") if trans else value
                display_ja = trans.get("ja") if trans else value

                header_blocks[value] = {
                    "en": display_en,
                    "ja": display_ja,
                }

                if isinstance(field, NUMERIC_FIELD_TYPES):
                    total_blocks[value] = {
                        "en": f"Total of {display_en}",
                        "ja": f"{display_ja}の合計",
                    }

        temp_header_block = [
            "contact__" + val
            for val in get_field(
                Contact,
                get_list_object_columns(Contact, excludes=["created_at", "url"]),
                ["CharField"],
            )
        ]
        temp_header_block.append("contact__first_name")
        if "contact__company" in temp_header_block:
            temp_header_block.remove("contact__company")
        if "contact__image_url" in temp_header_block:
            temp_header_block.remove("contact__image_url")

        temp_header_block = [
            "company__" + val
            for val in get_field(
                Company,
                get_list_object_columns(Company, excludes=["created_at"]),
                ["CharField"],
            )
        ]
        trans_contact = SEARCH_COLUMNS_DISPLAY
        for hb in temp_header_block:
            trans = trans_contact.get(hb)
            display_en = trans.get("en") if trans else hb
            display_ja = trans.get("ja") if trans else hb

            header_blocks[hb] = {
                "en": display_en,
                "ja": display_ja,
            }

        # Performance monitoring - start header_blocks section
        header_blocks_start_time = time.time()
        perf_logger.info(
            f"[PERF] Starting header_blocks section: {(header_blocks_start_time - start_time) * 1000:.2f}ms total"
        )

        # Optimize header_blocks building with batch queries
        contact_custom_fields = ContactsNameCustomField.objects.filter(
            workspace=workspace, type="text"
        ).only("id", "name")
        for cf in contact_custom_fields:
            trans = trans_contact.get("contact")
            display_en = trans.get("en") if trans else ""
            display_ja = trans.get("ja") if trans else ""
            header_blocks[f"contact__custom_field|{cf.id}"] = {
                "en": f"{display_en} - {cf.name}",
                "ja": f"{display_ja} - {cf.name}",
            }

        company_custom_fields = CompanyNameCustomField.objects.filter(
            workspace=workspace, type="text"
        ).only("id", "name")
        for cf in company_custom_fields:
            trans = trans_contact.get("company")
            display_en = trans.get("en") if trans else ""
            display_ja = trans.get("ja") if trans else ""
            header_blocks[f"company__custom_field|{cf.id}"] = {
                "en": f"{display_en} - {cf.name}",
                "ja": f"{display_ja} - {cf.name}",
            }
        custom_field_hierarchy = CompanyNameCustomField.objects.filter(
            workspace=workspace, type="hierarchy"
        ).only("id", "name", "choice_value")
        for cf in custom_field_hierarchy:
            trans = trans_contact.get("company")
            display_en = trans.get("en") if trans else ""
            display_ja = trans.get("ja") if trans else ""
            fields_hierarchy = ast.literal_eval(cf.choice_value)
            for f_ in fields_hierarchy:
                for hb in temp_header_block:
                    trans = trans_contact.get(hb)
                    display_en_field = trans.get("en") if trans else hb
                    display_ja_field = trans.get("ja") if trans else hb

                    header_blocks[
                        f"company__custom_field|{cf.id}__{f_['label']}__{hb}"
                    ] = {
                        "en": f"{cf.name}({f_['label']}) - {display_en_field}",
                        "ja": f"{cf.name}({f_['label']}) - {display_ja_field}",
                    }
        if custom_model:
            if custom_object_slug:
                custom_prop_model = custom_model.objects.filter(
                    workspace=workspace, custom_object=custom_object
                ).only("id", "name", "type")
            else:
                custom_prop_model = custom_model.objects.filter(
                    workspace=workspace,
                    type__in=[
                        "text",
                        "text-area",
                        "image",
                        "number",
                        "choice",
                        "svg",
                        "date",
                        "datetime",
                        "date_time",
                        "date_range",
                        "formula",
                    ],
                ).only("id", "name", "type")

            for field in custom_prop_model:
                if field.name:
                    header_blocks[f"custom_property__{field.id}"] = {
                        "en": field.name,
                        "ja": field.name,
                    }

                    if field.type in ["number", "formula"]:
                        total_blocks[f"custom_property__{field.id}"] = {
                            "en": f"Total of {field.name}",
                            "ja": f"{field.name}の合計",
                        }

        if not custom_object_slug:
            custom_prop_model = custom_model.objects.filter(
                workspace=workspace,
                type__in=[
                    "bill_objects",
                    "invoice_objects",
                    "order_objects",
                    "contact",
                    "company",
                    "purchase_order",
                    "subscription",
                ],
            ).only("id", "name", "type")

            for associate_obj in custom_prop_model:
                obj_ = associate_obj.name
                if associate_obj.type == "bill_objects":
                    associate_model = Bill
                    assc_col_display = DISPLAY_COLUMNS_BILL.copy()
                elif associate_obj.type == "invoice_objects":
                    associate_model = Invoice
                    assc_col_display = INVOICE_COLUMNS_DISPLAY.copy()
                elif associate_obj.type == "order_objects":
                    associate_model = ShopTurboOrders
                    assc_col_display = ORDERS_COLUMNS_DISPLAY.copy()
                elif associate_obj.type == "contact":
                    associate_model = Contact
                    assc_col_display = CONTACTS_COLUMNS_DISPLAY.copy()
                elif associate_obj.type == "company":
                    associate_model = Company
                    assc_col_display = COMPANY_COLUMNS_DISPLAY.copy()
                elif associate_obj.type == "purchase_order":
                    associate_model = PurchaseOrders
                    assc_col_display = PURCHASE_ORDER_COLUMNS_DISPLAY.copy()
                elif associate_obj.type == "subscription":
                    associate_model = ShopTurboSubscriptions
                    assc_col_display = SUBSCRIPTIONS_COLUMNS_DISPLAY.copy()
                else:
                    continue
                for field in associate_model._meta.fields:
                    if field.is_relation or field.name in ["id"]:
                        continue
                    else:
                        value = f"custom_property__{associate_obj.id}__{field.name}"
                        trans = assc_col_display.get(field.name)

                        display_en = trans.get("en") if trans else field.name
                        display_ja = trans.get("ja") if trans else field.name

                        header_blocks[value] = {
                            "en": f"{obj_} - {display_en}",
                            "ja": f"{obj_} - {display_ja}",
                        }

        if setting_type not in [TYPE_OBJECT_ITEM]:
            header_blocks["customer__name"] = {
                "en": "Customer - Name",
                "ja": "顧客 - 名",
            }

        if setting_type == TYPE_OBJECT_ITEM:
            line_item_table_column = header_blocks

        # Performance monitoring - start context building section
        context_start_time = time.time()
        context_start_queries = len(connection.queries)
        perf_logger.info(
            f"[PERF] Starting context building: {(context_start_time - start_time) * 1000:.2f}ms total"
        )

        # Optimize context building with conditional queries based on setting_type
        # Performance optimization: only load expensive queries for pages that need them
        is_lightweight_page = setting_type in [
            TYPE_OBJECT_INVOICE,
            TYPE_OBJECT_DELIVERY_NOTE,
            TYPE_OBJECT_SUBSCRIPTION,
            TYPE_OBJECT_ORDER,  # Add commerce_orders to lightweight pages
        ]

        if is_lightweight_page:
            # Lightweight context for simple pages
            context = {
                "object_type": SHOPTURBO_APP_TARGET,
                "app_slug": SHOPTURBO_APP_SLUG,
                "role": role,
                "groups": groups,
                "app_setting": app_setting,
                "number_formats": SHOPTURBO_NUMBER_FORMAT,
                "OBJECTS_CF_TYPES": OBJECTS_CF_TYPE,
                "menu_key": "workspace",
                "page_title": page_title,
                "page_type": "object_manager",
                "page_group_type": "workspace_setting",
                "custom_object_id": custom_object.id if custom_object else None,
                "custom_object": custom_object,
                "custom_objects": custom_objects
                if "custom_objects" in locals()
                else CustomObject.objects.filter(workspace=workspace).only(
                    "id", "name"
                ),
                "custom_object_slug": custom_object_slug,
                "column_values": column_values,
                "setting_type": setting_type,
                "object_name": object_name,
                "barcode_property": default_barcode_property
                if "default_barcode_property" in locals()
                else None,
                "barcode_types": BARCODE_TYPES + QRCODE,
                "barcode_column_values": barcode_column_values
                if "barcode_column_values" in locals()
                else [],
                "list_customize_template": customize_template
                if "customize_template" in locals()
                else [],
                "list_pl_customize_template": pl_customize_template
                if "pl_customize_template" in locals()
                else [],
                "supplier_properties": supplier_properties
                if "supplier_properties" in locals()
                else [],
                "line_item_table_column": line_item_table_column
                if "line_item_table_column" in locals()
                else {},
                "header_blocks": header_blocks if "header_blocks" in locals() else {},
                "list_template": list_template if "list_template" in locals() else [],
                "list_custom_pdf": list_custom_pdf
                if "list_custom_pdf" in locals()
                else [],
                "header_total_blocks": total_blocks
                if "total_blocks" in locals()
                else {},
                # Only add empty querysets for required context variables
                "InventoryNameCustomField": ShopTurboInventoryNameCustomField.objects.none(),
                "InventoryWarehouseNameCustomField": InventoryWarehouseNameCustomField.objects.none(),
                "InventoryTransactionNameCustomField": InventoryTransactionNameCustomField.objects.none(),
                "ItemsNameCustomField": ShopTurboItemsNameCustomField.objects.none(),
                "OrdersNameCustomField": ShopTurboOrdersNameCustomField.objects.none(),
                "SubscriptionsNameCustomField": ShopTurboSubscriptionsNameCustomField.objects.none(),
                "ShopTurboItemsDiscount": ShopTurboItemsDiscount.objects.none(),
                "ShopTurboShippingCosts": ShopTurboShippingCost.objects.none(),
                "ShopTurboSubscriptionShippingCosts": ShopTurboShippingCost.objects.none(),
                "JobScores": JobScore.objects.none(),
                "ShopTurboOrderLevelTaxs": ShopTurboOrderLevelTax.objects.none(),
                "contact_lists": ContactList.objects.none(),
                "shopturbo_default_order_column": [
                    str(v.name) for v in ShopTurboOrders._meta.fields
                ],
                "shopturbo_default_subscription_column": [
                    str(v.name) for v in ShopTurboSubscriptions._meta.fields
                ],
            }
        else:
            # Full context for complex pages that need all the data
            context = {
                "object_type": SHOPTURBO_APP_TARGET,
                "app_slug": SHOPTURBO_APP_SLUG,
                "role": role,
                "groups": groups,
                "app_setting": app_setting,
                "InventoryNameCustomField": ShopTurboInventoryNameCustomField.objects.filter(
                    workspace=workspace
                )
                .order_by("order")
                .only("id", "name", "type", "order"),
                "InventoryWarehouseNameCustomField": InventoryWarehouseNameCustomField.objects.filter(
                    workspace=workspace
                )
                .order_by("order")
                .only("id", "name", "type", "order"),
                "InventoryTransactionNameCustomField": InventoryTransactionNameCustomField.objects.filter(
                    workspace=workspace
                )
                .order_by("order")
                .only("id", "name", "type", "order"),
                "ItemsNameCustomField": ShopTurboItemsNameCustomField.objects.filter(
                    workspace=workspace
                )
                .order_by("order")
                .only("id", "name", "type", "order"),
                "OrdersNameCustomField": ShopTurboOrdersNameCustomField.objects.filter(
                    workspace=workspace
                )
                .order_by("order")
                .only("id", "name", "type", "order"),
                "SubscriptionsNameCustomField": ShopTurboSubscriptionsNameCustomField.objects.filter(
                    workspace=workspace
                )
                .order_by("order")
                .only("id", "name", "type", "order"),
                "ShopTurboItemsDiscount": ShopTurboItemsDiscount.objects.filter(
                    workspace=workspace
                )
                .exclude(discount_type="free_writing_discounts")
                .order_by("created_at")
                .only("id", "name", "discount_type", "created_at"),
                "ShopTurboShippingCosts": ShopTurboShippingCost.objects.filter(
                    workspace=workspace
                ).only("id", "name", "value"),
                "ShopTurboSubscriptionShippingCosts": ShopTurboShippingCost.objects.filter(
                    workspace=workspace
                ).only("id", "name", "value"),
                "JobScores": JobScore.objects.filter(workspace=workspace).only(
                    "id", "value"
                ),
                "ShopTurboOrderLevelTaxs": ShopTurboOrderLevelTax.objects.filter(
                    workspace=workspace
                ).only("id", "tax"),
                "contact_lists": ContactList.objects.filter(workspace=workspace).only(
                    "id", "name"
                ),
                "number_formats": SHOPTURBO_NUMBER_FORMAT,
                "OBJECTS_CF_TYPES": OBJECTS_CF_TYPE,
                "menu_key": "workspace",
                "page_title": page_title,
                "page_type": "object_manager",
                "page_group_type": "workspace_setting",
                "custom_object_id": custom_object.id if custom_object else None,
                "custom_object": custom_object,
                "custom_objects": custom_objects,
                "custom_object_slug": custom_object_slug,
                "column_values": column_values,
                "shopturbo_default_order_column": [
                    str(v.name) for v in ShopTurboOrders._meta.fields
                ],
                "shopturbo_default_subscription_column": [
                    str(v.name) for v in ShopTurboSubscriptions._meta.fields
                ],
                "setting_type": setting_type,
                "object_name": object_name,
                # Filter for Default Barcode Properties
                "barcode_property": default_barcode_property,
                "barcode_types": BARCODE_TYPES + QRCODE,
                "barcode_column_values": barcode_column_values,
                "list_customize_template": customize_template,
                "list_pl_customize_template": pl_customize_template,
                "supplier_properties": supplier_properties,
                "line_item_table_column": line_item_table_column,
                "header_blocks": header_blocks,
                "list_template": list_template,
                "list_custom_pdf": list_custom_pdf,
                "header_total_blocks": total_blocks,
            }

        # Performance monitoring - after context building
        after_context_time = time.time()
        after_context_queries = len(connection.queries)
        context_type = "lightweight" if is_lightweight_page else "full"
        perf_logger.info(
            f"[PERF] Context building complete ({context_type}): {(after_context_time - context_start_time) * 1000:.2f}ms, queries: {after_context_queries - context_start_queries}"
        )

        if custom_object_slug or setting_type in [
            TYPE_OBJECT_INVENTORY_WAREHOUSE,
            TYPE_OBJECT_CASE,
            TYPE_OBJECT_ITEM,
            TYPE_OBJECT_ORDER,
            TYPE_OBJECT_INVENTORY,
            TYPE_OBJECT_INVENTORY_TRANSACTION,
            TYPE_OBJECT_BILL,
            TYPE_OBJECT_EXPENSE,
            TYPE_OBJECT_INVOICE,
            TYPE_OBJECT_ESTIMATE,
            TYPE_OBJECT_DELIVERY_NOTE,
            TYPE_OBJECT_RECEIPT,
            TYPE_OBJECT_SLIP,
            TYPE_OBJECT_SUBSCRIPTION,
            TYPE_OBJECT_COMMERCE_METER,
        ]:
            # Performance monitoring - before populate_object_display_context
            before_populate_time = time.time()
            before_populate_queries = len(connection.queries)

            # Check if we need to populate object display context - skip for performance when not needed
            # Also skip for lightweight pages unless explicitly requested
            should_skip_display_context = request.GET.get(
                "skip_display_context"
            ) == "true" or (
                is_lightweight_page
                and request.GET.get("force_display_context") != "true"
            )

            if not should_skip_display_context:
                perf_logger.info(
                    f"[PERF] Starting populate_object_display_context: {(before_populate_time - start_time) * 1000:.2f}ms total"
                )
                context = populate_object_display_context(request, workspace, context)

                # Performance monitoring - after populate_object_display_context
                after_populate_time = time.time()
                after_populate_queries = len(connection.queries)
                perf_logger.info(
                    f"[PERF] populate_object_display_context complete: {(after_populate_time - before_populate_time) * 1000:.2f}ms, queries: {after_populate_queries - before_populate_queries}"
                )
            else:
                skip_reason = (
                    "lightweight page"
                    if is_lightweight_page
                    else "skip_display_context=true"
                )
                perf_logger.info(
                    f"[PERF] Skipping populate_object_display_context for performance ({skip_reason})"
                )

        # Performance monitoring - final timing
        end_time = time.time()
        end_queries = len(connection.queries)
        total_time = (end_time - start_time) * 1000
        total_queries = end_queries - initial_query_count

        perf_logger.info(
            f"[PERF] TOTAL workspace_setting execution: {total_time:.2f}ms, total queries: {total_queries}"
        )

        # Log slow requests (over 2 seconds)
        if total_time > 2000:
            perf_logger.warning(
                f"[PERF] SLOW REQUEST: {total_time:.2f}ms, queries: {total_queries}, setting_type: {setting_type}"
            )

        return render(
            request, "data/account/workspace/workspace_template.html", context
        )
