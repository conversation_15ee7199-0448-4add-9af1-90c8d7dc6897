import math
from typing import Optional

from data.models import ShopTurboOrders, ShopTurboDecimalPoint
from data.models.workspace import Workspace


def calculate_order_total_with_decimal_point(order_id: str, workspace: Workspace, decimal_type: Optional[str] = None) -> float:
    """
    Calculate order total with tax, applying decimal point configuration.
    Follows the pattern from order_total_fix function.
    
    Args:
        order_id: The order ID to calculate total for
        workspace: Workspace instance for configuration
        decimal_type: Optional override for decimal point type
        
    Returns:
        float: Calculated total price with tax
    """
    try:
        # Get the order with related data
        order = ShopTurboOrders.objects.select_related().prefetch_related(
            'shopturboitemsorders_set'
        ).filter(id=order_id).first()
        
        if not order:
            return 0.0
            
        # Get decimal point configuration
        if not decimal_type:
            general_decimal_point = ShopTurboDecimalPoint.objects.filter(
                workspace=workspace, app_name="general"
            ).first()
            if general_decimal_point:
                decimal_type = general_decimal_point.type
        
        # Get line items efficiently using the relationship
        line_items = order.shopturboitemsorders_set.all()
        
        total_price_tax = 0.0
        
        # Calculate total from line items
        if line_items:
            for line_item in line_items:
                if order.line_item_type == 'item_based_tax':
                    if order.tax_inclusive:
                        # Tax-inclusive: derive tax-exclusive price from inclusive price
                        line_item_total_price_tax = line_item.item_price_order * line_item.number_item
                        line_item_total_price = line_item_total_price_tax / (1 + line_item.item_price_order_tax/100)
                    else:
                        # Tax-exclusive: calculate inclusive price from exclusive price
                        line_item_total_price = line_item.item_price_order * line_item.number_item
                        line_item_total_price_tax = line_item_total_price * (1 + line_item.item_price_order_tax/100)
                else:
                    tax_rate = order.tax/100 if order.tax else 0
                    
                    if order.tax_inclusive:
                        # Tax-inclusive: derive tax-exclusive price from inclusive price
                        line_item_total_price_tax = line_item.total_price
                        line_item_total_price = line_item_total_price_tax / (1 + tax_rate) if tax_rate > 0 else line_item_total_price_tax
                    else:
                        # Tax-exclusive: calculate inclusive price from exclusive price
                        line_item_total_price = line_item.total_price
                        line_item_total_price_tax = line_item_total_price * (1 + tax_rate)
                
                # Apply decimal point formatting if needed
                if decimal_type in ['line_item_cut_off', 'line_item_cut_over']:
                    math_func = math.floor if decimal_type == 'line_item_cut_off' else math.ceil
                    line_item_total_price_tax = math_func(line_item_total_price_tax)
                
                total_price_tax += line_item_total_price_tax
        else:
            tax_rate = order.tax/100 if order.tax else 0
            
            if order.tax_inclusive:
                # Tax-inclusive: derive tax-exclusive price from inclusive price
                total_price_tax = order.total_price_without_tax  # This field name might be misleading if tax_inclusive=True
                total_price = total_price_tax / (1 + tax_rate) if tax_rate > 0 else total_price_tax
            else:
                # Tax-exclusive: calculate inclusive price from exclusive price
                total_price = order.total_price_without_tax
                total_price_tax = total_price * (1 + tax_rate)
                
        if decimal_type == 'cut_off':
            total_price_tax = math.floor(total_price_tax)
        elif decimal_type == 'cut_over':
            total_price_tax = math.ceil(total_price_tax)
            
        return total_price_tax
        
    except Exception as e:
        # Log the error in production
        print(f"Error calculating order total with decimal point: {e}")
        return 0.0


def calculate_order_total_without_tax_with_decimal_point(order_id: str, workspace: Workspace, decimal_type: Optional[str] = None) -> float:
    """
    Calculate order total without tax, applying decimal point configuration.
    Follows the pattern from order_total_fix function.
    
    Args:
        order_id: The order ID to calculate total for
        workspace: Workspace instance for configuration
        decimal_type: Optional override for decimal point type
        
    Returns:
        float: Calculated total price without tax
    """
    try:
        # Get the order with related data
        order = ShopTurboOrders.objects.select_related().prefetch_related(
            'shopturboitemsorders_set'
        ).filter(id=order_id).first()
        
        if not order:
            return 0.0
            
        # Get decimal point configuration
        if not decimal_type:
            general_decimal_point = ShopTurboDecimalPoint.objects.filter(
                workspace=workspace, app_name="general"
            ).first()
            if general_decimal_point:
                decimal_type = general_decimal_point.type
        
        # Get line items efficiently using the relationship
        line_items = order.shopturboitemsorders_set.all()
        
        total_price = 0.0
        
        # Calculate total from line items (without tax)
        if line_items:
            for line_item in line_items:
                if order.line_item_type == 'item_based_tax':
                    if order.tax_inclusive:
                        # Tax-inclusive: derive tax-exclusive price from inclusive price
                        line_item_total_price_tax = line_item.item_price_order * line_item.number_item
                        line_item_total_price = line_item_total_price_tax / (1 + line_item.item_price_order_tax/100)
                    else:
                        # Tax-exclusive: calculate inclusive price from exclusive price
                        line_item_total_price = line_item.item_price_order * line_item.number_item
                        line_item_total_price_tax = line_item_total_price * (1 + line_item.item_price_order_tax/100)
                else:
                    tax_rate = order.tax/100 if order.tax else 0
                    
                    if order.tax_inclusive:
                        # Tax-inclusive: derive tax-exclusive price from inclusive price
                        line_item_total_price_tax = line_item.total_price
                        line_item_total_price = line_item_total_price_tax / (1 + tax_rate) if tax_rate > 0 else line_item_total_price_tax
                    else:
                        # Tax-exclusive: calculate inclusive price from exclusive price
                        line_item_total_price = line_item.total_price
                        line_item_total_price_tax = line_item_total_price * (1 + tax_rate)
                
                # Apply decimal point formatting if needed
                if decimal_type in ['line_item_cut_off', 'line_item_cut_over']:
                    math_func = math.floor if decimal_type == 'line_item_cut_off' else math.ceil
                    line_item_total_price = math_func(line_item_total_price)
                
                total_price += line_item_total_price
        else:
            tax_rate = order.tax/100 if order.tax else 0
            
            if order.tax_inclusive:
                # Tax-inclusive: derive tax-exclusive price from inclusive price
                total_price_tax = order.total_price_without_tax  # This field name might be misleading if tax_inclusive=True
                total_price = total_price_tax / (1 + tax_rate) if tax_rate > 0 else total_price_tax
            else:
                # Tax-exclusive: calculate inclusive price from exclusive price
                total_price = order.total_price_without_tax
                total_price_tax = total_price * (1 + tax_rate)
                
        if decimal_type == 'cut_off':
            total_price = math.floor(total_price)
        elif decimal_type == 'cut_over':
            total_price = math.ceil(total_price)
            
        return total_price
        
    except Exception as e:
        # Log the error in production
        print(f"Error calculating order total without tax with decimal point: {e}")
        return 0.0


def get_decimal_type_from_legacy_format(type_list: str) -> Optional[str]:
    """
    Convert legacy template tag format to decimal type.
    
    Args:
        type_list: Comma-separated string like "line_item_cut_off,USD"
        
    Returns:
        str: Decimal type or None if invalid
    """
    try:
        parts = type_list.split(",")
        if len(parts) >= 1:
            return parts[0]
    except (AttributeError, IndexError):
        pass
    return None