import json
import logging

from django.core.cache import cache
from django.http import HttpResponse, HttpResponseNotFound, JsonResponse
from django.shortcuts import redirect, render
from django.utils import timezone
from django.views.decorators.http import require_POST
from django_hosts.resolvers import reverse


from data.constants.constant import (CHART_CHANNEL_STAT, CHART_POST_STAT,
                                     CHART_TYPE_NAME, PANEL_METRIC_TITLE,
                                     PANEL_TYPE_TITLE)
from data.constants.properties_constant import *
from data.constants.reports_constant import *
from data.models import *

from utils.decorator import login_or_hubspot_required
from utils.properties.properties import (
    get_list_object_columns, get_page_object
)
from utils.utility import get_workspace

logger = logging.getLogger(__name__)


def render_drawer_metrics(request):

    main_channel = request.GET.get('main_channel')
    json_response = request.GET.get('json_response')

    try:
        channel = None
        if main_channel:
            channel = Channel.objects.get(id=main_channel)

        if json_response:
            if channel.integration.slug in CHANNELS_MENU_DRAWER:
                panels = CHANNELS_MENU_DRAWER[channel.integration.slug]
                metric_options = []
                for p in panels:
                    if p in CHART_TYPE_NAME:
                        metric_options.append({
                            'value': p,
                            'display': CHART_TYPE_NAME[p]
                        })
                return HttpResponse(json.dumps(metric_options))
    except Exception as e:
        print(f' === reports.py - 143: Error {e}')

    return HttpResponse(status=200)


def render_manage_drawer(request):
    selected_report_id = request.GET.get('selected_report')
    selected_report = Report.objects.get(id=selected_report_id)
    context = {"selected_report": selected_report,
               "channel_by_user": [selected_report.channel]}
    return render(request, 'data/reports/reports-manage-drawer.html', context)

@login_or_hubspot_required
def report_views_drawer(request):
    type = request.GET.get('type', None)
    lang = request.LANGUAGE_CODE
    if type == 'advance-filter':
        data_filter = request.GET.get("data_filter", None)
        view_page = request.GET.get("view_page", None)
        filter_options = request.GET.get("filter_options", None)
        data_type = "string"
        field_choice = []

        if view_page == 'panels':
            fields = ReportPanel._meta.fields
        else:
            fields = Report._meta.fields

        if data_filter in ["is_deleted"]:
            if data_filter == "is_deleted":
                field_choice = [('active', 'Active'), ('archived', 'Archived')]

        default_columns_dict = {
            field.name: field.get_internal_type() for field in fields}
        if data_filter in default_columns_dict.keys():
            for check in ["float", "integer"]:
                if check in default_columns_dict[data_filter].lower():
                    data_type = "number"
                    break
            for check in ["date"]:
                if check in default_columns_dict[data_filter].lower():
                    data_type = "date"
                    break

        predefined_data_filter = request.GET.get(
            "predefined_data_filter", None)
        if predefined_data_filter:
            predefined_data_filter = ast.literal_eval(predefined_data_filter)
            if not filter_options:
                filter_options = predefined_data_filter.get('key')

        context = {
            "data_filter": data_filter,
            "data_type": data_type,
            "app_type": view_page,
            "predefined_data_filter": predefined_data_filter,
            'field_choice': field_choice,
            'uuid': uuid.uuid4(),
            "filter_options": filter_options
        }
        return render(request, 'data/shopturbo/list-data-filter-selector.html', context)

    else:
        view_type = [view for view in VIEW_MODE if view[0] in ['list']]
        view_page = request.GET.get('view_page', None)
        view_id = request.GET.get('view_id', None)
        view_filter = None
        page_obj = get_page_object(view_page, lang)

        if view_id:
            view = View.objects.get(id=view_id)
            view_filter = ViewFilter.objects.filter(view=view).first()

        if view_page == 'panels':
            # column_values = get_list_object_columns(ReportPanel)
            column_values = ['panel_id', 'name', 'panel_type', "is_deleted"]
        else:
            column_values = get_list_object_columns(Report)

        context = {
            'columns': page_obj['default_columns'],
            'VIEW_MODE': view_type,
            'view_filter': view_filter,
            'column_values': column_values,
            'view_page': view_page,
        }
        return render(request, 'data/reports/report-manage-view.html', context)
    
@login_or_hubspot_required
def get_create_report_drawer(request, platform=None, metrics=None, workflow=None):
    workspace = get_workspace(request.user)

    # Create cache key for channels data
    cache_key = f"report_drawer_channels_{workspace.id}_{platform or 'all'}"
    channels_with_metrics = cache.get(cache_key)

    if channels_with_metrics is None:
        # Optimize database query with select_related to reduce database hits
        if platform:
            channels = Channel.objects.select_related('integration').filter(
                workspace=workspace, integration__slug=platform
            ).order_by('name')
        else:
            channels = Channel.objects.select_related('integration').filter(
                workspace=workspace
            ).exclude(integration__slug='facebook').order_by('name')

        # Pre-calculate available metrics for each channel to avoid runtime calculations
        channels_with_metrics = []
        for channel in channels:
            channel_metrics = []
            if channel.integration.slug in CHANNELS_MENU_DRAWER:
                panels = CHANNELS_MENU_DRAWER[channel.integration.slug]
                for panel in panels:
                    if panel in CHART_TYPE_NAME:
                        channel_metrics.append({
                            'value': panel,
                            'display': CHART_TYPE_NAME[panel]
                        })
            channels_with_metrics.append({
                'channel': channel,
                'metrics': channel_metrics
            })

        # Cache for 5 minutes to improve performance
        cache.set(cache_key, channels_with_metrics, 300)

    # Extract channels for backward compatibility
    channels = [item['channel'] for item in channels_with_metrics]

    context = {
        'channel_by_user': channels,
        'channels_with_metrics': channels_with_metrics,
        'channels_menu': CHANNELS_MENU,
        'LANGUAGE_CODE': request.LANGUAGE_CODE,
        'metrics': metrics,
    }
    if workflow:
        context['workflow_id'] = workflow.id
    return render(request, 'data/partials/create-report-drawer.html', context)

