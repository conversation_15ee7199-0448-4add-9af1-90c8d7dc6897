{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<input type="hidden" name="current_index" value={{action_index}}>
<input type="hidden" name="action{% if action_index %}-{{action_index}}{% endif %}" value="{{action_slug}}">

<div class="mt-5">
    <div class="mb-5">
        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
            <span>
                {% if LANGUAGE_CODE == 'ja'%}
                    連携サービス
                {% else %}
                    Integrations
                {% endif %}
            </span>
        </label>
        <select required class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="false" 
            id="channel-select{% if action_index %}-{{action_index}}{% endif %}"
            data-allow-clear="false" onchange="toggleMappingSections(this)"
            name="select_integration_ids{% if action_index %}-{{action_index}}{% endif %}" placeholder="{% if LANGUAGE_CODE == 'ja' %}担当者{% else %}Assignee{% endif %}">
            {% for channel in channels %}
                <option value="{{channel.id}}" data-platform="{{channel.integration.slug}}" data-name="{{channel.name}}"
                    {% if channel.id|stringify == active_channel|stringify %}selected{% endif %}
                >
                    {% if channel.integration.slug == 'hubspot' %}
                        {{ channel.name|cut:" Power Inventory" }}
                    {% else %}
                        {{ channel.name }}
                    {% endif %}
                </option>
            {% endfor %}
        </select>
    </div>
</div>

<div class="mt-5">
    <div class="mb-5">
        <div id="hubspot-mapping-table-section{% if action_index %}-{{action_index}}{% endif %}" class="d-none">
            <div id="mapping-hubspot-field" class="mb-10 mt-8">
                {% csrf_token %}
                <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                    <input id="hubspot-map-checker{% if action_index %}-{{action_index}}{% endif %}" name="hubspot-map-checker{% if action_index %}-{{action_index}}{% endif %}" class="form-check-input" type="checkbox"
                        hx-post="{% host_url 'hubspot_inventory_fields_mapping_url' host 'app' %}"
                        hx-vals='{"module": "{{menu_key}}","action_index": "{{action_index}}"}'
                        hx-target="#hubspotMappingContainer{% if action_index %}-{{action_index}}{% endif %}"
                        hx-trigger="channelChanged from:body"
                        hx-encoding="multipart/form-data">
                </div>

                <div id="hubspotMappingContainer{% if action_index %}-{{action_index}}{% endif %}" class="mb-3"></div>
            </div>
        </div>
        <div id="shopify-mapping-table-section{% if action_index %}-{{action_index}}{% endif %}" class="d-none">
            <div id="mapping-item-field" class="mb-10 mt-8">
                {% csrf_token %}
                <div class="mb-6 form-check form-switch form-check-custom form-check-solid d-none ">
                    <input id="item-checker-1{% if action_index %}-{{action_index}}{% endif %}" name="item-checker{% if action_index %}-{{action_index}}{% endif %}" class="form-check-input" type="checkbox"
                        
                        hx-post="{% host_url 'sync_item_header_extractor' host 'app' %}"
                        hx-vals='{"module": "{{menu_key}}", "action_index": "{{action_index}}"}'
                        hx-target="#csvMappingContainer{% if action_index %}-{{action_index}}{% endif %}"
                        hx-trigger="load, channelChanged from:body"
                        hx-encoding="multipart/form-data"
                        onchange="contactMappingHandler(this)"
                    >
                    <label class="form-check-label fw-semibold text-gray-700 ms-3" for="allowchanges">
                        {% if LANGUAGE_CODE == 'ja' %}
                        連絡先情報のマッピング
                        {% else %}
                        Mapping Contact Infomation
                        {% endif %}
                    </label>
                </div>

                <div id="csvMappingContainer{% if action_index %}-{{action_index}}{% endif %}" class="mb-3"></div>
            </div>
        </div>
    </div>
</div>

<div id="filter-panel{% if action_index %}-{{action_index}}{% endif %}" class="d-none">
    <div id="sync-filters-field" class="mb-10 mt-8 d-none">
        <div class="mb-3">
            <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                <span>
                    {% if LANGUAGE_CODE == 'ja'%}
                        フィルターの種類
                    {% else %}
                        Filter Type
                    {% endif %}
                </span>
            </label>
            <select id="filter-type" name="filter-type{% if action_index %}-{{action_index}}{% endif %}" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターの種類{% else %}Filter Type{% endif %}" data-allow-clear="true">
                <option value=""></option>
                <option value="include" {% if input_data.filter_type == 'include' %} selected {% endif %}
                    >{% if LANGUAGE_CODE == 'ja'%} 含める {% else %} Include {% endif %}
                </option>
                <option value="exclude" {% if input_data.filter_type == 'exclude' %} selected {% endif %}
                >{% if LANGUAGE_CODE == 'ja'%} 除外する {% else %} Exclude {% endif %}</option>
            </select>
        </div>
        <div class="mb-3">
            <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                <span>
                    {% if LANGUAGE_CODE == 'ja'%}
                        フィルター
                    {% else %}
                        Filters
                    {% endif %}
                </span>
            </label>
            <select id="filter-select{% if action_index %}-{{action_index}}{% endif %}" name="filter{% if action_index %}-{{action_index}}{% endif %}" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}" data-allow-clear="true">
                <option value=""></option>
                {% for filter in filters %}
                    <option value="{{filter.id}}" {% if input_data.filter_id == filter.id|stringify %} selected {% endif %}>
                        {% if filter.name %}
                            {{filter.name}}
                        {% else %}
                            {{filter.id}}
                        {% endif %}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div id="filter-choice-field{% if action_index %}-{{action_index}}{% endif %}" class="mb-3 d-none">
            <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                <span>
                    {% if LANGUAGE_CODE == 'ja'%}
                        フィルターの選択肢
                    {% else %}
                        Filter Choice
                    {% endif %}
                </span>
            </label>
            <select id="filter-choice{% if action_index %}-{{action_index}}{% endif %}" name="filter-choice{% if action_index %}-{{action_index}}{% endif %}" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターの選択肢を選択{% else %}Select Filter Choice{% endif %}" data-hide-search="true" data-allow-clear="true">
            </select>
        </div>
    </div>

    <div id="advanced-filter-create{% if action_index %}-{{action_index}}{% endif %}" class="mt-5">
        <div class="mb-2 fs-4 justify-content-start fw-bolder">
            <div class="">
                {% if LANGUAGE_CODE == 'ja' %}フィルター{% else %}Filters{% endif %}
            </div>
            <select id="filter-select-2" class="bg-white border min-h-40px form-select form-select-solid w-100"      
                data-control="select2" 
                hx-get="{% host_url 'shopturbo_load_drawer' host 'app' %}"
                hx-target="#data-selector-content"
                hx-trigger="htmx-change"
                name="data_filter"
                data-placeholder="{% if LANGUAGE_CODE == 'ja' %}フィルターを選択{% else %}Select Filters{% endif %}"
                hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "app_type":"items"}'
                hx-swap="beforeend" 
                >
                <option value=''></option>

                {% for column_value in all_columns %}
                    <option value="{{column_value}}">
                        {% if column_value|search_custom_field_object_items:request %}
                            {% with channel_column=column_value|search_custom_field_object_items:request %}
                                {{channel_column.name}}
                            {% endwith %} 
                        {% else %}
                            {% with column_display=column_value|display_column_items:request %}
                                {{column_display}}
                            {% endwith %}
                        {% endif %}
                    </option>            
                {% endfor %}
            </select>
        </div>
        <div id="data-selector-content" class="mt-3"></div>
        {% for predefined_data_filter in active_filter_dictionary %}
        {% if active_filter_dictionary|get_attr:predefined_data_filter|get_attr:'key'|string_list_to_list|length > 0 %}
            {% for multiple_data_filter in active_filter_dictionary|get_attr:predefined_data_filter|split_dictionary %}
                <div                                 
                    hx-get="{% host_url 'shopturbo_load_drawer' host 'app' %}"
                    hx-target="#data-selector-content"
                    hx-trigger="load"
                    hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "data_filter":"{{ predefined_data_filter }}", "predefined_data_filter":"{{multiple_data_filter}}", "app_type":"{{view_page}}"}'
                    hx-swap="beforeend" 
                >
                </div>
            {% endfor %}
        {% else %}
        <div                                 
            hx-get="{% host_url 'shopturbo_load_drawer' host 'app' %}"
            hx-target="#data-selector-content"
            hx-trigger="load"
            hx-vals='{"drawer_type":"shopturbo-view-settings","type":"advance-filter", "data_filter":"{{ predefined_data_filter|safe }}", "predefined_data_filter":"{{active_filter_dictionary|get_attr:predefined_data_filter}}", "app_type":"{{view_page}}"}'
            hx-swap="beforeend" 
        >
        </div>
        {% endif %}
        {% endfor %}
    </div>

    <div id="update-inventory-toggle{% if action_index %}-{{action_index}}{% endif %}" class="d-none mb-5">
        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
            <span>
                {% if LANGUAGE_CODE == 'ja'%}在庫同期{% else %}Update Inventory{% endif %}
            </span>
        </label>

        <select id="update_inventory{% if action_index %}-{{action_index}}{% endif %}" name="update_inventory{% if action_index %}-{{action_index}}{% endif %}" class="bg-white form-select form-select-solid border h-40px select2-this" 
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="在庫同期"
            {% else %}
            data-placeholder="Update Inventory"
            {% endif %}
            data-allow-clear="true">
            <option value="no_update" {% if active_update_inventory_toggle == 'no_update' %} selected {% endif %}>
                {% if LANGUAGE_CODE == 'ja' %}
                    在庫を更新しない
                {% else %}
                    Do Not Update Inventory
                {% endif %}
            </option>
            <option value="update" {% if active_update_inventory_toggle == 'update' %} selected {% endif %}>
                {% if LANGUAGE_CODE == 'ja' %}
                    在庫を更新する
                {% else %}
                    Update Inventory
                {% endif %}
            </option>
            <option value="only_inventory" {% if active_update_inventory_toggle == 'only_inventory' %} selected {% endif %}>
                {% if LANGUAGE_CODE == 'ja' %}
                    在庫のみ更新（商品情報は更新しない）
                {% else %}
                    Update Inventory Only (do not update item)
                {% endif %}
            </option>
        </select>
    </div>

    <div id="sync-method-field" class="mb-10 mt-8 d-none">
        <div class="mb-6">
            <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary mb-2">
                <span>
                    {% if LANGUAGE_CODE == 'ja'%}
                        同期方法
                    {% else %}
                        Sync Method
                    {% endif %}
                </span>
            </label>
            <select required name="sync-key{% if action_index %}-{{action_index}}{% endif %}" class="{% include 'data/utility/select-form.html' %} select2-this" data-control="select2" data-hide-search="true">
                <option value="platform" {% if input_data %} {% if input_data.sync_key == 'platform' %} selected {% endif %} {% else %} selected {% endif %}>
                    {% if LANGUAGE_CODE == 'ja' %}
                    プラットフォームID
                    {% else %}
                    Platform ID 
                    {% endif %}
                </option>
                <option value="sku" {% if input_data %} {% if input_data.sync_key == 'sku' %} selected {% endif %} {% endif %}>
                    {% if LANGUAGE_CODE == 'ja' %}
                    在庫SKU
                    {% else %}
                    Inventory SKU
                    {% endif %}
                </option>
            </select>
        </div>
    </div>
    
</div>
<script>
    $('.select2-this').select2();
    $(document).ready(function() {
        var channelSelect{% if action_index %}_{{action_index}}{% endif %} = $('#channel-select{% if action_index %}-{{action_index}}{% endif %}');
        toggleMappingSections();

        channelSelect{% if action_index %}_{{action_index}}{% endif %}.change(function() {
            toggleMappingSections();
        });
    });

    
    function toggleMappingSections() {
        var shopifyMappingSection{% if action_index %}_{{action_index}}{% endif %} = $('#shopify-mapping-table-section{% if action_index %}-{{action_index}}{% endif %}');
        var hubspotMappingSection{% if action_index %}_{{action_index}}{% endif %} = $('#hubspot-mapping-table-section{% if action_index %}-{{action_index}}{% endif %}');
        var hubspotMapChecker{% if action_index %}_{{action_index}}{% endif %} = $('#hubspot-map-checker{% if action_index %}-{{action_index}}{% endif %}');
        var filterPanel{% if action_index %}_{{action_index}}{% endif %} = $('#filter-panel{% if action_index %}-{{action_index}}{% endif %}'); 
        var advancedFilterCreate{% if action_index %}_{{action_index}}{% endif %} = $('#advanced-filter-create{% if action_index %}-{{action_index}}{% endif %}');
        var updateInventoryToggle{% if action_index %}_{{action_index}}{% endif %} = $('#update-inventory-toggle{% if action_index %}-{{action_index}}{% endif %}');

        const selectedChannel = $('#channel-select{% if action_index %}-{{action_index}}{% endif %} option:selected');
        const platform = selectedChannel.data('platform');
        const channelName = selectedChannel.data('name');
        const channelId = selectedChannel.val();

        console.log("Selected channel: ", selectedChannel);
        console.log("Platform: ", platform);

        if (platform === 'hubspot') {
            shopifyMappingSection{% if action_index %}_{{action_index}}{% endif %}.addClass('d-none');
            hubspotMappingSection{% if action_index %}_{{action_index}}{% endif %}.removeClass('d-none');
            filterPanel{% if action_index %}_{{action_index}}{% endif %}.addClass('d-none')

            const inputs = {
                "channel_id": channelId,
                "hub_domain": channelName,
                "import_export_type": "{{ import_export_type }}",
                "object_type": "{{ object_type }}",
                "menu_key": "{{menu_key}}",
                "count_items": "{{count_items}}"
            }
            {% if item_ids|length > 0 %}
                inputs["item_ids"] = {{item_ids}}
            {% endif %}
            hubspotMapChecker.attr('hx-vals', JSON.stringify(inputs));

            console.log("HubSpot map checker: ", hubspotMapChecker);

            // Trigger the 'channelChanged' event on the body element
            htmx.trigger('body', 'channelChanged');

        } else if (platform === 'shopify' || platform === 'makeshop' || platform === 'yahoo-shopping' || platform === 'rakuten' || platform === 'amazon') {
            shopifyMappingSection{% if action_index %}_{{action_index}}{% endif %}.removeClass('d-none');
            hubspotMappingSection{% if action_index %}_{{action_index}}{% endif %}.addClass('d-none');
        } else {
            shopifyMappingSection{% if action_index %}_{{action_index}}{% endif %}.addClass('d-none');
            hubspotMappingSection{% if action_index %}_{{action_index}}{% endif %}.addClass('d-none');

        }

        if (platform === 'makeshop' || platform === 'yahoo-shopping' || platform === 'rakuten' || platform === 'amazon') {
            filterPanel{% if action_index %}_{{action_index}}{% endif %}.removeClass('d-none')
            advancedFilterCreate{% if action_index %}_{{action_index}}{% endif %}.removeClass('d-none')
        } else {
            filterPanel{% if action_index %}_{{action_index}}{% endif %}.addClass('d-none')
            advancedFilterCreate{% if action_index %}_{{action_index}}{% endif %}.addClass('d-none')
        }

        if (platform === 'makeshop' || platform === 'amazon' || platform === 'rakuten') {
            updateInventoryToggle{% if action_index %}_{{action_index}}{% endif %}.removeClass('d-none')
        } else {
            updateInventoryToggle{% if action_index %}_{{action_index}}{% endif %}.addClass('d-none')
        }

        // Reload mapping table
        Array.from(document.querySelectorAll("input[name='item-checker{% if action_index %}-{{action_index}}{% endif %}']")).forEach(element => {
            if (!Array.from(element.parentElement.parentElement.classList).includes('d-none')) {
                console.log(element)
                htmx.trigger(`#${element.id}`, "channelChanged")
            }
        });
    }
    
    function contactMappingHandler(elm){
        var mapping{% if action_index %}_{{action_index}}{% endif %} = document.getElementById("csvMappingContainer{% if action_index %}-{{action_index}}{% endif %}")
        if (elm.checked === true){
            if (mapping{% if action_index %}_{{action_index}}{% endif %}){
                mapping{% if action_index %}_{{action_index}}{% endif %}.classList.add('d-none')
            }
            
        }
        else{
            if (mapping{% if action_index %}_{{action_index}}{% endif %}){
                mapping{% if action_index %}_{{action_index}}{% endif %}.classList.add('d-none')
            }
            
        }
    }
    
</script>

<script>
    //if filter selected
    $(document).ready(function() {
        var filterId = $('#filter-select{% if action_index %}-{{action_index}}{% endif %}').val();
        console.log("Filter ID: ", filterId);
        filter_change(filterId);
    });

    $('#filter-select{% if action_index %}-{{action_index}}{% endif %}').on('change', function() {
        filter_change(this.value);
    })

    function filter_change(filterId){
        if (filterId) {
            var filterChoice = document.getElementById("filter-choice{% if action_index %}-{{action_index}}{% endif %}");
            var filterChoiceField = document.getElementById("filter-choice-field{% if action_index %}-{{action_index}}{% endif %}");
            filterChoiceField.classList.remove('d-none');
            filterChoice.innerHTML = '';
            
            {% for filter in filters %}
                if (filterId === '{{filter.id}}') {
                    var choiceValueStr = `{{filter.choice_value|safe}}`
                    //Replace ' with "
                    choiceValueStr = choiceValueStr.replace(/'/g, '"');
                    var choiceValue = JSON.parse(choiceValueStr);
                    filterChoice.innerHTML += `<option value=""></option>`;
                    for (var i = 0; i < choiceValue.length; i++) {
                        filterChoice.innerHTML += `<option value="${choiceValue[i].value}">${choiceValue[i].label}</option>`;
                    }
                }
            {% endfor %}

            {% if input_data.filter_choice %}
            //Loop through the options and check if the value is in the list
            var selectedValue = '{{input_data.filter_choice|safe}}';

            if (selectedValue) {
                var options = filterChoice.options;
                for (var i = 0; i < options.length; i++) {
                    if (options[i].value === selectedValue) {
                        filterChoice.selectedIndex = i;
                        break;
                    }
                }
            }
            {% endif %}
        }
    }

    
    $('#filter-select-2').select2();
    $('#filter-select-2').on('select2:select', function (e) {
        var selectElement = $(this).closest('select').get(0);
        selectElement && selectElement.dispatchEvent(new Event('htmx-change'));
    });
    htmx.on('htmx:afterRequest', (evt) => {
        //check which element triggered the htmx request. If it's the one you want call the function you need
        //you have to add htmx: before the event ex: 'htmx:afterRequest'
        if (evt.detail.elt.id === 'filter-select-2') {
            // Your custom code here
            $('#filter-select-2').val(null).trigger('change.select2');
        }
    })
</script>