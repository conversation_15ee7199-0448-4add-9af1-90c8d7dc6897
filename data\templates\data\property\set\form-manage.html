{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="border-0 card shadow-none rounded-0 bg-white">
    <style>
        .form-control[readonly] {
            background-color: #F6F1E9;
            opacity: 1; !important
        } 
    </style>

    <div class="card-header" id="kt_help_header">
        <h5 class="{% include "data/utility/card-header.html" %}">
            {% if set %}
                {% if LANGUAGE_CODE == 'ja'%}
                    フォームを管理
                {% else %}
                    Manage Form
                {% endif %}
            {% else %}
                {% if LANGUAGE_CODE == 'ja'%}
                    フォームを作成
                {% else %}
                    Create Form
                {% endif %}
            {% endif %}
        </h5>
        <div class="card-toolbar">
            <button type="button" class="btn btn-sm btn-icon explore-btn-dismiss me-n5" data-kt-drawer-dismiss="true">
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="black"></rect>
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="black"></rect>
                    </svg>
                </span>
            </button>
        </div>
    </div>

    <div class="card-body">
            <input id="page_group_type" hidden name="page_group_type" value="{{page_group_type}}">
            <div class="mb-5">
                <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0 required">
                    {% if LANGUAGE_CODE == 'ja'%}
                    名前
                    {% else %}
                    Name
                    {% endif %}
                </span>
                <form id="form-set-property-c5a51f20">
                    {% if set and not set.name %}
                        <input id="name" readonly required="true" class="form-control" type="text" name="name"
                            placeholder="{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %}"
                        /> 
                    {% else %}         
                        <input id="name" required="true" class="form-control" type="text" name="name"
                            placeholder="{% if LANGUAGE_CODE == 'ja' %}名前{% else %}Name{% endif %}"
                            {% if set %}value="{{set.name}}"{% endif %}
                        /> 
                    {% endif %}
                </form>
            </div>
            
            <div class="mb-5">
                <span class="fs-5 fw-bolder text-active-primary ms-0 mb-3 py-0 border-0">
                    {% if LANGUAGE_CODE == 'ja'%}
                    プロパティ
                    {% else %}
                    Properties
                    {% endif %}
                </span>

                <input id="child-properties"  class="form-control w-100 rounded-2 mb-4" name="children"
                    {% if LANGUAGE_CODE == 'ja'%}
                    placeholder="プロパティ" 
                    {% else %}
                    placeholder="Properties" 
                    {% endif %}
                />
            </div>
            
            {% if page_group_type == 'slips' %}
                <div class="mb-10">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="required">
                            {% if LANGUAGE_CODE == 'ja'%}デフォルト伝票タイプ{% else %}Default Slip Type{% endif %}
                        </span>
                    </label>
                    <select class="form-control h-40px select2-this" id="sub-children" name="sub_children" data-placeholder="{% if LANGUAGE_CODE == 'ja' %}伝票種類を選択{% else %}Select Slip Type{% endif %}">  
                        <option></option>
                            {% if 'slip_type'|get_custom_property_object:obj %}
                                {% with value_map_label='slip_type'|get_custom_property_object:obj|get_attr:'value'|string_list_to_list %}
                                    {% for value, label in value_map_label.items %}
                                        <option value="{{value}}" 
                                            {% if set.sub_children %}
                                                {% if set.sub_children == value %}
                                                    selected
                                                {% endif %}
                                            {% endif %}>
                                            {{label}}
                                        </option>
                                    {% endfor %}
                                {% endwith %}
                            {% else %}
                                {% for status in SLIP_TYPE %}
                                    {% if set.sub_children ==  status.0%}
                                        <option value="{{status.0}}" selected>
                                            {{status.0|get_display_slip_type:request}}
                                        </option>
                                    {% else %}
                                        <option value="{{status.0}}">
                                            {{status.0|get_display_slip_type:request}}
                                        </option>
                                    {% endif %}
                                {% endfor %}
                            {% endif %}
                    </select>
                </div>
            {% endif %}
            {% if set %}
                {% if page_group_type == "purchaseorder" or page_group_type == constant.TYPE_OBJECT_ORDER or page_group_type == constant.TYPE_OBJECT_CASE or page_group_type == 'estimates' %}
                    <div class="mb-4"
                        hx-get="{% host_url 'custom_property_list' host 'app' %}"
                        hx-vals='{"page_group_type": "{{page_group_type}}", "view_id":"{{view_id}}" , "form_id":"{{set.id}}" }'
                        hx-trigger="load"
                        hx-swap="#property-custom-table"
                    >
                        <div id="property-custom-table"></div>
                    </div> 
                {% endif %}
            {% endif %}
        
        <div class="mt-5">
            {% if not set %}
                <button class="btn btn-dark" id="submitBtn-c5a51f20"
                {% if view_form_trigger_from_drawer %} onclick="trigger_manage_or_Create_drawer(this)" {% endif %}
                {% if set %}
                    hx-post="{% host_url 'form_sets_manage' set.id host 'app' %}"
                {% else %}
                    hx-post="{% host_url 'form_sets_new' host 'app' %}"
                {% endif %}
                id="submitBtn"
                hx-target="#temp"
                hx-swap="innerHTML"
                hx-trigger="clickOnValidateForm[validateRequiredFields()]"
                hx-vals="js:{
                                {% if page_group_type == 'slips' %}
                                    'sub_children':document.getElementById('sub-children').value,
                                {% endif %}
                                'page_group_type':document.getElementById('page_group_type').value, 
                                'name':document.getElementById('name').value, 
                                'csrfmiddlewaretoken':'{{csrf_token}}',
                                'children':document.getElementById('child-properties').value
                            }"
                hx-on::before-request="document.getElementById('temp').innerHTML = '';"
                >
                    {% if LANGUAGE_CODE == 'ja'%}
                    作成
                    {% else %}
                    Create
                    {% endif %}
                </button>
            {% elif set %}
                
                {% if page_group_type|in_list:"purchaseorder,journal,slips,commerce_orders,customer_case,timegenie,commerce_items,contacts,company,estimates,invoices,receipts,delivery_slips,commerce_subscription,commerce_inventory,commerce_inventory_transaction,commerce_inventory_warehouse,expense,bill,task,commerce_meter" %}
                    {% if set %}
                        <button class="btn btn-dark" id="submitBtn-c5a51f20"
                        {% if view_form_trigger_from_drawer %} onclick="trigger_manage_or_Create_drawer(this)" {% endif %} 
                        
                        hx-post="{% host_url 'form_sets_manage' set.id host 'app' %}"
                        hx-target="#temp"
                        hx-on::before-request="document.getElementById('temp').innerHTML = '';"
                        {% if page_group_type == constant.TYPE_OBJECT_PURCHASE_ORDER or page_group_type == constant.TYPE_OBJECT_ORDER or page_group_type == constant.TYPE_OBJECT_CASE or page_group_type == constant.TYPE_OBJECT_ESTIMATE %}
                            hx-include="[name='enable_status'],[name='property_status_default'],[name='field_value']"
                        {% endif %}
                        hx-swap="innerHTML"
                        hx-trigger="clickOnValidateForm"
                        hx-vals="js:{
                                    {% if page_group_type == 'slips' %}
                                        'sub_children':document.getElementById('sub-children').value,
                                    {% endif %}
                                    'page_group_type':document.getElementById('page_group_type').value, 
                                    'object_type':document.getElementById('page_group_type').value, 
                                    'name':document.getElementById('name').value, 
                                    'csrfmiddlewaretoken':'{{csrf_token}}',
                                    'children':document.getElementById('child-properties').value
                                    }"
                        
                        >
                            {% if LANGUAGE_CODE == 'ja'%}
                            更新
                            {% else %}
                            Update
                            {% endif %}
                        </button>
                    {% endif %}
                {% endif %}
                    
                {% if set.name %}
                <button id="deleteButton" name="delete-set" type="submit" class="btn btn-danger ms-2"
                {% if view_form_trigger_from_drawer %} onclick="trigger_manage_or_Create_drawer(this)" {% endif %}
                
                {% if set %}
                    hx-post="{% host_url 'form_sets_manage' set.id host 'app' %}"
                {% else %}
                    hx-post="{% host_url 'form_sets_new' host 'app' %}"
                {% endif %}
                    hx-target="#temp"
                    hx-swap="innerHTML"
                    hx-trigger="click"
                    hx-vals="js:{'page_group_type':document.getElementById('page_group_type').value, 
                                'delete-set':document.getElementById('deleteButton').value, 
                                'csrfmiddlewaretoken':'{{csrf_token}}'
                                }"
                    hx-on::after-request="if(event.detail.successful) { htmx.trigger('#backButton', 'click'); }"
                
                >
                    {% if LANGUAGE_CODE == 'ja'%}
                    削除
                    {% else %}
                    Delete
                    {% endif %}
                </button>

                {% endif %}
            {% endif %}
            <div id="temp" hidden></div>
            {% if page_group_type|in_list:"commerce_orders,commerce_items,contacts,company,purchaseorder,customer_case,timegenie,task,commerce_subscription,commerce_inventory,commerce_inventory_transaction,commerce_inventory_warehouse,expense,bill" %}
                <button id="backButton" class="btn border ms-2"
                {% if view_form_trigger_from_drawer %} onclick="trigger_manage_or_Create_drawer(this)"
                {% else %}
                        {% if page_group_type == "commerce_orders" %}
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals='{"drawer_type":"shopturbo-view-settings","page": "orders", "view_id":"{{view_id}}"}'
                        hx-target="#manage-contacts-view-settings-drawer"
                        hx-indicator=".loading-drawer-spinner,.view-form"
                        hx-on::before-request="document.getElementById('manage-contacts-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == "commerce_items" %}
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals='{"drawer_type":"shopturbo-view-settings","page": "items", "view_id":"{{view_id}}"}'
                        hx-target="#manage-contacts-view-settings-drawer"
                        hx-indicator=".loading-drawer-spinner,.view-form"
                        hx-on::before-request="document.getElementById('manage-contacts-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == "commerce_subscription" %}
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals='{"drawer_type":"shopturbo-view-settings","page": "subscriptions", "view_id":"{{view_id}}"}'
                        hx-target="#manage-contacts-view-settings-drawer"
                        hx-indicator=".loading-drawer-spinner,.view-form"
                        hx-on::before-request="document.getElementById('manage-contacts-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == "commerce_inventory" %}
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals='{"drawer_type":"shopturbo-view-settings","page": "inventory", "view_id":"{{view_id}}"}'
                        hx-target="#manage-contacts-view-settings-drawer"
                        hx-indicator=".loading-drawer-spinner,.view-form"
                        hx-on::before-request="document.getElementById('manage-contacts-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == "commerce_inventory_transaction" %}
                        hx-get="{% url 'shopturbo_load_drawer' %}" 
                        hx-vals='{"drawer_type":"shopturbo-view-settings","page": "inventory-transaction", "view_id":"{{view_id}}"}'
                        hx-target="#manage-contacts-view-settings-drawer"
                        hx-on::before-request="document.getElementById('manage-contacts-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == "commerce_inventory_warehouse" %}
                        hx-get="{% url 'commerce_view_setting' %}" 
                        hx-vals='{"object_type": "commerce_inventory_warehouse","view_id":"{{view_id}}"}'
                        hx-target="#manage-contacts-view-settings-drawer"
                        hx-on::before-request="document.getElementById('manage-contacts-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == "contacts" %}
                        hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                        hx-vals='{"drawer_type":"contacts-view-settings","page": "contacts","view_id":"{{view_id}}"}'
                        hx-target="#manage-update-view-settings-drawer"
                        hx-on::before-request="document.getElementById('manage-update-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == "company" %}
                        hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                        hx-vals='{"drawer_type":"contacts-view-settings","page": "companies","view_id":"{{view_id}}"}'
                        hx-target="#manage-update-view-settings-drawer"
                        hx-on::before-request="document.getElementById('manage-update-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == "customer_case" %}
                        hx-get="{% url 'new_customerlinkapp_drawer' %}" 
                        hx-vals='{"drawer_type":"contacts-view-settings","page": "deals","view_id":"{{view_id}}"}'
                        hx-target="#manage-view-settings-drawer"
                        hx-on::before-request="document.getElementById('manage-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type == constant.TYPE_OBJECT_TASK %}
                        hx-get="{% url 'commerce_view_setting' %}" 
                        hx-vals='{"object_type":"{{constant.TYPE_OBJECT_TASK}}","view_id":"{{view_id}}", "p_id":"{{project_id}}"}'
                        hx-target="#view-drawer" 
                        hx-indicator=".loading-drawer-spinner,.view-form"
                        hx-on::before-request="document.getElementById('view-drawer').innerHTML = '';"
                        {% elif page_group_type == "timegenie" %}
                        hx-vals='{"page":"timegenie", "type":"update_view", "view_id":"{{view_id}}", "view_main": "{{view_main}}"}'
                        hx-get="{% url 'timegenie_view_drawer' %}" 
                        hx-target="#manage-update-view-settings-drawer"
                        hx-on::before-request="document.getElementById('manage-update-view-settings-drawer').innerHTML = '';"
                        {% elif page_group_type|in_list:"purchaseorder,expense,bill" %}
                        hx-get="{% url 'commerce_view_setting' %}" 
                        hx-vals='{"object_type": "{{page_group_type}}", "view_id":"{{view_id}}"}'
                        hx-target="#expenses_form"
                        hx-on::before-request="document.getElementById('expenses_form').innerHTML = ''"
                        hx-indicator=".loading-drawer-spinner,.expenses-form"
                        {% endif %}
                        hx-trigger='click'
                    {% endif %}
                    >
                    {% if LANGUAGE_CODE == 'ja'%}
                    戻る
                    {% else %}
                    Back 
                    {% endif %}
                </button>
            {% else %}
                <button id="backButton" class="btn border ms-2"
                {% if view_form_trigger_from_drawer %} onclick="trigger_manage_or_Create_drawer(this)" 
                {% else %}
                        hx-vals='{"object_type":"{{page_group_type}}","view_id":"{{view_id}}"}'
                        hx-get="{% url 'commerce_view_setting' %}" 
                        hx-target="#manage-contacts-view-settings-drawer"
                        hx-indicator=".loading-drawer-spinner,.view-form"
                        hx-trigger='click'
                        hx-on::before-request="document.getElementById('manage-contacts-view-settings-drawer').innerHTML = '';"
                {% endif %}
                    >
                    {% if LANGUAGE_CODE == 'ja'%}
                    戻る
                    {% else %}
                    Back
                    {% endif %}
                </button>
            {% endif %}
        </div>

        <script>
            (function() {
                let listenersAdded = false;

                if (!listenersAdded) {
                    const form = document.getElementById("form-set-property-c5a51f20");
                    const button = document.getElementById("submitBtn-c5a51f20");

                    button.addEventListener("click", (event) => {
                        console.log('Button clicked');
                        // Prevent default button behavior
                        // event.preventDefault();

                        // Check if form element is there
                        if (!form) {
                            button.dispatchEvent(
                                new CustomEvent("clickOnValidateForm", {
                                    bubbles: true,
                                    cancelable: true,
                                })
                            );
                        }

                        form.checkValidity()

                        // Check form validity
                        if (form.checkValidity()) {
                            // Trigger the custom event for HTMX
                            button.dispatchEvent(
                                new CustomEvent("clickOnValidateForm", {
                                    bubbles: true,
                                    cancelable: true,
                                })
                            );
                        } else {
                            // Show native validation messages
                            form.reportValidity();
                        }
                    });

                    button.addEventListener("clickOnValidateForm", () => {
                        console.log("HTMX custom trigger event fired.");
                    });

                    //listen htmx after request
                    document.querySelector("#submitBtn-c5a51f20").addEventListener("htmx:afterRequest", (event) => {
                        console.log("HTMX after request fired.");
                        if(event.detail.successful) {htmx.trigger('#backButton', 'click');}
                    });

                    document.querySelector("#backButton").addEventListener("click", () => {
                        console.log("Back button clicked!");
                    });

                    listenersAdded = true;
                }
            })();
        </script>
    </div>
</div>
<script>
    $('.select2-this').select2();
    $(document).ready(function() {   
        var tagify = new Tagify(document.getElementById("child-properties"), {
            whitelist: [
                {% for property in properties %}
                    {% with args=property.id|stringify|add:'|'|add:page_group_type %} 
                        {% with column_display=args|get_column_display:request %}
                            {% if property.id in required_properties %}
                                {% if " - line user id" in column_display.name|lower %}
                                    { "value": "{{column_display.name|get_line_name_channel}}", "id": "{{column_display.id}}", "readonly": "true"},
                                {% else %}
                                    { "value": "{{column_display.name}}", "id": "{{column_display.id}}", "readonly": "true"},
                                {% endif %}
                            {% else %}
                                {% if " - line user id" in column_display.name|lower %}
                                    { "value": "{{column_display.name|get_line_name_channel}}", "id": "{{column_display.id}}" },
                                {% else %}
                                    { "value": "{{column_display.name}}", "id": "{{column_display.id}}" },
                                {% endif %}
                            {% endif %}
                        {% endwith %}
                    {% endwith %}
                {% endfor %}
            ],
            enforceWhitelist: true,
            dropdown: {
                maxItems: {{ properties|length }},           // <- mixumum allowed rendered suggestions
                classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
                enabled: 0,             // <- show suggestions on focus
                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
            },
            originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(',')
        });

        {% for property in set.children %}
            {% with args=property|add:'|'|add:page_group_type %} 
                {% with column_display=args|get_column_display:request %}
                    {% if ' - line user id' in column_display.name|lower %}
                        tagify.addTags([{ value: "{{column_display.name|get_line_name_channel}}", id: "{{column_display.id}}"}]);
                    {% else %}
                        tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}"}]);
                    {% endif %}
                {% endwith %}
            {% endwith %}
        {% empty %}
            {% for property in properties %}
                {% if property.id in required_properties %}
                    {% with args=property.id|add:'|'|add:page_group_type %} 
                        {% with column_display=args|get_column_display:request %}
                            {% if ' - line user id' in column_display.name|lower %}
                                tagify.addTags([{ value: "{{column_display.name|get_line_name_channel}}", id: "{{column_display.id}}"}]);
                            {% else %}
                                tagify.addTags([{ value: "{{column_display.name}}", id: "{{column_display.id}}"}]);
                            {% endif %}
                        {% endwith %}
                    {% endwith %}
                {% endif %}
            {% endfor %}
        {% endfor %}

        var dragsort = new DragSort(tagify.DOM.scope, {
            selector:'.'+tagify.settings.classNames.tag,
            callbacks: {
                dragEnd: onDragEnd
            }
        })
    
        function onDragEnd(elm){
            tagify.updateValueByDOMTags()
        }
    })

    function onSubmitForm(event) {
        document.getElementById("default_form").click();
        return true;
    }

    var updateButton =  document.getElementById('updateButton')
    if (updateButton){
        document.getElementById('updateButton').addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.successful) {
                document.getElementById('backButton').click();
            }
        });
    }
    
    var deleteButton = document.getElementById('deleteButton')
    if (deleteButton){
        document.getElementById('deleteButton').addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.successful) {
                document.getElementById('backButton').click();
            }
        });
    }

    function trigger_manage_or_Create_drawer(elm){
        // Check if it's a back button
        if (elm.id === 'backButton') {
            // Skip validation for back button
        } else {
            // Check form validity for other buttons
            const form = document.getElementById("form-set-property-c5a51f20");
            if (!form.checkValidity()) {
                form.reportValidity();
                return; // Exit the function if form is not valid
            }
        }

        var view_form_trigger_from_drawer = "{{view_form_trigger_from_drawer}}";
        console.log("view_form_trigger_from_drawer: ",view_form_trigger_from_drawer)
        if (view_form_trigger_from_drawer){
            // Find all elements with the class
            var elements = document.querySelectorAll('.' + view_form_trigger_from_drawer);
            console.log("====== elements: ",elements)
            // Check if at least one element exists
            if (elements.length > 0) {
                // Only get the first element
                var firstElement = elements[0];
                
                // First click happens after 800ms delay
                setTimeout(() => {
                    firstElement.click();
                }, 800);
                
                // Second click happens after 1300ms (800ms + 500ms) delay
                setTimeout(() => {
                    firstElement.click();
                }, 200);
            }
            else{
                // First click happens after 800ms delay
                setTimeout(() => {
                    elements.click();
                }, 800);
                
                // Second click happens after 1300ms (800ms + 500ms) delay
                setTimeout(() => {
                    elements.click();
                }, 200);
            }
        }
    }
    
</script>
<script>
    function validateRequiredFields() {
        const form = document.getElementById("form-set-property-c5a51f20");
        return form.checkValidity();
    }
</script>
