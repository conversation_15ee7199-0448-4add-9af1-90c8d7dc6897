import traceback
import re
import uuid
import json

from django.core.paginator import EmptyPage, Paginator
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, OuterRef, Subquery
from django.db.models.functions import Cast

# Signal
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.http import require_GET
from django_hosts.resolvers import reverse
from hubspot import HubSpot

from data.applog import (
    manual_log,
)  # main initiation for All log. If you delete tell <PERSON><PERSON><PERSON>
from data.association import update_associate_object
from data.commerce import get_page_object, view_settings
from data.constants.associate_constant import ASSOCIATE_MAP, SOURCE_ASSOCIATE_MAP
from data.constants.contact_constant import (
    CONTACT_APP_SLUG,
    CONTACT_APP_TARGET,
    POSTS_PER_PAGE,
)
from data.constants.properties_constant import (
    TYPE_OBJECT_CASE,
    TYPE_OBJECT_ORDER,
    TYPE_OBJECT_TASK,
    TYPE_OBJECT_INVOICE,
    TYPE_OBJECT_COMPANY,
    TYPE_OBJECT_ESTIMATE,
    TYPE_OBJECT_CONTACT,
    OBJECT_TYPE_TO_SLUG,
    DEFAULT_OBJECT_DISPLAY,
)
from data.models import (
    Module,
    AdvanceSearchFilter,
    Deals,
    DealsNameCustomField,
    DealsValueCustomField,
    PropertySet,
    CustomProperty,
    TransferHistory,
    Notification,
    View,
    ViewFilter,
    DealsMappingFields,
    ContactsMappingFields,
    ContactsNameCustomField,
    CompanyNameCustomField,
    Channel,
    Association,
    Invoice,
    Estimate,
    Contact,
    Company,
    Task,
    DealsItems,
    ShopTurboItems,
    ShopTurboItemsPrice,
    DealsItemsValueCustomField,
    ImportMappingFields,
    Notes,
    AppLog,
    ObjectManager,
    DealsItemsNameCustomField,
    CompanyValueCustomField,
    ContactsValueCustomField,
)
from utils.decorator import login_or_hubspot_required
from utils.eccube import *
from utils.filter import build_view_filter, check_columns_type
from utils.freee_bg_jobs.freee import *
from utils.hubspot import import_hubspot_orders_as_deals
from utils.bgjobs.runner import trigger_bg_job
from utils.project import get_ordered_views
from utils.properties.properties import (
    get_default_property_set,
    get_object_display_based_columns,
)
from utils.salesforce import *
from utils.salesforce.cases import get_case_mapping_fields
from utils.serializer import *
from utils.twitter import *
from utils.utility import (
    get_workspace,
    is_valid_uuid,
    modular_view_filter,
    read_csv,
    save_custom_property,
    update_query_params_url,
    get_permission_filter,
    assign_object_owner,
)
from utils.workspace import get_permission
from data.case.background.export_csv_cases import ExportCSVCasesPayload, export_csv_cases
from data.orders.background.import_orders import ImportOrdersPayload, import_orders as hatchet_import_orders
from data.case.background.export_cases import ExportCasesPayload, export_cases as hatchet_export_cases
from utils.bgjobs.handler import add_hatchet_run_id, create_bg_job
from data.case.background.import_salesforce_cases import ImportSalesforceCasesPayload, import_salesforce_cases_task
from utils.logger import logger
from data.accounts.association_labels import save_association_label

@login_or_hubspot_required
def deals(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if lang == "ja":
        page_title = "案件"
    else:
        page_title = "Cases"

    CASE_POSTS_PER_PAGE = POSTS_PER_PAGE
    CASE_PER_PAGE_BEGIN = CASE_POSTS_PER_PAGE - 1

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug

    page_obj = get_page_object(TYPE_OBJECT_CASE, lang)
    base_model = page_obj["base_model"]
    custom_value_model = page_obj["custom_value_model"]
    custom_value_relation = page_obj["custom_value_relation"]
    custom_value_file_model = page_obj["custom_value_file_model"]
    custom_value_file_relation = page_obj["custom_value_file_relation"]
    additional_filter_fields = page_obj["additional_filter_fields"]
    id_field = page_obj["id_field"]

    if request.method == "GET":
        config_view = None
        view_id = request.GET.get("view_id", None)
        case_id = request.GET.get("id", None)

        target = "customer_case"
        order_by = ["-deal_id"]
        permission = get_permission(object_type=target, user=request.user)
        if not permission:
            permission = DEFAULT_PERMISSION

        if permission == "hide":
            context = {
                "permission": permission,
                "page_title": page_title,
            }
            return render(request, "data/contacts/cases.html", context)

        views = get_ordered_views(workspace, target, user=request.user)
        view_filter = modular_view_filter(
            workspace,
            target,
            view_id=view_id,
            column_view=DEFAULT_COLUMNS_CASE.copy(),
            user=request.user,
        )
        config_view = view_filter.view_type
        archive = view_filter.archive
        deals_columns = view_filter.column

        pagination_number = view_filter.pagination
        if not pagination_number:
            pagination_number = 25
        CASE_POSTS_PER_PAGE = pagination_number
        CASE_PER_PAGE_BEGIN = CASE_POSTS_PER_PAGE - 1

        # Filter by Search Keywords
        filter_conditions = Q(workspace=workspace)

        filter_conditions &= get_permission_filter(permission, request.user)

        # Handle status filter for archived items
        status = request.GET.get("status")
        if status == "archived":
            filter_conditions &= Q(status="archived")
        else:
            # By default, show only active items
            filter_conditions &= Q(status="active")

        boardStatus = request.GET.get("boardStatus", None)
        if boardStatus and config_view == "kanban":
            CASE_POSTS_PER_PAGE = 10
            CASE_PER_PAGE_BEGIN = CASE_POSTS_PER_PAGE - 1
            default_field_names = [field.name for field in Deals._meta.get_fields()]
            if view_filter.choice_customfield in default_field_names:
                if boardStatus == "unlisted":
                    filter_conditions &= Q(
                        **{f"{view_filter.choice_customfield}__isnull": True}
                    ) | Q(**{f"{view_filter.choice_customfield}": ""})
                else:
                    filter_conditions &= Q(
                        **{f"{view_filter.choice_customfield}": boardStatus}
                    )
            else:
                if boardStatus == "unlisted":
                    filter_conditions &= (
                        ~Q(
                            deals_custom_field_relations__field_name__id=view_filter.choice_customfield
                        )
                        | Q(deals_custom_field_relations__isnull=True)
                    ) | (
                        Q(
                            deals_custom_field_relations__field_name__id=view_filter.choice_customfield
                        )
                        & (
                            Q(deals_custom_field_relations__value__isnull=True)
                            | Q(deals_custom_field_relations__value="")
                        )
                    )
                else:
                    filter_conditions &= Q(
                        deals_custom_field_relations__field_name__id=view_filter.choice_customfield,
                        deals_custom_field_relations__value=boardStatus,
                    )

            if view_filter.kanban_order == "manual":
                order_by = ["kanban_order", "-deal_id"]
            if view_filter.kanban_order == "created_at":
                order_by = ["-created_at"]

        advance_search = AdvanceSearchFilter.objects.filter(
            workspace=workspace, object_type=TYPE_OBJECT_CASE, type="default"
        ).first()
        if not advance_search:
            advance_search = None

        app_setting, _ = AppSetting.objects.get_or_create(
            workspace=workspace, app_target=CONTACT_APP_TARGET
        )

        if advance_search:
            if advance_search.search_settings:
                app_setting.search_setting_case = advance_search.search_settings
                app_setting.save()
        print("search settings:", app_setting.search_setting_case)

        search_q = request.GET.get("q")
        if search_q:
            match_special_char = re.search(r"#(\d+)", search_q)
            if match_special_char:
                number = match_special_char.group(1)  # Get the number part
                if number:
                    filter_conditions &= Q(deal_id=number)
            else:
                filter_conditions &= (
                    (
                        Q(name__icontains=search_q)
                        | Q(contact__name__icontains=search_q)
                        | Q(company__name__icontains=search_q)
                    )
                    | Q(created_at_str__icontains=search_q)
                    | Q(updated_at_str__icontains=search_q)
                    | Q(deal_id__icontains=search_q)
                )

                search_filters = Q()
                search_filters |= Q(deal_id__icontains=search_q)
                if app_setting.search_setting_case:
                    search_fields = app_setting.search_setting_case.split(",")
                    search_fields = [field for field in search_fields]
                    force_filter_list = []

                    for s_field in search_fields:
                        search_filters |= apply_search_setting(
                            "customer_case",
                            view_filter,
                            s_field,
                            search_q,
                            force_filter_list=force_filter_list,
                        )

                filter_conditions &= search_filters

        if advance_search:
            if advance_search.is_active:
                advance_search_filter = advance_search.search_filter
                advance_search_filter_status = advance_search.search_filter_status
                if advance_search_filter_status:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                        and advance_search_filter_status.get(k) != "False"
                    }
                else:
                    advance_search_filter = {
                        k: v
                        for k, v in advance_search_filter.items()
                        if v["value"] != ""
                    }
                try:
                    if (
                        advance_search_filter.get("usage_status", {}).get("value")
                        == "all"
                    ):
                        del advance_search_filter["usage_status"]
                except KeyError:
                    pass

                view_filter.filter_value = advance_search_filter

        filter_conditions = build_view_filter(
            filter_conditions, view_filter, TYPE_OBJECT_CASE, request=request
        )

        # print('.................... Deals filter:', filter_conditions)
        deals = (
            Deals.objects.annotate(
                created_at_str=Cast("created_at", CharField()),
                updated_at_str=Cast("updated_at", CharField()),
            )
            .filter(filter_conditions)
            .order_by(*order_by)
            .distinct()
        )

        try:
            if view_filter.sort_order_by:
                sort_order_method = view_filter.sort_order_method
                sort_order_by = view_filter.sort_order_by

                if is_valid_uuid(sort_order_by):
                    field_name = DealsNameCustomField.objects.filter(
                        id=sort_order_by
                    ).first()
                    if field_name:
                        print(f"===== DEBUG: Sorting by custom field: {field_name.name} (type: {field_name.type}) =====")
                        custom_value_subquery = DealsValueCustomField.objects.filter(
                            deals=OuterRef("pk"), field_name=field_name
                        )
                        if field_name.type in ["date", "date_time"]:
                            custom_value_subquery = custom_value_subquery.values(
                                "value_time"
                            )[:1]
                        else:
                            custom_value_subquery = custom_value_subquery.values(
                                "value"
                            )[:1]

                        deals = deals.annotate(
                            custom_value=Subquery(custom_value_subquery)
                        )

                        if sort_order_method == "asc":
                            deals = deals.distinct("id", "custom_value").order_by(
                                "custom_value"
                            )
                        else:
                            deals = deals.distinct("id", "custom_value").order_by(
                                "-custom_value"
                            )
                else:
                    # handling customer|company|phone_number and similar
                    temp_sort_order = sort_order_by.split("|")
                    if len(temp_sort_order) == 3:
                        if temp_sort_order[2] == "first_name":
                            temp_sort_order[2] = "name"
                        sort_order_by = f"{temp_sort_order[1]}__{temp_sort_order[2]}"

                    if sort_order_method == "asc":
                        deals = deals.distinct("id", sort_order_by).order_by(
                            sort_order_by
                        )
                    else:
                        deals = deals.distinct("id", sort_order_by).order_by(
                            "-" + sort_order_by
                        )
            else:
                if (
                    not boardStatus and not config_view == "kanban"
                ):  # Execute below code if not in kanban
                    deals = deals.distinct("id", "deal_id").order_by(*order_by)

        except Exception as e:
            print("===== Debug Error at Deals =====", e)
            if (
                not boardStatus and not config_view == "kanban"
            ):  # Execute below code if not in kanban
                deals = deals.distinct("id", "deal_id").order_by(*order_by)

        # Debug: Show available custom fields for sorting
        available_custom_fields = DealsNameCustomField.objects.filter(workspace=workspace)
        print("===== DEBUG: Available custom fields for sorting =====")
        for cf in available_custom_fields:
            print(f"  - {cf.name} (type: {cf.type}, id: {cf.id})")
        print("===== END DEBUG =====")

        default_property_set = get_default_property_set(
            TYPE_OBJECT_CASE, workspace, lang
        )
        property_sets = PropertySet.objects.filter(
            workspace=workspace, target=TYPE_OBJECT_CASE
        ).order_by("created_at")

        set_id = request.GET.get("set_id")
        if view_filter.view.form:
            set_id = view_filter.view.form.id
        else:
            if not set_id:
                property_set_default = property_sets.filter(as_default=True).first()
                if property_set_default:
                    set_id = property_set_default.id

        try:
            paginator = Paginator(deals, CASE_POSTS_PER_PAGE)
            page = request.GET.get("page", 1)
            if not page:
                page = 1

            if page:
                page_content = paginator.page(page)
                deals = page_content.object_list
            else:
                page_content = paginator.page(1)
                deals = page_content.object_list
        except:
            page = 1
            deals = None
            page_content = None

        paginator_item_begin = (CASE_POSTS_PER_PAGE * int(page)) - CASE_PER_PAGE_BEGIN
        paginator_item_end = CASE_POSTS_PER_PAGE * int(page)

        members = []
        groups = request.user.group_set.all()
        for group in groups:
            ids = group.user.all().values_list("id", flat=True)
            members.extend(ids)

        members = list(set(members))
        members = ",".join(str(id) for id in members)

        context = {
            "page_title": page_title,
            "app_slug": CONTACT_APP_SLUG,
            "search_q": search_q,
            "deals_columns": deals_columns,
            "deals": deals,
            "paginator_item_begin": paginator_item_begin,
            "paginator_item_end": paginator_item_end,
            "page_content": page_content,
            "paginator": paginator,
            "config_view": config_view,
            "view_filter": view_filter,
            "view_id": view_id,
            "target": target,
            "views": views,
            # Breaking
            # 'case_id': case_id,
            "permission": permission,
            "property_sets": property_sets,
            "open_drawer": request.GET.get("open_drawer"),
            "side_drawer": request.GET.get("sidedrawer"),
            "set_id": set_id,
            "advance_search": advance_search,
            "group_members": members,
        }

        if config_view == "kanban" and boardStatus:
            custom_property = CustomProperty.objects.filter(
                workspace=workspace, model=Deals._meta.db_table, name="case_status"
            ).first()
            if custom_property:
                custom_property.value = ast.literal_eval(custom_property.value)
                property_value_list = list(custom_property.value.keys())
                excl_conditions = (
                    Q(case_status__in=property_value_list)
                    | Q(case_status__isnull=True)
                    | Q(case_status="")
                )
                Deals.objects.filter(workspace=workspace).exclude(
                    excl_conditions
                ).update(case_status=None)

            # ast.literal_eval(custom_property.value)
            if "checkbox" in view_filter.column:
                view_filter.column.remove("checkbox")
            context["boardStatus"] = boardStatus
            context["view_filter"] = view_filter
            return render(
                request, "data/common/kanban/kanban_case_template.html", context
            )

        return render(request, "data/contacts/cases.html", context)

    else:
        if "csv_download" in request.POST or "download" in request.POST:
            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                status="running",
                type="export_contact",
            )

            encoded_format = request.POST.get("encoded_format", None)

            columns = request.POST.get("column", None)
            view_id = request.POST.get("view_id", None)

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }

            payload = ExportCSVCasesPayload(
                user_id=str(request.user.id),
                workspace_id=str(workspace.id),
                view_id=str(view_id),
                history_id=str(history.id),
                columns=columns,
                filter_dictionary=filter_dictionary,
                encoded_format=encoded_format,
                language=lang,
                target=TYPE_OBJECT_CASE,
            )
            
            job_id = create_bg_job(
                workspace=workspace,
                user=request.user,
                function_name="export_csv_case",
                transfer_history=history,
                payload=payload.model_dump(mode="json"),
            )
            payload.background_job_id = job_id
            
        
            try:
                ref = export_csv_cases.run_no_wait(input=payload)
            except Exception as e:
                logger.error(f"EXPORT_JOB: Exception occurred during export_csv_cases: {str(e)}", exc_info=True)
                ref = None
                
            is_running = None
            if ref:
                logger.info(f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}")
                add_hatchet_run_id(job_id, ref)
                is_running = True
            else:
                logger.error(f"EXPORT_JOB: Failed to submit background job for user {request.user.email}")
                is_running = False
                
                
            if is_running:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブが正常に送信されました。CSVファイルがメールに送信されます。",
                        type="success",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="Export job submitted successfully, CSV file will be sent to your email.",
                        type="success",
                    )
            else:
                if lang == "ja":
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="エクスポートジョブの送信中にエラーが発生しました。サポートにお問い合わせください。",
                        type="error",
                    )
                else:
                    Notification.objects.create(
                        workspace=workspace,
                        user=request.user,
                        message="There is an error while submitting the export job. Please contact support.",
                        type="error",
                    )

            if module:
                return HttpResponseRedirect(
                    request.META.get(
                        "HTTP_REFERER",
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module,
                                "object_slug": module_object_slug,
                            },
                        ),
                    )
                )
            return HttpResponseRedirect(
                request.META.get("HTTP_REFERER", reverse("main", host="app"))
            )

        elif "bulk_delete_deals" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)
            objects.update(status="archived")

        elif "bulk_restore_deals" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)
            objects.update(status="active")

        elif "update-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            view_name = request.POST.get("view_name", None)
            view_table = request.POST.get("view", None)
            archive = request.POST.get("archive", None)
            column = request.POST.get("column", None)
            kanban_order_by = request.POST.get("kanban-order-by", None)
            kanban_unlisted = (
                True
                if request.POST.get("kanban_unlisted", False) == "on"
                else request.POST.get("kanban_unlisted", False)
            )
            status_selector = request.POST.get("status-selector", None)

            order_by = request.POST.get("order-by", None)
            sort_method = request.POST.get("sort-method", None)
            property_set_is_default_input = request.POST.get(
                "property_set_default", None
            )

            # update
            if view_id:
                view = View.objects.filter(id=view_id).first()
                if view.is_private and (view.user == request.user):
                    view.is_private = bool(request.POST.get("set-private-view", False))
                    view.user = request.user if view.is_private else None
                    view.save()
            # create
            else:
                is_private = bool(request.POST.get("set-private-view", False))
                user = request.user if is_private else None

                view = View.objects.create(
                    workspace=workspace,
                    title=view_name,
                    target="customer_case",
                    is_private=is_private,
                    user=user,
                )

            view_filter, _ = ViewFilter.objects.get_or_create(view=view)

            if property_set_is_default_input:
                default_set = PropertySet.objects.get(id=property_set_is_default_input)
                view.form = default_set
                default_set.as_default = True
                default_set.save()
                # check default
                PropertySet.objects.filter(
                    workspace=default_set.workspace, target=default_set.target
                ).exclude(id=default_set.id).update(as_default=False)

            if view_name:
                view.title = view_name

            if view_table:
                view_filter.view_type = view_table

            if order_by:
                view_filter.sort_order_by = order_by
            else:
                view_filter.sort_order_by = None

            if sort_method:
                view_filter.sort_order_method = sort_method
            else:
                if order_by:
                    view_filter.sort_order_method = "asc"
                else:
                    view_filter.sort_order_method = None

            pagination = request.POST.get("pagination", None)
            if pagination:
                view_filter.pagination = pagination
            else:
                view_filter.pagination = 25

            view_filter.archive = archive
            view_filter.kanban_unlisted = kanban_unlisted
            view_filter.kanban_order = kanban_order_by

            # convert to normal List
            column = column.split(",")
            if view and column[0]:
                view_filter.column = column
            else:
                view_filter.column = None

            if view_table and status_selector:
                if view_table == "kanban":
                    view_filter.choice_customfield = status_selector
                else:
                    view_filter.choice_customfield = None

            filter_dictionary = {}
            filter_types = request.POST.getlist("filter_type", None)
            filter_options = request.POST.getlist("filter_options", None)
            filter_values = request.POST.getlist("filter_value", None)
            for idx, filter_type in enumerate(filter_types):
                filter_dictionary[str(filter_type)] = {
                    "key": filter_options[idx],
                    "value": filter_values[idx],
                }
            view_filter.filter_value = filter_dictionary
            view_filter.save()
            view.save()

            if view.title:
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug

                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + "?view_id="
                    + str(view.id)
                )
            else:
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug

                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))
        elif "delete-view-button" in request.POST:
            view_id = request.POST.get("view_id", None)
            if view_id:
                View.objects.get(id=view_id).delete()
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
                )
                .order_by("order", "created_at")
                .first()
            )
            if module:
                module_slug = module.slug

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

        elif "bulk_duplicate" in request.POST:
            if "flag_all" in request.POST:
                view_id = request.POST.get("view_id")
                view = View.objects.filter(id=view_id).first()
                if not view:
                    view = View.objects.filter(
                        title=None, target=TYPE_OBJECT_ORDER, workspace=workspace
                    ).first()
                view_filter = view.viewfilter_set.first()
                filter_conditions = Q(workspace=workspace)
                filter_conditions = build_view_filter(
                    filter_conditions,
                    view_filter,
                    TYPE_OBJECT_ORDER,
                    force_filter_list=additional_filter_fields,
                )
                objects = base_model.objects.filter(filter_conditions)
            else:
                list_objects = request.POST.getlist("checkbox")
                objects = base_model.objects.filter(id__in=list_objects)

            for obj in objects:
                customFields = custom_value_model.objects.filter(
                    **{custom_value_relation: obj}
                )

                old_obj_id = obj.id
                obj.id = None
                setattr(obj, id_field, None)
                obj.created_at = timezone.now()
                obj.save()

                # keep customer
                old_objects = base_model.objects.filter(id=old_obj_id).first()
                for company in old_objects.company.all():
                    obj.company.add(company)
                for contact in old_objects.contact.all():
                    obj.contact.add(contact)
                obj.save()

                for field in customFields:
                    old_field_id = field.id
                    field.id = None
                    setattr(field, custom_value_relation, obj)
                    field.save()

                    # duplicate file or image
                    customFieldsValueFile = custom_value_file_model.objects.filter(
                        **{f"{custom_value_file_relation}__id": old_field_id}
                    )
                    if customFieldsValueFile:
                        for value_file in customFieldsValueFile:
                            value_file.id = None
                            value_file.created_at = timezone.now()
                            setattr(value_file, custom_value_file_relation, field)
                            value_file.save()

        elif "import_deals" in request.POST:
            # Save Mapping
            file_columns = request.POST.getlist("order-file-column", [])
            file_columns_name = request.POST.getlist("order-file-column-name", [])
            sanka_properties = request.POST.getlist("order-sanka-properties", [])
            ignores = request.POST.getlist("order-ignore", [])

            contact_file_columns = request.POST.getlist("file-column", [])
            contact_sanka_properties = request.POST.getlist("sanka-properties", [])
            contact_ignores = request.POST.getlist("ignore", [])

            item_file_columns = request.POST.getlist("item-file-column", [])
            item_sanka_properties = request.POST.getlist("sanka-item-properties", [])
            item_ignores = request.POST.getlist("item-ignore", [])

            mapping_custom_fields = {}
            mapping_contact_custom_fields = {}
            mapping_item_custom_fields = {}

            order_mapping = {}
            contact_mapping = {}
            item_mapping = {}

            for idx, file_column in enumerate(file_columns):
                # check ignore
                order_mapping[file_column] = sanka_properties[idx]
                if ignores[idx] == "True":
                    continue
                mapping_custom_fields[file_column] = sanka_properties[idx]
            for idx, file_column in enumerate(contact_file_columns):
                # check ignore
                contact_mapping[file_column] = contact_sanka_properties[idx]
                if contact_ignores[idx] == "True":
                    continue
                mapping_contact_custom_fields[file_column] = contact_sanka_properties[
                    idx
                ]
            for idx, file_column in enumerate(item_file_columns):
                # check ignore
                item_mapping[file_column] = item_sanka_properties[idx]
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            platform = request.POST.getlist("platform")
            diff_mapping_order = ["shopify"]
            if platform:
                if len(platform) > 1:
                    if platform[1] in diff_mapping_order:
                        platform = platform[1]
                    else:
                        platform = platform[0]
                else:
                    platform = platform[0]
            print(platform)

            mapping, _ = DealsMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )
            
            try:
                contact_workspace_mapping, _ = ContactsMappingFields.objects.get_or_create(
                    workspace=workspace, platform=platform
                )
            except ContactsMappingFields.DoesNotExist:
                contact_workspace_mapping = ContactsMappingFields.objects.create(
                    workspace=workspace, platform=platform
                )
            except ContactsMappingFields.MultipleObjectsReturned:
                contact_workspace_mapping = ContactsMappingFields.objects.filter(
                    workspace=workspace, platform=platform
                ).first()

            data = {}
            sanka_properties = request.POST.getlist("order-sanka-properties", [])
            contact_sanka_properties = request.POST.getlist("sanka-properties", [])
            for i, field in enumerate(order_mapping):
                if order_mapping[field] == "create_new" and ignores[i] != "True":
                    new_field, _ = DealsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": file_columns_name[i], "skip": ignores[i]}
                    sanka_properties[i] = file_columns_name[i]
                elif (
                    order_mapping[field] == "create_new|contact"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|contact"
                    new_field, _ = ContactsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                    sanka_properties[i] = new_column_value
                elif (
                    order_mapping[field] == "create_new|company"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|company"
                    new_field, _ = CompanyNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                    sanka_properties[i] = new_column_value
                else:
                    field_data = {"value": order_mapping[field], "skip": ignores[i]}
                data[field] = field_data
            mapping.input_data = data
            mapping.save()

            select_integration_ids = request.POST.getlist("select_integration_ids", [])

            file_columns = request.POST.getlist("order-file-column", [])
            ignores = request.POST.getlist("order-ignore", [])

            contact_file_columns = request.POST.getlist("file-column", [])
            contact_ignores = request.POST.getlist("ignore", [])

            mapping_custom_fields = {}
            mapping_contact_custom_fields = {}

            for idx, file_column in enumerate(file_columns):
                # check ignore
                if ignores[idx] == "True":
                    continue
                mapping_custom_fields[file_column] = sanka_properties[idx]

            for idx, file_column in enumerate(contact_file_columns):
                # check ignore
                contact_mapping[file_column] = contact_sanka_properties[idx]
                if contact_ignores[idx] == "True":
                    continue
                mapping_contact_custom_fields[file_column] = contact_sanka_properties[
                    idx
                ]

            channels = Channel.objects.filter(
                workspace=workspace, id__in=select_integration_ids
            )
            how_to_import = request.POST.get("how_to_import", None)
            key_item_field = request.POST.get("key_item_field", None)
            import_order_date = request.POST.get("import_order_date", None)
            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                type="import_case",
                name="Import Deals",
                status="running",
                progress=0,
            )

            for channel in channels:
                if channel.integration.slug == "hubspot":
                    # NOTE: For HubSpot, we need to import orders as deals, we cannot import cases directly
                    # Build payload using the model (keep using the model for background job)
                    payload = ImportOrdersPayload(
                        user=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        platform=channel.integration.slug,
                        mapping_custom_fields=json.dumps(mapping_custom_fields),
                        mapping_contact_custom_fields=json.dumps(mapping_contact_custom_fields),
                        history_id=str(history.id),
                        how_to_import=how_to_import,
                        key_item_field=key_item_field,
                        import_order_date=import_order_date,
                        as_deal=True,
                        lang=lang,
                    )
                    
                    job_id = create_bg_job(
                        workspace,
                        request.user,
                        "import_orders",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id
                    history.channel = channel
                    history.save()
                    
                    # Log export job parameters for debugging
                    logger.info(f"EXPORT_JOB: Starting orders import for user {request.user.email} in workspace {workspace.id}")
                    logger.info(f"EXPORT_JOB: Channel ID: {channel.id}, Platform: {channel.integration.slug}")
                    
                    ref = None
                    try:
                        ref = hatchet_import_orders.run_no_wait(input=payload)
                    except Exception as e:
                        logger.error(f"EXPORT_JOB: Exception occurred during import orders: {str(e)}", exc_info=True)
                        ref = None
                    
                    is_running = None  
                    if ref:
                        logger.info(f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}")
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        logger.error(f"EXPORT_JOB: Failed to submit background job for user {request.user.email}")
                        is_running = False
                    
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="受注レコードをインポートしております。少々お待ちください...",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Orders being imported. Please give it a few moment...",
                                type="success",
                            )
                    else:
                        if channel.integration.slug == "hubspot":
                            import_hubspot_orders_as_deals(
                                channel.id,
                                mapping_custom_fields=mapping_custom_fields,
                                lang=lang,
                            )
                elif channel.integration.slug == "salesforce":
                    logger.info(f"Case Mapping: {mapping_custom_fields}")
                    
                    # Extract Salesforce filtering parameters
                    enable_salesforce_filter = request.POST.get("enable-salesforce-filter", None)
                    if enable_salesforce_filter == "1":
                        enable_salesforce_filter = True
                    else:
                        enable_salesforce_filter = False
                    
                    salesforce_field = request.POST.get("salesforce-field", None)
                    if salesforce_field == "None":
                        salesforce_field = None
                    salesforce_filter = request.POST.get("salesforce-filter", None)
                    
                    # Convert case_mapping to JSON string
                    case_mapping_str = json.dumps(mapping_custom_fields) if mapping_custom_fields else None
                    
                    # Create transfer history record
                    history = TransferHistory.objects.create(
                        workspace=workspace,
                        user=request.user,
                        status="running",
                        type="import_case",
                        name="Import Salesforce Cases",
                        channel=channel,
                    )
                    
                    # Create payload for background job
                    payload = ImportSalesforceCasesPayload(
                        user_id=str(request.user.id),
                        workspace_id=str(workspace.id),
                        channel_id=str(channel.id),
                        history_id=str(history.id),
                        case_mapping=case_mapping_str,
                        language=lang,
                        enable_salesforce_filter=enable_salesforce_filter,
                        salesforce_field=salesforce_field,
                        salesforce_filter=salesforce_filter,
                        salesforce_checkpoint=0,
                    )
                    
                    # Create background job
                    job_id = create_bg_job(
                        workspace=workspace,
                        user=request.user,
                        function_name="import_salesforce_cases",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id
                    
                    # Log import job parameters for debugging
                    logger.info(f"IMPORT_SALESFORCE_CASES: Starting cases import for user {request.user.email} in workspace {workspace.id}")
                    logger.info(f"IMPORT_SALESFORCE_CASES: Import parameters - function: import_salesforce_cases, job_id: {job_id}")
                    logger.info(f"IMPORT_SALESFORCE_CASES: Channel ID: {channel.id}, History ID: {history.id}, Language: {lang}")
                    logger.info(f"IMPORT_SALESFORCE_CASES: Mapping: {case_mapping_str}")
                    logger.info(f"IMPORT_SALESFORCE_CASES: Filter - enabled: {enable_salesforce_filter}, field: {salesforce_field}, value: {salesforce_filter}")
                    
                    try:
                        ref = import_salesforce_cases_task.run_no_wait(input=payload)
                    except Exception as e:
                        logger.error(f"IMPORT_SALESFORCE_CASES: Exception occurred during import_salesforce_cases_task: {str(e)}", exc_info=True)
                        ref = None
                    
                    is_running = None
                    if ref:
                        logger.info(f"IMPORT_SALESFORCE_CASES: Background job submitted successfully for user {request.user.email}")
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        logger.error(f"IMPORT_SALESFORCE_CASES: Failed to submit background job for user {request.user.email}")
                        is_running = False
                    
                    if is_running:
                        logger.info(f"IMPORT_SALESFORCE_CASES: Successfully submitted import job for user {request.user.email}")
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Salesforceケースのインポートジョブが送信されました。完了時に通知されます。",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Salesforce cases import job submitted successfully. You will be notified when complete.",
                                type="success",
                            )
                    else:
                        logger.error(f"IMPORT_SALESFORCE_CASES: Failed to submit import job for user {request.user.email} in workspace {workspace.id}")
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Salesforceケースのインポートジョブの送信に失敗しました",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Failed to submit Salesforce cases import job",
                                type="error",
                            )

        elif "export_deals" in request.POST:
            # Save Mapping
            file_columns = request.POST.getlist("order-file-column", [])
            file_columns_name = request.POST.getlist("order-file-column-name", [])
            sanka_properties = request.POST.getlist("order-sanka-properties", [])
            ignores = request.POST.getlist("order-ignore", [])

            contact_file_columns = request.POST.getlist("file-column", [])
            contact_sanka_properties = request.POST.getlist("sanka-properties", [])
            contact_ignores = request.POST.getlist("ignore", [])

            item_file_columns = request.POST.getlist("item-file-column", [])
            item_sanka_properties = request.POST.getlist("sanka-item-properties", [])
            item_ignores = request.POST.getlist("item-ignore", [])

            mapping_custom_fields = {}
            mapping_contact_custom_fields = {}
            mapping_item_custom_fields = {}

            order_mapping = {}
            contact_mapping = {}
            item_mapping = {}

            for idx, file_column in enumerate(file_columns):
                # check ignore
                order_mapping[file_column] = sanka_properties[idx]
                if ignores[idx] == "True":
                    continue
                mapping_custom_fields[file_column] = sanka_properties[idx]
            for idx, file_column in enumerate(contact_file_columns):
                # check ignore
                contact_mapping[file_column] = contact_sanka_properties[idx]
                if contact_ignores[idx] == "True":
                    continue
                mapping_contact_custom_fields[file_column] = contact_sanka_properties[
                    idx
                ]
            for idx, file_column in enumerate(item_file_columns):
                # check ignore
                item_mapping[file_column] = item_sanka_properties[idx]
                if item_ignores[idx] == "True":
                    continue
                mapping_item_custom_fields[file_column] = item_sanka_properties[idx]

            platform = request.POST.getlist("platform")
            diff_mapping_order = ["shopify"]
            if platform:
                if len(platform) > 1:
                    if platform[1] in diff_mapping_order:
                        platform = platform[1]
                    else:
                        platform = platform[0]
                else:
                    platform = platform[0]
            print(platform)

            mapping, _ = DealsMappingFields.objects.get_or_create(
                workspace=workspace, platform=platform
            )

            data = {}
            sanka_properties = request.POST.getlist("order-sanka-properties", [])
            contact_sanka_properties = request.POST.getlist("sanka-properties", [])
            for i, field in enumerate(order_mapping):
                if order_mapping[field] == "create_new" and ignores[i] != "True":
                    new_field, _ = DealsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": file_columns_name[i], "skip": ignores[i]}
                    sanka_properties[i] = file_columns_name[i]
                elif (
                    order_mapping[field] == "create_new|contact"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|contact"
                    new_field, _ = ContactsNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                    sanka_properties[i] = new_column_value
                elif (
                    order_mapping[field] == "create_new|company"
                    and ignores[i] != "True"
                ):
                    new_column_value = f"{file_columns_name[i]}|company"
                    new_field, _ = CompanyNameCustomField.objects.get_or_create(
                        workspace=workspace, name=file_columns_name[i], type="text"
                    )
                    field_data = {"value": new_column_value, "skip": ignores[i]}
                    sanka_properties[i] = new_column_value
                else:
                    field_data = {"value": order_mapping[field], "skip": ignores[i]}
                data[field] = field_data
            mapping.input_data = data
            mapping.save()

            select_integration_ids = request.POST.getlist("select_integration_ids", [])

            file_columns = request.POST.getlist("order-file-column", [])
            ignores = request.POST.getlist("order-ignore", [])

            contact_file_columns = request.POST.getlist("file-column", [])
            contact_ignores = request.POST.getlist("ignore", [])

            mapping_custom_fields = {}
            mapping_contact_custom_fields = {}

            for idx, file_column in enumerate(file_columns):
                # check ignore
                if ignores[idx] == "True":
                    continue
                mapping_custom_fields[file_column] = sanka_properties[idx]

            for idx, file_column in enumerate(contact_file_columns):
                # check ignore
                contact_mapping[file_column] = contact_sanka_properties[idx]
                if contact_ignores[idx] == "True":
                    continue
                mapping_contact_custom_fields[file_column] = contact_sanka_properties[
                    idx
                ]

            channels = Channel.objects.filter(
                workspace=workspace, id__in=select_integration_ids
            )
            how_to_import = request.POST.get("how_to_import", None)
            key_item_field = request.POST.get("key_item_field", None)
            import_order_date = request.POST.get("import_order_date", None)
            history = TransferHistory.objects.create(
                workspace=workspace,
                user=request.user,
                type="export_case",
                name="Export Deals",
                status="running",
                progress=0,
            )

            for channel in channels:
                if channel.integration.slug == "hubspot":
                    case_ids = request.POST.getlist("case_ids", [])

                    payload = ExportCasesPayload(
                        user=str(request.user.id),
                        workspace_id=str(workspace.id),
                        platform=channel.integration.slug,
                        channel_id=str(channel.id),
                        case_ids=case_ids,
                        mapping_custom_fields=json.dumps(mapping_custom_fields),
                        history_id=str(history.id),
                        lang=lang,
                    )
                    
                    job_id = create_bg_job(
                        workspace,
                        request.user,
                        "export_cases",
                        transfer_history=history,
                        payload=payload.model_dump(mode="json"),
                    )
                    payload.background_job_id = job_id
                    history.channel = channel
                    history.save()
                    
                    # Log export job parameters for debugging
                    logger.info(f"EXPORT_JOB: Starting cases export user {request.user.email} in workspace {workspace.id}")
                    logger.info(f"EXPORT_JOB: Channel ID: {channel.id}, Platform: {channel.integration.slug}")
                    logger.info(f"EXPORT_JOB: Payload: {payload.model_dump(mode='json')}")
                    ref = None
                    try:
                        ref = hatchet_export_cases.run_no_wait(input=payload)
                    except Exception as e:
                        logger.error(f"EXPORT_JOB: Exception occurred during export cases: {str(e)}", exc_info=True)
                        ref = None
                    
                    is_running = None  
                    if ref:
                        logger.info(f"EXPORT_JOB: Background job submitted successfully for user {request.user.email}")
                        add_hatchet_run_id(job_id, ref)
                        is_running = True
                    else:
                        logger.error(f"EXPORT_JOB: Failed to submit background job for user {request.user.email}")
                        is_running = False
                    
                    if is_running:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="受注レコードをエクスポートしております。少々お待ちください...",
                                type="success",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="Cases being exported. Please give it a few moment...",
                                type="success",
                            )
                    else:
                        if lang == "ja":
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="エクスポートジョブの送信中にエラーが発生しました。サポートに連絡してください。",
                                type="error",
                            )
                        else:
                            Notification.objects.create(
                                workspace=workspace,
                                user=request.user,
                                message="There is an error while submitting the export job. Please contact support.",
                                type="error",
                            )
        elif "task_id" in request.POST:
            task_id = request.POST["task_id"]
            task_status = request.POST["task_status"]
            try:
                task = TransferHistory.objects.get(id=task_id)
                task.status = task_status
                task.save()
            except:
                print("TransferHistory does not exist")
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            module_slug = module.slug
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))


def apply_services_deals_view_filter(filter_conditions: Q, view_filter: ViewFilter):
    if view_filter:
        default_columns_dict = {
            field.name: field.get_internal_type() for field in Deals._meta.fields
        }
        filter_dictionary = view_filter.filter_value

        if filter_dictionary:
            for filter in filter_dictionary.keys():
                shopturbo_deals_columns = [str(v.name) for v in Deals._meta.fields]
                shopturbo_deals_columns.remove("id")
                shopturbo_deals_columns.remove("workspace")
                shopturbo_deals_columns.insert(2, "customer")
                if filter in shopturbo_deals_columns:
                    if check_columns_type(filter, default_columns_dict) == "string":
                        filter_key = filter_dictionary[filter]["key"]
                        filter_value = filter_dictionary[filter]["value"]

                        if filter == "customer":
                            filter = "contact__name"

                        if filter_key == "is":
                            filter_conditions &= Q(**{filter: filter_value})
                        elif filter_key == "is_not":
                            filter_conditions &= ~Q(**{filter: filter_value})
                        elif filter_key == "contains":
                            filter_conditions &= Q(
                                **{filter + "__icontains": filter_value}
                            )
                        elif filter_key == "does_not_contain":
                            filter_conditions &= ~Q(
                                **{filter + "__icontains": filter_value}
                            )
                        elif filter_key == "starts_with":
                            filter_conditions &= Q(
                                **{filter + "__startswith": filter_value}
                            )
                        elif filter_key == "ends_with":
                            filter_conditions &= Q(
                                **{filter + "__endswith": filter_value}
                            )

                    elif check_columns_type(filter, default_columns_dict) == "number":
                        if filter_dictionary[filter]["key"] == "less_than":
                            filter_conditions &= Q(
                                **{filter + "__lt": filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "less_than_equal":
                            filter_conditions &= Q(
                                **{filter + "__lte": filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "equal":
                            filter_conditions &= Q(
                                **{filter: filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "greater_than":
                            filter_conditions &= Q(
                                **{filter + "__gt": filter_dictionary[filter]["value"]}
                            )
                        elif filter_dictionary[filter]["key"] == "greater_than_equal":
                            filter_conditions &= Q(
                                **{filter + "__gte": filter_dictionary[filter]["value"]}
                            )

                elif filter == "created_at":
                    if filter_dictionary["created_at"]["value"]:
                        if filter_dictionary["created_at"]["key"] == "start_date":
                            filter_conditions &= Q(
                                created_at__gte=filter_dictionary["created_at"]["value"]
                            )
                        elif filter_dictionary["created_at"]["key"] == "end_date":
                            filter_conditions &= Q(
                                created_at__lte=filter_dictionary["created_at"]["value"]
                            )

                else:
                    filter_name = filter
                    contactsCustomFieldName = DealsNameCustomField.objects.filter(
                        name__iexact=filter, workspace=view_filter.view.workspace
                    ).first()
                    if not contactsCustomFieldName and is_valid_uuid(filter):
                        contactsCustomFieldName = DealsNameCustomField.objects.filter(
                            id=filter
                        ).first()
                        filter_name = contactsCustomFieldName.name

                    if contactsCustomFieldName:
                        if contactsCustomFieldName.type == "text":
                            filter_conditions &= Q(
                                deals_custom_field_relations__field_name__name=filter_name,
                                deals_custom_field_relations__field_name__type="text",
                                deals_custom_field_relations__value=filter_dictionary[
                                    filter
                                ]["value"],
                            )

                        elif contactsCustomFieldName.type == "choice":
                            filter_conditions &= Q(
                                deals_custom_field_relations__field_name__name=filter_name,
                                deals_custom_field_relations__field_name__type="choice",
                                deals_custom_field_relations__value=filter_dictionary[
                                    filter
                                ]["value"],
                            )

                        elif contactsCustomFieldName.type == "number":
                            if filter_dictionary[filter]["key"] == "less_than":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="number",
                                    deals_custom_field_relations__value_number__lt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "less_than_equal":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="number",
                                    deals_custom_field_relations__value_number__lte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "equal":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="number",
                                    deals_custom_field_relations__value_number=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="number",
                                    deals_custom_field_relations__value_number__gt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than_equal":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="number",
                                    deals_custom_field_relations__value_number__gte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )

                        elif contactsCustomFieldName.type == "formula":
                            if filter_dictionary[filter]["key"] == "less_than":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="formula",
                                    deals_custom_field_relations__value_number__lt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "less_than_equal":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="formula",
                                    deals_custom_field_relations__value_number__lte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "equal":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="formula",
                                    deals_custom_field_relations__value_number=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="formula",
                                    deals_custom_field_relations__value_number__gt=filter_dictionary[
                                        filter
                                    ]["value"],
                                )
                            if filter_dictionary[filter]["key"] == "greater_than_equal":
                                filter_conditions &= Q(
                                    deals_custom_field_relations__field_name__name=filter_name,
                                    deals_custom_field_relations__field_name__type="formula",
                                    deals_custom_field_relations__value_number__gte=filter_dictionary[
                                        filter
                                    ]["value"],
                                )

    return filter_conditions


@login_or_hubspot_required
def create_deals(request):
    target = "customer_case"
    lang = request.LANGUAGE_CODE
    source_url = request.POST.get("source_url")
    workspace = get_workspace(request.user)

    # DEBUG: Log all POST parameters
    print(f"[DEBUG] create_deals - All POST parameters:")
    for key, value in request.POST.items():
        print(f"[DEBUG]   {key}: {value}")
    print(f"[DEBUG] create_deals - source_url: {source_url}")
    print(f"[DEBUG] create_deals - initial target: {target}")

    if request.method == "POST":
        section = request.POST.get("section")
        view_id = request.POST.get("view_id")

        if section == "single-entry":
            # Association Related
            associate_id = request.POST.get("associate_id", None)
            object_id = request.POST.get("object_id", None)
            page = request.POST.get("page", 1)
            source = request.POST.get("source", None)
            associate_view_id = request.POST.get("associate_view_id", None)
            associate_type = None
            custom_model = None
            object_type = TYPE_OBJECT_CASE

            module_slug = request.POST.get("module", None)
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
            module = Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
            ).order_by("order", "created_at")
            if module_slug:
                module = module.filter(slug=module_slug).first()
            else:
                module = module.first()

            # Check if module exists before accessing its attributes
            if module:
                module_slug = module.slug
            else:
                # Handle the case where no module is found
                module_slug = None

            if associate_id and object_id and source:
                module_object_slug = OBJECT_TYPE_TO_SLUG[source]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=source
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug
                else:
                    # Handle the case where no module is found
                    module_slug = None

                try:
                    associate = Association.objects.get(id=associate_id)
                    associate_type = associate.associate_type
                    if associate_type != source:
                        attr_name = SOURCE_ASSOCIATE_MAP.get(source, None)
                        if attr_name:
                            associate_attribute = getattr(associate, attr_name, None)
                            if associate_attribute:
                                custom_model = associate_attribute
                    else:
                        for attr_name, _ in ASSOCIATE_MAP.items():
                            attr = getattr(associate, attr_name, None)
                            if attr:
                                custom_model = attr
                                break

                except Exception as e:
                    print(f"Error: {e}")

                if not custom_model:
                    if lang == "ja":
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="アソシエーションの取得に失敗しました。",
                            type="error",
                        )
                    else:
                        Notification.objects.create(
                            workspace=workspace,
                            user=request.user,
                            message="Failed to retrieve the association.",
                            type="error",
                        )

                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )

            name = request.POST.get("deals_name")
            contact_and_company = request.POST.getlist("contact_and_company", None)
            case_status = request.POST.get("case_status", None)
            owner = request.POST.get("owner", None)

            # First try to find an existing deal
            existing_deals = Deals.objects.filter(
                workspace=workspace, name=name, status="active"
            )

            if existing_deals.exists():
                # If multiple deals exist, use the first one
                deal = existing_deals.first()
            else:
                # If no deal exists, create a new one
                deal = Deals.objects.create(
                    workspace=workspace, name=name, status="active"
                )

            if case_status:
                deal.case_status = case_status

            invoices = request.POST.getlist("invoices", [])
            if invoices and invoices != [""]:
                deal.invoices.clear()
                invoice_objs = Invoice.objects.filter(id__in=invoices)
                deal.invoices.add(*invoice_objs)
                deal.save()
            else:
                deal.invoices.clear()

            estimates = request.POST.getlist("estimates", [])
            if estimates and estimates != [""]:
                deal.estimates.clear()
                estimate_objs = Estimate.objects.filter(id__in=estimates)
                deal.estimates.add(*estimate_objs)
                deal.save()
            else:
                deal.estimates.clear()

            association_label = AssociationLabel.objects.filter(workspace=workspace,object_source=TYPE_OBJECT_CASE, label__iexact='customer').first()
            if association_label:
                AssociationLabelObject.reset_associations_for_object(
                    deal, 
                    workspace, 
                    association_label
                )
            if contact_and_company and contact_and_company[0] != "":
                for contact_and_company_id in contact_and_company:
                    if Contact.objects.filter(id=contact_and_company_id):
                        contact = Contact.objects.get(id=contact_and_company_id)
                        deal.contact.add(contact)
                        if association_label:
                            AssociationLabelObject.create_association(deal, contact, workspace, association_label)
                        
                    elif Company.objects.filter(id=contact_and_company_id):
                        company = Company.objects.get(id=contact_and_company_id)
                        deal.company.add(company)
                        if association_label:
                            AssociationLabelObject.create_association(deal, company, workspace, association_label)

            tasks = request.POST.getlist("tasks", [])
            if tasks:
                for task_id in tasks:
                    task = Task.objects.get(id=task_id)
                    deal.tasks.add(task)

            # Deal Items
            items = request.POST.getlist("item_case", [])
            item_prices = request.POST.getlist("item_price_case", [])
            number_of_items = request.POST.getlist("number_of_items_case", [])

            line_item_properties = {
                key: request.POST.getlist(key)
                for key in request.POST
                if key.startswith("line_item_property")
            }

            if len(items) > 0:
                DealsItems.objects.filter(deal=deal).delete()
            order_view = 0
            for idx, item in enumerate(items):
                if is_valid_uuid(item):
                    item = ShopTurboItems.objects.get(id=item)
                    try:
                        price = item_prices[idx]
                        if is_valid_uuid(price):
                            price = ShopTurboItemsPrice.objects.get(id=price)
                    except:
                        price = None

                    try:
                        number_of_item = number_of_items[idx]
                    except:
                        number_of_item = 0

                    if price:
                        deal_item = DealsItems.objects.create(
                            deal=deal,
                            item=item,
                            item_price=price,
                            number_item=number_of_item,
                        )
                    else:
                        deal_item = DealsItems.objects.create(
                            deal=deal, item=item, number_item=number_of_item
                        )
                else:
                    try:
                        if item_prices[idx] == "":
                            price = 0
                        else:
                            price = item_prices[idx]
                    except:
                        price = 0

                    try:
                        if number_of_items[idx] == "":
                            number_of_item = 0
                        else:
                            number_of_item = number_of_items[idx]
                    except:
                        number_of_item = 0

                    deal_item = DealsItems.objects.create(
                        deal=deal,
                        custom_item_name=item,
                        item_price_deal=price,
                        number_item=number_of_item,
                    )

                deal_item.order_view = order_view
                order_view += 1
                deal_item.save()

                for key, value in line_item_properties.items():
                    custom_field_value, _ = (
                        DealsItemsValueCustomField.objects.get_or_create(
                            field_name_id=key.split("|")[-1], item_deal=deal_item
                        )
                    )
                    custom_field_value.value = value[idx]
                    custom_field_value.save()

            assign_object_owner(deal, owner, request, TYPE_OBJECT_CASE)
            deal.save(
                log_data={
                    "user": request.user,
                    "status": "create",
                    "workspace": workspace,
                }
            )

            save_association_label(request, deal, TYPE_OBJECT_CASE)

            save_custom_property(request, deal)

            print(
                f"[DEBUG] associate_id: {associate_id}, object_id: {object_id}, source: {source}, custom_model: {custom_model}"
            )
            print(f"[DEBUG] associate_type: {associate_type}")
            target_id = None

            if associate_type and object_id and source and custom_model:
                print(f"[DEBUG] Taking associate_type path")
                redirect_url = update_associate_object(
                    request,
                    associate_type,
                    source,
                    lang,
                    object_id,
                    custom_model,
                    deal,
                    object_type,
                    page,
                    module_slug,
                    module_object_slug,
                    associate_view_id,
                )
                print(f"[DEBUG] associate_type redirect_url: {redirect_url}")
                return redirect(redirect_url)
            elif source and object_id:
                print(
                    f"[DEBUG] Taking source and object_id path - source: {source}, object_id: {object_id}"
                )
                obj = None
                if source == TYPE_OBJECT_TASK:
                    try:
                        task = Task.objects.get(id=object_id)
                    except Task.DoesNotExist:
                        print(f"[DEBUG] Failed to get task with id: {object_id}")
                        pass

                    if task:
                        deal.tasks.add(task)
                        deal.save()

                    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_TASK]
                    module = Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_TASK
                    ).order_by("order", "created_at")
                    if module_slug != "None" and module_slug:
                        module = module.filter(slug=module_slug).first()
                    else:
                        module = module.first()

                    # Check if module exists before accessing its attributes
                    if module:
                        module_slug = module.slug
                    else:
                        # Handle the case where no module is found
                        module_slug = None

                    url = reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    if task:
                        url = update_query_params_url(
                            url,
                            {
                                "view_id": str(view_id),
                                "target": TYPE_OBJECT_TASK,
                                "id": [str(task.id)],
                                "p_id": str(task.project_target.id),
                            },
                        )

                elif source == TYPE_OBJECT_INVOICE:
                    try:
                        invoice = Invoice.objects.get(id=object_id)
                    except Invoice.DoesNotExist:
                        print(f"[DEBUG] Failed to get invoice with id: {object_id}")
                        pass

                    if invoice:
                        deal.invoices.add(invoice)
                        deal.save()

                    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_INVOICE]
                    module = Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_INVOICE
                    ).order_by("order", "created_at")
                    if module_slug != "None" and module_slug:
                        module = module.filter(slug=module_slug).first()
                    else:
                        module = module.first()

                    # Check if module exists before accessing its attributes
                    if module:
                        module_slug = module.slug
                    else:
                        # Handle the case where no module is found
                        module_slug = None

                    url = reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    if invoice:
                        url = update_query_params_url(
                            url,
                            {
                                "view_id": str(view_id),
                                "target": TYPE_OBJECT_INVOICE,
                                "id": [str(invoice.id)],
                            },
                        )

                        target_id = str(invoice.id)

            # association related
            if request.POST.get("type_association", "") == "create-association":
                print(f"[DEBUG] Taking type_association == 'create-association' path")
                if "object_type" in request.POST:
                    object_type = request.POST.get("object_type")
                source = request.POST.get("source")
                print(
                    f"[DEBUG] type_association - source: {source}, object_type: {object_type}"
                )
                module_object_slug = OBJECT_TYPE_TO_SLUG[source]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=source
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug
                else:
                    # Handle the case where no module is found
                    module_slug = None
                if source == TYPE_OBJECT_COMPANY:
                    object_id = request.POST.get("object_id")
                    company_obj = Company.objects.filter(id=object_id).first()

                    if object_type == TYPE_OBJECT_CASE:
                        deal.company.add(company_obj)
                    deal.save()

                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?target={source}&id={company_obj.id}"
                    )
                elif source == TYPE_OBJECT_ESTIMATE:
                    object_id = request.POST.get("object_id")
                    estimate = Estimate.objects.filter(id=object_id).first()
                    estimate.deals.add(deal)

                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?target={source}&id={estimate.id}"
                    )

                elif source == TYPE_OBJECT_CONTACT:
                    object_id = request.POST.get("object_id")
                    contact = Contact.objects.filter(id=object_id).first()
                    deal.contact.add(contact)

                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?target={source}&id={contact.id}"
                    )

                elif source == TYPE_OBJECT_ORDER:
                    print(f"[DEBUG] type_association - TYPE_OBJECT_ORDER path")
                    object_id = request.POST.get("object_id")
                    print(f"[DEBUG] type_association - order object_id: {object_id}")
                    from data.models.order import ShopTurboOrders

                    order = ShopTurboOrders.objects.filter(id=object_id).first()
                    if order:
                        order.cases.add(deal)
                        order.save()

                        association_label = AssociationLabel.objects.filter(workspace=workspace,object_source=TYPE_OBJECT_ORDER, label__iexact=TYPE_OBJECT_CASE).first()
                        if association_label:
                            AssociationLabelObject.reset_associations_for_object(
                                order, 
                                workspace, 
                                association_label
                            )
                            AssociationLabelObject.create_association(order, deal, workspace, association_label)

                        print(f"[DEBUG] type_association - order found and case added")
                    else:
                        print(f"[DEBUG] type_association - order NOT found")

                    redirect_url = (
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                        + f"?target={source}&id={object_id}"
                    )
                    print(
                        f"[DEBUG] type_association - ORDER redirect_url: {redirect_url}"
                    )
                    return redirect(redirect_url)

            if target_id:
                print(f"[DEBUG] Taking target_id path - target_id: {target_id}")
                source_url = update_query_params_url(
                    source_url, {"id": target_id}, overwrite=True
                )
                print(f"[DEBUG] target_id redirect_url: {source_url}")
                return redirect(source_url)

            if source_url:
                print(f"[DEBUG] Taking source_url fallback path")
                print(f"[DEBUG] source_url: {source_url}")
                print(f"[DEBUG] target: {target}")
                print(f"[DEBUG] deal.id: {deal.id}")
                source_url = update_query_params_url(
                    source_url, {"target": target, "id": [str(deal.id)]}, overwrite=True
                )
                print(f"[DEBUG] source_url fallback redirect_url: {source_url}")
                return redirect(source_url)

            if module:
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                    + f"?id={deal.id}"
                )

        elif section == "bulk-entry":
            csv = request.FILES.get("csv_upload", False)
            if not csv:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Failed to retreive the CSV file.",
                    type="error",
                )
                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug

                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))
            file_columns = request.POST.getlist("file-column")
            sanka_properties = request.POST.getlist("sanka-properties-deal")
            ignores = request.POST.getlist("ignore")
            import_method = request.POST.get("import-method-deal")
            key_field = request.POST.getlist("deal-key-field")

            try:
                mapping_storage, _ = ImportMappingFields.objects.get_or_create(
                    workspace=workspace, object_type=TYPE_OBJECT_CASE
                )
                mapping = {
                    sanka_prop: file_col
                    for sanka_prop, file_col in zip(sanka_properties, file_columns)
                }
                mapping_storage.input_pair = mapping
                mapping_storage.save()

                transfer_history = TransferHistory.objects.create(
                    workspace=workspace,
                    user=request.user,
                    status="running",
                    type="import_case",
                )
                transfer_history.name = csv.name
                transfer_history.save()
                df = read_csv(csv)

                for index, row in df.iterrows():
                    base_fields = {}
                    many_to_many_fields = {}
                    custom_fields = {}
                    key_fields = {}
                    for idx, file_column in enumerate(file_columns):
                        # check ignore
                        if ignores[idx] == "True":
                            continue

                        if row[file_column] == "nan" or row[file_column] == None:
                            continue

                        sanka_prop = sanka_properties[idx]
                        if sanka_prop in ["contact", "company"]:
                            many_to_many_fields[sanka_prop] = row[file_column]
                        elif is_valid_uuid(sanka_prop):
                            custom_fields[sanka_prop] = row[file_column]
                        elif sanka_prop != "deal_id":
                            base_fields[sanka_prop] = row[file_column]

                        if sanka_prop in key_field:
                            if sanka_prop == "contact":
                                key_fields["contact__contact_id__in"] = [
                                    v.strip()
                                    for v in row[file_column].split(
                                        CSV_DELIMITER_LIST_FIELD
                                    )
                                ]
                            elif sanka_prop == "company":
                                key_fields["company__company_id__in"] = [
                                    v.strip()
                                    for v in row[file_column].split(
                                        CSV_DELIMITER_LIST_FIELD
                                    )
                                ]
                            elif is_valid_uuid(sanka_prop):
                                key_fields["deals_custom_field_relations__value"] = row[
                                    file_column
                                ]
                            else:
                                key_fields[sanka_prop] = row[file_column]

                    deal = None
                    if import_method == "create_and_update":
                        if key_field:
                            deal = Deals.objects.filter(
                                **key_fields, workspace=workspace
                            ).first()
                            if deal:
                                for key, value in base_fields.items():
                                    setattr(deal, key, value)
                                deal.save()
                            else:
                                deal = Deals.objects.create(
                                    **base_fields, status="active", workspace=workspace
                                )

                    elif import_method == "create":
                        deal = Deals.objects.create(
                            **base_fields, status="active", workspace=workspace
                        )

                    elif import_method == "update":
                        if key_field:
                            deal = Deals.objects.filter(
                                **key_fields, workspace=workspace
                            ).first()
                            if deal:
                                for key, value in base_fields.items():
                                    setattr(deal, key, value)
                                deal.save()

                    if deal:
                        deal.contact.clear()
                        deal.company.clear()
                        for key, values in many_to_many_fields.items():
                            values = str(values)
                            if values == "nan":
                                values = None
                            if values:
                                values = [
                                    v.strip()
                                    for v in values.split(CSV_DELIMITER_LIST_FIELD)
                                ]
                                if key == "contact":
                                    if values[0].isdigit():
                                        contact = Contact.objects.filter(
                                            workspace=workspace, contact_id__in=values
                                        )
                                    else:
                                        contact = Contact.objects.filter(
                                            workspace=workspace, name__in=values
                                        )

                                    deal.contact.set(contact)
                                elif key == "company":
                                    if values[0].isdigit():
                                        company = Company.objects.filter(
                                            workspace=workspace, company_id__in=values
                                        )
                                    else:
                                        company = Company.objects.filter(
                                            workspace=workspace, name__in=values
                                        )
                                    deal.company.set(company)

                    # Write Custom Field
                    for key, value in custom_fields.items():
                        name_custom_field = DealsNameCustomField.objects.filter(
                            id=key
                        ).first()
                        CustomFieldValue, _ = (
                            DealsValueCustomField.objects.get_or_create(
                                field_name=name_custom_field, deals=deal
                            )
                        )

                        if value != "nan":
                            CustomFieldValue.value = value
                            CustomFieldValue.save()

                    progress = 100 * (index + 1) / df.shape[0]
                    transfer_history.progress = progress
                    transfer_history.save()

                    if transfer_history.status == "canceled":
                        break

                if transfer_history.status != "canceled":
                    transfer_history.status = "completed"
                    transfer_history.save()

            except Exception as e:
                import traceback

                traceback.print_exc()
                Notification.objects.create(
                    workspace=workspace,
                    user=request.user,
                    message=f"Wrong format uploaded, Please use this format | {str(e)}",
                    message_ja=f"アップロードされた形式が間違っています。この形式を使用してください | {str(e)}",
                    cta_text="Template",
                    cta_text_ja="テンプレート",
                    cta_target=TEMPLATE_FILE["customer_case"][lang],
                    type="error",
                )

                module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
                module = (
                    Module.objects.filter(
                        workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
                    )
                    .order_by("order", "created_at")
                    .first()
                )
                if module:
                    module_slug = module.slug
                    return redirect(
                        reverse(
                            "load_object_page",
                            host="app",
                            kwargs={
                                "module_slug": module_slug,
                                "object_slug": module_object_slug,
                            },
                        )
                    )
                return redirect(reverse("main", host="app"))
            if lang == "ja":
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="CSVから案件が追加されました。",
                    type="success",
                )
            else:
                Notification.objects.create(
                    workspace=get_workspace(request.user),
                    user=request.user,
                    message="Cases added successfully from CSV.",
                    type="success",
                )
            module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
                )
                .order_by("order", "created_at")
                .first()
            )
            if module:
                module_slug = module.slug
                return redirect(
                    reverse(
                        "load_object_page",
                        host="app",
                        kwargs={
                            "module_slug": module_slug,
                            "object_slug": module_object_slug,
                        },
                    )
                )
            return redirect(reverse("main", host="app"))

    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        module_slug = module.slug
        return redirect(
            reverse(
                "load_object_page",
                host="app",
                kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
            )
        )
    return redirect(reverse("main", host="app"))


@login_or_hubspot_required
def manage_deals(request, id):
    workspace = get_workspace(request.user)
    target = "customer_case"
    source_url = request.POST.get("source_url")

    module_slug = request.POST.get("module", None)
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
    module = Module.objects.filter(
        workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
    ).order_by("order", "created_at")
    if module_slug:
        module = module.filter(slug=module_slug).first()
    else:
        module = module.first()

    # Check if module exists before accessing its attributes
    if module:
        module_slug = module.slug
    else:
        # Handle the case where no module is found
        module_slug = None

    if source_url:
        source_url = update_query_params_url(source_url, {"target": target, "id": [id]})

    view_id = request.POST.get("view_id", None)
    view = None
    if view_id:
        view = View.objects.get(id=view_id)

    deal = Deals.objects.get(id=id)

    if "delete-deals" in request.POST:
        deal.status = "archived"
        deal.save(log_data={"user": request.user, "workspace": workspace})
        if source_url:
            return redirect(source_url)
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))
    elif "restore-deals" in request.POST:
        deal.status = "active"
        deal.save(log_data={"user": request.user, "workspace": workspace})
        if source_url:
            return redirect(source_url)
        module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
        module = (
            Module.objects.filter(
                workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
            )
            .order_by("order", "created_at")
            .first()
        )
        if module:
            return redirect(
                reverse(
                    "load_object_page",
                    host="app",
                    kwargs={
                        "module_slug": module_slug,
                        "object_slug": module_object_slug,
                    },
                )
            )
        return redirect(reverse("main", host="app"))
    elif "update" in request.POST:
        deals_name = request.POST.get("deals_name")
        contact_and_company = request.POST.getlist("contact_and_company", None)
        case_status = request.POST.get("case_status", None)
        invoices = request.POST.getlist("invoices", [])
        tasks = request.POST.getlist("tasks", [])
        estimates = request.POST.getlist("estimates", [])
        currency = request.POST.get("currency", None)
        owner = request.POST.get("owner", None)

        # Deal Items
        items = request.POST.getlist("item_case", [])
        item_prices = request.POST.getlist("item_price_case", [])
        number_of_items = request.POST.getlist("number_of_items_case", [])

        line_item_properties = {
            key: request.POST.getlist(key)
            for key in request.POST
            if key.startswith("line_item_property")
        }

        if len(items) > 0:
            DealsItems.objects.filter(deal=deal).delete()
        order_view = 0
        for idx, item in enumerate(items):
            if is_valid_uuid(item):
                item = ShopTurboItems.objects.get(id=item)
                try:
                    price = item_prices[idx]
                    if is_valid_uuid(price):
                        price = ShopTurboItemsPrice.objects.get(id=price)
                except:
                    price = None

                try:
                    number_of_item = number_of_items[idx]
                except:
                    number_of_item = 0

                if price:
                    deal_item = DealsItems.objects.create(
                        deal=deal,
                        item=item,
                        item_price=price,
                        number_item=number_of_item,
                    )
                else:
                    deal_item = DealsItems.objects.create(
                        deal=deal, item=item, number_item=number_of_item
                    )
            else:
                try:
                    if item_prices[idx] == "":
                        price = 0
                    else:
                        price = item_prices[idx]
                except:
                    price = 0

                try:
                    if number_of_items[idx] == "":
                        number_of_item = 0
                    else:
                        number_of_item = number_of_items[idx]
                except:
                    number_of_item = 0

                deal_item = DealsItems.objects.create(
                    deal=deal,
                    custom_item_name=item,
                    item_price_deal=price,
                    number_item=number_of_item,
                )

            deal_item.order_view = order_view
            order_view += 1
            deal_item.save()

            for key, value in line_item_properties.items():
                custom_field_value, _ = (
                    DealsItemsValueCustomField.objects.get_or_create(
                        field_name_id=key.split("|")[-1], item_deal=deal_item
                    )
                )
                custom_field_value.value = value[idx]
                custom_field_value.save()

        if deals_name:
            deal.name = deals_name

        if case_status:
            deal.case_status = case_status

        if currency:
            deal.currency = currency

        if invoices and invoices != [""]:
            deal.invoices.clear()
            invoice_objs = Invoice.objects.filter(id__in=invoices)
            deal.invoices.add(*invoice_objs)
        else:
            deal.invoices.clear()

        if estimates and estimates != [""]:
            deal.estimates.clear()
            estimate_objs = Estimate.objects.filter(id__in=estimates)
            deal.estimates.add(*estimate_objs)
        else:
            deal.estimates.clear()

        if tasks and tasks != [""]:
            deal.tasks.clear()
            for task_id in tasks:
                task = Task.objects.get(id=task_id)
                deal.tasks.add(task)
        else:
            deal.tasks.clear()

        manual_log_contact = manual_log(deal, "contact", request.user)
        manual_log_company = manual_log(deal, "company", request.user)
        
        association_label = AssociationLabel.objects.filter(workspace=workspace,object_source=TYPE_OBJECT_CASE, label__iexact='customer').first()
        if association_label:
            AssociationLabelObject.reset_associations_for_object(
                deal, 
                workspace, 
                association_label
            )
        deal.contact.clear()
        deal.company.clear()
        if contact_and_company and contact_and_company[0] != "":
            for contact_and_company_id in contact_and_company:
                if Contact.objects.filter(id=contact_and_company_id):
                    contact = Contact.objects.get(id=contact_and_company_id)
                    deal.contact.add(contact)
                    if association_label:
                        AssociationLabelObject.create_association(deal, contact, workspace, association_label)
                elif Company.objects.filter(id=contact_and_company_id):
                    company = Company.objects.get(id=contact_and_company_id)
                    deal.company.add(company)
                    if association_label:
                        AssociationLabelObject.create_association(deal, company, workspace, association_label)


        manual_log(deal, "contact", request.user, manual_log_contact)
        manual_log(deal, "company", request.user, manual_log_company)

        assign_object_owner(deal, owner, request, TYPE_OBJECT_CASE)

        deal.save(log_data={"user": request.user, "workspace": workspace})

        save_custom_property(request, deal)
        save_association_label(request, deal, TYPE_OBJECT_CASE)

    elif "update_tasks" in request.POST:
        tasks = request.POST.getlist("tasks", [])
        if tasks and tasks != [""]:
            deal.tasks.clear()
            for task_id in tasks:
                task = Task.objects.get(id=task_id)
                deal.tasks.add(task)
        else:
            deal.tasks.clear()

        deal.save(log_data={"user": request.user, "workspace": workspace})

    elif "update_invoices" in request.POST:
        invoices = request.POST.getlist("invoices", [])
        if invoices and invoices != [""]:
            deal.invoices.clear()
            invoice_objs = Invoice.objects.filter(id__in=invoices)
            deal.invoices.add(*invoice_objs)
        else:
            deal.invoices.clear()

    elif "update_estimates" in request.POST:
        estimates = request.POST.getlist("estimates", [])
        if estimates and estimates != [""]:
            deal.estimates.clear()
            estimate_objs = Estimate.objects.filter(id__in=estimates)
            deal.estimates.add(*estimate_objs)
        else:
            deal.estimates.clear()

    elif "remove_related_item" in request.POST:
        related_item_id = request.POST.get("related_item_id", "")

        try:
            if "delete_estimate" in request.POST:
                estimate = deal.estimates.remove(related_item_id)
                estimate.save()
            elif "delete_invoice" in request.POST:
                invoice = deal.invoices.remove(related_item_id)
                invoice.save()
        except Exception as e:
            print("[Error deleting relation object to case] ", e)
            pass

    if "remove_related_contact" in request.GET:
        deal.contact.clear()
        deal.save(log_data={"user": request.user, "workspace": workspace})
        return redirect(reverse("main", host="app"))
    elif "remove_related_company" in request.GET:
        deal.company.clear()
        deal.save(log_data={"user": request.user, "workspace": workspace})
        return redirect(reverse("main", host="app"))

    if source_url:
        return redirect(source_url)
    module_object_slug = OBJECT_TYPE_TO_SLUG[TYPE_OBJECT_CASE]
    module = (
        Module.objects.filter(
            workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
        )
        .order_by("order", "created_at")
        .first()
    )
    if module:
        url = reverse(
            "load_object_page",
            host="app",
            kwargs={"module_slug": module_slug, "object_slug": module_object_slug},
        )
        if deal:
            url = update_query_params_url(url, {"target": target, "id": [str(deal.id)]})
        return redirect(url)
    return redirect(reverse("main", host="app"))


def kanban_status_api(request):
    if request.method == "POST":
        data = json.loads(request.body.decode("utf-8"))
        id = data["id"]
        type = data["type"]
        view_id = data["view_id"]
        target_status = data["target_status"]
        newOrder = data["newOrder"]

        # Handle multiple IDs for multi-drag
        if isinstance(id, list):
            item_ids = id
        else:
            item_ids = [id]

        if type == "contacts":
            view = View.objects.get(id=view_id)
            view_filter = ViewFilter.objects.filter(view=view).first()
            if view_filter:
                if view_filter.choice_customfield:
                    nameCustomFieldValue = ContactsNameCustomField.objects.get(
                        id=view_filter.choice_customfield
                    )

                    for contact_id in item_ids:
                        contact = Contact.objects.get(id=contact_id)
                        if nameCustomFieldValue and contact:
                            valueCustomFieldValue, _ = (
                                ContactsValueCustomField.objects.get_or_create(
                                    field_name=nameCustomFieldValue, contact=contact
                                )
                            )
                            if valueCustomFieldValue:
                                if target_status == "unlisted":
                                    target_status = ""

                                valueCustomFieldValue.value = target_status
                                valueCustomFieldValue.save()

        elif type == "company":
            view = View.objects.get(id=view_id)
            view_filter = ViewFilter.objects.filter(view=view).first()
            if view_filter:
                if view_filter.choice_customfield:
                    nameCustomFieldValue = CompanyNameCustomField.objects.get(
                        id=view_filter.choice_customfield
                    )

                    for company_id in item_ids:
                        company = Company.objects.get(id=company_id)
                        if nameCustomFieldValue and company:
                            valueCustomFieldValue, _ = (
                                CompanyValueCustomField.objects.get_or_create(
                                    field_name=nameCustomFieldValue, company=company
                                )
                            )
                            if valueCustomFieldValue:
                                if target_status == "unlisted":
                                    target_status = ""

                                valueCustomFieldValue.value = target_status
                                valueCustomFieldValue.save()

        elif type == "deals":
            view = View.objects.get(id=view_id)
            view_filter = ViewFilter.objects.filter(view=view).first()
            if view_filter:
                if view_filter.choice_customfield:
                    if is_valid_uuid(view_filter.choice_customfield):
                        nameCustomFieldValue = DealsNameCustomField.objects.get(
                            id=view_filter.choice_customfield
                        )

                        for deal_id in item_ids:
                            deal = Deals.objects.get(id=deal_id)
                            if nameCustomFieldValue and deal:
                                valueCustomFieldValue, _ = (
                                    DealsValueCustomField.objects.get_or_create(
                                        field_name=nameCustomFieldValue, deals=deal
                                    )
                                )
                                if valueCustomFieldValue:
                                    if target_status == "unlisted":
                                        target_status = ""

                                    valueCustomFieldValue.value = target_status
                                    valueCustomFieldValue.save()
                    else:
                        # default column
                        for deal_id in item_ids:
                            deal = Deals.objects.get(id=deal_id)
                            if target_status == "unlisted":
                                target_status = None
                            setattr(deal, view_filter.choice_customfield, target_status)
                            deal.save()

                # Vertical Scroll
                if newOrder:
                    for idx, case_id in enumerate(newOrder):
                        case = Deals.objects.get(id=case_id)
                        case.kanban_order = idx
                        case.save()

    return JsonResponse({"status": 200})


def kanban_board_api(request):
    data = json.loads(request.body.decode("utf-8"))

    view_id = data["view_id"]
    target_status = ast.literal_eval(data["target_status"])
    view_filter = ViewFilter.objects.filter(id=view_id).first()
    view_filter.kanban_board = [item["id"] for item in target_status]
    view_filter.save()

    return JsonResponse({"status": 200})


@login_or_hubspot_required
def case_row_detail(request, id):
    workspace = get_workspace(request.user)
    deal = get_object_or_404(Deals, id=id)
    view_id = request.GET.get("view_id", None)
    target = request.GET.get("target", None)
    module_slug = request.GET.get("menu_key", None)
    column_view = []

    module = Module.objects.filter(
        workspace=get_workspace(request.user), object_values__contains=target
    ).order_by("order", "created_at")
    if module_slug:
        module = module.filter(slug=module_slug).first()
    else:
        module = module.first()

    # Check if module exists before accessing its attributes
    if module:
        module_slug = module.slug
    else:
        # Handle the case where no module is found
        module_slug = None

    if "customer_case" in target:
        column_view = DEFAULT_COLUMNS_CASE.copy()
        column_view.append("checkbox")

    view_filter = modular_view_filter(
        workspace, target, view_id=view_id, column_view=column_view
    )

    case_columns = view_filter.column
    property_sets = PropertySet.objects.filter(
        workspace=workspace, target=TYPE_OBJECT_CASE
    ).order_by("-created_at")
    # Use module_slug instead of directly accessing module.slug
    context = {
        "object_type": TYPE_OBJECT_CASE,
        "deal": deal,
        "case_columns": case_columns,
        "view_id": view_id,
        "view_filter": view_filter,
        "menu_key": module_slug,  # Use module_slug which has been safely set
        "selected_case_id": request.GET.get("selected_case_id"),
        "page": request.GET.get("page", 1),
        "property_sets": property_sets,
    }
    return render(request, "data/contacts/case-row-partial-loop.html", context)


def case_update_notes_form(request, id=None):
    workspace = get_workspace(request.user)
    if request.method == "POST":
        description = request.POST.get("note_description")

        if request.POST.get("edit"):
            notes = Notes.objects.get(id=id)
            notes.description = description
            notes.save()

            update = Notes.objects.filter(
                deal=notes.deal, workspace=workspace
            ).order_by("-created_at")
            context = {
                "updates": update,
            }
            response = render(
                request, "data/contacts/partial-case-notes-rows.html", context
            )
            return response

        elif request.POST.get("delete"):
            notes = Notes.objects.get(id=id)
            notes.delete()

            update = Notes.objects.filter(
                deal=notes.deal, workspace=workspace
            ).order_by("-created_at")

            context = {
                "updates": update,
            }
            response = render(
                request, "data/contacts/partial-case-notes-rows.html", context
            )
            return response

        note_date = request.POST.get("note_date")
        if id:
            deal = Deals.objects.get(id=id)
            Notes.objects.create(
                deal=deal,
                description=description,
                date=note_date,
                assignee=request.user,
                workspace=workspace,
                target=TYPE_OBJECT_CASE,
            )

            update = Notes.objects.filter(deal=deal, workspace=workspace).order_by(
                "-created_at"
            )

            context = {
                "updates": update,
            }
            response = render(
                request, "data/contacts/partial-case-notes-rows.html", context
            )
        else:
            response = render(request, "data/contacts/partial-case-notes-rows.html")

        return response

    deal = Deals.objects.get(id=id)

    context = {"deal": deal, "uuid": str(uuid.uuid4())}
    return render(request, "data/contacts/case-notes-form.html", context)


@login_or_hubspot_required
def case_notes_form(request):
    return render(request, "data/contacts/case-notes-form.html")


@login_or_hubspot_required
def get_cases_events(request, id):
    workspace = get_workspace(request.user)
    try:
        deal = Deals.objects.get(id=id, workspace=workspace)
    except Deals.DoesNotExist:
        return HttpResponse(status=404)

    app_logs = AppLog.objects.filter(workspace=workspace, deal=deal).first()

    context = {
        "app_logs": app_logs,
        "deal": deal,
    }
    return render(request, "data/contacts/case/case-events.html", context)


@login_or_hubspot_required
def get_cases_options(request):
    workspace = get_workspace(request.user)
    q = request.GET.get("q")
    page = request.GET.get("page", 1)

    filter_conditions = Q(workspace=workspace, status="active")
    if q:
        q_id = q

        try:
            if q[0] == "#" or q[0] == "0":
                q_id = int(q[1:].replace(" ", ""))
        except Exception as e:
            print(e)

        filter_conditions &= Q(deal_id__icontains=q_id)

    deals = Deals.objects.filter(filter_conditions).order_by("-deal_id")

    res = []
    ITEMS_PER_PAGE = 30
    deals_paginator = Paginator(deals, ITEMS_PER_PAGE)
    paginated_deals = []
    more_pagination = False
    if page:
        try:
            deals_page_content = deals_paginator.page(page if page else 1)
            paginated_deals = deals_page_content.object_list
            more_pagination = deals_page_content.has_next()
        except EmptyPage:
            pass

    for deal in paginated_deals:
        try:
            text = f"#{deal.deal_id:04d} - {deal.name}"
        except Exception:
            pass

        res.append(
            {
                "id": str(deal.id),
                "text": text,
            }
        )

    context = {"results": res, "pagination": {"more": more_pagination}}

    return JsonResponse(context)


@login_or_hubspot_required
@require_GET
def cases_to_order_association_drawer(request):
    print(f"[DEBUG] cases_to_order_association_drawer - All GET parameters:")
    for key, value in request.GET.items():
        print(f"[DEBUG]   {key}: {value}")

    source = request.GET.get("source", None)
    object_id = request.GET.get("object_id", None)
    page = request.GET.get("page", 1)
    view_id = request.GET.get("view_id", None)
    module = request.GET.get("module", None)
    object_type = request.GET.get("object_type", None)

    print(
        f"[DEBUG] cases_to_order_association_drawer - source: {source}, object_id: {object_id}"
    )

    obj = None

    try:
        if source == TYPE_OBJECT_ORDER:
            from data.models.order import ShopTurboOrders

            obj = ShopTurboOrders.objects.get(id=object_id)
    except:
        pass

    context = {
        "source": source,
        "object_id": object_id,
        "page": page,
        "view_id": view_id,
        "obj": obj,
        "module": module,
        "object_type": object_type,
        "form_id": uuid.uuid4(),
    }

    return render(request, "data/association/default-create-add/cases.html", context)


def case_association_drawer(request):
    if request.method == "GET":
        source = request.GET.get("source", None)
        object_id = request.GET.get("object_id", None)
        page = request.GET.get("page", 1)
        view_id = request.GET.get("view_id", None)
        module = request.GET.get("module", None)
        print("[DEBUG] module case_association", module)

        if source == TYPE_OBJECT_TASK:
            obj = Task.objects.get(id=object_id)
        elif source == TYPE_OBJECT_INVOICE:
            obj = Invoice.objects.get(id=object_id)
        elif source == TYPE_OBJECT_COMPANY:
            obj = None
            company = Company.objects.get(id=object_id)
            deal_objs = Deals.objects.filter(company=company)
        elif source == TYPE_OBJECT_ESTIMATE:
            obj = Estimate.objects.get(id=object_id)
        elif source == TYPE_OBJECT_CONTACT:
            obj = Contact.objects.get(id=object_id)

        context = {
            "source": source,
            "object_id": object_id,
            "page": page,
            "view_id": view_id,
            "obj": obj,
            "module": module,
        }

        if source == TYPE_OBJECT_COMPANY:
            context["deal_objs"] = deal_objs

        return render(request, "data/association/default-create-add/case.html", context)
    else:
        return HttpResponse(200)


@login_or_hubspot_required
@require_GET
def autocomplete_case(request):
    workspace = get_workspace(request.user)
    search = request.GET.get("search", "")
    ids = request.GET.getlist("ids[]", "")

    # Filter out invalid IDs (empty strings, "None", etc.) and validate UUIDs
    valid_ids = []
    if ids:
        for id_value in ids:
            if id_value and id_value != "None" and is_valid_uuid(id_value):
                valid_ids.append(id_value)

    filter_condition = Q(workspace=workspace) & ~Q(status="archived")

    # filter with edit permission
    permission = get_permission(object_type=TYPE_OBJECT_CASE, user=request.user)
    filter_condition &= get_permission_filter(
        permission, request.user, permission_type="edit"
    )

    page = int(request.GET.get("page", 1))

    results_per_page = 10 if len(valid_ids) <= 0 else len(valid_ids)
    start = (page - 1) * results_per_page
    end = start + results_per_page

    if search:
        filter_condition &= Q(
            deal_id=search
            | Q(name__icontains=search)
            | Q(contact__name__icontains=search)
            | Q(company__name__icontains=search)
        )

    if valid_ids:
        filter_condition &= Q(id__in=valid_ids)

    obj, created = ObjectManager.objects.get_or_create(
        workspace=workspace, page_group_type=TYPE_OBJECT_CASE
    )
    if created:
        try:
            col_display = ",".join(DEFAULT_OBJECT_DISPLAY[TYPE_OBJECT_CASE])
        except:
            col_display = "deal_id,name"
        obj.column_display = col_display
        obj.save()
    else:
        col_display = obj.column_display if obj.column_display else "deal_id,name"

    columns = col_display.split(",")
    results = Deals.objects.filter(filter_condition).order_by("-created_at")[start:end]
    data = [
        {
            "id": item.id,
            "text": get_object_display_based_columns(
                TYPE_OBJECT_CASE,
                item,
                columns,
                workspace.timezone,
                request.LANGUAGE_CODE,
            ),
        }
        for item in results
    ]

    return JsonResponse({"results": data, "pagination": {"more": results.exists()}})


def load_price_info(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    drawer_type = request.GET.get("drawer_type", None)
    if drawer_type == "add-item":
        predefined_item = request.GET.get("predefined_item", None)
        predefined_price = request.GET.get("predefined_price", None)
        predefined_number_of_items = request.GET.get("predefined_number_of_items", None)
        currency = request.GET.get("currency", None)
        predefined_shopturbo_item = request.GET.get("predefined_shopturbo_item", None)
        additional_fields = request.GET.get("additional_fields", None)
        default_fields = request.GET.get("default_fields", None)

        if additional_fields:
            additional_fields = ast.literal_eval(additional_fields)
            obj_additional_fields = []
            for additional_field in additional_fields:
                obj_additional_fields.append(
                    DealsItemsNameCustomField.objects.get(id=additional_field)
                )
            additional_fields = obj_additional_fields
        if default_fields:
            default_fields = ast.literal_eval(default_fields)
        print("here", additional_fields)

        condition_filter = Q(workspace=workspace, status="active")
        if currency:
            condition_filter &= Q(currency=currency)
        shopturbo_items = ShopTurboItems.objects.filter(condition_filter)
        predefined_additional_field_values = {}
        print("predefined_shopturbo_item", predefined_shopturbo_item)
        if predefined_shopturbo_item and additional_fields:
            for additional_field in additional_fields:
                additional_field_value = DealsItemsValueCustomField.objects.filter(
                    field_name=additional_field,
                    item_deal_id=int(predefined_shopturbo_item),
                ).first()
                if additional_field_value:
                    predefined_additional_field_values[additional_field.name] = (
                        additional_field_value.value
                    )
                else:
                    predefined_additional_field_values[additional_field.name] = ""
        print("log a", predefined_additional_field_values)

        context = {
            "shopturbo_items": shopturbo_items,
            "unique_id": uuid.uuid4(),
            "load_price_type": "main",
            "predefined_item": predefined_item,
            "predefined_price": predefined_price,
            "predefined_number_of_items": predefined_number_of_items,
            "additional_fields": additional_fields,
            "default_fields": default_fields,
            "predefined_additional_field_values": predefined_additional_field_values,
        }
        return render(
            request,
            "data/common/custom_field/price-info/case-price-information-item.html",
            context,
        )
    elif drawer_type == "load_price":
        item = request.GET.get("item_case", None)
        predefined_price = request.GET.get("predefined_price", None)
        default_fields = request.GET.get("default_fields", None)
        if default_fields:
            default_fields = ast.literal_eval(default_fields)
        shopturbo_item_prices = []
        if item:
            shopturbo_item_prices = ShopTurboItemsPrice.objects.filter(item__id=item)
        context = {
            "shopturbo_item_prices": shopturbo_item_prices,
            "load_price_type": "price",
            "predefined_price": predefined_price,
            "default_fields": default_fields,
        }
        return render(
            request,
            "data/common/custom_field/price-info/case-price-information-item.html",
            context,
        )
    elif drawer_type == "add-manual-item":
        predefined_item = request.GET.get("predefined_item", None)
        predefined_price = request.GET.get("predefined_price", None)
        predefined_number_of_items = request.GET.get("predefined_number_of_items", None)
        shopturbo_item_prices = []
        CustomFieldName = request.GET.get("CustomFieldName", None)
        currency = request.GET.get("currency", None)
        predefined_shopturbo_item = request.GET.get("predefined_shopturbo_item", None)
        additional_fields = request.GET.get("additional_fields", None)
        default_fields = request.GET.get("default_fields", None)

        if additional_fields:
            additional_fields = ast.literal_eval(additional_fields)
            obj_additional_fields = []
            for additional_field in additional_fields:
                obj_additional_fields.append(
                    DealsItemsNameCustomField.objects.get(id=additional_field)
                )
            additional_fields = obj_additional_fields
        if default_fields:
            default_fields = ast.literal_eval(default_fields)

        print("====== currency: ", currency)
        predefined_additional_field_values = {}
        if predefined_shopturbo_item and additional_fields:
            for additional_field in additional_fields:
                additional_field_value = DealsItemsValueCustomField.objects.filter(
                    field_name=additional_field,
                    item_deal_id=int(predefined_shopturbo_item),
                ).first()
                if additional_field_value:
                    predefined_additional_field_values[additional_field.name] = (
                        additional_field_value.value
                    )
                else:
                    predefined_additional_field_values[additional_field.name] = ""
        print("log b", predefined_additional_field_values)

        context = {
            "shopturbo_item_prices": shopturbo_item_prices,
            "load_price_type": "main-manual",
            "predefined_item": predefined_item,
            "predefined_price": predefined_price,
            "predefined_number_of_items": predefined_number_of_items,
            "additional_fields": additional_fields,
            "predefined_additional_field_values": predefined_additional_field_values,
            "default_fields": default_fields,
            "CustomFieldName": CustomFieldName,
            "currency": currency,
            "uuid": uuid.uuid4(),
        }
        return render(
            request,
            "data/common/custom_field/price-info/case-price-information-item.html",
            context,
        )
    else:
        obj_id = request.GET.get("obj_id", None)
        currency = request.GET.get("currency", None)
        set_id = request.GET.get("set_id", None)

        line_item_properties = []
        default_fields = []
        if set_id:
            property_set = PropertySet.objects.get(id=set_id)
            line_item_properties = [
                str(id)
                for id in property_set.deal_line_items.values_list("id", flat=True)
            ]
            default_properties = property_set.default_properties

        value = DealsItems.objects.filter(deal_id=obj_id).order_by("order_view")

        print("log", obj_id, currency, set_id, line_item_properties, value)

        context = {
            "deal_id": obj_id,
            "predefined_shopturbo_items": value,
            "currency": currency,
            "additional_fields": line_item_properties,
            "default_fields": default_properties,
        }

        return render(
            request,
            "data/common/custom_field/price-info/case-price-information.html",
            context,
        )


@login_or_hubspot_required
def load_drawer(request):
    drawer_type = request.GET.get("drawer_type", False)
    workspace = get_workspace(request.user)

    if drawer_type == "case-view-export-import":
        section = request.GET.get("section", None)
        print('section:', section)

        if section == "history":
            import_export_type = request.GET.get("import_export_type", None)
            filter_conditions = Q(workspace=workspace)
            if import_export_type == "import":
                filter_conditions &= Q(type="import_case")
            elif import_export_type == "export":
                filter_conditions &= Q(type="export_case")

            history = TransferHistory.objects.filter(filter_conditions).order_by(
                "-created_at"
            )

            context = {"history": history, "object_type": TYPE_OBJECT_CASE, "import_export_type": import_export_type}
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-items-import-export-history.html",
                context,
            )

        elif section == "import_selector":
            context = {
                "page_group_type": TYPE_OBJECT_CASE,
                "view_id": request.GET.get("view_id", None),
                "section": "bulk-entry",
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-case-import-source-selector.html",
                context,
            )

        elif section == "export_selector":
            context = {
                "page_group_type": TYPE_OBJECT_CASE,
                "contact_ids": request.GET.get("contact_ids", None),
                "view_id": request.GET.get("view_id", None),
                "section": "bulk-entry",
            }
            print('Initial context:', context)
            return render(
                request,
                "data/shopturbo/sync-contacts-export-source-selector.html",
                context,
            )

        elif section == "csv_import":
            context = {
                "page_group_type": TYPE_OBJECT_CASE,
                "view_id": request.GET.get("view_id", None),
                "section": "bulk-entry",
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-case-import-csv.html",
                context,
            )
    
        elif section == "other_exports":
            return view_settings(request)

        elif section == "integrations":
            channels = Channel.objects.filter(
                Q(
                    workspace=get_workspace(request.user),
                    integration__slug__in=["hubspot","salesforce"],
                )
                | Q(
                    workspace=get_workspace(request.user),
                    api_key=settings.HUBSPOT_CLIENT_ID,
                )
            ).exclude(status="draft")
            module = (
                Module.objects.filter(
                    workspace=workspace, object_values__contains=TYPE_OBJECT_CASE
                )
                .order_by("order", "created_at")
                .first()
            )
            module_slug = None
            if module:
                module_slug = module.slug
            
            case_ids = request.GET.get("ids", [])
            if isinstance(case_ids, str):
                try:
                    case_ids = ast.literal_eval(case_ids)
                    case_ids = case_ids[0].split(',')
                except:
                    case_ids = []
            
            if len(case_ids) == 0:
                case_ids = Deals.objects.filter(
                    workspace=workspace, status="active"
                ).values_list("id", flat=True)
            print('[DEBUG] case_ids:', case_ids)
            
            context = {
                "import_export_type": request.GET.get("import_export_type", None),
                "channels": channels,
                "object_type": TYPE_OBJECT_CASE,
                "menu_key": module_slug,
                "case_ids": case_ids,
            }
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-case-import-export.html",
                context,
            )

        else:
            checkbox = None

            if "checkbox" in request.GET:
                checkbox = request.GET.getlist("checkbox", False)

            import_export_type = request.GET.get("import_export_type", None)
            view_id = request.GET.get("view_id", None)
            context = {
                "contact_ids": checkbox,
                "import_export_type": import_export_type,
                "page_group_type": TYPE_OBJECT_CASE,
                "view_id": view_id,
            }
            print('[DEBUG] context:', context)
            return render(
                request,
                "data/shopturbo/manage-sync-settings-shopturbo-contacts.html",
                context,
            )

        return render(request, "data/contacts/manage-contacts-download.html", context)

    return HttpResponse(200)


def sync_case_header_extractor(request):
    workspace = get_workspace(request.user)
    lang = request.LANGUAGE_CODE
    if request.method == "POST":
        mapping_type = request.POST.get("mapping_type", None)
        platform_columns = []
        contacts_columns = ["name", "last_name", "email", "phone_number"]
        if "action_index" in request.POST:
            action_index = request.POST.get("action_index")
            select_integration_ids = request.POST.get(
                f"select_integration_ids-{action_index}"
            )
        else:
            select_integration_ids = request.POST.get("select_integration_ids")

        try:
            channel = Channel.objects.get(id=select_integration_ids)
        except Exception as e:
            print(f"WARNING === shopturbo.py -- 7714: {e}")
            return HttpResponse(404)

        header_list = []
        function_type = None
        if mapping_type == "order":
            default_mapping_field = None
            ordersnamecustomfield = (
                DealsNameCustomField.objects.filter(workspace=workspace)
                .exclude(type__in=["image", "user", "formula"])
                .values_list("name", flat=True)
            )
            if ordersnamecustomfield:
                default_mapping_field = ordersnamecustomfield[0]

            if channel.integration.slug == "hubspot":
                platform_columns.extend(
                    [
                        col
                        for col in DEFAULT_COLUMNS_CASE
                        if col in ("name", "case_status")
                    ]
                )
                function_type = request.POST.get("function_type", None)
                access_token = channel.access_token
                api_client = HubSpot(access_token=access_token)

                if function_type == "import":
                    header_list = [
                        {
                            "value": "deal_name",
                            "name": "Deal - Deal Name",
                            "name_ja": "取引 - 取引名",
                            "skip": False,
                            "default": "name",
                        },
                        {
                            "value": "deal_stage",
                            "name": "Deal - Deal Stage",
                            "name_ja": "取引 - 取引ステージ",
                            "skip": False,
                            "default": default_mapping_field,
                        },
                    ]
                    try:
                        token_url = "https://api.hubapi.com/oauth/v1/token"
                        data = {
                            "grant_type": "refresh_token",
                            "client_id": settings.HUBSPOT_CLIENT_ID,
                            "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                            "refresh_token": channel.refresh_token,
                        }
                        response = requests.post(token_url, data=data)
                        token_info = response.json()
                        channel.access_token = token_info.get("access_token")
                        channel.save()

                        access_token = channel.access_token

                        deal_properties_url = (
                            "https://api.hubapi.com/properties/v1/deals/properties"
                        )
                        headers = {
                            "Authorization": f"Bearer {access_token}",
                            "Content-Type": "application/json",
                        }
                        deal_properties_response = requests.get(
                            deal_properties_url, headers=headers
                        )
                        deal_properties_response.raise_for_status()

                        # Process the deal properties
                        deal_properties = deal_properties_response.json()
                        if function_type == "import":
                            deal_inclusions = [
                                "sanka_id",
                                "platform",
                                "platform_id",
                                "hs_lastmodifieddate",
                                "hs_latest_approval_status",
                                "notes",
                                "pipeline",
                                "hs_acv",
                                "hs_arr",
                                "dealtype",
                                "description",
                                "hs_deal_score",
                                "hs_deal_stage_probability",
                                "hs_priority",
                            ]
                        else:
                            deal_inclusions = [
                                "sanka_id",
                                "platform",
                                "platform_id",
                                "hs_lastmodifieddate",
                                "notes",
                                "pipeline",
                                "dealtype",
                                "description",
                                "hs_deal_stage_probability",
                                "hs_priority",
                            ]
                        custom_properties = [
                            prop
                            for prop in deal_properties
                            if not prop.get("hubspotDefined", True)
                            or prop.get("name") in deal_inclusions
                        ]
                        for property in custom_properties:
                            if function_type == "import":
                                _exc = ["sanka_id"]
                            else:
                                _exc = ["sanka_id", "platform", "platform_id"]
                            if property.get("name", "") not in _exc:
                                header_list.append(
                                    {
                                        "value": f"{property.get('name', '')}|deal",
                                        "name": f"Deal - {property.get('label', property.get('name', ''))}",
                                        "name_ja": f"取引 - {property.get('label', property.get('name', ''))}",
                                        "skip": False,
                                        "default": default_mapping_field,
                                        "hubspot_default": property.get(
                                            "hubspotDefined"
                                        ),
                                    }
                                )

                        hubspot_defined_inclusions = [
                            "name",
                            "firstname",
                            "email",
                            "phone",
                            "address",
                            "address2",
                            "contact_address",
                            "company_address",
                            "city",
                            "company",
                            "country",
                            "fax",
                            "industry",
                            "jobtitle",
                            "lastmodifieddate",
                            "mobilephone",
                            "state",
                            "website",
                            "zip",
                            "total_revenue",
                            "work_email",
                            "annualrevenue",
                        ]

                        contact_properties_url = (
                            "https://api.hubapi.com/properties/v1/contacts/properties"
                        )
                        contact_properties_response = requests.get(
                            contact_properties_url, headers=headers
                        )
                        contact_properties_response.raise_for_status()

                        contact_properties = contact_properties_response.json()
                        custom_contact_properties = [
                            prop
                            for prop in contact_properties
                            if not prop.get("hubspotDefined", True)
                            or prop.get("name") in hubspot_defined_inclusions
                        ]
                        for property in custom_contact_properties:
                            if property.get("name", "") not in [
                                "sanka_id",
                                "platform",
                                "platform_id",
                            ]:
                                header_list.append(
                                    {
                                        "value": f"{property.get('name', '')}|contact",
                                        "name": f"Contact - {property.get('label', property.get('name', ''))}",
                                        "name_ja": f"連絡先 - {property.get('label', property.get('name', ''))}",
                                        "skip": False,
                                        "default": default_mapping_field,
                                        "hubspot_default": property.get(
                                            "hubspotDefined"
                                        ),
                                    }
                                )

                        company_properties_url = (
                            "https://api.hubapi.com/properties/v1/companies/properties"
                        )
                        company_properties_response = requests.get(
                            company_properties_url, headers=headers
                        )
                        company_properties_response.raise_for_status()

                        company_properties = company_properties_response.json()
                        custom_company_properties = [
                            prop
                            for prop in company_properties
                            if not prop.get("hubspotDefined", True)
                            or prop.get("name") in hubspot_defined_inclusions
                        ]
                        for property in custom_company_properties:
                            if property.get("name", "") not in [
                                "sanka_id",
                                "platform",
                                "platform_id",
                                "email",
                            ]:
                                header_list.append(
                                    {
                                        "value": f"{property.get('name', '')}|company",
                                        "name": f"Company - {property.get('label', property.get('name', ''))}",
                                        "name_ja": f"会社 - {property.get('label', property.get('name', ''))}",
                                        "skip": False,
                                        "default": default_mapping_field,
                                        "hubspot_default": property.get(
                                            "hubspotDefined"
                                        ),
                                    }
                                )
                    except Exception as e:
                        print(f"Error occurred: {e}")
                elif function_type == 'export':
                    mapping_type = 'case'
                    header_list = []
                    try:
                        token_url = "https://api.hubapi.com/oauth/v1/token"
                        data = {
                            "grant_type": "refresh_token",
                            "client_id": settings.HUBSPOT_CLIENT_ID,
                            "client_secret": settings.HUBSPOT_CLIENT_SECRET,
                            "refresh_token": channel.refresh_token,
                        }
                        response = requests.post(token_url, data=data)
                        token_info = response.json()
                        channel.access_token = token_info.get("access_token")
                        channel.save()

                        access_token = channel.access_token

                        ticket_properties_url = (
                            "https://api.hubapi.com/crm/v3/properties/tickets"
                        )
                        headers = {
                            "Authorization": f"Bearer {access_token}",
                            "Content-Type": "application/json",
                        }
                        ticket_properties_response = requests.get(
                            ticket_properties_url, headers=headers
                        )
                        ticket_properties_response.raise_for_status()

                        # Process the ticket properties
                        ticket_properties = ticket_properties_response.json()['results']
                        ticket_inclusions = [
                            "subject",
                            "content"
                        ]
                        custom_properties = [
                            prop
                            for prop in ticket_properties
                            if not prop.get("hubspotDefined", True)
                            or prop.get("name") in ticket_inclusions
                        ]
                        for property in custom_properties:
                            if function_type == "import":
                                _exc = ["sanka_id"]
                            else:
                                _exc = ["sanka_id", "platform", "platform_id"]
                            if property.get("name", "") not in _exc:
                                header_list.append(
                                    {
                                        "value": f"{property.get('name', '')}",
                                        "name": f"{property.get('label', property.get('name', ''))}",
                                        "name_ja": f"{property.get('label', property.get('name', ''))}",
                                        "skip": False,
                                        "default": default_mapping_field,
                                        "hubspot_default": property.get(
                                            "hubspotDefined"
                                        ),
                                    }
                                )
                    except Exception as e:
                        print(f"Error occurred: {e}")

            platform_columns = ["create_new", "name", "case_status"]
            ordersnamecustomfield = (
                DealsNameCustomField.objects.filter(workspace=workspace)
                .exclude(type__in=["image", "user", "formula", "shipping_info"])
                .values_list("name", flat=True)
            )
            platform_columns.extend(ordersnamecustomfield)
            
            # Sub Property
            sub_property_columns = {}
            shipping_infos = DealsNameCustomField.objects.filter(
                workspace=workspace, type="shipping_info"
            )
        
        elif mapping_type == "case":
            if channel.integration.slug == "salesforce":
                header_list = []
                mapping_fields = None
                object_type = request.POST.get("object_type", "case")
                if object_type == "case":
                    mapping_fields = get_case_mapping_fields(str(channel.id))

                if mapping_fields:
                    # Convert case_mapping_fields to header_list format
                    for field in mapping_fields:
                        field_name = field.get("name", "")
                        field_label = field.get("label", field_name)

                        # Skip system fields and certain lookup fields
                        if field_name not in ["sanka_id", "platform", "platform_id"]:
                            default_value = ""

                            # Set default mappings for common fields
                            if field_name == "Subject":
                                default_value = "name"
                            elif field_name == "Status":
                                default_value = "case_status"
                            elif field_name == "OwnerId":
                                default_value = "owner"
                            elif field_name == "ContactId":
                                default_value = "contact"
                            elif field_name == "AccountId":
                                default_value = "company"
                            # Description and Priority will be handled as custom fields
                            elif field_name == "Description":
                                default_value = "create_new"
                            elif field_name == "Priority":
                                default_value = "create_new"
                            elif field_name == "Type":
                                default_value = "create_new"

                            header_list.append(
                                {
                                    "value": field_name,
                                    "name": field_label,
                                    "name_ja": field_label,  # Using same label for Japanese
                                    "skip": False,
                                    "default": default_value,
                                }
                            )
            
            platform_columns = ["create_new", "name", "case_status", "customer"]
            ordersnamecustomfield = (
                DealsNameCustomField.objects.filter(workspace=workspace)
                .exclude(type__in=["image", "user", "formula", "shipping_info"])
                .values_list("name", flat=True)
            )
            platform_columns.extend(ordersnamecustomfield)
            
            # Sub Property
            sub_property_columns = {}
            shipping_infos = DealsNameCustomField.objects.filter(
                workspace=workspace, type="shipping_info"
            )

        try:
            mapping, _ = DealsMappingFields.objects.get_or_create(
                workspace=workspace, platform=channel.integration.slug
            )
            if mapping.input_data:
                for item in header_list:
                    if item["value"] in mapping.input_data:
                        item["default"] = mapping.input_data[item["value"]]["value"]
                        item["skip"] = mapping.input_data[item["value"]]["skip"]
        except Exception:
            traceback.print_exc()
        
        try:
            if shipping_infos:
                for shipping_info in shipping_infos:
                    if shipping_info.sub_property:
                        sub_property_columns[shipping_info.name] = []
                        for sub_property in ast.literal_eval(
                            shipping_info.sub_property
                        ):
                            sub_property_columns[shipping_info.name].append(
                                sub_property
                            )
        except Exception:
            traceback.print_exc()
        contacts_columns.extend(
            ContactsNameCustomField.objects.filter(workspace=workspace)
            .exclude(type__in=["image", "user", "formula"])
            .values_list("name", flat=True)
        )

        company_columns = ["name", "email", "phone_number"]
        company_columns.extend(
            CompanyNameCustomField.objects.filter(workspace=workspace)
            .exclude(type__in=["image", "user", "formula"])
            .values_list("name", flat=True)
        )
        if channel.integration.slug in ["hubspot"]:
            is_multi_object = True
        else:
            is_multi_object = False

        context = {
            "header_list": header_list,
            "platform_columns": platform_columns,
            "platform": channel.integration.slug,
            "sub_property_columns": sub_property_columns,
            "contacts_columns": contacts_columns,
            "company_columns": company_columns,
            "is_multi_object": is_multi_object,
            "function_type": function_type,
            "mapping_type": mapping_type,
        }
        return render(
            request, "data/shopturbo/sync-cases-import-mapping.html", context
        )
    return HttpResponse(200)


def apply_search_setting(
    page_group_type,
    view_filter: ViewFilter,
    search_key,
    search_value,
    force_filter_list=[],
):
    view = view_filter.view
    dummy_view_filter = ViewFilter(
        view=view, filter_value={search_key: {"key": "contains", "value": search_value}}
    )
    return build_view_filter(
        Q(), dummy_view_filter, page_group_type, force_filter_list=force_filter_list
    )
