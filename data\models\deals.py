import uuid
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User

from data.models.constant import *
from data.constants.constant import *
from data.models import ObjectManager

from data.models.base import save_tags_values, validate_value_custom_field
from data.models.order import LINE_ITEMS_CF_TYPE


class DealsNotes(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    deal = models.ForeignKey(
        "Deals", on_delete=models.CASCADE, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    date = models.DateTimeField(null=True, blank=True,)
    assignee = models.ForeignKey(
        User, blank=True, null=True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(default=timezone.now)


SHOPTURBO_DEALS_STATUS = [
    ('active', 'Active'),
    ('archived', 'Archived'),
]

CASE_STATUS = [
    ('todo', 'TODO'),
    ('doing', 'DOING'),
    ('done', 'DONE'),
]


class Deals(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    deal_id = models.IntegerField(null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)

    company = models.ManyToManyField("Company", blank=True)
    contact = models.ManyToManyField("Contact", blank=True)
    status = models.CharField(
        choices=SHOPTURBO_DEALS_STATUS, max_length=30, null=True, blank=True)
    case_status = models.CharField(
        choices=CASE_STATUS, max_length=30, null=True, blank=True)

    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    kanban_order = models.IntegerField(null=True, blank=True)

    invoices = models.ManyToManyField(
        "Invoice", blank=True, related_name='deals')
    estimates = models.ManyToManyField(
        "Estimate", blank=True, related_name='deals')
    tasks = models.ManyToManyField("Task", blank=True, related_name='deals')

    owner = models.ForeignKey(
        "UserManagement", on_delete=models.SET_NULL, null=True, blank=True)

    objects = ObjectManager()  # Delete for Multiple. Such as filter().delete()
    
    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)
            self._log_data = log_data

        if not self.deal_id:
            # Use the safe ID generation utility to prevent race conditions
            from utils.safe_id_generation import safe_id_mixin_save
            safe_id_mixin_save(self, 'deal_id', '{:03d}')

        super().save(*args, **kwargs)


class DealsPlatforms(models.Model):
    deal = models.ForeignKey(Deals, on_delete=models.CASCADE,
                             null=True, blank=True, related_name='deal_platforms')
    channel = models.ForeignKey(
        "Channel", on_delete=models.SET_NULL, null=True, blank=True)
    platform_id = models.CharField(max_length=200, null=True, blank=True)
    platform_display_name = models.CharField(
        max_length=500, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)


DEALS_CF_TYPE = [
    ('text', 'Text'),
    ('number', 'Number'),
    ('text-area', 'Text Area'),
    ('image', 'Image'),
    ('date', 'Date'),
    ('date_time', 'Date Time'),
    ('date_range', 'Date Range'),
    ('choice', 'Choice'),
    ('user', 'User'),
    ('image_group', 'Image Group'),
    ('formula', 'Formula'),
    ('tag', 'Tag'),
    ('bill_objects', 'Bill Object'),
    ('invoice_objects', 'Invoice Object'),
    ('contact', 'Contact Object'),
    ('company', 'Company Object'),
    ('purchase_order', 'Purchase Order Object'),
    ('subscription', 'Subscription Object'),
    ('warehouse_objects', 'Warehouse Object')
]


DEALS_NUMBER_FORMAT = [
    ('number', 'Number'),
    ('%', '%'),

    # Convert 3 tuples from currency_model to 2 tuples (Follow django choice rule))
    *[(currency[0].lower(), currency[0].upper()) for currency in CURRENCY_MODEL]
]


class DealsItems(models.Model):
    deal = models.ForeignKey(
        "Deals", on_delete=models.CASCADE, null=True, blank=True)

    item = models.ForeignKey("ShopTurboItems", on_delete=models.CASCADE,
                             null=True, blank=True, related_name="shopturbo_item_deals")
    item_variant = models.ForeignKey(
        "ShopTurboItemsVariations", on_delete=models.CASCADE, null=True, blank=True)
    item_price = models.ForeignKey(
        "ShopTurboItemsPrice", on_delete=models.CASCADE, null=True, blank=True)
    customer_item_price = models.ForeignKey(
        "ShopTurboCustomerItemsPrice", on_delete=models.CASCADE, null=True, blank=True)
    deal_platform = models.ForeignKey(
        "DealsPlatforms", on_delete=models.CASCADE, null=True, blank=True)

    custom_item_name = models.CharField(max_length=500, null=True, blank=True)

    platform_item_id = models.CharField(max_length=200, null=True, blank=True)
    number_item = models.FloatField(null=True, blank=True)
    item_price_deal = models.FloatField(null=True, blank=True)
    item_price_deal_tax = models.FloatField(null=True, blank=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    total_price = models.FloatField(null=True, blank=True)
    order_view = models.IntegerField(null=True, blank=True)

    item_status = models.CharField(max_length=500, null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)
            self._log_data = log_data
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)
            self._log_data = log_data
        super().delete(*args, **kwargs)

    def __str__(self):
        if self.item:
            if self.item.name:
                return f'{self.item.name}'
            else:
                return f'{self.item.item_id}'
        else:
            return f'{self.id}'


class DealsItemsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=LINE_ITEMS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    number_format = models.CharField(
        choices=DEALS_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)


class DealsItemsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    field_name = models.ForeignKey(
        DealsItemsNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    item_deal = models.ForeignKey(
        DealsItems, on_delete=models.CASCADE, null=True, blank=True)
    value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)


class DealsNameCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    # for displaying formula result
    value_display = models.CharField(max_length=50, null=True, blank=True)
    workspace = models.ForeignKey(
        "Workspace", on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=500, null=True, blank=True)
    type = models.CharField(choices=DEALS_CF_TYPE,
                            max_length=20, null=True, blank=True)
    number_format = models.CharField(
        choices=DEALS_NUMBER_FORMAT, max_length=20, null=True, blank=True)
    descriptions = models.TextField(null=True, blank=True)
    choice_value = models.TextField(null=True, blank=True)
    conditional_choice_mapping = models.JSONField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)
    unique = models.BooleanField(default=False)
    required_field = models.BooleanField(default=False)
    multiple_select = models.BooleanField(default=False)
    show_badge = models.BooleanField(default=False)
    badge_color = models.CharField(max_length=50, null=True, blank=True)
    tag_value = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    edit_by = models.ForeignKey(
        User, on_delete=models.CASCADE, null=True, blank=True)
    line_item_properties = models.TextField(null=True, blank=True)
    hidden_properties = models.TextField(default=[])


class DealsValueCustomField(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    field_name = models.ForeignKey(
        DealsNameCustomField, on_delete=models.CASCADE, null=True, blank=True)
    deals = models.ForeignKey(Deals, on_delete=models.CASCADE, null=True,
                              blank=True, related_name='deals_custom_field_relations')
    value = models.TextField(null=True, blank=True)
    file = models.FileField(verbose_name="Custom Field file",
                            upload_to="shopturbo-deals-customfield-files", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    value_number = models.FloatField(null=True, blank=True)
    value_time = models.DateTimeField(null=True, blank=True)
    order = models.IntegerField(null=True, default=0, blank=True)

    def save(self, *args, **kwargs):
        if "log_data" in kwargs.keys():
            log_data = kwargs.pop('log_data', None)
            self._log_data = log_data

        if self.field_name and (self.field_name.type == 'tag'):
            save_tags_values(self, args, kwargs)
        else:
            validate_value_custom_field(self, args, kwargs)


class DealsValueFile(models.Model):
    name = models.CharField(max_length=256, null=True, blank=True)
    file = models.FileField(verbose_name="Deals Custom Field file",
                            upload_to="deals-custom-field-files")
    valuecustomfield = models.ForeignKey(
        DealsValueCustomField, on_delete=models.CASCADE, null=True, blank=True)
    media_type = models.CharField(
        max_length=50, choices=POST_MEDIA_TYPE, default='image')
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return str(self.id) + ": " + str(self.name)
