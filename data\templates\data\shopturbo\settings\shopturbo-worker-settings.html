
{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}

<div class="mt-5" style="width: 100%;">
    
    <div class="mt-5 container-fluid px-0 scroll-y scoll-x">


        <div class="mb-10">
            <label class="d-flex align-items-center fs-3 fw-bolder me-3">
                <span class="">

                    {{setting_type|object_group_type:request}}

                    {% if LANGUAGE_CODE == 'ja' %}
                    オブジェクト
                    {% else %}
                    Object
                    {% endif %}
                </span>
            </label>

        </div>



        {% if setting_type == 'contacts' %}
        <div class="mb-0-10">
            <div id="change-stamp-section" class="mb-10">

                <form method="POST" action="{% host_url 'customerlink_settings' host 'app' %}" enctype="multipart/form-data" onkeydown="return event.key != 'Enter';">
                    {% csrf_token %}
                    <input type="hidden" class="" name="app_settings" value="manage_app"/>

                    <div class="mb-10"
                        hx-get="{% host_url 'properties' host 'app' %}"
                        hx-vals='{"page_group_type": "contacts"}'
                        hx-trigger="load"
                        hx-target="#properties-table"
                    >
                        <div id="properties-table"></div>
                    </div>
                    
                    <div class="mt-5 mb-5">
                        <label class="{% include 'data/utility/form-label.html' %}">
                            <span >
                                {% if LANGUAGE_CODE == 'ja' %}
                                検索設定
                                {% else %}
                                Search Settings
                                {% endif %}
                            </span>
                        </label>

                        <input
                        {% if LANGUAGE_CODE == 'ja' %}
                        placeholder="検索設定"
                        {% else %}
                        placeholder="Search Settings"
                        {% endif %}
                        id="search_setting_contact"
                        name="search_setting_contact" class="form-control" 
                        
                        value=""></input>
                    </div>
                    
                    <button id="submit-customfield-contacts-button" name="submit-customfield-contacts-button" type="submit" class="btn btn-primary mb-4">
                        {% if LANGUAGE_CODE == 'ja'%}
                        更新
                        {% else %}
                        Update
                        {% endif %}
                    </button>
                </form>
            </div>
        </div>
        {% endif %}
        <div class="mb-10">
            <div id="change-stamp-section" class="mb-10">
                <form method="POST" action="{% host_url 'messagerunner_settings' host 'app' %}" enctype="multipart/form-data" onkeydown="return event.key != 'Enter';">
                    {% csrf_token %}

                    <div class="mb-10"
                        hx-get="{% host_url 'properties' host 'app' %}"
                        hx-vals='{"page_group_type": "{{setting_type}}"}'
                        hx-trigger="load"
                        hx-target="#properties-table"
                    >
                        <div id="properties-table"></div>
                    </div>

                    <button id="submit-customfield-deals-button" name="submit-customfield-deals-button" type="submit" class="btn btn-dark my-5">
                        {% if LANGUAGE_CODE == 'ja'%}
                        更新
                        {% else %}
                        Update
                        {% endif %}
                    </button>
                </form> 

            </div>
        </div>
    </div>
</div>

<style>
    .stretch-select-custom span {
        height:100% !important;
    }
    .stretch-select-custom span span span span{
        {% comment %} display: flex !important; {% endcomment %}
        align-content: center;
        flex-wrap: wrap;
        margin-bottom: auto;
        margin-top: auto;
    }
</style>
<style>
    .loading-spinner{
        display:none;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>

<script>
    $(document).ready(function() {
        $('.select2-this').select2();
    });

    function delete_deals_custom_field_name(elm){
        elm.parentElement.parentElement.parentElement.parentElement.remove()
    }
</script>
