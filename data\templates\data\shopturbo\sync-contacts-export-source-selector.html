{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="card-body">
    <style>
            .selection option {
                display: none;
            }    
    </style>
    <div class="fv-rowd-flex flex-column mb-8" {% if hide_title %}hidden{% endif %}>
        <label class="{% include 'data/utility/form-label.html' %}">
            <span class="">
                {% if LANGUAGE_CODE == 'ja'%}レコードID{% else %}Record ID{% endif %}
            </span>
        </label>
        <select form="object-action-form" class="form-control object-ids-select2-this" name="object_ids" multiple="multiple" id="object_ids">
        </select>
    </div>
    <div class="fv-rowd-flex flex-column mb-8">
        <label class="d-flex align-items-center fs-5 fw-bolder text-active-primary fw-bolder mb-2">
            <span class="min-w-100px">
                {% if LANGUAGE_CODE == 'ja'%}
                エクスポート先を選択
                {% else %}
                Choose Export Destination
                {% endif %}
            </span>
        </label>
        
        <div class="mb-2">
            <select id="export_source" class="bg-white form-select form-select-solid border h-40px select2-this" 
            
            {% if page_group_type == 'contacts' %}
                hx-get="{% url 'load_drawer_sync_contacts' %}"
                hx-vals = '{"drawer_type":"shopturbo-view-sync-contacts","section":"export_container","contact_ids":"{% if contact_ids and contact_ids != "None" %}{{contact_ids}}{% endif %}","import_export_type":"export","view_id":"{{view_id}}"}'
            {% elif page_group_type == 'company' %}
                hx-get="{% url 'new_customerlinkapp_drawer' %}"
                hx-vals = '{"drawer_type":"contacts-view-settings","page":"companies","view_id":"{{view_filter.view.id}}","download_view": true,"import_export_type":"export","company_ids":"{% if company_ids and company_ids != "None" %}{{company_ids}}{% endif %}"}'
            {% elif page_group_type == 'customer_case' %}
                hx-get="{% url 'case_load_drawer' %}"
                hx-vals = '{"drawer_type":"case-view-export-import","download_view": true,"import_export_type":"export","ids":"{% if contact_ids and contact_ids != "None" %}{{contact_ids}}{% endif %}","view_id":"{{view_id}}","object_type": "customer_case"}'

            {% endif %}
            hx-target="#export-content" 
            hx-swap="innerHTML"
            hx-trigger="htmx-change"
            onchange="onChangeFilterSelect(this);"
            {% if LANGUAGE_CODE == 'ja'%}
            data-placeholder="エクスポート先を選択"
            {% else %}
            data-placeholder="Select Export Destination"
            {% endif %}
            >  

            <option></option>   
           
            <option value="csv" {% if pre_selected_export_selector == 'csv' %} selected {% endif %}>{% if LANGUAGE_CODE == 'ja'%}CSVファイル{% else %}CSV{% endif %}</option>
            <option value="integrations" {% if pre_selected_export_selector == 'integrations' %} selected {% endif %}>{% if LANGUAGE_CODE == 'ja'%}連携サービス{% else %}Integrations{% endif %}</option>                   
            </select>
        </div>

        <div id="export-content"></div>


    </div>
</div>

<script>
    $(document).ready(function(e) {
        var url = "{% host_url 'auto_complete_company' host 'app' %}"
        {% if page_group_type == 'contacts' %}
        url = "{% host_url 'auto_complete_contact' host 'app' %}"
        {% elif page_group_type == 'customer_case' %}
        url = "{% host_url 'auto_complete_case' host 'app' %}"
        {% endif %}
        console.log("URL {{page_group_type}}", url)

        $(".object-ids-select2-this").select2({
            placeholder: "{% if LANGUAGE_CODE == 'ja' %}レコードを選択{% else %}Select Record{% endif %}",
            allowClear: true,
            ajax: {
                url: url,
                dataType: "json",
                delay: 250,
                data: function (params) {
                    return {
                        search: params.term,
                        page: params.page || 1
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.results,
                        pagination: {
                            more: data.pagination.more,
                        }
                    };
                },
                cache: true
            }
        })

        {% if company_ids and company_ids != "None" %}
            var selectedObjectId = [
                {% for company_id in company_ids|convert_to_list %}
                    {% if company_id != "None" and company_id %}
                    "{{ company_id }}",
                    {% endif %}
                {% endfor %}
            ];
        {% elif contact_ids and contact_ids != "None" %}
            var selectedObjectId = [
                    {% for contact_id in contact_ids|convert_to_list %}
                    {% if contact_id != "None" and contact_id %}
                    "{{ contact_id }}",
                    {% endif %}
                {% endfor %}
            ];
        {% endif %}
        
        console.log(selectedObjectId)
        if (selectedObjectId !== undefined && selectedObjectId != '') {
            if (selectedObjectId.length > 0) {
                $.ajax({
                    url: url,
                    type: "GET",
                    data: { 'ids[]': selectedObjectId },
                    success: function(response) {
                        response.results.forEach(function(item) {
                            var preselectedOption = new Option(item.text, item.id, true, true); 
                            $('.object-ids-select2-this').append(preselectedOption)
                        })
    
                        $('.object-ids-select2-this').trigger('change');
                        $('.object-ids-select2-this').trigger({
                            type: 'select2:select',
                            params: {
                                data: response.results
                            }
                        });
    
                        // Prevent the dropdown from opening or closing
                        // Avoid user changes the preselected orderid
                        $('.object-ids-select2-this').on('select2:opening select2:closing', function(e) {
                            e.preventDefault();
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error("Error fetching preselected option:", error);
                    }
                });
            }
        }
    })

    $('.object-ids-select2-this').on('change', function(e) {
        var selectedValues = $(this).val();
        var formSelectObject = $('.object-id-select')

        if (selectedValues && selectedValues.length > 0) {
            selectedValues.forEach(function(value) {
                // Check if the value already exists in the dropdown
                if (formSelectObject.find("option[value='" + value + "']").length === 0) {
                    // If not, create a new option and append it
                    formSelectObject.append($('<option>', {
                        value: value,
                        text: value,
                        selected: true  // Automatically select the new option
                    }));
                } else {
                    // If the option exists, select it
                    formSelectObject.find("option[value='" + value + "']").prop('selected', true);
                }
            });
        } else {
            // If no selection, clear the selection in the second dropdown
            formSelectObject.val(null);
        }
    });    
</script>

<script>
    $('#export_source').select2()

    function onChangeFilterSelect(element) {
        var currentHxVals = element.getAttribute('hx-vals');
        var currentHxValsObject = JSON.parse(currentHxVals);
        
        let section =""
        let selectedValue = element.value
        if (selectedValue === 'csv') {
            
            {% if page_group_type == 'company' %}
                currentHxValsObject.drawer_type='contacts-view-settings'
            {% elif page_group_type == 'contacts' %}
                currentHxValsObject.drawer_type='shopturbo-view-sync-contacts'
            {% else %}
                currentHxValsObject.drawer_type='case-view-export-import'
            {% endif %}
            
            {% if page_group_type == 'company' %}
                section = 'export_download';
            {% elif page_group_type == 'contacts' %}
                section = 'export_download';
            {% else %}
                section = 'other_exports';
            {% endif %}
        } else if (selectedValue === 'integrations') {
            {% if page_group_type == 'company' %}
                currentHxValsObject.drawer_type='contacts-view-settings'
            {% elif page_group_type == 'contacts' %}
                currentHxValsObject.drawer_type='shopturbo-view-sync-contacts'
            {% else %}
                currentHxValsObject.drawer_type='case-view-export-import'
            {% endif %}
            
            section = 'integrations';
        }
        currentHxValsObject.section = section
        
        element.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));
        console.log("element: ", element)

        htmx.trigger('#export_source', 'htmx-change');
    }

    $(document).ready(function() {
        {% if pre_selected_export_selector == 'csv' %}
            var element = document.getElementById('export_source')
            onChangeFilterSelect(element)
        {% endif %}
    })
</script>