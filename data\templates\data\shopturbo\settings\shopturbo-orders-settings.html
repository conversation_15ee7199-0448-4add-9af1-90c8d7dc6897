{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% block content %}

<div class="mt-5" style="width: 100%;">
    <div class="mb-10">
        <label class="d-flex align-items-center fs-3 fw-bolder me-3">
            <span class="">

                {{setting_type|page_group_to_object:LANGUAGE_CODE}}

                {% if LANGUAGE_CODE == 'ja' %}
                オブジェクト
                {% else %}
                Object
                {% endif %}
            </span>
        </label>

    </div>

    <div class="mt-5 container-fluid px-0 scroll-y scoll-x">
        {% if setting_type == "commerce_orders" %}
        <form class="mb-5 pb-5" method="POST" action="{% host_url 'shopturbo_settings' host 'app' %}"
            onkeydown="return event.key != 'Enter';" id="order_app_setting">
            <input type="hidden" class="" name="app_settings" value="manage_app"/>
            {% csrf_token %}

            <div class="mb-10">
                <div class="mb-10"
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_orders"}'
                    hx-trigger="load"
                    hx-target="#properties-table"
                >
                    <div id="properties-table"></div>
                </div>

                {% comment %} <div class="mb-10"
                    hx-get="{% host_url 'property_sets' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_orders"}'
                    hx-trigger="load"
                    hx-swap="#property-sets-table"
                >
                    <div id="property-sets-table"></div>
                </div> {% endcomment %}

                {% comment %} Search Settings {% endcomment %}
                <div class="mb-10 d-none">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            検索設定
                            {% else %}
                            Search Settings
                            {% endif %}
                        </span>
                    </label>

                    <input
                    {% if LANGUAGE_CODE == 'ja' %}
                    placeholder="検索設定"
                    {% else %}
                    placeholder="Search Settings"
                    {% endif %}
                    id="search_setting_order"
                    name="search_setting_order" class="form-control" 
                    
                    value=""></input>
                </div>
            </div>
            
            {% comment %} ASSOCIATION LABEL SETTINGS {% endcomment %}
            <div
                hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                hx-vals='{"page_group_type": "commerce_orders"}'
                hx-trigger="load"
            ></div>

            {% comment %} Shipping Fee {% endcomment %}
            <div class="mb-10">
                <div class="fv-rowd-flex flex-column">
                    <div class="mb-5" id="shipping_fee_section">
                        <label class="fs-4 fw-bold mb-2">
                            <span class=""> 
                                {% if LANGUAGE_CODE == 'ja'%}
                                送料
                                {% else %}
                                Shipping Cost
                                {% endif %}
                                </h2>
                            </span>
                            <a href='javascript:;' onclick='add_shippings(this);'>
                                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24"/>
                                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                    </g>
                                </svg>
                            </a>
                        </label>

                        <div id="shipping-costs-container">
                            <!-- Shipping costs will be loaded here via AJAX -->
                            <div class="text-center py-3" id="shipping-loading">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading shipping costs...</span>
                                </div>
                                <div class="mt-2">Loading shipping costs...</div>
                            </div>
                        </div>

                        <script>
                        console.log("=== SHIPPING COSTS AJAX DEBUG ===");
                        console.log("Starting AJAX request for shipping costs");

                        // Load shipping costs via AJAX
                        fetch("{% host_url 'get_shipping_costs_data' host 'app' %}")
                            .then(response => {
                                console.log("Shipping AJAX Response status:", response.status);
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.text();
                            })
                            .then(html => {
                                console.log("Shipping AJAX Response received, length:", html.length);
                                console.log("Shipping AJAX Response preview:", html.substring(0, 200));
                                document.getElementById('shipping-costs-container').innerHTML = html;
                                console.log("Shipping AJAX content inserted successfully");
                            })
                            .catch(error => {
                                console.error("Shipping AJAX Error:", error);
                                document.getElementById('shipping-costs-container').innerHTML =
                                    '<div class="alert alert-danger">Error loading shipping costs: ' + error.message + '</div>';
                            });
                        </script>
                        
                    </div>
                </div>
                
            </div>

            {% comment %} Discount {% endcomment %}
            <div class="mb-10" id="discount_section">
                <div class="fv-rowd-flex flex-column">
                    <div class="mb-5">
                        <label class="fs-4 fw-bold mb-2">
                            <span class=""> 
                                {% if LANGUAGE_CODE == 'ja'%}
                                割引
                                {% else %}
                                Discount
                                {% endif %}
                                </h2>
                            </span>
                            <a href='javascript:;' onclick='add_discount(this);'>
                                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24"/>
                                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                    </g>
                                </svg>
                            </a>
                        </label>

                        <div id="discount-container">
                            <!-- Discounts will be loaded here via AJAX -->
                            <div class="text-center py-3" id="discount-loading">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading discounts...</span>
                                </div>
                                <div class="mt-2">Loading discounts...</div>
                            </div>
                        </div>

                        <script>
                        console.log("=== DISCOUNT AJAX DEBUG ===");
                        console.log("Starting AJAX request for discounts");

                        // Load discounts via AJAX
                        fetch("{% host_url 'get_order_settings_data' host 'app' %}")
                            .then(response => {
                                console.log("AJAX Response status:", response.status);
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.text();
                            })
                            .then(html => {
                                console.log("AJAX Response received, length:", html.length);
                                console.log("AJAX Response preview:", html.substring(0, 200));
                                document.getElementById('discount-container').innerHTML = html;
                                console.log("AJAX content inserted successfully");
                            })
                            .catch(error => {
                                console.error("AJAX Error:", error);
                                document.getElementById('discount-container').innerHTML =
                                    '<div class="alert alert-danger">Error loading discounts: ' + error.message + '</div>';
                            });
                        </script>
                    </div>
                </div>
            </div>

            {% comment %} Order Tax {% endcomment %}
            <div class="mb-10">
                <div class="fv-rowd-flex flex-column mb-3" id="tax_section">
                        
                        <label class="fs-4 fw-bold mb-2">
                            <span class=""> 
                                {% if LANGUAGE_CODE == 'ja'%}
                                注文税率
                                {% else %}
                                Order Tax Rate
                                {% endif %}
                                </h2>
                            </span>
                            <a href='javascript:;' onclick='add_order_level_tax(this);'>
                                <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24"/>
                                        <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                        <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                    </g>
                                </svg>
                            </a>
                        </label>

                        <div id="order-taxes-container">
                            <!-- Order taxes will be loaded here via AJAX -->
                            <div class="text-center py-3" id="taxes-loading">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Loading order taxes...</span>
                                </div>
                                <div class="mt-2">Loading order taxes...</div>
                            </div>
                        </div>

                        <script>
                        console.log("=== ORDER TAXES AJAX DEBUG ===");
                        console.log("Starting AJAX request for order taxes");

                        // Load order taxes via AJAX
                        fetch("{% host_url 'get_order_taxes_data' host 'app' %}")
                            .then(response => {
                                console.log("Taxes AJAX Response status:", response.status);
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.text();
                            })
                            .then(html => {
                                console.log("Taxes AJAX Response received, length:", html.length);
                                console.log("Taxes AJAX Response preview:", html.substring(0, 200));
                                document.getElementById('order-taxes-container').innerHTML = html;
                                console.log("Taxes AJAX content inserted successfully");
                            })
                            .catch(error => {
                                console.error("Taxes AJAX Error:", error);
                                document.getElementById('order-taxes-container').innerHTML =
                                    '<div class="alert alert-danger">Error loading order taxes: ' + error.message + '</div>';
                            });
                        </script>
                </div>
            </div>

            <div class="mt-5">
                <div class="fv-rowd-flex flex-column mb-3">
                    <label class="fs-4 fw-bold mb-2">
                        <span class=""> 
                            {% if LANGUAGE_CODE == 'ja'%}
                            バーコードの種類を選択
                            {% else %}
                            Default Barcode Type
                            {% endif %}
                        </span>
                    </label>
    
                    <select class="bg-white border {% include 'data/utility/select-form.html' %} select2-this"
                        data-control="select2" 
                        {% if LANGUAGE_CODE == 'ja'%}
                        data-placeholder="バーコードの個々の設定"
                        {% else %}
                        data-placeholder="Select Barcode Settings"
                        {% endif %}
                        name="barcode-type"
                        >
                        {% for barcode_type in barcode_types %}
                            <option value="{{barcode_type.0}}"
                                {% if barcode_property %}
                                    {% if barcode_property.type == barcode_type.0 %}
                                        selected
                                    {% endif %}
                                {% endif %}
                            >
                                {{barcode_type.1}}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div class="mt-5">
                <div class="fv-rowd-flex flex-column mb-3">
                    <label class="fs-4 fw-bold mb-2">
                        <span class=""> 
                            {% if LANGUAGE_CODE == 'ja'%}
                            バーコードのプロパティを選択
                            {% else %}
                            Barcode Query Properties
                            {% endif %}
                        </span>
                    </label>

                    <select class="bg-white border {% include 'data/utility/select-form.html' %} select2-this" 
                        name="barcode-properties" 
                        data-control="select2" 
                        {% if LANGUAGE_CODE == 'ja'%}
                        data-placeholder="バーコードのプロパティを選択" 
                        {% else %}
                        data-placeholder="Select Barcode Properties" 
                        {% endif %}
                        >
                        {% for barcode_column in barcode_column_values %}
                            <option value="{{barcode_column}}"
                                {% if barcode_property %}
                                    {% if barcode_property.column_field == barcode_column %}
                                        selected
                                    {% endif %}
                                {% endif %}
                            >
                                {% if barcode_column|search_custom_field_object_items:request %}
                                    {% with channel_column=barcode_column|search_custom_field_object_items:request %}
                                        {{channel_column.name}}
                                    {% endwith %} 
                                {% else %}
                                    {% with column_display=barcode_column|display_column_items:request %}
                                        {{column_display}}
                                    {% endwith %}
                                {% endif %}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            {% include 'data/partials/pdf-template-list.html' with default_pdf_color=app_setting.order_pdf_color default_pdf_font_type=app_setting.order_pdf_font_type default_pdf_template=app_setting.order_pdf_template preselected_col=app_setting.order_pdf_line_item_table preselected_header_block=app_setting.order_pdf_header_block preselected_payment_block=app_setting.order_pdf_payment_block preselected_send_from_block=app_setting.order_pdf_send_from_block preselected_send_to_block=app_setting.order_pdf_send_to_block preselected_ship_to_block=app_setting.order_pdf_ship_to_block preselected_notes_block=app_setting.order_pdf_notes_block list_template=list_template preselected_footer_template=app_setting.order_pdf_footer_template pdf_title=app_setting.order_pdf_title|default_if_none:"" preselected_col_display=app_setting.order_pdf_line_item_table_display preselected_header_block_display=app_setting.order_pdf_header_block_display preselected_payment_block_display=app_setting.order_pdf_payment_block_display preselected_send_from_block_display=app_setting.order_pdf_send_from_block_display preselected_send_to_block_display=app_setting.order_pdf_send_to_block_display preselected_notes_block_display=app_setting.order_pdf_notes_block_display default_line_item_bg_color=app_setting.order_pdf_line_item_table_bg_color default_line_item_font_color=app_setting.order_pdf_line_item_table_font_color default_line_item_font_size=app_setting.order_pdf_line_item_table_font_size preselected_user_block=app_setting.order_pdf_user_block preselected_user_block_display=app_setting.order_pdf_user_block_display %}
        </form>

        <div id="order-logo-uploads">
            <div class="mb-5" id="">
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span >
                        {% if LANGUAGE_CODE == 'ja' %}
                        企業ロゴ
                        {% else %}
                        Company Logo
                        {% endif %}
                    </span>
                    
                </label>
                {% include 'data/partials/partial-stamp.html' with image_file=app_setting.order_logo_file setting_type="order_logo" target_post_url="procurement_settings"%}
            </div> 
        </div>

        <div id="order-stamp-uploads" class="">
            <div class="mb-5" id="">
                <label class="{% include 'data/utility/form-label.html' %}">
                    <span >
                        {% if LANGUAGE_CODE == 'ja' %}
                        社印
                        {% else %}
                        Company Signature
                        {% endif %}
                    </span>
                    
                </label>
                {% include 'data/partials/partial-stamp.html' with image_file=app_setting.order_stamp_file setting_type="order_stamp" target_post_url="procurement_settings" %}
            </div> 
        </div>

        <div id="submit-custom-admin-button" class="mt-5">
            <button form="order_app_setting" type="submit" name="submit-orders-settings-btn" class="btn btn-dark">
                {% if LANGUAGE_CODE == 'ja'%}
                更新
                {% else %}
                Update
                {% endif %}
            </button>
        </div>
        {% endif %}

        {% if setting_type == "commerce_subscription" %}
            <form class="mb-10 pb-5" method="POST" action="{% host_url 'shopturbo_settings' host 'app' %}"
                onkeydown="return event.key != 'Enter';">
                <input type="hidden" class="" name="app_settings" value="manage_app"/>
                {% csrf_token %}
                
                <div class="mb-10"
                    hx-get="{% host_url 'properties' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_subscription"}'
                    hx-trigger="load"
                    hx-target="#properties-table"
                >
                    <div id="properties-table"></div>
                </div>


                {% comment %} ASSOCIATION LABEL SETTINGS {% endcomment %}
                <div
                    hx-get="{% host_url 'association_labels_settings' host 'app' %}"
                    hx-vals='{"page_group_type": "commerce_subscription"}'
                    hx-trigger="load"
                ></div>

                {% comment %} Shipping Fee {% endcomment %}
                <div class="mb-10">
                    <div class="fv-rowd-flex flex-column">
                        <div class="mb-5" id="shipping_fee_section">
                            <label class="fs-4 fw-bold mb-2">
                                <span class=""> 
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    送料
                                    {% else %}
                                    Shipping Cost
                                    {% endif %}
                                    </h2>
                                </span>
                                <a href='javascript:;' onclick='add_subscription_shippings(this);'>
                                    <span class="svg-icon svg-icon-primary svg-icon-2x"><!--begin::Svg Icon | path:/var/www/preview.keenthemes.com/metronic/releases/2021-05-14-112058/theme/html/demo8/dist/../src/media/svg/icons/Files/File-plus.svg--><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <polygon points="0 0 24 0 24 24 0 24"/>
                                            <path d="M5.85714286,2 L13.7364114,2 C14.0910962,2 14.4343066,2.12568431 14.7051108,2.35473959 L19.4686994,6.3839416 C19.8056532,6.66894833 20,7.08787823 20,7.52920201 L20,20.0833333 C20,21.8738751 19.9795521,22 18.1428571,22 L5.85714286,22 C4.02044787,22 4,21.8738751 4,20.0833333 L4,3.91666667 C4,2.12612489 4.02044787,2 5.85714286,2 Z" fill="#000000" fill-rule="nonzero" opacity="0.3"/>
                                            <path d="M11,14 L9,14 C8.44771525,14 8,13.5522847 8,13 C8,12.4477153 8.44771525,12 9,12 L11,12 L11,10 C11,9.44771525 11.4477153,9 12,9 C12.5522847,9 13,9.44771525 13,10 L13,12 L15,12 C15.5522847,12 16,12.4477153 16,13 C16,13.5522847 15.5522847,14 15,14 L13,14 L13,16 C13,16.5522847 12.5522847,17 12,17 C11.4477153,17 11,16.5522847 11,16 L11,14 Z" fill="#000000"/>
                                        </g>
                                    </svg>
                                </a>
                            </label>

                            {% for ShopTurboSubscriptionShippingCost in ShopTurboSubscriptionShippingCosts %}
                            <div class="mb-5">

                                <input type="hidden" name="subscription_shipping_field_id" value="{{ShopTurboSubscriptionShippingCost.id}}"></input>

                                <div class="d-flex ">
                                    <div class="input-group flex-nowrap align-items-strecth">  
                                        <div class="input-prehead">
                                            <select class="{% include 'data/utility/select-form.html' %} input-prehead select2-this" name="subscription_shipping_field_number_format" data-placeholder="value">
                                                
                                            {% if workspace.currencies %}
                                            <optgroup
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                label="デフォルト通貨"
                                                {% else %}
                                                label="Default Currency"
                                                {% endif %}
                                            >
                                                {% for currency in workspace.currencies|string_list_to_list %}
                                                    {% if forloop.counter == 1 %}
                                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                    {% elif ShopTurboSubscriptionShippingCost.number_format == currency %}
                                                        <option value="{{currency}}" selected>{{currency|get_currencies_text_symbol}}</option>
                                                    {% else %}
                                                        <option value="{{currency}}">{{currency|get_currencies_text_symbol}}</option>
                                                    {% endif %}
                                                {% endfor %}

                                            </optgroup>
                                            {% endif %}

                                            <optgroup
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                label="すべての通貨"
                                                {% else %}
                                                label="All Currencies"
                                                {% endif %}
                                            >
                                            {% include 'data/partials/money-currency.html' with obj=ShopTurboSubscriptionShippingCost.number_format %}
                                            </optgroup>
                                            </select>
                                        </div>

                                        <input class="form-control w-50 h-40px" name="subscription_shipping_field_value"
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            placeholder="送料の値" 
                                            {% else %}
                                            placeholder="Shipping Price Value" 
                                            {% endif %}
                                            
                                            {% if ShopTurboSubscriptionShippingCost.value %}
                                            value = '{{ShopTurboSubscriptionShippingCost.value}}'
                                            {% endif %}
                                        />
                                        
                                        <input class="form-control d-flex w-25 h-40px" name="subscription_shipping_field_name"
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            placeholder="送料名" 
                                            {% else %}
                                            placeholder="Shipping Fee Name" 
                                            {% endif %}

                                            {% if ShopTurboSubscriptionShippingCost.name %}
                                            value = '{{ShopTurboSubscriptionShippingCost.name}}'
                                            {% endif %}
                                        />
                                    </div>
                                    <div class="input-group-append">
                                        <button class="btn btn-danger" onclick='delete_subscription_shipping(this);' type="button">X</button>
                                    </div> 
                                </div>


                            </div>
                            {% endfor %}
                            
                        </div>
                    </div>
                    
                </div>

                {% include 'data/account/workspace/settings/workspace-object-manager.html' %}

                <div class="my-10 d-none">
                    <label class="{% include 'data/utility/form-label.html' %}">
                        <span class="">
                            {% if LANGUAGE_CODE == 'ja' %}
                            検索設定
                            {% else %}
                            Search Settings
                            {% endif %}
                        </span>
                    </label>

                    <input
                    {% if LANGUAGE_CODE == 'ja' %}
                    placeholder="検索設定"
                    {% else %}
                    placeholder="Search Settings"
                    {% endif %}
                    id="search_setting_subscriptions"
                    name="search_setting_subscriptions" class="form-control" 
                    
                    value=""></input>
                </div>
                

                <div class="mt-5">
                    <button type="submit" class="btn btn-dark" name="submit-subscriptions-settings-btn">
                        {% if LANGUAGE_CODE == 'ja'%}
                        更新
                        {% else %}
                        Update
                        {% endif %}
                    </button>
                </div>
            </form>
        {% endif %}

        {% include 'data/shopturbo/settings/shopturbo-billings-settings.html' %}
    </div>
</div>

{% endblock %}


{% block js %}

{% if setting_type == "commerce_orders" %} 
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>

<script>
    function delete_shipping(elm){
        elm.parentElement.parentElement.parentElement.remove()
    }
</script>

<script>
    var search_setting_order = document.querySelector("#search_setting_order");
    var search_setting_order_tagify = new Tagify(search_setting_order, {
        whitelist: [
        { "value": "ID", "id": "id_predefined"},
        {% for column in column_values %}
            {% if column|search_custom_field_object_orders:request %}
                {% with channel_column=column|search_custom_field_object_orders:request %}
                    { "value": "{{channel_column.name}}", "id": "{{channel_column.id}}" },
                {% endwith %} 
            {% elif column|search_channel_objects:request %}
                {% with channel_column=column|search_channel_objects:request %}
                    { "value": "{{channel_column}}", "id": "{{channel_column}}" },
                {% endwith %} 
            
            {% elif 'contact_custom_field_relations' in column %}
                {% with raw_column=column|split:"|"%}
                    {% with custom_column=raw_column.1|search_custom_field_object_contacts:request %}
                        { "value": "- {{'contact'|display_column_orders:request}} {{custom_column.name}}", "id": "{{column}}" },
                    {% endwith %}
                {% endwith %} 
            {% elif 'company_custom_field_relations' in column %}
                {% with raw_column=column|split:"|"%}
                    {% with custom_column=raw_column.1|search_custom_field_object_company:request %}
                        { "value": "- {{'company'|display_column_orders:request}} {{custom_column.name}}", "id": "{{column}}" },
                    {% endwith %}
                {% endwith %} 
            {% elif 'shopturbo_item_custom_field_relations' in column %}
                {% with raw_column=column|split:"|"%}
                    {% with custom_column=raw_column.1|search_custom_field_object_items:request %}
                        { "value": "{{'items'|display_column_orders:request}} {{custom_column.name}}", "id": "{{column}}" },
                    {% endwith %}
                {% endwith %} 
            {% else %}  
                {% with column_display=column|display_column_orders:request %}
                    { "value": "{{column_display|title}}", "id": "{{column}}" },
                {% endwith %}
            {% endif %}
        {% endfor %}
        ],
        dropdown: {
            maxItems: {{ column_values|safe|length }},           // <- mixumum allowed rendered suggestions
            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
            enabled: 0,             // <- show suggestions on focus
            closeOnSelect: true    // <- do not hide the suggestions dropdown once an item has been selected
        },
        enforceWhitelist: true,
        searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
        originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
        });
    
        search_setting_order_tagify.addTags([{ value: "ID", id: "id_predefined", readonly:"true" }]);
        
    {% if app_setting.search_setting_order %}
        {% with app_setting.search_setting_order|split:"," as search_setting_value %}
            {% for value in search_setting_value %} 
                {% if value|search_custom_field_object_orders:request %}
                    {% with channel_column=value|search_custom_field_object_orders:request %}
                        search_setting_order_tagify.addTags([{ value: "{{channel_column.name}}", id: "{{channel_column.id}}" }]);
                    {% endwith %} 
                {% elif value|search_channel_objects:request %}
                    {% with channel_column=value|search_channel_objects:request %}
                        search_setting_order_tagify.addTags([{ value: "{{channel_column|title}}", id: "{{channel_column}}" }]);
                    {% endwith %}  
                {% elif 'contact_custom_field_relations' in value %}
                    {% with raw_column=value|split:"|"%}
                        {% with custom_column=raw_column.1|search_custom_field_object_contacts:request %}
                            search_setting_order_tagify.addTags([{ value: "- {{'contact'|display_column_orders:request}} {{custom_column.name}}", id: "{{value}}" }]);
                        {% endwith %}
                    {% endwith %} 
                {% elif 'company_custom_field_relations' in value %}
                    {% with raw_column=value|split:"|"%}
                        {% with custom_column=raw_column.1|search_custom_field_object_company:request %}
                            search_setting_order_tagify.addTags([{ value: "- {{'company'|display_column_orders:request}} {{custom_column.name}}", id: "{{value}}" }]);
                        {% endwith %}
                    {% endwith %} 
                {% elif 'shopturbo_item_custom_field_relations' in value %}
                    {% with raw_column=value|split:"|"%}
                        {% with custom_column=raw_column.1|search_custom_field_object_items:request %}
                            search_setting_order_tagify.addTags([{ value: "{{'items'|display_column_orders:request}} {{custom_column.name}}", id: "{{value}}" }]);
                        {% endwith %}
                    {% endwith %} 
                {% else %}  
                    {% if value != 'unset' %}
                        {% with column_display=value|display_column_orders:request %}
                            search_setting_order_tagify.addTags([{ value: "{{column_display|title}}", id: "{{value}}" }]);
                        {% endwith %}
                    {% endif %}
                {% endif %}
            {% endfor %}
        {% endwith %}
    {% endif %}
</script>
{% endif%}

{% if setting_type == "commerce_subscription" %} 
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
<script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify/dist/tagify.polyfills.min.js"></script>
<script>
    var search_setting_subscriptions = document.querySelector("#search_setting_subscriptions");
    var search_setting_subscriptions_tagify = new Tagify(search_setting_subscriptions, {
        whitelist: [
        {% for column in column_values %}
            {% if column|search_custom_field_object_subscriptions:request %}
                {% with channel_column=column|search_custom_field_object_subscriptions:request %}
                    { "value": "{{channel_column.name}}", "id": "{{channel_column.id}}" },
                {% endwith %} 
            {% elif column|search_channel_objects:request %}
                {% with channel_column=column|search_channel_objects:request %}
                    { "value": "{{channel_column}}", "id": "{{channel_column}}" },
                {% endwith %} 
            {% else %}
                {% with column_display=column|display_column_subscriptions:request %}
                    { "value": "{{column_display|title}}", "id": "{{column}}" },
                {% endwith %}
            {% endif %}
        {% endfor %}
        ],
        dropdown: {
            maxItems: 20,           // <- mixumum allowed rendered suggestions
            classname: "tagify__inline__suggestions", // <- custom classname for this dropdown, so it could be targeted
            enabled: 0,             // <- show suggestions on focus
            closeOnSelect: true    // <- do not hide the suggestions dropdown once an item has been selected
        },
        enforceWhitelist: true,
        searchKeys: ['value'],  // very important to set by which keys to search for suggesttions when typing
        originalInputValueFormat: valuesArr => valuesArr.map(item => item.id).join(','), // Include item.id in the submitted values
        });

    {% if app_setting.search_setting_subscriptions %}
        {% with app_setting.search_setting_subscriptions|split:"," as search_setting_value %}
            {% for value in search_setting_value %} 
                {% if value|search_custom_field_object_subscriptions:request %}
                    {% with channel_column=value|search_custom_field_object_subscriptions:request %}
                        search_setting_subscriptions_tagify.addTags([{ value: "{{channel_column.name}}", id: "{{channel_column.id}}" }]);
                    {% endwith %} 
                {% elif value|search_channel_objects:request %}
                    {% with channel_column=value|search_channel_objects:request %}
                        search_setting_subscriptions_tagify.addTags([{ value: "{{channel_column|title}}", id: "{{channel_column}}" }]);
                    {% endwith %}  
                {% else %}
                    {% if value != 'unset' %}
                        search_setting_subscriptions_tagify.addTags([{ value: "{{value|display_column_subscriptions:request}}", id: "{{value}}" }]);
                    {% endif %}
                {% endif %}
            {% endfor %}
        {% endwith %}
    {% endif %}
</script>

{% endif %}


{% endblock %}

